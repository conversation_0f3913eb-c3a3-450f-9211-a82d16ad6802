import ApiS2 from "@/Api/apiS2Config";
import Loading from "@/Components/Loading";
import { formatNumber } from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/StaticData";
import { Carousel } from "@mantine/carousel";
import { Tooltip } from "@mantine/core";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { FaArrowUpLong } from "react-icons/fa6";
import ActivityData from "./ActivityData";

export default function DataQuality({}) {
  const [data, setData] = useState();
  const [NoData, setNoData] = useState();

  const DataQuality = [
    {
      name: "Final DQI",
      transition:
        data?.last_record_effect?.Final_DQI === "No Change"
          ? "-"
          : data?.last_record_effect?.Final_DQI > 0
          ? "up"
          : "down",
      transitionNum:
        data?.last_record_effect?.Final_DQI !== 0
          ? (data?.last_record_effect?.Final_DQI || 0)?.toFixed(2)
          : data?.last_record_effect?.Final_DQI,
      value: (data?.average_score?.Final_DQI || 0)?.toFixed(3),
      date: "Wed, Jul 20",
      descriptions:
        "Overall data quality index after adjusting for alignment between AD and EF scores.",
    },
    {
      name: "Match Score",
      transition:
        data?.last_record_effect?.Match_Score === "No Change"
          ? "-"
          : data?.last_record_effect?.Match_Score >= 0
          ? "up"
          : "down",
      transitionNum:
        data?.last_record_effect?.Match_Score !== 0
          ? data?.last_record_effect?.Match_Score?.toFixed(2)
          : data?.last_record_effect?.Match_Score,
      value: data?.average_score?.Match_Score
        ? data?.average_score?.Match_Score?.toFixed(3)
        : "0",
      date: "Wed, Jul 20",
      descriptions:
        "Reflects the degree of alignment between AD and EF scores across criteria.",
    },
    {
      name: "Uncertainty",
      transition:
        data?.last_record_effect?.Uncertainty === "No Change"
          ? "-"
          : data?.last_record_effect?.Uncertainty > 0
          ? "up"
          : "down",
      transitionNum:
        data?.last_record_effect?.Uncertainty !== 0
          ? (data?.last_record_effect?.Uncertainty || 0)?.toFixed(2)
          : data?.last_record_effect?.Uncertainty,
      value: data?.average_score?.Uncertainty
        ? data?.average_score?.Uncertainty?.toFixed(3)
        : "0",
      date: "Wed, Jul 20",
      descriptions:
        "Provides a measure of confidence in the Final DQI, with lower values indicating higher confidence in data quality.",
    },
    {
      name: "Quality Label",
      transition:
        data?.last_record_effect?.Quality_Label === "No Change" ? "-" : "up",
      transitionNum: null,
      value: data?.average_score?.Quality_Label || "No Data",
      date: "Wed, Jul 20",
      descriptions:
        "Quality rating based on the Final DQI (e.g., “Very Good,” “Good,” etc.).",
    },
    {
      name: "Lower Range Size",
      transition:
        data?.last_record_effect?.["Lower Range Size (t CO2e)"] === "No Change"
          ? "-"
          : data?.last_record_effect?.["Lower Range Size (t CO2e)"] > 0
          ? "up"
          : "down",
      transitionNum:
        data?.last_record_effect?.["Lower Range Size (t CO2e)"] !== 0
          ? (
              data?.last_record_effect?.["Lower Range Size (t CO2e)"] || 0
            ).toFixed(2)
          : data?.last_record_effect?.["Lower Range Size (t CO2e)"],
      value: formatNumber(
        data?.average_score?.["Lower Range Size (t CO2e)"] || 0
      ),
      date: "Wed, Jul 20",
      descriptions:
        "Estimated lower bound of emissions, representing a conservative emissions estimate based on data quality.",
    },
    {
      name: "Upper Range Size",
      transition:
        data?.last_record_effect?.["Upper Range Size (t CO2e)"] === "No Change"
          ? "-"
          : data?.last_record_effect?.["Upper Range Size (t CO2e)"] > 0
          ? "up"
          : "down",
      transitionNum:
        data?.last_record_effect?.["Upper Range Size (t CO2e)"] !== 0
          ? (
              data?.last_record_effect?.["Upper Range Size (t CO2e)"] || 0
            ).toFixed(2)
          : data?.last_record_effect?.["Upper Range Size (t CO2e)"],
      value: formatNumber(
        data?.average_score?.["Upper Range Size (t CO2e)"] || 0
      ),
      date: "Wed, Jul 20",
      descriptions:
        "Estimated upper bound of emissions, representing an optimistic emissions estimate based on data quality.",
    },
    {
      name: "Lower Uncertainty Range",
      transition:
        data?.last_record_effect?.["Lower Uncertainty Range (t CO2e)"] ===
        "No Change"
          ? "-"
          : data?.last_record_effect?.["Lower Uncertainty Range (t CO2e)"] > 0
          ? "up"
          : "down",
      transitionNum:
        data?.last_record_effect?.["Lower Uncertainty Range (t CO2e)"] !== 0
          ? (
              data?.last_record_effect?.["Lower Uncertainty Range (t CO2e)"] ||
              0
            ).toFixed(2)
          : data?.last_record_effect?.["Lower Uncertainty Range (t CO2e)"],
      value: formatNumber(
        data?.average_score?.["Lower Uncertainty Range (t CO2e)"] || 0
      ),
      date: "Wed, Jul 20",
      descriptions:
        "Lower emissions estimate boundary after adjusting for uncertainty.",
    },
    {
      name: "Upper Uncertainty Range",
      transition:
        data?.last_record_effect?.["Upper Uncertainty Range (t CO2e)"] ===
        "No Change"
          ? "-"
          : data?.last_record_effect?.["Upper Uncertainty Range (t CO2e)"] > 0
          ? "up"
          : "down",
      transitionNum:
        data?.last_record_effect?.["Upper Uncertainty Range (t CO2e)"] !== 0
          ? (
              data?.last_record_effect?.["Upper Uncertainty Range (t CO2e)"] ||
              0
            ).toFixed(2)
          : data?.last_record_effect?.["Upper Uncertainty Range (t CO2e)"],
      value: formatNumber(
        data?.average_score?.["Upper Uncertainty Range (t CO2e)"] || 0
      ),
      date: "Wed, Jul 20",
      descriptions:
        "Upper emissions estimate boundary after adjusting for uncertainty.",
    },
    {
      name: "Emissions Based on Higher Quality Data",
      transition:
        data?.last_record_effect?.[
          "Emissions Based on Higher Quality Data (t CO2e)"
        ] === "No Change"
          ? "-"
          : data?.last_record_effect?.[
              "Emissions Based on Higher Quality Data (t CO2e)"
            ] > 0
          ? "up"
          : "down",
      transitionNum:
        data?.last_record_effect?.[
          "Emissions Based on Higher Quality Data (t CO2e)"
        ] !== 0
          ? (
              data?.last_record_effect?.[
                "Emissions Based on Higher Quality Data (t CO2e)"
              ] || 0
            ).toFixed(2)
          : data?.last_record_effect?.[
              "Emissions Based on Higher Quality Data (t CO2e)"
            ],
      value: formatNumber(
        data?.average_score?.[
          "Emissions Based on Higher Quality Data (t CO2e)"
        ] || 0
      ),
      date: "Wed, Jul 20",
      descriptions:
        "Portion of total emissions based on higher-quality data, taking into account the DQI and completeness.",
    },
    {
      name: "Emissions Based on Low Quality Data",
      transition:
        data?.last_record_effect?.[
          "Emissions Based on Low Quality Data (t CO2e)"
        ] === "No Change"
          ? "-"
          : data?.last_record_effect?.[
              "Emissions Based on Low Quality Data (t CO2e)"
            ] > 0
          ? "up"
          : "down",
      transitionNum:
        data?.last_record_effect?.[
          "Emissions Based on Low Quality Data (t CO2e)"
        ] !== 0
          ? (
              data?.last_record_effect?.[
                "Emissions Based on Low Quality Data (t CO2e)"
              ] || 0
            ).toFixed(2)
          : data?.last_record_effect?.[
              "Emissions Based on Low Quality Data (t CO2e)"
            ],
      value: formatNumber(
        data?.average_score?.["Emissions Based on Low Quality Data (t CO2e)"] ||
          0
      ),
      date: "Wed, Jul 20",
      descriptions:
        "Portion of total emissions attributed to lower-quality data.",
    },
    {
      name: "Data Health Status",
      transition:
        data?.last_record_effect?.["DQ Problem?"] === "No Change" ? "-" : "up",
      transitionNum: null,
      value: data?.average_score?.["DQ Problem?"] || "No Data",
      date: "Wed, Jul 20",
      descriptions:
        "Flag indicating whether data quality meets acceptable thresholds, based on Quality Label.",
    },
  ];

  const get_data_quality_score = async () => {
    try {
      const { data } = await ApiS2.get("/admin/get_average_data_quality_score");
      setData(data);
      //console.log(data);
    } catch ({ response }) {
      response.data.message && setNoData(response.data.message);
      //console.log(response.data);
    }
  };
  useEffect(() => {
    get_data_quality_score();
  }, []);
  const { t } = useTranslation();

  return (
    <>
      {!data && !NoData ? (
        <Loading />
      ) : (
        <>
          <>
            <div className="bg-white p-5  rounded-lg shadow-md">
              <Carousel
                // withIndicators
                slideSize={{
                  base: "100%",
                  sm: "50%",
                  md: "33.333333%",
                  lg: "20%",
                }}
                slideGap={{ base: 0, sm: "md" }}
                controlsOffset="0"
                className="px-7"
                loop
                align="center"
              >
                {DataQuality?.map((item, indx) => {
                  return (
                    <Carousel.Slide key={indx} className="">
                      <div className="md:border-e border-dashed border-[#D9DEF1] p-1 flex justify-between items-start w-full">
                        {console.log(item?.transitionNum)}
                        <div className={`w-full`}>
                          <Tooltip
                            multiline
                            w={220}
                            radius={"md"}
                            withArrow
                            transitionProps={{ duration: 200 }}
                            // className={`${SelectedTemplate ? "hidden" : ""}`}
                            label={
                              <span className="capitalize   text-center">
                                {item.descriptions}
                              </span>
                            }
                          >
                            <h1 className="flex text-sm font-normal">
                              {item.name}
                            </h1>
                          </Tooltip>

                          <div className="flex justify-between items-center w-full">
                            <p
                              className={`font-bold ${
                                item.name === "Data Health Status" ||
                                item.name === "Quality Label"
                                  ? "text-red-600"
                                  : "text-primary"
                              }  text-sm`}
                            >
                              {item.value}
                              {item.name !== "Data Health Status" &&
                                item.name !== "Quality Label" &&
                                item.name !== "Final DQI" &&
                                item.name !== "Match Score" &&
                                item.name !== "Uncertainty" && (
                                  <small className="text-nowrap">
                                    {" "}
                                    (T CO2e)
                                  </small>
                                )}
                            </p>
                            <p
                              className={`me-2 py-1 px-2 min-w-12 text-xs  rounded-xl flex justify-between items-center
               ${
                 item.transition === "-"
                   ? "bg-[#ffe6e6] text-[#FF0000]"
                   : item.transition === "up"
                   ? "bg-[#e6f9eb] text-[#01BD36]"
                   : "bg-[#ffe6e6] text-[#FF0000]"
               } 
               ${
                 !item.transitionNum || item.transitionNum === `0.00`
                   ? "hidden"
                   : ""
               }`}
                            >
                              <FaArrowUpLong
                                className={
                                  item.transition !== "up" && "rotate-180"
                                }
                              />
                              {item.transitionNum}
                            </p>
                          </div>
                          <p className="text-sm text-[#071148]">{item.date}</p>
                        </div>
                      </div>
                    </Carousel.Slide>
                  );
                })}
              </Carousel>
            </div>

            <div className=" my-5">
              <div className="my-5 lg:mt-0 flex justify-end"></div>
              <ActivityData AD_Scores={data?.average_score.AD_Scores} />
            </div>
          </>
        </>
      )}
    </>
  );
}
