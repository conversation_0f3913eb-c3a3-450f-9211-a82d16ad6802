import { Button, Table } from "@mantine/core";
import { showNotification } from "@mantine/notifications";
import { useEffect, useRef, useState } from "react";
import { MdArrowDownward } from "react-icons/md";
import ApiS3 from "@/Api/apiS3";
import { IoAddOutline } from "react-icons/io5";
import { tHead } from "../../../Constants/riskIdentTable";

import { IoIosSearch } from "react-icons/io";
import { PiExportLight } from "react-icons/pi";
import { Link } from "react-router-dom";
import RiskRow from "./components/RiskRow";

import * as XLSX from "xlsx";
import NewHeatMap from "./NewHeatMap";

export default function RiskIdenTable() {
  const [loading, setLoading] = useState(false);
  const [myData, setMyData] = useState([]);
  const tableRef = useRef();
  const [searchQuery, setSearchQuery] = useState("");
  const [filters, setFilters] = useState([]);

  // refetch state
  const [refetch, setRefetch] = useState(false);
  const [SelectedTemplate, setSelectedTemplate] = useState();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await ApiS3.get(`risk`);
        if (response?.data) {
          const newDataTable = response.data.map((item) => {
            // Destructure the item to exclude unwanted properties
            const { _id, attachment, updatedAt, createdAt, ...rest } = item;

            // Create a new object with the desired properties
            return {
              ...rest,
              impactSeverity: item.impactSeverity?.name || "", // Get name or empty string
              likelihoodId: item.likelihoodId?.name || "", // Get name or empty string
              reputationalImpact: item.reputationalImpact?.name || "", // Get name or empty string
              valueChainScope: item.valueChainScope.join(", "), // Join array into a string
              impactOnSDG: item.impactOnSDG.join(", "), // Join array into a string
              affectedStakeholders: item.affectedStakeholders.join(", "), // Join array into a string
            };
          });
          setSelectedTemplate(newDataTable);

          setMyData(response.data); // Update table data
        }
      } catch (error) {
        showNotification({
          title: "Error",
          message: "Failed to fetch data",
          color: "red",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [refetch]);

  useEffect(() => {
    setSearchQuery("");
    if (filters.length == 0) {
      setFilters(tHead);
    }
  }, [filters]);

  const rows = myData.map((item, index) => (
    <RiskRow
      key={index}
      item={item}
      refetchFn={() => setRefetch((prev) => !prev)}
    />
  ));

  const exportToExcel = (data) => {
    // Create a new workbook
    const workbook = XLSX.utils.book_new();

    // Convert the data to a worksheet
    const worksheet = XLSX.utils.json_to_sheet(data);

    // Add the worksheet to the workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, "Risk Data");

    // Generate the Excel file
    XLSX.writeFile(workbook, "Risk_Data.xlsx");
  };

  return (
    <>
      <NewHeatMap risks={myData} loading={loading} />
      <div className="bg-[#F7F4F4] mt-12">
        <div className="flex flex-row items-center justify-between shadow-lg font-semibold text-[#000] bg-[#fff] mb-4 py-2 px-4">
          <div className=" flex flex-row text-lg  font-normal">
            <Button
              className="border-[1px] relative z-0 w-[105px] h-[42px] text-sm font-bold rounded-lg text-secondary-300 border-secondary-300 bg-white px-1"
              size="md"
              onClick={() => exportToExcel(SelectedTemplate)}
            >
              <span className="me-1">
                <PiExportLight className="text-lg" />
              </span>
              <span>Export</span>
            </Button>

            <div className="border bg-[#D1D1D1] ml-6"></div>
          </div>
          <div className="flex flex-row mr-4 gap-4">
            <div className="flex flex-row items-center bg-[#F5F5F5] rounded-lg px-6 py-2">
              <input
                placeholder="Find"
                className="w-full bg-transparent input-none outline-none"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <IoIosSearch className="text-[#626364] w-6 h-6 ml-10" />
            </div>

            <div className="btns flex items-center flex-wrap gap-6">
              <Link
                to={`/green-shield/financial/ESG-risk-management/main/systems/add-risk`}
                className="flex items-center justify-center border-[1px] w-[105px] h-[42px] text-sm font-bold rounded-lg bg-primary text-white px-1"
                size="md"
              >
                <span className="me-1">
                  <IoAddOutline className="text-lg" />
                </span>
                <span>Add Risk</span>
              </Link>
            </div>
          </div>
        </div>

        <div className="flex bg-white relative overflow-y-hidden">
          <Table.ScrollContainer
            className="scrollable-container"
            maw={"99%"}
            style={{ display: "flex", alignItems: "center" }}
          >
            <Table
              miw={1500}
              verticalSpacing="lg"
              className="scrollable-container"
              ref={tableRef}
            >
              <Table.Thead className="border-b-2 bg-white pb-6 text-base text-center mb-5">
                <Table.Tr>
                  {tHead
                    .filter((head) => (filters ? filters.includes(head) : true))
                    .map((el, i) => (
                      <Table.Th key={i} className="text-secondary-500">
                        <div className="flex items-center justify-center gap-2">
                          <span className="font-medium">{el}</span>
                          <MdArrowDownward className="text-secondary-300" />
                        </div>
                      </Table.Th>
                    ))}

                  <Table.Th className="text-secondary-500 text-center">
                    Actions
                  </Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody className="relative">
                {loading && (
                  <div className="absolute bg-primary/50 rounded-lg top-0 left-0 w-full h-full flex items-center justify-center">
                    <div className="submit-loader" />
                  </div>
                )}
                {myData.length !== 0 && rows}
              </Table.Tbody>
            </Table>
          </Table.ScrollContainer>
        </div>
      </div>
    </>
  );
}
