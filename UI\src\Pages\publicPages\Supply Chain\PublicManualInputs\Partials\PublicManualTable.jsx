import React from "react";
import cx from "clsx";
import { useState } from "react";
import {
  Table,
  Checkbox,
  ScrollArea,
  rem,
  Button,
  TextInput,
  Pagination,
} from "@mantine/core";
import { MdDelete, MdModeEdit } from "react-icons/md";
import { RiDeleteBin6Line } from "react-icons/ri";
import { IoFilter } from "react-icons/io5";
import { CiSearch } from "react-icons/ci";
import { useTranslation } from "react-i18next";

const data = [
  {
    id: "1",
    time: "lorem, ipsum",
    from: "lorem, ipsum",
    to: "lorem, ipsum",
    AssetsType: "lorem, ipsum",
    // Assets: "lorem, ipsum",
    activity: "lorem ipsum",
    assets: "lorem ipsum",
    efactors: "lorem ipsum",
    uom: "lorem ipsum",
    quantity: "lorem ipsum",
    ReportingYear: "lorem ipsum",
    Evidence_Notes: "lorem ipsum",
  },
  {
    id: "2",
    time: "lorem, ipsum",
    from: "lorem, ipsum",
    to: "lorem, ipsum",
    AssetsType: "lorem, ipsum",
    // Assets: "lorem, ipsum",
    activity: "lorem ipsum",
    assets: "lorem ipsum",
    efactors: "lorem ipsum",
    uom: "lorem ipsum",
    quantity: "lorem ipsum",
    ReportingYear: "lorem ipsum",
    Evidence_Notes: "lorem ipsum",
  },
  {
    id: "3",
    time: "lorem, ipsum",
    from: "lorem, ipsum",
    to: "lorem, ipsum",
    AssetsType: "lorem, ipsum",
    // Assets: "lorem, ipsum",
    activity: "lorem ipsum",
    assets: "lorem ipsum",
    efactors: "lorem ipsum",
    uom: "lorem ipsum",
    quantity: "lorem ipsum",
    ReportingYear: "lorem ipsum",
    Evidence_Notes: "lorem ipsum",
  },
];

export default function PublicManualTable() {
  const [selection, setSelection] = useState([]);
  const { t } = useTranslation();
  const toggleRow = (id) =>
    setSelection((current) =>
      current.includes(id)
        ? current.filter((item) => item !== id)
        : [...current, id]
    );
  const toggleAll = () =>
    setSelection((current) =>
      current.length === data.length ? [] : data.map((item) => item.id)
    );

  const rows = data.map((item) => {
    const selected = selection.includes(item.id);
    const del = (e) => {
      e.target.closest("tr").remove();
    };

    return (
      <Table.Tr
        key={item.id}
        className={`${cx({
          ["bg-[#07838F1A]"]: selected,
        })} text-sm font-bold text-[#626364] text-center`}
      >
        <Table.Td>
          <span
            className="flex justify-center items-center text-xl"
            onClick={del}
          >
            <RiDeleteBin6Line />
          </span>
        </Table.Td>
        <Table.Td>
          <Checkbox
            checked={selection.includes(item.id)}
            onChange={() => toggleRow(item.id)}
            color="#07838F"
          />
        </Table.Td>
        <Table.Td>{item.time}</Table.Td>
        <Table.Td>{item.from}</Table.Td>
        <Table.Td>{item.to}</Table.Td>
        <Table.Td>{item.AssetsType}</Table.Td>
        <Table.Td>{item.assets}</Table.Td>
        <Table.Td>{item.activity}</Table.Td>
        <Table.Td>{item.efactors}</Table.Td>
        <Table.Td>{item.uom}</Table.Td>
        <Table.Td>{item.quantity}</Table.Td>
        <Table.Td>{item.ReportingYear}</Table.Td>
        <Table.Td>{item.Evidence_Notes}</Table.Td>
        {/* <Table.Td>...</Table.Td> */}
      </Table.Tr>
    );
  });

  return (
    <div className="bg-[#F7F4F4] mt-7">
      <div className="p-2 my-1 bg-white shadow-lg rounded-xl">
        <div className="grid items-center justify-end px-4 bg-white grid-cols-1 lg:grid-cols-2 rounded-xl">
          <div className="sm:flex sm:justify-around lg:justify-start">
            <div>
              <Button className="text-black bg-transparent hover:bg-transparent hover:text-black">
                <MdDelete className="me-1" />
                Delete
              </Button>
            </div>
            <div>
              <Button
                className="text-black bg-transparent hover:bg-transparent hover:text-black"
                // onClick={''}
              >
                <MdModeEdit className="me-1" />
                Edit
              </Button>
            </div>
          </div>
          <div >
            <div className="grid items-start justify-center grid-cols-1 md:grid-cols-3 lg:grid-cols-4 sm:items-center gap-x-5">
              <div className="col-span-1  m-3 w-full mx-auto">
                <p className="font-semibold  bg-[#EBEBEB] p-2 text-[#00C0A9] rounded-lg shadow-sm flex items-center w-full justify-center">
                  <span className="text-black">
                    <IoFilter className="mx-2" />
                  </span>
                  Filter
                </p>
              </div>

              <div className="col-span-1 m-3 py-2 px-4 bg-[#EBEBEB] rounded-lg shadow-sm w-full mx-auto">
                <p className="font-semibold text-[#9C9C9C] text-center">Columns</p>
              </div>

              <TextInput
                className="w-full col-span-1 lg:col-span-2"
                placeholder="Search by Topic Area or Assessment Question"
                rightSection={<CiSearch className="w-5 h-5" />}
                // value={''}
                // onChange={''}
                // disabled
              />
            </div>
          </div>
        </div>
      </div>

      <ScrollArea>
        <Table
          // miw={800}
          verticalSpacing="sm"
          className="p-2 my-1 bg-white shadow-lg rounded-xl"
        >
          <Table.Thead className="border-b-2 border pb-6 text-secondary-500 font-bold text-base text-center">
            <Table.Tr>
              <Table.Th className="text-center">Delete</Table.Th>
              <Table.Th style={{ width: rem(40) }}>
                <Checkbox
                  onChange={toggleAll}
                  checked={selection.length === data.length}
                  indeterminate={
                    selection.length > 0 && selection.length !== data.length
                  }
                  color="#07838F"
                />
              </Table.Th>
              <Table.Th className="text-center">Timestamp</Table.Th>
              <Table.Th className="text-center">From</Table.Th>
              <Table.Th className="text-center">To</Table.Th>
              <Table.Th className="text-center">Assets Type</Table.Th>
              <Table.Th className="text-center">Assets</Table.Th>
              <Table.Th className="text-center">Activity</Table.Th>
              <Table.Th className="text-center">E-Factors</Table.Th>
              <Table.Th className="text-center">UOM</Table.Th>
              <Table.Th className="text-center">Quantity</Table.Th>
              <Table.Th className="text-center">Reporting Year</Table.Th>
              <Table.Th className="text-center">Evidence/Notes</Table.Th>
              {/* <Table.Th className="text-center">
                <span className="flex gap-3 justify-center">
                  <span className="bg-white text-[#404B52] rounded-md w-8 h-8 flex justify-center items-center text-sm font-medium cursor-pointer">
                    <MdKeyboardArrowLeft />
                  </span>
                  <span className="bg-white text-[#404B52] rounded-md w-8 h-8 flex justify-center items-center text-sm font-medium cursor-pointer">
                    <MdKeyboardArrowRight />
                  </span>
                </span>
              </Table.Th> */}
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>{rows}</Table.Tbody>
        </Table>
      </ScrollArea>
      <div className="md:flex justify-between mt-5">
        <p className="text-sm text-gray-600">
          {t(
            "showingData"
            //  {
            //   start: (currentPage - 1) * rowsPerPage + 1,
            //   end: Math.min(currentPage * rowsPerPage, data.length),
            //   total: data.length,
            // }
          )}
        </p>
        <Pagination
        // page={}
        // onChange={(e) => {
        //   setCurrentPage(e);
        //   setEdit(false);
        // }}
        // total={totalPages}
        // className={`mt-5 ${DueDateError||actionItemsError?'cursor-not-allowed':''}`}
        // disabled={DueDateError||actionItemsError}
        className="flex justify-center mt-5 md:mt-0"
        />
      </div>
    </div>
  );
}
