import ApiS1Config from "@/Api/apiS1Config";
import { useSelectAssessment } from "@/Contexts/SelectAssessmentContext";
import { Button, Image } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { notifications } from "@mantine/notifications";
import { useState } from "react";
import { IoIosLock } from "react-icons/io";
import { MdHistory } from "react-icons/md";
import { useNavigate } from "react-router-dom";
import CustomPopup from "./CustomPopup";
import HistoryPopUp from "./HistoryPopUp";

function TypeItem({
  title,
  img,
  isActive,
  havCurrentAssessment,
  path,
  popUp,
  assessmentType,
  summaryPath,
  historyPath,
}) {
  const [opened, { open, close }] = useDisclosure(false);
  const [historyOpened, { open: historyOpen, close: historyClose }] =
    useDisclosure(false);
  const navigation = useNavigate();
  const { setSummary } = useSelectAssessment();
  const [loading, setLoading] = useState(false);
  const launchQuiz = () => {
    setLoading(true);
    ApiS1Config.post(
      "/create_assessment",
      {},
      {
        headers: { assessmentType: assessmentType },
      }
    )
      .then((res) => {
        setLoading(false);
        //console.log(res);
        notifications.show({
          message: "Assessment created successfully!",
          color: "green",
        });
        navigation(path);
      })
      .catch((error) => {
        //console.log(error);
        notifications.show({
          message: "Failed to create assessment!",
          color: "red",
        });
      });
  };

  return (
    <>
      <section
        className={`w-full overflow-hidden rounded-2xl shadow-lg mt-6 h-[310px] flex flex-col justify-between hover:-translate-y-2 duration-200 ease-out group relative 
  ${!isActive && "opacity-50 cursor-not-allowed"}`}
      >
        <div className="h-[315px] rounded-t-2xl overflow-hidden">
          <Image
            src={img}
            alt={`${title} image`}
            className="group-hover:scale-105 group-hover:translate-y-[-5px] h-[315px]  object-cover object-center rounded-t-2xl transition-transform duration-500 ease-out relative"
          />
          <h2 className="absolute z-10 bottom-0 left-1/2 -translate-x-1/2 font-bold text-center text-sm md:text-lg bg-white w-[101%] opacity-70 text-secondary-400 tracking-wide py-3 group-hover:hidden transition-opacity duration-700">
            {title}
          </h2>
        </div>

        <div className="absolute inset-0 flex flex-col justify-center items-center bg-white bg-opacity-80 opacity-0 group-hover:opacity-100 group-hover:z-10 transition-opacity duration-300 w-[101%] overflow-hidden">
          <div className="flex flex-col justify-between items-center pb-5 px-1 h-full w-full">
            <h2 className="font-bold text-center text-sm md:text-lg text-secondary-400 tracking-wide py-3">
              {title}
            </h2>
            <div className="space-y-2 flex flex-col-reverse gap-3 mt-auto w-full px-5">
              <Button
                onClick={historyOpen}
                variant="default"
                color="#05808b"
                size="sm"
                className={`w-full font-semibold text-center rounded-lg px-0 shadow-xl ${
                  isActive
                    ? "bg-white hover:bg-secondary-300 hover:text-white text-secondary-300  border-2 border-secondary-300 hover:border-secondary-300 "
                    : "bg-white text-secondary-500 hover:text-secondary-500 border-2 border-secondary-500 cursor-not-allowed"
                }`}
                disabled={!isActive}
              >
                <MdHistory className="me-1" />
                Assessment History
              </Button>

              {!havCurrentAssessment ? (
                <Button
                  className={`w-full  font-semibold text-center rounded-lg px-0 shadow-xl border-2 gap-2 ${
                    loading
                      ? "bg-secondary-300 hover:bg-secondary-300 border-secondary-300"
                      : "bg-secondary-300 hover:text-secondary-300 hover:border-secondary-300 text-white border-2 hover:bg-white"
                  }`}
                  size="sm"
                  onClick={() => {
                    if (assessmentType === "GreenSight AI") {
                      open();
                    } else if (assessmentType === "DecisionSight") {
                      navigation(path);
                    } else {
                      launchQuiz();
                    }
                  }}
                  disabled={!isActive}
                  loading={loading}
                >
                  {!isActive && <IoIosLock className="me-1" />}
                  {loading ? "Starting..." : "Start Now"}
                </Button>
              ) : (
                <Button
                  size="sm"
                  className={`  font-semibold text-center rounded-lg px-0 shadow-xl ${
                    isActive
                      ? "bg-secondary-300 hover:text-secondary-300 hover:border-secondary-300 text-white border-2 hover:bg-white"
                      : "cursor-not-allowed border-secondary-500 text-white border-2 hover:bg-secondary-500 bg-secondary-500 hover:text-white"
                  }`}
                  onClick={() => {
                    setSummary(assessmentType);
                    navigation(summaryPath);
                  }}
                  disabled={!isActive}
                >
                  Resume
                </Button>
              )}
            </div>
          </div>
        </div>
      </section>

      <CustomPopup
        opened={opened}
        close={close}
        Active={isActive}
        path={path}
        assessmentType={assessmentType}
        loading={loading}
        launchQuiz={launchQuiz}
      />
      {historyOpened && (
        <HistoryPopUp
          opened={historyOpened}
          close={historyClose}
          assessmentType={assessmentType}
        />
      )}
    </>
  );
}

export default TypeItem;
