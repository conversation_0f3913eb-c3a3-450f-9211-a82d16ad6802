import { useNavigate } from "react-router";

const TopicCart = (props) => {
  const { image, topicName, id } = props;
  const navigate = useNavigate();
  return (
    <div className="group relative bg-white rounded-[10px] p-2.5 lg:w-90">
      <div className="w-full  md:h-60 overflow-hidden bg-gray-200 rounded-md aspect-h-1 aspect-w-1 lg:aspect-none group-hover:opacity-75 cursor-pointer">
        <img
          src={image}
          alt="Front of men&#039;s Basic Tee in black."
          className="object-cover object-center w-[600px]  h-[250px]  hover:scale-125 transition-all"
          onClick={() => navigate(`/green-shield/resources/${id}`)}
        />
      </div>
      {/* <h3 className="mt-4 text-sm font-medium">
                <span className="text-[#40cdbc]"><PERSON> • 17 Jan 2024</span>
            </h3> */}

      <div
        className="flex items-center justify-between "
        onClick={() => {
          navigate(`/green-shield/resources/${id}`);
        }}
      >
        <p className="mt-1 text-xl font-semibold">{topicName}</p>

        <svg
          width="24"
          height="28"
          viewBox="0 0 24 28"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M7 21L17 11M17 11H7M17 11V21"
            stroke="#101828"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </div>
    </div>
  );
};

export default TopicCart;
