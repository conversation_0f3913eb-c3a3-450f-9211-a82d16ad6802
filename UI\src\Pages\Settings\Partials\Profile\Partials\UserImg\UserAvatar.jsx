import { Avatar } from '@mantine/core'
import { useUserStore } from '@/Store/useUser';
import Loading from '@/Components/Loading';
export function UserAvatar({ userName ,size = 94}) {
    const {getAvatar,loading} = useUserStore((state) => state);
    return <>
        <div style={{position: 'relative'}}>
            {loading ? <><div style={{position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, zIndex: 1, opacity: 0.5}}><Loading /></div> 
                <Avatar key={userName} name={userName} src={getAvatar()} color="initials" size={size} className='opacity-50' /> </>:
            <Avatar key={userName} name={userName} src={getAvatar()} color="initials" size={size} />
            }
        </div>
    </>
}