import ApiS1Config from "@/Api/apiS1Config";
import Loading from "@/Components/Loading";
import {
 Button,
 Checkbox,
 FileInput,
 NumberInput,
 Select,
 Table,
 TagsInput,
 TextInput,
 Textarea,
 Tooltip,
} from "@mantine/core";
import { YearPickerInput } from "@mantine/dates";
import { showNotification } from "@mantine/notifications";
import cx from "clsx";
import dayjs from "dayjs";
import React, { useEffect, useState } from "react";
import { CiSearch } from "react-icons/ci";
import { FaArrowUp } from "react-icons/fa";
import { FaArrowDown } from "react-icons/fa6";
import { IoIosCheckmarkCircleOutline } from "react-icons/io";
import { IoCloseCircleOutline, IoLink } from "react-icons/io5";
import { MdModeEdit, MdOutlineFileDownload } from "react-icons/md";
import SdgImpactFilter from "./SdgImpactFilter";

const SdgImpactTable = ({
 SdgWeights,
 SdgWeightColorMap,
 sdgAlignment,
 sdgImpact,
 postAnswers,
 postLoading,
}) => {
 const [selection, setSelection] = useState([]);
 const [baselineYear, setBaselineYear] = useState({});
 const [targetYear, setTargetYear] = useState({});
 const [currentYear, setCurrentYear] = useState({});
 const [selectedSdgWeightState, setSelectedSdgWeightState] = useState({});
 const [evidenceUrls, setEvidenceUrls] = useState({});
 const [evidenceFile, setEvidenceFile] = useState({});
 const [evidences, setEvidence] = useState({});
 const [backEndEvidences, setBackEndEvidence] = useState({});
 const [actionItems, setActionItems] = useState({});
 const [tags, setTags] = useState({});
 const [updatedValue, setUpdatedValue] = useState({});
 const [uploadLoading, setUploadLoading] = useState({});
 const [search, setSearch] = useState("");
 const [sortedData, setSortedData] = useState();
 const [sortDirection, setSortDirection] = useState("asc");
 const [reverseSortDirection, setReverseSortDirection] = useState(false);
 const [edit, setEdit] = useState(false);
 const [value, setValue] = useState([]);
 const [PickBaseline, setPickBaseline] = useState();
 const [PickTarget, setPickTarget] = useState();
 const [PickCurrentResult, setPickCurrentResult] = useState();
 //console.log(updatedValue);
 
 useEffect(() => {
  setPickBaseline(
   sdgImpact?.baseline_year ? new Date(sdgImpact.baseline_year, 0, 1) : null
  );
  setPickTarget(
   sdgImpact?.target_year ? new Date(sdgImpact.target_year, 0, 1) : null
  );
  setPickCurrentResult(
   sdgImpact?.current_year ? new Date(sdgImpact.current_year, 0, 1) : null
  );

  if (!sdgImpact || !sdgImpact.impactArea) return;

  setSortedData(sdgImpact.impactArea || []);
  sdgImpact.impactArea.forEach((item) =>
   item.questions.forEach((question, idx) => {
    handleSelectSdgWeightChange(
     idx,
     question.SDGWeight ? SdgWeights[question.SDGWeight] : ""
    );
    handleURLInputChange(idx, question.evidence ? question.evidence[1] : "");
    handleActionItemsInputChange(
     idx,
     question.actionItems ? question.actionItems : ""
    );
   })
  );
 }, [sdgImpact]);

 useEffect(() => {
  sortedData?.map((item) =>
   item?.questions?.map((question, idx) => {
    question?.evidence &&
     setBackEndEvidence((prev) => ({
      ...prev,
      [`${item.id}-${idx}`]: question?.evidence[0],
     }));
   })
  );
 }, [sortedData]);
 
 //console.log(evidences);
 const toggleRow = (rowId) => {
  setSelection((prevSelection) => {
   const isSelected = prevSelection.includes(rowId);
   if (isSelected) {
    return prevSelection.filter((id) => id !== rowId);
   } else {
    return [...prevSelection, rowId];
   }
  });
 };
 const toggleAll = () => {
  const allIds = sdgImpact?.impactArea?.map((item) => item.id);
  setSelection((current) => (current.length === allIds.length ? [] : allIds));
 };
 const updateEditState = () => {
  // Create an object where each rowId in selection is set to true
  const newEditState = selection.reduce((acc, rowId) => {
   acc[rowId] = true;
   return acc;
  }, {});

  // Update state
  setEdit((prevEditState) => ({
   ...prevEditState,
   ...newEditState,
  }));
 };

 const handleSelectSdgWeightChange = (rowId, value) => {
  setSelectedSdgWeightState((prev) => ({ ...prev, [rowId]: value }));
 };
 const handleFileInputChange = (rowId, file, type) => {
  if (type === "delete") {
   // Remove the item from the state
   setEvidenceFile((prev) => {
    const updatedFiles = { ...prev };
    delete updatedFiles[rowId];
    return updatedFiles;
   });
   setEvidence((prev) => {
    const updatedFiles = { ...prev };
    delete updatedFiles[rowId];
    return updatedFiles;
   });
   setBackEndEvidence((prev) => {
    const updatedFiles = { ...prev };
    delete updatedFiles[rowId];
    return updatedFiles;
   });
  } else {
   // Update evidenceFile with the new file
   setEvidenceFile((prev) => ({ ...prev, [rowId]: file }));
  }
 };
 const handleURLInputChange = (rowId, file) => {
  // Update evidenceFile with the file
  setEvidenceUrls((prev) => ({ ...prev, [rowId]: file }));
 };
 const handleActionItemsInputChange = (rowId, value) => {
  setActionItems((prev) => ({ ...prev, [rowId]: value }));
 };
 const handleTagsChange = (rowId, tags) => {
  setTags((prev) => ({ ...prev, [rowId]: tags }));
 };
 const handleBaselineYearChange = (rowId, value) => {
  setBaselineYear((prev) => ({ ...prev, [rowId]: value }));
 };
 const handleTargetYearChange = (rowId, value) => {
  setTargetYear((prev) => ({ ...prev, [rowId]: value }));
 };
 const handleCurrentYearChange = (rowId, value) => {
  setCurrentYear((prev) => ({ ...prev, [rowId]: value }));
 };
 const handleKPIChange = (rowId, value) => {
  setUpdatedValue((prev) => ({ ...prev, [rowId]: value }));
 };
 const uploadEvidence = async (rowId) => {
  // let fileArray;
  setUploadLoading((prev) => ({ ...prev, [rowId]: true }));
  const formData = new FormData();

  if (evidenceFile[rowId]) {
   if (Array.isArray(evidenceFile[rowId])) {
    evidenceFile[rowId].forEach((file) =>
     formData.append("evidence_files", file)
    );
   } else {
    formData.append("evidence_files", evidenceFile[rowId]);
   }
  }

  if (evidenceFile[rowId]) {
   try {
    let { data: UploadedFile } = await ApiS1Config.post(
     "upload_evidence_files",
     formData,
     {
      headers: {
       "Content-Type": "multipart/form-data",
      },
     }
    );
    setUploadLoading((prev) => ({ ...prev, [rowId]: false }));

    //console.log(UploadedFile);
    showNotification({
     message: "File uploaded successfully",
     color: "green",
    });
    const fileArray = Object.entries(UploadedFile).map(([name, url]) => ({
     name,
     url,
    }));
    setEvidence((prev) => ({ ...prev, [rowId]: fileArray }));
   } catch (error) {
    setUploadLoading((prev) => ({ ...prev, [rowId]: false }));

    //console.log(error);
    showNotification({
     message: "File not uploaded",
     color: "red",
    });
   }
  }
 };
 const collectData = (assessmentName, categoryName) => {
  const data = {
   name: sdgImpact.name,
   solved: sdgImpact.solved,
   title: sdgImpact.title,
   baseline_year: dayjs(PickBaseline).format("YYYY"),
   current_year: dayjs(PickCurrentResult).format("YYYY"),
   target_year: dayjs(PickTarget).format("YYYY"),

   impactArea: sdgImpact?.impactArea?.map((item) => {
    return {
     id: item.id,
     name: item.name,
     questions: item.questions.map((question, idx) => {
      const selectedWeightEntry = Object.entries(SdgWeights).find(
       ([key, val]) => val === selectedSdgWeightState[`${item.id}-${idx}`]
      );

      const selectedWeight = selectedWeightEntry
       ? Number(selectedWeightEntry[0])
       : null;

      return {
       id: question.id,
       CSRDReference: question.CSRDReference,
       SDG: question.SDG,
       SDGWeight: selectedWeight === null ? question.SDGWeight : selectedWeight,

       updatedValue: updatedValue[`${item.id}-${idx}`] || question.updatedValue,

       evidence: [
        !evidences[`${item.id}-${idx}`]
         ? (question.evidence && question.evidence[0]) || null
         : evidences[`${item.id}-${idx}`],
        !evidenceUrls[`${item.id}-${idx}`]
         ? (question.evidence && question.evidence[1]) || null
         : evidenceUrls[`${item.id}-${idx}`],
       ],

       actionItems: !actionItems[`${item.id}-${idx}`]
        ? question.actionItems
        : [actionItems[`${item.id}-${idx}`]],

       baseline: !baselineYear[`${item.id}-${idx}`]
        ? question.baseline
        : baselineYear[`${item.id}-${idx}`],

       target: !targetYear[`${item.id}-${idx}`]
        ? question.target
        : targetYear[`${item.id}-${idx}`],

       currentResult: !currentYear[`${item.id}-${idx}`]
        ? question.currentResult
        : currentYear[`${item.id}-${idx}`],

       tags: [tags[`${item.id}-${idx}`]] || [],
      };
     }),
    };
   }),
   url: sdgImpact.url,
  };
  //console.log(updatedValue);
  //console.log(data);

  postAnswers(data, assessmentName, categoryName);
 };

 const handleSearchChange = (event) => {
  const value = event.target.value;
  setSearch(value);
  const filteredData = sdgImpact?.impactArea?.filter(
   (item) =>
    item.name.toLowerCase().includes(value.toLowerCase()) ||
    item.questions.some((question) =>
     question.SDG.toLowerCase().includes(value.toLowerCase())
    )
  );

  setSortedData(filteredData);
 };

 const handleSort = (columnKey) => {
  const sorted = [...sortedData].sort((a, b) => {
   if (sortDirection === "asc") {
    return a[columnKey] > b[columnKey] ? 1 : -1;
   } else {
    return a[columnKey] < b[columnKey] ? 1 : -1;
   }
  });

  setSortedData(sorted);
  setSortDirection(sortDirection === "asc" ? "desc" : "asc");
 };

 const isAlignmentSolved = sdgAlignment?.solved;

 const rows = sortedData?.map((item, impactIndex) =>
  item.questions.map((question, questionIndex) => {
   // //console.log(item);

   const selected = selection?.includes(item.id);
   const rowId = `${item.id}-${questionIndex}`;
   const isFirstQuestion = questionIndex === 0;
   const isDisabled = question.solved === true;
   const isNotAplicaple =
    sdgAlignment.topics[impactIndex]?.questions[questionIndex]?.readinessLevel;

   const isDisabledCondition = () => {
    return (
     (!edit && isDisabled) ||
     !isAlignmentSolved ||
     isNotAplicaple === 6 ||
     isNotAplicaple === null
    );
   };
   return (
    <React.Fragment key={rowId}>
     <Table.Tr
      className={`${cx({
       ["bg-[#07838F1A]"]: selected,
      })} ${isAlignmentSolved ? "" : "cursor-not-allowed opacity-50"} ${
       isNotAplicaple === 6 || isNotAplicaple === null
        ? "cursor-not-allowed opacity-50"
        : ""
      }`}
     >
      {isFirstQuestion && (
       <Table.Td rowSpan={item.questions.length}>
        <Checkbox
         checked={selected}
         onChange={() => toggleRow(item.id)}
         color="#07838F"
        />
       </Table.Td>
      )}

      {/* SDG column */}
      {isFirstQuestion && (
       <Table.Td rowSpan={item.questions.length}>
        <p
         className={`w-52 text-left mx-auto ${
          !edit && isDisabled ? "cursor-not-allowed opacity-50" : ""
         }`}
        >
         {question.SDG}
        </p>
       </Table.Td>
      )}

      {/* impact Area column */}
      {isFirstQuestion && (
       <Table.Td rowSpan={item.questions.length}>
        <p
         className={`w-52 text-left mx-auto  ${
          !edit && isDisabled ? "cursor-not-allowed opacity-50" : ""
         }`}
        >
         {item.name}
        </p>
       </Table.Td>
      )}

      {/* KeyPerformanceIndicator column */}
      <Table.Td>
       <div
        className={`w-36 flex justify-center mx-auto ${
         !question.needsUpdate && "text-center"
        } ${!edit && isDisabled ? "cursor-not-allowed opacity-50" : ""}`}
       >
        <NumberInput
         type="text"
         className={` rounded ${!question.needsUpdate && "hidden"}`}
         disabled={isDisabledCondition()}
         onChange={(e) => handleKPIChange(rowId, e)}
         defaultValue={question.updatedValue}
         placeholder="Enter KPI value"
         rightSection
        />
       </div>
      </Table.Td>
      {/* KeyPerformanceIndicator column */}
      <Table.Td>
       <div
        className={`w-80 text-left mx-auto ${
         !edit && isDisabled ? "cursor-not-allowed opacity-50" : ""
        }`}
       >
        <p className="">
         {question.updatedValue
          ? question.updatedValue
          : question.needsUpdate && ' "" '}
         {question["KeyPerformanceIndicator(KPI)"]}
        </p>
       </div>
      </Table.Td>

      {/* Baseline year column */}
      <Table.Td>
       <div className="w-52 mx-auto">
        <NumberInput
         placeholder={`Ex: 2000 ${question.needsUpdate ? "%" : ""}`}
         suffix={question.needsUpdate ? "%" : ""}
         hideControls
         disabled={isDisabledCondition()}
         value={
          baselineYear[rowId] !== undefined && baselineYear[rowId] !== null
           ? baselineYear[rowId].toString()
           : Array.isArray(question.baseline)
           ? question.baseline[0] !== null
             ? question.baseline[0].toString()
             : "" // Handle array case
           : typeof question.baseline === "number"
           ? question.baseline.toString()
           : ""
         }
         onChange={(value) => handleBaselineYearChange(rowId, value)}
         //min={1500}
         // max={100}
        />
       </div>
      </Table.Td>

      {/* Target year column */}
      <Table.Td>
       <div className="w-52 mx-auto">
        <NumberInput
         placeholder={`Ex: 2000 ${question.needsUpdate ? "%" : ""}`}
         suffix={question.needsUpdate ? "%" : ""}
         hideControls
         disabled={isDisabledCondition()}
         value={
          targetYear[rowId] !== undefined && targetYear[rowId] !== null
           ? targetYear[rowId].toString()
           : Array.isArray(question.target)
           ? question.target[0] !== null
             ? question.target[0].toString()
             : "" // Handle array case
           : typeof question.target === "number"
           ? question.target.toString()
           : ""
         }
         onChange={(value) => handleTargetYearChange(rowId, value)}
         // min={1500}
         // max={100}
        />
       </div>
      </Table.Td>

      {/* Current result year column */}
      <Table.Td>
       <div className="w-52 mx-auto">
        <NumberInput
         placeholder={`Ex: 2000 ${question.needsUpdate ? "%" : ""}`}
         suffix={question.needsUpdate ? "%" : ""}
         hideControls
         disabled={isDisabledCondition()}
         value={
          currentYear[rowId] !== undefined && currentYear[rowId] !== null
           ? currentYear[rowId].toString()
           : typeof question.currentResult === "string"
           ? question.currentResult // Keep it as string if it's already a string
           : typeof question.currentResult === "number"
           ? question.currentResult.toString()
           : ""
         }
         onChange={(value) => handleCurrentYearChange(rowId, value)}
         // min={1500}
         // max={100}
        />
       </div>
      </Table.Td>

      {/* SDG weight column */}
      <Table.Td>
       <div className="w-52 mx-auto">
        <Select
         disabled={isDisabledCondition()}
         defaultSearchValue={SdgWeights[question.SDGWeight]}
         value={selectedSdgWeightState[rowId]}
         onChange={(value) => handleSelectSdgWeightChange(rowId, value)}
         classNames={{
          root: "w-fit",
          input: "text-center",
         }}
         radius="xl"
         size="xs"
         className="w-full"
         withCheckIcon={false}
         allowDeselect={false}
         rightSectionWidth="0"
         placeholder="Weight"
         data={Object.keys(SdgWeightColorMap)}
         styles={(theme) => {
          const sdgValue =
           selectedSdgWeightState[rowId] || SdgWeights[question.SDGWeight];

          const sdgStyles = SdgWeightColorMap[sdgValue] || {
           bg: "rgba(0, 0, 0, 0.1)",
           text: "black",
          };

          return {
           input: {
            backgroundColor: sdgStyles.bg,
            color: sdgStyles.text,
            fontWeight: "500",
            border: `none`,
            padding: "16px 12px",
            borderRadius: "15px",
            fontSize: "14px",
            boxShadow: `0 2px 4px rgba(0, 0, 0, 0.1)`,
           },
          };
         }}
        />
       </div>
      </Table.Td>

      {/* Progress column */}
      <Table.Td>
       <div
        className={`w-36 text-center mx-auto ${
         !edit && isDisabled ? "cursor-not-allowed opacity-50" : ""
        }`}
       >
        <p>{question.progress !== null ? question.progress : "No Progress"}</p>
       </div>
      </Table.Td>

      {/* Impact Score column */}
      <Table.Td>
       <div
        className={`w-36 text-center mx-auto ${
         !edit && isDisabled ? "cursor-not-allowed opacity-50" : ""
        }`}
       >
        <p>
         {question.impactScore !== null ? question.impactScore : "No Impact"}
        </p>
       </div>
      </Table.Td>

      {/* Weighted Impact column */}
      <Table.Td>
       <div
        className={`w-36 text-center mx-auto ${
         !edit && isDisabled ? "cursor-not-allowed opacity-50" : ""
        }`}
       >
        <p>
         {question.weightedImpact !== null
          ? question.weightedImpact
          : "No Weight"}
        </p>
       </div>
      </Table.Td>

      {/* Evidence/Notes column */}
      <Table.Td
       className={`w-1/5 text-center ${
        !value.includes("Evidence") ? "hidden" : ""
       }  ${!edit && isDisabled ? "cursor-not-allowed opacity-50" : ""}`}
      >
       <div
        className={`w-56 text-center mx-auto ${
         !edit && isDisabled ? "cursor-not-allowed opacity-50" : ""
        }`}
       >
        {!backEndEvidences[rowId] &&
        !evidenceFile[rowId] &&
        !evidenceFile[rowId]?.length ? (
         <FileInput
          classNames={{ root: "w-fit", input: "px-12" }}
          leftSection={<MdOutlineFileDownload className="w-5 h-5" />}
          variant="unstyled"
          placeholder="Upload evidence"
          className="w-full bg-[#F2F2F2] mb-3"
          radius="md"
          leftSectionPointerEvents="none"
          disabled={!edit && isDisabled ? isDisabled : ""}
          onChange={(file) => handleFileInputChange(rowId, file)}
          multiple
         />
        ) : (
         <div className="flex items-center justify-around mb-1">
          <div className="text-start w-10/12 ">
           {(backEndEvidences &&
            backEndEvidences[rowId]?.map((item, indx) => (
             <Button
              key={indx}
              variant="subtle"
              href={item?.url}
              target="_blank"
              component="a"
              className="w-full py-0"
             >
              <p key={indx} className="truncate py-0">
               {item.name}
              </p>
             </Button>
            ))) ||
            evidenceFile[rowId]?.map((item, indx) => (
             <p key={indx} className="truncate ">
              {item.name}
             </p>
            ))}
          </div>
          <div className="flex">
           {uploadLoading[rowId] ? (
            <Loading />
           ) : (
            <>
             <IoCloseCircleOutline
              className={` text-red-600 ${
               !edit && isDisabled ? " cursor-not-allowed" : "cursor-pointer"
              }`}
              onClick={() => {
               if (!edit && isDisabled) return;
               if (evidenceFile[rowId] || backEndEvidences[rowId])
                handleFileInputChange(rowId, [], "delete");
               // if (evidence) question.evidence[0] = null;
              }}
             />
             {evidenceFile[rowId] && !evidences[rowId] && (
              <IoIosCheckmarkCircleOutline
               className={` text-green-600 ${
                !edit && isDisabled ? " cursor-not-allowed" : "cursor-pointer"
               }`}
               onClick={() => {
                // if (!edit && isDisabled) return;
                // if (evidenceFile[rowId])
                //   handleFileInputChange(rowId, [], "delete");
                // if (evidence) question.evidence[0] = null;
                uploadEvidence(rowId);
               }}
              />
             )}
            </>
           )}
          </div>
         </div>
        )}
        <TextInput
         classNames={{ root: "w-fit", input: "ps-8 pe-2" }}
         leftSection={<IoLink className="w-5 h-5" />}
         variant="unstyled"
         placeholder="enter an URL"
         className="w-full bg-[#e3f0fd]"
         radius="md"
         leftSectionPointerEvents="none"
         disabled={!edit && isDisabled ? isDisabled : ""}
         onChange={(e) => handleURLInputChange(rowId, e.target.value)}
         defaultValue={question?.evidence && question?.evidence[1]}
        />
       </div>
      </Table.Td>

      {/* Tags colleagues */}
      <Table.Td
       className={`w-1/5 text-center ${
        !value.includes("Tag Colleagues") ? "hidden" : ""
       }  ${!edit && isDisabled ? "cursor-not-allowed opacity-50" : ""}`}
      >
       <div className="w-56 mx-auto">
        <TagsInput
         placeholder="@..."
         maxtags={4}
         disabled={isDisabledCondition()}
         classNames={{ input: "max-w-96" }}
         onChange={(tags) => handleTagsChange(rowId, tags)}
        />
       </div>
      </Table.Td>

      {/* Action Items columns */}
      <Table.Td
       className={`w-1/5 text-center ${
        !value.includes("Action Items") ? "hidden" : ""
       }  ${!edit && isDisabled ? "cursor-not-allowed opacity-50" : ""}`}
      >
       <div className="w-56 mx-auto">
        <TagsInput
         placeholder="Press Enter to add item"
         disabled={isDisabledCondition()}
         classNames={{ input: "max-w-80" }}
         clearable
         defaultValue={
          Array.isArray(question?.actionItems)
           ? question.actionItems.filter(
              (item) => item && typeof item === "string"
             )
           : []
         }
         onChange={(tags) => handleActionItemsInputChange(rowId, tags)}
        />
       </div>
      </Table.Td>

      {/* Owner input */}
      <Table.Td
       className={`w-1/5 text-center ${
        !value.includes("Owner") ? "hidden" : ""
       }  ${!edit && isDisabled ? "cursor-not-allowed opacity-50" : ""}`}
      >
       <div className="w-56 mx-auto">
        <Textarea
         placeholder="Enter Owner"
         className="max-w-80"
         disabled={isDisabledCondition()}
         classNames={{ input: "max-w-80" }}
        />
       </div>
      </Table.Td>

      {/* DueDate input */}
      <Table.Td
       className={`w-1/5 text-center ${
        !value.includes("Due Date") ? "hidden" : ""
       }  ${!edit && isDisabled ? "cursor-not-allowed opacity-50" : ""}`}
      >
       <div className="w-56 mx-auto">
        <TagsInput
         placeholder="Press Enter to add a item"
         disabled={isDisabledCondition()}
         classNames={{ input: "max-w-80" }}
         clearable
         defaultValue={
          Array.isArray(question?.date)
           ? question.date.filter((item) => item && typeof item === "string")
           : []
         }
        />
       </div>
      </Table.Td>
     </Table.Tr>
    </React.Fragment>
   );
  })
 );
 return (
  <>
   {!search && !sortedData?.length ? (
    <Loading />
   ) : (
    <>
     <h1 className="hidden mb-3 text-center capitalize md:block">
      To scroll Right and left Hold Shift and Scroll using your mouse
     </h1>
     <div className="p-2 my-1 shadow-lg grid items-center  xl:justify-between px-4 bg-white xl:grid-cols-3 rounded-lg w-full">
      <div className="xl:col-span-1 w-full flex justify-center xl:justify-start">
       <Button
        className="text-black bg-transparent hover:bg-transparent hover:text-black border border-gray-600 w-full xl:w-auto"
        onClick={updateEditState}
       >
        <MdModeEdit className="me-1" />
        Edit
       </Button>
      </div>
      <div className="xl:col-span-2 grid items-start  justify-center grid-cols-1 lg:grid-cols-3 xl:grid-cols-4 sm:items-center w-full lg:gap-5">
       <div className="md:col-span-1 hidden xl:flex justify-center items-center m-3  p-2 rounded-lg shadow-sm ms-auto "></div>

       <div className=" md:col-span-1 m-3  bg-[#EBEBEB] rounded-lg shadow-sm md:ms-auto mx-auto w-full">
        <SdgImpactFilter setValue={setValue} value={value} />
       </div>

       <TextInput
        className="w-full col-span-2"
        placeholder="Search by Topic Area or Assessment Question"
        rightSection={<CiSearch className="w-5 h-5" />}
        value={search}
        onChange={handleSearchChange}
        // disabled
       />
      </div>
     </div>
     {search && sortedData?.length === 0 ? (
      <h1 className="capitalize text-center mt-5">Your Search is not Found</h1>
     ) : (
      <div className="bg-white p-2 my-1 rounded-xl shadow-lg">
       <Table.ScrollContainer className="scrollable-container" maw={"99%"}>
        <Table
         verticalSpacing="sm"
         horizontalSpacing={""}
         className="scrollable-container"
        >
         <Table.Thead className="pb-6 text-base font-thin">
          <Table.Tr className="text-secondary-500">
           <Table.Th className="">
            <Checkbox
             onChange={toggleAll}
             checked={selection.length === sdgImpact?.impactArea.length}
             indeterminate={
              selection.length > 0 &&
              selection.length !== sdgImpact?.impactArea.length
             }
             color="#07838F"
            />
           </Table.Th>

           <Table.Th className="text-center cursor-pointer">
            <h1 className="flex items-center justify-left ms-4 gap-3">
             SDG
             {reverseSortDirection === true ? (
              <FaArrowUp className="ms-1 text-[#00C0A9]" />
             ) : (
              <FaArrowDown className="ms-1 text-[#00C0A9]" />
             )}
            </h1>
           </Table.Th>

           <Table.Th
            className="text-center cursor-pointer"
            onClick={() => {
             handleSort("name");
             setReverseSortDirection((prev) => !prev);
            }}
           >
            <h1 className="flex items-center justify-left ms-4 gap-3">
             Impact Area
             {reverseSortDirection === true ? (
              <FaArrowUp className="ms-1 text-[#00C0A9]" />
             ) : (
              <FaArrowDown className="ms-1 text-[#00C0A9]" />
             )}
            </h1>
           </Table.Th>

           <Table.Th
            className="text-center w-1/4 cursor-pointer"
            onClick={() => {
             handleSort("SDG");
             setReverseSortDirection((prev) => !prev);
            }}
           >
            <h1 className="flex items-center justify-left ms-4 gap-3">
             (KPI) Value
            </h1>
           </Table.Th>
           <Table.Th
            className="text-center w-1/4 cursor-pointer"
            onClick={() => {
             handleSort("SDG");
             setReverseSortDirection((prev) => !prev);
            }}
           >
            <h1 className="flex items-center justify-left ms-4 gap-3">
             Key Performance Indicator (KPI)
            </h1>
           </Table.Th>

           <Table.Th className="w-1/5">
            <div className="flex flex-col items-center justify-center">
             <h1 className="flex items-center justify-center ms-4 gap-3 text-nowrap">
              Baseline Year
             </h1>
             <YearPickerInput
              placeholder="Pick date"
              variant="unstyled"
              value={PickBaseline}
              onChange={setPickBaseline}
              className="text-center text-gray-600"
             />
            </div>
           </Table.Th>

           <Table.Th className="text-center w-1/5">
            <div className="flex flex-col items-center justify-center">
             <h1 className="flex items-center justify-center ms-4 gap-3">
              Target Year
             </h1>
             <YearPickerInput
              placeholder="Pick date"
              variant="unstyled"
              value={PickTarget}
              onChange={setPickTarget}
              className="text-center text-gray-600"
             />
            </div>
           </Table.Th>

           <Table.Th className="text-center w-1/5">
            <div className="flex flex-col items-center justify-center">
             <h1 className="flex items-center justify-center ms-4 gap-3">
              Current Result Year
             </h1>
             <YearPickerInput
              placeholder="Pick date"
              variant="unstyled"
              value={PickCurrentResult}
              onChange={setPickCurrentResult}
              className="text-center text-gray-600"
             />
            </div>
           </Table.Th>

           <Table.Th className="text-center w-1/5">
            <h1 className="flex items-center justify-center ms- gap-3">
             SDG Weight
             <FaArrowDown className="ms-1 text-[#00C0A9]" />
            </h1>
           </Table.Th>

           <Table.Th className="text-center w-1/5">
            <h1 className="flex items-center justify-center ms- gap-3">
             Progress
            </h1>
           </Table.Th>

           <Table.Th className="text-center w-1/5">
            <h1 className="flex items-center justify-center ms- gap-3">
             Impact Score
            </h1>
           </Table.Th>

           <Table.Th className="text-center w-1/5">
            <h1 className="flex items-center justify-center ms- gap-3">
             Weighted Impact
            </h1>
           </Table.Th>

           <Table.Th
            className={`w-1/5 text-center ${
             !value.includes("Evidence") ? "hidden" : ""
            }`}
           >
            <h1 className="flex items-center justify-center ms-4 gap-3">
             Evidence/Notes
             <FaArrowDown className="ms-1 text-[#00C0A9]" />
            </h1>
           </Table.Th>

           <Table.Th
            className={`w-1/5 text-center ${
             !value.includes("Tag Colleagues") ? "hidden" : ""
            }`}
           >
            <h1 className="flex items-center justify-center ms-4 gap-3">
             Tag Colleagues
             <FaArrowDown className="ms-1 text-[#00C0A9]" />
            </h1>
           </Table.Th>

           <Table.Th
            className={`w-1/5 text-center ${
             !value.includes("Action Items") ? "hidden" : ""
            }`}
           >
            <h1 className="flex items-center justify-center ms-4 gap-3">
             Action Items
             <FaArrowDown className="ms-1 text-[#00C0A9]" />
            </h1>
           </Table.Th>

           <Table.Th
            className={`w-1/5 text-center ${
             !value.includes("Owner") ? "hidden" : ""
            }`}
           >
            <h1 className="flex items-center justify-center ms-4 gap-3">
             Owner
             <FaArrowDown className="ms-1 text-[#00C0A9]" />
            </h1>
           </Table.Th>

           <Table.Th
            className={`w-1/5 text-center ${
             !value.includes("Due Date") ? "hidden" : ""
            }`}
           >
            <h1 className="flex items-center justify-center ms-4 gap-3">
             Due Date
             <FaArrowDown className="ms-1 text-[#00C0A9]" />
            </h1>
           </Table.Th>
          </Table.Tr>
         </Table.Thead>
         <Table.Tbody className="text-gray-600 text-base font-semibold">
          {rows}
         </Table.Tbody>
        </Table>
       </Table.ScrollContainer>
       <div className="flex items-center justify-end">
        <Tooltip
         multiline
         w={200}
         radius={"md"}
         withArrow
         transitionProps={{ duration: 200 }}
         label={
          !isAlignmentSolved ? (
           <span className="capitalize flex justify-center text-center">
            Please solve the all sdg alignment assessment first!
           </span>
          ) : (
           <span className="capitalize flex justify-center text-center">
            Save your answer
           </span>
          )
         }
        >
         <Button
          className={`bg-primary text-white hover:bg-primary mt-5 ${
           isAlignmentSolved
            ? "hover:opacity-90"
            : "cursor-not-allowed opacity-50"
          }`}
          disabled={isAlignmentSolved ? false : true}
          onClick={
           postLoading
            ? () => {}
            : () => {
               collectData("SDG Assessment", "SDG Impact Measurement");
              }
          }
         >
          {postLoading ? <Loading /> : "Save"}
         </Button>
        </Tooltip>
       </div>
      </div>
     )}
    </>
   )}
  </>
 );
};

export default SdgImpactTable;
