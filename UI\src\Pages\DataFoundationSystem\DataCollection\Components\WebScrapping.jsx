import ApiScrapping from "@/Api/apiSrapping";
import { TagsInput } from "@mantine/core";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

import { Table, Checkbox, Pagination, ScrollArea } from "@mantine/core";
import ScrappedResult from "./ScrappedResult";
import { KEYWORDS } from "../Constants";

const WebScrapping = () => {
  // search loading
  const [loading, setLoading] = useState(false);
  const [scrapeLoading, setScrapeLoading] = useState(false);




  // =============== Scrapped Data (Result) =========================
  const [scrapedResultUrls, setScrapedResultUrls] = useState([]);
  const [metadata, setMetadata] = useState(null);




  // =============== urls to scrape =========================
  const [urls, setUrls] = useState([]);
  const [keywords, setKeywords] = useState(["Carbon", "ESG"]);




  // =============== urls to scrape Pagination =========================
  const PAGE_SIZE = 5;
  const [page, setPage] = useState(1);
  const paginatedData = urls.slice((page - 1) * PAGE_SIZE, page * PAGE_SIZE);





  // =============== Selected urls to scrape  =========================
  const [selected, setSelected] = useState([]);
  const [selectAll, setSelectAll] = useState(false);




  const handleSelectAll = (checked) => {
    setSelectAll(checked);
    if (checked) {
      const currentPageIds = paginatedData.map((row) => row);
      setSelected((prev) => Array.from(new Set([...prev, ...currentPageIds])));
    } else {
      const currentPageIds = paginatedData.map((row) => row);
      setSelected((prev) => prev.filter((id) => !currentPageIds.includes(id)));
    }
  };

  const handleSelectRow = (id, checked) => {
    if (checked) {
      setSelected((prev) => [...prev, id]);
    } else {
      setSelected((prev) => prev.filter((item) => item !== id));
    }
  };

  // Check if all rows on current page are selected
  const allCurrentPageSelected =
    paginatedData.every((row) => selected.includes(row)) &&
    paginatedData.length > 0;

  // =============== Search Function =========================
  const searchFunction = async (e) => {
    e.preventDefault();
    if (!keywords.length) {
      toast.error("Please add at least one keyword");
      return;
    }
    try {
      setLoading(true);
      const response = await ApiScrapping.post(
        "/keywords/search",
        { keywords: keywords, provider: "google" },
        {
          headers: { "Scraper-Type": "static" },
        }
      );
      setUrls(response.data.urls);
      toast.success(response.data.message);
    } catch (error) {
      toast.error(error.response?.data?.message || "Failed to scrape URL");
    } finally {
      setLoading(false);
    }
  };

  // first call /web/scrape api and keywords
  const scrapeStepOne = async (e) => {
    e.preventDefault();

    // if (scrapedResultUrls) has a data then fire the === > step 2 === > scrapeStepTwo()

    if (scrapedResultUrls.length > 0) {
      scrapeStepTwo();
      
    }else {
      try {
        setScrapeLoading(true);
        const response = await ApiScrapping.post(
          "/web/scrape",
          {
            urls: selected,
            keywords: keywords,
          },
          {
            headers: { "Scraper-Type": "static" },
          }
        );
        setScrapedResultUrls(response.data.data.data);
        setMetadata(response.data.data.metadata);
  
        toast.success(response.data.message);
      } catch (error) {
        toast.error(error.response?.data?.error || "Failed to scrape URL");
      } finally {
        setScrapeLoading(false);
      }
    }

  };

  // second call /web/add_urls and metadata
  // same selected urls but with another api ==> with metadata ==> id
  const scrapeStepTwo = async () => {
    try {
      setScrapeLoading(true);
      const response = await ApiScrapping.post(
        "/web/add_urls",
        {
          metadata_id: metadata.id,
          urls: selected,
        },
        {
          headers: { "Scraper-Type": "static" },
        }
      );
      setScrapedResultUrls(response.data.data.data);
      setMetadata(response.data.data.metadata);
      toast.success(response.data.message);
    } catch (error) {
      toast.error(error.response?.data?.error || "Failed to scrape URL");
    } finally {
      setScrapeLoading(false);
    }
  };

  const getScrappedData = async () => {
    try {
      const res = await ApiScrapping.get("/web/get_scraped_data");
      console.log("🚀 ~ getScrappedData ~ res:", res.data.data)
    } catch (error) {
      toast.error(error.response?.data?.error || "Failed to scrape URL");
    } finally {
      setScrapeLoading(false);
    }
  };

  useEffect(() => {
    getScrappedData();
  }, []);

  return (
    <>
      <form
        className="mt-5 bg-white rounded-xl border-2 p-3"
        onSubmit={(e) => searchFunction(e)}
      >
        <label className="text-[#494949] font-medium mb-2">
          Enter Key words
        </label>
        <div className="flex mb-4">
          <TagsInput
            className="w-full border-2 rounded-l-xl p-1"
            classNames={{
              input: "border-none",
            }}
            data={[]}
            value={keywords}
            onChange={(e)=>{
              setKeywords(e)
              setScrapedResultUrls([])
              setUrls([])
              setSelected([])
            }}
          />

          <button
            disabled={loading}
            type="submit"
            className="bg-primary h-[50px] flex items-center justify-center gap-2 text-white text-lg font-semibold rounded-r-lg px-12 py-2 border-1 border-l-0 border-primary hover:bg-opacity-80 transition duration-300"
          >
            Go
            {loading && <div className="submit-loader" />}
          </button>
        </div>

        <div className="flex flex-wrap gap-2 mt-2">
          {KEYWORDS.map((suggestion, index) => (
            <button
              key={index}
              type="button"
              onClick={() => {
                if (!keywords.includes(suggestion)) {
                  setKeywords([...keywords, suggestion]);
                  setScrapedResultUrls([]);
                  setUrls([]);
                  setSelected([]);
                }
              }}
              disabled={keywords.includes(suggestion)}
              className={`px-3 py-1 text-sm rounded-full text-gray-700 transition-colors ${
                keywords.includes(suggestion)
                  ? "bg-gray-100 cursor-not-allowed"
                  : "bg-primary/20 hover:bg-gray-200"
              }`}
            >
              {suggestion}
            </button>
          ))}
        </div>
      </form>

      <div className="mt-8 bg-white rounded-xl border-2 p-6">
        <h2 className="text-xl font-semibold mb-4">URLs for Scraping</h2>
        <ScrollArea>
          <Table highlightOnHover verticalSpacing="sm">
            <Table.Thead bg={"#F5F4F5"}>
              <Table.Tr>
                <Table.Th>
                  <Checkbox
                    checked={allCurrentPageSelected}
                    indeterminate={
                      selected.some((id) => paginatedData.map((row) => row)) &&
                      !allCurrentPageSelected
                    }
                    onChange={(event) =>
                      handleSelectAll(event.currentTarget.checked)
                    }
                    aria-label="Select all URLs"
                  />
                </Table.Th>
                <Table.Th>URLs</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {paginatedData.map((row) => (
                <Table.Tr key={row}>
                  <Table.Td>
                    <Checkbox
                      checked={selected.includes(row)}
                      onChange={(event) =>
                        handleSelectRow(row, event.currentTarget.checked)
                      }
                      aria-label={`Select URL ${row}`}
                    />
                  </Table.Td>
                  <Table.Td>
                    <span style={{ wordBreak: "break-all" }}>{row}</span>
                  {/* <td className="truncate max-w-xs" style={{ maxWidth: 400 }}>
                  </td> */}
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </ScrollArea>
        <div className="flex items-center justify-between mt-4">
          <span className="text-sm text-gray-500">
            Showing {(page - 1) * PAGE_SIZE + 1} to{" "}
            {Math.min(page * PAGE_SIZE, urls.length)} of {urls.length}
          </span>
          <Pagination
            total={Math.ceil(urls.length / PAGE_SIZE)}
            value={page}
            onChange={setPage}
            size="sm"
            radius="xl"
          />
          <button
            className="bg-primary flex items-center justify-center gap-2 text-white px-6 py-2 rounded font-semibold hover:bg-primary/80 transition disabled:opacity-50"
            disabled={selected.length === 0 || scrapeLoading}
            onClick={scrapeStepOne}
          >
            Scrape Selected URLs
            {scrapeLoading && <div className="submit-loader" />}
          </button>
        </div>
      </div>

      {/* scrapping result */}
      {
        scrapedResultUrls.length > 0 && 
      <ScrappedResult urls={scrapedResultUrls} keyWords={keywords} metadata={metadata} />
      }




    </>
  );
};

export default WebScrapping;
