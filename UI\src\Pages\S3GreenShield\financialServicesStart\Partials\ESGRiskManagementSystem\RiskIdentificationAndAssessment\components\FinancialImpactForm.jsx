import {
  MultiSelect,
  Select,
  Button,
  NumberInput,
  Textarea,
  Loader,
  Modal,
  InputWrapper,
  Input,
} from "@mantine/core";

import {
  aiTextType,
  Categories,
  FINANCIAL_IMPACT_TYPES,
  IMPACT_TIMEFRAMES,
  STAKEHOLDERS,
} from "./StaticData";
import { useState } from "react";
import { MdKeyboardArrowDown } from "react-icons/md";
import { showNotification } from "@mantine/notifications";
import { useDisclosure } from "@mantine/hooks";
import { FaRegCopy } from "react-icons/fa";
import AiApi from "@/Api/aiApiConfig";
import Loading from "@/Components/Loading";
import { AIIcon } from "@/assets/icons";

const FinancialImpactForm = ({
  handleChange,
  formData,
  setIsAiDoneExposure,
  setIsAiDoneOpportunity,
  type,
  reputationalImpactData,
  updateRisk,
  loading,
  resetFinancialForm,
}) => {
  const {
    financialImpactType,
    financialExposure,
    financialImpactTimeframe,
    affectedStakeholders,
    reputationalImpact,
    financialExposureDescription,
    opportunityDescription,
    category,
    value,
  } = formData;

  const [isActive, setIsActive] = useState("");
  const [opened, { open, close }] = useDisclosure(false);

  const [aiTextExposure, setAiTextExposure] = useState("");
  const [aiTextOpportunity, setAiTextOpportunity] = useState("");

  const [aiOpportunityLoad, setAiOpportunityLoad] = useState(false);

  const [exposureAiLoad, setexposureAiLoad] = useState(false);

  const aiFuncExposure = async (type) => {
    if (financialExposureDescription.length < 20) {
      showNotification({ message: `Minimum character 20`, color: "red" });
      return;
    }

    const data = {
      processor: "risk_description",
      resources: {
        prompt: financialExposureDescription,
        output_type: type,
      },
    };
    setexposureAiLoad(true);
    try {
      const res = await AiApi.post(`process_request`, data);
      setAiTextExposure(res.data.ai_response);
      open();
      setIsAiDoneExposure(true);
      setIsActive("exposure");
      setexposureAiLoad(false);
    } catch (er) {
      console.log("🚀 ~ aiFuncExposure ~ er:", er);
    }
  };

  const aiFuncOpportunity = async (type) => {
    if (opportunityDescription.length < 20) {
      showNotification({ message: `Minimum character 20`, color: "red" });
      return;
    }

    const data = {
      processor: "risk_description",
      resources: {
        prompt: opportunityDescription,
        output_type: type,
      },
    };
    setAiOpportunityLoad(true);
    try {
      const res = await AiApi.post(`process_request`, data);
      setAiTextOpportunity(res.data.ai_response);
      open();
      setIsAiDoneOpportunity(true);
      setIsActive("opportunity");
      setexposureAiLoad(false);
    } catch (er) {
      console.log("🚀 ~ aiFuncExposure ~ er:", er);
    }
    setAiOpportunityLoad(false);
  };

  const copyFunc = (type) => {
    const text = type == "exposure" ? aiTextExposure : aiTextOpportunity;
    navigator.clipboard
      .writeText(text)
      .then(() => {
        showNotification({
          message: "Text copied to clipboard!",
          color: "green",
        });
        close();
      })
      .catch(() => {
        showNotification({ message: "Failed to copy text", color: "red" });
      });
  };

  return (
    <div>
      {isActive == "exposure" && (
        <Modal
          size={"lg"}
          opened={opened}
          onClose={close}
          title="Ai suggestion"
        >
          <p className="border border-gray-300 rounded-2xl p-2 mb-3">
            {aiTextExposure}
          </p>

          <Button className="bg-primary" onClick={() => copyFunc("exposure")}>
            Copy <FaRegCopy />
          </Button>
        </Modal>
      )}

      {isActive == "opportunity" && (
        <Modal
          size={"lg"}
          opened={opened}
          onClose={close}
          title="Ai suggestion"
        >
          <p className="border border-gray-300 rounded-2xl p-2 mb-3">
            {aiTextOpportunity}
          </p>

          <Button
            className="bg-primary"
            onClick={() => copyFunc("opportunity")}
          >
            Copy <FaRegCopy />
          </Button>
        </Modal>
      )}

      <div className="my-10">
        <h5 className="text-primary font-bold text-center mb-10 lg:text-2xl">
          Financial Impact & Opportunity Section
        </h5>

        <div className="grid lg:grid-cols-3 gap-5 mt-5">
          <Select
            disabled={type === "view"}
            rightSection={<MdKeyboardArrowDown />}
            value={financialImpactType}
            onChange={(value) => {
              handleChange("financialImpactType", value);
              console.log(value);
            }}
            label="Financial Impact Type"
            placeholder="Select impact type"
            data={FINANCIAL_IMPACT_TYPES}
          />

          <NumberInput
            disabled={type === "view"}
            value={financialExposure}
            onChange={(value) => handleChange("financialExposure", value)}
            label="Financial Exposure"
            placeholder="Enter Numeric input"
          />

          <Select
            disabled={type === "view"}
            rightSection={<MdKeyboardArrowDown />}
            value={financialImpactTimeframe}
            onChange={(value) =>
              handleChange("financialImpactTimeframe", value)
            }
            label="Financial Impact Timeframe"
            placeholder="Select timeframe"
            data={IMPACT_TIMEFRAMES}
          />
        </div>
        <div className="grid lg:grid-cols-2 gap-5 mt-5">
          <MultiSelect
            disabled={type === "view"}
            rightSection={<MdKeyboardArrowDown />}
            value={affectedStakeholders || []}
            onChange={(value) => {
              if (value.length == 0) {
                handleChange("affectedStakeholders", []);
              } else {
                handleChange("affectedStakeholders", value);
              }
            }}
            label="Affected Stakeholders"
            placeholder={
              affectedStakeholders.length > 0 ? "" : "Select stakeholders"
            }
            data={STAKEHOLDERS}
          />

          <Select
            disabled={type === "view"}
            rightSection={<MdKeyboardArrowDown />}
            value={reputationalImpact}
            onChange={(value) => handleChange("reputationalImpact", value)}
            label="Reputational Impact"
            placeholder="Scale 1-5"
            data={reputationalImpactData}
          />
        </div>

        <div className="relative mb-8 mt-8">
          <div className="flex justify-between items-center">
            <label
              htmlFor="fin-decs"
              className="text-base font-medium text-black"
            >
              Financial Exposure Description
            </label>
          </div>

          <div className="relative border border-gray-300 min-h-[145px] rounded py-2 bg-white">
            <div
              title="Ai Suggestion"
              disabled={exposureAiLoad}
              className={`absolute right-2 top-2 group`}
            >
              {exposureAiLoad ? (
                <Loader color="#07838F" size="sm" type="dots" />
              ) : (
                <AIIcon className="cursor-pointer" />
              )}
              <div
                className={`${
                  exposureAiLoad ? "hidden" : "flex"
                } gap-1 absolute z-20 top-5 right-0 flex-col opacity-0 group-hover:opacity-100 transition-opacity duration-200`}
              >
                {aiTextType.map((type, index) => (
                  <Button
                    variant="transparent"
                    key={index}
                    onClick={() => aiFuncExposure(type)}
                    className={`base-border p-1 capitalize bg-white hover:bg-primary hover:text-white`}
                  >
                    {type}
                  </Button>
                ))}
              </div>
            </div>

            <Textarea
              disabled={type === "view"}
              id="fin-decs"
              value={financialExposureDescription || ""}
              onChange={(e) =>
                handleChange("financialExposureDescription", e.target.value)
              }
              placeholder="Enter Text"
              classNames={{
                root: "w-[96%] min-h-[145px]",
                input: "min-h-[145px] border-none",
              }}
            />
          </div>
        </div>
        <label
          htmlFor="opportunity-decs"
          className="text-base font-medium text-black"
        >
          Opportunity Description
        </label>

        <div className="relative border border-gray-300 min-h-[145px] rounded py-2 bg-white">
          <div
            title="Ai Suggestion"
            disabled={aiOpportunityLoad}
            className={`absolute right-2 top-2 group`}
          >
            {aiOpportunityLoad ? (
              <Loader color="#07838F" size="sm" type="dots" />
            ) : (
              <AIIcon className="cursor-pointer" />
            )}
            <div
              className={`${
                aiOpportunityLoad ? "hidden" : "flex"
              } gap-1 absolute z-20 top-5 right-0 flex-col opacity-0 group-hover:opacity-100 transition-opacity duration-200`}
            >
              {aiTextType.map((type, index) => (
                <Button
                  variant="transparent"
                  key={index}
                  onClick={() => aiFuncOpportunity(type)}
                  className={`base-border p-1 capitalize bg-white hover:bg-primary hover:text-white`}
                >
                  {type}
                </Button>
              ))}
            </div>
          </div>
          <Textarea
            disabled={type === "view"}
            id="opportunity-decs"
            value={opportunityDescription || ""}
            onChange={(e) =>
              handleChange("opportunityDescription", e.target.value)
            }
            placeholder="Enter Text"
            classNames={{
              root: "w-[96%] min-h-[145px]",
              input: "min-h-[145px] border-none",
            }}
          />
        </div>

        <div className="grid lg:grid-cols-2 gap-5 mt-5">
          <Select
            disabled={type === "view"}
            rightSection={<MdKeyboardArrowDown />}
            value={category}
            onChange={(value) => handleChange("category", value)}
            label="Category"
            placeholder="Select category"
            data={Categories}
          />

          <InputWrapper label="Value">
            <Input
              onChange={(e) => handleChange("value", e.target.value)}
              disabled={type == "view"}
              value={value}
              placeholder="Value"
            />
          </InputWrapper>
        </div>

        <div className="flex gap-5 mt-5">
          <Button
            onClick={resetFinancialForm}
            className="hover:bg-slate-300 flex-1 bg-bg-lite1 text-gray-700 py-2 px-4 text-center border-r border-gray-300"
          >
            Clear
          </Button>
          {type === "edit" && (
            <Button
              onClick={updateRisk}
              className="flex-1 bg-teal-500 hover:bg-green-600 text-white py-2 px-4 text-center"
            >
              Save {loading && <Loading />}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default FinancialImpactForm;
