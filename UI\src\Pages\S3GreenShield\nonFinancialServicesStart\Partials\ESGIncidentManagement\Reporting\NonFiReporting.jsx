import S3Layout from "@/Layout/S3Layout";
import S3ComingSoonView from "@/Pages/S2ZeroNetPages/ComingSoon/S3ComingSoonView";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import React from "react";
import { CgSandClock } from "react-icons/cg";

export default function NonFiReporting() {
  const { greenShieldNonFiESGIncidentManagement } = useSideBarRoute();

  return (
    <S3Layout menus={greenShieldNonFiESGIncidentManagement}>
      {/* <S3ComingSoonView */}
      <h2 className="flex  items-center justify-center gap-3 text-center py-12 text-2xl animate-pulse">
        <CgSandClock /> Coming Soon
      </h2>
    </S3Layout>
  );
}
