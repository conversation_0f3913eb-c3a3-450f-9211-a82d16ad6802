import { createContext, useContext, useState } from "react";

const StrategyContext = createContext();

export function useStrategy() {
  return useContext(StrategyContext);
}

export function StrategyProvider({ activeTab, setActiveTab, children }) {
  const [savedStrategy, setSavedStrategy] = useState(null);

  return (
    <StrategyContext.Provider
      value={{ savedStrategy, setSavedStrategy, activeTab, setActiveTab }}
    >
      {children}
    </StrategyContext.Provider>
  );
}
