import { MdCo2 } from "react-icons/md";
import {
    Box,
    Checkbox,
    NumberInput,
    Select,
    Text,
    TextInput,
    UnstyledButton,
} from "@mantine/core";
import { FiDatabase } from "react-icons/fi";
import { TbMoneybag, TbTargetArrow } from "react-icons/tb";
import { LuChartNoAxesColumn } from "react-icons/lu";
import { useState } from "react";
import { motion } from "framer-motion";

const Section = ({ title, icon: Icon, inputs }) => {
    /*
        inputs = [{
            label: String,
            onClick: Function,
            defaultValue: String || Number || Null (default),
            size: 1 (default) || 2 || 3,
            description: String || Null (default),
            type: "string" (default) || "select" || "number"
            options: String[] || Null (default) (if type is select, you have pass this prop)
            max: Number || Null (default) (if type is number, you can pass this prop)
            min: Number || Null (default) (if type is number, you can pass this prop)
            placeholder: String || Null (default)
        }]
    */
    return (
        <motion.div 
            className="flex flex-col p-6 bg-white shadow-md rounded-lg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            whileHover={{ 
                scale: 1.02,
                boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
                transition: { duration: 0.3, ease: "easeInOut" }
            }}
            whileTap={{ scale: 0.98 }}
        >
            {/* Icon && Title */}
            <div className="flex justify-start items-center w-full mb-4 gap-4">
                <span className="bg-[#E7F3F4] p-2 rounded-lg">
                    <Icon size={24} color="#2C5A8C" />
                </span>
                <span className="font-semibold text-2xl">{title}</span>
            </div>

            {/* Inputs */}
            <div className="grid grid-cols-6 gap-4">
                {inputs.map((input, idx) => (
                    <div
                        key={idx}
                        className={` ${
                            input.size == 3
                                ? "col-span-2"
                                : input.size == 2
                                ? "col-span-3"
                                : "col-span-6"
                        }`}
                    >
                        {input.type === "number" ? (
                            <NumberInput
                                label={input.label}
                                defaultValue={input.defaultValue || ""}
                                onChange={(value) => input.onChange(value)}
                                rightSection={<></>}
                                rightSectionWidth={20}
                                max={input.max}
                                min={input.min}
                                classNames={{
                                    label: "text-sm mb-1 font-medium",
                                    input: "border-gray-200 focus:border-blue-500 rounded-lg",
                                }}
                                placeholder={input.placeholder || ""}
                            />
                        ) : input.type === "select" ? (
                            <Select
                                label={input.label}
                                data={input.options}
                                defaultValue={input.defaultValue || ""}
                                onChange={(value) => input.onChange(value)}
                                classNames={{
                                    label: "text-sm mb-1 font-medium",
                                    input: "rounded-lg",
                                }}
                                placeholder={input.placeholder || ""}
                            />
                        ) : (
                            <TextInput
                                label={input.label}
                                defaultValue={input.defaultValue || ""}
                                onChange={(value) => input.onChange(value)}
                                classNames={{
                                    label: "text-sm mb-1 font-medium",
                                    input: "rounded-lg",
                                }}
                                placeholder={input.placeholder || ""}
                            />
                        )}

                        {input.description && (
                            <p className="text-xs text-gray-500 mt-1">
                                {input.description}
                            </p>
                        )}
                    </div>
                ))}
            </div>
        </motion.div>
    );
};

const CheckboxSection = () => {
    const [values, setValues] = useState([false, true, false, false]);

    const toggleValue = (index) => {
        setValues((current) => {
            const newValue = [...current];
            newValue[index] = !current[index];
            return newValue;
        });
    };

    const checkboxes = [
        {
            title: "Net Zero 2050",
            subTitle: "1.5°C warming",
            index: 0,
        },
        {
            title: "Delayed Transition",
            subTitle: "1.7°C warming",
            index: 1,
        },
        {
            title: "Current Policies",
            subTitle: "3.2°C warming",
            index: 2,
        },
        {
            title: "NDCs",
            subTitle: "2.5°C warming",
            index: 3,
        },
    ];

    return (
        <Box className="rounded-lg bg-[#07838F1A] px-8 py-4">
            <Text fw={600} size="xl" mb={16}>
                Select Climate Scenarios
            </Text>
            <div className="grid grid-cols-4 items-center gap-3">
                {checkboxes.map((checkbox) => (
                    <UnstyledButton
                        key={checkbox.title}
                        onClick={() => toggleValue(checkbox.index)}
                        className="bg-white p-4 rounded-xl flex"
                    >
                        <Checkbox
                            checked={values[checkbox.index]}
                            size="md"
                            mr="sm"
                            styles={{
                                input: { cursor: "pointer" },
                                label: { display: "none" },
                            }}
                        />
                        <div>
                            <Text fw={700} fs={24}>
                                {checkbox.title}
                            </Text>
                            <Text size="sm" c="dimmed">
                                {checkbox.subTitle}
                            </Text>
                        </div>
                    </UnstyledButton>
                ))}
            </div>
        </Box>
    );
};

export default function DataTab() {
    const sections = [
        {
            title: "Emissions Baseline",
            icon: MdCo2,
            inputs: [
                {
                    label: "Scope 1 (tCO2e)",
                    onChange: (value) => console.log("Scope 1:", value),
                    size: 3,
                    type: "number",
                    placeholder: "1",
                },
                {
                    label: "Scope 1 (tCO2e)",
                    onChange: (value) => console.log("Scope 1:", value),
                    size: 3,
                    type: "number",
                    placeholder: "1",
                },
                {
                    label: "Scope 1 (tCO2e)",
                    onChange: (value) => console.log("Scope 1:", value),
                    size: 3,
                    type: "number",
                    placeholder: "1",
                },
                {
                    label: "Baseline Year",
                    onChange: (value) => console.log("Baseline Year:", value),
                    type: "select",
                    options: Array.from({ length: 24 }, (_, i) =>
                        (2000 + i).toString()
                    ),
                    description: "Reference year for emissions baseline",
                    placeholder: "2023",
                },
                {
                    label: "Verification Status",
                    onChange: (value) =>
                        console.log("Verification Status:", value),
                    type: "select",
                    options: ["Third Party Verified"],
                    placeholder: "Third Party Verified",
                },
            ],
        },
        {
            title: "Financial Metrics",
            icon: FiDatabase,
            inputs: [
                {
                    label: "Annual Revenue (USD)",
                    onChange: (value) => console.log("Annual Revenue:", value),
                    type: "number",
                    description: "Latest fiscal year revenue",
                    placeholder: "150000000",
                },
                {
                    label: "Operating Expenses (USD)",
                    onChange: (value) =>
                        console.log("Operating Expenses:", value),
                    type: "number",
                    placeholder: "120000000",
                },
                {
                    label: "Total Assets (USD)",
                    onChange: (value) => console.log("Total Assets:", value),
                    type: "number",
                    placeholder: "500000000",
                },
                {
                    label: "CAPEX Budget (USD)",
                    onChange: (value) => console.log("Baseline Year:", value),
                    type: "select",
                    options: Array.from({ length: 24 }, (_, i) =>
                        (2000 + i).toString()
                    ),
                    description: "Annual capital expenditure",
                    placeholder: "2023",
                },
            ],
        },
        {
            title: "Physical Assets",
            icon: TbMoneybag,
            inputs: [
                {
                    label: "Primary Facility Locations",
                    onChange: (value) =>
                        console.log("Primary Facility Locations:", value),
                    type: "select",
                    description: "Comma-separated list of major facilities",
                    placeholder: "New York, Chicago, Los Angeles",
                    options: ["New York", "Chicago", "Los Angeles"],
                },
                {
                    label: "Critical Infrastructure Value (USD)",
                    onChange: (value) =>
                        console.log("Critical Infrastructure Value:", value),
                    type: "number",
                    placeholder: "120000000",
                },
                {
                    label: "Total Assets (USD)",
                    onChange: (value) => console.log("Total Assets:", value),
                    type: "number",
                    placeholder: "180000000",
                },
                {
                    label: "Asset Vulnerability Assessment",
                    onChange: (value) =>
                        console.log("Asset Vulnerability Assessment:", value),
                    type: "select",
                    options: ["Complated in last 12 months"],
                    description: "Annual capital expenditure",
                    placeholder: "Complated in last 12 months",
                },
            ],
        },
        {
            title: "Market Position",
            icon: TbMoneybag,
            inputs: [
                {
                    label: "Current Market Share (%)",
                    onChange: (value) =>
                        console.log("Current Market Share:", value),
                    type: "number",
                    min: 0,
                    max: 100,
                    description: "Comma-separated list of major facilities",
                    placeholder: "15%",
                },
                {
                    label: "Product Portfolio",
                    onChange: (value) =>
                        console.log("Product Portfolio:", value),
                    type: "select",
                    placeholder: "Sustainable products",
                    description: "Select all applicable categories",
                    options: ["Sustainable products"],
                },
                {
                    label: "Key Customer Segments",
                    onChange: (value) =>
                        console.log("Key Customer Segments Assets:", value),
                    placeholder: "Manufacturing, Retail, Government",
                },
            ],
        },
        {
            title: "Emission Projection",
            icon: LuChartNoAxesColumn,
            inputs: [
                {
                    label: "Target Year",
                    onChange: (value) => console.log("Target Year:", value),
                    type: "select",
                    options: Array.from({ length: 24 }, (_, i) =>
                        (2025 + i).toString()
                    ),
                    placeholder: "2030",
                },
                {
                    label: "Target Year",
                    onChange: (value) => console.log("Target Year:", value),
                    type: "select",
                    options: ["Use Company Targets (Linear)"],
                    placeholder: "Use Company Targets (Linear)",
                    description: "How to project emissions",
                },
                {
                    label: "Standard Growth Rate (%/year)",
                    onChange: (value) =>
                        console.log("Standard Growth Rate:", value),
                    type: "number",
                    min: 0,
                    max: 100,
                    placeholder: "75%",
                    description: "For scopes without targets",
                },
                {
                    label: "Scope Coverage",
                    onChange: (value) => console.log("Scope Coverage:", value),
                    type: "select",
                    options: ["All Scopes (1+2+3)"],
                    placeholder: "All Scopes (1+2+3)",
                },
                {
                    label: "Default Scenario",
                    onChange: (value) =>
                        console.log("Default Scenario:", value),
                    type: "select",
                    options: ["Net Zero 2050"],
                    description: "Reference scenario for analysis",
                    placeholder: "Net Zero 2050",
                },
                {
                    label: "Analysis Intervals",
                    onChange: (value) =>
                        console.log("Analysis Intervals:", value),
                    type: "select",
                    options: ["Annual"],
                    placeholder: "Annual",
                },
            ],
        },
        {
            title: "Decarbonisation Target",
            icon: TbTargetArrow,
            inputs: [
                {
                    label: "Target Year",
                    onChange: (value) => console.log("Target Year:", value),
                    type: "select",
                    options: Array.from({ length: 24 }, (_, i) =>
                        (2025 + i).toString()
                    ),
                    description: "Long-term neutrality target",
                    placeholder: "2030",
                },
                {
                    label: "Public Commitment",
                    onChange: (value) =>
                        console.log("Public Commitment:", value),
                    placeholder: "Public Commitment made",
                },
                {
                    label: "Interim Target",
                    onChange: (value) => console.log("Interim Target:", value),
                    placeholder: "50% by 2030",
                    description: "Near-term milestone",
                },
                {
                    label: "Scope Coverage",
                    onChange: (value) => console.log("Scope Coverage:", value),
                    type: "select",
                    options: ["All Scopes (1+2+3)"],
                    placeholder: "All Scopes (1+2+3)",
                },
                {
                    label: "SBTi Status",
                    onChange: (value) => console.log("SBTi Status:", value),
                    type: "select",
                    options: ["SPTi validated"],
                    placeholder: "SPTi validated",
                },
            ],
        },
    ];

    const lastSection = {
        title: "Market Position",
        icon: TbMoneybag,
        inputs: [
            {
                label: "Time Horizon",
                onChange: (value) => console.log("Target Year:", value),
                type: "select",
                options: Array.from({ length: 24 }, (_, i) =>
                    (2025 + i).toString()
                ),
                size: 2,
                placeholder: "2050 (Standard)",
            },
            {
                label: "Analysis Intervals",
                onChange: (value) => console.log("Analysis Intervals:", value),
                type: "select",
                options: ["Annual"],
                size: 2,
                placeholder: "Annual",
            },
        ],
    };

    return (
        <div className="flex flex-col gap-6">
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, ease: "easeOut" }}
                // className='flex flex-col md:flex-row flex-wrap items-center w-full px-2 md:px-4 lg:px-6 justify-center gap-4 mt-6'
            >
                <div className="grid grid-cols-3 gap-4 grid-rows-2">
                    {sections.map((section, index) => (
                        <Section key={index} {...section} />
                    ))}
                </div>
            </motion.div>
            <CheckboxSection />
            <Section {...lastSection} />
        </div>
    );
}
