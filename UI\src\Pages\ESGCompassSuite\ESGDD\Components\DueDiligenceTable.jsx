import {
 Button,
 FileInput,
 ScrollArea,
 Select,
 Table,
 TagsInput,
 TextInput,
} from "@mantine/core";
import { notifications, showNotification } from "@mantine/notifications";
import { useEffect, useState } from "react";
import { FaArrowDown } from "react-icons/fa6";

import ApiS1Config from "@/Api/apiS1Config";
import Loading from "@/Components/Loading";
import { useTranslation } from "react-i18next";
import { CiSearch } from "react-icons/ci";
import { FaCheckCircle } from "react-icons/fa";
import { IoCloseCircleOutline, IoLink } from "react-icons/io5";
import { MdModeEdit, MdOutlineFileDownload } from "react-icons/md";
import MultiselectFilter from "../../doubleMateriality/Partials/DoubleMaterialityReadiness/Partials/MultiselectFilter";
import { ReadinessLevel, readinessColorMap, scopes } from "./dueDiligenceData";

import { IoIosCheckmarkCircleOutline } from "react-icons/io";
import { useNavigate } from "react-router";
import Owners from "@/Components/Owner&Taging/Owners";

const DueDiligenceTable = ({ data, isAllSolved, refetch }) => {
 const [tData, settData] = useState(data);
 const navigate = useNavigate();
 const { t } = useTranslation();
 const [postLoading, setPostLoading] = useState(false);
 const [selection, setSelection] = useState(["0"]);
 const [rowIdNum, setRowIdNum] = useState();
 const [dueDate, setDueDate] = useState([]);
 const [actions, setActions] = useState([]);
 const [value, setValue] = useState([]);
 const [columnFilter, setColumnFilter] = useState([]);
 const [selectedScope, setselectedScope] = useState("Environment");
 const [evidenceUrls, setEvidenceUrls] = useState({});
 const [evidenceFile, setEvidenceFile] = useState({});
 const [evidences, setEvidence] = useState({});
 const [backEndEvidences, setBackEndEvidence] = useState({});
 //console.log("🚀 ~ DueDiligenceTable ~ backEndEvidences:", backEndEvidences);
 const [uploadLoading, setUploadLoading] = useState({});
 const [answerdQuestion, setAnswerdQuestion] = useState([]);
 const [answerdQuestion2, setAnswerdQuestion2] = useState();
 const [rerender, setrerender] = useState(false);

 const [scope, setScope] = useState(
  data["ESG Due Diligence"]?.filter(
   (scop) => scop?.categoryName == selectedScope
  )
 );

 const handleTabs = (tab) => {
  setselectedScope((p) => (p = tab));
  let clonedata = data;
  let s = clonedata["ESG Due Diligence"]?.filter(
   (scop) => scop.categoryName == selectedScope
  );
  setScope((p) => (p = s));
  setrerender((p) => (p = true));
  setTimeout(() => {
   setrerender((p) => (p = false));
  }, 0.5);
 };

 useEffect(() => {
  let clonedata = data;
  let s = clonedata["ESG Due Diligence"]?.filter(
   (scop) => scop.categoryName == selectedScope
  );
  setScope((p) => (p = s));
  setAnswerdQuestion((p) => (p = s));
  setAnswerdQuestion2((p) => (p = s));
  //console.log(s, "scope");
  // setBackEndEvidence;
 }, [selectedScope]);
 //console.log(answerdQuestion, "answerdQuestion");

 useEffect(() => {
  data["ESG Due Diligence"]
   ?.filter((scop) => scop.categoryName == selectedScope)[0]
   ?.topics?.map((item) =>
    item?.questions?.map((question, idx) => {
     question?.evidence &&
      setBackEndEvidence((prev) => ({
       ...prev,
       [`${item.id}-${idx}`]: question?.evidence[0],
      }));
    })
   );
 }, [scope, refetch, selectedScope]);
 const handleURLInputChange = (rowId, file, question, item, feild) => {
  //console.log("del", rowId, file);

  // Update evidenceFile with the file
  setEvidenceUrls((prev) => ({ ...prev, [rowId]: file }));
 };
 const handleFileInputChange = (rowId, file, type, question, item, feild) => {
  //console.log(rowId, file, type);

  if (type === "delete") {
   // Remove the item from the state
   setEvidenceFile((prev) => {
    const updatedFiles = { ...prev };
    delete updatedFiles[rowId];
    return updatedFiles;
   });
   setEvidence((prev) => {
    const updatedFiles = { ...prev };
    delete updatedFiles[rowId];
    return updatedFiles;
   });
   setBackEndEvidence((prev) => {
    const updatedFiles = { ...prev };
    delete updatedFiles[rowId];
    return updatedFiles;
   });
   // let currentScopeQuestion = null;
   //   currentScopeQuestion = answerdQuestion2[0]?.topics
   //     .find((t) => t.name == item.name)
   //     .questions.map((el) => {
   //       if (el.id == question.id) {
   //         // get a dynamic key and assign the value
   //         let evExist =
   //           el.evidence == null
   //             ? [fileArray, null]
   //             : [[...el.evidence[0], ...fileArray], el.evidence[1]];
   //         el.evidence = evExist;
   //       }
   //       return el;
   //     });
   //   let assignTopic = answerdQuestion2[0].topics.map((t) => {
   //     if (t.name == item.name) {
   //       t.questions = currentScopeQuestion;
   //     }
   //     return t;
   //   });
   //   let newc = answerdQuestion2[0];
   //   newc.topics = assignTopic;

   //   setAnswerdQuestion2([newc]);
  } else {
   // Update evidenceFile with the new file
   setEvidenceFile((prev) => ({ ...prev, [rowId]: file }));
  }
 };
 const uploadEvidence = async (rowId, item, question) => {
  // let fileArray;
  setUploadLoading((prev) => ({ ...prev, [rowId]: true }));
  const formData = new FormData();

  if (evidenceFile[rowId]) {
   if (Array.isArray(evidenceFile[rowId])) {
    evidenceFile[rowId].forEach((file) =>
     formData.append("evidence_files", file)
    );
   } else {
    formData.append("evidence_files", evidenceFile[rowId]);
   }
  }

  if (evidenceFile[rowId]) {
   try {
    let { data: UploadedFile } = await ApiS1Config.post(
     "upload_evidence_files",
     formData,
     {
      headers: {
       "Content-Type": "multipart/form-data",
      },
     }
    );
    setUploadLoading((prev) => ({ ...prev, [rowId]: false }));

    //console.log(UploadedFile);
    showNotification({
     message: "File uploaded successfully",
     color: "green",
    });
    const fileArray = Object.entries(UploadedFile).map(([name, url]) => ({
     name,
     url,
    }));
    setEvidence((prev) => ({ ...prev, [rowId]: fileArray }));

    let currentScopeQuestion = null;
    currentScopeQuestion = answerdQuestion2[0]?.topics
     .find((t) => t.name == item.name)
     .questions.map((el) => {
      if (el.id == question.id) {
       // get a dynamic key and assign the value
       let evExist =
        el.evidence == null ? [fileArray, null] : [fileArray, el.evidence[1]];
       el.evidence = evExist;
      }
      return el;
     });
    let assignTopic = answerdQuestion2[0].topics.map((t) => {
     if (t.name == item.name) {
      t.questions = currentScopeQuestion;
     }
     return t;
    });
    let newc = answerdQuestion2[0];
    newc.topics = assignTopic;

    setAnswerdQuestion2([newc]);
   } catch (error) {
    setUploadLoading((prev) => ({ ...prev, [rowId]: false }));

    //console.log(error);
    showNotification({
     message: "File not uploaded",
     color: "red",
    });
   }
  }
 };
 //console.log(evidences);
 const handleSelectDynamic = (id, value, question, item, feild) => {
  setRowIdNum(id);
  let currentScopeQuestion = null;
  if (feild == "readinessLevel") {
   let readinessLevelArray = ReadinessLevel.find(
    (rdsLvl) => rdsLvl.level == value
   );
   //console.log(readinessLevelArray);

   currentScopeQuestion = answerdQuestion2[0]?.topics
    .find((t) => t.name == item.name)
    .questions.map((el) => {
     if (el.id == question.id) {
      // get a dynamic key
      el.readinessLevel = readinessLevelArray.id;
     }
     //console.log(el);

     return el;
    });
  } else {
   currentScopeQuestion = answerdQuestion2[0]?.topics
    .find((t) => t.name == item.name)
    .questions.map((el) => {
     if (el.id == question.id) {
      // get a dynamic key and assign the value
      el[feild] = value;
     }
     return el;
    });
  }

  let assignTopic = answerdQuestion2[0].topics.map((t) => {
   if (t.name == item.name) {
    t.questions = currentScopeQuestion;
   }
   return t;
  });
  let newc = answerdQuestion2[0];
  newc.topics = assignTopic;

  setAnswerdQuestion([newc]);
  setAnswerdQuestion2([newc]);
 };

 const handleSearchChange = (event) => {
  const value = event.target.value;
  setValue(value);
 };

 if (rerender) {
  return <Loading />;
 }

 const rows = scope[0]?.topics
  .filter((t) => t.name.toLowerCase().includes(value))
  .map((item) => {
   return item.questions.map((question, idx) => {
    const rowId = `${item.id}-${idx}`;
    const isFirstQuestion = idx === 0;

    let readness = ReadinessLevel.find(
     (el) => el.id == question["readinessLevel"]
    );

    let level = readness == undefined ? "" : readness?.level;

    const ownerStrings = question.owner?.map(o=> o?.user_name)


    return (
     <Table.Tr
      key={rowId}
      // className={cx("bg-[#07838F1A]")}
     >
      {isFirstQuestion && (
       <Table.Td rowSpan={item.questions.length}>
        <p className="text-left min-w-[200px]">{item.name}</p>
       </Table.Td>
      )}
      {/* Topic question column */}
      <Table.Td className="">
       <p className="flex mx-auto min-w-[260px] justify-left text-left">
        {question.questionText}
       </p>
      </Table.Td>

      {/* Readiness Level column */}
      <Table.Td className="w-1/4">
       <div className="flex justify-center w-[260px] mx-auto">
        <Select
         value={level}
         onChange={(value) =>
          handleSelectDynamic(rowId, value, question, item, "readinessLevel")
         }
         radius="xl"
         classNames={{
          root: "w-fit",
          input: "text-center",
         }}
         size="xs"
         withCheckIcon={false}
         allowDeselect={false}
         rightSectionWidth="0"
         placeholder="Select your level"
         data={Object.keys(readinessColorMap)}
         styles={(theme) => ({
          input: {
           backgroundColor: readinessColorMap[readness?.level]?.bg,
           color: readinessColorMap[readness?.level]?.text,
           border: `1px solid ${readinessColorMap[readness?.level]?.border}`,
           padding: "16px 12px",
           borderRadius: "15px",
           fontSize: "14px",
           fontWeight: "500",
           boxShadow: `0 2px 4px rgba(0, 0, 0, 0.1)`,
          },
          dropdown: {
           backgroundColor: "white",
          },
          item: {
           "&[data-selected]": {
            backgroundColor: theme.colors.gray[0],
            color: "black",
            opacity: 1,
           },
          },
         })}
        />
       </div>
      </Table.Td>

      {/* Evidence/Notes column */}
      <Table.Td
       className={`w-1/5 text-left ${
        !columnFilter.includes("Evidence") ? "hidden" : ""
       } 
                `}
      >
       <div className={`w-56 text-left mx-auto`}>
        {!backEndEvidences[rowId] &&
        !evidenceFile[rowId] &&
        !evidenceFile[rowId]?.length ? (
         <FileInput
          classNames={{ root: "w-fit", input: "px-12" }}
          leftSection={<MdOutlineFileDownload className="w-5 h-5" />}
          variant="unstyled"
          placeholder="Upload evidence"
          className="w-full bg-[#F2F2F2] mb-3"
          radius="md"
          leftSectionPointerEvents="none"
          // disabled={!edit && isDisabled ? isDisabled : ""}
          onChange={(file) =>
           handleFileInputChange(rowId, file, question, item, "evidence")
          }
          multiple
         />
        ) : (
         <div className="flex items-center justify-around mb-1">
          <div className="text-start w-10/12 ">
           {(backEndEvidences &&
            backEndEvidences[rowId]?.map((item, indx) => (
             <Button
              key={indx}
              variant="subtle"
              href={item?.url}
              target="_blank"
              component="a"
              className="w-full py-0"
             >
              <p key={indx} className="truncate py-0">
               {item.name}
              </p>
             </Button>
            ))) ||
            evidenceFile[rowId]?.map((item, indx) => (
             <p key={indx} className="truncate ">
              {item.name}
             </p>
            ))}
          </div>
          <div className="flex">
           {uploadLoading[rowId] ? (
            <Loading />
           ) : (
            <>
             <IoCloseCircleOutline
              className={` text-red-600 cursor-pointer
                            `}
              onClick={() => {
               // if (!edit && isDisabled) return;
               if (evidenceFile[rowId] || backEndEvidences[rowId])
                handleFileInputChange(rowId, [], "delete", question, item);
               // if (evidence) question.evidence[0] = null;
              }}
             />
             {evidenceFile[rowId] && !evidences[rowId] && (
              <IoIosCheckmarkCircleOutline
               className={` text-green-600 cursor-pointer
                              `}
               onClick={() => {
                uploadEvidence(rowId, item, question);
               }}
              />
             )}
            </>
           )}
          </div>
         </div>
        )}

        <TextInput
         classNames={{ root: "w-fit", input: "ps-8 pe-2" }}
         leftSection={<IoLink className="w-5 h-5" />}
         variant="unstyled"
         placeholder="enter an URL"
         className="w-full bg-[#e3f0fd]"
         radius="md"
         leftSectionPointerEvents="none"
         // disabled={!edit && isDisabled ? isDisabled : ""}
         onChange={(e) =>
          handleURLInputChange(
           rowId,
           e.target.value,
           question,
           item,
           "evidence"
          )
         }
         defaultValue={question?.evidence && question?.evidence[1]}
        />
       </div>
      </Table.Td>

      {/* Action Items column */}
      <Table.Td
       className={`w-1/5 text-left ${
        !columnFilter.includes("Action Items") ? "hidden" : ""
       }`}
      >
       <TagsInput
        placeholder="Enter Action"
        maxTags={4}
        className="w-80"
        classNames={{ input: "max-w-96" }}
        onChange={(e) => {
         handleSelectDynamic(rowId, e, question, item, "actionItems");
         setActions((p) => e);
        }}
        defaultValue={
         Array.isArray(question?.actionItems)
          ? question?.actionItems.filter(
             (item) => item && typeof item === "string"
            )
          : []
        }
       />
      </Table.Td>
      <Table.Td
       className={`w-1/5 text-left ${
        !columnFilter.includes("Owner") ? "hidden" : ""
       }`}
      >
       <>
        <Owners currentOwners={ownerStrings} handleOwners={(ownersList)=> handleSelectDynamic(rowId, ownersList, question, item, "owner")} />
       </>
      </Table.Td>

      <Table.Td
       className={`w-1/5 text-left ${
        !columnFilter.includes("DueDate") ? "hidden" : ""
       }`}
      >
       <div className="flex justify-center items-center mx-auto">
        <TagsInput
         placeholder="Press Enter to add a date"
         classNames={{ input: "w-80" }}
         clearable
         defaultValue={
          Array.isArray(question?.date)
           ? question?.date.filter((item) => item && typeof item === "string")
           : []
         }
         onChange={(value) => {
          handleSelectDynamic(rowId, value, question, item, "date");
          setRowIdNum(rowId);
          setDueDate(value);
         }}
        />
        {rowIdNum == rowId && (
         <>
          {actions.length !== dueDate.length ? (
           <p className="text-xs text-center text-red-500">
            Due Date must equal the length of action items.
           </p>
          ) : (
           ""
          )}
         </>
        )}
       </div>
      </Table.Td>
     </Table.Tr>
    );
   });
  });

 const sendData = async () => {
  if (dueDate.length != actions.length) {
   notifications.show({
    title: `Due Date must equal the length of action items.`,
    color: "red",
    // icon: <FaCheckCircle />,
    position: "bottom-left",
   });
   return;
  }

  setPostLoading(true);

  // refetch(true)
  try {
   const { data } = await ApiS1Config.post(`/post_scope`, answerdQuestion[0], {
    headers: {
     assessmentName: "ESG Due Diligence",
     categoryName: selectedScope,
    },
   });
   notifications.show({
    title: data?.Message,
    color: "green",
    icon: <FaCheckCircle />,
   });
   setPostLoading(false);
   refetch(false);
  } catch (error) {
   setPostLoading(false);
   //console.log(error);
   notifications.show({
    title: error.response.data.Message,
    color: "red",
   });
   // navigate("/dueDiligence-report");
  }
 };

 return (
  <>
   <div className="flex justify-center gap-5 mb-8 ">
    {scopes.map((scop) => (
     <Button
      onClick={() => handleTabs(scop.name)}
      variant="filled"
      className={`lg:w-[255px] lg:h-[65px] lg:text-xl
              ${
               scop.name == selectedScope
                ? `hover:opacity-70 text-white rounded-lg font-semibold bg-primary`
                : "bg-secondary-primary-lite text-primary"
              }`}
      key={scop.id}
     >
      {scop.id == 3 ? "Governance" : <>{scop.name}</>}
     </Button>
    ))}
   </div>
   <div className="p-2 my-1 shadow-lg grid items-center  xl:justify-between px-4 bg-white xl:grid-cols-3 rounded-lg w-full">
    <div className="xl:col-span-1 w-full flex justify-center xl:justify-start">
     <Button
      className="text-black bg-transparent hover:bg-transparent hover:text-black border border-gray-600 w-full xl:w-auto"
      // onClick={updateEditState}
     >
      <MdModeEdit className="me-1" />
      Edit
     </Button>
    </div>
    <div className="xl:col-span-2 grid items-start  justify-center grid-cols-1 lg:grid-cols-3 xl:grid-cols-4 sm:items-center w-full lg:gap-5">
     <div className="md:col-span-1 hidden xl:flex justify-center items-center m-3  p-2 rounded-lg shadow-sm ms-auto "></div>

     <div className=" md:col-span-1 m-3  bg-[#EBEBEB] rounded-lg shadow-sm md:ms-auto mx-auto w-full">
      <MultiselectFilter setValue={setColumnFilter} value={columnFilter} />
     </div>

     <TextInput
      className="w-full col-span-2"
      placeholder="Search by Topic Area or Assessment Question"
      rightSection={<CiSearch className="w-5 h-5" />}
      // value={search}
      onChange={handleSearchChange}
      // disabled
     />
    </div>
   </div>
   <div className="p-2 my-1 bg-white shadow-lg rounded-xl">
    <ScrollArea h={500}>
     <Table miw={1000} verticalSpacing="sm" className="scrollable-container">
      <Table.Thead className="pb-6 text-base font-thin">
       <Table.Tr className="text-secondary-500">
        <Table.Th className="w-1/5 text-left">
         <h1 className="flex w-full items-center justify-left gap-3 ms-4 text-left">
          {t("topicArea")} <FaArrowDown className="ms-1 text-[#00C0A9]" />
         </h1>
        </Table.Th>
        <Table.Th className="text-left">
         <h1 className="flex items-center justify-left gap-3 ms-4">
          {t("assessmentQuestion")}{" "}
          <FaArrowDown className="ms-1 text-[#00C0A9]" />
         </h1>
        </Table.Th>
        <Table.Th className="text-left w-1/5">
         <h1 className="flex items-center justify-center gap-3 ms-4">
          {t("assessmentLevel")} <FaArrowDown className="ms-1 text-[#00C0A9]" />
         </h1>
        </Table.Th>
        <Table.Th
         className={`w-1/5 text-center ${
          !columnFilter.includes("Evidence") ? "hidden" : ""
         }`}
        >
         <h1 className="flex items-center justify-center gap-3 ms-4">
          {t("evidenceNotes")} <FaArrowDown className="ms-1 text-[#00C0A9]" />
         </h1>
        </Table.Th>
        <Table.Th
         className={`w-1/5 text-center ${
          !columnFilter.includes("ActionItems") ? "hidden" : ""
         }`}
        >
         <h1 className="flex items-center justify-center gap-3 ms-4">
          {t("actionItems")} <FaArrowDown className="ms-1 text-[#00C0A9]" />
         </h1>
        </Table.Th>
        <Table.Th
         className={`w-1/5 text-center ${
          !columnFilter.includes("Owner") ? "hidden" : ""
         }`}
        >
         <h1 className="flex items-center justify-center gap-3 ms-4">
          {t("owner")} <FaArrowDown className="ms-1 text-[#00C0A9]" />
         </h1>
        </Table.Th>
        <Table.Th
         className={`w-1/5 text-center ${
          !columnFilter.includes("DueDate") ? "hidden" : ""
         }`}
        >
         <h1 className="flex items-center justify-center gap-3 ms-4">
          {t("dueDate")} <FaArrowDown className="ms-1 text-[#00C0A9]" />
         </h1>
        </Table.Th>
       </Table.Tr>
      </Table.Thead>
      <Table.Tbody className="text-base text-left font-semibold text-gray-600">
       {rows}
      </Table.Tbody>
     </Table>
     <br />
    </ScrollArea>
    <div className="flex mt-5 gap-5 items-center justify-end">
     <div className="flex gap-5">
      <Button
       disabled={postLoading || isAllSolved}
       className={`text-white ${
        postLoading || isAllSolved
         ? "cursor-not-allowed opacity-50 bg-primary"
         : "bg-primary hover:opacity-90 "
       }`}
       onClick={() => sendData()}
      >
       {postLoading ? <Loading /> : "Save"}
      </Button>
     </div>
    </div>
    <br />
    <br />
    <br />
   </div>
  </>
 );
};

export default DueDiligenceTable;
