import { methods } from "@/Pages/CarbonSystem/Constants/EmissionConstants";
import { useState } from "react";
import FuelBasedMethod from "../TransportationMethods/FuelBasedMethod";
import DistanceBasedMethod from "../TransportationMethods/DistanceBasedMethod";
import SpendBasedMethod from "../TransportationMethods/SpendBasedMethod";

const UpStreamTransportaion = ({ upStreamOrDown }) => {
  console.log("🚀 ~ UpStreamTransportaion ~ upStreamOrDown:", upStreamOrDown);
  // name1: "Fuel-Based Method",
  // name2: "Distance-Based Method",
  // name3: "Spend-Based Method",
  const [activeMethod, setActiveMethod] = useState(methods[0].name);

  return (
    <div>
      <div className="grid lg:grid-cols-3 gap-4">
        {methods.map((method) => (
          <button
            key={method.name}
            title={method.name}
            className={`relative flex flex-col gap-2 text-left text-lg font-semibold py-4 px-6 transition-all rounded-lg border-2 ${
              activeMethod === method.name
                ? "active-tab text-white"
                : "text-[#5A5A5A] border-gray-300 hover:bg-gray-100"
            }`}
            onClick={() => setActiveMethod(method.name)}
          >
            <div className="font-bold">{method.name}</div>
            <div className="text-sm text-gray-800 font-normal">
              {method.description}
            </div>
          </button>
        ))}
      </div>

      {activeMethod == "Fuel-Based Method" && <FuelBasedMethod />}
      {activeMethod == "Distance-Based Method" && <DistanceBasedMethod />}
      {activeMethod == "Spend-Based Method" && <SpendBasedMethod />}
    </div>
  );
};

export default UpStreamTransportaion;
