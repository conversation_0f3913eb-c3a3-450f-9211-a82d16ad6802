import { useState } from "react";
import axios from "axios";
import Cookies from "js-cookie";
import { useGetData } from "./useGetData";

export const useDeleteData = () => {
  const [loading, setLoading] = useState(false);
  const { handleGetData } = useGetData();

  const handleDeleteData = async (id) => {
    setLoading(true);
    try {
      const { data } = await axios.delete(
        `https://pdf-extraction-staging.azurewebsites.net/delete/${id}`,
        {
          headers: {
            "Content-Type": "application/json",
            withCredentials: true,
            Authorization: `Bearer ${Cookies.get("level_user_token")}`,
          },
        }
      );
      console.log(data);
      setLoading(false);
      handleGetData();
    } catch (error) {
      console.log(error);
      setLoading(false);
    }
  };

  return { loading, handleDeleteData };
};
