
definitions:
  services:
    docker:
      memory: 4066

pipelines:
  branches:
    main:
      - step:
          name: "Build and Push Docker Image"
          size: 2x  # Increase memory
          deployment: staging
          services:
            - docker
          script:
            - docker system prune -a -f
            - export IMAGE_NAME=diagnostic-ui-jenkins
            - docker build -t $IMAGE_NAME -f UI/Dockerfile ./UI
            - docker tag $IMAGE_NAME $ACR_USERNAME.azurecr.io/diagnostic-ui-jenkins:staging
            - echo "$ACR_PASSWORD" | docker login $ACR_USERNAME.azurecr.io --username $ACR_USERNAME --password-stdin
            - docker push $ACR_USERNAME.azurecr.io/diagnostic-ui-jenkins:staging
