import React, { useEffect, useState } from "react";
import RadarChart from "./Charts/RadarChart";
// import LineChart from "./Charts/LineChart";
import QuestionRes from "./QuestionRes";
import { Link } from "react-router-dom";
import useAxios from "@/hooks/useAxios";
import Loading from "./Loading";

const GridChart = () => {
  const [catsAnalysis, setCatsAnalysis] = useState(null);
  const [reportData, setReportData] = useState(null);

  const { data, loading, error } = useAxios({
    url: "/get_report",
    method: "POST",
  });

  useEffect(() => {
    data && setCatsAnalysis(data.resultCategorySet);
    data && setReportData(data);
  }, [loading]);

  const renderQuestionNum = (questionNumArr) => {
    return questionNumArr.map((questionNum) => "Q" + questionNum.questionId);
  };

  const renderQuestionData = (questionDataArr) => {
    return questionDataArr.map((questionData) => questionData.responseOption);
  };

  const handleRedirect = () => {
    window.location.href = "/diagnosis/1";
  };

  return !loading ? (
    catsAnalysis ? (
      <div className="relative">
        <div className="flex-grow px-2">
          <div className="grid grid-cols-2 gap-4 p-4 pb-0">
            <div className="bg-gray-200/30 backdrop-blur-md hover:scale-[1.01] transition-all shadow-md p-4 rounded-xl">
              <h2 className="mb-4 font-semibold text-2xl">
                Your Sustainability Maturity Assessment Results
              </h2>
              <p>
                Following the analysis of your provided inputs, we are pleased
                to present your assessment outcomes.
              </p>
              <div className="h-fit flex justify-end mt-4">
                <a
                  href={reportData.pdf_url}
                  target="_blank"
                  className="bg-[#2b939c] text-2xl text-white px-6 py-3 float-end rounded-lg active:scale-95 transition-all"
                >
                  Download Report
                </a>
              </div>
            </div>
            <div className="bg-gray-200/30 backdrop-blur-md hover:scale-[1.01] transition-all shadow-md p-4 rounded-xl">
              <div className="flex flex-col justify-between mb-8">
                <h3 className="font-semibold text-2xl mb-4">
                  Current Assessment
                </h3>
                <div
                  onClick={handleRedirect}
                  className="bg-[#2b939c] text-xl py-1 px-2 rounded-md text-white cursor-pointer w-fit"
                >
                  Resume
                </div>
              </div>
              <div className="opacity-60">
                <h3 className="font-semibold text-2xl mb-4">
                  Prevous Assessments
                </h3>
                <div></div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4 p-4">
            <div className="bg-gray-200/30 backdrop-blur-md cursor-zoom-in select-none hover:scale-[1.01] active:scale-150 active:translate-x-1/2 active:z-50 active:-translate-y-1/2 transition-all shadow-md p-4 h-[300px] rounded-xl">
              <RadarChart
                labels={renderQuestionNum(catsAnalysis[1].questionObjects)}
                data={renderQuestionData(catsAnalysis[1].questionObjects)}
                label="Environmental"
              />
            </div>

            <div className="bg-gray-200/30 backdrop-blur-md cursor-zoom-in select-none hover:scale-[1.01] active:scale-150 active:-translate-y-1/2 active:z-50 transition-all shadow-md p-4 h-[300px] rounded-xl">
              <RadarChart
                labels={renderQuestionNum(catsAnalysis[2].questionObjects)}
                data={renderQuestionData(catsAnalysis[2].questionObjects)}
                label="Social"
                backgroundColor="rgba(10,111,133, 0.2)"
                borderColor="rgb(10,111,133)"
              />
            </div>

            <div className="bg-gray-200/30 backdrop-blur-md cursor-zoom-in select-none hover:scale-[1.01] active:scale-150 active:-translate-x-1/2 active:z-50 active:-translate-y-1/2 transition-all shadow-md p-4 h-[300px] rounded-xl">
              <RadarChart
                labels={renderQuestionNum(catsAnalysis[3].questionObjects)}
                data={renderQuestionData(catsAnalysis[3].questionObjects)}
                label="Governance"
                backgroundColor="rgba(17,43,101, 0.2)"
                borderColor="rgba(17,43,101, 1)"
              />
            </div>
          </div>
        </div>

        {/*<div className="flex flex-col">
          {catsAnalysis
            .flatMap((cat) => cat.questionObjects)
            .map((question, questionIndex) => (
              <QuestionRes
                questionText={question.questionObjects.questionText}
                {...question}
                key={questionIndex}
              />
            ))}
        </div>*/}
      </div>
    ) : (
      <div>
        <span>You should finish your assessment first</span>
        <Link
          to="/summary"
          className="bg-[#2b939c] min-w-[100px] text-2xl text-white px-6 py-3 float-end rounded-lg active:scale-95 transition-all"
        >
          Go to Summary
        </Link>
      </div>
    )
  ) : (
    <Loading />
  );
};

export default GridChart;
