import fileAdd from '@/assets/images/file-add.svg';
import { Group, Text, rem, useMantineTheme } from '@mantine/core';
import { showNotification } from '@mantine/notifications';
import { IconDownload, IconX, IconX as IconXNotification } from '@tabler/icons-react';
import { useDropzone } from 'react-dropzone';
import { IoCloseCircleSharp } from 'react-icons/io5';
import classes from './UploadFile.module.css';

export default function UploadFile({ onFilesChange, files }) {
  const theme = useMantineTheme();

  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt'],
    },
    maxSize: 10 * 1024 ** 2,
    onDrop: (acceptedFiles) => {
      const newFiles = [...files, ...acceptedFiles];

      if (newFiles.length > 2) {
        showNotification({
          title: 'Error',
          message: 'You can only upload a maximum of two files.',
          color: 'red',
          icon: <IconXNotification />,
        });
        return;
      }

      const totalSize = newFiles.reduce((acc, file) => acc + file.size, 0);

      if (totalSize > 10 * 1024 ** 2) {
        showNotification({
          title: 'Error',
          message: 'Total size of files must not exceed 10MB.',
          color: 'red',
          icon: <IconXNotification />,
        });
        return;
      }

      onFilesChange(newFiles);
    },
  });

  const handleRemoveFile = (e, fileName) => {
    e.preventDefault();
    e.stopPropagation();
    const updatedFiles = files.filter((file) => file.name !== fileName);
    if (onFilesChange) {
      onFilesChange(updatedFiles);
    }
  };

  return (
    <>
      <div className={`${classes.wrapper} cursor-pointer`}>
        {files.length < 2 && (
          <div
            {...getRootProps({
              className: `${classes.dropzone} ${isDragActive ? 'active' : ''} ${isDragReject ? 'reject' : ''}`,
            })}
          >
            <input {...getInputProps()} />
            <div>
              <Group justify="center">
                {isDragActive && !isDragReject ? (
                  <IconDownload style={{ width: rem(50), height: rem(50) }} color={theme.colors.blue[6]} stroke={1.5} />
                ) : isDragReject ? (
                  <IconX style={{ width: rem(50), height: rem(50) }} color={theme.colors.red[6]} stroke={1.5} />
                ) : (
                  <img src={fileAdd} alt="add a File" className="cursor-pointer w-12 h-12" />
                )}
              </Group>
              <Text ta="center" fw={500} fz="md" my="sm" className="text-slate-400 underline">
                {isDragActive && !isDragReject ? 'Drop files here' : isDragReject ? 'Invalid file type or size' : 'Click to upload'}
              </Text>
              <Text ta="center" fz="sm" mt="xs" c="dimmed">
                <span className="border p-1 rounded-lg me-2 bg-[#07838F1A] text-primary">PDF</span>
                <span className="border p-1 rounded-lg me-2 bg-[#07838F1A] text-primary">DOCX</span>
                <span className="border p-1 rounded-lg me-2 bg-[#07838F1A] text-primary">TXT</span>
                <span className="border border-[#07838F1A] p-1 rounded-lg me-2 bg-transparent text-primary">{'>'} 10 MB</span>
              </Text>
            </div>
          </div>
        )}

        {files.length > 0 && (
          <ul className="w-full">
            {files.map((file, index) => (
              <li
                key={index}
                className="flex items-center justify-between mt-4 p-5 border border-primary  bg-transparent text-primary rounded-lg truncate w-full"
              >
                <span className="me-1">{index + 1} -</span>
                <h1
                  style={{
                    maxWidth: '100%',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }}
                >
                  {file.name}
                </h1>
                <button onClick={(e) => handleRemoveFile(e, file.name)} className=" ">
                  <IoCloseCircleSharp className="ms-2" />
                </button>
              </li>
            ))}
          </ul>
        )}
      </div>
    </>
  );
}
