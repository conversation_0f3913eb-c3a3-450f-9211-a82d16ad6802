import { forwardRef, useImperativeHandle, useRef } from 'react';
import { createPortal } from 'react-dom';

const Modal = forwardRef(function Modal({ title, children }, ref) {
  const dialog = useRef();
  useImperativeHandle(ref, () => {
    return {
      open() {
        dialog.current.showModal();
      },
      close() {
        dialog.current.close();
      },
    };
  });

  return createPortal(
    <dialog ref={dialog} className="p-3 flex flex-col rounded ">
      <form method="dialog" className="flex justify-between items-center my-2 px-3 gap-4">
        <p className="text-3xl font-bold">{title}</p>
        <button type="submit" className="bg-[#07848f7a] text-white hover:bg-primary duration-300 font-bold py-1 px-3 rounded">
          X
        </button>
      </form>
      {children}
    </dialog>,
    document.getElementById('modal')
  );
});

export default Modal;
