import { MdOutlineEditNote } from "react-icons/md";
import { useNavigate } from "react-router";
import { useSustain360Store } from "@/Store/Assessment/useSustain360Store";

function ReviewQuiz({ Id, quiz, icon, solved, numResolve, style , QuestionNumber}) {
    const navigate = useNavigate();
    const { setActiveAssesmentSection } = useSustain360Store(); 
    const handleRedirect = () => {
          let scope = 1;
          console.log(Id)
          const questionId = parseInt(Id, 10);
          if (questionId >= 1 && questionId <= 10) {
          scope = "General";
          } else if (questionId >= 11 && questionId <= 17) {
          scope = "Environment";
          } else if (questionId >= 18 && questionId <= 21) {
          scope = "Social";
          } else if (questionId >= 22 && questionId <= 28) {
          scope = "Governance";
          } else if (questionId === 29) {
          scope = "Finalize";
          }
          // /diagnosis/General/question/1
          setActiveAssesmentSection(scope,questionId);
          if (scope) {

          navigate(`/Insights-reporing/greensight/diagnosis`);
          // window.location.href = `/diagnosis/${scope}/question/${Id}`;
          }
      };

    return (
      <div
      className={`flex w-full text-[#57595A] text-[12px] items-center ${style}`}
      >
      <div className="w-[10%] text-center p-2">Q.{QuestionNumber}</div>
      <div className="w-[60%] p-2">{quiz}</div>
      <div className="w-[20%] text-center p-2 flex items-center justify-center">
        <span>{icon}</span>
        <div className="w-[60%]">
        <span>{numResolve} </span>
        <span>{solved}</span>
        </div>
      </div>

      <button
        className={`w-[10%] flex justify-center p-2 text-[20px] text-primary
              // {
              // solved === "solved"
              //   ? "text-gray-400 cursor-not-allowed"
              //   : "text-primary"
            // }
            `}
        onClick={handleRedirect}
        // disabled={solved === "solved"}
      >
        <MdOutlineEditNote />
      </button>
      </div>
    );
}

export default ReviewQuiz;
