// import RadarChart from "@/Components/Charts/RadarChart";
import { LineChart, RadarChart } from "@mantine/charts";
import { ScrollArea } from "@mantine/core";
import { useTranslation } from "react-i18next";

export default function ActivityData({ AD_Scores }) {
 const data = [
  {
   product: "Source (Physical)",
   sales: AD_Scores?.source_physical,
  },
  {
   product: "Source (Service)",
   sales: AD_Scores?.source_service,
  },
  {
   product: "Location",
   sales: AD_Scores?.location,
  },
  {
   product: "Age ",
   sales: AD_Scores?.age,
  },
  {
   product: "Technology",
   sales: AD_Scores?.technology,
  },
  {
   product: "Completeness",
   sales: AD_Scores?.completeness,
  },
 ];
 const data2 = [
  {
   date: "Source (Physical)",
   Emissions: AD_Scores?.source_physical,
  },
  {
   date: "Source (Service)",
   Emissions: AD_Scores?.source_service,
  },
  {
   date: "Location",
   Emissions: AD_Scores?.location,
  },
  {
   date: "Age ",
   Emissions: AD_Scores?.age,
  },
  {
   date: "Technology",
   Emissions: AD_Scores?.technology,
  },
  {
   date: "Completeness",
   Emissions: AD_Scores?.completeness,
  },
 ];
 // console.log(AD_Scores);
 const CustomTick = (props) => {
  const { x, y, payload } = props;
  return (
   <text
    x={x}
    y={y}
    fill={getColor1(payload.value)} // تغيير اللون بناءً على الاسم
    textAnchor="middle"
    fontSize={12}
   >
    {payload.value}
   </text>
  );
 };
 const { t } = useTranslation();
 const getColor = (label) => {
  const colors = {
   "Source (Physical)": "#FF6007",
   "Source (Service)": "#298BED",
   Location: "#07838F",
   "Age ": "#9160C1",
   Technology: "#FFAB07",
   Completeness: "#00C0A9",
  };
  return colors[label] || "black"; // اللون الافتراضي
 };

 return (
  <>
   <div className="grid lg:grid-cols-3 gap-5">
    <div className="bg-white rounded-lg shadow-md p-3 lg:col-span-1">
     <RadarChart
      h={300}
      data={data}
      dataKey="product"
      series={[{ name: "sales", color: "teal", strokeColor: "" }]}
     />
     {/* <RadarChart data={data}/> */}
    </div>
    <ScrollArea className="lg:col-span-2">
     <div className="bg-white rounded-lg shadow-md p-3 ">
      <div>
       <LineChart
        className="py-8 px-4 w-[800px] md:w-auto"
        h={300}
        data={data2}
        dataKey="date"
        withDots={false}
        curveType="bump"
        series={[{ name: "Emissions", color: "#00C0A9" }]}
        xAxisProps={{
         tick: (props) => {
          const { x, y, payload } = props;
          return (
           <text
            x={x}
            y={y + 20}
            fill={getColor(payload.value)}
            className="text-xs cursor-pointer"
            textAnchor="middle"
           >
            {payload.value}
           </text>
          );
         },
        }}
       />
      </div>
     </div>
    </ScrollArea>
   </div>
  </>
 );
}
