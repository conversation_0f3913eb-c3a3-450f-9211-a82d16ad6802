import React from "react";
import { LineChart } from "@mantine/charts";

const EmissionsForecast = () => {
  const data = [
    {
      date: "2012",
      //   Worst: 40000,
      Emissions: 40000,
    },
    {
      date: "2013",
      //   Worst: 38000,
      Emissions: 35000,
    },
    {
      date: "2014",
      //   Worst: 36000,
      Emissions: 42000,
    },
    {
      date: "2015",
      //   Worst: 34000,
      Emissions: 44000,
    },
    {
      date: "2016",
      //   Worst: 32000,
      Emissions: 40000,
    },
    {
      date: "2017",
      //   Worst: 30000,
      Emissions: 30000,
    },
    {
      date: "2018",
      //   Worst: 28000,
      Emissions: 20000,
    },
    {
      date: "2019",
      //   Worst: 26000,
      Emissions: 28000,
    },
    {
      date: "2020",
      //   Worst: 24000,
      Emissions: 35000,
    },
    {
      date: "2021",
      //   Worst: 22000,
      Emissions: 36000,
    },
    {
      date: "2022",
      //   Worst: 20000,
      Emissions: 30000,
    },
    {
      date: "2023",
      //   Worst: 18000,
      Emissions: 32000,
    },
    {
      date: "2024",
      //   Worst: 16000,
      Emissions: 35000,
    },
    {
      date: "2025",
      //   Worst: 16000,
      Emissions: 35000,
    },
    {
      date: "2026",
      //   Worst: 16000,
      Emissions: 40000,
    },
    {
      date: "2027",
      //   Worst: 16000,
      Emissions: 35000,
    },
    {
      date: "2028",
      //   Worst: 16000,
      Emissions: 30000,
    },
    {
      date: "2029",
      //   Worst: 16000,
      Emissions: 32000,
    },
    {
      date: "2030",
      //   Worst: 16000,
      Emissions: 38000,
    },
    {
      date: "2031",
      Worst: 35000,
      Expected: 35000,
      Best: 35000,
      Emissions: 35000,
    },
    {
      date: "2032",
      Worst: 35000,
      Expected: 34000,
      Best: 30000,
      //   Emissions: 35000,
    },
    {
      date: "2033",
      Worst: 40000,
      Expected: 32000,
      Best: 29000,
      //   Emissions: 35000,
    },
    {
      date: "2034",
      Worst: 45000,
      Expected: 38000,
      Best: 28000,
      //   Emissions: 35000,
    },
    {
      date: "2035",
      Worst: 50000,
      Expected: 35000,
      Best: 25000,
      //   Emissions: 34000,
    },
    {
      date: "2036",
      Worst: 55000,
      Expected: 39000,
      Best: 21000,
      //   Emissions: 40000,
    },
    {
      date: "2037",
      Worst: 60000,
      Expected: 34000,
      Best: 18000,
      //   Emissions: 45000,
    },
    {
      date: "2038",
      Worst: 65000,
      Expected: 33000,
      Best: 14000,
      //   Emissions: 35000,
    },
    {
      date: "2039",
      Worst: 70000,
      Expected: 36000,
      Best: 10000,
      //   Emissions: 35000,
    },
    {
      date: "2040",
      Worst: 75000,
      Expected: 34000,
      Best: 7000,
      //   Emissions: 35000,
    },
    {
      date: "2041",
      Worst: 80000,
      Expected: 38000,
      Best: 2000,
      //   Emissions: 35000,
    },
  ];

  return (
    <>
      <div className="flex items-center justify-between p-2 mb-4">
        <h3 className="text-2xl font-semibold">Total Emissions</h3>
      </div>

      <LineChart
        className="bg-white py-8 px-4 rounded-lg shadow-lg"
        h={300}
        data={data}
        dataKey="date"
        withDots={false}
        curveType="bump"
        // dotProps={{ r: 2 }}
        // activeDotProps={{ r: 3, strokeWidth: 1 }}
        series={[
          //   { name: "Apples", color: "indigo.6" },
          { name: "Worst", color: "red.6", strokeDasharray: "5 5" },
          { name: "Expected", color: "yellow.6", strokeDasharray: "5 5" },
          { name: "Best", color: "teal.6", strokeDasharray: "5 5" },
          { name: "Emissions", color: "blue.6" },
        ]}
      />
    </>
  );
};

export default EmissionsForecast;
