import React, { useState } from "react";
import { Checkbox, Combobox, Group, Input, useCombobox } from "@mantine/core";
import { IoFilter } from "react-icons/io5";
import { CiFilter } from "react-icons/ci";

const groceries = ["Evidence", "Action Items", "Owner", "Due Date"];

export default function MultiselectFilter({ setValue, value }) {
  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
    onDropdownOpen: () => combobox.updateSelectedOptionIndex("active"),
  });

  const handleValueSelect = (val) => {
    setValue((current) =>
      current?.includes(val)
        ? current.filter((v) => v !== val)
        : [...current, val]
    );
  };

  const handleValueRemove = (val) => {
    setValue((current) => current.filter((v) => v !== val));
  };

  const options = groceries.map((item) => (
    <Combobox.Option value={item} key={item} active={value?.includes(item)}>
      <Group gap="lg">
        <Checkbox
          checked={value?.includes(item)}
          onChange={() => handleValueSelect(item)}
          aria-hidden
          tabIndex={-1}
          style={{ pointerEvents: "none" }}
        />
        <span>{item}</span>
      </Group>
    </Combobox.Option>
  ));

  return (
    <Combobox
      store={combobox}
      onOptionSubmit={(val) => handleValueSelect(val)}
      withinPortal={false}
      
    >
      <Combobox.DropdownTarget>
        <p
          className="font-semibold text-[#9C9C9C] w-full flex justify-center  items-center cursor-pointer py-1 text-nowrap"
          onClick={() => combobox.toggleDropdown()}
        >
        <CiFilter className="mx-2 text-primary" />Filter  Columns
        </p>
        {/* <p >Pick one or more values</p> */}
        {/* <Input onClick={() => combobox.toggleDropdown()} readOnly placeholder="Pick one or more values" /> */}
      </Combobox.DropdownTarget>

      <Combobox.Dropdown>
        <Combobox.Options>{options}</Combobox.Options>
      </Combobox.Dropdown>
    </Combobox>
  );
}
