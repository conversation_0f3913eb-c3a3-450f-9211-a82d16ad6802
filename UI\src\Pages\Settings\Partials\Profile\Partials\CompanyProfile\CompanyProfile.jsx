import { Button, Select, TextInput } from "@mantine/core";
import { useForm } from "@mantine/form";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router";
import { UserImg } from "../UserImg/UserImg";

export default function CompanyProfile() {
  const [disabled, setDisabled] = useState(true);
  const navigate = useNavigate();
  const form = useForm({
    mode: "uncontrolled",
    initialValues: {
      region: "",
      country: "",
      sector: "",
      no_of_Employees: "",
      locations: "",
      no_of_Assets: "",
    },
    validate: {
      //   user_name: isNotEmpty("User Name is required"),
    },
  });

  //   const updateUserInfo = async (values) => {
  //     setLoading(true);
  //     const { user_name, phoneNumber: phone_number, country } = values;
  //     const value = { user_name, phone_number, country };
  //     try {
  //       setLoading(true);
  //       const { data } = await ApiProfile.post("/update-user-information", value);
  //       if (data.message === "User information updated successfully") {
  //         showNotification({
  //           title: "Success",
  //           message: data.message,
  //           color: "teal",
  //           icon: <IconCheckNotification />,
  //         });
  //         setLoading(false);
  //         console.log(data);
  //         getUserData();
  //       }

  //       // console.log(data);
  //     } catch (error) {
  //       setLoading(true);
  //       if (
  //         error.response.data.message === "No changes detected, same old values"
  //       ) {
  //         setLoading(false);
  //         showNotification({
  //           title: "Error",
  //           message: error.response.data.message,
  //           color: "red",
  //           icon: <IconXNotification />,
  //         });
  //       }
  //       console.log(error);
  //     }
  //     setDisabled(true);
  //   };
  //   useEffect(() => {
  //     if (profileData) {
  //       form.setValues({
  //         user_name: profileData.userName || "",
  //         phoneNumber: profileData.phoneNumber || "",
  //         country: profileData.country || "",
  //         companyName: profileData.company_name || "",
  //         role: profileData.userRole || "",
  //       });
  //     }
  //   }, [profileData]);
  const { t } = useTranslation();
  return (
    <>
      <div className="p-5">
        <div className="">
          <UserImg
            userName={"aa"}
            // userEmail={profileData.email}
          />
        </div>

        <form onSubmit={form.onSubmit("")}>
          <div className="grid grid-cols-1 mt-5 sm:gap-x-5 md:gap-x-10 lg:gap-x-28 text-start">
            <Select
              {...form.getInputProps("region")}
              key={form.key("region")}
              type="text"
              label="Region"
              placeholder={
                // profileData.userName
                //   ? "Update your User Name"
                //   :
                "Enter your Region"
              }
              mb="md"
              disabled={disabled}
            />
            <Select
              {...form.getInputProps("country")}
              key={form.key("country")}
              mb="md"
              label="Country"
              //   data={["User", "Admin"]}
              placeholder="Enter your Country"
              //   defaultValue={profileData.userRole}
              disabled
            />
            <Select
              {...form.getInputProps("sector")}
              key={form.key("sector")}
              mb="md"
              label="Sector"
              //   data={["User", "Admin"]}
              placeholder="Enter your Sector"
              //   defaultValue={profileData.userRole}
              disabled
            />
            <TextInput
              {...form.getInputProps("no_of_Employees")}
              key={form.key("no_of_Employees")}
              type="text"
              label="No. of Employees "
              placeholder="Enter your No. of Employees"
              mb="md"
              disabled
            />
            <TextInput
              {...form.getInputProps("locations")}
              key={form.key("locations")}
              type="text"
              label="Locations"
              placeholder={
                // profileData.country
                //   ? "Update your Country"
                //   :
                "Enter your Locations"
              }
              mb="md"
              disabled={disabled}
            />
            <TextInput
              {...form.getInputProps("no_of_Assets")}
              key={form.key("no_of_Assets")}
              type="text"
              label="No. of Assets "
              placeholder={
                // profileData.phoneNumber
                //   ? "Update your Phone Number"
                //   :
                "Enter your Phone No. of Assets"
              }
              mb="md"
              disabled={disabled}
            />
          </div>
        </form>
        <div>
          <div className="justify-end col-span-1 my-20 lg:flex">
            <Button
              className="bg-primary hover:bg-primary"
              size="md"
              onClick={() => {
                navigate("/Settings/CompanyProfile/Configurations/Assets");
                // setDisabled(false);
              }}
            >
              {t("Edit Configurations")}
            </Button>
            {/* {disabled ? (
              <Button
                className="bg-primary hover:bg-primary"
                size="md"
                onClick={() => {
                  setDisabled(false);
                }}
              >
                {Loading ? (
                  <PiSpinnerLight className="text-2xl animate-spin" />
                ) : (
                  "Edit Configurations"
                )}
              </Button>
            ) : (
              <Button
                className="bg-primary hover:bg-primary"
                size="md"
                onClick={form.onSubmit(updateUserInfo)}
              >
                {loading ? (
                  <PiSpinnerLight className="text-2xl animate-spin" />
                ) : (
                  "Update"
                )}
              </Button>
            )} */}
          </div>
        </div>
      </div>
    </>
  );
}
