// export const data = generateData();

// function generateData() {
//   const data = [];

//   for (let year = 2021; year <= 2044; year++) {
//     const newData = {
//       date: year.toString(),
//       Scope1: getRandomNumberInRange(8000, 50000),
//       Scope2: getRandomNumberInRange(1000, 50000),
//       Scope3: getRandomNumberInRange(1000, 50000),
//     };

//     data.push(newData);
//   }

//   return data;
// }

// // Helper function to generate random number within a range
// function getRandomNumberInRange(min, max) {
//   return Math.floor(Math.random() * (max - min + 1)) + min;
// }
export const data = [
  // {
  //   date: "2022",
  //   // Scope1: 1800,
  //   // Scope2: 2338,
  //   // Scope3: 2452,
  // },
  // {
  //   date: "2023",
  //   Scope1: 1850,
  //   Scope2: 1800,
  //   Scope3: 2402,
  // },

  // data.js

  { date: '2024', Scope1: 15000, Scope2: 25000, Scope3: 23000 },
  { date: '2025', Scope1: 20000, Scope2: 30000, Scope3: 28000 },
  { date: '2026', Scope1: 25000, Scope2: 35000, Scope3: 32000 },
  { date: '2027', Scope1: 30000, Scope2: 40000, Scope3: 37000 },
  { date: '2028', Scope1: 35000, Scope2: 45000, Scope3: 42000 },
  { date: '2029', Scope1: 40000, Scope2: 50000, Scope3: 47000 },
  { date: '2030', Scope1: 45000, Scope2: 55000, Scope3: 52000 },
  { date: '2040', Scope1: 50000, Scope2: 60000, Scope3: 62000 },
  { date: '2050', Scope1: 60000, Scope2: 70000, Scope3: 72000 },

];
