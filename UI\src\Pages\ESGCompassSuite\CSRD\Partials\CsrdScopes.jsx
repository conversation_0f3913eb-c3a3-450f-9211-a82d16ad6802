import React from "react";
import { useCsrdContext } from "@/Contexts/CsrdContext";
import { RingProgress, Text } from "@mantine/core";
import { useNavigate } from "react-router";
import { TbHeartHandshake } from "react-icons/tb";
import { MdSocialDistance } from "react-icons/md";
import { IoIosGlobe } from "react-icons/io";
import { CgArrowsExchangeAlt } from "react-icons/cg";


const CsrdScopes = () => {
  // const { t } = useTranslation();
  const { CSRDScopesData, setSelectedScope } = useCsrdContext();
  const navigate = useNavigate();

  // This Array is for Custom styles for each category & its icon
  const scopesStyles = {
    "Cross-cutting Standards": {
      color: "298BED21",
      textColor: "298BED",
      icon: <CgArrowsExchangeAlt className="w-10 h-10 me-3" />,
    },
    Environment: {
      color: "D9EEEB",
      textColor: "00C0A9",
      icon: <IoIosGlobe className="w-10 h-10 me-3" />,
    },
    Social: {
      color: "9160C121",
      textColor: "9160C1",
      icon: <MdSocialDistance className="w-10 h-10 me-3" />,
    },
    Governance: {
      color: "F9E5C2",
      textColor: "FFAB07",
      icon: <TbHeartHandshake className="w-10 h-10 me-3" />,
    },
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 my-11">
      {/* This map is to render each category(cross-cutting,enviroment,social,governance) */}
      {CSRDScopesData?.map((item, index) => {
        const style = scopesStyles[item.category] || {};
        return (
          <div key={index} className="my-3 mx-4">
            <div
              className="py-4 px-1 bg-gradient-to-r from-cyan-600 to-teal-500 rounded-2xl grid justify-center items-center gap-5 text-xl text-white font-semibold h-15 text-center"
              style={{ backgroundColor: `#${style.color}` }}
            >
              <p className="text-wrap text-base flex items-center justify-center">
                {style.icon} {item.category}
              </p>
            </div>

            {/* 
            mapping over each category (cross-cutting,enviroment,social,governance) 
            to render its corresponding assessments names(ESRS 1, ESRS E1, ...etc) 
            and titles(General requirements, Business conduct, ...etc) 
             */}
            <div className="mt-4">
              {item.scope.map((scope, idx) => (
                <React.Fragment key={idx}>
                  <div
                    className="text-black flex items-center w-full cursor-pointer ease-out duration-200 hover:-translate-y-2 my-2 rounded-full"
                    style={{ backgroundColor: `#${style.color}` }}
                    onClick={() => {
                      setSelectedScope({ title: item.category, scope });
                      navigate(
                        `selectedScope/${item.category}/${scope.title}`
                      );
                    }}
                  >
                    <div className="flex items-center py-1 px-1 w-full">
                      <RingProgress
                        size={70}
                        thickness={8}
                        className="outline outline-4 outline-white rounded-full"
                        rootColor={`${
                          !scope.percentage ? `transparent` : "white"
                        }`}
                        
                        sections={[
                          {
                            value: `${!scope.percentage ? 0 : scope.percentage}`,
                            color: `#${style.textColor}`,
                          },
                        ]}
                        label={
                          <Text
                            size={"11px"}
                            className="flex justify-center items-center font-bold"
                            style={{
                              pointerEvents: "none",
                              color: `#${style.textColor}`,
                            }}
                          >
                            {`${scope.percentage ? `${scope.percentage}%` : "Start"}`}
                          </Text>
                        }
                      />
                      <p
                        className="font-bold text-xl w-full text-center me-5"
                        style={{ color: `#${style.textColor}` }}
                      >
                        {scope.title}
                      </p>
                    </div>
                  </div>
                  <p className="font-bold text-base text-[#404B52] text-center lg:ms-10 text-wrap w-10/12">
                    {scope.name}
                  </p>
                </React.Fragment>
              ))}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default CsrdScopes;
