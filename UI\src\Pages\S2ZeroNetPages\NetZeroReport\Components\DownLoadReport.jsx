import React from "react";
import { useTranslation } from "react-i18next";
import { FiDownload } from "react-icons/fi";
import { Link } from "react-router-dom";

const DownLoadReport = ({ pdfUrl, linkStyle }) => {
  const { t } = useTranslation();

  return (
    <Link
      to={pdfUrl}
      download
      rel="noopener noreferrer"
      className={linkStyle}
    >
      <span>{t("downloadReport")}</span>
      <span>
        <FiDownload className="text-lg" />
      </span>
    </Link>
  );
};

export default DownLoadReport;
