import React, { useState } from "react";
import { But<PERSON>, Modal } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { RxQuestionMarkCircled } from "react-icons/rx";
import RiskCalculation from "../../GreenWashingRiskAssessment/Components/RiskCalculation";
import ApexChart from "./HeatMap/ApexChart";
import AssessmentMartrixChart from "./AssessmentMartrixChart";

export default function AssessmentMatrix() {
  const [opened, { open, close }] = useDisclosure(false);

  return (
    <div>
      <div className="flex justify-end">
        <Button
          className="text-[#07838F] underline font-semibold"
          variant="transparent"
          onClick={open}
        >
          <RxQuestionMarkCircled /> How is risk calculated?
        </Button>
      </div>
      <Modal
        size={"90%"}
        opened={opened}
        onClose={close}
        withCloseButton={false}
      >
        <RiskCalculation />
      </Modal>

      <div className="flex justify-center px-10  py-10 rounded-lg shadow-lg bg-[#fff]">
        <div className="w-[80%]">
          {/* <ApexChart /> */}
          <AssessmentMartrixChart />
        </div>
      </div>
    </div>
  );
}
