
import Loading from '@/Components/Loading';
import { Link } from 'react-router-dom';

export default function RiskHeatMap({ risks = [],loading,impactSeverity='impactSeverity' }) {
  // console.log("🚀 ~ RiskHeatMap ~ risks:", risks)
  // Calculate position for a risk dot on the heatmap
  const calculatePosition = (impact, likelihood) => {
    const score = impact * likelihood;
    const xPercentage = ((score - 1) / 24) * 100;
    const yPercentage = ((5 - likelihood) / 4) * 100;
    return { x: `${xPercentage}%`, y: `${yPercentage}%` };
  };

  const getRiskLevel = (impact, likelihood) => {
    const score = impact * likelihood;
    switch (true) {
      case score >= 16 && score <= 25:
        return 'CRITICAL';
      case score >= 10 && score <= 15:
        return 'HIGH';
      case score >= 4 && score <= 9:
        return 'MEDIUM';
      case score >= 1 && score <= 3:
        return 'LOW';
      default:
        return '';
    }
  };

  // Get color class based on risk level
  const getRiskLevelColor = (impact, likelihood) => {
    const level = getRiskLevel(impact, likelihood);
    switch (level) {
      case 'CRITICAL':
        return 'bg-red-600';
      case 'HIGH':
        return 'bg-orange-500';
      case 'MEDIUM':
        return 'bg-yellow-400';
      case 'LOW':
        return 'bg-green-500';
      default:
        return 'bg-gray-400';
    }
  };

  // Group risks by impact and likelihood
  const groupedRisks = risks.reduce((acc, risk) => {
    if (!risk[impactSeverity]?.value || !risk.likelihoodId?.value) return acc;

    const key = `${risk[impactSeverity].value}-${risk.likelihoodId.value}`;
    if (!acc[key]) {
      acc[key] = {
        impact: risk[impactSeverity].value,
        likelihood: risk.likelihoodId.value,
        risks: [],
      };
    }
    acc[key].risks.push(risk);
    return acc;
  }, {});

  return (
    <div className="flex flex-col space-y-4 squar-chart rounded-2xl p-4">
        {
            loading ? <div className='full-center w-16 m-auto bg-primary/50 z-30 h-full rounded-xl p-1'> <div className='submit-loader' /> </div>: ''
        }
      <div className="relative w-full h-[500px] rounded-xl py-3">
        {/* Gradient background */}
        <div
          className="absolute inset-0 rounded-xl py-3"
          style={{
            background:
              'linear-gradient(79.55deg, #02ae50 0%, #bdba16 26.5%, #ffbe00 54.5%, #fd6418 83%, #f72e24 100%)',
          }}
        ></div>

        {/* Risk dots with tooltips */}
        {Object.values(groupedRisks).map((group, index) => {
          const position = calculatePosition(group.impact, group.likelihood);
          const score = group.impact * group.likelihood;
          const isHighScore = score > 20;

          return (
            <div
              key={index}
              className="absolute w-4 h-4 rounded-full bg-white border border-gray-800 cursor-pointer transform -translate-x-1/2 -translate-y-1/2 hover:scale-125 transition-transform group"
              style={{ left: position.x, top: position.y }}
            >
              {/* Tooltip showing all risks at this position */}
              <div
                className={`absolute z-[1000] bg-white rounded-md shadow-lg p-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-opacity duration-200 min-w-max ${
                  isHighScore
                    ? 'right-2 top-1/2 -translate-y-1/2'
                    : 'left-2 top-1/2 -translate-y-1/2'
                }`}
              >
                <p className="text-xs text-gray-600 mb-1">
                  Impact: {group.impact} | Likelihood: {group.likelihood}
                </p>
                <span
                  className={`px-2 py-1 rounded text-xs text-white ${getRiskLevelColor(
                    group.impact,
                    group.likelihood
                  )}`}
                >
                  {getRiskLevel(group.impact, group.likelihood)}
                </span>
                <div className="mt-2 h-20 overflow-auto">
                  {
                    impactSeverity !== 'impactId'? 
                    <>
                    {group.risks.map((risk) => (
                      <Link
                        key={risk._id}
                        to={`/green-shield/financial/ESG-risk-management/main/systems/iro-assessment/${risk._id}?type=view`}
                        className="block text-xs text-blue-600 hover:underline"
                      >
                        <p>Go to the Risk</p>
                        <p>Risk Id : {risk.riskId}</p>
                        <p>Event : {risk.event}</p>
                        <p>Risk Level: {risk.inherentRisk}</p>
                        <p>Residual: {risk.residualRisk}</p>
                          <hr />
                      </Link>
                    ))}
                    </>:
                    <>
                     {group.risks.map((risk) => (
                      <div
                      key={risk._id}
                        className="block text-xs text-blue-600 hover:underline"
                      >
                        <p>Risk Id : {risk.riskId}</p>
                        <p>Residual: {risk.residualRisk}</p>
                          <hr />
                      </div>
                    ))}
                    </>

                  }
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}