import React, { useEffect, useState } from "react";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import logo from "/assets/Images/logo.png";

function SignUp() {
  const [loginState, setLoginState] = useState({
    email: "",
    password: "", 
  });

  useEffect(() => {
    //function to execute when Enter is pressed
    function handleKeyPress(e) {
      if (e.key === "Enter") {
        login();
      }
    }

    //adding the event listener
    window.addEventListener("keypress", handleKeyPress);

    //cleanup function to execute when the component is unmounting
    return () => {
      //removing the event listener
      window.removeEventListener("keypress", handleKeyPress);
    };
  }, [loginState]);

  const navigate = useNavigate();

  const loginInputHandle = (e) => {
    const { name, value } = e.target;
    setLoginState((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  const login = async () => {
    try {
      const response = await axios.post(
        "https://diagnostics-api-s1.azurewebsites.net/company/login",
        loginState
      );
      if (response.status === 200) {
        // Handle successful login
        toast.success("Login successful");
        //console.log(response.data.company_id);
        localStorage.setItem("company_id", response.data.company_id);
        navigate("/get-started");
      }
    } catch (error) {
      // Handle login error
      toast.error("Login failed. Please check your credentials and try again.");
      console.error(error);
    }
  };

  return (
    <div className="flex flex-col bg-gradient-to-r h-screen from-[#112b65] to-[#02c2ab] px-[5%]">
      <div className="flex items-center gap-4 text-3xl font-semibold text-white pt-10">
        <img src={logo} alt="LevelUp logo" className="w-16" />
        LevelUp ESG
      </div>
      <ToastContainer />
      <div className="md:flex w-full items-center my-auto">
        <div className="md:w-1/2 my-auto h-full md:px-16">
          <h1 className="text-3xl font-bold text-white mb-4">Login</h1>
          <div>
            <div className="flex flex-col mb-2">
              <label htmlFor="email" className="text-white">
                Enter Your Email
              </label>
              <input
                value={loginState.email}
                onChange={loginInputHandle}
                className="p-2 border border-slate-400 mt-1 outline-0 focus:border-[#02c1aa] rounded-md"
                type="email"
                name="email"
                placeholder="Your email"
                id="email"
              />
            </div>
            <div className="flex flex-col mb-2">
              <label htmlFor="password" className="text-white">
                Password
              </label>
              <input
                value={loginState.password}
                onChange={loginInputHandle}
                className="p-2 border border-slate-400 mt-1 outline-0 focus:border-[#02c1aa] rounded-md"
                type="password"
                name="password"
                placeholder="Password"
                id="password"
              />
            </div>
            <div className="mt-4 gap-3 flex justify-center items-center">
              <button
                onClick={login}
                className="px-3 py-2 text-lg rounded-md w-full text-white bg-[#02c1aa] hover:bg-[#2be4ce]"
              >
                Submit
              </button>
            </div>
            <p className="font-semibold text-lg mt-5 text-blue-200 ">
              By logging in, you agree to our
              <a
                className="pl-1 underline underline-offset-2"
                href="https://levelupesg.co/legal/privacy-policy"
              >
                Terms and Conditions
              </a>
            </p>
          </div>
        </div>
        <div className="flex flex-col max-md:hidden items-center w-1/2 px-16 h-full ">
          <h2 className="font-bold text-3xl mb-4 text-white">
            Welcome to LevelUp ESG. Your Intelligent Path to Net Zero &
            Sustainable Growth
          </h2>
        </div>
      </div>
    </div>
  );
}

export default SignUp;
