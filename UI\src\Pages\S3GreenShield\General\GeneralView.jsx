import React from "react";

import S3Layout from "@/Layout/S3Layout";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import S3ComingSoonView from "@/Pages/S2ZeroNetPages/ComingSoon/S3ComingSoonView";

import MainCard from "./Partials/MainCard";
import firstImg from "@/assets/images/FinancialServices_bg.png";
import secImg from "@/assets/images/nonFinancialbg.png";
import { Link } from "react-router-dom";
import { Image } from "@mantine/core";

export default function GeneralView() {
  const { greenShieldMenu } = useSideBarRoute();

  const data = [
    {
      name: "Financial Services",
      details:
        "Optimise ESG risk management and compliance for banks, investors, and insurers.",
      img: firstImg,
      link: "/green-shield/financial-service-start",
    },
    {
      name: "Non-Financial Services",
      details:
        "Streamline ESG performance and risk mitigation across diverse industries.",
      img: secImg,
      link: "/green-shield/nonFinancial-services-start",
    },
  ];
  return (
    <>
      <S3Layout
        menus={greenShieldMenu}
        navbarTitle="GreenShield"
        // breadcrumbItems={[
        //     { title: "Launchpad", href: "/get-started" },
        //     { title: "GreenShield", href: "/green-shield/general" },
        // ]}
      >
        <div className="bg-white font-semibold text-2xl shadow-lg rounded-lg p-4 -mt-6 mb-4 flex justify-center items-center ">
          Select Your Service Area
        </div>

        <div className="flex justify-center gap-20 ">
          {data.map((el, i) => (
            <Link
              to={el.link}
              key={i}
              className={`group card w-[350px] rounded-lg overflow-hidden h-[659px] relative`}
            >
              <div className="layout bg-black opacity-50 w-full h-full absolute top-0 left-0 z-10 "></div>

              <div className="layoutContent group bg-inherit w-full h-full absolute top-0 left-0 text-white px-3 z-20">
                <h1 className="layoutTittle font-inter text-center md:text-start text-[30px] lg:text-[40px] font-bold md:px-[30px] pt-10 md:pt-[52px]">
                  {el.name}
                </h1>
              </div>
              <div className="layoutParagraph absolute top-56 z-20 text-white font-medium text-2xl md:px-[32px] transform translate-y-12 opacity-0 transition-all duration-[500ms] ease-in group-hover:translate-y-10 group-hover:opacity-100 text-center md:text-start text-[15px] lg:text-[21px] tracking-widest">
                <p className="md:leading-7">{el.details}</p>
              </div>
              <div className="card-img w-full h-full">
                <Image
                  src={el.img}
                  className="w-full h-full object-cover snap-center"
                  loading="lazy"
                  width={1000}
                  height={1000}
                />
              </div>
            </Link>
          ))}
        </div>
      </S3Layout>
    </>
  );
}
