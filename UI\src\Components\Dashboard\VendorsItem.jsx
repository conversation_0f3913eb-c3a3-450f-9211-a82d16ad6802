import React from "react";
import  PropType from 'prop-types';

function VendorsItem({ img, name, country , num,icon}) {
  return (
    <div className="flex justify-between items-center border-b-[1px] pb-6 mt-4">
      <div className="flex items-center gap-[1rem]">
        <div className="w-[50px] h-[50px] rounded-full overflow-hidden">
          <img src={img} className="w-full h-full object-cover" />
        </div>
        <div>
          <div className='text-[16px] font-light'>{name}</div>
          <div className="text-[15px] text-[#D9D9D9]">{country}</div>
        </div>
      </div>
      <div className="flex items-center gap-[1rem]">
        <div className="font-bold text-[18px]">{num} Kg</div>
        <div className="text-red-700 text-[18px]">{icon}</div>
      </div>
    </div>
  );
}

VendorsItem.propTypes = {
  img: PropType.string,
  name: PropType.string,
  country: PropType.string,
  num: PropType.string,
  icon: PropType.element
};

export default VendorsItem;
