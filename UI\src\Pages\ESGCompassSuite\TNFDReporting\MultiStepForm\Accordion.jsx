import { useState, useEffect } from "react";
import { Accordion } from "@/Components/ui/accordion";
import { AccordionItemComponent } from "./AccordionItemComponent";
import { ProgressBar } from "@/Components/MultiStepForm/ProgressBar";
import { CiEdit } from "react-icons/ci";
import { fetchAssessmentData } from "@/Api/assessmentApi";
import Cookies from "js-cookie";
import { toast } from "react-toastify";
import { Button, Menu } from "@mantine/core";
import { IoIosArrowDown } from "react-icons/io";
import { IoSettingsOutline } from "react-icons/io5";
import { Checkbox } from "@mantine/core";

export default function AccordionDemo({ onPreviousStep }) {
  const System = "TNFD";
  const [currentSectionIndex, setCurrentSectionIndex] = useState(0);
  const [allMarkedComplete, setAllMarkedComplete] = useState(false);
  const [sections, setSections] = useState([]);
  const [loading, setLoading] = useState(true);
  const [reportId, setReportId] = useState(null);
  const [isAssessmentCompleted, setIsAssessmentCompleted] = useState(false);
  const [detailsData, setDetailsData] = useState(null);

  const [visibility, setVisibility] = useState({
    details: true,
    evidence: true,
    framework: true,
    remarks: true,
    performance: true,
    qualityAssurance: true,
  });

  const fetchData = async () => {
    try {
      const { sections, id } = await fetchAssessmentData({ System });
      setSections(sections);
      setReportId(id);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching assessment data:", error);
      toast.error("Failed to fetch assessment data");
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
    detailsSection();
  }, []);

  const currentSection = sections[currentSectionIndex];

  const completedCount =
    currentSection?.topics?.reduce((count, topic) => {
      const isTopicComplete = (topic.disclosures || []).every((question) =>
        question.disclosure_text?.trim()
      );
      return count + (isTopicComplete ? 1 : 0);
    }, 0) || 0;

  useEffect(() => {
    if (sections.length > 0) {
      const allCompleteInCurrentSection =
        currentSection?.topics?.every((subsection) =>
          (subsection.disclosures || []).every((question) =>
            question.disclosure_text?.trim()
          )
        ) ?? false;
      setAllMarkedComplete(allCompleteInCurrentSection);
    }
  }, [currentSection, currentSectionIndex, sections]);

  const saveAssessment = async () => {
    const token = Cookies.get("level_user_token");
    if (!token) {
      toast.error("Authorization token is missing");
      return;
    }

    try {
      const response = await fetch(
        `https://issb-report-api-staging.azurewebsites.net/api/v1/reports/${reportId}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({ status: "Completed" }),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to save assessment: ${response.statusText}`);
      } else {
        setIsAssessmentCompleted(true);
      }

    } catch (error) {
      console.error("Error saving assessment:", error);
      toast.error(`Failed to save assessment: ${error.message}`);
    }
  };

  const generateAndDownloadFile = async (format) => {
    const token = Cookies.get("level_user_token");
    if (!token) {
      toast.error("Authorization token is missing");
      return;
    }
  
    try {
      const generateEndpoint = `https://issb-report-api-staging.azurewebsites.net/api/v1/reports/${reportId}/generate-${format}`;
      const downloadEndpoint = (fileId) =>
        `https://docvault-staging.azurewebsites.net/api/v1/files/${fileId}/download`;
  
      const generateResponse = await fetch(generateEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });
  
      if (!generateResponse.ok) {
        throw new Error(`Failed to generate ${format.toUpperCase()}: ${generateResponse.statusText}`);
      }
  
      const generateData = await generateResponse.json();
      const fileData = generateData.report[`${format}_documents`][0];
      const fileId = fileData.id;
      const fileName = fileData.name;
  
      const downloadResponse = await fetch(downloadEndpoint(fileId), {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
  
      if (!downloadResponse.ok) {
        throw new Error(`Failed to download ${format.toUpperCase()}: ${downloadResponse.statusText}`);
      }
  
      const blob = await downloadResponse.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
  
    } catch (error) {
      console.error(`Error generating or downloading ${format.toUpperCase()}:`, error);
      toast.error(`Failed to generate or download ${format.toUpperCase()}: ${error.message}`);
    }
  };


    const detailsSection = async () => {
      const token = Cookies.get("level_user_token");
      if (!token) {
        toast.error("Authorization token is missing");
        return;
      }
      try {
        const response = await fetch(`https://levelupportals1api-staging.azurewebsites.net/get_additional_assessment_data`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
            assessmentName: "Double Materiality",
            toolName: "Double Materiality Assessment",
          },
        });
        if (!response.ok) {
          throw new Error(`Failed to fetch details: ${response.statusText}`);
        }
        const data = await response.json();
  
        setDetailsData(data);
      } catch (error) {
        console.error("Error fetching details data:", error);
        toast.error("Failed to fetch details data");
      }
    };
    
    const handleVisibilityChange = (section) => {
      setVisibility((prev) => ({
        ...prev,
        [section]: !prev[section],
      }));
    };
  

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!currentSection || !currentSection.topics) {
    return <div className="text-center mt-10">No data available</div>;
  }

  return (
    <div className="w-full flex flex-col gap-4 mx-auto">
      <div className="p-6 bg-[#FFFFFF] rounded-lg border-2 border-[#E8E7EA]">
        <div className="flex max-md:flex-col gap-4 items-center justify-between mb-4">
          <div>
            <div className="flex gap-2 items-center">
              <h2 className="font-semibold text-[#272727]">TNFD Report</h2>
              <CiEdit className="text-[#07838F]" />
            </div>
            <div className="text-[#939393] font-semibold">
              Section {currentSectionIndex + 1} of {sections.length}
            </div>
          </div>
          <div  className="flex gap-2 items-center">
          <Menu shadow="md" width={200} position="bottom-end">
              <Menu.Target>
                <Button
                  disabled={!reportId}
                  className={`${
                    reportId
                      ? "bg-[#07838F] hover:bg-[#07838fce]"
                      : "bg-[#616161] cursor-not-allowed"
                  } text-white px-4 py-2 rounded-md flex items-center gap-2`}
                >
                  Generate Report <IoIosArrowDown className="ml-2" />
                </Button>
              </Menu.Target>
              <Menu.Dropdown>
                <Menu.Item
                  onClick={() => generateAndDownloadFile("pdf")}
                  className="text-[#616161] hover:bg-gray-100 font-semibold"
                >
                  Generate as PDF
                </Menu.Item>
                <Menu.Item
                  onClick={() => generateAndDownloadFile("docx")}
                  className="text-[#616161] hover:bg-gray-100 font-semibold"
                >
                  Generate as DOCX
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>
            <Menu shadow="md" width={300} position="bottom-end">
              <Menu.Target>
                <Button
                  variant="subtle"
                  className="bg-[#07838F] hover:bg-[#07838fce] text-white px-2 py-2 rounded-md flex items-center gap-2"
                >
                  <IoSettingsOutline className="text-xl text-white" />
                </Button>
              </Menu.Target>
              <Menu.Dropdown>
                <Menu.Label>Display Options</Menu.Label>
                <Menu.Item>
                  <Checkbox
                    checked={visibility.details}
                    onChange={() => handleVisibilityChange("details")}
                    label="Show details"
                    color="#07838F"
                  />
                </Menu.Item>
                <Menu.Item>
                  <Checkbox
                    checked={visibility.framework}
                    onChange={() => handleVisibilityChange("framework")}
                    label="Show framework requirements"
                    color="#07838F"
                  />
                </Menu.Item>
                <Menu.Item>
                  <Checkbox
                    checked={visibility.remarks}
                    onChange={() => handleVisibilityChange("remarks")}
                    label="Show remarks"
                    color="#07838F"
                  />
                </Menu.Item>
                <Menu.Item>
                  <Checkbox
                    checked={visibility.evidence}
                    onChange={() => handleVisibilityChange("evidence")}
                    label="Show evidence"
                    color="#07838F"
                  />
                </Menu.Item>
                <Menu.Item>
                  <Checkbox
                    checked={visibility.performance}
                    onChange={() => handleVisibilityChange("performance")}
                    label="Show performance"
                    color="#07838F"
                  />
                </Menu.Item>
                <Menu.Item>
                  <Checkbox
                    checked={visibility.qualityAssurance}
                    onChange={() => handleVisibilityChange("qualityAssurance")}
                    label="Show quality assurance"
                    color="#07838F"
                  />
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>
          </div>
        </div>
        <ProgressBar
          completed={completedCount}
          total={currentSection.topics.length}
        />
      </div>

      <div className="bg-[#FFFFFF] p-6 rounded-lg max-md:text-start border-2 border-[#E8E7EA]">
        <div className="mb-4">
          <h1 className="text-2xl md:text-center font-bold flex items-center gap-2 justify-center">
            {currentSection.title}
          </h1>
        </div>

        <Accordion type="single" collapsible className="w-full">
          {currentSection.topics.map((topic) => (
            <AccordionItemComponent
              key={topic.index}
              value={topic.index}
              reportId={reportId}
              title={topic.title}
              subtitle={topic.description}
              titleID={topic.index}
              questions={topic.disclosures || []}
              setSections={setSections}
              visibility={visibility}
              detailsData={detailsData}
              topicId={topic.id}
              topicReviewers={topic.reviewers}
              topicDueDate={topic.due_date}
              double_materiality_uid={topic.double_materiality_uid}
              topicComments={topic.comments}
              quantitative_metrics={topic.quantitative_metrics}
              qualitative_metrics={topic.qualitative_metrics}
              quality_assurance={topic.quality_assurance}
              framework_requirements={topic.framework_requirements}
              xbrl_tags={topic.xbrl_tags}
              evidences={topic.evidences}
            />
          ))}
        </Accordion>

        <div className="flex max-md:flex-col justify-between items-center mt-6 gap-2 flex-wrap">
          <button
            onClick={() => {
              if (currentSectionIndex === 0) {
                onPreviousStep();
              } else {
                setCurrentSectionIndex((prev) => Math.max(0, prev - 1));
              }
            }}
            className={`${
              currentSectionIndex === 0
                ? "bg-gray-300"
                : "bg-[#07838F] hover:bg-[#07838fce]"
            } text-white px-6 py-1 md:w-40 w-full rounded cursor-pointer font-semibold`}
          >
            Previous
          </button>

          <div className="flex max-md:w-full flex-col md:flex-row gap-2 md:gap-4">
            <button
              disabled={
                isAssessmentCompleted ||
                (currentSectionIndex === sections.length - 1 &&
                  !allMarkedComplete)
              }
              onClick={() => {
                if (currentSectionIndex < sections.length - 1) {
                  setCurrentSectionIndex((prev) => prev + 1);
                } else {
                  saveAssessment();
                }
              }}
              className={`${
                isAssessmentCompleted ||
                (currentSectionIndex === sections.length - 1 &&
                  !allMarkedComplete)
                  ? "bg-gray-300 cursor-not-allowed"
                  : "bg-[#07838f] hover:bg-[#07848fab]"
              } text-white px-6 md:w-40 w-full py-1 rounded cursor-pointer font-semibold`}
            >
              {currentSectionIndex === sections.length - 1 ? "Save" : "Next"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
