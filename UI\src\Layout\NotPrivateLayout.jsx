import { Notifications } from "@mantine/notifications";
import PublicNavbar from "@/Components/PublicNavbar";

const NotPrivateLayout = ({
  children,
  navbarTitle,
}) => {

  return (
    <div className="flex">
      <div className="flex-grow px-6 py-[5vh] h-screen overflow-hidden overflow-y-auto max-md:text-center bg-[#f7f4f4] ">
        <PublicNavbar navbarTitle={navbarTitle}/>
        {children}
      </div>
      {/* {activeChatbot && <Chatbot />} */}
    </div>
  );
};

export default NotPrivateLayout;
