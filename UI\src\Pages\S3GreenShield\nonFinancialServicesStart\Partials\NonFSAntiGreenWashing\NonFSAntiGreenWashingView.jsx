import React, { useState } from "react";
import S3Layout from "@/Layout/S3Layout";

import useSideBarRoute from "@/hooks/useSideBarRoute";

import { PiExportLight } from "react-icons/pi";
import { IoAddOutline } from "react-icons/io5";
import { FaRegTrashAlt } from "react-icons/fa";

import { Button } from "@mantine/core";
import SearchBox from "@/Components/SearchBox";
import { CiFilter } from "react-icons/ci";
import NonFSAntiTable from "./Components/NonFSAntiTable";
import FinAntiTable from "@/Pages/S3GreenShield/financialServicesStart/Partials/FinancialAntiGreenWashing/Components/FinAntiTable";


export default function NonFSAntiGreenWashingView() {
  const { greenShieldMenu } = useSideBarRoute();
  const [addCategory,setAddCategory] = useState(false)

  return (
    <>
      <S3Layout menus={greenShieldMenu}>
        <div className="flex flex-wrap justify-between items-center bg-white rounded-xl p-6">
          <div className="title">
            <h1 className="text-xl">Anti-GreenWashing Assessment </h1>
            <h2 className="text-sm text-secondary-lite-100 mt-1">
              A descriptive body text comes here
            </h2>
          </div>

          <div className="btns flex items-center gap-6">
            <Button
              className="border-[1px] w-[105px] h-[42px] text-sm font-bold rounded-lg text-secondary-300 border-secondary-300 bg-white px-1"
              size="md"
            >
              <span className="me-1">
                <PiExportLight className="text-lg" />
              </span>
              <span>Export</span>
            </Button>
            <Button
            onClick={()=> setAddCategory(true)}

              className="border-[1px] w-[105px] h-[42px] text-sm font-bold rounded-lg bg-secondary-300 px-1"
              size="md"
            >
              <span className="me-1">
                <IoAddOutline className="text-lg" />
              </span>
              <span>Add</span>
            </Button>
          </div>
        </div>

        <div className="search-filter-delete flex items-center justify-between flex-wrap mt-5">
          <SearchBox
            className="w-[341px] h-[38px]"
            classNames={{ input: "border-none rounded-lg shadow-sm" }}
          />

        <div className="delete-filter flex flex-wrap gap-6">

            <Button className="w-[36px] h-[36px] flex justify-center items-center bg-secondary-300 rounded-xl ">
                <FaRegTrashAlt className="text-white" />
            </Button>
        
          <div className="flex items-center bg-white h-[38px] rounded-lg shadow-sm">
            <CiFilter className="mx-2" />
            <IoAddOutline className="mx-2" />
            <p className="font-semibold mx-2 m-0">Filter</p>
          </div>
        </div>
        </div>

        {/* <NonFSAntiTable /> */}
        <FinAntiTable addCategory={addCategory} />
      </S3Layout>
    </>
  );
}
