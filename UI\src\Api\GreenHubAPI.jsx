// src/axiosConfig.js

import axios from "axios";
import Cookies from "js-cookie";
// const baseURL = "https://green-hub.azurewebsites.net";
const baseURL = "https://green-hub-staging.azurewebsites.net";

// const baseURL = "http://localhost:8080";

const GreenHubAPI = axios.create({
  baseURL,
  headers: {
    Authorization: `Bearer ${Cookies.get("level_user_token")}`,
  },
});

GreenHubAPI.interceptors.request.use(
  (config) => {
    // Modify config before sending the request
    config.headers["Authorization"] = `Bearer ${Cookies.get(
      "level_user_token"
    )}`;
    return config;
  },
  (error) => {
    // Handle request error
    return Promise.reject(error);
  }
);

export default GreenHubAPI;
