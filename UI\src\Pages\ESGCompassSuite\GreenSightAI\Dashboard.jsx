import ApiSustain360 from "@/Api/apiSustain360";
import Tree from "@/Components/Dashboard/Tree";
import Loading from "@/Components/Loading";
import MainLayout from "@/Layout/MainLayout";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import { AreaChart } from "@mantine/charts";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import Assessments from "../../../Components/Assessments";
import ESGComponent from "../../../Components/Charts/ESGComponent";
import Performance from "../../../Components/Performance";
import { Button, ScrollArea } from "@mantine/core";

const Dashboard = ({ setActive }) => {
  const [assessments, setAssessments] = useState();
  const [activeAssesmentIndex, setActiveAssesmentIndex] = useState(0);
  const [lineData, setLineData] = useState([]);
  const { esgAssessmentMenu } = useSideBarRoute();
  const [loading, setLoading] = useState(false);

  // Get the first assessment data for ESGComponent
  const [firstAssessment, setFirstAssessment] = useState(null);

  const fetchAssessmentData = async () => {
    setLoading(true);
    try {
      await ApiSustain360.get("/api/assessments/dashboard", {
        headers: { assessmentType: "GreenSight AI" },
      })
        .then((response) => response.data.all_assessment_summaries)
        .then((assessmentData) => {
      console.log("assessmentData", assessmentData);

          setAssessments(assessmentData);
          const data = assessmentData.map((item) => ({
            Assessment: item?.total_score,
            date: item?.name,
          }));
          setLineData(data);
          setFirstAssessment(assessmentData[0]);
        });
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchAssessmentData();
  }, []);
  const { t } = useTranslation();
  return (
    <>
      {(loading) ? (<>
        <Loading />
      </>) : (assessments != null) ? (
        <div className="flex flex-col items-center py-[5vh] w-full">
          <div className="grid grid-cols-1 gap-5 md:grid-cols-2">
            {firstAssessment ? (
              <div className="h-auto bg-white rounded-md shadow w-full">
                <h3 className="mt-6 text-sm font-medium text-center text-gray-500 ">
                  {t("Latest Assessment")}
                </h3>
                <ESGComponent assessmentData={firstAssessment} />
              </div>
            ) : (
              <Loading />
            )}

            {lineData?.length > 0 ? (
              <div className="h-auto py-5 mb-6 lg:ml-8 bg-white rounded-md shadow pe-4">
                <h1 className="mb-5 text-sm font-medium text-center text-gray-500">
                  {t("Progress Tracker")}
                </h1>
                <AreaChart
                  h={300}
                  data={lineData}
                  dataKey="date"
                  series={[{ name: "Assessment", color: "blue.6" }]}
                  curveType="natural"
                  withDots={false}
                />
              </div>
            ) : (
              <Loading />
            )}
          </div>
          <div className="hidden md:flex">
            <Tree />
          </div>
          {assessments ? (
            <>
              <div className="w-[95%] h-auto mb-6">
                <h2 className="text-xl mb-4">Assessments</h2>
                <ScrollArea scrollbarSize={5}>
                  <Assessments
                    assessments={assessments}
                    activeTabIndex={activeAssesmentIndex}
                    setActiveTabIndex={setActiveAssesmentIndex}
                  />
                </ScrollArea>
              </div>
              <div className="w-[95%] ">
                <Performance
                  assessments={assessments}
                  activeTabIndex={activeAssesmentIndex}
                />
              </div>
            </>
          ) : (
            <Loading />
          )}
        </div>
      ) : (
        <div className="flex flex-col items-center pt-[10vh] h-[100dvh] w-full">
          <h2 className="text-xl mb-4">No Assessment Found, go to the back to Assessment Page and create a new assessment</h2>
          <Button size="md" className="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary/80" onClick={() => setActive("Assessment")}>Back to Assessment Page</Button>
        </div>
      )}
    </>
  )
};

export default Dashboard;
