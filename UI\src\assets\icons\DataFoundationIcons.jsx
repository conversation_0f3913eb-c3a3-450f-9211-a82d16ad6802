export const URLIcon = ({color})=>(
    <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M14.99 18.4219H16.5C19.52 18.4219 22 15.9519 22 12.9219C22 9.90188 19.53 7.42188 16.5 7.42188H14.99" stroke={color ? "#07838F" : "#5A5A5A"} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
<path d="M9 7.42188H7.5C4.47 7.42188 2 9.89188 2 12.9219C2 15.9419 4.47 18.4219 7.5 18.4219H9" stroke={color ? "#07838F" : "#5A5A5A"} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
<path d="M8 12.9219H16" stroke={color ? "#07838F" : "#5A5A5A"} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
</svg>

)


export const WebIcon = ({color})=>(
    <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M22.126 12.9219C22.126 7.40188 17.646 2.92188 12.126 2.92188C6.60598 2.92188 2.12598 7.40188 2.12598 12.9219C2.12598 18.4419 6.60598 22.9219 12.126 22.9219" stroke={color ? "#07838F" : "#5A5A5A"} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
<path d="M8.12607 3.92188H9.12607C7.17607 9.76188 7.17607 16.0819 9.12607 21.9219H8.12607" stroke={color ? "#07838F" : "#5A5A5A"} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
<path d="M15.126 3.92188C16.096 6.84188 16.586 9.88188 16.586 12.9219" stroke={color ? "#07838F" : "#5A5A5A"} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
<path d="M3.12598 16.9219V15.9219C6.04598 16.8919 9.08598 17.3819 12.126 17.3819" stroke={color ? "#07838F" : "#5A5A5A"} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
<path d="M3.12598 9.92344C8.96598 7.97344 15.286 7.97344 21.126 9.92344" stroke={color ? "#07838F" : "#5A5A5A"} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
<path d="M18.326 22.3219C20.0933 22.3219 21.526 20.8892 21.526 19.1219C21.526 17.3546 20.0933 15.9219 18.326 15.9219C16.5587 15.9219 15.126 17.3546 15.126 19.1219C15.126 20.8892 16.5587 22.3219 18.326 22.3219Z" stroke={color ? "#07838F" : "#5A5A5A"} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
<path d="M22.126 22.9219L21.126 21.9219" stroke={color ? "#07838F" : "#5A5A5A"} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
</svg>

)

export const InternalFilesIcon = ({color})=>(
    <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M22.126 11.9219V17.9219C22.126 21.9219 21.126 22.9219 17.126 22.9219H7.12598C3.12598 22.9219 2.12598 21.9219 2.12598 17.9219V7.92188C2.12598 3.92188 3.12598 2.92188 7.12598 2.92188H8.62598C10.126 2.92188 10.456 3.36188 11.026 4.12188L12.526 6.12187C12.906 6.62187 13.126 6.92188 14.126 6.92188H17.126C21.126 6.92188 22.126 7.92188 22.126 11.9219Z" stroke={color ? "#07838F" : "#5A5A5A"} strokeWidth="2" strokeMiterlimit="10"/>
</svg>

)

export const SuccessAlertIcon = ()=>(
    <svg width="26" height="35" viewBox="0 0 26 35" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M1 17.5C1 24.1274 6.37258 29.5 13 29.5C19.6274 29.5 25 24.1274 25 17.5C25 15.1199 24.3071 12.9016 23.112 11.036C22.9657 10.8076 22.8926 10.6935 22.7648 10.6698C22.637 10.6461 22.5273 10.7268 22.308 10.8881C21.7985 11.2629 21.2803 11.6952 20.7597 12.1735C19.4594 13.3681 18.2064 14.7914 17.1072 16.1719C16.0101 17.5496 15.0797 18.8674 14.4233 19.8415C14.0955 20.328 13.837 20.7274 13.6613 21.0039C13.5734 21.1421 13.5062 21.2495 13.4615 21.3216L13.4115 21.4027L13.3995 21.4223L13.3968 21.4267C13.1917 21.7647 12.8227 21.9699 12.4274 21.9648C12.032 21.9596 11.6688 21.7457 11.4727 21.4024C10.413 19.5481 9.53771 18.7132 9.01102 18.3445C8.7493 18.1613 8.57125 18.0913 8.49294 18.0664C8.48496 18.0639 8.48098 18.0626 8.47041 18.0601C8.46622 18.059 8.45923 18.0576 8.455 18.0568C8.44431 18.0548 8.43296 18.0535 8.41027 18.051C7.85242 17.9891 7.4186 17.516 7.4186 16.9416C7.4186 16.3251 7.91838 15.8253 8.53488 15.8253C8.6231 15.8155 8.87359 15.8246 9.16986 15.9389C9.47527 16.0361 9.85536 16.2103 10.2913 16.5155C10.8822 16.9291 11.57 17.5789 12.3186 18.5982C12.3819 18.6844 12.5121 18.6827 12.5719 18.594C13.2513 17.5856 14.217 16.2174 15.3607 14.7811C16.5022 13.3477 17.8347 11.8291 19.2492 10.5295C19.7269 10.0906 20.2215 9.66965 20.7283 9.28223C21.0046 9.07104 21.1427 8.96544 21.1465 8.8221C21.1503 8.67877 21.0257 8.57266 20.7765 8.36046C18.6821 6.57661 15.9668 5.5 13 5.5C6.37258 5.5 1 10.8726 1 17.5Z" fill="url(#paint0_linear_42_19897)"/>
<defs>
<linearGradient id="paint0_linear_42_19897" x1="0.548872" y1="33.0385" x2="29.8848" y2="30.4806" gradientUnits="userSpaceOnUse">
<stop stopColor="#2C5A8C"/>
<stop offset="0.46" stopColor="#1C889C"/>
<stop offset="1" stopColor="#13B1A8"/>
</linearGradient>
</defs>
</svg>

)