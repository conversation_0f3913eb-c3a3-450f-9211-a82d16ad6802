@tailwind base;
@tailwind components;
@tailwind utilities;
@import '@mantine/dropzone/styles.css';

body {
  background-color: #F7F4F4;
}

.active .step {
    @apply bg-[#1C4D88]
}
.complete .step{
    @apply bg-green-300 text-black  duration-500
}
::selection{
    background-color: #16999A;
    color: #fff;
}

.bg-dots {
  background-color: #FFFFFF; /* Default background color */
  background-image: radial-gradient(#888 0.05em, transparent 0.05em),
                    radial-gradient(#888 0.05em, transparent 0.05em);
  background-size: 1.5em 1.5em;
  background-position: 0 0, 0.75em 0.75em;
}

.chatbox-scrollbar::-webkit-scrollbar {
  width: 12px;
}
.chatbox-scrollbar::-webkit-scrollbar-track {
  background: #f0f0f0;
}
.chatbox-scrollbar::-webkit-scrollbar-thumb {
  background-color: #a8a8a8;
  border-radius: 20px;
  border: 3px solid #f0f0f0;
}
.chatbox-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #919191;
}


.between {
  @apply 'flex justify-between items-center'
}

.highlighted {
  border: 2px solid #FF0000; /* Example: Highlight with a red border */
  background-color: rgba(255, 0, 0, 0.2); /* Light red background */
}


