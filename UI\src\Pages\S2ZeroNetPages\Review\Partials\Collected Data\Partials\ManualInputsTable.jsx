import ApiS2 from "@/Api/apiS2Config";
import Loading from "@/Components/Loading";
import { formatNumber } from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/StaticData";
import {
 Button,
 Checkbox,
 Pagination,
 rem,
 ScrollArea,
 Table,
 TextInput,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { showNotification } from "@mantine/notifications";
import cx from "clsx";
import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { CiSearch } from "react-icons/ci";
import { FaEdit } from "react-icons/fa";
import { RiDeleteBin6Line } from "react-icons/ri";
import ReviewMultiselectFilter from "../ReviewMultiselectFilter";
import UpdateReviewPopUp from "./Partials/UpdateReviewPopUp";

import useFormatTextKey from "@/hooks/useFormatTextKey";

const ManualInputsTable = ({
 Status,
 InputSource,
 UserType,
 assetTypeAll,
 getTableData,
 data,
 loadingTable,
 approvedRow,
 loadingApprovedManual,
 toggleLoading,
 error,
}) => {
 useEffect(() => {
  !data && getTableData("manual");
 }, []);
 // console.log(data);

 const { t } = useTranslation();
 const [value, setValue] = useState([]);
 const [selectedRow, setSelectedRow] = useState();
 const [selectedItemsId, setSelectedItems] = useState();
 const [search, setSearch] = useState();
 const [sortedData, setSortedData] = useState();
 const [DelLoading, setDelLoading] = useState({});
 const [selection, setSelection] = useState([]);
 const [opened, { open, close }] = useDisclosure(false);
 const [currentPage, setCurrentPage] = useState(1);
 const rowsPerPage = 10;
 const totalPages = Math.ceil(data?.length / rowsPerPage);
 //  console.log(selection);
 const DelToggleLoading = (id, value) => {
  setDelLoading((prevState) => ({
   ...prevState,
   [id]: value,
  }));
 };
 const currentData = useMemo(() => {
  return (
   (data &&
    data?.slice((currentPage - 1) * rowsPerPage, currentPage * rowsPerPage)) ||
   []
  );
 }, [data, currentPage, rowsPerPage]);
 // console.log(search);

 useEffect(() => {
  setSortedData(currentData);
  let filteredData;
  if (value[0] === "Pending") {
   filteredData = currentData.filter((item) => item.reviewState === null);
  } else if (value[0] === "Accepted") {
   filteredData = currentData.filter((item) => item.reviewState === true);
  } else if (value[0] === "Rejected") {
   filteredData = currentData.filter((item) => item.reviewState === false);
  } else {
   filteredData = currentData;
  }

  if (search) {
   filteredData = filteredData.filter((data) =>
    data.items.some((item) => {
     let activity = item.customFactor?.activity || {};
     let eFactors = item.customFactor?.eFactors || {};
     let uom = item.customFactor?.uom || {};

     return (
      item.companyAsset?.assetName
       ?.toLowerCase()
       .includes(search.toLowerCase()) ||
      Object.values(activity).some((value) =>
       value?.toString().toLowerCase().includes(search.toLowerCase())
      ) ||
      Object.values(eFactors).some((value) =>
       value?.toString().toLowerCase().includes(search.toLowerCase())
      ) ||
      Object.values(uom).some((value) =>
       value?.toString().toLowerCase().includes(search.toLowerCase())
      ) ||
      item.reportingYear?.toString().includes(search)
     );
    })
   );
  }

  setSortedData(filteredData);
 }, [value, search, currentData]);

 const handleSearchChange = (event) => {
  setSearch(event);
 };

 const toggleRow = (id) =>
  setSelection((current) =>
   current.includes(id)
    ? current.filter((item) => item !== id)
    : [...current, id]
  );
 const toggleAll = () =>
  setSelection((current) =>
   current.length === data.length ? [] : data.map((item) => item.id)
  );
 const del = async (e, id) => {
  const dataset = { dataset_ids: [e] };
  console.log(dataset);
  try {
   DelToggleLoading(id, true);
   const { data } = await ApiS2.post("/batch_inputs/delete_dataset", dataset);
   showNotification({
    message: "Deleted Successfully",
    color: "teal",
   });
   getTableData("manual");
   DelToggleLoading(id, false);
   console.log(data);
  } catch (error) {
   showNotification({
    message: "Failed to Delete",
    color: "red",
   });
   DelToggleLoading(id, false);
   console.log(error);
  }
 };
 const rows = sortedData?.map((item) => {
  const selected = selection.includes(item.id);
  // console.log(selected);

  let assetType,
   fromDate,
   toDate,
   assetName,
   activity,
   eFactors,
   uom,
   quantity,
   reportingYear,
   dataset,
   index,
   Emissions;

  const evidence = item?.uploadFileLinks?.evidence;
  const evidenceKey = Object.keys(evidence);
  const isLoadingManual = loadingApprovedManual[item.id];
  item.items.forEach((items, idx) => {
   const assets = assetTypeAll.find(
    (asset) => asset.id === items?.customFactor?.emissionSourceId
   );
   assetType = assets?.asset;
   fromDate = items.fromDate;
   toDate = items.toDate;
   assetName = items?.companyAsset?.assetName;
   activity = items?.customFactor?.activity;
   eFactors = items?.customFactor?.eFactors;
   uom = items?.customFactor?.uom;
   quantity = items?.quantity;
   reportingYear = items?.reportingYear;
   dataset = items?.dataset;
   index = idx;
   Emissions = items?.calculatedCarbonFactors?.Total_Emissions;
  });

  return (
   <Table.Tr
    key={item.id}
    className={`${cx({
     ["bg-[#07838F1A]"]: selected,
    })} text-sm font-bold text-[#626364] text-center`}
   >
    <Table.Td>
     <Checkbox
      checked={selection.includes(item.id)}
      onChange={() => toggleRow(item.id)}
      color="#07838F"
     />
    </Table.Td>
    <Table.Td>
     <span
      className="flex justify-center items-center text-xl"
      onClick={() => {
       if (!DelLoading[item.id]) {
        del(dataset, item.id);
       }
      }}
     >
      {DelLoading[item?.id] ? (
       <Loading />
      ) : (
       <RiDeleteBin6Line className="cursor-pointer" />
      )}
     </span>
    </Table.Td>
    <Table.Td>
     <span
      className={`flex justify-center items-center text-xl ${
       item.reviewState !== null ? "opacity-50 cursor-not-allowed" : ""
      }`}
      onClick={() => {
       if (item.reviewState === null) {
        open();
        setSelectedRow(item.id);
        setSelectedItems(index);
       }
      }}
     >
      <FaEdit
       className={
        item.reviewState !== null
         ? "opacity-50 cursor-not-allowed"
         : "cursor-pointer"
       }
      />
     </span>
    </Table.Td>
    <Table.Td>
     <div className="w-20 block mx-auto">
      <p className="">{item.uploadedDT?.split("T")[1]?.substring(0, 5)}</p>
     </div>
    </Table.Td>
    <Table.Td>
     <div className="w-20 block mx-auto">
      <p className="">{fromDate}</p>
     </div>
    </Table.Td>
    <Table.Td>
     <div className="w-20 block mx-auto">
      <p className="">{toDate}</p>
     </div>
    </Table.Td>
    <Table.Td>
     <div className="block mx-auto">{Emissions && formatNumber(Emissions)}</div>
    </Table.Td>
    <Table.Td>
     <div className="min-w-24 text-nowrap block mx-auto">{assetType}</div>
    </Table.Td>
    <Table.Td>
     <div className="w-24 block mx-auto">
      <p className="">{assetName}</p>
     </div>
    </Table.Td>
    <Table.Td>
     <div className="min-w-60 ">
      {activity &&
       Object.entries(activity).map(([key, value],idx) => (
        <p className="mt-3" key={idx}>
         {useFormatTextKey(key)}: {value}
        </p>
       ))}
     </div>
    </Table.Td>
    <Table.Td>
     <div className="min-w-60 ">
      {/* <p className="">{eFactors}</p> */}
      {eFactors &&
       Object.entries(eFactors).map(([key, value],idx) => (
        <p className="mt-3" key={idx}>
         {useFormatTextKey(key)}: {value}
        </p>
       ))}
     </div>
    </Table.Td>
    <Table.Td>
     <div className="min-w-60 ">
      {/* <p className="">{uom}</p> */}
      {uom &&
       Object.entries(uom).map(([key, value],idx) => (
        <p className="mt-3" key={idx}>
         {useFormatTextKey(key)}: {value}
        </p>
       ))}
     </div>
    </Table.Td>
    <Table.Td>
     <div className="block min-w-24  mx-auto">
      {/* <p className="">{quantity}</p> */}
      {quantity &&
       Object.entries(quantity).map(([key, value],idx) => (
        <p className="" key={idx}>
         {useFormatTextKey(key)}: {value}
        </p>
       ))}
     </div>
    </Table.Td>
    <Table.Td>
     <div className="w-24 block mx-auto">
      <p className="">{reportingYear}</p>
     </div>
    </Table.Td>
    <Table.Td>
     {" "}
     <div className="">
      <p className="w-full">
       {evidenceKey.map((item, idx) => (
        <Button
         key={idx}
         variant="subtle"
         href={evidence[item]}
         target="_blank"
         component="a"
        >
         {item}
        </Button>
       )) || "Not Found"}
      </p>
     </div>
    </Table.Td>
    <Table.Td>
     <p
      style={{
       backgroundColor: InputSource[item.inputType]?.bg,
       border: `2px solid ${InputSource[item.inputType]?.border}`,
       color: InputSource[item.inputType]?.text,
      }}
      className="p-2 text-nowrap rounded-3xl "
     >
      <span className="capitalize"> {item.inputType}</span>
     </p>
    </Table.Td>
    <Table.Td>
     <p
      style={{
       backgroundColor: UserType[item.userType]?.bg,
       border: `2px solid ${UserType[item.userType]?.border}`,
       color: UserType[item.userType]?.text,
      }}
      className="p-2 text-nowrap rounded-3xl "
     >
      <span className="capitalize">{item.userType}</span>
     </p>
    </Table.Td>
    <Table.Td>
     <div className=" block mx-auto">
      <p
       className={`py-2 px-10 rounded-2xl`}
       style={{
        backgroundColor:
         item.reviewState === null
          ? Status.Pending.bg
          : item.reviewState === true
          ? Status.Accepted.bg
          : item.reviewState === false
          ? Status.Rejected.bg
          : "",
        color:
         item.reviewState === null
          ? Status.Pending.text
          : item.reviewState === true
          ? Status.Accepted.text
          : item.reviewState === false
          ? Status.Rejected.text
          : "",
       }}
      >
       {item.reviewState === null
        ? "Pending"
        : item.reviewState === true
        ? "Accepted"
        : item.reviewState === false
        ? "Rejected"
        : "Not Found"}
      </p>
     </div>
    </Table.Td>
    <Table.Td>
     <div className=" block mx-auto">
      <Button
       className={`bg-primary text-white  hover:text-white rounded-lg 
              ${
               item?.reviewState === true
                ? "bg-gray-500 cursor-not-allowed bg-opacity-20"
                : "bg-primary hover:bg-primary"
              }`}
       disabled={item?.reviewState === true}
       onClick={() => {
        if (!isLoadingManual) {
         toggleLoading(item.id, true, "manual");
         approvedRow(dataset, "manual")
          .then(() => {
           toggleLoading(item.id, false, "manual");
          })
          .catch(() => {
           toggleLoading(item.id, false, "manual");
          });
        }
       }}
      >
       {isLoadingManual ? <Loading /> : "Approved"}
      </Button>
     </div>
    </Table.Td>
   </Table.Tr>
  );
 });
 // console.log(error);
 return (
  <>
   {loadingTable ? (
    <Loading />
   ) : (
    <>
     <div className="bg-white mt-7 p-1 rounded-lg overflow-hidden">
      <div className="p-2 mt-1 Find-the-records  grid items-center  xl:justify-between px-4 bg-white xl:grid-cols-3 rounded-t-lg w-full">
       <div className="xl:col-span-1 w-full flex justify-center xl:justify-start"></div>
       <div className="xl:col-span-2 grid items-start  justify-center grid-cols-1 lg:grid-cols-3 xl:grid-cols-4 sm:items-center w-full lg:gap-5">
        <div className="md:col-span-1 hidden xl:flex justify-center items-center m-3  p-2 rounded-lg shadow-sm ms-auto "></div>

        <TextInput
         className="w-full col-span-2 Find-the-records-via-search-bar"
         placeholder="Search by Topic Area or Assessment Question"
         rightSection={<CiSearch className="w-5 h-5" />}
         value={search}
         onChange={(e) => handleSearchChange(e.target.value)}
         // disabled
        />
        <div className="Find-the-records-via-filters md:col-span-1 m-3 bg-white hover:bg-white border-2 border-[#E8E7EA] rounded-lg shadow-sm md:ms-auto mx-auto w-full">
         <ReviewMultiselectFilter setValue={setValue} value={value} />
        </div>
       </div>
      </div>
      <>
       {(search && sortedData?.length === 0) ||
       (value.length && sortedData?.length === 0) ? (
        <h1 className="mt-5 text-center capitalize">
         Your Search is not Found
        </h1>
       ) : (
        <>
         <ScrollArea>
          <Table
           // miw={800}
           verticalSpacing="sm"
           className="p-2  bg-white shadow-lg  "
           withTableBorder
           highlightOnHover
          >
           <Table.Thead className="border-b-2 border pb-6 bg-[#f5f4f5] text-secondary-500 font-bold text-base text-center">
            <Table.Tr>
             <Table.Th style={{ width: rem(40) }}>
              <Checkbox
               onChange={toggleAll}
               checked={selection?.length === data?.length}
               indeterminate={
                selection.length > 0 && selection.length !== data.length
               }
               color="#07838F"
              />
             </Table.Th>
             <Table.Th className="text-center Manage-and-Review-your-Data-Delete">{t("Delete")}</Table.Th>
             <Table.Th className="text-center Manage-and-Review-your-Data-Edit">{t("Edit")}</Table.Th>
             <Table.Th className="text-center">{t("Time Stamp")}</Table.Th>
             <Table.Th className="text-center">{t("From")}</Table.Th>
             <Table.Th className="text-center">{t("To")}</Table.Th>
             <Table.Th className="text-center">{t("Emissions")}</Table.Th>
             <Table.Th className="text-center">{t("Assets Type")}</Table.Th>
             <Table.Th className="text-center">{t("Assets")}</Table.Th>
             <Table.Th className="text-center">{t("Activity")}</Table.Th>
             <Table.Th className="text-center">{t("eFactor")}</Table.Th>
             <Table.Th className="text-center">{t("UOM")}</Table.Th>
             <Table.Th className="text-center">{t("Quantity")}</Table.Th>
             <Table.Th className="text-center">{t("Reporting Year")}</Table.Th>
             <Table.Th className="text-center">{t("Evidence/Notes")}</Table.Th>
             <Table.Th className="text-center">{t("Input Source")}</Table.Th>
             <Table.Th className="text-center">{t("User Type")}</Table.Th>
             <Table.Th className="text-center">{t("Status")}</Table.Th>
             <Table.Th className="text-center">{t("Action")}</Table.Th>
             {/* <Table.Th className="text-center">
                <span className="flex justify-center gap-3">
                  <span className="bg-white text-[#404B52] rounded-md w-8 h-8 flex justify-center items-center text-sm font-medium cursor-pointer">
                    <MdKeyboardArrowLeft />
                  </span>
                  <span className="bg-white text-[#404B52] rounded-md w-8 h-8 flex justify-center items-center text-sm font-medium cursor-pointer">
                    <MdKeyboardArrowRight />
                  </span>
                </span>
              </Table.Th> */}
            </Table.Tr>
           </Table.Thead>
           <Table.Tbody>{rows}</Table.Tbody>
          </Table>
         </ScrollArea>

         {/* Pagination */}
         <div className="md:flex justify-between mt-5">
          <p className="text-sm text-black" hidden={!sortedData?.length}>
           {t("showingData", {
            start: (currentPage - 1) * rowsPerPage + 1,
            end: Math.min(currentPage * rowsPerPage, data?.length),
            total: data?.length,
           })}
          </p>
          <Pagination
           page={currentPage}
           onChange={(e) => {
            setCurrentPage(e);
            // setEdit(false);
           }}
           total={totalPages}
           color="#dde7e9"
           autoContrast
           className={`flex justify-center mt-5 md:mt-0 ${
            !sortedData?.length && "hidden"
           }`}
          />
         </div>
        </>
       )}
      </>
     </div>
     {/* {console.log(selectedRow)} */}
     {selectedRow && opened && (
      <UpdateReviewPopUp
       opened={opened}
       onClose={close}
       data={selectedRow && data.find((item) => item.id === selectedRow)}
       assetTypeAll={assetTypeAll}
       getTableData={getTableData}
       setSelectedItems={setSelectedItems}
       setSelectedRow={setSelectedRow}
       selectedItemsId={selectedItemsId}
      />
     )}
    </>
   )}
  </>
 );
};

export default ManualInputsTable;
