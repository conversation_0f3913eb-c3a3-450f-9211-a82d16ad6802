// CustomPopup.js
import React, { useState } from "react";
import PropTypes from "prop-types";
import popup from "@/assets/svg/Upgrade-rafiki 1.svg";
import { useNavigate } from "react-router-dom";
import ApiS1Config from "@/Api/apiS1Config";
import { notifications } from "@mantine/notifications";
import { Button, Image, Modal } from "@mantine/core";
import { FaLongArrowAltUp } from "react-icons/fa";
import { FaArrowUpLong } from "react-icons/fa6";

function UpgradePopup({ opened, close }) {
 const navigate = useNavigate();

 return (
  <Modal
   opened={opened}
   onClose={close}
   centered
   size="50rem"
   className=""
   transitionProps={{ transition: "fade", duration: 800 }}
   withCloseButton={false}
  >
   <div className="">
    <p className="text-xl font-bold flex items-center mb-2">
     <FaArrowUpLong className="bg-[#07848f79] p-1 w-5 h-5 me-1 rounded-md text-primary" />
     Upgrade for access{" "}
    </p>
    <hr />
   </div>
   <div className="">
    <div className="flex justify-center items-center mb-5">
      <Image src={popup} alt="popup" className="w-[294px] h-[294px]"/>
    </div>
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
      <Button className="bg-gray-500 hover:bg-gray-500" onClick={close}>Later</Button>
      <Button className="bg-primary hover:bg-primary">Upgrade</Button>
    </div>
   </div>
  </Modal>
 );
}

export default UpgradePopup;
