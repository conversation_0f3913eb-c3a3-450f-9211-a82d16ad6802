const MaturityLevelScale = () => {
  const levels = [
    { name: "Beginning", range: "0-39%" },
    { name: "Developing", range: "40-55%" },
    { name: "Maturing", range: "56-70%" },
    { name: "Advanced", range: "71-85%" },
    { name: "Leading", range: "86-100%" },
  ];

  const colors = [
    "bg-[#E81E1E]",
    "bg-[#F4B351]",
    "bg-[#298BED]",
    "bg-[#9160C1]",
    "bg-[#00C0A9]",
  ];

  return (
    <div className="p-6 my-6 bg-[#FFFFFF] rounded-lg border-2 border-[#E8E7EA]">
      <h1 className="text-2xl font-bold text-gray-800 mb-6">
        Maturity Level Scale
      </h1>

      <div className="space-y-3 flex items-center justify-around max-sm:flex-col">
        {levels.map((level, index) => (
          <div key={index} className="flex flex-col items-center">
            <div className="flex items-center gap-2">
              <span className={`h-3 w-3 rounded-full ${colors[index]}`}></span>
              <span className="font-medium">{level.name}</span>
            </div>
            <span className="text-sm font-mono">{level.range}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MaturityLevelScale;
