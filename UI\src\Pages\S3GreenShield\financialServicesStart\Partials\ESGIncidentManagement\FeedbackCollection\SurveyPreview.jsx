import { Text, Title, Paper, Divider } from "@mantine/core";

const SurveyPreview = (props) => {
  const { surveyTitle, surveyDescription, groups } = props;
  console.log("Survey Preview Props", props);
  //   console.log("Survey Preview Tab", props.survey);
  return (
    <div className="mx-auto bg-white p-6 rounded-xl border border-gray-200 shadow-sm">
      <div className="mb-6">
        <Title order={2} className="text-[#07838F] mb-2">
          {surveyTitle || "Survey Preview"}
        </Title>
        <Text className="text-gray-600 mb-4">
          {surveyDescription || "Please answer the following questions"}
        </Text>
        <Divider className="my-4" />
      </div>

      {groups.map((group, groupIndex) => (
        <Paper key={group.id} shadow="xs" p="md" withBorder className="mb-6">
          <Title order={3} className="mb-2">
            {group.groupTitle || `Group ${groupIndex + 1}`}
          </Title>

          {group.groupDescription && (
            <Text className="text-gray-500 mb-4">{group.groupDescription}</Text>
          )}

          <div className="space-y-6 mt-4">
            {group.questions.map((question, questionIndex) => (
              <div key={questionIndex} className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-start mb-3">
                  <Text className="font-medium">
                    {question.text || `Question ${questionIndex + 1}`}
                    {question.required && (
                      <span className="text-red-500 ml-1">*</span>
                    )}
                  </Text>
                </div>

                {question.type === "text" && (
                  <div className="bg-white border border-gray-300 rounded p-2 h-24">
                    <Text color="dimmed" size="sm" italic>
                      Text input field
                    </Text>
                  </div>
                )}

                {question.type === "radio" && question.options && (
                  <div className="space-y-2">
                    {question.options.map((option, optIndex) => (
                      <div
                        key={optIndex}
                        className="flex items-center space-x-2"
                      >
                        <input
                          type="radio"
                          disabled
                          className="w-4 h-4 border-gray-300"
                          name={`preview-${group.id}-${questionIndex}`}
                        />
                        <Text>{option || `Option ${optIndex + 1}`}</Text>
                      </div>
                    ))}
                  </div>
                )}

                {question.type === "checkbox" && question.options && (
                  <div className="space-y-2">
                    {question.options.map((option, optIndex) => (
                      <div
                        key={optIndex}
                        className="flex items-center space-x-2"
                      >
                        <input
                          type="checkbox"
                          disabled
                          className="w-4 h-4 border-gray-300 rounded"
                        />
                        <Text>{option || `Option ${optIndex + 1}`}</Text>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </Paper>
      ))}

      <div className="flex justify-between mt-6">
        <button
          disabled
          className="px-6 py-2 bg-gray-200 text-gray-600 rounded-full"
        >
          Previous
        </button>
        <button
          disabled
          className="px-6 py-2 bg-[#07838F] text-white rounded-full"
        >
          Next
        </button>
      </div>

      <div className="text-center mt-8 text-gray-500">
        <Text size="sm">
          This is a preview of how your survey will appear to respondents.
        </Text>
        <Text size="sm">Submit buttons are disabled in preview mode.</Text>
      </div>
    </div>
  );
};

export default SurveyPreview;
