import React from "react";
import { Link } from "react-router-dom";

export default function Menubar({ links, className }) {
  return (
    <div
      className={`flex flex-rows justify-between shadow-lg font-semibold text-[#9C9C9C]  rounded-lg  ${className}`}
    >
      {links.map((link, index) => (
        <Link key={index} to=".." relative="path">
          {link}
        </Link>
      ))}
    </div>
  );
}
