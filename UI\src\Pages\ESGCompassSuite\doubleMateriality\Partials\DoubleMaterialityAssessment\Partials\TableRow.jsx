import { Table } from "@mantine/core";
import React from "react";

export default function TableRow({ item }) {
  return (
    <Table.Tr
      key={item.id}
      className={cx({
        "bg-[#07838F1A]": selection.includes(item.id),
      })}
      style={{ textAlign: "center", fontWeight: "bold", color: "#626364" }}
    >
      <Table.Td>
        <Checkbox
          checked={selection.includes(item.id)}
          onChange={() => toggleRow(item.id)}
          color="#07838F"
        />
      </Table.Td>
      <Table.Td>{item.name}</Table.Td>
      <Table.Td>{item.Category}</Table.Td>
      <Table.Td>
        <TextInput
          value={item.Impact_Score}
          onChange={(e) =>
            handleScoreInputChange(item.id, "Impact_Score", e.target.value)
          }
        />
      </Table.Td>
      <Table.Td>
        <TextInput
          value={item.Financial_Score}
          onChange={(e) =>
            handleScoreInputChange(item.id, "Financial_Score", e.target.value)
          }
        />
      </Table.Td>
      <Table.Td className="flex justify-center text-md">
        <span
          className={`flex justify-center items-center p-1 rounded w-1/2 text-center ${
            item.Priority === "High"
              ? "bg-red-400 text-red-900"
              : item.Priority === "Medium"
              ? "bg-yellow-400 text-yellow-900 px-2"
              : "bg-green-400"
          }`}
        >
          {propertyLevelMap[item.Priority]}
        </span>
      </Table.Td>
    </Table.Tr>
  );
}
