import {
    Box,
    Title,
    Text,
    List,
    Group,
    Table,
    ThemeIcon,
    Stack,
    Paper,
} from "@mantine/core";
import { FaCheck } from "react-icons/fa";

export default function EquatorPrinciplesGuide() {
    return (
        <Box mx="auto" maxWidth="1200px" p="md">
            <Title order={1} mb="xl">
                Equator Principles (EP) Assessment Tool User Guide
            </Title>

            {/* Overview Section */}
            <Stack spacing="lg">
                <Text size="lg" mb="sm">
                    <b>Overview</b>
                </Text>
                <Text c="dimmed">
                    The Equator Principles (EP) Assessment Tool helps financial
                    institutions evaluate project-level compliance with EP
                    guidelines for responsible project financing. This guide
                    provides instructions for using the tool to assess,
                    document, and improve environmental and social risk
                    management practices.
                </Text>

                {/* Getting Started Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Getting Started</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>Creating a New Assessment</Text>
                        <Text size="sm">
                            Click the "New Assessment" button on the dashboard
                        </Text>
                    </List.Item>
                </List>

                {/* Conducting an Assessment Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Conducting an Assessment</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>Navigation and Structure</Text>
                        <List listStyleType="decimal" spacing="sm" ml="1.5rem">
                            <List.Item>
                                Project Identification and Risk Assessment
                            </List.Item>
                            <List.Item>Climate Change Considerations</List.Item>
                            <List.Item>
                                Environmental and Social Management
                            </List.Item>
                            <List.Item>
                                Human Rights and Stakeholder Engagement
                            </List.Item>
                            <List.Item>
                                Land Acquisition and Indigenous Peoples
                            </List.Item>
                            <List.Item>Labour and Working Conditions</List.Item>
                            <List.Item>Independent Review and Audits</List.Item>
                            <List.Item>Monitoring and Reporting</List.Item>
                            <List.Item>
                                Financial Institution Oversight
                            </List.Item>
                        </List>
                    </List.Item>
                </List>

                {/* Rating System Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Rating System</b>
                </Text>
                <Table striped highlightOnHover>
                    <thead className="bg-gray-50 text-xl">
                        <tr>
                            <th className="py-2">Readiness Level</th>
                            <th className="py-2">Description</th>
                        </tr>
                    </thead>
                    <tbody className="text-lg">
                        <tr>
                            <td>
                                <Text fw={700}>1 - Not Started</Text>
                            </td>
                            <td>
                                No policies, procedures, or systems in place
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <Text fw={700}>2 - Initial Planning</Text>
                            </td>
                            <td>Awareness and initial planning begun</td>
                        </tr>
                        <tr>
                            <td>
                                <Text fw={700}>3 - In Development</Text>
                            </td>
                            <td>
                                Active work underway but not yet fully
                                implemented
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <Text fw={700}>4 - Partially Implemented</Text>
                            </td>
                            <td>Some aspects in place but gaps remain</td>
                        </tr>
                        <tr>
                            <td>
                                <Text fw={700}>5 - Fully Implemented</Text>
                            </td>
                            <td>
                                All necessary elements implemented with
                                monitoring
                            </td>
                        </tr>
                    </tbody>
                </Table>

                {/* Completing Each Section Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Completing Each Section</b>
                </Text>
                <List listStyleType="decimal" spacing="sm">
                    <List.Item>
                        <Text>Assign an appropriate Readiness Level (1-5)</Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Add Evidence to justify your rating (documentation,
                            observations, interviews)
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>Specify Actions Required for improvement</Text>
                    </List.Item>
                    <List.Item>
                        <Text>The system saves assessment automatically</Text>
                    </List.Item>
                </List>

                {/* Documentation and Evidence Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Documentation and Evidence</b>
                </Text>
                <List listStyleType="decimal" spacing="sm">
                    <List.Item>
                        <Text>
                            Click the "Add Search Evidence" button within each
                            criterion
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Select file(s) from your computer or cloud storage
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>Add descriptive labels to each file</Text>
                    </List.Item>
                    <List.Item>
                        <Text>Click "Upload"</Text>
                    </List.Item>
                </List>
                <Text size="sm" c="dimmed">
                    The system accepts PDF, Word, Excel, JPG, and PNG files up
                    to 20 MB each.
                </Text>

                {/* Adding Required Action Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Adding Required Action</b>
                </Text>
                <List listStyleType="decimal" spacing="sm">
                    <List.Item>
                        <Text>
                            Enter Action Item: In the "Action item" field, type
                            a clear and specific description of the action
                            required (e.g., "Conduct staff training on ESMS")
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Set Due Date: Use the date picker next to the
                            "mm/dd/yyyy" field to select a deadline for
                            completing the action (e.g., 06/15/2025)
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Tag Owner: In the "Tag Owner" dropdown, select the
                            responsible individual or team member to assign
                            ownership
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Click "Add Action" button to save the action item
                        </Text>
                    </List.Item>
                </List>

                {/* Generating Reports Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Generating Reports</b>
                </Text>
                <List listStyleType="decimal" spacing="sm">
                    <List.Item>
                        <Text>From the dashboard, select the assessment</Text>
                    </List.Item>
                    <List.Item>
                        <Text>Click "Assess and Generate Report"</Text>
                    </List.Item>
                </List>

                {/* Scoring and Evaluation Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Scoring and Evaluation</b>
                </Text>

                <Table striped highlightOnHover>
                    <thead className="bg-gray-50 text-xl">
                        <tr>
                            <th className="py-2">Readiness Level</th>
                            <th className="py-2">Score Range</th>
                            <th className="py-2">Grade</th>
                            <th className="py-2">Description</th>
                        </tr>
                    </thead>
                    <tbody className="text-lg">
                        <tr>
                            <td>
                                <Text fw={700}>1 - Not Started</Text>
                            </td>
                            <td>0.0 - 0.083333</td>
                            <td>D-</td>
                            <td>Very poor EP compliance</td>
                        </tr>
                        <tr>
                            <td>
                                <Text fw={700}>5 (global best practice)</Text>
                            </td>
                            <td>0.016667 - 1.0</td>
                            <td>A+</td>
                            <td>World-class EP compliance</td>
                        </tr>
                    </tbody>
                </Table>

                {/* Tip Section */}
                <Paper shadow="sm" p="md" radius="md" mt="xl" withBorder>
                    <Group position="apart">
                        <ThemeIcon variant="light" radius="xl" size="2rem">
                            <FaCheck size="1.2rem" />
                        </ThemeIcon>
                        <Text fw={500}>
                            You may return to incomplete sections later.
                        </Text>
                    </Group>
                </Paper>
            </Stack>
        </Box>
    );
}
