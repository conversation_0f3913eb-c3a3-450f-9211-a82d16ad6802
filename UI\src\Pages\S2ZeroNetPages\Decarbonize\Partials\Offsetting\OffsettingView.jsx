import { Button } from "@mantine/core";
import React from "react";
import { useTranslation } from "react-i18next";
import { CgSandClock } from "react-icons/cg";
import { ImLeaf } from "react-icons/im";
import { LuPanelRightClose } from "react-icons/lu";
import { RxBarChart } from "react-icons/rx";

export default function OffsettingView() {
  const { t } = useTranslation();
  return (
    <div className="relative">
      <div className="absolute inset-0 bg-[#f7f4f4f7] bg-opacity-100 flex items-start pt-20 justify-center z-10 rounded-lg">
        <h2 className="flex items-center justify-center gap-3 py-12 text-2xl text-center animate-pulse">
          <CgSandClock /> {t("Coming Soon")}
        </h2>
      </div>
      <div>
        <div className="bg-white p-5 rounded-lg shadow-lg">
          <h1 className="text-lg font-bold font-inter">Offsetting </h1>
          <p className="text-sm font-normal text-[#667085]">
            Offset Your Carbon Footprint Today
          </p>
        </div>
        <div className="mt-5">
          <h1 className="text-2xl font-semibold">Why Offset Your Carbon?</h1>
          <p className="text-base font-medium text-[#667085]">
            By offsetting your carbon emissions, you're taking a crucial step
            towards combating climate change. Our decarbonisation system allows
            you to neutralise your environmental impact and contribute to a
            sustainable future.
          </p>
        </div>
        <div className="bg-white p-5 my-5 rounded-lg shadow-lg ">
          <h1 className="text-sm font-semibold text-gray-500">How It Works</h1>
          <div className="flex items-center my-5">
            <p className="me-3">
              <ImLeaf className="text-secondary-300 w-5 h-5" />
            </p>
            <div>
              <h1 className="text-sm font-semibold">Calculate</h1>
              <p className="text-xs font-medium text-[#667085]">
                Measure your carbon footprint using our advanced calculator.
              </p>
            </div>
          </div>
          <div className="flex items-center my-5">
            <p className="me-3">
              <RxBarChart className="text-secondary-300 w-5 h-5" />
            </p>
            <div>
              <h1 className="text-sm font-semibold">Choose</h1>
              <p className="text-xs font-medium text-[#667085]">
                Select from our verified offset projects that align with your
                values.
              </p>
            </div>
          </div>
          <div className="flex items-center my-5">
            <p className="me-3">
              <LuPanelRightClose className="text-secondary-300 w-5 h-5" />
            </p>
            <div>
              <h1 className="text-sm font-semibold">Offset</h1>
              <p className="text-xs font-medium text-[#667085]">
                Contribute to projects that reduce or remove carbon from the
                atmosphere.
              </p>
            </div>
          </div>
        </div>
        <div className="mt-5">
          <h1 className="text-2xl font-semibold">Our Projects</h1>
          <p className="text-base font-medium text-[#667085]">
            Select from our verified offset projects that align with your
            values.
          </p>
        </div>
        <div className="grid md:grid-cols-2 gap-5">
          <div className="bg-white p-5 mt-5 rounded-xl ">
            <h1 className="text-lg font-bold font-inter mt-40">
              Reforestation in the Amazon
            </h1>
            <p className="text-base font-medium text-[#667085]">
              Help restore the lungs of our planet by supporting large-scale
              tree planting initiatives.
            </p>
            <Button className="mt-3 rounded-lg bg-secondary-300 hover:bg-secondary-300">
              Learn More
            </Button>
          </div>
          <div className="bg-white p-5 mt-5 rounded-xl ">
            <h1 className="text-lg font-bold font-inter mt-40">
              Solar Energy in Africa
            </h1>
            <p className="text-base font-medium text-[#667085]">
              Invest in clean energy solutions that provide sustainable power to
              communities in need.
            </p>
            <Button className="mt-3 rounded-lg bg-secondary-300 hover:bg-secondary-300">
              Learn More
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
