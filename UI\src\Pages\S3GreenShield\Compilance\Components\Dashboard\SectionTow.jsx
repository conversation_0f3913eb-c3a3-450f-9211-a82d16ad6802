import { Progress } from "@mantine/core";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { FiDownload } from "react-icons/fi";
import { MdCreditScore, MdOutlineRemoveRedEye } from "react-icons/md";
import measure from '@/assets/images/mesure.png'
import axios from "axios";
import Cookies from "js-cookie";
const SectionTow = () => {
  const { t } = useTranslation();
  const [assessment, setAssessment] = useState({})


  useEffect(() => {
    const fetchComplianceScore = async () => {
      try {
        const token = Cookies.get("level_user_token");
        if (!token) {
          console.error("No token found in localStorage");
          return;
        }

        const response = await axios.get(
          "https://portals3-staging-dwdwc5d6dnhyhpbb.uksouth-01.azurewebsites.net/compliance-assessement/lastAssessment",
          {
            headers: {
              Authorization: `Bear<PERSON> ${token}`,
            },
            withCredentials: true,
          }
        );
        console.log(response.data)
        setAssessment(response?.data)
      } catch (error) {
        console.error("Error fetching compliance assessment:", error);
      }
    };

    fetchComplianceScore();
  }, []);


  return (
    <>
      <div className="flex">
        <h3 className="my-12 text-xl font-medium mx-auto">Compliance Overview</h3>
      </div>     
       <div className="flex justify-center gap-5 gap-y-5 lg:flex-nowrap flex-wrap">
        <div className="card-div relative flex flex-col justify-between w-[326.73px] h-[166px] bg-white py-7 px-4 rounded-2xl">
          <div className="w-[60px] h-[50px] flex justify-center items-center bg-secondary-300 rounded-2xl absolute -top-7 left-[40%] ">
            <MdCreditScore size={30} color="white" />
          </div>
          <h3 className="text-center text-xl font-medium ">Compliance Score</h3>
          <div className="pr">
            <div className="flex justify-between">
              {" "}
              <small>Compliance Score</small> <small>{assessment.complianceScore ? assessment.complianceScore.toFixed(2) : 0}</small>{" "}
            </div>
            <Progress color={"#00C0A9"} value={assessment.complianceScore ? assessment.complianceScore : 0} size={5} mb="xs" />
          </div>
          <h3 className="text-center">Last compliance level</h3>
        </div>

        <div className="risk-level flex flex-col justify-between w-[320px] h-[166px] bg-white py-7 px-4 rounded-2xl relative">
          <div className="w-[60px] h-[50px] flex justify-center items-center bg-secondary-300 rounded-2xl absolute -top-7 left-[40%] ">
            <img src={measure} alt="measure" />
          </div>
          <h3 className="text-center text-xl font-medium">Final Score</h3>
          <span className="mx-auto text-xl">{assessment.finalScore ? assessment.finalScore : 0}</span>
          <h3 className="">Based on current assessment</h3>
        </div>

        {/* <div className="flex flex-wrap items-center gap-6 max-w-[300px] ">
          <button className="border-[1px] flex items-center px-4 justify-between lg:w-full h-[56px] text-[17px] font-bold rounded-lg text-secondary-300 border-secondary-300 bg-white hover:text-white hover:bg-secondary-300">
            <span>{t("viewReport")}</span>
            <span>
              <MdOutlineRemoveRedEye className="text-lg" />
            </span>
          </button>

          <button className="border-[1px] flex items-center px-4 justify-between lg:w-full h-[56px] text-sm font-bold rounded-lg text-white bg-primary ease-out duration-200 hover:translate-y-2 hover:bg-secondary-400">
            <span>{t("downloadReport")}</span>
            <span>
              <FiDownload className="text-lg" />
            </span>
          </button>
        </div> */}
      </div>
    </>
  );
};

export default SectionTow;
