import SupplierData from "./Partials/SupplierData";
import S2Layout from "@/Layout/S2Layout";
import { useLocation } from "react-router";
import { Tabs } from "@mantine/core";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import { useState } from "react";
import SupplierInputs from "./Partials/SupplierInputs";
import { useTranslation } from "react-i18next";
import { IoMdHome } from "react-icons/io";
import GuideModalButton from "@/Components/Guide/GuideModalButton";
import Guide from "./Guide";

const SupplierView = () => {
    const { pathname } = useLocation();
    const { netZeroMenu } = useSideBarRoute();

    const [activeTab, setActiveTab] = useState(pathname.split("/")[3]);
    const { t } = useTranslation();
    return (
        <S2Layout
            menus={netZeroMenu}
            breadcrumbItems={[
                { title: <IoMdHome size={20} />, href: "/get-started" },
                {
                    title: "Scope3Connect",
                    href: "#",
                },
            ]}
            navbarTitle={"Scope3Connect"}
        >
            <div className="pb-8">
                <div className="w-full justify-between flex items-center py-5 px-3">
                    <GuideModalButton buttonText="  Add Supplier Guide">
                        <Guide />
                    </GuideModalButton>
                </div>
                <Tabs
                    className=""
                    defaultValue={"Add-Supplier"}
                    onChange={setActiveTab}
                >
                    <Tabs.List
                        justify="center"
                        className="mb-6 flex justify-around text-gray-500 py-3 rounded-md overflow-hidden bg-white before:border-none"
                    >
                        <Tabs.Tab
                            value="Add-Supplier"
                            className={`text-lg  
                ${
                    activeTab == "Add-Supplier"
                        ? "text-primary border-b-2 border-primary"
                        : " border-none"
                } 
                py-3 font-semibold hover:bg-transparent hover:opacity-100`}
                        >
                            {t("Add Supplier")}
                        </Tabs.Tab>

                        <Tabs.Tab
                            value="Suppliers-Information"
                            className={`text-lg  ${
                                activeTab == "Suppliers-Information"
                                    ? "text-primary border-b-2 border-primary"
                                    : " border-none"
                            } 
            py-3 font-semibold hover:bg-transparent hover:opacity-100`}
                        >
                            {t("Suppliers Information")}
                        </Tabs.Tab>
                    </Tabs.List>

                    <Tabs.Panel value="Add-Supplier">
                        <SupplierInputs activeTab={activeTab} />
                    </Tabs.Panel>

                    <Tabs.Panel value="Suppliers-Information">
                        <SupplierData activeTab={activeTab} />
                    </Tabs.Panel>
                </Tabs>
            </div>
        </S2Layout>
    );
};

export default SupplierView;
