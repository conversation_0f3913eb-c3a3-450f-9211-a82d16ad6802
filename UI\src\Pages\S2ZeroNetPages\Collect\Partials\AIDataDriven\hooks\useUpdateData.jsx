import { useState } from "react";
import axios from "axios";
import Cookies from "js-cookie";
import { useGetData } from "./useGetData";

export const useUpdateData = () => {
  const [loading, setLoading] = useState(false);
  const { handleGetData } = useGetData();

  const handleUpdateData = async (updateData, id) => {
    if (!updateData) {
      alert("No Data");
      return;
    }
    setLoading(true);
    try {
      const { data } = await axios.patch(
        `https://pdf-extraction-staging.azurewebsites.net/update/${id}`,
        { data: updateData },
        {
          headers: {
            "Content-Type": "application/json",
            withCredentials: true,
            Authorization: `Bearer ${Cookies.get("level_user_token")}`,
          },
        }
      );
      console.log(data);
      setLoading(false);
      handleGetData();
    } catch (error) {
      console.log(error);
      setLoading(false);
    }
  };

  return { loading, handleUpdateData };
};
