import { useEffect, useState } from "react";
import History from "@/Components/ChainSight/Reporting/History";
import Overall from "@/Components/ChainSight/Reporting/Overall";
import Cards from "@/Components/ChainSight/Reporting/Cards";
import Charts from "@/Components/ChainSight/Reporting/Charts";
import StrengthsGaps from "@/Components/ChainSight/Reporting/StrengthsGaps";
import PriorityActions from "@/Components/ChainSight/Reporting/PriorityActions";
import MaturityLevelScale from "@/Components/ChainSight/Reporting/MaturityLevelScale";
import axios from "axios";
import Cookies from "js-cookie";
import { NoAssessmentIcon } from "@/assets/icons";

const Reporting = ({ isActive }) => {
  const url = "https://chain-sight-staging.azurewebsites.net";
  const [latestReport, setLatestReport] = useState(null);
  const [loading, setLoading] = useState(true);

  const fetchData = async () => {
    try {
      const response = await axios.get(
        `${url}/api/assessments/analysis/last`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("level_user_token")}`,
          },
        }
      );

      if (response.status === 200) {
        setLoading(false);
        const data = response.data;
        setLatestReport(data);
      }
    } catch (error) {
      console.error("Error fetching reporting data:", error);
    }
  };

  useEffect(() => {
    fetchData();
  }, [isActive]);

  if (!latestReport) {
    return (
      <div className="flex flex-col gap-4 items-center justify-center h-screen">
        <NoAssessmentIcon />
        <div className="text-center">
          <p className="text-gray-500">
            Your personalized results will appear once you&apos;ve finished the
            assessment.
          </p>
          <p className="text-gray-500">
            Complete it now to unlock your analysis.
          </p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div>
      <History latestReport={latestReport} />
      <Overall latestReport={latestReport} />
      <Cards latestReport={latestReport} />
      <Charts latestReport={latestReport} />
      <StrengthsGaps latestReport={latestReport} />
      <PriorityActions latestReport={latestReport} />
      <MaturityLevelScale latestReport={latestReport} />
    </div>
  );
};

export default Reporting;
