import React, { useEffect, useState } from "react";
import cx from "clsx";
import {
  Table,
  Checkbox,
  ScrollArea,
  rem,
  Button,
  TextInput,
} from "@mantine/core";
import { RiDeleteBin6Line } from "react-icons/ri";

export default function AddNewIncidentTable({ onSetData }) {
  const [data, setData] = useState([]);
  const [selection, setSelection] = useState([]);

  useEffect(() => {
    if (data) {
      onSetData(data);
    }
  }, [data]);

  const toggleRow = (id) =>
    setSelection((current) =>
      current.includes(id)
        ? current.filter((item) => item !== id)
        : [...current, id]
    );

  const toggleAll = () =>
    setSelection((current) => {
      if (data.length === 0) return [];
      return current.length === data.length ? [] : data.map((item) => item.id);
    });

  const addNewRow = () => {
    setData([
      ...data,
      {
        isNew: true,
        fullName: "",
        jobRole: "",
        email: "",
      },
    ]);
  };

  const updateRowData = (id, field, value) => {
    setData((currentData) =>
      currentData.map((item) =>
        item.id === id ? { ...item, [field]: value } : item
      )
    );
  };

  const deleteRow = (id) => {
    setData(data.filter((item) => item.id !== id));
  };

  const rows = data.map((item) => {
    const selected = selection.includes(item.id);

    return (
      <Table.Tr
        key={item.id} // Use the unique ID as the key
        className={`${cx({
          ["bg-[#07838F1A]"]: selected,
        })} text-sm font-bold text-[#626364] text-center`}
      >
        <Table.Td>
          <span
            className="flex justify-center items-center text-xl cursor-pointer"
            onClick={() => deleteRow(item.id)}
          >
            <RiDeleteBin6Line />
          </span>
        </Table.Td>
        <Table.Td>
          <Checkbox
            checked={selection.includes(item.id)}
            onChange={() => toggleRow(item.id)}
            color="#07838F"
          />
        </Table.Td>
        <Table.Td>
          {item.isNew ? (
            <TextInput
              value={item.fullName}
              onChange={(e) =>
                updateRowData(item.id, "fullName", e.target.value)
              }
              placeholder="enter text ..."
            />
          ) : (
            item.fullName
          )}
        </Table.Td>
        <Table.Td>
          {item.isNew ? (
            <TextInput
              value={item.jobRole}
              onChange={(e) =>
                updateRowData(item.id, "jobRole", e.target.value)
              }
              placeholder="enter text ..."
            />
          ) : (
            item.jobRole
          )}
        </Table.Td>
        <Table.Td>
          {item.isNew ? (
            <TextInput
              value={item.email}
              onChange={(e) => updateRowData(item.id, "email", e.target.value)}
              placeholder="enter text ..."
            />
          ) : (
            item.email
          )}
        </Table.Td>
      </Table.Tr>
    );
  });

  return (
    <div className="bg-white p-5 rounded-lg mt-7">
      <div className="w-full">
        <Button
          className="bg-[#05808b57] border-2 border-primary font-bold text-primary hover:bg-[#05808b57] hover:text-primary  w-full"
          size="lg"
          onClick={addNewRow}
        >
          Add New Individual
        </Button>
      </div>
      <ScrollArea>
        <Table miw={800} verticalSpacing="sm">
          <Table.Thead className="border-b-2 border-primary pb-6 text-primary font-bold text-base text-center">
            <Table.Tr>
              <Table.Th className="text-center">Delete</Table.Th>
              <Table.Th style={{ width: rem(40) }}>
                <Checkbox
                  onChange={toggleAll}
                  checked={data.length > 0 && selection.length === data.length}
                  indeterminate={
                    data.length > 0 &&
                    selection.length > 0 &&
                    selection.length !== data.length
                  }
                  color="#07838F"
                />
              </Table.Th>
              <Table.Th className="text-center">Full Name</Table.Th>
              <Table.Th className="text-center">Job Role</Table.Th>
              <Table.Th className="text-center">Email</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>{rows}</Table.Tbody>
        </Table>
      </ScrollArea>
      {/* <!-- Pagination --> */}
    </div>
  );
}
