
export const FINANCIAL_IMPACT_TYPES = [
  { value: "risk", label: "Risk" },
  { value: "opportunity", label: "Opportunity" },
];
export const IMPACT_TIMEFRAMES = [
  { value: "shortTerm", label: "Short-term (<1 year)" },
  { value: "mediumTerm", label: "Medium-term (1-3 years)" },
  { value: "longTerm", label: "Long-term (>3 years)" },
];

export const STAKEHOLDERS = [
  { value: "employees", label: "Employees" },
  { value: "suppliers", label: "Suppliers" },
  { value: "customers", label: "Customers" },
  { value: "communities", label: "Communities" },
  { value: "investors", label: "Investors" },
];

export const REPUTATIONAL_IMPACT_SCALE = [
  { value: "1", label: "Insignificant (1)" },
  { value: "2", label: "Minor (2)" },
  { value: "3", label: "Moderate (3)" },
  { value: "4", label: "Major (4)" },
  { value: "5", label: "Critical (5)" }
];

export const ROOT_CAUSE_CATEGORIES = [
  { value: "people", label: "People" },
  { value: "process", label: "Process" },
  { value: "technology", label: "Technology" },
  { value: "externalFactors", label: "External Factors" },
  { value: "governance", label: "Governance" },
  { value: "regulatory", label: "Regulatory" },
];

export const SDG_OPTIONS = Array.from({ length: 17 }, (_, i) => ({
  value: `sdg${i + 1}`,
  label: `SDG ${i + 1}`,
}));

export const EXPOSURE_LEVELS = [
  { value: "critical", label: "Critical" },
  { value: "high", label: "High" },
  { value: "medium", label: "Medium" },
  { value: "low", label: "Low" },
];

export const Frequency = [
  { value: "daily", label: "Daily" },
  { value: "weekly", label: "Weekly" },
  { value: "monthly", label: "Monthly" },
  { value: "yearly", label: "Yearly" },
];

export const domains = [
  "Physical Climate",
  "Circular Economy",
  "Transition Risk",
  "Natural Capital",
  "Waste Management",
];
export const scopeChain = ["Operations", "Upstream", "Downstream"];
export const impactTypes = ["Direct", "Indirect"];
export const actionStatusData = ['Pending', 'In Progress', 'Completed'];
export const Categories = [
  "Strategic",
  "Operational",
  "Financial",
  "ESG"
];

export const likelihoodLevels = ["Rare (1)", "Unlikely (2)", "Possible (3)", "Likely (4)", "Very Likely (5)"];

export const ConsequencesData = [
  "Social",
  "Environmental",
  "Legal/Regulatory",
  "Reputational",
  "Operational",
  "Financial"
];
export const strategies = ["Exploit", "Avoid", "Transfer", "Accept", "Mitigate"];


// value percentage and label

export const effectivenessLevels = [
  { value: '10', label: "Poor/None (10% reduction)" },
  { value:  "30", label: "Weak (30% reduction)" },
  { value: "50", label: "Moderate (50% reduction)" },
  { value:  "70", label: "Effective (70% reduction)" },
  { value:  "90", label: "Very Effective (90% reduction)" },
];

export const trends = [
  'Increasing', 'Stable', 'Decreasing' 
];

// inherent risk data
// Scale 1-5 (Low, Medium, High, Top, Extreme) 
export const inherentRiskData = [
  { value: "1", label: "Low" },
  { value: "2", label: "Medium" },
  { value: "3", label: "High" },
  { value: "4", label: "Top" },
  { value: "5", label: "Critical" },
]


export const thresholdLevels = [
  {
    value: "low",
    label: "Low",
    color: "#00d25b",
    bgColor: "rgba(0, 210, 91, 0.2)",
  },
  {
    value: "medium",
    label: "Medium",
    color: "#ffab00",
    bgColor: "rgba(255, 171, 0, 0.2)",
  },
  {
    value: "high",
    label: "High",
    color: "#b07b14",
    bgColor: "#ffa500de",
  },
  {
    value: "critical",
    label: "Critical",
    color: "#AB0202",
    bgColor: "#AB02024D",
  },
];

export const aiTextType = ["refine", "shorten", "lengthen"]