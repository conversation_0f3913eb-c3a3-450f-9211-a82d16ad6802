import Dashboard from '@/Pages/ESGCompassSuite/GreenSightAI/Dashboard';
import Result from '@/Pages/ESGCompassSuite/GreenSightAI/ResultPage/Result.jsx';
import { Navigate, Route } from 'react-router';
// import SummaryPage from "@/Pages/SummaryPage";
import DoubleMaterialityView from '@/Pages/ESGCompassSuite/doubleMateriality/DoubleMaterialityView';
import DoubleMaterialityAssessment from '@/Pages/ESGCompassSuite/doubleMateriality/Partials/DoubleMaterialityAssessment/DoubleMaterialityAssessment';
import DoubleMaterialityReadiness from '@/Pages/ESGCompassSuite/doubleMateriality/Partials/DoubleMaterialityReadiness/DoubleMaterialityReadiness';
import DoubleMaterialityReporting from '@/Pages/ESGCompassSuite/doubleMateriality/Partials/DoubleMaterialityReporting/DoubleMaterialityReporting';
import GetStart from '@/Pages/GetStart/GetStart';
// import Csrd from "@/Pages/ESGCompassSuite/CSRD/Csrd";
import CsrdReport from '@/Pages/ESGCompassSuite/CSRD/Partials/CsrdReport';
import CsrdScopes from '@/Pages/ESGCompassSuite/CSRD/Partials/CsrdScopes';
import CsrdTable from '@/Pages/ESGCompassSuite/CSRD/Partials/CsrdTable';
import DecisionSight from '@/Pages/ESGCompassSuite/DecisionSight/DecisionSight';
import DSDashboard from '@/Pages/ESGCompassSuite/DecisionSight/Partials/DSDashboard/DSDashboard';
import DSDataQuality from '@/Pages/ESGCompassSuite/DecisionSight/Partials/DSDataQuality';
import DSRecommendations from '@/Pages/ESGCompassSuite/DecisionSight/Partials/DSRecommendations/DSRecommendations';
import ScenarioPlanningView from '@/Pages/ESGCompassSuite/DecisionSight/Partials/ScenarioPlanning/ScenarioPlanningView';
import DSReport from '@/Pages/ESGCompassSuite/DecisionSight/Partials/DSReport';
import Diagnosis from '@/Pages/ESGCompassSuite/GreenSightAI/questions/Diagnosis';
// import SelectAssessmentType from "@/Pages/ESGCompassSuite/AssessmentType/SelectAssessmentType";
import SelectAssessmentTypeWithContext from '@/Pages/ESGCompassSuite/AssessmentType/SelectAssessmentTypeWithContext';
import CSRDWithContext from '@/Pages/ESGCompassSuite/CSRD/CSRDWithContext';
import SummaryPage from '@/Pages/ESGCompassSuite/GreenSightAI/Summary/SummaryPage';
import PortfolioPage from '@/Pages/ESGCompassSuite/GreenSightAI/Portfolio/PortfolioPage';
import SdgAlignmentView from '@/Pages/ESGCompassSuite/SDG/Partials/SdgAlignment/SdgAlignmentView';
import SdgImpactView from '@/Pages/ESGCompassSuite/SDG/Partials/SdgImpact/SdgImpactView';
import SdgView from '@/Pages/ESGCompassSuite/SDG/SdgView';
// import SdgReport from "@/Pages/ESGCompassSuite/SDG/Partials/SdgReport/Partials/SdgReport";
import SdgReportView from '@/Pages/ESGCompassSuite/SDG/Partials/SdgReport/SdgReportView';
import GreenSight from '@/Pages/ESGCompassSuite/GreenSightAI/GreenSight';
import NotificationsDashboard from "@/Pages/Dashboard/Notifications";

import IFC_GCF from "@/Pages/ESGCompassSuite/IFC_GCF";
import EquatorPrinciplesAssessment from "@/Pages/ESGCompassSuite/EquatorPrinciplesAssessment";

const ESGPulseRoutes = () => {
  return (
    <>
      <Route path="/dashboard" element={<Dashboard />} />
      <Route
          path="/dashboard/notifications"
          element={<NotificationsDashboard />}
      />
      <Route path="/Insights-reporing/greensight" element={<GreenSight />} />
      <Route path="/Insights-reporing/greensight/summary" element={<SummaryPage />} />
      <Route path="/Insights-reporing/greensight/portfolio" element={<PortfolioPage />} />
      <Route path="/Insights-reporing/greensight/diagnosis" element={<Diagnosis />} />

      <Route path="/get-started" element={<GetStart />} />
      <Route path="/select-assessment" element={<SelectAssessmentTypeWithContext />} />
      <Route path="/result" element={<Result />} />

      <Route path="/Insights-reporing/materiality-assessment" element={<DoubleMaterialityView />}>
        <Route index={true} element={<DoubleMaterialityReadiness />} />
        <Route path="Assessment" element={<DoubleMaterialityAssessment />} />
        <Route path="Reporting" element={<DoubleMaterialityReporting />} />
      </Route>

      <Route path="/Insights-reporing/financeguard/ifc-gcf" element={<IFC_GCF />} />
      <Route path="/Insights-reporing/financeguard/equator-principles" element={<EquatorPrinciplesAssessment />} />

      <Route path="/Insights-reporing/sdg-impact" element={<SdgView />}>
        <Route index={true} element={<SdgAlignmentView />} />
        <Route path="impact-measure" element={<SdgImpactView />} />
        <Route path="report" element={<SdgReportView />} />
      </Route>

      <Route path="/Grc/regulatory-readiness/csrd-dashboard" element={<CSRDWithContext />}>
        <Route index={true} element={<CsrdScopes />} />
        <Route path="selectedScope/:scopeTitle/:assessmentTitle" element={<CsrdTable />} />
      </Route>

      <Route path="/csrd-report" element={<CsrdReport />} />

      <Route path="/dashboard/DecisionSight" element={<DecisionSight />}>
        <Route index element={<Navigate to="Dashboard" />} />
        <Route path="Dashboard" element={<DSDashboard />} />
        <Route path="DataQuality" element={<DSDataQuality />} />
        <Route path="DSRecommendations" element={<DSRecommendations />} />
        <Route path="ScenarioPlanning" element={<ScenarioPlanningView />} />
      </Route>
      <Route path="/DecisionSight-report" element={<DSReport />} />
    </>
  );
};

export default ESGPulseRoutes;
