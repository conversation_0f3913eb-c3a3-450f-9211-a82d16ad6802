import {
    Box,
    Title,
    Text,
    List,
    Stack,
    Paper,
    Group,
    ThemeIcon,
} from "@mantine/core";
import { FaCheck } from "react-icons/fa";

export default function CarbonSystemUserGuide() {
    return (
        <Box mx="auto" maxWidth="1200px" p="md">
            <Title order={1} mb="xl">
                Carbon System User Guide
            </Title>

            <Stack spacing="lg">
                {/* Monitor Data Section */}
                <Text size="lg" mb="sm">
                    <b>Monitor Data from the Dashboard</b>
                </Text>
                <Text size="sm" c="dimmed" mb="sm">
                    Navigation: Emissions Calculation &gt; Emissions Overview
                </Text>

                <Text size="lg" mt="lg" mb="sm">
                    <b>This view provides a quick overview of:</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>Scope 1, 2, and 3 emissions</List.Item>
                    <List.Item>Monthly comparison (bar chart)</List.Item>
                    <List.Item>CO₂e emissions to date</List.Item>
                    <List.Item>Emissions trends over time</List.Item>
                </List>

                <Text size="lg" mt="lg" mb="sm">
                    <b>You can hover for:</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>Detailed values</List.Item>
                    <List.Item>Source factor</List.Item>
                    <List.Item>% change from previous month</List.Item>
                </List>

                {/* Optional Tip Banner */}
                <Paper shadow="sm" p="md" radius="md" mt="xl" withBorder>
                    <Group position="apart">
                        <ThemeIcon variant="light" radius="xl" size="2rem">
                            <FaCheck size="1.2rem" />
                        </ThemeIcon>
                        <Text fw={500}>
                            Hover over data points for granular insights during
                            stakeholder reviews!
                        </Text>
                    </Group>
                </Paper>
            </Stack>
        </Box>
    );
}
