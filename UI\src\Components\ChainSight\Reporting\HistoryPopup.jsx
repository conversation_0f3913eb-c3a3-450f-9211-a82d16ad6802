import Loading from "@/Components/Loading";
import Share from "./Share";
import ViewPDF from "./ViewPDF";
import { Button, Modal, ScrollArea, Table } from "@mantine/core";
import { useEffect, useState } from "react";
import { FiDownload } from "react-icons/fi";
import Cookies from "js-cookie";

function HistoryPopup({ opened, close }) {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState();

  const getAssessmentHistory = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        "https://chain-sight-staging.azurewebsites.net/api/assessments/reports",
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${Cookies.get("level_user_token")}`,
          },
        }
      );
      const data = await response.json();
      setData(data);
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.error("Error fetching assessment history:", error);
    }
  };

  const handleDownloadPDF = async (PDFLink, item) => {
    if (!PDFLink) {
      console.error("No report URL available");
      return;
    }

    // Fetch the PDF as a binary blob
    const response = await fetch(PDFLink, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${Cookies.get("level_user_token")}`,
      },
    });
    if (!response.ok) {
      console.error("Failed to download report");
      return;
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.style.display = "none";
    a.href = url;
    a.download = `Sustainable_Procurement_Evaluation_${item.id}.pdf`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    a.remove();
  };

  useEffect(() => {
    getAssessmentHistory();
  }, []);

  const rows = data?.map((item) => (
    <Table.Tr key={`${item.id}-${item.name}`}>
      <Table.Td className="text-center">
        <p>{item.createdAt.slice(0, 10)}</p>
      </Table.Td>
      <Table.Td className="text-center">
        <p>{item.total_score}/{item.data.score.full_score}</p>
      </Table.Td>
      <Table.Td className="text-center">
        <p>{item.maturity_level}</p>
      </Table.Td>
      <Table.Td className="mx-auto ">
        <div className="flex justify-center items-center  h-12 mx-auto">
          <ViewPDF
            btnStyle={
              item?.report_url
                ? "border-[1px] flex items-center px-2 justify-between  h-[40px] text-[17px] font-bold rounded-lg text-secondary-300 border-secondary-300 bg-white hover:text-white hover:bg-secondary-300"
                : "border-[1px] flex items-center px-2 justify-between h-[40px] text-[17px] font-bold rounded-lg text-white border-gray-300 bg-gray-300 hover:text-white hover:bg-gray-300 cursor-not-allowed"
            }
            pdfUrl={item?.report_url}
            text={"View Report"}
            disabled={!item?.report_url && true}
          />
        </div>
      </Table.Td>
      <Table.Td className="mx-auto">
        <div className="flex justify-center items-center">
          <Share
            link={item?.report_url}
            btnStyle={
              item?.report_url
                ? "border-[1px] flex items-center px-2 justify-between h-[40px] text-[17px] font-bold rounded-lg text-secondary-300 border-secondary-300 bg-white hover:text-white hover:bg-secondary-300"
                : "border-[1px] flex items-center px-2 justify-between h-[40px] text-[17px] font-bold rounded-lg text-white border-gray-300 bg-gray-300 hover:text-white hover:bg-gray-300 cursor-not-allowed"
            }
            disabled={!item?.report_url && true}
          />
        </div>
      </Table.Td>
      <Table.Td className="mx-auto">
        <div className="flex justify-center items-center">
          <Button
            component="a"
            className={
              item?.report_url
                ? "border-[1px] flex items-center px-4 justify-between h-[40px] text-sm font-bold rounded-lg text-white bg-secondary-300 ease-out duration-200 hover:translate-y-2 hover:bg-secondary-400"
                : "border-[1px] flex items-center px-2 justify-between h-[40px] text-[17px] font-bold rounded-lg text-white border-gray-300 bg-gray-300 hover:text-white hover:bg-gray-300 cursor-not-allowed"
            }
            onClick={() => handleDownloadPDF(item?.report_url, item)}
            download
            disabled={!item?.report_url ? true : false}
          >
            Download Report
            <span>
              <FiDownload className="text-lg ms-1" />
            </span>
          </Button>
        </div>
      </Table.Td>
    </Table.Tr>
  ));
  return (
    <Modal
      opened={opened}
      onClose={close}
      centered
      size={"100%"}
      withCloseButton={true}
      title="Sustainable Procurement Evaluation History"
    >
      {loading ? (
        <Loading />
      ) : (
        <>
          <ScrollArea>
            <Table
              miw={800}
              verticalSpacing="sm"
              className="p-2 my-1 bg-white  rounded-xl"
            >
              <Table.Thead className="border-b-2 border pb-6 text-secondary-500 font-bold text-base text-center">
                <Table.Tr>
                  <Table.Th className="text-center">Date</Table.Th>
                  <Table.Th className="text-center">Score</Table.Th>
                  <Table.Th className="text-center">Maturity level</Table.Th>
                  <Table.Th className="text-center">View report</Table.Th>
                  <Table.Th className="text-center">Share</Table.Th>
                  <Table.Th className="text-center">Download report</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>{rows}</Table.Tbody>
            </Table>
          </ScrollArea>
        </>
      )}
    </Modal>
  );
}

export default HistoryPopup;
