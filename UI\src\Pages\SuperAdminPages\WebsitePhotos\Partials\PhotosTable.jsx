import { ScrollArea, Table } from "@mantine/core";
import React from "react";
import UserRow from "./UserRow";
import Loading from "@/Components/Loading";

const PhotosTable = ({ tableData = [], refetch, loading,position }) => {
  const rows = tableData.map((user, idx) => (
    <UserRow position={position} user={user} key={idx} refetch={refetch} />
  ));

  return (
    <div className="relative">
      {
        loading && <div className="absolute z-50 h-full w-full rounded-2xl bg-primary/50"> <Loading /> </div>
      }
      <ScrollArea>
        <Table miw={800} verticalSpacing="sm">
          <Table.Thead className="pb-6 text-base font-bold text-center border-b-2 border-primary text-primary">
            <Table.Tr>
              {" "}
              <Table.Th className="text-start">User Name </Table.Th>
              <Table.Th className="text-start">Image </Table.Th>
              <Table.Th className="text-start">Position </Table.Th>
              <Table.Th className="text-start">Actions </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody> {rows} </Table.Tbody>
        </Table>
      </ScrollArea>
    </div>
  );
};

export default PhotosTable;
