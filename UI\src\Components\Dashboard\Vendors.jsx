import React from "react";
import VendorsItem from "./VendorsItem";
import photoUser from "../../../public/assets/Images/img.jpg";
import { TbTriangleInvertedFilled } from "react-icons/tb";

function Vendors() {
  return (
    <div className="bg-white py-[1rem] px-[1.5rem] rounded-2xl shadow-2xl w-[90%]  xl:w-[60%] mx-auto">
      <div className="flex justify-between items-center mb-5">
        <h1 className="font-bold text-[18px] ">
          Vendors with Maximum Emissions
        </h1>
        <a
          href="#"
          className=" text-[14px] text-[#07838F] font-light border-b-[1px] border-[#07838F]"
        >
          View All
        </a>
      </div>
      <div>
        <VendorsItem
          img={photoUser}
          name="Amrooda Cement"
          country="United States"
          num="100123"
          icon=<TbTriangleInvertedFilled />
        />
        <VendorsItem
          img={photoUser}
          name="VXF Pipelines Ltd."
          country="United Arab Emirates"
          num="51122"
          icon=<TbTriangleInvertedFilled className="text-green-600" />
        />
        <VendorsItem
          img={photoUser}
          name="Amrooda Cement"
          country="United States"
          num="100123"
          icon=<TbTriangleInvertedFilled />
        />
      </div>
    </div>
  );
}

export default Vendors;
