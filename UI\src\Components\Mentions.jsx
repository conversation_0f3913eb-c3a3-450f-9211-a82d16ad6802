import { useState, useRef } from 'react'

export default function MentionInput({
  users = [],
  onChange,
  value = '',
  placeholder = 'Type @ to mention someone',
  className = '',
}) {
  const [input, setInput] = useState(value)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [filteredUsers, setFilteredUsers] = useState([])
  const [cursorPosition, setCursorPosition] = useState(0)
  const inputRef = useRef(null)

  const handleChange = (e) => {
    const val = e.target.value
    setInput(val)
    onChange?.(val)

    const lastAt = val.lastIndexOf('@')
    const afterAt = val.slice(lastAt + 1)

    if (lastAt !== -1 && (afterAt.length >= 1 || afterAt === '')) {
      const filtered = users.filter((u) =>
        u.name.toLowerCase().includes(afterAt.toLowerCase())
      )
      setFilteredUsers(filtered)
      setShowSuggestions(true)
    } else {
      setShowSuggestions(false)
    }

    setCursorPosition(e.target.selectionStart)
  }

  const handleSelectUser = (user) => {
    const before = input.slice(0, input.lastIndexOf('@'))
    const after = input.slice(cursorPosition)
    const newText = `${before}${user.name} ${after}`

    setInput(newText)
    setShowSuggestions(false)
    onChange?.(newText)

    setTimeout(() => inputRef.current.focus(), 0)
  }

  return (
    <div className={`relative w-full ${className}`}>
      <textarea
        ref={inputRef}
        value={input}
        onChange={handleChange}
        className="w-full p-3 border rounded-md resize-none focus:outline-none"
        placeholder={placeholder}
        rows={4}
      />

      {showSuggestions && filteredUsers.length > 0 && (
        <ul className="absolute z-10 w-full bg-white border rounded-md mt-1 max-h-48 overflow-y-auto shadow-md">
          {filteredUsers.map((user) => (
            <li
              key={user.id}
              onClick={() => handleSelectUser(user)}
              className="px-4 py-2 cursor-pointer hover:bg-gray-100"
            >
              {user.name}
            </li>
          ))}
        </ul>
      )}
    </div>
  )
}
