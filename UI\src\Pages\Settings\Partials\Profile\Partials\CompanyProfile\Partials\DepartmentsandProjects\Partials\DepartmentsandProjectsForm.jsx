import { useState, useEffect } from "react";
import { Button, TextInput, Select, MultiSelect } from "@mantine/core";
import ApiS2 from "@/Api/apiS2Config";
import { showNotification } from "@mantine/notifications";
import { useTranslation } from "react-i18next";
import { IoIosArrowDown } from "react-icons/io";
import Loading from "@/Components/Loading";
import countries from "world-countries";
import { driver } from "driver.js";
import "driver.js/dist/driver.css";

const prioritized = [
  "United Kingdom",
  "United Arab Emirates", 
  "United States",
  "Saudi Arabia",
  "Qatar",
];

// Sort countries: prioritized first, then the rest
const sortedCountries = [
  ...countries.filter((c) => prioritized.includes(c.name.common)),
  ...countries
    .filter((c) => !prioritized.includes(c.name.common))
    .sort((a, b) => a.name.common.localeCompare(b.name.common)),
];

// Common location options (you can customize this list based on your needs)
const locationOptions = [
  "Headquarters",
  "Branch Office",
  "Manufacturing Plant",
  "Warehouse",
  "Distribution Center",
  "Research & Development Center",
  "Sales Office",
  "Service Center",
  "Data Center",
  "Training Center",
  "Regional Office",
  "Remote Office",
  "Factory",
  "Laboratory",
  "Retail Store",
  "Call Center",
  "Logistics Hub",
  "Innovation Center",
  "Operations Center",
  "Customer Service Center"
];

// Type options for the dropdown
const typeOptions = [
  "Department",
  "Project"
];

const DepartmentsandProjectsForm = ({ onSuccess }) => {
  const [loading, setLoading] = useState({});
  const [data, setData] = useState([
    {
      id: Date.now().toString(),
      type: "", // Added missing type field
      name: "",
      region: "",
      country: "",
      sector: "",
      numberOfEmployees: "",
      locations: [],
      numberOfAssets: "",
    },
  ]);

  const { t } = useTranslation();

  const getGuideSteps = () => [
    {
      element: ".multi-select-emission",
      popover: {
        title: t(
          "Each asset can have one or multiple emission sources associated with it."
        ),
        description: t(
          "Dropdown to pick the emission source (e.g., Refrigerants, Purchased Electricity, etc.)"
        ),
        side: "bottom",
        align: "center",
      },
    },
    {
      element: ".input-site-name",
      popover: {
        title: t(
          "Enter the name of the site where the asset is located (e.g., Headquarters Building)."
        ),
        side: "bottom",
        align: "center",
      },
    },
    {
      element: ".input-asset-name",
      popover: {
        title: t(
          "Enter the unique name or identifier for the asset (e.g., Diesel Generator)."
        ),
        side: "bottom",
        align: "center",
      },
    },
    {
      element: ".select-country",
      popover: {
        title: t("Dropdown to select the country of the asset."),
        side: "bottom",
        align: "center",
      },
    },
    {
      element: ".input-reference",
      popover: {
        title: t(
          "Optional text to add a reference code or identifier for the asset, such as a serial number or internal code (e.g., DG-001)."
        ),
        side: "top",
        align: "center",
      },
    },
    {
      element: ".input-notes",
      popover: {
        title: t(
          "Optional text box for any additional information or comments about the asset (e.g., Backup generator used during"
        ),
        side: "top",
        align: "center",
      },
    },
    {
      element: ".button-add-more",
      popover: {
        title: t("Allow you to add more the one asset at the same time."),
        side: "bottom",
        align: "center",
      },
    },
    {
      element: ".button-save",
      popover: {
        title: t("to save the asset and add it to your assets list."),
        side: "top",
        align: "center",
      },
    },
  ];

  const startFormGuide = () => {
    const driverFormObj = driver({
      showProgress: true,
      popoverClass: "my-custom-popover-class",
      nextBtnText: "Next",
      prevBtnText: "Back",
      doneBtnText: "Done",
      overlayColor: "rgba(0, 0, 0, 0)",
      progressText: "Step {{current}} of {{total}}",
      steps: getGuideSteps(),
      onDestroyStarted: () => {
        localStorage.setItem("hasSeenFormGuide", "true");
        driverFormObj.destroy();
      },
    });
    driverFormObj.drive();
  };

  useEffect(() => {
    const hasSeenGuide = localStorage.getItem("hasSeenFormGuide");
    if (!hasSeenGuide) {
      startFormGuide();
    }
    return () => {
      const driverFormObj = driver();
      if (driverFormObj.isActive()) {
        driverFormObj.destroy();
      }
    };
  }, []);

  const msg = (txt, color = "green") => {
    showNotification({ message: txt, color: color });
  };

  const validateFormData = (item) => {
    const { type, name, region, country, sector } = item;
    
    // Added type validation
    if (!type) {
      msg("Type is required", "red");
      return false;
    }
    
    if (!name || name.trim().length < 3) {
      msg("Name must be at least 3 characters long", "red");
      return false;
    }
    
    if (!region || region.trim().length < 3) {
      msg("Region must be at least 3 characters long", "red");
      return false;
    }
    
    if (!country) {
      msg("Country is required", "red");
      return false;
    }
    
    if (!sector || sector.trim().length < 3) {
      msg("Sector must be at least 3 characters long", "red");
      return false;
    }

    // Validate numeric fields if provided
    if (item.numberOfEmployees && isNaN(Number(item.numberOfEmployees))) {
      msg("Number of Employees must be a valid number", "red");
      return false;
    }

    if (item.numberOfAssets && isNaN(Number(item.numberOfAssets))) {
      msg("Number of Assets must be a valid number", "red");
      return false;
    }

    return true;
  };

  const handleSubmit = async (item) => {
    if (!validateFormData(item)) {
      return;
    }

    const submitData = {
      type: item.type, // Added missing type field
      name: item.name.trim(),
      region: item.region.trim(),
      country: item.country,
      sector: item.sector.trim(),
      numberOfEmployees: item.numberOfEmployees ? Number(item.numberOfEmployees) : null,
      locations: item.locations || [],
      numberOfAssets: item.numberOfAssets ? Number(item.numberOfAssets) : null,
    };

    toggleLoading(item.id, true);
    try {
      const { data } = await ApiS2.post(
        "https://portal-auth-main-staging.azurewebsites.net/company-admin/departments-and-projects",
        submitData
      );
      
      msg("Department/Project added successfully");
      deleteSelectedRows(item.id);
      
      // If this was the last item, close modal and refresh
      if (data.length === 1) {
        onSuccess && onSuccess();
      }
    } catch (error) {
      console.error("Submit error:", error);
      console.error("Error response:", error.response?.data);
      msg(error.response?.data?.message || "Failed to add department/project", "red");
    } finally {
      toggleLoading(item.id, false);
    }
  };

  const addNewRow = () => {
    setData([
      ...data,
      {
        id: Date.now().toString(),
        type: "", // Added missing type field
        name: "",
        region: "",
        country: "",
        sector: "",
        numberOfEmployees: "",
        locations: [],
        numberOfAssets: "",
      },
    ]);
  };

  const deleteSelectedRows = (itemId) => {
    setData(data.filter((item) => item.id !== itemId));
  };

  const handleInputChange = (field, value, item) => {
    const updatedData = data.map((dataItem) =>
      dataItem.id === item.id ? { ...dataItem, [field]: value } : dataItem
    );
    setData(updatedData);
  };

  const toggleLoading = (id, value) => {
    setLoading((prevState) => ({
      ...prevState,
      [id]: value,
    }));
  };

  const formCards = data.map((item) => {
    return (
      <div className="border-b-2 py-3 mb-5 border-gray-400" key={item.id}>
        <div className="grid lg:grid-cols-2 gap-4">
          <div>
            <Select
              label="Type"
              comboboxProps={{ withinPortal: true }}
              data={typeOptions} // Added data prop with type options
              placeholder="Select Type"
              searchable
              rightSection={!item.type && <IoIosArrowDown />}
              value={item.type}
              onChange={(value) => handleInputChange("type", value, item)}
              clearable
              required
            />
          </div>
          <div>
            <TextInput
              label="Name"
              value={item.name}
              onChange={(e) => handleInputChange("name", e.target.value, item)}
              placeholder="Enter Department/Project Name"
              required
            />
          </div>

          <div>
            <TextInput
              label="Region"
              value={item.region}
              onChange={(e) => handleInputChange("region", e.target.value, item)}
              placeholder="Enter Region"
              required
            />
          </div>

          <div>
            <Select
              label="Country"
              comboboxProps={{ withinPortal: true }}
              data={sortedCountries.map((country) => country.name.common)}
              placeholder="Select Country"
              searchable
              rightSection={!item.country && <IoIosArrowDown />}
              value={item.country}
              onChange={(value) => handleInputChange("country", value, item)}
              clearable
              required
            />
          </div>

          <div>
            <TextInput
              label="Sector"
              value={item.sector}
              onChange={(e) => handleInputChange("sector", e.target.value, item)}
              placeholder="Enter Sector"
              required
            />
          </div>

          <div>
            <TextInput
              label="No. of Employees"
              value={item.numberOfEmployees}
              onChange={(e) => handleInputChange("numberOfEmployees", e.target.value, item)}
              placeholder="Enter Number of Employees"
              type="number"
            />
          </div>

          <div>
            <MultiSelect
              label="Locations"
              data={locationOptions}
              value={item.locations}
              onChange={(value) => handleInputChange("locations", value, item)}
              placeholder="Select or enter locations"
              searchable
              creatable
              getCreateLabel={(query) => `+ Create "${query}"`}
              onCreate={(query) => {
                const item = { value: query, label: query };
                locationOptions.push(query);
                return item;
              }}
              comboboxProps={{ withinPortal: true }}
              clearable
            />
          </div>

          <div >
            <TextInput
              label="No. of Assets"
              value={item.numberOfAssets}
              onChange={(e) => handleInputChange("numberOfAssets", e.target.value, item)}
              placeholder="Enter Number of Assets"
              type="number"
            />
          </div>
        </div>

        <div className="grid lg:grid-cols-2 gap-3 mt-3">
          <Button
            variant="outline"
            disabled={loading[item.id]}
            className="font-bold text-xs border-gray-200 px-5"
            onClick={() => deleteSelectedRows(item.id)}
            type="button"
          >
            Cancel
          </Button>
          <Button
            disabled={loading[item.id]}
            className="button-save font-bold text-xs bg-primary text-white hover:bg-primary hover:text-white px-5"
            onClick={() => handleSubmit(item)}
            type="submit"
          >
            {loading[item.id] ? <Loading /> : "Save"}
          </Button>
        </div>
      </div>
    );
  });

  return (
    <div>
      <div className="flex justify-end mt-4">
        <Button
          onClick={addNewRow}
          className="button-add-more bg-teal-500 hover:bg-teal-600 text-white font-medium py-2 px-6 rounded-lg transition-colors"
        >
          Add More
        </Button>
      </div>
      <div>{formCards}</div>
    </div>
  );
};

export default DepartmentsandProjectsForm;