export const data = [
 {
  categoryName: "Environment",
  solved: null,
  title: "Environment Matters Due Diligence Assessment",
  topics: [
   {
    id: "1",
    name: "Regulatory Compliance",
    questions: [
     {
      CSRDReference: "",
      TrafficLight: null,
      actionItems: [],
      date: [],
      evidence: [],
      id: "1",
      owner: [],
      questionText:
       "Does the company comply with all relevant local and international environmental laws, standards, and regulations?",
      readinessLevel: null,
      solved: null,
     },
     {
      CSRDReference: "",
      TrafficLight: null,
      actionItems: [],
      date: [],
      evidence: [],
      id: "2",
      owner: [],
      questionText:
       "Are all required environmental permits and certifications up-to-date?",
      readinessLevel: null,
      solved: null,
     },
     {
      CSRDReference: "",
      TrafficLight: null,
      actionItems: [],
      date: [],
      evidence: [],
      id: "3",
      owner: [],
      questionText:
       "Has the company successfully completed all required environmental audits and sign-offs?",
      readinessLevel: null,
      solved: null,
     },
    ],
   },
   {
    id: "2",
    name: "Environmental Management System",
    questions: [
     {
      CSRDReference: "",
      TrafficLight: null,
      actionItems: [],
      date: [],
      evidence: [],
      id: "4",
      owner: [],
      questionText:
       "Does the company have an ISO 14001 certified environmental management system?",
      readinessLevel: null,
      solved: null,
     },
     {
      CSRDReference: "",
      TrafficLight: null,
      actionItems: [],
      date: [],
      evidence: [],
      id: "5",
      owner: [],
      questionText:
       "Are there processes in place to assess and mitigate environmental risks?",
      readinessLevel: null,
      solved: null,
     },
    ],
   },
   {
    id: "3",
    name: "Incident Management",
    questions: [
     {
      CSRDReference: "",
      TrafficLight: null,
      actionItems: [],
      date: [],
      evidence: [],
      id: "6",
      owner: [],
      questionText:
       "Has the company had any serious environmental incidents in the last five years?",
      readinessLevel: null,
      solved: null,
     },
     {
      CSRDReference: "",
      TrafficLight: null,
      actionItems: [],
      date: [],
      evidence: [],
      id: "7",
      owner: [],
      questionText:
       "Is there a system for recording and investigating all environmental incidents?",
      readinessLevel: null,
      solved: null,
     },
    ],
   },
   {
    id: "4",
    name: "Effluents and Emissions",
    questions: [
     {
      CSRDReference: "",
      TrafficLight: null,
      actionItems: [],
      date: [],
      evidence: [],
      id: "8",
      owner: [],
      questionText:
       "Are effluents and emissions (including hazardous ones) properly managed and treated?",
      readinessLevel: null,
      solved: null,
     },
     {
      CSRDReference: "",
      TrafficLight: null,
      actionItems: [],
      date: [],
      evidence: [],
      id: "9",
      owner: [],
      questionText:
       "Are effluent and emission qualities and quantities regularly monitored?",
      readinessLevel: null,
      solved: null,
     },
     {
      CSRDReference: "",
      TrafficLight: null,
      actionItems: [],
      date: [],
      evidence: [],
      id: "10",
      owner: [],
      questionText:
       "Do effluents and emissions comply with local regulations and IFC guidelines (if applicable)?",
      readinessLevel: null,
      solved: null,
     },
    ],
   },
   {
    id: "5",
    name: "Resource Conservation",
    questions: [
     {
      CSRDReference: "",
      TrafficLight: null,
      actionItems: [],
      date: [],
      evidence: [],
      id: "11",
      owner: [],
      questionText:
       "Does the company monitor and have targets to reduce energy and resource use?",
      readinessLevel: null,
      solved: null,
     },
    ],
   },
   {
    id: "6",
    name: "Waste Management",
    questions: [
     {
      CSRDReference: "",
      TrafficLight: null,
      actionItems: [],
      date: [],
      evidence: [],
      id: "12",
      owner: [],
      questionText:
       "Is there a comprehensive waste management system, including recycling and reduction initiatives?",
      readinessLevel: null,
      solved: null,
     },
     {
      CSRDReference: "",
      TrafficLight: null,
      actionItems: [],
      date: [],
      evidence: [],
      id: "13",
      owner: [],
      questionText: "Are hazardous wastes properly managed?",
      readinessLevel: null,
      solved: null,
     },
    ],
   },
   {
    id: "7",
    name: "Water Management",
    questions: [
     {
      CSRDReference: "",
      TrafficLight: null,
      actionItems: [],
      date: [],
      evidence: [],
      id: "14",
      owner: [],
      questionText:
       "Is water use monitored and managed, especially in water-stressed areas?",
      readinessLevel: null,
      solved: null,
     },
    ],
   },
   {
    id: "8",
    name: "Biodiversity and Land Use",
    questions: [
     {
      CSRDReference: "",
      TrafficLight: null,
      actionItems: [],
      date: [],
      evidence: [],
      id: "15",
      owner: [],
      questionText:
       "Has the company assessed and mitigated its impacts on biodiversity and sensitive ecosystems?",
      readinessLevel: null,
      solved: null,
     },
     {
      CSRDReference: "",
      TrafficLight: null,
      actionItems: [],
      date: [],
      evidence: [],
      id: "16",
      owner: [],
      questionText:
       "Are land clearance activities, if any, well-managed and consulted with local communities?",
      readinessLevel: null,
      solved: null,
     },
    ],
   },
   {
    id: "9",
    name: "Climate Change",
    questions: [
     {
      CSRDReference: "",
      TrafficLight: null,
      actionItems: [],
      date: [],
      evidence: [],
      id: "17",
      owner: [],
      questionText:
       "Has the company assessed its carbon footprint and taken measures to reduce greenhouse gas emissions?",
      readinessLevel: null,
      solved: null,
     },
     {
      CSRDReference: "",
      TrafficLight: null,
      actionItems: [],
      date: [],
      evidence: [],
      id: "18",
      owner: [],
      questionText:
       "Is the company exploring opportunities under the Clean Development Mechanism?",
      readinessLevel: null,
      solved: null,
     },
    ],
   },
   {
    id: "10",
    name: "Supply Chain Environmental Management",
    questions: [
     {
      CSRDReference: "",
      TrafficLight: null,
      actionItems: [],
      date: [],
      evidence: [],
      id: "19",
      owner: [],
      questionText:
       "Does the company assess and influence the environmental practices of its key suppliers and subcontractors?",
      readinessLevel: null,
      solved: null,
     },
    ],
   },
  ],
  url: "ESG_Due_Diligence_Environment",
 },
];
