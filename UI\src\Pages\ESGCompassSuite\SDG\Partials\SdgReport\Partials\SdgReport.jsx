import ApiS1Config from "@/Api/apiS1Config";
import Loading from "@/Components/Loading";
import HistoryPopUp from "@/Pages/ESGCompassSuite/AssessmentType/Partials/HistoryPopUp";
import Share from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/Share";
import { btnStyle, linkStyle } from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/StaticData";
import ViewPDF from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/ViewPDF";
import reportImg from "@/assets/images/mountain-logo.png";
import { AreaChart } from "@mantine/charts";
import { Button, RingProgress, Text } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { CiShare2 } from "react-icons/ci";
import { FiDownload } from "react-icons/fi";
import { MdHistory, MdOutlineRemoveRedEye } from "react-icons/md";

export default function SdgReport() {
 const [GetDynamicData, setDynamicData] = useState();
 const [lineData, setLineData] = useState([]);
 const { t } = useTranslation();
 const [historyOpened, { open: historyOpen, close: historyClose }] =
  useDisclosure(false);
 const getDynamicData = async () => {
  try {
   const { data: dynamic } = await ApiS1Config.get("/dynamic_data", {
    headers: {
     assessmentType: "SDG Assessment",
    },
   });
   await ApiS1Config.get("/dashboard", {
    headers: { assessmentType: "SDG Assessment" },
   })
    .then((response) => response.data.all_assessment_summaries)
    .then((assessmentData) => {
     const data = assessmentData.map((item) => {
      let name = item?.name?.split(" ").slice(0, -1).join(" ");
      const Day = name?.split(" ")[0];
      const Month = name?.split(" ")[1].slice(0, 3);
      // //console.log(Day);
      return {
       Month: item?.total_score,
       date: `${Day} ${Month}`,
      };
     });

     setLineData(data);
    });
   setDynamicData(dynamic);
  } catch (error) {
   //console.log(error);
  }
 };
 useEffect(() => {
  getDynamicData();
 }, []);
 return (
  <>
   {!GetDynamicData ? (
    <Loading />
   ) : (
    <div className="flex flex-col w-full left-section min-h-svh">
     <div className="report-page flex flex-col lg:flex-row items-center lg:justify-between gap-y-3 flex-wrap mb-[38px]">
      <div>
       <Button className={linkStyle} size="md" onClick={historyOpen}>
        <MdHistory className="me-1" />
        Assessment History
       </Button>
      </div>
      <div className="flex flex-col lg:flex-row items-center justify-center gap-6">
       
       <ViewPDF
        btnStyle={
         GetDynamicData?.pdf_url
          ? btnStyle
          : "border-[1px] flex items-center px-2  h-[56px] w-[195px]  justify-center text-[17px] font-bold rounded-lg text-white border-gray-300 bg-gray-300 hover:text-white hover:bg-gray-300 cursor-not-allowed"
        }
        pdfUrl={GetDynamicData?.pdf_url}
        text={"View Report"}
        disabled={!GetDynamicData?.pdf_url && true}
       />
      
       <Share
        link={GetDynamicData?.pdf_url}
        btnStyle={
         GetDynamicData?.pdf_url
          ? btnStyle
          : "border-[1px] flex items-center px-2  h-[56px] w-[195px]  justify-center text-[17px] font-bold rounded-lg text-white border-gray-300 bg-gray-300 hover:text-white hover:bg-gray-300 cursor-not-allowed"
        }
        disabled={!GetDynamicData?.pdf_url && true}
       />
       <Button
        component="a"
        className={
         GetDynamicData?.pdf_url
          ? linkStyle
          : "border-[1px] flex items-center px-2  h-[56px] w-[195px]  justify-center text-[17px] font-bold rounded-lg text-white border-gray-300 bg-gray-300 hover:text-white hover:bg-gray-300 cursor-not-allowed"
        }
        href={GetDynamicData?.pdf_url}
        download
        disabled={!GetDynamicData?.pdf_url ? true : false}
       >
        Download Report
        <span>
         <FiDownload className="text-lg ms-1" />
        </span>
       </Button>
      </div>
     </div>

     <div className="lg:flex  mt-5 mb-7 gap-9 ">
      {/* donut chart */}
      <div className="md:flex bg-white p-5 flex-grow lg:w-1/2 justify-around  items-center rounded-lg shadow-[0px_0px_10px_0px_#0000001A]">
       <div>
        <h5 className="font-bold">{t("overall impact Score")}</h5>
        <span className="flex items-center justify-center w-full">
         <RingProgress
          size={180}
          thickness={20}
          roundCaps
          className="w-full aspect-square text-center flex justify-center items-center scale-x-[-1] rotate-90"
          sections={[
           {
            value:
             // (GetDynamicData?.overall_score?.average_score || 0) * 20
             (GetDynamicData?.score?.impact_score || 0) * 20,
            color: "#29919B",
           },
          ]}
          rootColor="#D4E9EB"
          label={
           <Text
            c="black"
            fw={700}
            ta="center"
            size="xl"
            className="rotate-90 scale-x-[-1] text-3xl flex justify-center items-center"
           >
            {GetDynamicData?.score?.impact_score || 0}
           </Text>
          }
         />
        </span>
       </div>
       <div>
        <h5 className="font-bold">{t("overall Alignment Score")}</h5>
        <span className="flex items-center justify-center w-full">
         <RingProgress
          size={180}
          thickness={20}
          roundCaps
          className="w-full aspect-square text-center flex justify-center items-center scale-x-[-1] rotate-90"
          sections={[
           {
            value:
             // (GetDynamicData?.overall_score?.average_score || 0) * 20
             (GetDynamicData?.score?.alignment_score || 0) * 20,
            color: "#29919B",
           },
          ]}
          rootColor="#D4E9EB"
          label={
           <Text
            c="black"
            fw={700}
            ta="center"
            size="xl"
            className="rotate-90 scale-x-[-1] text-3xl flex justify-center items-center"
           >
            {GetDynamicData?.score?.alignment_score || 0}
           </Text>
          }
         />
        </span>
       </div>
      </div>

      {/* line chart */}
      {lineData?.length > 0 ? (
       <div className="bg-white flex-grow lg:w-1/2 text-center p-5 rounded-lg shadow-[0px_0px_10px_0px_#0000001A] mt-5 lg:mt-0">
        <h5 className="mb-6 font-bold">{t("progressTracker")}</h5>
        <AreaChart
         h={200}
         data={lineData}
         dataKey="date"
         series={[{ name: "Month", color: "blue.6" }]}
         curveType="natural"
         withDots={false}
        />
       </div>
      ) : (
       <Loading />
      )}
     </div>
     <div className="flex items-center justify-center w-full mx-auto mt-auto img-wrapper max-w-7xl">
      <img src={reportImg} className="object-center w-full" alt="Report" />
     </div>
    </div>
   )}
   {historyOpened && (
    <HistoryPopUp
     opened={historyOpened}
     close={historyClose}
     assessmentType={"SDG Assessment"}
    />
   )}
  </>
 );
}
