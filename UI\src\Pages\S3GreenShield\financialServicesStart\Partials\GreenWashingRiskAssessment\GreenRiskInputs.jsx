import {
  <PERSON>ton,
  CloseButton,
  FileInput,
  MultiSelect,
  Select,
  TextInput,
  Textarea,
  rem,
} from "@mantine/core";
import { DateInput } from "@mantine/dates";
import { isNotEmpty, useField, useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import React, { useState, useEffect } from "react";
import { IoArrowDownOutline } from "react-icons/io5";
import Calendar from "@/assets/images/fluent--calendar-16-regular.png";
import Upload from "@/assets/images/solar--upload-outline.png";
import Axios from "axios";
import Cookies from "js-cookie";
import Position from "rsuite/esm/internals/Overlay/Position";
import { FaExclamationCircle } from "react-icons/fa";
import { AddIcon } from "@/assets/icons/RiskManagment";
import authConfig from "@/Api/authConfig";

const GreenRiskInputs = ({ onRiskAdded }) => {
  // Define styles as JavaScript objects
  const styles = {
    errorBorder: { border: "2px solid red" },
    inputNormal: { border: "1px solid #ced4da" },
    errorIcon: {
      color: "red",
      position: "absolute",
      top: "35%",
      right: "5%",
      fontSize: "20px",
    },
  };

  const isNotEmpty = (value) =>
    typeof value === "string" && value.trim() !== ""
      ? null
      : "This field is required";

  const form = useForm({
    initialValues: {
      greenwashingTypeId: "",
      riskName: "test risk name",
      riskDescription: "",
      greenwashingRiskArea: "",
      riskCategoryId: "",
      riskOwner: "",
      dateIdentified: "",
      inherentRisk: "",
      likelihoodId: "",
      impactId: "",
      residualRisk: "",
      inherentRiskScore: 0,
      currentControlEffectiveness: "",
      residualRiskScore: "",
      riskToleranceCategorizationId: "",
      mitigationStrategyTypeId: "",
      // relatedRisks: [],
      attachment: "",
      alignsWithRiskAppetite: "Yes",
      notes: "",
      status: "Pending",
      approverId: "",
      lastUpdate: "",
      lastUpdatedById: "",
    },
    validate: {
      // carbonImpactEstimation: isNotEmpty,
      // potentialFinancialLoss: isNotEmpty,
      // minimumFinancialLoss: isNotEmpty,
      // maximumFinancialLoss: isNotEmpty,
      // riskId: isNotEmpty,
      greenwashingTypeId: isNotEmpty,
      riskCategoryId: isNotEmpty,
      // riskName: isNotEmpty,
      riskDescription: isNotEmpty,
      riskOwner: isNotEmpty,
      likelihoodId: isNotEmpty,
      impactId: isNotEmpty,
      // inherentRisk: isNotEmpty,
      // residualRisk: isNotEmpty,
      notes: isNotEmpty,
      approverId: isNotEmpty,
      lastUpdatedById: isNotEmpty,
      riskToleranceCategorizationId:isNotEmpty,
    },
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const [esgCategoriesOptions, setEsgCategoriesOptions] = useState([]);
  const [keyRiskIndicatorsOptions, setKeyRiskIndicatorsOptions] = useState([]);

  const [
    financialImpactTimeFramesOptions,
    setFinancialImpactTimeFramesOptions,
  ] = useState([]);
  const [stakeholdersOptions, setStakeholdersOptions] = useState([]);
  const [impactsOptions, setImpactsOptions] = useState([]);
  const [likelihoodsOptions, setLikelihoodsOptions] = useState([]);
  const [riskCategoriesOptions, setRiskCategoriesOptions] = useState([]);
  const [
    riskToleranceCategorizationsOptions,
    setRiskToleranceCategorizationsOptions,
  ] = useState([]);
  const [mitigationStrategyTypesOptions, setMitigationStrategyTypesOptions] =
    useState([]);
  const [relatedRiskOptions, setRelatedRiskOptions] = useState([]);
  const [greenwashingTypesOptions, setgreenwashingTypesOptions] = useState([]);
  const [greenwashingTypesAreaOptions, setgreenwashingTypesAreaOptions] =
    useState([]);

  const [residualRiskScore, setResidualRiskScore] = useState(0);
  const [currentControlEffectiveness, setCurrentControlEffectiveness] =
    useState("");
    const [company, setCompany] = useState([]);

  useEffect(() => {
    const Company = async () => {
      try {
        const response = await authConfig.get("/get_all_user_by_token");

        const users = response.data.companyUsers;

        if (Array.isArray(users)) {
         const userAuthId = users
            .filter((user) => !!user.userAuthId)
            .map((user) => ({
              value: user.userAuthId,
              label: user.userName,
            }));
          setCompany(userAuthId);
        } else {
          console.error("Error:", response.data);
        }
      } catch (error) {
        console.error("Error company:", error);
      }
    };

    Company();
  }, []);


  useEffect(() => {
    const fetchMasterData = async () => {
      setLoading(true);
      try {
        const token = Cookies.get("level_user_token");

        const response = await Axios.get(
          "https://portals3-h3bjdkhtbghye2aa.uksouth-01.azurewebsites.net/master-data/anti-green-washing-risk",
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
            withCredentials: true,
          }
        );

        const esgCategories = response.data.esgCategories.map((category) => ({
          value: category._id,
          label: category.name,
        }));

        const keyRiskIndicators = response.data.keyRiskIndicators.map(
          (keyRiskIndicator) => ({
            value: keyRiskIndicator._id,
            label: keyRiskIndicator.name,
          })
        );

        const financialImpactTimeFrames =
          response.data.financialImpactTimeFrames.map(
            (financialImpactTimeFrame) => ({
              value: financialImpactTimeFrame._id,
              label: financialImpactTimeFrame.name,
            })
          );

        const stakeholders = response.data.stakeholders.map((stakeholder) => ({
          value: stakeholder._id,
          label: stakeholder.name,
        }));

        const likelihoods = response.data.likelihoods.map((likelihood) => ({
          value: likelihood._id,
          label: likelihood.name,
          score: likelihood.value, // Keep the score to use to calculate inherent risk score
        }));

        const impacts = response.data.impacts.map((impact) => ({
          value: impact._id,
          label: impact.name,
          score: impact.value, // Keep the score to use to calculate inherent risk score
        }));

        const riskCategories = response.data.riskCategories.map(
          (riskCategory) => ({
            value: riskCategory._id,
            label: riskCategory.name,
          })
        );

        const riskToleranceCategorizations =
          response.data.riskToleranceCategorizations.map(
            (riskToleranceCategorization) => ({
              value: riskToleranceCategorization._id,
              label: riskToleranceCategorization.name,
            })
          );

        const mitigationStrategyTypes =
          response.data.mitigationStrategyTypes.map(
            (mitigationStrategyTypeId) => ({
              value: mitigationStrategyTypeId._id,
              label: mitigationStrategyTypeId.name,
            })
          );

        const risks = (response.data?.risks ?? []).map((risks) => ({
          value: risks?._id ?? "",
          label: risks?.riskName ?? "",
        }));

        const greenwashingTypes = response.data.greenwashingTypes.map(
          (type) => ({
            value: type._id,
            label: type.name,
            area: type.greenwashingRiskArea,
            riskDescription: type.riskDescription,
          })
        );

        // All possible Greenwashing Risk Areas
        const greenwashingRiskAreas = Array.from(
          new Set(greenwashingTypes.map((type) => type.area))
        ).map((area) => ({ value: area, label: area }));

        setRelatedRiskOptions(risks);
        setEsgCategoriesOptions(esgCategories);
        setKeyRiskIndicatorsOptions(keyRiskIndicators);
        setFinancialImpactTimeFramesOptions(financialImpactTimeFrames);
        setStakeholdersOptions(stakeholders);
        setLikelihoodsOptions(likelihoods);
        setImpactsOptions(impacts);
        setRiskCategoriesOptions(riskCategories);
        setRiskToleranceCategorizationsOptions(riskToleranceCategorizations);
        setMitigationStrategyTypesOptions(mitigationStrategyTypes);
        setgreenwashingTypesOptions(greenwashingTypes);
        setgreenwashingTypesAreaOptions(greenwashingRiskAreas);

        if (risks.length > 0) {
          form.setFieldValue("risks", [risks[0].value]);
        }

        if (greenwashingTypes.length > 0) {
          form.setFieldValue("greenwashingTypes", [greenwashingTypes[0].value]);
        }

        if (esgCategories.length > 0) {
          form.setFieldValue("esgCategoryId", esgCategories[0].value);
        }

        if (keyRiskIndicators.length > 0) {
          form.setFieldValue("keyRiskIndicators", [keyRiskIndicators[0].value]);
        }

        if (financialImpactTimeFrames.length > 0) {
          form.setFieldValue(
            "financialImpactTimeFrameId",
            financialImpactTimeFrames[0].value
          );
        }

        if (stakeholders.length > 0) {
          form.setFieldValue("affectedStakeholders", [stakeholders[0].value]);
        }

        if (impacts.length > 0) {
          form.setFieldValue("reputationalImpactId", impacts[0].value);
        }

        setLoading(false);
      } catch (err) {
        setError(err.message);
        setLoading(false);
      }
    };

    fetchMasterData();
  }, []);

  const icon = (
    <IoArrowDownOutline
      style={{ width: rem(12), height: rem(12), color: "#00C0A9" }}
    />
  );

  const validateAndConvert = (value) => {
    const num = Number(value);
    return isNaN(num) ? 0 : num;
  };

  const handleSave = async () => {
    const isValid = form.validate(); // Validate all fields

    if (!isValid.hasErrors) {
      const data = form.values; // Get all the form data
      setLoading(true); // Start the loading state

      try {
        const token = Cookies.get("level_user_token");
const dataToSend = {
        esgCategoryId: data.esgCategoryId,
        keyRiskIndicators: data.keyRiskIndicators,
        financialImpactTimeFrameId: data.financialImpactTimeFrameId,
        affectedStakeholders: data.affectedStakeholders,
        greenwashingTypeId: data.greenwashingTypeId,
        likelihoodId: data.likelihoodId,
        impactId: data.impactId,
        riskToleranceCategorizationId: data.riskToleranceCategorizationId,
        riskName: data.riskName,
        riskDescription: data.riskDescription,
        

      };

    
      if (data.riskCategoryId) dataToSend.riskCategoryId = data.riskCategoryId;
      if (data.riskOwner) dataToSend.riskOwner = data.riskOwner;
      if (data.notes) dataToSend.notes = data.notes;
      if (data.approverId) dataToSend.approverId = data.approverId;
      if (data.lastUpdatedById) dataToSend.lastUpdatedById = data.lastUpdatedById;

    
      if (data.potentialFinancialLoss) dataToSend.potentialFinancialLoss = data.potentialFinancialLoss;
      if (data.minimumFinancialLoss) dataToSend.minimumFinancialLoss = validateAndConvert(data.minimumFinancialLoss);
      if (data.maximumFinancialLoss) dataToSend.maximumFinancialLoss = validateAndConvert(data.maximumFinancialLoss);
      if (data.reputationalImpactId) dataToSend.reputationalImpactId = data.reputationalImpactId;
      if (data.greenwashingRiskArea) dataToSend.greenwashingRiskArea = data.greenwashingRiskArea;
      if (data.inherentRisk) dataToSend.inherentRisk = data.inherentRisk;
      if (data.residualRisk) dataToSend.residualRisk = data.residualRisk;
      if (data.inherentRiskScore) dataToSend.inherentRiskScore = validateAndConvert(data.inherentRiskScore);
      if (currentControlEffectiveness) dataToSend.currentControlEffectiveness = validateAndConvert(currentControlEffectiveness);
      if (residualRiskScore) dataToSend.residualRiskScore = validateAndConvert(residualRiskScore);
      if (data.mitigationStrategyTypeId) dataToSend.mitigationStrategyTypeId = data.mitigationStrategyTypeId;
      if (data.attachment) dataToSend.attachment = data.attachment;
      if (data.status) dataToSend.status = data.status;
        // Make the POST request to save the risk data
        const response = await Axios.post(
          "https://portals3-h3bjdkhtbghye2aa.uksouth-01.azurewebsites.net/anti-green-washing-risk",
           dataToSend,
          // {
          //   esgCategoryId: data.esgCategoryId,
          //   keyRiskIndicators: data.keyRiskIndicators,
          //   // carbonImpactEstimation: validateAndConvert(data.carbonImpactEstimation),
          //   // dueDate: data.dueDate
          //   // ? new Date(data.dueDate).toISOString()
          //   //   : "",  // Convert date to ISO format
          //   potentialFinancialLoss: data.potentialFinancialLoss,
          //   minimumFinancialLoss: validateAndConvert(data.minimumFinancialLoss),
          //   maximumFinancialLoss: validateAndConvert(data.maximumFinancialLoss),
          //   financialImpactTimeFrameId: data.financialImpactTimeFrameId,
          //   affectedStakeholders: data.affectedStakeholders,
          //   reputationalImpactId: data.reputationalImpactId,
          //   greenwashingTypeId: data.greenwashingTypeId,
          //   greenwashingRiskArea: data.greenwashingRiskArea,
          //   // riskId: data.riskId,
          //   riskName: data.riskName,
          //   riskDescription: data.riskDescription,
          //   riskCategoryId: data.riskCategoryId,
          //   // riskOwnerId: data.riskOwner,
          //   // dateIdentified: data.dateIdentified
          //   //   ? new Date(data.dateIdentified).toISOString()
          //   //   : "",  // Convert date to ISO format
          //   inherentRisk: data.inherentRisk,
          //   likelihoodId: data.likelihoodId,
          //   impactId: data.impactId,
          //   residualRisk: data.residualRisk,
          //   inherentRiskScore: validateAndConvert(data.inherentRiskScore),
          //   currentControlEffectiveness: validateAndConvert(
          //     currentControlEffectiveness
          //   ), // Use state value
          //   residualRiskScore: validateAndConvert(residualRiskScore), // Use state value
          //   riskToleranceCategorizationId: data.riskToleranceCategorizationId,
          //   mitigationStrategyTypeId: data.mitigationStrategyTypeId,
          //   // relatedRisks: data.relatedRisks,
          //   attachment: data.attachment,
          //   // alignsWithRiskAppetite: data.alignsWithRiskAppetite,
          //   // notes: data.notes,
          //   status: data.status,
          //   approverId: data.approverId,
          //   // lastUpdate: data.lastUpdate
          //   //   ? new Date(data.lastUpdate).toISOString()
          //   //   : "",
          //   lastUpdatedById: data.lastUpdatedById,
          // },
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response && (response.status === 200 || response.status === 201)) {
          notifications.show({
            title: "Success",
            message: "Risk data saved successfully!",
            color: "green",
          });

          // If onRiskAdded exists, trigger it to refresh the table
          if (onRiskAdded) {
            onRiskAdded(); // Trigger the table refresh
          }
        } else {
          notifications.show({
            title: "Error",
            message: `Unexpected response: ${response?.statusText}`,
            color: "red",
          });
        }
      } catch (error) {
        notifications.show({
          title: "Error",
          message: `Failed to save risk data: ${error.message}`,
          color: "red",
        });
      } finally {
        setLoading(false); // End the loading state
      }
    } else {
      notifications.show({
        title: "Validation Error",
        message: "Please fill all the required fields.",
        color: "red",
      });
    }
  };

  // Handle the selection change
  const handleGreenwashingTypeChange = (value) => {
    if (!value) {
      notifications.show({
        title: "Selection Required",
        message: "At least one Type of Green Washing must be selected!",
        color: "red",
      });
      return;
    }

    const selectedType = greenwashingTypesOptions.find(
      (type) => type.value === value
    );

    form.setFieldValue("greenwashingTypeId", value);

    if (selectedType) {
      form.setFieldValue("greenwashingRiskArea", selectedType.area);
      form.setFieldValue("riskDescription", selectedType.riskDescription);
    } else {
      form.setFieldValue("riskDescription", "");
    }
  };

  // Function to handle dropdown change to ensure that at least one option selected
  const handleSelectChange = (field, value) => {
    const currentValue = form.values[field];

    // If the current value is already selected, prevent it from being removed
    if (value || currentValue) {
      form.setFieldValue(field, value || currentValue); // Ensure there's always a selection
    } else {
      notifications.show({
        title: "Selection Required",
        message: `At least one ${field.replace(
          /([A-Z])/g,
          " $1"
        )} must be selected!`,
        color: "red",
      });
    }
  };

  // This useEffect will run whenever likelihoodId or impactId changes
  useEffect(() => {
    const likelihood = likelihoodsOptions.find(
      (option) => option.value === form.values.likelihoodId
    );
    const impact = impactsOptions.find(
      (option) => option.value === form.values.impactId
    );

    if (likelihood && impact) {
      const score = likelihood.score * impact.score; // Calculate the score
      form.setFieldValue("inherentRiskScore", score); // Update form value
    } else {
      form.setFieldValue("inherentRiskScore", 0); // Reset score if no selection
    }
  }, [
    form.values.likelihoodId,
    form.values.impactId,
    likelihoodsOptions,
    impactsOptions,
  ]);

  // Adjusted calculateResidualRiskScore to be called when necessary
  useEffect(() => {
    calculateResidualRiskScore();
  }, [form.values.inherentRiskScore, currentControlEffectiveness]);

  const calculateResidualRiskScore = () => {
    const inherentRiskScore = form.values.inherentRiskScore || 0;
    const effectivenessPercentage = currentControlEffectiveness
      ? (parseInt(currentControlEffectiveness) - 1) * 0.25
      : 0;

    const residualRisk = inherentRiskScore * (1 - effectivenessPercentage);
    setResidualRiskScore(residualRisk);
    console.log("Calculated Residual Risk Score: ", residualRisk);
  };

  return (
    <div>
      <div className="flex items-center gap-2 text-lg font-bold">
        <AddIcon />
        <span>Add Risk</span>
      </div>
      {/* Risk Name Risk Description */}
      <h1 className="font-bold bg-[#F5F4F5] my-5 p-1">Risk Identification</h1>
      <div className="grid lg:grid-cols-2 md:grid-cols-2 gap-4">
        <div className="relative">
          <TextInput
            disabled={true}
            {...form.getInputProps("riskId")}
            key={form.key("riskId")}
            type="text"
            id="riskId"
            label="Risk ID"
            placeholder="Auto ID"
            mb="md"
            radius="md"
            className="font-semibold"
            styles={() => ({
              Textarea: {
                ...(form.errors.riskId
                  ? styles.errorBorder
                  : styles.inputNormal),
                "&:focus": { border: "2px solid blue" },
              },
            })}
            onFocus={() => {
              form.setFieldError("riskId", "");
            }}
            // styles={{ input: form.errors.riskId ? styles.errorBorder : styles.inputNormal, }}
          />
          {form.errors.riskId && (
            <FaExclamationCircle style={styles.errorIcon} />
          )}
        </div>

        <Select
          {...form.getInputProps("greenwashingTypeId")}
          key={form.key("greenwashingTypeId")}
          mb="md"
          id="greenwashingTypeId"
          name="greenwashingTypeId"
          value={form.values.greenwashingTypeId}
          onChange={handleGreenwashingTypeChange} // Ensures selection is not cleared
          comboboxProps={{ withinPortal: true }}
          data={greenwashingTypesOptions}
          placeholder="Hidden Trade-offs"
          label="Type of Green Washing"
          radius="md"
          rightSectionPointerEvents="none"
          rightSection={icon}
          className="font-semibold"
        />

        <div className="relative">
          <TextInput
            {...form.getInputProps("riskDescription")}
            key={form.key("riskDescription")}
            type="text"
            id="riskDescription"
            label="Risk Description"
            placeholder="Enter Text"
            mb="md"
            radius="md"
            className="font-semibold"
            styles={() => ({
              Textarea: {
                ...(form.errors.riskDescription
                  ? styles.errorBorder
                  : styles.inputNormal),
                "&:focus": { border: "2px solid blue" },
              },
            })}
            onFocus={() => {
              form.setFieldError("riskDescription", "");
            }}
            // styles={{ input: form.errors.riskDescription ? styles.errorBorder : styles.inputNormal, }}
          />
          {form.errors.riskDescription && (
            <FaExclamationCircle style={styles.errorIcon} />
          )}
        </div>

        <Select
          {...form.getInputProps("greenwashingRiskArea")}
          key={form.key("greenwashingRiskArea")}
          mb="md"
          id="greenwashingRiskArea"
          name="greenwashingRiskArea"
          data={greenwashingTypesAreaOptions} // All possible risk areas available for selection
          value={form.values.greenwashingRiskArea}
          onChange={(value) =>
            handleSelectChange("greenwashingRiskArea", value)
          } // Ensures at least one area selected
          comboboxProps={{ withinPortal: true }}
          placeholder="Product Claims"
          label="Greenwashing Risk Area"
          radius="md"
          rightSectionPointerEvents="none"
          rightSection={icon}
          className="font-semibold"
        />

        {/* <div className="relative">
          <TextInput
            {...form.getInputProps("riskName")}
            key={form.key("riskName")}
            type="text"
            id="riskName"
            label="Risk Name"
            placeholder="Enter Text"
            mb="md"
            radius="md"
            className="font-semibold"
            styles={() => ({
              Textarea: {
                ...(form.errors.riskName
                  ? styles.errorBorder
                  : styles.inputNormal),
                "&:focus": { border: "2px solid blue" },
              },
            })}
            onFocus={() => {
              form.setFieldError("riskName", "");
            }}
            // styles={{ input: form.errors.riskName ? styles.errorBorder : styles.inputNormal, }}
          />
          {form.errors.riskName && (
            <FaExclamationCircle style={styles.errorIcon} />
          )}
        </div> */}

        <Select
          {...form.getInputProps("riskCategoryId")}
          key={form.key("riskCategoryId")}
          mb="md"
          id="riskCategoryId"
          name="riskCategoryId"
          onChange={(value) => handleSelectChange("riskCategoryId", value)}
          comboboxProps={{ withinPortal: true }}
          data={riskCategoriesOptions}
          placeholder="Physical Risk - Acute"
          label="Risk Category"
          radius="md"
          rightSectionPointerEvents="none"
          rightSection={icon}
          className="font-semibold"
        />

        <DateInput
          {...form.getInputProps("dateIdentified")}
          key={form.key("dateIdentified")}
          placeholder="DD/MM/YYYY"
          rightSection={
            <img src={Calendar} alt="calendar" className="w-6 h-6" />
          }
          id="dateIdentified"
          name="dateIdentified"
          label="Date Identified"
          mb="md"
          radius="md"
          className="font-semibold"
        />
      </div>

      <hr className="border border-[#C9C9C9] mb-4" />

      <h1 className="font-bold bg-[#F5F4F5] my-5 p-1">Risk Assessment</h1>
      <div className="grid lg:grid-cols-2 md:grid-cols-2 gap-4">
        {/* <div className="relative">
          <TextInput
            {...form.getInputProps("inherentRisk")}
            key={form.key("inherentRisk")}
            type="text"
            id="inherentRisk"
            label="Inherent Risk"
            placeholder="Enter Text..."
            mb="md"
            radius="md"
            className="font-semibold"
            styles={() => ({
              Textarea: {
                ...(form.errors.inherentRisk ? styles.errorBorder : styles.inputNormal),
                '&:focus': { border: '2px solid blue', },
              },
            })}
            onFocus={() => { form.setFieldError("inherentRisk", ""); }}
            // styles={{ input: form.errors.inherentRisk ? styles.errorBorder : styles.inputNormal,  }}                
          />
          {form.errors.inherentRisk && ( <FaExclamationCircle style={styles.errorIcon}/> )}
        </div> */}

        <Select
          {...form.getInputProps("likelihoodId")}
          key={form.key("likelihoodId")}
          // type="number"
          id="likelihoodId"
          data={likelihoodsOptions}
          value={form.values.likelihoodId}
          onChange={(value) => handleSelectChange("likelihoodId", value)}
          label="Likelihood"
          placeholder="4 Likely"
          mb="md"
          radius="md"
          rightSectionPointerEvents="none"
          rightSection={icon}
          className="font-semibold"
        />

        <Select
          {...form.getInputProps("impactId")}
          key={form.key("impactId")}
          // type="number"
          id="impactId"
          data={impactsOptions}
          value={form.values.impactId}
          onChange={(value) => handleSelectChange("impactId", value)}
          label="Impact"
          placeholder="4 Major"
          mb="md"
          radius="md"
          rightSectionPointerEvents="none"
          rightSection={icon}
          className="font-semibold"
        />

        {/* <div className="relative">
          <TextInput
            {...form.getInputProps("residualRisk")}
            key={form.key("residualRisk")}
            type="text"
            id="residualRisk"
            label="Residual Risk"
            placeholder="Enter Text"
            mb="md"
            radius="md"
            className="font-semibold"
            styles={() => ({
              Textarea: {
                ...(form.errors.residualRisk
                  ? styles.errorBorder
                  : styles.inputNormal),
                "&:focus": { border: "2px solid blue" },
              },
            })}
            onFocus={() => {
              form.setFieldError("residualRisk", "");
            }}
            // styles={{ input: form.errors.residualRisk ? styles.errorBorder : styles.inputNormal, }}
          />
          {form.errors.residualRisk && (
            <FaExclamationCircle style={styles.errorIcon} />
          )}
        </div> */}

        <div className="relative">
          <TextInput
            {...form.getInputProps("inherentRiskScore")}
            key={form.key("inherentRiskScore")}
            type="number"
            id="inherentRiskScore"
            label="Inherent Risk Score"
            value={form.values.inherentRiskScore}
            disabled
            placeholder="xxxxx"
            mb="md"
            radius="md"
            className="font-semibold"
            styles={() => ({
              Textarea: {
                ...(form.errors.inherentRiskScore
                  ? styles.errorBorder
                  : styles.inputNormal),
                "&:focus": { border: "2px solid blue" },
              },
            })}
            onFocus={() => {
              form.setFieldError("inherentRiskScore", "");
            }}
            // styles={{ input: form.errors.inherentRiskScore ? styles.errorBorder : styles.inputNormal,  }}
          />
          {form.errors.inherentRiskScore && (
            <FaExclamationCircle style={styles.errorIcon} />
          )}
        </div>

        <Select
          {...form.getInputProps("currentControlEffectiveness")}
          key={form.key("currentControlEffectiveness")}
          id="currentControlEffectiveness"
          label="Current Control Effectiveness"
          placeholder="Select"
          value={currentControlEffectiveness}
          onChange={(value) => {
            if (value) {
              // If a valid value is selected, update the form and state
              handleSelectChange("currentControlEffectiveness", value);
              setCurrentControlEffectiveness(value);
              calculateResidualRiskScore();
            } else {
              // Prevent clearing the selection and notify the user
              notifications.show({
                title: "Selection Required",
                message:
                  "You must select at least one option for Current Control Effectiveness.",
                color: "red",
              });
            }
          }}
          data={["1", "2", "3", "4", "5"]}
          mb="md"
          radius="md"
          clearable={true} // Allow user to see the clearable option, but prevent actual clearing
          rightSectionPointerEvents="none"
          rightSection={icon}
          className="font-semibold"
        />

        <div className="relative">
          <TextInput
            {...form.getInputProps("residualRiskScore")}
            key={form.key("residualRiskScore")}
            type="number"
            id="residualRiskScore"
            label="Residual Risk Score"
            value={residualRiskScore}
            disabled
            placeholder="xxxx"
            mb="md"
            radius="md"
            className="font-semibold"
            styles={() => ({
              Textarea: {
                ...(form.errors.residualRiskScore
                  ? styles.errorBorder
                  : styles.inputNormal),
                "&:focus": { border: "2px solid blue" },
              },
            })}
            onFocus={() => {
              form.setFieldError("residualRiskScore", "");
            }}
            // styles={{ input: form.errors.residualRiskScore ? styles.errorBorder : styles.inputNormal,  }}
          />
          {form.errors.residualRiskScore && (
            <FaExclamationCircle style={styles.errorIcon} />
          )}
        </div>

        <Select
          {...form.getInputProps("riskToleranceCategorizationId")}
          key={form.key("riskToleranceCategorizationId")}
          // type="number"
          id="riskToleranceCategorizationId"
          onChange={(value) =>
            handleSelectChange("riskToleranceCategorizationId", value)
          }
          label="Risk Tolerance Categorisation"
          placeholder="Minimal"
          data={riskToleranceCategorizationsOptions}
          mb="md"
          radius="md"
          rightSectionPointerEvents="none"
          rightSection={icon}
          className="font-semibold"
        />

        <Select
          {...form.getInputProps("mitigationStrategyTypeId")}
          key={form.key("mitigationStrategyTypeId")}
          // type="number"
          id="mitigationStrategyTypeId"
          onChange={(value) =>
            handleSelectChange("mitigationStrategyTypeId", value)
          }
          label="Mitigation Strategy Type"
          placeholder="Tolerate"
          data={mitigationStrategyTypesOptions}
          mb="md"
          radius="md"
          rightSectionPointerEvents="none"
          rightSection={icon}
          className="font-semibold"
        />

        <div className="relative">
          <TextInput
            {...form.getInputProps("riskOwner")}
            key={form.key("riskOwner")}
            type="text"
            id="riskOwner"
            label="Risk Owner"
            placeholder="Jane Smith, Head of Operations"
            mb="md"
            radius="md"
            className="font-semibold"
            styles={() => ({
              Textarea: {
                ...(form.errors.riskOwner
                  ? styles.errorBorder
                  : styles.inputNormal),
                "&:focus": { border: "2px solid blue" },
              },
            })}
            onFocus={() => {
              form.setFieldError("riskOwner", "");
            }}
            // styles={{ input: form.errors.riskOwner ? styles.errorBorder : styles.inputNormal, }}
          />
          {form.errors.riskOwner && (
            <FaExclamationCircle style={styles.errorIcon} />
          )}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        {/* <MultiSelect
          {...form.getInputProps("relatedRisks")}
          label="Related Risks"
          placeholder="Carbon pricing impact"
          data={relatedRiskOptions}
          defaultValue={["CR-2023-002: Carbon pricing impact"]}
          radius="md"
          rightSectionPointerEvents="none"
          rightSection={icon}
          className="font-semibold"
        /> */}

        <div className="">
          <h1>Attachments</h1>
          <div className="flex gap-3 border-[1px] border-secondary-gray-300 rounded-lg">
            <FileInput
              variant="unstyled"
              leftSection={
                <img src={Upload} alt="calendar" className="w-4 h-4" />
              }
              // label="Attachments"
              placeholder="File"
              leftSectionPointerEvents="none"
              className="font-semibold underline text-[#BFBFBF]"
            />

            <div className="flex items-center gap-2">
              <div className="h-[50%] w-[2px] bg-secondary-gray-300 " />
              <TextInput
                h={20}
                variant="unstyled"
                {...form.getInputProps("url")}
                key={form.key("url")}
                type="text"
                id="url"
                className="font-semibold text-[#298BED]"
                placeholder="URL"
                mb="md"
                radius="md"
              />
            </div>
          </div>
        </div>

        <Select
          {...form.getInputProps("alignsWithRiskAppetite")}
          key={form.key("alignsWithRiskAppetite")}
          // type="number"
          id="alignsWithRiskAppetite"
          onChange={(value) =>
            handleSelectChange("alignsWithRiskAppetite", value)
          }
          className="font-semibold"
          label="Aligns with Risk Appetite? "
          placeholder="Yes/No"
          data={["Yes", "No"]}
          mb="md"
          radius="md"
          rightSectionPointerEvents="none"
          rightSection={icon}
        />
      </div>

      <div className="grid lg:grid-cols-1 gap-4">
        <div className="relative">
          <Textarea
            {...form.getInputProps("notes")}
            key={form.key("notes")}
            size="sm"
            label="Comments/Notes"
            autosize
            minRows={2}
            radius="md"
            className="font-semibold"
            styles={() => ({
              Textarea: {
                ...(form.errors.notes
                  ? styles.errorBorder
                  : styles.inputNormal),
                "&:focus": { border: "2px solid blue" },
              },
            })}
            onFocus={() => {
              form.setFieldError("notes", "");
            }}
            // styles={{ input: form.errors.notes ? styles.errorBorder : styles.inputNormal,  }}
            placeholder="Recent IPCC report indicates increased likelihood of extreme weather events in our key operational areas. Consider engaging external climate risk consultants for more detailed scenario analysis."
          />
          {form.errors.notes && (
            <FaExclamationCircle
              style={{
                color: "red",
                position: "absolute",
                top: "55%",
                right: "2%",
                fontSize: "20px",
              }}
            />
          )}
        </div>
      </div>


      <h1 className="font-bold bg-[#F5F4F5] my-5 p-1">Approval and Workflow</h1>
      <div className="grid lg:grid-cols-2 md:grid-cols-2 gap-4">
        <Select
          {...form.getInputProps("status")}
          key={form.key("status")}
          onChange={(value) => handleSelectChange("status", value)}
          label="Status"
          placeholder=""
          data={["Pending", "In Progress", "Completed"]}
          defaultValue={["Pending"]}
          radius="md"
          rightSectionPointerEvents="none"
          rightSection={icon}
          className="font-semibold"
        />

        <div className="relative">
           <Select
        {...form.getInputProps("approverId")}
        key={form.key("approverId")}
        id="approverId"
        label="Approver"
        placeholder="John Doe, Chief Risk Officer"
        data={company}
        searchable
        nothingFound="No options"
        mb="md"
        radius="md"
        className="font-semibold"
        styles={() => ({
          input: {
            ...(form.errors.approverId
              ? styles.errorBorder
              : styles.inputNormal),
            "&:focus": { border: "2px solid blue" },
          },
        })}
        onFocus={() => {
          form.setFieldError("approverId", "");
        }}
      />
      {form.errors.approverId && (
        <FaExclamationCircle style={styles.errorIcon} />
      )}
        </div>

        <DateInput
          {...form.getInputProps("lastUpdate")}
          key={form.key("lastUpdate")}
          placeholder="dd/MM/YYYY"
          rightSectionPointerEvents="none"
          rightSection={
            <img src={Calendar} alt="calendar" className="w-6 h-6" />
          }
          id="lastUpdate"
          name="lastUpdate"
          label="Last Updated"
          mb="md"
          radius="md"
          className="font-semibold"
        />

        <div className="relative">
 <Select
  {...form.getInputProps("lastUpdatedById")}
  key={form.key("lastUpdatedById")}
  id="lastUpdatedById"
  label="Last Updated By"
  placeholder="Jane Smith"
  data={company} 
  searchable
  nothingFound="No options"
  radius="md"
  className="font-semibold"
  styles={() => ({
    input: {
      ...(form.errors.lastUpdatedById ? styles.errorBorder : styles.inputNormal),
      "&:focus": { border: "2px solid blue" },
    },
  })}
  onFocus={() => {
    form.setFieldError("lastUpdatedById", "");
  }}
/>
</div>
      </div>

      <hr className="border border-[#C9C9C9] my-4" />

      <div className="grid grid-cols-2 gap-8 mb-20">
        <Button
          variant="default"
          className="rounded-lg font-semibold"
        >
          Clear
        </Button>

        <Button
          variant="filled"
          className="flex gap-3 justify-center rounded-lg bg-primary font-semibold"
          onClick={handleSave}
          disabled={loading}
        >
          Save {loading && <div className="submit-loader" /> }
        </Button>
      </div>
    </div>
  );
};

export default GreenRiskInputs;
