import {
    Box,
    Title,
    Text,
    List,
    Stack,
    Paper,
    Group,
    ThemeIcon,
  } from "@mantine/core";
  import { FaCheck } from "react-icons/fa";
  
  export default function CarbonSystemUserGuide() {
    return (
      <Box mx="auto" maxWidth="1200px" p="md">
        <Title order={1} mb="xl">Carbon System User Guide</Title>
  
        <Stack spacing="lg">
          {/* Supplier Information Section */}
          <Text size="lg" mb="sm"><b>Supplier Information</b></Text>
          <Text size="lg" mt="lg" mb="sm"><b>View & Manage Supplier Records</b></Text>
          
          <Text size="lg" mt="lg" mb="sm"><b>Table Overview</b></Text>
          <List listStyleType="disc" spacing="sm">
            <List.Item>
              <Text fw={700}>Each supplier entry includes:</Text>
              <List listStyleType="circle" spacing="sm" ml="1.5rem">
                <List.Item>Name</List.Item>
                <List.Item>Supplies Category</List.Item>
                <List.Item>Contact</List.Item>
                <List.Item>Date of Contact</List.Item>
                <List.Item>To (engagement end date)</List.Item>
                <List.Item>Region</List.Item>
                <List.Item>Sector</List.Item>
                <List.Item>Services</List.Item>
              </List>
            </List.Item>
          </List>
  
          <Text size="lg" mt="lg" mb="sm"><b>You can:</b></Text>
          <List listStyleType="disc" spacing="sm">
            <List.Item>Edit supplier details by selecting a row and clicking Edit.</List.Item>
            <List.Item>Delete selected suppliers via checkbox + Delete.</List.Item>
            <List.Item>Use Column Selector to hide/show specific data columns.</List.Item>
          </List>
  
          {/* Filter & Search Section */}
          <Text size="lg" mt="lg" mb="sm"><b>Filter & Search</b></Text>
          <Text size="sm" c="dimmed" mb="sm">
            Click Filter or use the search bar:
          </Text>
          <List listStyleType="disc" spacing="sm">
            <List.Item>Filter by Name, Region, Sector, Category, etc.</List.Item>
            <List.Item>Search by keywords or assessment question.</List.Item>
          </List>
          
          <Text size="sm" c="dimmed" mt="sm" mb="sm">
            Use this for:
          </Text>
          <List listStyleType="circle" spacing="sm">
            <List.Item>ESG reporting preparation</List.Item>
            <List.Item>Supplier audit or traceability analysis</List.Item>
            <List.Item>Regional/scope-based summaries</List.Item>
          </List>
  
          {/* Export Data Section */}
          <Text size="lg" mt="lg" mb="sm"><b>Export Data</b></Text>
          <Text size="sm" c="dimmed" mb="sm">
            Click the Export button:
          </Text>
          <List listStyleType="disc" spacing="sm">
            <List.Item>Exports data in CSV or Excel format</List.Item>
            <List.Item>Useful for integration into reporting software or internal ESG dashboards</List.Item>
          </List>
          <Text size="sm" c="dimmed" mt="sm">
            File size limit: 20 MB per export (same as system-wide limits)
          </Text>
  
          {/* Request Data from Suppliers Section */}
          <Text size="lg" mt="lg" mb="sm"><b>Request Data from Suppliers</b></Text>
          <Text size="sm" c="dimmed" mb="sm">
            Each supplier row includes an <Text fw={700}>Action</Text> column with a "Request" button.
          </Text>
          
          <Text size="sm" c="dimmed" mt="sm" mb="sm">
            When clicked:
          </Text>
          <List listStyleType="decimal" spacing="sm">
            <List.Item>Automatically sends a data request to the Contact Email</List.Item>
            <List.Item>Request includes:</List.Item>
            <List>
              <List.Item>Standardized emissions questionnaire</List.Item>
              <List.Item>Secure upload link or portal access (if integrated)</List.Item>
              <List.Item>Status can be tracked (functionality assumed for future versions)</List.Item>
            </List>
          </List>
  
          {/* Use Case Section */}
          <Text size="lg" mt="lg" mb="sm"><b>Use Case</b></Text>
          <Text c="dimmed">
            Ideal for Scope 3 Category 1-15 emission tracking, value chain engagement, and supplier sustainability programs.
          </Text>
  
          {/* Optional Tip Banner (add if needed) */}
          <Paper shadow="sm" p="md" radius="md" mt="xl" withBorder>
            <Group position="apart">
              <ThemeIcon variant="light" radius="xl" size="2rem">
                <FaCheck size="1.2rem" />
              </ThemeIcon>
              <Text fw={500}>
                Use the Column Selector to focus on critical ESG metrics during audits!
              </Text>
            </Group>
          </Paper>
        </Stack>
      </Box>
    );
  }