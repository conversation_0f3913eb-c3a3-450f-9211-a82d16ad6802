import { ExportIcon } from "@/assets/icons/ReportAndAnalytic";
import { openIssuesBadges } from "../Constants";
import { DonutChart } from "@mantine/charts";
import { useEffect, useRef, useState } from "react";
import ApiS3 from "@/Api/apiS3";
import Loading from "@/Components/Loading";

import * as htmlToImage from 'html-to-image';


const OpenIssues = () => {
  const chartValues = [
    { title: "High Impact", color: "#0D9488" ,value: 0, },
    { title: "Medium Impact", color: "#F59E0B" ,value: 0,},
    { title: "Low Impact", color: "#D1D5DB" ,value: 0, },
    { title: "Overdue Actions", color: "#EF4444" ,value: 0,},
  ];
  const [chartData, setchartData] = useState([])

  const [loading, setloading] = useState(false)

  const getData = async () => {
    setloading(true)
    try {
      const res = await ApiS3.get("findings/findingsChart");
      const data = chartValues.map((item,i)=> {
        if(i+1 == 1) {
          return {...item,value: res.data.high }
        }
        if(i+1  ==2) {
          return {...item,value: res.data.medium }
        }
        if(i+1  ==3) {
          return {...item,value: res.data.low }
        }
        if(i+1  ==4) {
          return {...item,value: res.data.overdueActions }
        }
      });
      setchartData(data);
    } catch (er) {
        console.log("🚀 ~ getData ~ er:", er)
    }
    setloading(false)
  };

  useEffect(() => {
    getData();
  },[]);

  const chart = useRef(null);  


  const handleExportPNG = () => {
    if (chart.current) {
        htmlToImage
            .toPng(chart.current, {
                quality: 0.95, // Image quality (0 to 1)
                pixelRatio: 2, // Increase resolution
                backgroundColor: '#f5f5f5', // Match the background color
            })
            .then((dataUrl) => {
                // Create a link element to trigger the download
                const link = document.createElement('a');
                link.href = dataUrl;
                link.download = 'Open Issues.png'; // File name for the download
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            })
            .catch((error) => {
                console.error('Error exporting PNG:', error);
                alert('Failed to export PNG. Please try again.');
            });
    }
  };
  return (
    <div className="mt-10 bg-white shadow-md rounded-2xl p-4">
      <div className="flex flex-wrap justify-between items-center mb-10">
        <h5 className="text-xl">Open Issues</h5>

        <button onClick={handleExportPNG} className="bg-bg-lite2 text-primary flex justify-between items-center rounded-xl p-3 gap-2">
          {/* <MdOutlineFilterList /> */}
          <ExportIcon />
          <span className="text-sm font-bold">PNG Export</span>
        </button>
      </div>

      <div ref={chart} className="grid lg:grid-cols-2 gap-4">
       {loading ?<Loading /> :  <DonutChart data={chartData} />}
        <div className="flex flex-col gap-2">
          {openIssuesBadges.map((badge) => (
            <div className="flex items-center gap-2" key={badge.title}>
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: badge.color }}
              ></div>
              <span className="text-sm font-bold">{badge.title}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default OpenIssues;
