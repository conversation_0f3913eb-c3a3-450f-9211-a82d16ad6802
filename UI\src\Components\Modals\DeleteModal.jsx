import { useDisclosure } from "@mantine/hooks";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ader } from "@mantine/core";
import { IoTrashOutline } from "react-icons/io5";
import ApiS3 from "@/Api/apiS3";
import { useState } from "react";
import { showNotification } from "@mantine/notifications";
import ApiScrapping from "@/Api/apiSrapping";


function DeleteModal({ sType, apiLink,refreshFn, ids = [] }) {
  const [opened, { open, close }] = useDisclosure(false);

  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    setLoading(true);
    if(sType === 's3'){
        try {   
            const response = await ApiS3.delete(apiLink);
            console.log(response);
            if(response.status === 200){
                showNotification({
                    title: "Success",
                    message: "Risk deleted successfully",
                    color: "green",
                });
            }
        } catch (error) {
            console.log(error);
        }
    }


    if(sType == 'scrapping'){
      try {
        const response = await ApiScrapping.post(apiLink,{ids:ids});
        console.log("🚀 ~ handleDelete ~ response:", response)
        if(response.status === 200){
          showNotification({
            title: "Success",
            message: "Scrapped data deleted successfully",
            color: "green",
          });
        }
      } catch (error) {
        showNotification({
          title: "error",
          message: error.response?.data?.message || "Failed to delete",
          color: "red",
      });
      } finally {
        setLoading(false);
      }
    }
    setLoading(false);
    refreshFn();
    close();
  };

  return (
    <>
      <Modal opened={opened} onClose={close}>
        {/* Modal content */}
        <div className="flex flex-col gap-4">
            <p>Are you sure you want to delete this item?</p>
            <div className="flex justify-end gap-2">
                <Button variant="default" onClick={close}>Cancel</Button>
                <Button disabled={loading} variant="outline" color="red" onClick={handleDelete}>
                    {loading ? <Loader size={16} /> : 'Delete'}
                </Button>
            </div>
        </div>
      </Modal>

      <button
        onClick={open}
        className="w-8 h-8 bg-[#F5F4F5] rounded-md flex items-center justify-center"
      >
        <IoTrashOutline className="text-red-500" size={15} />
      </button>
    </>
  );
}

export default DeleteModal;
