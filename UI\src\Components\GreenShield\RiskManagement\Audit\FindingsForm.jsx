import { useEffect, useState } from "react";
import { MdKeyboardArrowDown } from "react-icons/md";

import {
  Select,
  Button,
  Textarea,
  Loader,
  Input,
  InputWrapper,
} from "@mantine/core";

import AiApi from "@/Api/aiApiConfig";

import { showNotification } from "@mantine/notifications";
import { DateInput } from "@mantine/dates";
import { AIIcon, ESGAuditCalenderIcon } from "@/assets/icons";
import { useForm } from "@mantine/form";
import ApiS3 from "@/Api/apiS3";
import Loading from "@/Components/Loading";
import { toast } from "react-toastify";
import { aiTextType } from "@/Pages/S3GreenShield/financialServicesStart/Partials/ESGRiskManagementSystem/RiskIdentificationAndAssessment/components/StaticData";
import AuthConfig from "@/Api/authConfig";

function FindingsForm({ handleChange, formData, setIsAiDone, resetFindings }) {
  const { recommendations } = formData;
  const [aiLoad, setAiLoad] = useState(false);
  const [loading, setLoading] = useState(false);
  const [relatedStrategies, setRelatedStrategies] = useState([]);

  const [isSubmit, setIsSubmit] = useState(false);
  const [tagColleaguesOptions, setTagColleaguesOptions] = useState([]);

  useEffect(() => {
    const tagColleaguesOptions = async () => {
      try {
        const response = await AuthConfig.get("/get_all_user_by_token");
        console.log("Full response", response.data);

        const users = response.data.companyUsers;

        if (Array.isArray(users)) {
          const userAuthId = users
            .filter((user) => !!user.userAuthId)
            .map((user) => ({
              value: user.userAuthId,
              label: user.userName,
            }));
          setTagColleaguesOptions(userAuthId);
        } else {
          console.error("Error:", response.data);
        }
      } catch (error) {
        console.error("Error tagColleaguesOptions:", error);
      }
    };

    tagColleaguesOptions();
  }, []);

  const form = useForm({
    mode: "controlled",
    initialValues: {
      ...formData,
    },
  });

  const aiFunc = async (type) => {
    if (recommendations.length < 20) {
      showNotification({ message: `Minimum character 20`, color: "red" });
      return;
    }

    const data = {
      processor: "recommendations",
      resources: {
        prompt: recommendations,
        output_type: type,
      },
    };
    setAiLoad(true);
    try {
      const res = await AiApi.post(`process_request`, data);
      handleChange("recommendation", res.data.ai_response);
      setIsAiDone(true);
    } catch (er) {
      console.log("🚀 ~ aiFunc ~ er:", er);
    }
    setAiLoad(false);
  };

  useEffect(() => {
    async function getRelatedStrategies() {
      setLoading(true);
      try {
        const response = await ApiS3.get("/auditControls");
        if (response.status === 200) {
          setRelatedStrategies(
            response.data.map((strategy) => ({
              ...strategy,
              value: strategy._id,
              label: strategy.evaluation,
            }))
          );
        }
      } catch (error) {
        toast.fail("Something went wrong");
        setRelatedStrategies(["Something went wrong"]);
        console.log(error.message);
      } finally {
        setLoading(false);
      }
    }

    getRelatedStrategies();
  }, []);

  const handleSubmit = form.onSubmit(async (values) => {
    setIsSubmit(true);
    try {
      const response = await ApiS3.post("/findings", values);
      if (response.status === 201) {
        form.reset();
        form.setValues({
          controlDescription: null,
          riskLevel: null,
          status: null,
        });
        resetFindings();
        toast.success("Finding created successfully");
      }
    } catch (error) {
      toast.fail("Something went wrong");
      console.log(error.message);
    }
    setIsSubmit(false);
  });

  return loading ? (
    <Loading />
  ) : (
    <form onSubmit={handleSubmit} className="my-10">
      <h5 className="text-primary font-bold text-center mb-10 lg:text-2xl">
        Findings
      </h5>
      <div className="flex-wrap sm:flex-nowrap flex items-center justify-between gap-4">
        <InputWrapper label="Finding ID">
          <Input
            className="my-2"
            placeholder="Auto"
            {...form.getInputProps("id")}
            size="md"
            disabled
            required
          />
        </InputWrapper>

        <InputWrapper label="Related Strategy/Control" className="w-full">
          <Select
            className="my-2"
            rightSection={<MdKeyboardArrowDown />}
            {...form.getInputProps("controlDescription")}
            onChange={(value) => {
              handleChange("controlDescription", value);
              form.setFieldValue("controlDescription", value);
            }}
            name="controlDescription"
            placeholder="Link to Control ID/Strategy"
            data={relatedStrategies}
            size="md"
            required
          />
        </InputWrapper>
      </div>
      <InputWrapper label="Finding Description" className="w-full">
        <Textarea
          className="my-2"
          id="findingDescription"
          // disabled={aiLoad}
          {...form.getInputProps("findingDescription")}
          onChange={(e) => {
            handleChange("findingDescription", e.target.value);
            form.setFieldValue("findingDescription", e.target.value);
          }}
          // label="Impact Description"
          placeholder="Enter Text"
          classNames={{
            root: "w-full",
            // label: "text-base font-medium mb-4",
            input:
              "border min-h-[145px] mt-5 border-gray-300 rounded px-3 py-2",
          }}
          required
        />
      </InputWrapper>
      <InputWrapper label="Gap Identified" className="w-full">
        <Textarea
          className="my-2"
          id="gapIdentified"
          {...form.getInputProps("gapIdentified")}
          onChange={(e) => {
            handleChange("gapIdentified", e.target.value);
            form.setFieldValue("gapIdentified", e.target.value);
          }}
          placeholder="Enter Text"
          classNames={{
            root: "w-full",
            input:
              "border min-h-[145px] mt-5 border-gray-300 rounded px-3 py-2",
          }}
          required
        />
      </InputWrapper>
      <InputWrapper label="Priority Based on Risk Level" className="w-full">
        <Select
          className="my-2"
          rightSection={<MdKeyboardArrowDown />}
          {...form.getInputProps("riskLevel")}
          onChange={(value) => {
            handleChange("riskLevel", value);
            form.setFieldValue("riskLevel", value);
          }}
          name="riskLevel"
          placeholder="High"
          data={["Critical", "High", "Medium", "Low"]}
          size="md"
          required
        />
      </InputWrapper>
      <div className="flex justify-between items-center mt-3">
        <label htmlFor="imp-decs" className="text-base font-medium text-black">
          Recommendations
        </label>
      </div>

      <div className="relative border border-gray-300 min-h-[145px] rounded py-2 bg-white mt-3">
        <div
          title="Ai Suggestion"
          disabled={aiLoad}
          className={`absolute right-2 top-2 group`}
        >
          {aiLoad ? (
            <Loader color="#07838F" size="sm" type="dots" />
          ) : (
            <AIIcon className="cursor-pointer" />
          )}
          <div
            className={`${
              aiLoad ? "hidden" : "flex"
            } gap-1 absolute z-20 top-5 right-0 flex-col opacity-0 group-hover:opacity-100 transition-opacity duration-200`}
          >
            {aiTextType.map((type, index) => (
              <Button
                variant="transparent"
                key={index}
                onClick={() => aiFunc(type)}
                className={`base-border p-1 capitalize bg-white hover:bg-primary hover:text-white`}
              >
                {type}
              </Button>
            ))}
          </div>
        </div>

        <Textarea
          id="recommendations"
          value={recommendations || ""}
          {...form.getInputProps("recommendations")}
          onChange={(e) => {
            handleChange("recommendations", e.target.value);
            form.setFieldValue("recommendations", e.target.value);
          }}
          placeholder="Enter Text"
          classNames={{
            root: "w-[96%] min-h-[145px]",
            input: "min-h-[145px] border-none",
          }}
          required
        />
      </div>
      <InputWrapper label="Recommendation Plan" className="w-full">
        <Textarea
          className="my-2"
          id="recommendationPlan"
          // disabled={aiLoad}

          {...form.getInputProps("recommendationPlan")}
          onChange={(e) => {
            handleChange("recommendationPlan", e.target.value);
            form.setFieldValue("recommendationPlan", e.target.value);
          }}
          // label="Impact Description"
          placeholder="Enter Text"
          classNames={{
            root: "w-full",
            // label: "text-base font-medium mb-4",
            input:
              "border min-h-[145px] mt-5 border-gray-300 rounded px-3 py-2",
          }}
          required
        />
      </InputWrapper>

      <div className="flex-wrap sm:flex-nowrap flex items-center justify-between gap-4">
        <InputWrapper label="Status" className="w-full">
          <Select
            className="my-2"
            rightSection={<MdKeyboardArrowDown />}
            {...form.getInputProps("status")}
            onChange={(value) => {
              handleChange("status", value);
              form.setFieldValue("status", value);
            }}
            name="status"
            placeholder="Open"
            data={["Open", "In Progress", "Completed"]}
            size="md"
            required
          />
        </InputWrapper>
        <InputWrapper label="Owner" className="w-full">
          <Select
            className=""
            id="owner"
            // disabled={aiLoad}
            name="owner"
            {...form.getInputProps("owner")}
            onChange={(value) => {
              handleChange("owner", value);
              form.setFieldValue("owner", value);
            }}
            // label="Impact Description"
            rightSection={<MdKeyboardArrowDown />}
            placeholder="Enter Text"
            size="md"
            data={tagColleaguesOptions}
            required
          />
        </InputWrapper>
        <InputWrapper label="Due Date" className="w-full">
          <DateInput
            className="my-2"
            rightSection={<ESGAuditCalenderIcon />}
            {...form.getInputProps("dueDate")}
            onChange={(value) => {
              handleChange("dueDate", value);
              form.setFieldValue("dueDate", value);
            }}
            placeholder="Date"
            size="md"
            rightSectionPointerEvents="none"
            required
          />
        </InputWrapper>
      </div>
      <div className="flex gap-5 mt-5">
        <Button
          onClick={() => {
            form.reset();
            form.setValues({
              controlDescription: null,
              riskLevel: null,
              status: null,
            });
            resetFindings();
          }}
          className="flex-1 bg-bg-lite1 text-gray-700 py-2 px-4 text-center border-r border-gray-300 hover:bg-teal-700 duration-500"
        >
          Clear
        </Button>
        <Button
          disabled={isSubmit}
          type="submit"
          className="flex-1 bg-teal-500 text-white py-2 px-4 text-center hover:bg-teal-700 duration-500"
        >
          Save {isSubmit ? <Loading /> : ""}
        </Button>
      </div>
    </form>
  );
}

export default FindingsForm;
