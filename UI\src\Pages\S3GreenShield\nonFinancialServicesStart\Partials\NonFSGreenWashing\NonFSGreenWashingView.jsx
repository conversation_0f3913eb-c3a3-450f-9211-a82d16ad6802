import React from "react";
import S3Layout from "@/Layout/S3Layout";
import useSideBarRoute from "@/hooks/useSideBarRoute";

import { <PERSON><PERSON>, Modal } from "@mantine/core";
import SearchBox from "@/Components/SearchBox";
import { useDisclosure } from "@mantine/hooks";

// import GreenRiskAssessmentTable from "./Components/GreenRiskAssessmentTable";
import RiskCalculation from "./Components/RiskCalculation";

import { PiExportLight } from "react-icons/pi";
import { IoAddOutline } from "react-icons/io5";
import { FaRegTrashAlt, FaRegEdit } from "react-icons/fa";
import { CiFilter } from "react-icons/ci";
import { CgSandClock } from "react-icons/cg";
import GreenRiskAssessmentTable from "@/Pages/S3GreenShield/financialServicesStart/Partials/GreenWashingRiskAssessment/Components/GreenRiskAssessmentTable";
// import FinancialAntiGreenWashingView from "@/Pages/S3GreenShield/financialServicesStart/Partials/FinancialAntiGreenWashing/FinancialAntiGreenWashingView";

const NonFSGreenWashingView = () => {
  const { greenShieldMenu } = useSideBarRoute();

  const [opened, { open, close }] = useDisclosure(false);

  return (
    <S3Layout menus={greenShieldMenu}>
      
      <div className="flex justify-end mt-9">
        <Button className="text-primary" variant="transparent" onClick={open}>
          How is risk calculated?
        </Button>
      </div>
      {/* <h2 className="flex  items-center justify-center gap-3 text-center py-12 text-2xl animate-pulse">
        <CgSandClock /> chart Coming Soon
      </h2>
      */}
      <Modal
        size={"90%"}
        opened={opened}
        onClose={close}
        withCloseButton={false}
      >
        <RiskCalculation />
      </Modal>

      {/* <div className="flex flex-wrap justify-between items-center bg-white rounded-xl p-6">
        <div className="title">
          <h1 className="text-xl">Risk Manegement</h1>
          <h2 className="text-sm text-secondary-lite-100 mt-1">
            A descriptive body text comes here
          </h2>
        </div>

        <div className="btns flex items-center gap-6">
          <Button
            className="border-[1px] w-[105px] h-[42px] text-sm font-bold rounded-lg text-secondary-300 border-secondary-300 bg-white px-1"
            size="md"
          >
            <span className="me-1">
              <PiExportLight className="text-lg" />
            </span>
            <span>Export</span>
          </Button>
          <Button
            className="border-[1px] w-[105px] h-[42px] text-sm font-bold rounded-lg bg-secondary-300 px-1"
            size="md"
          >
            <span className="me-1">
              <IoAddOutline className="text-lg" />
            </span>
            <span>Add Risk</span>
          </Button>
        </div>
      </div> */}

      {/* <div className="search-filter-delete flex items-center justify-between flex-wrap mt-5">
        <SearchBox
          className="w-[341px] h-[38px]"
          classNames={{ input: "border-none rounded-lg shadow-sm" }}
        />

        <div className="delete-filter flex flex-wrap gap-6">
          <Button className="w-[36px] h-[36px] flex justify-center items-center bg-white rounded-xl ">
            <FaRegEdit className="text-secondary-300" />
          </Button>

          <Button className="w-[36px] h-[36px] flex justify-center items-center bg-secondary-300 rounded-xl ">
            <FaRegTrashAlt className="text-white" />
          </Button>

          <div className="flex items-center bg-white h-[38px] rounded-lg shadow-sm">
            <CiFilter className="mx-2" />
            <IoAddOutline className="mx-2" />
            <p className="font-semibold mx-2 m-0">Filter</p>
          </div>
        </div>
      </div> */}

      {/* <GreenRiskAssessmentTable /> */}
      {/* <FinancialAntiGreenWashingView /> */}
      <GreenRiskAssessmentTable />
    </S3Layout>
  );
};

export default NonFSGreenWashingView;
