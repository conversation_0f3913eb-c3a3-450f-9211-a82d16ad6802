import { useState, useEffect, useCallback, useRef } from "react";
import { AIIcon } from "@/assets/icons";
import { Loader } from "@mantine/core";
import { debounce } from "lodash";
import Cookies from "js-cookie";
import io from "socket.io-client";

// Fetch and cache user ID
const fetchUserId = async (token) => {
  try {
    const response = await fetch(
      "https://portal-auth-main-staging.azurewebsites.net/account_information",
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );
    if (!response.ok) {
      throw new Error(`HTTP error: ${response.status}`);
    }
    const data = await response.json();
    return data.userId || null;
  } catch (error) {
    console.error("[fetchUserId] Failed to fetch user ID:", error.message);
    return null;
  }
};

let cachedUserId = null;

export function TextAreaGroup({ id, label, question, value, setSections }) {
  const [localValue, setLocalValue] = useState(value || "");
  const [loadingAI, setLoadingAI] = useState(false);
  const [isButtonVisible, setIsButtonVisible] = useState(!!value?.trim());
  const [isSomeoneTyping, setIsSomeoneTyping] = useState(false);
  const [typingUser, setTypingUser] = useState(null);
  const [currentUserId, setCurrentUserId] = useState(cachedUserId);
  const socketRef = useRef(null);
  const isTypingRef = useRef(false); // Track typing state

  // Create a stable debounce function for stop_typing
  const stopTypingDebounce = useRef(
    debounce(() => {
      if (isTypingRef.current) {
        sendTypingStatus("stop_typing");
        isTypingRef.current = false;
      }
    }, 2000)
  ).current;

  // Send typing status event
  const sendTypingStatus = (type, text = null) => {
    if (socketRef.current?.connected && currentUserId) {
      socketRef.current.emit(type, {
        disclosure_id: id,
        user: { user_id: currentUserId },
        ...(text !== null && { disclosure_text: text }),
      });
    } else {
      console.warn(
        `[sendTypingStatus] Socket not connected or user_id missing, cannot send ${type}`
      );
    }
  };

  // Debounced typing event
  const debouncedTyping = useCallback(
    debounce((newValue) => {
      sendTypingStatus("typing", newValue);
    }, 250),
    [currentUserId, id]
  );

  // Handle AI enhancement
  const handleEnhanceAnswer = async () => {
    if (!localValue.trim()) {
      return;
    }
    setLoadingAI(true);

    try {
      const token = Cookies.get("level_user_token");
      if (!token) {        throw new Error("Token not found");
      }

      const response = await fetch(
        "https://gen-ai0-staging.azurewebsites.net/process_request",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            processor: "issb_report",
            resources: {
              question: question.requirement_text,
              user_answer: localValue,
            },
          }),
        }
      );

      const data = await response.json();
      if (data?.ai_response?.enhanced_answer) {
        const enhancedAnswer = data.ai_response.enhanced_answer;
        setLocalValue(enhancedAnswer);
        sendTypingStatus("typing", enhancedAnswer);
      }
    } catch (error) {
      console.error("[handleEnhanceAnswer] Error fetching AI response:", error);
    } finally {
      setLoadingAI(false);
    }
  };

  // Handle textarea focus
  const handleFocus = () => {
    if (!currentUserId) {
      return;
    }
    if (!socketRef.current?.connected) {
      return;
    }
    sendTypingStatus("typing", localValue);
    isTypingRef.current = true;
  };

  // Handle textarea blur
  const handleBlur = () => {
    if (!currentUserId || !socketRef.current?.connected) {
      return;
    }
    sendTypingStatus("stop_typing");
    isTypingRef.current = false;
    stopTypingDebounce.cancel(); // Cancel any pending debounce
  };

  // Handle input change
  const handleInputChange = (e) => {
    const newValue = e.target.value;
    setLocalValue(newValue);
    setIsButtonVisible(!!newValue.trim());

    if (!currentUserId || !socketRef.current?.connected) {
      return;
    }

    isTypingRef.current = true;
    debouncedTyping(newValue);
    stopTypingDebounce(); // Reset the debounce timer
  };

  // Set up Socket.IO
  useEffect(() => {
    const token = Cookies.get("level_user_token");
    if (!token) {
      return;
    }

    const initialize = async () => {
      if (!cachedUserId) {
        const userId = await fetchUserId(token);
        if (!userId) {
          return;
        }
        cachedUserId = userId;
        setCurrentUserId(userId);
      } else {
        setCurrentUserId(cachedUserId);
      }

      socketRef.current = io(
        "https://issb-report-api-staging.azurewebsites.net/",
        {
          path: "/socket.io/",
          reconnection: true,
          reconnectionAttempts: Infinity,
          randomizationFactor: 0.5,
          auth: { token: `Bearer ${token}` },
          withCredentials: true,
          extraHeaders: { Authorization: `Bearer ${token}` },
        }
      );

      socketRef.current.on("connect", () => {
        socketRef.current.emit("join_disclosure", { disclosure_id: id });
      });

      socketRef.current.on("connect_error", (error) => {
        console.error("[Socket.IO] Connection error:", error.message);
      });

      socketRef.current.on("disconnect", (reason) => {
      });

      socketRef.current.on("typing", (data) => {
        if (data.disclosure_id === id) {
          if (data.user?.user_id === currentUserId) {
            return;          
          }
          setTypingUser(data.user);
          setIsSomeoneTyping(true);
          if (data.disclosure_text !== localValue) {
            setLocalValue(data.disclosure_text);
            setSections((prevSections) => {
              const updatedSections = [...prevSections];
              const sectionIndex = updatedSections.findIndex((section) =>
                section.topics.some((topic) =>
                  topic.disclosures.some((d) => d.id === id)
                )
              );
              if (sectionIndex !== -1) {
                const topicIndex = updatedSections[
                  sectionIndex
                ].topics.findIndex((topic) =>
                  topic.disclosures.some((d) => d.id === id)
                );
                const disclosureIndex = updatedSections[sectionIndex].topics[
                  topicIndex
                ].disclosures.findIndex((d) => d.id === id);
                updatedSections[sectionIndex].topics[topicIndex].disclosures[
                  disclosureIndex
                ].disclosure_text = data.disclosure_text;
              }
              return updatedSections;
            });
          }
        }
      });

      socketRef.current.on("stop_typing", (data) => {
        if (data.disclosure_id === id && data.user?.user_id !== currentUserId) {
          setIsSomeoneTyping(false);
          setTypingUser(null);
        }
      });

      socketRef.current.on("sync_disclosure", (data) => {
        if (data.disclosure_id === id) {
          setLocalValue(data.disclosure_text);
          setSections((prevSections) => {
            const updatedSections = [...prevSections];
            const sectionIndex = updatedSections.findIndex((section) =>
              section.topics.some((topic) =>
                topic.disclosures.some((d) => d.id === id)
              )
            );
            if (sectionIndex !== -1) {
              const topicIndex = updatedSections[sectionIndex].topics.findIndex(
                (topic) => topic.disclosures.some((d) => d.id === id)
              );
              const disclosureIndex = updatedSections[sectionIndex].topics[
                topicIndex
              ].disclosures.findIndex((d) => d.id === id);
              updatedSections[sectionIndex].topics[topicIndex].disclosures[
                disclosureIndex
              ].disclosure_text = data.disclosure_text;
            }
            return updatedSections;
          });
        }
      });
    };

    initialize();

    return () => {
      if (socketRef.current) {
        socketRef.current.emit("leave_disclosure", { disclosure_id: id });
        socketRef.current.disconnect();
        socketRef.current = null;
      }
      stopTypingDebounce.cancel(); // Cancel any pending debounce on unmount
    };
  }, [id, setSections, value]);

  // Update local value when prop value changes
  useEffect(() => {
    setLocalValue(value || "");
    setIsButtonVisible(!!value?.trim());
  }, [value]);

  const isDisabled =
    loadingAI || (isSomeoneTyping && typingUser?.user_id !== currentUserId);

  return (
    <div className="flex flex-col gap-2 relative">
      <label htmlFor={id} className="font-medium">
        {label}
      </label>
      <div className="relative">
        <textarea
          id={id}
          rows={4}
          className="w-full border border-gray-300 rounded-md p-2 pr-10"
          placeholder="Enter text or N/A if not applicable to you ..."
          value={localValue}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          disabled={isDisabled}
        />
        {isButtonVisible && (
          <button
            onClick={handleEnhanceAnswer}
            disabled={loadingAI}
            className="absolute right-2 top-2"
          >
            {loadingAI ? (
              <Loader color="#07838F" size="sm" type="dots" />
            ) : (
              <AIIcon className="cursor-pointer" />
            )}
          </button>
        )}
        {isSomeoneTyping && typingUser?.user_id !== currentUserId && (
          <span className="absolute top-[-20px] right-2 text-[#07838F] text-sm">
            {typingUser?.user_name} is typing...
          </span>
        )}
      </div>
    </div>
  );
}