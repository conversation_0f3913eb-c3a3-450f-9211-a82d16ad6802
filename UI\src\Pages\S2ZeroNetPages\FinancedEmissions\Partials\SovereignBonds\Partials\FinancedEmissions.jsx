import Api_PCAF from "@/Api/apiS2_PCAF";
import { useSovereignBondsStore } from "@/Store/useSovereignBondsStore";
import { Spinner } from "@react-pdf-viewer/core";
import axios from "axios";
import Cookies from "js-cookie";
import React from "react";
import { BsStars } from "react-icons/bs";
import countries from "world-countries";

const prioritized = [
  "United Kingdom",
  "United Arab Emirates",
  "United States",
  "Saudi Arabia",
  "Qatar",
];

// Sort countries: prioritized first, then the rest
const sortedCountries = [
  ...countries.filter((c) => prioritized.includes(c.name.common)),
  ...countries
    .filter((c) => !prioritized.includes(c.name.common))
    .sort((a, b) => a.name.common.localeCompare(b.name.common)),
];

const FinancedEmissions = () => {
  const {
    value_of_bond_investment,
    total_government_debt,
    country_total_ghg_emissions,
    emission_source,
    data_quality_score,
    country,
    loading,

    setValueOfBondInvestment,
    setCountry,
    setTotalGovernmentDebt,
    setCountryTotalGHGEmissions,
    setEmissionSource,
    setDataQualityScore,
    setLoading,
    setResults,
    estimate_total_government_debt_loading,
    estimate_country_total_ghg_emissions_loading,
    setEstimateTotalGovernmentDeptLoading,
    setEstimateCountryTotalGHGEmissionLoading,
  } = useSovereignBondsStore();

  const handleCalculate = async () => {
    setLoading(true);
    Api_PCAF.post("/sovereign-bonds", {
      value_of_bond_investment: value_of_bond_investment,
      country_emissions: "0",
      total_government_debt: total_government_debt,
      country_total_ghg_emissions: country_total_ghg_emissions,
      emission_source: emission_source,
      data_quality_score: data_quality_score,
    })
      .then((res) => {
        setLoading(false);
        const financedEmissions = res.data;
        setResults(financedEmissions);
      })
      .catch((error) => {
        console.log(error);
        setLoading(false);
      });
  };
  const estimate_total_government_debt = async () => {
    const questions = getAnsweredQuestions();
    console.log(questions);
    setEstimateTotalGovernmentDeptLoading(true);
    axios
      .post(
        "https://gen-ai0-staging.azurewebsites.net/process_request",
        {
          processor: "pcaf",
          resources: {
            qa_context: questions,
            estimation_needed: "Total Government Debt",
          },
        },
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("level_user_token")}`,
          },
        }
      )
      .then((res) => {
        setEstimateTotalGovernmentDeptLoading(false);
        setTotalGovernmentDebt(res.data.ai_response.value);
        console.log(res.data);
      })
      .catch((err) => {
        setEstimateTotalGovernmentDeptLoading(false);
        console.log(err);
      });
  };
  const estimate_country_total_ghg_emissions = async () => {
    const questions = getAnsweredQuestions();
    console.log(questions);
    setEstimateCountryTotalGHGEmissionLoading(true);
    axios
      .post(
        "https://gen-ai0-staging.azurewebsites.net/process_request",
        {
          processor: "pcaf",
          resources: {
            qa_context: questions,
            estimation_needed: "Country's Total GHG Emissions (tCO2e)",
          },
        },
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("level_user_token")}`,
          },
        }
      )
      .then((res) => {
        setEstimateCountryTotalGHGEmissionLoading(false);
        setCountryTotalGHGEmissions(res.data.ai_response.value);
        console.log(res.data);
      })
      .catch((err) => {
        setEstimateCountryTotalGHGEmissionLoading(false);
        console.log(err);
      });
  };

  const getAnsweredQuestions = () => {
    let questions = [];

    if (value_of_bond_investment !== "") {
      questions.push({
        question: "Value of Bond Investment",
        user_answer: value_of_bond_investment,
      });
    }
    if (total_government_debt !== "") {
      questions.push({
        question: "Total Government Debt",
        user_answer: total_government_debt,
      });
    }
    if (country_total_ghg_emissions !== "") {
      questions.push({
        question: "Country's Total GHG Emissions",
        user_answer: country_total_ghg_emissions,
      });
    }
    if (emission_source !== "") {
      questions.push({
        question: "Emission Source",
        user_answer: emission_source,
      });
    }
    if (data_quality_score !== "") {
      questions.push({
        question: "Data Quality Score",
        user_answer: data_quality_score,
      });
    }
    if (country !== "") {
      questions.push({
        question: "Country",
        user_answer: country,
      });
    }

    return questions;
  };
  return (
    <div>
      <div className="flex flex-col gap-4 rounded-xl bg-white p-4 border-[#E8E7EA] border-2">
        <h1 className="flex justify-center text-center bg-[#E6F3F4] text-[#8F8F8F] p-2 rounded-xl">
          Financed Emissions = (Value of Bond Investment / Total Government
          Debt) + Country&apos;s Total GHG Emissions
        </h1>
        <table className="w-full">
          <thead className="bg-[#F5F4F5]">
            <tr className="font-bold">
              <th className="text-start p-3 ">Parameter</th>
              <th className="text-start p-3 ">Value</th>
              <th className="text-start p-3 ">AI Assistant</th>
            </tr>
          </thead>
          <tbody>
            <tr className="border border-gray-300">
              <td className="p-3 font-medium border border-gray-300">
                Value of Bond Investment
              </td>
              <td className="p-3 border border-gray-300">
                <input
                  type="text"
                  className="border border-gray-400 rounded-lg p-2 w-full"
                  placeholder="e.g., 500,000"
                  onChange={(e) => setValueOfBondInvestment(e.target.value)}
                  value={value_of_bond_investment}
                />
              </td>
              <td className="p-3 border border-gray-300">Required Input</td>
            </tr>
            <tr className="border border-gray-300">
              <td className="p-3 font-medium border border-gray-300">
                Country
              </td>
              <td className="p-3 border border-gray-300">
                <select
                  className="border border-gray-400 rounded-lg p-2 w-full"
                  value={country}
                  onChange={(e) => setCountry(e.target.value)}
                >
                  {sortedCountries.map((country, index) => (
                    <option key={index} value={country.name.common}>
                      {country.name.common}
                    </option>
                  ))}
                </select>
              </td>
              <td className="p-3 border border-gray-300">Required</td>
            </tr>
            <tr className="border border-gray-300">
              <td className="p-3 font-medium border border-gray-300">
                Total Government Debt
              </td>
              <td className="p-3 border border-gray-300">
                <input
                  disabled={estimate_total_government_debt_loading}
                  type="text"
                  className={"border border-gray-400 rounded-lg p-2 w-full" + (estimate_total_government_debt_loading ? " cursor-not-allowed text-gray-500" : "")}
                  placeholder="e.g., 2,000,000,000,000"
                  onChange={(e) => setTotalGovernmentDebt(e.target.value)}
                  value={total_government_debt}
                />
              </td>
              <td className="p-3 border border-gray-300">
                <button disabled={estimate_total_government_debt_loading} className="flex flex-row items-center justify-center gap-2 rounded-md bg-[#1C889C] text-[#fff] p-2 w-full" onClick={estimate_total_government_debt}>
                  {estimate_total_government_debt_loading ? <Spinner size='24px' /> : <BsStars /> } Estimate
                </button>
              </td>
            </tr>
            <tr className="border border-gray-300">
              <td className="p-3 font-medium border border-gray-300">
                Country&apos;s Total GHG Emissions (tCO2e)
              </td>
              <td className="p-3 border border-gray-300">
                <input
                  disabled={estimate_country_total_ghg_emissions_loading}
                  type="text"
                  className={"border border-gray-400 rounded-lg p-2 w-full" + (estimate_country_total_ghg_emissions_loading ? " cursor-not-allowed text-gray-500" : "")}
                  placeholder="e.g., 450,000,000"
                  onChange={(e) => setCountryTotalGHGEmissions(e.target.value)}
                  value={country_total_ghg_emissions}
                />
              </td>
              <td className="p-3 border border-gray-300">
                <button disabled={estimate_country_total_ghg_emissions_loading} className="flex flex-row items-center justify-center gap-2 rounded-md bg-[#1C889C] text-[#fff] p-2 w-full" onClick={estimate_country_total_ghg_emissions}>
                    {estimate_country_total_ghg_emissions_loading ? <Spinner size='24px' /> : <BsStars /> } Estimate
                </button>
              </td>
            </tr>
            <tr className="border border-gray-300">
              <td className="p-3 font-medium border border-gray-300">
                Emission Source
              </td>
              <td className="p-3 border border-gray-300">
                <select
                  className="border border-gray-400 rounded-lg p-2 w-full"
                  onChange={(e) => setEmissionSource(e.target.value)}
                  value={emission_source}
                >
                  <option>National GHG Inventory</option>
                  <option>Technology</option>
                  <option>Finance</option>
                </select>
              </td>
              <td className="p-3 border border-gray-300">Auto-updates</td>
            </tr>
            <tr className="border border-gray-300">
              <td className="p-3 font-medium border border-gray-300">
                Data Quality Score
              </td>
              <td className="p-3 border border-gray-300">
                <input
                  type="text"
                  className="border border-gray-400 rounded-lg p-2 w-full"
                  placeholder="2 - National GHG Inventory Number"
                  onChange={(e) => setDataQualityScore(e.target.value)}
                  value={data_quality_score}
                />
              </td>
              <td className="p-3 border border-gray-300">Auto-updates</td>
            </tr>
          </tbody>
        </table>

        <button
          disabled={loading}
          className="flex w-full justify-center text-center text-base font-semibold bg-[#07838F] text-[#ffffff] p-2 rounded-lg"
          onClick={handleCalculate}
        >
          {loading ? <Spinner size="24px" /> : "Calculate Financed Emissions"}
        </button>
      </div>
    </div>
  );
};

export default FinancedEmissions;
