import ApiS1Config from "@/Api/apiS1Config";
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import _ from "lodash";
import { notifications, showNotification } from "@mantine/notifications";
import { useNavigate } from "react-router";

const DoubleMaterialityContext = createContext();

const DoubleMaterialityProvider = ({ children }) => {
  const PriorityLevels = { 1: "Low", 2: "Medium", 3: "High", 4: "Top" };
  const ReadinessLevel = {
    1: "Not Started",
    2: "Initial Planning",
    3: "In Development",
    4: "Partially Implemented",
    5: "Fully Implemented",
    6: "Not Applicable",
  };

  const readinessColorMap = {
    "Not Started": {
      bg: "#AB020233",
      border: "#AB0202",
      text: "#AB0202",
    },
    "Not Applicable": {
      bg: "#e0e2e7",
      border: "#667085",
      text: "#667085",
    },
    "Initial Planning": {
      bg: "#e9dff3",
      border: "#9160C1",
      text: "#9160C1",
    },
    "In Development": {
      bg: "#ffeecd",
      border: "#FFAB07",
      text: "#FFAB07",
    },
    "Partially Implemented": {
      bg: "#d4e8fb",
      border: "#298BED",
      text: "#298BED",
    },
    "Fully Implemented": {
      bg: "#e2f6e7",
      border: "#01BD36",
      text: "#01BD36",
    },
  };

  const prioritySelectColorMap = {
    Top: {
      bg: "#AB02024D",
      border: "",
      text: "#AB0202",
    },
    High: {
      bg: "#ffd0b5",
      border: "#ffedca",
      text: "#ff6e1d",
    },
    Medium: {
      bg: "#ffe6b5",
      border: "#00C0A9",
      text: "#ffb932",
    },
    Low: {
      bg: "#c7f1d3",
      border: "#01BD36",
      text: "#01BD36",
    },
  };
  const navigate = useNavigate();
  const [selectScope, setSelectScope] = useState();
  const [postLoading, setPostLoading] = useState(false);
  const [reportLoading, setReportLoading] = useState(false);
  // const [DynamicDataLoading, setDynamicDataLoading] = useState(false);
  const [GetDynamicData, setDynamicData] = useState();
  const [RequestBody, setRequestBody] = useState([]);
  const [DMAData, setDMAData] = useState([]);
  const [SingleMTData, setSingleMTData] = useState([]);
  const [propertyLevelMap, setPropertyLevelMap] = useState([]);
  const [lineData, setLineData] = useState([]);

  const headers = {
    assessmentName: "Double Materiality",
    toolName: "Double Materiality Assessment",
  };
  const getAssessmentData = async () => {
    try {
      const { data } = await ApiS1Config.get("/get_assessment_data", {
        headers,
      });
      setSelectScope(data["Double Materiality Preparedness"]);
    } catch (error) {
      error.response.data.Message === "Company assessment not found"
        ? navigate("/select-assessment")
        : "";
      if (error.response.data.Message === "There is no active assessment") {
        navigate("/doubleMateriality/Reporting");
        showNotification({
          message: (
            <span className="capitalize">{error.response.data.Message}</span>
          ),
          color: "red",
        });
      }
    }
  };
  const postAssessmentData = async (value) => {
    setPostLoading(true);
    try {
      setPostLoading(true);
      const { data } = await ApiS1Config.post("/post_scope", value, {
        headers,
      });
      setPostLoading(false);
      getAssessmentData();
      showNotification({
        message: data.Message,
        color: "green",
        position: "top-right",
      });
    } catch (error) {
      setPostLoading(false);
    }
  };

  useEffect(() => {
    getAssessmentData();
    getAdditionalAssessmentData();
  }, []);
  const getReport = async () => {
    setReportLoading(true);
    try {
      setReportLoading(true);
      const { data } = await ApiS1Config.post(
        "/get_report",
        {},
        {
          headers: {
            assessmentType: "Double Materiality",
            toolName: "Double Materiality Assessment",
          },
        }
      );
      setReportLoading(false);
      navigate("/doubleMateriality/Reporting");
      //console.log(data);
    } catch (error) {
      setReportLoading(false);
      //console.log(error);
    }
  };
  const getDynamicData = async () => {
    try {
      const { data: dynamic } = await ApiS1Config.get("/dynamic_data", {
        headers: {
          assessmentType: "Double Materiality",
          toolName: "Double Materiality Assessment",
        },
      });
      await ApiS1Config.get("/dashboard", {
        headers: { assessmentType: "Double Materiality" },
      })
        .then((response) => response.data.all_assessment_summaries)
        .then((assessmentData) => {
          // setAssessments(assessmentData);
          const data = assessmentData.map((item) => {
            let name = item?.name?.split(" ").slice(0, -1).join(" ");
            const Day = name?.split(" ")[0];
            const Month = name?.split(" ")[1].slice(0, 3);
            // //console.log(Day);
            return {
              Month: item?.total_score,
              date: `${Day} ${Month}`,
            };
          });

          setLineData(data);
        });
      setDynamicData(dynamic);
    } catch (error) {
      //console.log(error);
    }
  };

  const getAdditionalAssessmentData = async () => {
    try {
      const assessmentData = await ApiS1Config.get(
        "/get_additional_assessment_data",
        {
          headers,
        }
      );
      setRequestBody(assessmentData.data["Double Materiality Assessment"]);
      setPropertyLevelMap(assessmentData.data["Priority levels"]);
      setDMAData(
        assessmentData.data["Double Materiality Assessment"].scope.double.topics
      );
      setSingleMTData(
        assessmentData.data["Double Materiality Assessment"].scope.single.topics
      );
    } catch (error) {
      setRequestBody([]);
      setPropertyLevelMap([]);
      setDMAData([]);
    }
  };

  const postAdditionalAssessmentData = useMemo(
    () =>
      _.debounce((updatedData) => {
        ApiS1Config.post(
          "/post_additional_scope",
          { ...updatedData },
          {
            headers,
          }
        )
          .then((res) => {
            notifications.show({
              message: res.data.Message,
            });
            getAdditionalAssessmentData();
          })
          .catch((error) => {
            console.log(error);
            notifications.show({
              message: `Error in submiting`,
            });
          });
      }, 1000),
    []
  );

  const handleScoreInputChange = useCallback(
    (id, field, value,scope) => {
      if(scope == 'single'){
        const updatedData = SingleMTData.map((item) =>
          item.id == id ? { ...item, [field]: Number(value) } : item
        );
        setSingleMTData(updatedData);
        
      }else {
        const updatedData = DMAData.map((item) =>
          item.id == id ? { ...item, [field]: Number(value) } : item
        );
        setDMAData(updatedData);

      }
    },
    [DMAData,SingleMTData]
  );

  const handleComments = (id, field, value,scope) => {
    if(scope == 'single'){
      const updatedData = SingleMTData.map((item) =>
        item.id == id ? { ...item, [field]: value} : item
      );
      setSingleMTData(updatedData);
      
    }else {
      const updatedData = DMAData.map((item) =>
        item.id == id ? { ...item, [field]: value } : item
      );
      setDMAData(updatedData);

    }
  }
  

  const handleSelect = (id,field,value,scope) => {
    if(scope=="double"){
      const updatedData = DMAData.map((item) =>
        item.id == id ? { ...item, [field]: value } : item
      );
      setDMAData(updatedData)
      
    }else {
      const updatedData = SingleMTData.map((item) =>
        item.id == id ? { ...item, [field]: value } : item
      );
      setSingleMTData(updatedData)
    }
  }
  

  // materiality type == double or single
  const handleSubmitChanges = async (scopeName) => {
    const headers = {
      assessmentName: "Double Materiality",
      toolName: "Double Materiality Assessment",
      scopeName: scopeName,
    };

    setPostLoading(true);

    console.log(DMAData[0] ,'DMAData[0] ',);
    console.log( SingleMTData[0] ,' SingleMTData[0] ',);
    
    
    const res = await ApiS1Config.post(
      "/post_additional_scope",
      { topics: scopeName == "double" ? DMAData : SingleMTData },
      {
        headers,
      }
    );
    setPostLoading(false);
    notifications.show({
      message: res.data.Message,
    });
    getAdditionalAssessmentData();
  };

  return (
    <DoubleMaterialityContext.Provider
      value={{
        selectScope,
        DMAData,
        SingleMTData,
        handleScoreInputChange,
        propertyLevelMap,
        PriorityLevels,
        ReadinessLevel,
        readinessColorMap,
        prioritySelectColorMap,
        postAssessmentData,
        postLoading,
        getReport,
        reportLoading,
        getAssessmentData,
        // DynamicDataLoading,
        getDynamicData,
        GetDynamicData,
        lineData,
        handleSubmitChanges,
        handleSelect,
        handleComments
      }}
    >
      {children}
    </DoubleMaterialityContext.Provider>
  );
};

export const useDoubleMateriality = () => useContext(DoubleMaterialityContext);

export default DoubleMaterialityProvider;
