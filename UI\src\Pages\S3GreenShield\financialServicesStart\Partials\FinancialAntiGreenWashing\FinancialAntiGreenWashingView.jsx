import React, { useState } from "react";
// import S3Layout from "@/Layout/S3Layout";

// import useSideBarRoute from "@/hooks/useSideBarRoute";

import { PiExportLight } from "react-icons/pi";
import { IoAddOutline } from "react-icons/io5";
import { FaRegTrashAlt, FaQuestionCircle } from "react-icons/fa";
import { CiFilter } from "react-icons/ci";
import FinAntiTable from "./Components/FinAntiTable";

import { Button, Modal } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import SearchBox from "@/Components/SearchBox";
import ScoringMetrics from "./Components/ScoringMetrics";
import ScoreCard from "./Components/ScoreCard";

export default function FinancialAntiGreenWashingView() {
  // const { greenShieldMenu } = useSideBarRoute();
  const [opened, { open, close }] = useDisclosure(false);

  const [addCategory, setAddCategory] = useState(false);

  return (
    <>
      <Button
        onClick={open}
        variant="transparent"
        mx={0}
        className="text-primary underline hover:text-[#37868F] flex justify-end w-full"
        px={0}
        rightSection={<FaQuestionCircle className="text-lg" />}
      >
        Assessment Scoring System Guide?
      </Button>
      <Modal
        size={"95%"}
        opened={opened}
        onClose={close}
        withCloseButton={false}
      >
        <div className="flex justify-between">
          <ScoringMetrics />
          <ScoreCard />
        </div>
      </Modal>
      <div className="flex flex-wrap justify-between items-center rounded-xl p-2">
        <SearchBox
          className="w-[341px] h-[38px]"
          classNames={{ input: "border-none rounded-lg shadow-sm" }}
        />

        <div className="delete-filter flex flex-wrap gap-6">
          <div className="flex items-center bg-white h-[38px] rounded-lg shadow-sm">
            <CiFilter className="mx-2" />
            <IoAddOutline className="mx-2" />
            <p className="font-semibold mx-2 m-0">Filter</p>
          </div>
          <Button
            className=" w-[105px] h-[38px] text-sm font-bold rounded-lg text-[#ffffff] bg-[#00C0A9] px-1 hover:bg-[#00C0A9]"
            size="md"
          >
            <span className="me-1">
              <PiExportLight className="text-lg" />
            </span>
            <span>Export</span>
          </Button>
        </div>

        {/* <div className="btns flex items-center gap-6">
          <Button
            onClick={() => setAddCategory(true)}
            className="border-[1px] w-[105px] h-[42px] text-sm font-bold rounded-lg bg-secondary-300 px-1"
            size="md"
          >
            <span className="me-1">
              <IoAddOutline className="text-lg" />
            </span>
            <span>Add</span>
          </Button>
        </div> */}
      </div>

      <FinAntiTable addCategory={addCategory} />
    </>
  );
}
