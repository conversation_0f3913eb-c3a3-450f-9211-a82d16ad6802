import S2Layout from "@/Layout/S2Layout";
import React from "react";
import { useTranslation } from "react-i18next";

// icons & imgs
import { CgSandClock } from "react-icons/cg";
const S2ComingSoonView = () => {
  const { t } = useTranslation();
  return (
    <S2Layout>
      <h2 className="flex items-center justify-center gap-3 py-12 text-2xl text-center animate-pulse">
        <CgSandClock /> {t("Coming Soon")}
      </h2>
    </S2Layout>
  );
};

export default S2ComingSoonView;
