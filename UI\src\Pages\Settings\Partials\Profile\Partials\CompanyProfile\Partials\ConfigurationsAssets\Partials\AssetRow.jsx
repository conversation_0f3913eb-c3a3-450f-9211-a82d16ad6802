import React, { useState, useEffect } from "react";
import cx from "clsx";
import { Checkbox, Input, MultiSelect, Select, Table } from "@mantine/core";
import { BiEdit } from "react-icons/bi";
import ApiS2 from "@/Api/apiS2Config";
import { showNotification } from "@mantine/notifications";
import Loading from "@/Components/Loading";
import countries from "world-countries";
import { IoIosArrowDown } from "react-icons/io";

const prioritized = ['United Kingdom', 'United Arab Emirates', 'United States', 'Saudi Arabia', 'Qatar'];

// Sort countries: prioritized first, then the rest
const sortedCountries = [
  ...countries.filter(c => prioritized.includes(c.name.common)),
  ...countries
    .filter(c => !prioritized.includes(c.name.common))
    .sort((a, b) => a.name.common.localeCompare(b.name.common))
];

const AssetRow = ({
  item,
  selection,
  toggleRow,
  fetchAgain,
  assetTypeDrop,
  assetTypeAll,
  itemId,
  setItemId,
}) => {
  const selected = selection.includes(item.id);

  const msg = (txt, color = "green") => {
    showNotification({ message: txt, color: color, timing: 7000 });
  };

  const [isSubmiting, setIsSubmiting] = useState(false);
  const [projects, setProjects] = useState([]);
  const [projectsLoading, setProjectsLoading] = useState(false);
  
  const filteredAssetsIds = item.linked_emission_sources.map(aset=> aset.id);
  const [assetTypeId, setAssetTypeId] = useState(filteredAssetsIds);
  const [selectedProjectIds, setSelectedProjectIds] = useState(item.projectIds || []);
  
  const [formData, setFormData] = useState({
    siteName: item?.siteName,
    assetName: item?.assetName,
    location: item?.location,
    reference: item?.reference,
    notes: item?.notes,
  });

  useEffect(() => {
    const fetchProjects = async () => {
      setProjectsLoading(true);
      try {
        const response = await ApiS2.get('https://portal-auth-main-staging.azurewebsites.net/departments-and-projects');
        const projectsData = response.data?.departmentsAndProjects || [];
        setProjects(projectsData);
      } catch (error) {
        console.error('Error fetching projects:', error);
        msg('Failed to fetch projects data', 'red');
        setProjects([]);
      } finally {
        setProjectsLoading(false);
      }
    };

    fetchProjects();
  }, []);

  const getProjectNames = (projectIds) => {
    if (!projects.length || !projectIds?.length) return [];
    
    return projects
      .filter(project => projectIds.includes(project.id))
      .map(project => project.name || `Project ${project.id}`);
  };

  const getProjectsDropdownData = () => {
    return projects.map(project => ({
      value: project.id.toString(),
      label: project.name || `Project ${project.id}`
    }));
  };

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleProjectChange = (selectedValues) => {
    const projectIds = selectedValues.map(val => parseInt(val));
    setSelectedProjectIds(projectIds);
  };

  const validateFormData = () => {
    for (const [key, value] of Object.entries(formData)) {
      if (key !== "notes" && key !== "reference") {
        if (!value || value.trim().length < 3) {
          msg(`${key} must be at least 3 characters long`, "red");
          return false; // Stop validation if a field is invalid
        }
      }
    }
    return true; // All fields are valid
  };

  const handleAssetType = (value) => {
    //! when updating assettype , we make the user enter asset name
    const assetsItems = assetTypeAll.filter((el) => value.includes(el.asset));    
    const assetsIds = assetsItems.map(el=> el.id);
    setAssetTypeId(assetsIds);
  };

  const submitUpdate = async () => {
    if (formData.location == "") {
      msg(`Country required`, "red");
      return; // Stop validation if a field is invalid
    }
    if (assetTypeId.length == 0) {
      msg(`Emission Source required`, "red");
      return; // Stop validation if a field is invalid
    }
    if (formData.assetName.length < 3) {
      msg(`assetName required at least 3 characters`, "red");
      return; // Stop validation if a field is invalid
    }

    if (validateFormData()) {
      // Proceed with form submission
      let newItem = {
        [item.id]: { 
          ...formData, 
          assetTypeIds: assetTypeId,
          projectIds: selectedProjectIds
        },
      };

      setIsSubmiting(true);
      try {
        let res = await ApiS2.post(`admin/update-company-assets`, newItem);
        setIsSubmiting(false);
        msg(res.data.message);
        fetchAgain();
      } catch (error) {
        setIsSubmiting(false);
        console.log("🚀 ~ updateCustom ~ error:", error);
        msg(error.response.data.message, "red");
      }
    }
  };

  // required
  // site name, asset name location emission source

  let assetsValues = item.linked_emission_sources.map((e,idx)=> <div className="mt-2" key={idx}> {e.emission} </div>);
  let projectNames = getProjectNames(item.projectIds);

  return (
    <Table.Tr
      key={item.id}
      className={`${cx({
        ["bg-[#07838F1A]"]: selected,
      })} text-sm font-bold text-[#626364] text-center`}
    >
      <Table.Td>
        <Checkbox
          checked={selection.includes(item.id)}
          onChange={() => toggleRow(item.id)}
          color="#07838F"
        />
      </Table.Td>
      <Table.Td className="flex justify-center flex-col gap-1 items-center">
        {item.id == itemId ? (
          <div className="flex gap-2 items-center">
            <button
              className="cursor-pointer text-white bg-red-500 rounded-xl h-[30px] w-10 p-1 "
              onClick={() => setItemId(0)}
            >
              X
            </button>
            <button
              disabled={isSubmiting}
              onClick={() => submitUpdate(item)}
              className="bg-primary text-white h-[30px] flex justify-center items-center p-1 rounded-xl"
            >
              Send {isSubmiting && <Loading />}
            </button>
          </div>
        ) : (
          <button
            className="h-[40px] flex justify-center items-center"
            onClick={() => setItemId(item.id)}
            type="button"
          >
            <BiEdit />
          </button>
        )}
      </Table.Td>
      
      {/* Projects and Departments Column */}
      <Table.Td className="w-[200px] pl-9">
        <>
          {item.id == itemId ? (
            <div className="flex items-center justify-center">
              {projectsLoading ? (
                <Loading />
              ) : (
                <MultiSelect
                  placeholder="Select Projects..."
                  data={getProjectsDropdownData()}
                  value={selectedProjectIds.map(id => id.toString())}
                  onChange={handleProjectChange}
                  searchable
                  clearable
                />
              )}
            </div>
          ) : (
            <div className="w-[200px] text-start">
              {projectNames.length > 0 ? (
                projectNames.map((name, idx) => (
                  <div className="mt-2" key={idx}>{name}</div>
                ))
              ) : (
                <div className="text-gray-400">No projects</div>
              )}
            </div>
          )}
        </>
      </Table.Td>

      {/* Existing Emission Sources Column */}
      <Table.Td className="w-[150px] pl-9">
        <>
          {item.id == itemId ? (
            <div className="flex items-center justify-center">
              <MultiSelect
                error={assetTypeId.length == 0}
                placeholder="Pick Emission Source ..."
                data={assetTypeDrop}
                onChange={(e) => handleAssetType(e)}
              />
            </div>
          ) : (
            <div className="w-[200px] text-start">{assetsValues}</div>
          )}
        </>
      </Table.Td>
      
      <Table.Td className="pl-7">
        <Input.Wrapper error={formData.siteName.length < 3 && item.id == itemId  && 'must be at least 3 characters long'}>
          <Input
            className="w-[150px]"
            unstyled={item.id == itemId ? false : true}
            disabled={item.id == itemId ? false : true}
            value={formData.siteName}
            onChange={(e) => handleInputChange("siteName", e.target.value)}
            placeholder="Site Name"
            error={!formData.siteName && "Site Name is required"}
          />
        </Input.Wrapper>
      </Table.Td>
      <Table.Td>
        <Input.Wrapper error={formData.assetName.length < 3 && item.id == itemId  &&  'must be at least 3 characters long'}>
          <Input
            className="w-[150px]"
            unstyled={item.id == itemId ? false : true}
            disabled={item.id == itemId ? false : true}
            value={formData.assetName}
            onChange={(e) => handleInputChange("assetName", e.target.value)}
            placeholder="Asset Name"
            error={!formData.assetName && "Asset Name is required"}
          />
        </Input.Wrapper>
      </Table.Td>
      <Table.Td className="w-[150px]">
        <div className="w-full">
          {" "}
          {item.id == itemId ? (
            <Select
            error={formData.location == ''&& item.id == itemId  && `location is required` }
              defaultValue={formData.location}
              id="Country"
              name="Country"
              comboboxProps={{ withinPortal: true }}
              data={sortedCountries.map((country) => country.name.common)}
              placeholder="Select Country ..."
              searchable
              disabled={item.id == itemId ? false : true}
              rightSection={!item?.location && <IoIosArrowDown />}
              value={formData.location}
              onChange={(e) => handleInputChange("location", e)}
              clearable
            />
          ) : (
            <div className="w-[150px] text-start">{item.location}</div>
          )}
        </div>
      </Table.Td>
      <Table.Td className="w-[120px]">
        <div className="flex items-center justify-center">
          <Input
            className="w-[150px]"
            unstyled={item.id == itemId ? false : true}
            disabled={item.id == itemId ? false : true}
            defaultValue={formData.reference}
            onChange={(e) => handleInputChange("reference", e.target.value)}
          />
        </div>
      </Table.Td>
      <Table.Td className="w-[150px] text-center ">
        <div className="flex items-center text-center justify-center">
          <Input
            className="w-[150px] text-center"
            defaultValue={formData.notes}
            unstyled={item.id == itemId ? false : true}
            disabled={item.id == itemId ? false : true}
            value={formData.notes}
            onChange={(e) => handleInputChange("notes", e.target.value)}
          />
        </div>
      </Table.Td>
    </Table.Tr>
  );
};

export default AssetRow;