/* eslint-disable react-refresh/only-export-components */
import ApiS3 from "@/Api/apiS3";
import { createContext, useCallback, useEffect, useState } from "react";
import { useNavigate } from "react-router";
import { useSearchParams } from "react-router-dom";
import { toast } from "react-toastify";

export const AcademyContext = createContext({
  courses: [],
  setSection: () => {},
  setSearchParams: () => {},
  action: () => {},
  section: 1,
  loading: false,
});

const AcademyContextProvider = ({ children }) => {
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const [searchParams, setSearchParams] = useSearchParams();
  let tab = searchParams.get("tab");
  const [section, setSection] = useState(tab ?? "available");

  useEffect(() => {
    setSection(tab ?? "available"); // Update state when query param changes
  }, [tab]);

  const fetchCourses = useCallback(
    async function fetchCourses() {
      setLoading(true);
      try {
        if (section === "available") {
          const response = await ApiS3.get("/academy/portal");
          if (response.status === 200) {
            setCourses(response.data);
          } else {
            console.log(response.data.message);
          }
        }
        if (section === "courses") {
          const response = await ApiS3.get("/academy/myCourses");
          if (response.status === 200) {
            setCourses(response.data);
          } else {
            console.log(response.data.message);
          }
        }
      } catch (error) {
        console.error("Error fetching courses:", error);
        toast.error("Something went wrong!");
      } finally {
        setLoading(false);
      }
    },
    [section]
  );

  // enroll to Course function
  const Enroll = async (course) => {
    try {
      const response = await ApiS3.get("/academy/portal/enroll/" + course._id);
      if (response.status === 200) {
        toast.success("Enrolled Successfully");
        navigate(`mycourses/${response.data.courseId}`);
      } else {
        toast.error("can't enroll you in this course! please try again later.");
      }
    } catch (error) {
      console.error("Error fetching courses:", error);
    }
  };

  // go to the course Page
  const gotoCourse = (course) => {
    navigate(`mycourses/${course?._id}`);
  };

  useEffect(() => {
    fetchCourses();
  }, [fetchCourses]);

  let action;
  if (section === "available") action = Enroll;
  if (section === "courses") action = gotoCourse;

  const contextValues = {
    courses,
    loading,
    setSection,
    setSearchParams,
    section,
    action,
  };

  return (
    <AcademyContext.Provider value={contextValues}>
      {children}
    </AcademyContext.Provider>
  );
};

export default AcademyContextProvider;
