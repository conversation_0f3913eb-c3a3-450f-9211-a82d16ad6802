import React, { useState } from "react";
import Chart from "./Chart";
import Graph<PERSON>ey from "./GraphKey";
import { data } from "./data";
const ChartPanel = () => {
  const [visibleSeries, setVisibleSeries] = useState({
    Scope1: true,
    Scope2: true,
    Scope3: true,
  });

  const handleSwitchChange = (seriesKey) => {
    setVisibleSeries((prevState) => ({
      ...prevState,
      [seriesKey]: !prevState[seriesKey],
    }));
  };

  return (
    <div className="grid md:grid-cols-3 lg:grid-cols-4 justify-between gap-3">
      <div className="md:col-span-2 lg:col-span-3">
        <Chart
          data={data}
          visibleSeries={visibleSeries}
          onSwitchChange={handleSwitchChange}
        />
      </div>
      <div className="col-span-1">
        <GraphKey
          visibleSeries={visibleSeries}
          onSwitchChange={handleSwitchChange}
        />
      </div>
    </div>
  );
};

export default ChartPanel;
