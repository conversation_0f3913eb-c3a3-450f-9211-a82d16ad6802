import { Group, Text, rem, useMantineTheme } from "@mantine/core";
import { showNotification } from "@mantine/notifications";
import { IconDownload, IconX, IconX as IconXNotification } from "@tabler/icons-react";
import { useRef, useState } from "react";
import { useDropzone } from "react-dropzone";
import { CgFileAdd } from "react-icons/cg";
import { IoCloseCircleSharp } from "react-icons/io5";
import classes from "../Pages/S3GreenShield/financialServicesStart/Partials/ESGIncidentManagement//Partials/AddNewIncident/Partials/UploadFile.module.css";

function UploadFile({ onFilesChange, formik }) {
  const theme = useMantineTheme();
  const openRef = useRef(null);
  const [files, setFiles] = useState([]);

  const { getRootProps, getInputProps, isDragActive, isDragReject, open } =
    useDropzone({
      accept: {
        "application/pdf": [".pdf"],
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
          [".docx"],
        "text/plain": [".txt"],
      },
      maxFiles: 2,
      maxSize: 10 * 1024 ** 2,
      onDrop: (acceptedFiles) => {
        const newFiles = [...files, ...acceptedFiles];
        const totalSize = newFiles.reduce((acc, file) => acc + file.size, 0);

        if (newFiles.length > 2) {
          showNotification({
            title: "Error",
            message: "You can only upload a maximum of two files.",
            color: "red",
            icon: <IconXNotification />,
          });
          return;
        }

        if (totalSize > 10 * 1024 ** 2) {
          showNotification({
            title: "Error",
            message: "Total size of files must not exceed 10MB.",
            color: "red",
            icon: <IconXNotification />,
          });
          return;
        }

        setFiles(newFiles);
        formik.setFieldValue("files", newFiles);
        if (onFilesChange) {
          onFilesChange(newFiles);
        }
      },
    });

  const handleRemoveFile = (e, fileName) => {
    e.preventDefault();
    e.stopPropagation();
    const updatedFiles = files.filter((file) => file.name !== fileName);
    setFiles(updatedFiles);
    if (onFilesChange) {
      onFilesChange(updatedFiles);
    }
  };

  return (
    <>
      <div className={`${classes.wrapper} cursor-pointer`}>
        {
          <div
            {...getRootProps({
              className: `${classes.dropzone} ${isDragActive ? "active" : ""} ${
                isDragReject ? "reject" : ""
              }`,
            })}
            ref={openRef}
          >
            <input {...getInputProps()} />
            <div onClick={open}>
              <Group justify="center">
                {isDragActive && !isDragReject ? (
                  <IconDownload
                    style={{ width: rem(50), height: rem(50) }}
                    color={theme.colors.blue[6]}
                    stroke={1.5}
                  />
                ) : isDragReject ? (
                  <IconX
                    style={{ width: rem(50), height: rem(50) }}
                    color={theme.colors.red[6]}
                    stroke={1.5}
                  />
                ) : (
                  <CgFileAdd
                    className="text-primary"
                    style={{ width: rem(50), height: rem(50) }}
                    stroke={1.5}
                  />
                )}
              </Group>

              <Text
                ta="center"
                fw={500}
                fz="md"
                my="sm"
                className="underline text-slate-400"
              >
                {isDragActive && !isDragReject
                  ? "Drop files here"
                  : isDragReject
                  ? "Invalid file type or size"
                  : "Click to upload"}
              </Text>

              <Text ta="center" fz="sm" mt="xs" c="dimmed">
                <span className="border p-1 rounded-lg me-2 bg-[#05808b53] text-primary">
                  PDF
                </span>
                <span className="border p-1 rounded-lg me-2 bg-[#05808b53] text-primary">
                  DOCX
                </span>
                <span className="border p-1 rounded-lg me-2 bg-[#05808b53] text-primary">
                  TXT
                </span>
                <span className="p-1 bg-transparent border rounded-lg border-primary me-2 text-primary">
                  {">"} 10 MB
                </span>
              </Text>
            </div>
          </div>
        }

        {files.length > 0 && (
          <div className="w-full p-5 mt-4 truncate bg-transparent border rounded-lg border-primary text-primary">
            <ul>
              {files.map((file, index) => (
                <li key={index} className="flex items-center justify-between">
                  <span className="me-1">{index + 1} -</span>
                  <h1
                    style={{
                      maxWidth: "100%",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                    }}
                  >
                    {file.name}
                  </h1>
                  <button
                    onClick={(e) => handleRemoveFile(e, file.name)}
                    className=""
                  >
                    <IoCloseCircleSharp className="ms-2" />
                  </button>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </>
  );
}

export default UploadFile;
