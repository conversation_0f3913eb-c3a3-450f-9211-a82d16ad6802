import {
  Input,
  InputWrapper,
  Select,
  Button,
  Textarea,
  Loader,
  Modal,
} from "@mantine/core";
import { showNotification } from "@mantine/notifications";

import {
  aiTextType,
  ConsequencesData,
  effectivenessLevels,
  Frequency,
  strategies,
  thresholdLevels,
  trends,
} from "./StaticData";
import Apex<PERSON>hart from "../../ESGRiskUniverseAndGovernance/HeatMap/ApexChart";
import { MdKeyboardArrowDown } from "react-icons/md";
import { useEffect, useState } from "react";
import { useDisclosure } from "@mantine/hooks";
import AiApi from "@/Api/aiApiConfig";
import { FaRegCopy } from "react-icons/fa";
import Loading from "@/Components/Loading";
import { ThresholdSelector } from "../Helpers/ThresholdSelect";
import ThresholdBadge from "../Helpers/ThresholdBadge";
import { AIIcon } from "@/assets/icons";

const RiskAssessmentForm = ({
  handleChange,
  formData,
  setIsRiskDone,
  likelihoodLevels,
  impactSeverityData,
  type,
  addRiskFn,
  loading,
  resetRiskAssessment,
}) => {
  const {
    event,
    riskDescription,
    KRI,
    threshold,
    frequency,
    riskOwner,
    division,
    likelihoodId,
    impactSeverity,
    consequences,
    inherentRisk,
    controlEffectiveness,
    riskResponse,
    trend,
    residualRisk,
    thresholdLevel,
  } = formData;

  const [aiText, setAiText] = useState("");
  const [aiLoad, setAiLoad] = useState(false);
  const [opened, { open, close }] = useDisclosure(false);

  const aiFunc = async (type) => {
    if (riskDescription.length < 20) {
      showNotification({ message: `Minimum character 20`, color: "red" });
      return;
    }
    const data = {
      processor: "risk_description",
      resources: {
        prompt: riskDescription,
        output_type: type,
      },
    };
    setAiLoad(true);
    try {
      const res = await AiApi.post(`process_request`, data);
      setAiText(res.data.ai_response);
      setIsRiskDone(true);
      open();
    } catch (er) {
      console.log("🚀 ~ aiFunc ~ er:", er);
    }
    setAiLoad(false);
  };
  const copyFunc = () => {
    navigator.clipboard
      .writeText(aiText)
      .then(() => {
        showNotification({
          message: "Text copied to clipboard!",
          color: "green",
        });
        close();
      })
      .catch(() => {
        showNotification({ message: "Failed to copy text", color: "red" });
      });
  };

  const [inherentBadge, setinherentBadge] = useState("");
  const [residualBadge, setresidualBadge] = useState("");

  const selectedLevel =
    thresholdLevels.find((level) => level.value == inherentBadge) || "";
  const selectedResidual =
    thresholdLevels.find((level) => level.value == residualBadge) || "";

  useEffect(() => {
    switch (true) {
      case inherentRisk >= 1 && inherentRisk <= 3:
        setinherentBadge("low");
        break;
      case inherentRisk >= 4 && inherentRisk <= 9:
        setinherentBadge("medium");
        break;
      case inherentRisk >= 10 && inherentRisk <= 15:
        setinherentBadge("high");
        break;
      case inherentRisk >= 16 && inherentRisk <= 25:
        setinherentBadge("critical");
        break;
      default:
        setinherentBadge("");
    }
  }, [inherentRisk]);

  useEffect(() => {
    switch (true) {
      case residualRisk >= 1 && residualRisk <= 3:
        setresidualBadge("low");
        break;
      case residualRisk >= 4 && residualRisk <= 9:
        setresidualBadge("medium");
        break;
      case residualRisk >= 10 && residualRisk <= 15:
        setresidualBadge("high");
        break;
      case residualRisk >= 16 && residualRisk <= 25:
        setresidualBadge("critical");
        break;
      default:
        setresidualBadge("");
    }
  }, [residualRisk]);

  return (
    <div className="my-10">
      <Modal size={"lg"} opened={opened} onClose={close} title="Ai suggestion">
        <p className="border border-gray-300 rounded-2xl p-2 mb-3">{aiText}</p>

        <Button className="bg-primary" onClick={copyFunc}>
          Copy <FaRegCopy />
        </Button>
      </Modal>
      <h5 className="text-primary font-bold text-center mb-10 lg:text-2xl">
        Risk Assessment
      </h5>

      <div className="relative mb-4 mt-8">
        {/* event input */}
        <div className="grid lg:grid-cols-2 gap-5 mt-5">
          <InputWrapper label="Event">
            <Input
              id="event"
              disabled={type === "view"}
              placeholder="Enter Text"
              value={event}
              onChange={(e) => handleChange("event", e.target.value)}
            />
          </InputWrapper>
        </div>

        <div className="flex justify-between items-center mt-5">
          <label
            htmlFor="risk-decs"
            className="text-base font-medium text-black"
          >
            Risk Description
          </label>
        </div>
        <div className="relative border border-gray-300 min-h-[145px] rounded py-2 bg-white">
          <div
            title="Ai Suggestion"
            disabled={aiLoad}
            className={`absolute right-2 top-2 group`}
          >
            {aiLoad ? (
              <Loader color="#07838F" size="sm" type="dots" />
            ) : (
              <AIIcon className="cursor-pointer" />
            )}
            <div className={`${aiLoad ? 'hidden': 'flex'} gap-1 absolute z-20 top-5 right-0 flex-col opacity-0 group-hover:opacity-100 transition-opacity duration-200`}>
              {aiTextType.map((type, index) => (
                <Button
                  variant="transparent"
                  key={index}
                  onClick={() => aiFunc(type)}
                  className={`base-border p-1 capitalize bg-white hover:bg-primary hover:text-white`}
                >
                  {type}
                </Button>
              ))}
            </div>
          </div>
          <Textarea
            disabled={type === "view"}
            id="risk-decs"
            placeholder="Enter Text"
            value={riskDescription}
            onChange={(e) => handleChange("riskDescription", e.target.value)}
            classNames={{
              root: "w-[96%] min-h-[145px]",
              input: "min-h-[145px] border-none",
            }}
          />
        </div>
      </div>
      <InputWrapper label="KRI">
        <Input
          id="KRI"
          disabled={type === "view"}
          placeholder="Enter KRI"
          value={KRI}
          onChange={(e) => handleChange("KRI", e.target.value)}
        />
      </InputWrapper>

      <div className="grid lg:grid-cols-3 gap-5 mt-5">
        <InputWrapper label="Threshold">
          <Input
            id="Threshold"
            disabled={type === "view"}
            placeholder="Enter Threshold"
            value={threshold}
            onChange={(e) => handleChange("threshold", e.target.value)}
          />
        </InputWrapper>

        <ThresholdSelector
          disabled={type === "view"}
          value={thresholdLevel}
          onChange={(e) => handleChange("thresholdLevel", e)}
          levels={thresholdLevels}
          type={type}
        />
        <Select
          disabled={type === "view"}
          rightSection={<MdKeyboardArrowDown />}
          label="Frequency"
          placeholder="Daily"
          data={Frequency}
          value={frequency}
          onChange={(value) => handleChange("frequency", value)}
        />
      </div>

      <div className="grid lg:grid-cols-2 gap-5 mt-5">
        <InputWrapper label="Risk Owner">
          <Input
            disabled={type === "view"}
            placeholder="Name and Role"
            value={riskOwner}
            onChange={(e) => handleChange("riskOwner", e.target.value)}
          />
        </InputWrapper>

        <InputWrapper label="Division">
          <Input
            disabled={type === "view"}
            placeholder="Enter Text"
            value={division}
            onChange={(e) => handleChange("division", e.target.value)}
          />
        </InputWrapper>

        <Select
          disabled={type === "view"}
          rightSection={<MdKeyboardArrowDown />}
          label="Likelihood"
          placeholder="Very Likely (5)"
          data={likelihoodLevels || []}
          value={likelihoodId}
          onChange={(value) => handleChange("likelihoodId", value)}
        />

        <Select
          disabled={type === "view"}
          rightSection={<MdKeyboardArrowDown />}
          label="Impact Severity"
          placeholder="Critical (5)"
          data={impactSeverityData || []}
          value={impactSeverity}
          onChange={(value) => handleChange("impactSeverity", value)}
        />

        <Select
          disabled={type === "view"}
          rightSection={<MdKeyboardArrowDown />}
          label="Consequences"
          placeholder="Reputational"
          data={ConsequencesData || []}
          value={consequences}
          onChange={(value) => handleChange("consequences", value)}
        />

        <div className="flex flex-col">
          <label className="text-sm font-medium mb-1">Inherent Risk</label>
          <div className="border border-gray-300 rounded px-3 py-1.5 bg-white flex gap-3">
            {inherentRisk || "Automated Calculation"}
            <ThresholdBadge level={selectedLevel} />
          </div>
        </div>

        <Select
          disabled={type === "view"}
          rightSection={<MdKeyboardArrowDown />}
          label="Control Effectiveness"
          placeholder="Moderate (3)"
          data={effectivenessLevels}
          value={controlEffectiveness}
          onChange={(value) => handleChange("controlEffectiveness", value)}
        />

        <div className="flex flex-col">
          <label className="text-sm font-medium mb-1">Residual Risk</label>
          <div className="border border-gray-300 rounded px-3 py-1.5 bg-white flex gap-3">
            {residualRisk || "Automated Calculation"}
            <ThresholdBadge level={selectedResidual} />
          </div>
        </div>

        <Select
          disabled={type === "view"}
          rightSection={<MdKeyboardArrowDown />}
          label="Risk Response"
          placeholder="Mitigate"
          data={strategies}
          value={riskResponse}
          onChange={(value) => handleChange("riskResponse", value)}
        />

        <Select
          disabled={type === "view"}
          rightSection={<MdKeyboardArrowDown />}
          label="Trend"
          placeholder="Increasing , Stable , Decreasing"
          data={trends}
          value={trend}
          onChange={(value) => handleChange("trend", value)}
        />
      </div>

      <ApexChart highlightValue={inherentRisk} />
      <div className="flex gap-5 mt-5">
        {type !== "view" && (
          <Button
            onClick={resetRiskAssessment}
            className="hover:bg-slate-300 flex-1 bg-bg-lite1 text-gray-700 py-2 px-4 text-center h-10 border-r border-gray-300"
          >
            Clear
          </Button>
        )}
        {type !== "view" && (
          <Button
            onClick={addRiskFn}
            className="flex-1 bg-primary hover:bg-green-700 text-white py-2 px-4 text-center h-10"
          >
            Save {loading && <div className="submit-loader" />}
          </Button>
        )}
      </div>
    </div>
  );
};
// add memoization

export default RiskAssessmentForm;
