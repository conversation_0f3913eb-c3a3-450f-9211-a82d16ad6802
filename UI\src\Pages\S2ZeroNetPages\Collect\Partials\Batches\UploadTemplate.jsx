import ApiS2 from "@/Api/apiS2Config";
import Loading from "@/Components/Loading";
import { Button, FileInput, Select, Tooltip } from "@mantine/core";
import { showNotification } from "@mantine/notifications";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { FaChevronDown } from "react-icons/fa";
import { PiExportLight } from "react-icons/pi";
import { IoIosArrowDown, IoMdClose } from "react-icons/io";
import { LuCopyCheck } from "react-icons/lu";

export default function UploadTemplate({
 assetTypeAll,
 getTableData,
 allItemSpecificities,
}) {
 const { t } = useTranslation();
 const [file, setFile] = useState(null);
 const [loading, setLoading] = useState(false);
 const [evidence, setEvidence] = useState([]);
 const [selectedAssetType, setSelectedAssetType] = useState();
 const [selectedLocation, setSelectedLocation] = useState();
 const [selectedSourcePhysical, setSelectedSourcePhysical] = useState();
 const [selectedSourceService, setSelectedSourceService] = useState();

 const handleRemoveEvidence = (indexToRemove) => {
  setEvidence((prevEvidence) =>
   prevEvidence.filter((_, idx) => idx !== indexToRemove)
  );
 };

 const storeFilledTemplate = async () => {
  const filesCollect = {
   batch_file: file,
   evidence_files: evidence,
  };

  const formData = new FormData();
  for (const key in filesCollect) {
   if (filesCollect[key]) {
    formData.append(key, filesCollect[key]);
   }
  }

  try {
   setLoading(true);
   const { data } = await ApiS2.post(
    "/batch_inputs/process_batch_file",
    formData,
    {
     headers: {
      "Content-Type": "multipart/form-data",
      "Asset-Type": selectedAssetType,
      "Location-Specificity": selectedLocation,
      "Source-Service": selectedSourceService,
      "Source-Physical": selectedSourcePhysical,
     },
    }
   );

   getTableData("batch");
   setFile(null);
   setEvidence([]);
   setSelectedAssetType(null);
   setLoading(false);
   showNotification({
    message: "Batch input added successfully",
    color: "green",
   });
  } catch (error) {
   setLoading(false);
   showNotification({
    message: error.response?.data?.message || "An error occurred",
    color: "red",
   });
  }
 };

 useEffect(() => {
  if (!file) {
   setEvidence([]);
  }
 }, [file]);

 const isStep2Complete = !!file;
 const isStep3Complete =
  selectedLocation && selectedSourcePhysical && selectedSourceService;

 return (
  <>
   {/* Step 2: Upload Template */}
   <div className="md:flex justify-between items-start mt-4">
    <p className="font-semibold Fill-Template text-[16px] leading-[24px]">
     <span className="text-secondary-300 capitalize me-1">
      {t("uploadTemplate.step2")}
     </span>
     {t("Add data in the template, save the file, and upload the template.")}
    </p>

    <div>
     <div className="md:flex items-center">
      <Select
       data={assetTypeAll.map((item) => item?.asset)}
       placeholder="Select the Asset Type"
       className="md:me-10 w-60 md:w-72 mx-auto"
       radius={10}
       value={selectedAssetType}
       onChange={(e) => {
        setSelectedAssetType(e);
        setFile(null);
        setEvidence([]);
       }}
       rightSection={<FaChevronDown />}
      />

      <Tooltip
       multiline
       w={220}
       radius="md"
       withArrow
       transitionProps={{ duration: 200 }}
       label={
        !selectedAssetType && (
         <span className="capitalize flex justify-center">
          Please select Asset Type first
         </span>
        )
       }
       className={!selectedAssetType ? "" : "hidden"}
      >
       <label
        htmlFor="upload_File"
        className={`Upload-Completed-Template flex items-center justify-center gap-4 p-2 px-9 text-white shadow-md cursor-pointer rounded-xl mt-5 md:mt-0 ${
         !selectedAssetType
          ? "cursor-not-allowed opacity-20 bg-slate-800"
          : "bg-secondary-300"
        }`}
       >
        <PiExportLight />
        <span>{t("Upload Template")}</span>
       </label>
      </Tooltip>
      <FileInput
       id="upload_File"
       type="file"
       className="hidden"
       value={file}
       onChange={(newFile) => setFile(newFile)}
       accept=".xlsx,.xls"
       disabled={!selectedAssetType}
      />
     </div>

     {/* Upload Evidence */}
     <div className="mt-5 md:flex justify-end">
      <Tooltip
       multiline
       w={220}
       radius="md"
       withArrow
       transitionProps={{ duration: 200 }}
       label={
        !file ? (
         <span className="capitalize flex justify-center">
          Please upload template first
         </span>
        ) : (
         <span className="capitalize">
          You can select multiple files by holding CTRL
         </span>
        )
       }
      >
       <label
        htmlFor="upload_template"
        className={`flex items-center justify-center gap-4 p-2 px-9 text-white shadow-md rounded-xl mt-5 md:mt-0 ${
         !file
          ? "cursor-not-allowed opacity-20 bg-slate-800"
          : "bg-secondary-300"
        }`}
       >
        <PiExportLight />
        <span>{t("Upload Evidence")}</span>
       </label>
      </Tooltip>
      <FileInput
       id="upload_template"
       type="file"
       className="hidden"
       value={evidence}
       onChange={(newFile) => setEvidence(newFile)}
       disabled={!file}
       multiple
       accept=".pdf"
      />
     </div>
    </div>
   </div>

   {/* Uploaded Files Section */}
   {file && (
    <div className="md:flex justify-between items-center mt-5 md:mt-3">
     <div className="bg-[#e7f3f4] md:flex flex-col gap-5 p-5 rounded-lg md:w-1/2">
      <div className="justify-between items-center w-full md:flex">
       <div>
        <h1 className="text-sm font-bold">{file?.name}</h1>
        <p className="font-medium text-sm text-primary">Upload complete</p>
       </div>
       <Button
        className="bg-white text-red-600 rounded-2xl hover:bg-white hover:text-red-600 mt-5 md:mt-0"
        onClick={() => {
         setFile(null);
         setEvidence([]);
        }}
       >
        Remove <IoMdClose className="text-neutral-700" />
       </Button>
      </div>

      {evidence?.map((item, idx) => (
       <div
        className="mt-3 md:flex justify-between items-center w-full"
        key={idx}
       >
        <div>
         <h1 className="text-sm font-bold">{item?.name}</h1>
         <p className="font-medium text-sm text-primary">Upload complete</p>
        </div>
        <Button
         className="bg-white text-red-600 rounded-2xl hover:bg-white hover:text-red-600 mt-5 md:mt-0"
         onClick={() => handleRemoveEvidence(idx)}
        >
         Remove <IoMdClose className="text-neutral-700" />
        </Button>
       </div>
      ))}
     </div>
    </div>
   )}

   <hr className="h-[2px] bg-gray-300 mx-auto mt-3" />

   {/* Step 3: Data Quality Assessment */}
   <div className="md:flex justify-between items-start mt-4">
    <p className="font-semibold text-[16px] leading-[24px]">
     <span className="text-secondary-300 capitalize me-1">{t("Step3 :")}</span>
     {t(
      "Add Location Specificity, Source Physical, Source Service for Data Quality Assessment."
     )}
    </p>
   </div>

   <div className="mt-5">
    <div className="grid grid-cols-1 md:grid-cols-3 gap-5 items-center">
     <Select
      value={selectedLocation}
      onChange={setSelectedLocation}
      rightSection={<IoIosArrowDown />}
      label="Location Specificity"
      radius={10}
      size="md"
      placeholder="Choose"
      data={allItemSpecificities?.location_specificity?.map(
       (item) => item.location
      )}
      clearable
     />
     <Select
      value={selectedSourcePhysical}
      onChange={setSelectedSourcePhysical}
      rightSection={<IoIosArrowDown />}
      label="Source Physical"
      radius={10}
      size="md"
      placeholder="Choose"
      data={allItemSpecificities?.source_physical?.map((item) => item.source)}
     />
     <Select
      value={selectedSourceService}
      onChange={setSelectedSourceService}
      rightSection={<IoIosArrowDown />}
      label="Source Service"
      radius={10}
      size="md"
      placeholder="Choose"
      data={allItemSpecificities?.source_service?.map((item) => item.source)}
     />
    </div>
   </div>

   {/* Submit Button */}
   <div className="md:flex justify-end items-center mt-5 md:mt-3">
    <Tooltip
     multiline
     w={220}
     radius="md"
     withArrow
     transitionProps={{ duration: 200 }}
     label={
      (!isStep2Complete || !isStep3Complete) && (
       <span className="capitalize flex justify-center">
        Please finish {!isStep2Complete && <span className="ms-1">step2</span>}{" "}
        {!isStep3Complete && <span className="ms-1">step3</span>}
       </span>
      )
     }
     hidden={isStep2Complete && isStep3Complete}
    >
     <Button
      className={`px-[62px] flex justify-center gap-1 items-center rounded-xl shadow-md border-2 text-white mx-auto md:mx-0 mt-5 md:mt-0 ${
       !isStep2Complete || !isStep3Complete
        ? "cursor-not-allowed opacity-20 bg-slate-800 hover:bg-slate-800"
        : "bg-primary hover:bg-primary"
      }`}
      size="md"
      onClick={storeFilledTemplate}
      disabled={!isStep2Complete || !isStep3Complete || loading}
     >
      {loading ? (
       <Loading />
      ) : (
       <span className="flex">
        <LuCopyCheck className="me-1" />
        {t("Submit Data")}
       </span>
      )}
     </Button>
    </Tooltip>
   </div>
  </>
 );
}