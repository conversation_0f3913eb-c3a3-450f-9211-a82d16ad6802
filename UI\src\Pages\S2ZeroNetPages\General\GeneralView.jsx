import S2Layout from "@/Layout/S2Layout";
import ApiS2 from "@/Api/apiS2Config";
import useRoutes from "@/Routes/useRoutes";
import { RecommendationsDecisionSighStartIcon } from "@/assets/svg/ImageSVG";
import { Bar<PERSON>hart } from "@mantine/charts";
import { Button, Menu, MenuItem } from "@mantine/core";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { FaCalendarAlt } from "react-icons/fa";
import { FaArrowDown, FaArrowUp } from "react-icons/fa6";
import Awards from "./Partials/Awards";
import Card from "./Partials/Card";
import { useDisclosure } from "@mantine/hooks";
import AIEstimationModal from "./Partials/AIEstimationModal";
import {
    CollectIconColord,
    CollectIconUnColord,
    DefaultColoredIcon,
    DefaultUnColoredIcon,
    MeasureIconColord,
    MeasureIconUnColord,
    ReportIconColord,
    ReportIconUnColord,
    ReviewIconColord,
    ReviewIconUncolord,
} from "@/assets/icons/EmissionCalculationIcons";
import CollectView from "../Collect/CollectView";
import ReviewView from "../Review/ReviewView";
import MeasureView from "../Measure/MeasureView";
import ReportView from "../NetZeroReport/ReportView";
import DataValidation from "../DataValidation/DataValidationView";
import { IoMdHome } from "react-icons/io";
import GuideModalButton from "@/Components/Guide/GuideModalButton";
import EmissionGuide from "./EmissionGuide";
import CollectGuide from "./CollectGuide";

const GeneralView = () => {
    const [tabs, setTabs] = useState(0);
    const tabsNames = [
        {
            name: "Emission Overview",
            id: 0,
            colordIcon: <DefaultColoredIcon />,
            unColoredIcon: <DefaultUnColoredIcon />,
        },
        {
            name: "Collect",
            id: 1,
            colordIcon: <CollectIconColord />,
            unColoredIcon: <CollectIconUnColord />,
        },
        {
            name: "Review",
            id: 2,
            colordIcon: <ReviewIconColord />,
            unColoredIcon: <ReviewIconUncolord />,
        },
        {
            name: "Measure",
            id: 3,
            colordIcon: <MeasureIconColord />,
            unColoredIcon: <MeasureIconUnColord />,
        },
        {
            name: "Report",
            id: 5,
            colordIcon: <ReportIconColord />,
            unColoredIcon: <ReportIconUnColord />,
        },
    ];

    const [barData, setBarData] = useState([]);
    const [netZeroData, setNetZeroData] = useState(null);
    const [availableDates, setAvailableDates] = useState([]); // List of all available dates
    const [selectedDates, setSelectedDates] = useState([]); // User-selected dates
    const { getStart } = useRoutes();
    const [sources, setSources] = useState([]); // Store the original assets data
    const { t } = useTranslation();
    const [opened, { open, close }] = useDisclosure(false);
    const greenShades = [
        "#dcfce7",
        "#bbf7d0",
        "#86efac",
        "#4ade80",
        "#22c55e",
        "#16a34a",
        "#15803d",
        "#166534",
        "#14532d",
        "#052e16",
    ];
    // Extract asset names for consistent color mapping
    const assetNames =
        barData.length > 0
            ? Object.keys(barData[0]).filter((key) => key !== "month")
            : [];

    const fetchData = useCallback(async () => {
        try {
            const { data } = await ApiS2.get("/carbon-factors/get-charts-data");

            if (data.netzero_overview) {
                setNetZeroData(data.netzero_overview);
            }

            // Extract all unique dates from assets
            const allDates = Array.from(
                new Set(
                    data.sources_breakdown.flatMap((asset) =>
                        Object.keys(asset.monthly_emissions)
                    )
                )
            );

            setAvailableDates(allDates);
            setSelectedDates(allDates.slice(0, 2)); // Default to the first two dates
            setSources(data.sources_breakdown); // Store the original data

            // Transform data into chart format
            transformChartData(data.sources_breakdown, allDates.slice(0, 2));
        } catch (error) {
            console.error(
                "Error fetching data:",
                error.response || error.message
            );
        }
    }, []);
    useEffect(() => {
        fetchData();
    }, []);

    const getArrowIcon = (percent) => {
        if (percent > 0) return <FaArrowUp className="text-[#70D162]" />;
        if (percent < 0) return <FaArrowDown className="text-red-500" />;
        return "";
    };

    const transformChartData = (sources, selectedDates) => {
        const transformedData = selectedDates.map((date) => {
            let entry = { month: date };
            sources.forEach(({ asset, monthly_emissions }) => {
                entry[asset] = monthly_emissions[date] || 0; // Default to 0 if missing
            });
            return entry;
        });

        setBarData(transformedData);
    };

    const handleDateSelection = (date) => {
        let updatedDates = selectedDates.includes(date)
            ? selectedDates.filter((d) => d !== date) // Remove if already selected
            : [...selectedDates, date]; // Add new date

        setSelectedDates(updatedDates);
        transformChartData(sources, updatedDates); // Use original sources instead of barData
    };

    return (
        <S2Layout
            navbarTitle="Emissions Calculation"
            breadcrumbItems={[
                { title: <IoMdHome size={20}/>, href: getStart.path },
                { title: "Emissions Calculation", href: "#" },
            ]}
        >
            <main className="min-h-screen w-full max-w-none px-2 sm:px-2 lg:px-4 xl:px-6">
                <div
                    className="flex flex-wrap gap-2 sm:gap-4 lg:gap-6 xl:gap-10 mb-5 justify-left"
                    role="tablist"
                >
                    {tabsNames.map((tab, idx) => (
                        <div key={tab.id} className="flex items-center">
                            <button
                                className={`p-2 rounded-xl flex items-center gap-1 sm:gap-2 font-semibold transition-colors duration-200 text-xs sm:text-sm lg:text-base
                  ${tab.id === tabs ? "bg-white" : "hover:bg-gray-100"}
                  `}
                                onClick={() => setTabs(tab.id)}
                                role="tab"
                                aria-selected={tab.id === tabs}
                                aria-controls={`tabpanel-${tab.id}`}
                            >
                                <div className="flex-shrink-0">
                                    {tab.id == tabs
                                        ? tab.colordIcon
                                        : tab.unColoredIcon}
                                </div>
                                <span
                                    className={`${
                                        tab.id === tabs ? "gradient-text" : ""
                                    } whitespace-nowrap`}
                                >
                                    {tab.name}
                                </span>
                            </button>
                            {idx !== tabsNames.length - 1 && (
                                <span className="hidden lg:block w-[1px] h-6 bg-[#1C889C] mx-2 xl:mx-4"></span>
                            )}
                        </div>
                    ))}
                </div>

                {tabs == 0 && (
                    <>
                        <div className="w-full justify-between flex items-center py-5 px-3">
                            <GuideModalButton buttonText="Emission Overview Guide">
                                <EmissionGuide />
                            </GuideModalButton>
                        </div>
                        <div className="w-full max-w-8xl mx-auto grid grid-cols-1 gap-4 lg:gap-5 lg:grid-cols-12 items-start px-2 sm:px-2">
                            <Card
                                className="lg:col-span-12 my-4 lg:my-7"
                                cardTitle="Awards"
                                viewAllLink="#"
                            >
                                <Awards />
                            </Card>

                            {/* Emissions Breakdown Card*/}
                            <Card
                                className="my-4 lg:my-6 bg-white shadow-md lg:col-span-7 xl:col-span-7 rounded-xl w-full"
                                cardTitle="Emissions Breakdown"
                                viewAllLink="#"
                            >
                                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 px-4 mb-4">
                                    <h3 className="text-base lg:text-lg font-semibold">
                                        Emission Data
                                    </h3>

                                    <Menu>
                                        <Menu.Target>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="w-full sm:w-auto"
                                            >
                                                <FaCalendarAlt className="mr-2" />{" "}
                                                Select Dates
                                            </Button>
                                        </Menu.Target>
                                        <Menu.Dropdown>
                                            {availableDates.map((date) => (
                                                <MenuItem
                                                    key={date}
                                                    onClick={() =>
                                                        handleDateSelection(
                                                            date
                                                        )
                                                    }
                                                    className={
                                                        selectedDates.includes(
                                                            date
                                                        )
                                                            ? "font-bold text-blue-600"
                                                            : ""
                                                    }
                                                >
                                                    {date}
                                                </MenuItem>
                                            ))}
                                        </Menu.Dropdown>
                                    </Menu>
                                </div>

                                <div className="flex flex-col xl:flex-row gap-4 w-full">
                                    {/* Chart Section */}
                                    <div className="flex-1 min-w-0 overflow-hidden flex justify-center">
                                        {barData.length > 0 ? (
                                            <div className="w-full max-w-full">
                                                <BarChart
                                                    h={400}
                                                    data={barData}
                                                    dataKey="month"
                                                    type="stacked"
                                                    series={assetNames.map(
                                                        (name, index) => ({
                                                            name,
                                                            color: greenShades[
                                                                index %
                                                                    greenShades.length
                                                            ],
                                                        })
                                                    )}
                                                    className="w-full"
                                                />
                                            </div>
                                        ) : (
                                            <p className="text-center">
                                                Loading chart data...
                                            </p>
                                        )}
                                    </div>

                                    {/* Legend Section */}
                                    <div className="xl:w-1/4 xl:min-w-[200px] flex flex-col justify-start items-center xl:items-start">
                                        <h4 className="text-base lg:text-lg font-bold mb-3 text-center xl:text-left">
                                            Assets
                                        </h4>
                                        <div className="grid grid-cols-2 sm:grid-cols-3 xl:grid-cols-1 gap-2 w-full justify-items-center xl:justify-items-start">
                                            {barData.length > 0 &&
                                                Object.keys(barData[0])
                                                    .filter(
                                                        (key) => key !== "month"
                                                    )
                                                    .map((name, index) => (
                                                        <div
                                                            key={index}
                                                            className="flex items-center gap-2 justify-center xl:justify-start"
                                                        >
                                                            <span
                                                                className="inline-block w-3 h-3 lg:w-4 lg:h-4 rounded-full flex-shrink-0"
                                                                style={{
                                                                    backgroundColor:
                                                                        greenShades[
                                                                            index %
                                                                                greenShades.length
                                                                        ],
                                                                }}
                                                            ></span>
                                                            <span className="text-xs lg:text-sm truncate text-center xl:text-left">
                                                                {name}
                                                            </span>
                                                        </div>
                                                    ))}
                                        </div>
                                    </div>
                                </div>
                            </Card>

                            {/* Emissions Overview Card */}
                            <Card
                                className="my-4 lg:my-6 lg:col-span-5 xl:col-span-5 rounded-xl"
                                cardTitle="Emissions Overview"
                                viewAllLink="#"
                            >
                                <div className="bg-white flex justify-center py-4 lg:py-5 rounded-lg">
                                    <Button
                                        className="text-sm lg:text-base text-white bg-gradient-to-r from-[#2C5A8C] via-[#1C889C] to-[#13B1A8] hover:bg-[#e6f3f4] hover:text-white rounded-lg border-0 px-4 py-2"
                                        size="md"
                                        onClick={open}
                                    >
                                        <div className="flex items-center justify-center gap-2">
                                            <RecommendationsDecisionSighStartIcon />
                                            <span>
                                                {t("Generate AI Estimation")}
                                            </span>
                                        </div>
                                    </Button>
                                </div>

                                <div className="bg-white flex w-full flex-col p-3 lg:p-4 overflow-hidden mt-3 rounded-lg shadow-md space-y-4">
                                    {/* Current Net Zero Overview */}
                                    <div className="flex flex-col items-center border-b pb-4">
                                        <h1 className="font-bold text-lg lg:text-xl text-center mb-4">
                                            Current Net Zero Overview
                                        </h1>
                                        <div className="flex flex-col sm:flex-row w-full justify-center sm:justify-between gap-4 max-w-md sm:max-w-none mx-auto">
                                            <div className="flex flex-col items-center">
                                                <p className="text-lg lg:text-2xl">
                                                    CO2 e
                                                </p>
                                                <p className="font-bold text-2xl lg:text-3xl text-[#07838F]">
                                                    {netZeroData
                                                        ? netZeroData.difference.toLocaleString()
                                                        : "Loading..."}
                                                </p>
                                            </div>
                                            <div className="flex flex-col items-center">
                                                <p className="text-lg lg:text-2xl font-bold text-center">
                                                    From last month
                                                </p>
                                                <p className="flex flex-row items-center justify-center font-bold text-2xl lg:text-3xl text-[#70D162]">
                                                    {netZeroData
                                                        ? getArrowIcon(
                                                              netZeroData.percent
                                                          )
                                                        : "Loading..."}
                                                    <span className="ml-1">
                                                        {netZeroData
                                                            ? Math.abs(
                                                                  netZeroData.percent
                                                              ) + "%"
                                                            : ""}
                                                    </span>
                                                </p>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Monthly Net Zero Target */}
                                    <div className="flex flex-col items-center">
                                        <h1 className="font-bold text-lg lg:text-xl text-center mb-4">
                                            Monthly Net Zero Target
                                        </h1>
                                        <div className="flex flex-col sm:flex-row w-full justify-center sm:justify-between gap-4 max-w-md sm:max-w-none mx-auto">
                                            <div className="flex flex-col items-center">
                                                <p className="text-lg lg:text-2xl">
                                                    CO2 e
                                                </p>
                                                <p className="font-bold text-2xl lg:text-3xl text-[#07838F]">
                                                    412,314
                                                </p>
                                            </div>
                                            <div className="flex flex-col items-center">
                                                <p className="text-lg lg:text-2xl font-bold text-center">
                                                    From last month
                                                </p>
                                                <p className="flex flex-row items-center justify-center font-bold text-2xl lg:text-3xl text-[#70D162]">
                                                    <FaArrowUp />
                                                    <span className="ml-1">
                                                        34.3%
                                                    </span>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </Card>
                        </div>
                        <AIEstimationModal close={close} opened={opened} />
                    </>
                )}

                {tabs == 1 && (
                    <>
                        <div className="w-full justify-between flex items-center py-5 px-3">
                            <GuideModalButton buttonText="Emission Overview Guide">
                                <CollectGuide />
                            </GuideModalButton>
                        </div>
                        <CollectView />
                    </>
                )}

                {tabs == 2 && <ReviewView />}

                {tabs == 3 && <MeasureView target="emissions-overview" />}

                {tabs == 4 && <DataValidation />}

                {tabs == 5 && <ReportView />}
            </main>
        </S2Layout>
    );
};

export default GeneralView;
