export const pdfUrlS2 =
 "https://wwwsa.blob.core.windows.net/s2-evidence/1_1_22Oct2024_10-33-16-98_output.pdf";

export const btnStyle =
 "border-[1px] flex items-center px-4 justify-center w-[195px]   h-[56px] text-[17px] font-bold rounded-lg text-secondary-300 border-secondary-300 bg-white hover:text-white hover:bg-secondary-300";

export const linkStyle =
 "border-[1px] flex items-center px-4 justify-between w-[195px] h-[56px] text-sm font-bold rounded-lg text-white bg-secondary-300 ease-out duration-200 hover:translate-y-2 hover:bg-secondary-400";
export function formatNumber(value) {
 if (value >= 1_000_000_000_000) {
  return (value / 1_000_000_000_000)?.toFixed(2) + "T";
 } else if (value >= 1_000_000_000) {
  return (value / 1_000_000_000)?.toFixed(2) + "B"; 
 } else if (value >= 1_000_000) {
  return (value / 1_000_000)?.toFixed(2) + "M";
 } else if (value >= 1_000) {
  return (value / 1_000)?.toFixed(2) + "K";
 }
 return value?.toFixed(2);
}
