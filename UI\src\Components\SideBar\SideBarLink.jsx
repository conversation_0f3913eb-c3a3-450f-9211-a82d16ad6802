import { useAuth } from "@/Contexts/AuthContext";
import { Link, useLocation, useSearchParams } from "react-router-dom";
import Swal from "sweetalert2";

export default function SideBarLink({ title, url, systemName, S1Name }) {
  const { CompanyAccess, S1CurrentAssessment ,superAdmin} = useAuth();

  const { pathname } = useLocation();
  const [searchParams] = useSearchParams();
  const activeTab = searchParams.get("tab");
  const hasAccess = (systemName) =>
    systemName
      ? Object.values(CompanyAccess || {}).some(
          (accessGroup) => accessGroup[systemName]
        )
      : true;

  const hasCurrentAssessment = (S1Name) =>
    S1Name ? S1CurrentAssessment?.[S1Name] === true : true;

  const handleClick = (S1Name, systemName) => {
    if (hasAccess(systemName) && S1Name && !hasCurrentAssessment(S1Name)) {
      Swal.fire({
        title: "Error!",
        text: "You have not started this assessment yet, please start it from the Dashboard.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };
  const accessible = hasAccess(systemName);
  const assessmentStarted = hasCurrentAssessment(S1Name);

  const canNavigate = accessible && assessmentStarted;

  // Determine active state based on pathname and optional tab
  let isActive;
  if (activeTab) {
    isActive = url.includes(`tab=${activeTab}`);
  } else {
    isActive = pathname.includes(url);
  }

  return (
    <>
    {
      !superAdmin && title == 'Your GreenHub' ?

      ''
      : 
      <Link
        to={canNavigate ? url : "#"}
        className={`pl-1 mt-3 w-full flex sm:flex-col group-hover:flex-row text-[#6F6F6F] 
          transition-all py-[0.6rem] text-xs rounded-lg
          ${
            accessible
              ? "cursor-pointer hover:bg-[#e7f3f4]  dark:hover:bg-[#2c2c2c] hover:text-primary font-semibold"
              : "cursor-not-allowed"
          } 
          ${
            isActive ? "text-primary font-bold bg-[#e7f3f4] " : "text-primary"
          }`}
      onClick={() => handleClick(S1Name, systemName)}
      title={title}
    >
      <span>{title}</span>
    </Link>
    }
    </>
  );
}
