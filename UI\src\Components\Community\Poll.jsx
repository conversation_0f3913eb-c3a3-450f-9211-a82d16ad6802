import ApiS3 from '@/Api/apiS3';
import { useAuth } from '@/Contexts/AuthContext';
import { useCallback, useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const Poll = ({ options, questionId }) => {
  const [hasUserVoted, setHasUserVoted] = useState(false);
  const { user } = useAuth();
  const [updatedOptions, setUpdatedOptions] = useState(options);

  const allvoters = updatedOptions?.reduce((percentage, option) => {
    return percentage + option?.voters?.length;
  }, 0);

  const checkIfUserVoted = useCallback(() => {
    if (user) {
      setHasUserVoted(updatedOptions.some((opt) => opt.voters.includes(user.userId.toString())));
    }
  }, [user, updatedOptions]);

  async function handleVotePoll(option) {
    try {
      if (hasUserVoted) return toast.error('You already voted before');

      const response = await ApiS3.post(`/community/questions/poll/vote/${questionId}`, option);

      if (response.status === 201) {
        // Update the options to reflect the new vote count
        setUpdatedOptions((prevOptions) =>
          prevOptions.map((opt) => (opt._id === option._id ? { ...opt, voters: [...opt.voters, user.userId.toString()] } : opt))
        );
        checkIfUserVoted(); // Explicitly check if the user has voted
      }
    } catch (error) {
      toast.error('Please try again later');
      console.log(error);
    }
  }

  useEffect(() => {
    checkIfUserVoted();
  }, [checkIfUserVoted]); // Re-run checkIfUserVoted if user or options change

  return (
    <div className="">
      {updatedOptions.map((option) => {
        const percentage = (option?.voters?.length / allvoters) * 100;
        return (
          <div className="flex flex-col my-4" key={option._id}>
            <div className="text-xl leading-none flex justify-between items-center my-2">
              <div className="flex items-center gap-4">
                {!hasUserVoted && (
                  <button
                    onClick={() => handleVotePoll({ optionText: option.text, _id: option._id })}
                    className={`border-2 rounded-full size-4 outline outline-2 border-white outline-offset-2 transition-all duration-200 ${
                      option.voters.includes(user?.userId?.toString())
                        ? 'bg-primary outline-primary'
                        : ' hover:bg-primary hover:outline-primary outline-gray-200'
                    }`}
                  ></button>
                )}
                <p>{option.text}</p>
              </div>
              {hasUserVoted && (
                <p>
                  {option.voters.length} / {allvoters}
                </p>
              )}
            </div>
            <div className="bg-teal-500/20 h-3 rounded my-2 w-full">
              {hasUserVoted && (
                <div
                  className="bg-primary h-full rounded"
                  style={{
                    width: `${percentage}%`,
                  }}
                ></div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default Poll;
