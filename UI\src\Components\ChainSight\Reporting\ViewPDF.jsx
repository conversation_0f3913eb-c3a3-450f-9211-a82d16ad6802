import { useEffect, useState } from "react";
import { Modal } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { useTranslation } from "react-i18next";
import { MdOutlineRemoveRedEye } from "react-icons/md";
import Loading from "@/Components/Loading";
import { Worker, Viewer } from "@react-pdf-viewer/core";
import { defaultLayoutPlugin } from "@react-pdf-viewer/default-layout";
import "@react-pdf-viewer/core/lib/styles/index.css";
import "@react-pdf-viewer/default-layout/lib/styles/index.css";
import Cookies from "js-cookie";

const ViewPDF = ({ pdfUrl, btnStyle, text, disabled }) => {
  const { t } = useTranslation();
  const defaultLayoutPluginInstance = defaultLayoutPlugin();
  const [opened, { open, close }] = useDisclosure(false);
  const [isBlocked, setIsBlocked] = useState(false);
  const [pdfData, setPdfData] = useState(null); // Store Blob URL
  const [errorMessage, setErrorMessage] = useState("");

  useEffect(() => {
    const fetchPDF = async () => {
      if (!pdfUrl) {
        console.error("No PDF URL provided");
        setErrorMessage("No PDF URL provided. Please ensure the report is available.");
        setIsBlocked(true);
        return;
      }

      const token = Cookies.get("level_user_token");
      if (!token) {
        console.error("No authorization token found");
        setErrorMessage("Please log in to view the PDF.");
        setIsBlocked(true);
        return;
      }

      try {
        const response = await fetch(pdfUrl, {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          const text = await response.text();
          console.error("Response content:", text);
          setErrorMessage(`Failed to fetch PDF (Status: ${response.status}). Please try again or contact support.`);
          throw new Error("Failed to fetch PDF");
        }

        const contentType = response.headers.get("content-type");

        // Accept both application/pdf and application/octet-stream
        if (!contentType.includes("application/pdf") && !contentType.includes("application/octet-stream")) {
          console.error("Unexpected content type:", contentType);
          const text = await response.text();
          console.error("Response content:", text);
          setErrorMessage("Received invalid PDF data. Please contact support.");
          throw new Error("Response is not a PDF");
        }

        // Verify PDF content
        const blob = await response.blob();
        const arrayBuffer = await blob.arrayBuffer();
        const uint8Array = new Uint8Array(arrayBuffer.slice(0, 5));
        const isPDF = uint8Array[0] === 37 && uint8Array[1] === 80 && uint8Array[2] === 68 && uint8Array[3] === 70; // %PDF
        if (!isPDF) {
          console.error("Response is not a valid PDF");
          setErrorMessage("Received invalid PDF data. Please contact support.");
          throw new Error("Response is not a valid PDF");
        }

        // Create a Blob URL
        const blobUrl = window.URL.createObjectURL(blob);
        setPdfData(blobUrl);
        setIsBlocked(false);

        // Clean up Blob URL when component unmounts or pdfUrl changes
        return () => {
          window.URL.revokeObjectURL(blobUrl);
        };
      } catch (error) {
        console.error("Error loading PDF:", error);
        setErrorMessage("An error occurred while loading the PDF. Please check your session or contact support.");
        setIsBlocked(true);
      }
    };

    fetchPDF();
  }, [pdfUrl]);

  return (
    <>
      <button onClick={open} className={btnStyle} disabled={disabled}>
        <span>
          <MdOutlineRemoveRedEye className="text-2xl ms-1 mr-2" />
        </span>
        <span>{t(text)}</span>
      </button>
      <Modal opened={opened} onClose={close} size={"95%"} centered>
        {isBlocked ? (
          <div className="error-message">
            <p>{t(errorMessage || "There seems to be an issue displaying the PDF. Please check if you have a valid session or contact support if the issue persists.")}</p>
          </div>
        ) : !pdfData ? (
          <Loading />
        ) : (
          <Worker workerUrl="https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js">
            <Viewer
              fileUrl={pdfData} // Pass Blob URL
              plugins={[defaultLayoutPluginInstance]}
            />
          </Worker>
        )}
      </Modal>
    </>
  );
};

export default ViewPDF;