import React from "react";
const PublicNavbar = ({ navbarTitle }) => {
  const renderHead = () => {
    if (navbarTitle) {
      return (
        <p className="text-lg sm:text-2xl md:text-2xl lg:text-3xl xl:text-4xl font-bold text-white">
          {navbarTitle}
        </p>
      );
    } else {
      return (
        <p className="">Transform your company’s path to sustainability</p>
      );
    }
  };

  return (
    <>
      <div
        className="flex justify-between relative items-center mb-2.5 bg-primary text-gray-200 shadow rounded-md px-6 py-3 z-50 h-20"
        data-aos="fade-down"
      >
        <div>{renderHead()}</div>
      </div>
    </>
  );
};

export default PublicNavbar;
