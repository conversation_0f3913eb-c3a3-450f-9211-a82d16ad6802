// AssesmentPage.jsx
import { useEffect } from "react";
import { useAssessmentStore } from "../AssessmentContext";
import Section from "../components/Section";
import Guide from "../components/AssessmentGuide";
import { Loader, Progress, Text } from "@mantine/core";
import { TbDownload, TbHistory } from "react-icons/tb";
import { FaRegEye } from "react-icons/fa";
import { FaRegEye as FaRegEye6 } from "react-icons/fa6";
import {
    IoArrowDownOutline,
    IoShareSocial,
    IoShareSocialOutline,
} from "react-icons/io5";
import GuideModalButton from "@/Components/Guide/GuideModalButton";

export default function AssesmentPage() {
    const {
        assessment,
        loading,
        error,
        completedSections,
        fetchAssessment,
        fetchCompanyUsers,
    } = useAssessmentStore();

    useEffect(() => {
        fetchAssessment();
        fetchCompanyUsers();
    }, []);

    const getProgressPercentage = () => {
        return assessment?.sections?.length > 0
            ? (completedSections / assessment.sections.length) * 100
            : 0;
    };

    return (
        <div className="flex flex-col w-full h-full overflow-hidden overflow-y-auto">
            {loading ? (
                <div className="w-full flex justify-center items-center h-[80vh]">
                    <Loader __size="24" />
                </div>
            ) : error ? (
                <div className="flex justify-center items-center w-full h-[80vh] text-red-600/40 font-semibold text-4xl">
                    {error}
                </div>
            ) : (
                <div className="w-full flex flex-col gap-8">
                    {/* Progress bar and action buttons */}
                    <div className="w-full flex flex-col py-6 px-8 gap-6 bg-white rounded-2xl">
                        <div className="w-full justify-between flex items-center py-5 px-3">
                            <GuideModalButton buttonText="Assessment Guide">
                                <Guide />
                            </GuideModalButton>
                        </div>
                        <div className="flex items-center w-full gap-2">
                            <Progress
                                w="100%"
                                value={getProgressPercentage()}
                                radius="xl"
                                size="lg"
                                classNames={{
                                    root: "bg-[#C7DDE0] rounded-md",
                                    section: "bg-[#07838F] rounded-md",
                                }}
                            />
                            <Text c="#939393" fz="sm" fw={600} w="84">
                                {completedSections} /{" "}
                                {assessment.sections.length} Steps
                            </Text>
                        </div>
                        {/* Action buttons */}
                        {/*
                        <div className="flex items-center justify-between w-full">
                            <button className="px-4 py-2 rounded-xl bg-secondary-300 hover:bg-primary text-white transition-all duration-200 cursor-pointer font-semibold text-xl flex items-center gap-2 group">
                                <span className="transition-all duration-50 hover:duration-200 group-hover:rotate-[360deg]">
                                    <TbHistory size={24} />
                                </span>{" "}
                                Assessment History
                            </button>
                            <div className="flex items-center gap-6">
                                <button className="px-4 py-2 rounded-xl bg-transparent border-2 border-primary text-primary hover:bg-primary hover:text-white transition-all duration-200 cursor-pointer font-semibold text-xl flex items-center gap-2 group">
                                    <div className="w-6 relative">
                                        <span className="transition-all duration-150 hover:duration-300 group-hover:scale-105 opacity-100 group-hover:opacity-0 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                                            <FaRegEye size={24} />
                                        </span>
                                        <span className="transition-all duration-150 hover:duration-300 group-hover:scale-105 opacity-0 group-hover:opacity-100 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                                            <FaRegEye6 size={24} />
                                        </span>
                                    </div>{" "}
                                    View Report
                                </button>
                                <button className="px-4 py-2 rounded-xl bg-transparent border-2 border-primary text-primary hover:bg-primary hover:text-white transition-all duration-200 cursor-pointer font-semibold text-xl flex items-center gap-2 group">
                                    <div className="w-6 relative">
                                        <span className="transition-all duration-150 hover:duration-300 group-hover:scale-105 opacity-100 group-hover:opacity-0 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                                            <IoShareSocial size={24} />
                                        </span>
                                        <span className="transition-all duration-150 hover:duration-300 group-hover:scale-105 opacity-0 group-hover:opacity-100 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                                            <IoShareSocialOutline size={24} />
                                        </span>
                                    </div>{" "}
                                    Share
                                </button>
                                <button className="px-4 py-2 rounded-xl bg-primary border-2 border-primary hover:border-secondary-300 hover:bg-secondary-300 text-white transition-all duration-200 cursor-pointer font-semibold text-xl flex items-center gap-2 group">
                                    <div className="w-6 relative">
                                        <span className="transition-all duration-150 hover:duration-300 group-hover:translate-y-[calc(-50%+2px)] opacity-100 group-hover:opacity-0 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                                            <TbDownload size={24} />
                                        </span>
                                        <span className="transition-all duration-150 hover:duration-300 group-hover:translate-y-[calc(-50%+2px)] opacity-0 group-hover:opacity-100 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                                            <IoArrowDownOutline size={24} />
                                        </span>
                                    </div>{" "}
                                    Download Report
                                </button>
                            </div>
                        </div>
                        */}
                    </div>

                    {/* Sections */}
                    <div className="flex flex-col gap-6 bg-white p-6 rounded-xl">
                        {assessment.sections.map((section, index) => (
                            <Section key={index} section={section} />
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
}
