import { Button } from "@mantine/core";
import { PiTimer } from "react-icons/pi";
import { useDisclosure } from "@mantine/hooks";
import { FiDownload } from "react-icons/fi";
import Cookies from "js-cookie";
import ViewPDF from "./ViewPDF";
import HistoryPopup from "./HistoryPopup";
import Share from "./Share";

const History = ({ latestReport }) => {
  const PDFLink = latestReport?.report_url;
  const [historyOpened, { open: historyOpen, close: historyClose }] =
    useDisclosure(false);

  const handleDownloadPDF = async (PDFLink) => {
    if (!PDFLink) {
      console.error("No report URL available");
      return;
    }

    // Fetch the PDF as a binary blob
    const response = await fetch(PDFLink, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${Cookies.get("level_user_token")}`,
      },
    });
    if (!response.ok) {
      console.error("Failed to download report");
      return;
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.style.display = "none";
    a.href = url;
    a.download = `Sustainable_Procurement_Evaluation.pdf`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    a.remove();
  };

  return (
    <div className="flex justify-between p-6 bg-white rounded-lg border-2 border-[#E8E7EA] mb-6">
      <Button
        w={220}
        onClick={historyOpen}
        leftSection={<PiTimer size={16} />}
        className="text-[#07838F] hover:bg-[#07838F] border border-[#E8E7EA] bg-white rounded-lg"
      >
        Assessment History
      </Button>
      {historyOpened && (
        <HistoryPopup opened={historyOpened} close={historyClose} />
      )}
      <ViewPDF
        btnStyle="text-[#07838F] hover:text-white w-[220px] flex justify-center items-center hover:bg-[#07838F] border border-[#E8E7EA] bg-white rounded-lg"
        pdfUrl={PDFLink}
        text={"Show Report"}
        disabled={!PDFLink && true}
      />
      <Share
        link={PDFLink}
        btnStyle={
          PDFLink
            ? "text-[#07838F] hover:text-white w-[220px] flex justify-center items-center hover:bg-[#07838F] border border-[#E8E7EA] bg-white rounded-lg"
            : "text-[#07838F] hover:text-white w-[220px] flex justify-center items-center hover:bg-[#07838F] border border-[#E8E7EA] bg-white rounded-lg cursor-not-allowed"
        }
        disabled={!PDFLink && true}
      />
      <Button
        color="#07838F"
        w={220}
        onClick={() => {
          handleDownloadPDF(PDFLink);
        }}
        className="rounded-lg"
        leftSection={<FiDownload size={16} />}
        download
      >
        Download Report
      </Button>
    </div>
  );
};
export default History;
