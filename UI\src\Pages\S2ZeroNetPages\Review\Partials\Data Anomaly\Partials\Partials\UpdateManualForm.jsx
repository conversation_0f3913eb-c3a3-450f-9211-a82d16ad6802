import ApiS2 from "@/Api/apiS2Config";
import Loading from "@/Components/Loading";
import { Button, Select, TextInput } from "@mantine/core";
import { DateInput, YearPickerInput } from "@mantine/dates";
import { isNotEmpty, useForm } from "@mantine/form";
import { showNotification } from "@mantine/notifications";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { FaCalendarDay } from "react-icons/fa";
import { FaRegCircleCheck } from "react-icons/fa6";
import { IoIosArrowDown } from "react-icons/io";
export default function UpdateManualForm({
 allItemSpecificities,
 dataCFByAT,
 loading,
 AssetsError,
 onClose,
 data,
 assetType,
 getTableData,
 setSelectedItems,
 selectedItemsId,
 CompanyAssetAll,
 companyAssetDrop,
 closePopup,
}) {
 const [companyAssetSelected, setCompanyAssetSelected] = useState();
 const [submitLoading, setSubmitLoading] = useState(false);
 const [selectedActivity, setSelectedActivity] = useState(null);
 const [selectedEFactors, setSelectedEFactors] = useState(null);
 const [selectedUom, setSelectedUom] = useState(null);
 //  const [additionalPayload, setAdditionalPayload] = useState({});
 const [dateValue, setDateValue] = useState(null);
 const [startDateValue, setStartDateValue] = useState(null);
 const [endDateValue, setEndDateValue] = useState(null);
 const [tempData, setTempData] = useState([]);

 //console.log(data);
 //  //console.log(tempData);

 useEffect(() => {
  let tempData = dataCFByAT || [];

  if (selectedActivity) {
   tempData = tempData.filter((item) => item.activity === selectedActivity);
  }

  if (selectedEFactors) {
   if (selectedEFactors !== "No Options") {
    tempData = tempData.filter((item) => item.eFactors === selectedEFactors);
   }
  }

  if (selectedUom) {
   if (selectedUom !== "No Options") {
    tempData = tempData.filter((item) => item.uom === selectedUom);
   }
  }
  // //console.log(tempData);

  setTempData(tempData);
 }, [dataCFByAT, selectedActivity, selectedEFactors, selectedUom]);

 // From Core
 const form = useForm({
  mode: "uncontrolled",
  initialValues: {
   fromDate: "",
   toDate: "",
   // assetsType: "",
   assets: "",
   activity: "",
   eFactors: "",
   uom: "",
   quantity: "",
   reportingYear: "",
   source_physical: "",
   source_service: "",
   location_specificity: "",
  },
  validate: {
   fromDate: isNotEmpty("Start Date is required"),
   toDate: isNotEmpty("End Date is required"),
   // assetsType: isNotEmpty("Assets Type is required"),
   assets: isNotEmpty("Assets is required"),
   activity: isNotEmpty("Activity is required"),
   quantity: isNotEmpty("quantity is required"),
   reportingYear: isNotEmpty("Reporting Years required"),
   source_physical: isNotEmpty("Source Physical required"),
   source_service: isNotEmpty("Source Service required"),
   location_specificity: isNotEmpty("Location Specificity required"),
  },
 });
 // //console.log(additionalPayload);
 const handledFormSubmit = async (value) => {
  // //console.log(value);

  try {
   setSubmitLoading(true);
   const companyAssetID = CompanyAssetAll.filter(
    (item) => item.assetName === value.assets
   )[0]?.id;

   // const assetTypeId = assetTypeAll.filter(
   //   (item) => item.asset === value.assetsType
   // )[0]?.id;
   const obj = {
    [data?.items[selectedItemsId]?.id]: {
     customFactorId: tempData[0]?.id,
     companyAssetId: companyAssetID,
     quantity: value.quantity,
     reportingYear: value.reportingYear,
     toDate: value.toDate,
     fromDate: value.fromDate,
     SourcePhysicalId:
      (value.source_physical &&
       allItemSpecificities.source_physical.find(
        (item) => item.source === value.source_physical
       )?.id) ||
      "",
     SourceServiceId:
      (value.source_service &&
       allItemSpecificities.source_service.find(
        (item) => item.source === value.source_service
       )?.id) ||
      "",
     LocationSpecificityId:
      (value.location_specificity &&
       allItemSpecificities.location_specificity.find(
        (item) => item.location === value.location_specificity
       )?.id) ||
      "",
    },
   };
   //console.log(obj);
   ApiS2.put("/batch_inputs/update_inputs", obj)
    .then(({ data }) => {
     setSubmitLoading(false);
     getTableData("manual");
     getTableData("batch");
     data?.inputType === "batch" && closePopup();
     showNotification({
      message: "Form Data Added Successfully",
      color: "green",
     });
     onClose();
     form.reset();
     restAllInputs();
     //  //console.log(data);
    })
    .catch((error) => {
     setSubmitLoading(false);
     //console.log(error);
     showNotification({
      message: error.response.data.message,
      color: "red",
     });
    });
  } catch (error) {
   setSubmitLoading(false);
   //console.log(error);
   showNotification({
    message: error.response.data.error,
    color: "red",
   });
  }
 };

 // rest All Inputs Fun
 const restAllInputs = () => {
  setSelectedActivity(null);
  setSelectedEFactors(null);
  setSelectedUom(null);
  setDateValue(null);
  setStartDateValue(null);
  setEndDateValue(null);
 };
 // form.setValues useEffect
 const getSpecificity = (items, id, key) =>
  (id && items?.find((item) => item?.id === id)?.[key]) || "";

 const formatDate = (date) => (date ? dayjs(date).format("DD/MM/YYYY") : "");

 useEffect(() => {
  const selectedItem = data?.items[selectedItemsId] || {};

  form.setValues({
   assets: companyAssetSelected,
   activity: selectedActivity === "No Options" ? null : selectedActivity,
   eFactors: selectedEFactors === "No Options" ? null : selectedEFactors,
   uom: selectedUom === "No Options" ? null : selectedUom || null,
   reportingYear: dateValue?.getFullYear() || "",
   fromDate: (startDateValue && formatDate(startDateValue)) || "",
   toDate: (endDateValue && formatDate(endDateValue)) || "",
   quantity: selectedItem.quantity || null,
   source_physical: getSpecificity(
    allItemSpecificities?.source_physical,
    selectedItem.SourcePhysicalId,
    "source"
   ),
   source_service: getSpecificity(
    allItemSpecificities?.source_service,
    selectedItem?.SourceServiceId,
    "source"
   ),
   location_specificity: getSpecificity(
    allItemSpecificities?.location_specificity,
    selectedItem?.LocationSpecificityId,
    "location"
   ),
  });
 }, [
  companyAssetSelected,
  selectedActivity,
  selectedEFactors,
  selectedUom,
  dateValue,
  startDateValue,
  endDateValue,
  data,
 ]);
 useEffect(() => {
  const selectedItem = data?.items[selectedItemsId] || {};

  setStartDateValue(new Date(selectedItem.fromDate) || null);
  setEndDateValue(new Date(selectedItem.toDate) || null);
  setCompanyAssetSelected(selectedItem.companyAsset?.assetName || null);
  setSelectedActivity(selectedItem.customFactor?.activity || null);
  setSelectedEFactors(selectedItem.customFactor?.eFactors || null);
  setSelectedUom(selectedItem.customFactor?.uom || null);
  setDateValue(
   selectedItem.reportingYear
    ? new Date(parseInt(selectedItem.reportingYear, 10), 0, 1)
    : null
  );
 }, [data]);
 const { t } = useTranslation();
 // //console.log(data);

 return (
  <>
   {loading ? (
    <Loading />
   ) : AssetsError ? (
    <h1 className="capitalize text-center">{AssetsError}</h1>
   ) : (
    <form
     className="w-full px-10 py-4 bg-white  rounded-xl"
     onSubmit={form.onSubmit(handledFormSubmit)}
    >
     <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 sm:gap-x-5 md:gap-x-10 lg:gap-x-28 gap-y-10 text-start">
      <DateInput
       {...form.getInputProps("fromDate")}
       key={form.key("fromDate")}
       label="From"
       placeholder="DD/MM/YYYY"
       valueFormat="DD/MM/YYYY"
       rightSection={<FaCalendarDay />}
       value={
        startDateValue || new Date(data?.items[selectedItemsId]?.fromDate)
       }
       onChange={setStartDateValue}
       radius={10}
       size="md"
      />
      <DateInput
       {...form.getInputProps("toDate")}
       key={form.key("toDate")}
       label="To"
       placeholder="DD/MM/YYYY"
       valueFormat="DD/MM/YYYY"
       value={endDateValue}
       onChange={setEndDateValue}
       rightSection={<FaCalendarDay />}
       radius={10}
       size="md"
      />
      <TextInput
       label="Emission Source"
       placeholder="Choose"
       defaultValue={assetType?.asset}
       radius={10}
       size="md"
       readOnly
      />
      <Select
       {...form.getInputProps("assets")}
       key={form.key("assets")}
       onChange={setCompanyAssetSelected}
       label="Assets"
       // disabled={!assetTypeSelected}
       placeholder="Choose"
       data={companyAssetDrop}
       defaultSearchValue={companyAssetSelected}
       radius={10}
       size="md"
       rightSection={<IoIosArrowDown />}
      />

      <Select
       {...form.getInputProps("activity")}
       key={form.key("activity")}
       label="Activity"
       radius={10}
       placeholder="Choose"
       rightSection={<IoIosArrowDown />}
       onChange={(e) => {
        setSelectedActivity(e);
        setSelectedEFactors(null);
        setSelectedUom(null);
        // setAdditionalPayload({});
       }}
       data={
        dataCFByAT
         ? [...new Set((dataCFByAT || []).map((item) => item.activity))]
         : []
       }
       value={selectedActivity}
      />

      <Select
       {...form.getInputProps("eFactors")}
       key={form.key("eFactors")}
       label="E-Factors"
       placeholder="Choose"
       rightSection={<IoIosArrowDown />}
       radius={10}
       onChange={(e) => {
        setSelectedEFactors(e);
        setSelectedUom(null);
       }}
       data={
        selectedActivity
         ? (dataCFByAT || [])
            .filter((item) => item.activity === selectedActivity)
            .map((item) => ({
             value: item.eFactors || "No Options",
             label: item.eFactors || "No Options",
            }))
            .filter(
             (option, index, self) =>
              index === self.findIndex((o) => o.value === option.value)
            ) // Filter out duplicates
         : [
            {
             value: "No Options",
             label: "No Options",
            },
           ]
       }
       disabled={!selectedActivity}
      />

      <Select
       {...form.getInputProps("uom")}
       key={form.key("uom")}
       rightSection={<IoIosArrowDown />}
       label="UOM"
       radius={10}
       placeholder="Choose"
       onChange={setSelectedUom}
       value={selectedUom}
       data={
        selectedActivity && selectedEFactors
         ? (dataCFByAT || [])
            .filter(
             (item) =>
              item.activity === selectedActivity ||
              item.eFactors === selectedEFactors
            )
            .map((item) => ({
             value: item.uom || "No Options",
             label: item.uom || "No Options",
            }))
            .filter(
             (option, index, self) =>
              index === self.findIndex((o) => o.value === option.value)
            ) // Remove duplicates
            .concat(
             (dataCFByAT || []).some(
              (item) =>
               item.activity === selectedActivity ||
               item.eFactors === selectedEFactors
             )
              ? []
              : [
                 {
                  value: "No Options",
                  label: "No Options",
                 },
                ]
            )
         : [
            {
             value: "No Options",
             label: "No Options",
            },
           ]
       }
       disabled={!selectedActivity || !selectedEFactors}
      />

      <TextInput
       // rightSection={" "}
       {...form.getInputProps("quantity")}
       key={form.key("quantity")}
       label="Quantity"
       placeholder="Enter Quantity..."
       radius={10}
       size="md"
      />
      <YearPickerInput
       {...form.getInputProps("reportingYear")}
       key={form.key("reportingYear")}
       label="Reporting Year"
       placeholder="Enter Year..."
       value={dateValue}
       onChange={(date) => {
        setDateValue(date); // Store the year in state
       }}
       radius={10}
       size="md"
      />

      <Select
       {...form.getInputProps("location_specificity")}
       key={form.key("location_specificity")}
       rightSection={<IoIosArrowDown />}
       label="Location Specificity"
       radius={10}
       size="md"
       placeholder="Choose"
       data={allItemSpecificities?.location_specificity?.map(
        (item) => item.location
       )}
       clearable
      />
      <Select
       {...form.getInputProps("source_physical")}
       key={form.key("source_physical")}
       rightSection={<IoIosArrowDown />}
       label="Source Physical"
       radius={10}
       size="md"
       placeholder="Choose"
       data={allItemSpecificities?.source_physical?.map((item) => item.source)}
      />
      <Select
       {...form.getInputProps("source_service")}
       key={form.key("source_service")}
       rightSection={<IoIosArrowDown />}
       label="Source Service"
       radius={10}
       size="md"
       placeholder="Choose"
       // onChange={setSelectedUom}
       // value={selectedUom}
       data={allItemSpecificities?.source_service?.map((item) => item.source)}
      />

      <div></div>
      <Button
       onClick={() => {
        onClose();
        // setSelectedRow(null);
        setSelectedItems(null);
       }}
       color=""
       radius={10}
       size="md"
       className="w-full bg-red-700 hover:bg-red-700"
      >
       Cancel
      </Button>
      <Button
       type={"submit"}
       disabled={submitLoading}
       color=""
       radius={10}
       size="md"
       className="w-full bg-[#00C0A9] hover:bg-[#00C0A9] text-white"
      >
       {submitLoading ? (
        <Loading />
       ) : (
        <span className="flex items-center ">
         <FaRegCircleCheck className="me-1" /> {t("Confirm")}
        </span>
       )}
      </Button>
     </div>
    </form>
   )}
  </>
 );
}
