import { Button, Progress } from "@mantine/core";

const SelectFormBtn = ({ item, selected, setSelected }) => {
  return (
    <Button
      onClick={() => setSelected(item.id)}
      variant="default"
      unstyled
    >
      <div
        className={`mb-5 flex items-center gap-3 animate-3 rounded-xl h-16 overflow-hidden pr-6 ${
          item.id == selected ? "shadow-md bg-[#56A1A91A] " : "bg-white"
        } `}
      >
        <div
          className={`bg-primary h-0 animate-3 ${
            item.id == selected ? "w-2 h-full" : "w-0"
          } `}
        ></div>
        <div
          className={`full-center rounded-full h-11 w-11 bg-bg-lite1 ${
            selected == item.id ? "bg-white" : "ml-4"
          } `}
        >
          {item.id == selected ? (
            item.sIcon
          ) : (
            <img width={30} src={item.imgSrc} alt={`${item.title} icon`} />
          )}
        </div>
        <h5 className="font-bold flex-1">{item.title}</h5>
      </div>
      <Progress value={item.progress} color="#05808B" />
    </Button>
  );
};

export default SelectFormBtn;
