import React from "react";
import cx from "clsx";
import { useState } from "react";
import {
  Table,
  Checkbox,
  ScrollArea,
  Group,
  Avatar,
  Text,
  rem,
} from "@mantine/core";
import { MdKeyboardArrowLeft } from "react-icons/md";
import { Md<PERSON>eyboardArrowRight } from "react-icons/md";
import { RiDeleteBin6Line } from "react-icons/ri";
import { FaArrowDown } from "react-icons/fa";
import { useTranslation } from "react-i18next";

const data = [
  {
    id: "1",
    Question:
      "Have you mapped out your entire value chain (customers, employees, suppliers, local communities)?",
    Financial_Materiality: "Not Started",
    Impact_Materiality: "Not Started",
  },
  {
    id: "2",
    Question:
      "Do you have methods in place to gather stakeholder feedback across the value chain?",
    Financial_Materiality: "Initial Planning",
    Impact_Materiality: "Initial Planning",
  },
  {
    id: "3",
    Question:
      "Can you identify the key areas where your operations impact stakeholders in the value chain?",
    Financial_Materiality: "Partially Implemented",
    Impact_Materiality: "Partially Implemented",
  },
];

export default function CSRDStakeholderTable() {
  const { t } = useTranslation();
  const [selection, setSelection] = useState(["1"]);
  const toggleRow = (id) =>
    setSelection((current) =>
      current.includes(id)
        ? current.filter((item) => item !== id)
        : [...current, id]
    );
  const toggleAll = () =>
    setSelection((current) =>
      current.length === data.length ? [] : data.map((item) => item.id)
    );

  const rows = data.map((item) => {
    const selected = selection.includes(item.id);

    return (
      <Table.Tr
        key={item.id}
        className={`${cx({
          ["bg-[#07838F1A]"]: selected,
        })} text-sm font-bold text-[#667085] text-center`}
      >
        <Table.Td>
          <Checkbox
            checked={selection.includes(item.id)}
            onChange={() => toggleRow(item.id)}
            color="#00C0A9"
          />
        </Table.Td>
        <Table.Td className="text-start">{item.Question}</Table.Td>
        <Table.Td className="text-start">{item.Financial_Materiality}</Table.Td>
        <Table.Td className="text-start">{item.Impact_Materiality}</Table.Td>
      </Table.Tr>
    );
  });

  return (
    <ScrollArea className="w-full py-5 bg-white rounded-lg">
      <Table miw={800} verticalSpacing="md">
        <Table.Thead className="border-b-2 pb-6 text-[#667085] font-extrabold text-center">
          <Table.Tr>
            <Table.Th style={{ width: "40px" }}>
              <Checkbox
                onChange={toggleAll}
                checked={selection.length === data.length}
                indeterminate={
                  selection.length > 0 && selection.length !== data.length
                }
                color="#00C0A9"
              />
            </Table.Th>
            <Table.Th className="w-1/3 text-center lg:w-1/2">
              {t("question")}
            </Table.Th>
            <Table.Th className="text-start">
              <h1 className="flex items-center justify-start">
                {t("financialMateriality")}
                <FaArrowDown className="ms-1 text-[#00C0A9]" />
              </h1>
            </Table.Th>
            <Table.Th className="text-start">
              <h1 className="flex items-center justify-start">
                {t("impactMateriality")}
                <FaArrowDown className="ms-1 text-[#00C0A9]" />
              </h1>
            </Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>{rows}</Table.Tbody>
      </Table>
    </ScrollArea>
  );
}
