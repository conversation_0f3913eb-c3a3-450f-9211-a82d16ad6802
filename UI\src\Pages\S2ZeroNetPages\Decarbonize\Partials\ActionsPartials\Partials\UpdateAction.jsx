import React, { useState } from "react";
import {
  Modal,
  Button,
  Select,
  Input,
  InputWrapper,
  Radio,
  Group,
  MultiSelect,
} from "@mantine/core";
import { YearPickerInput } from "@mantine/dates";
import { useTranslation } from "react-i18next";
import { isNotEmpty, useForm } from "@mantine/form";
import ApiS2 from "@/Api/apiS2Config";
import Loading from "@/Components/Loading";

import { showNotification } from "@mantine/notifications";
import { useDisclosure } from "@mantine/hooks";
import { MdEdit } from "react-icons/md";

const UpdateAction = ({ action, assetTypes, refetchAgain, allAssingees }) => {
  const { t } = useTranslation();

  const [opened, { open, close }] = useDisclosure(false);

  const categories = assetTypes?.map((categ) => {
    const c = { value: `${categ.id}`, label: categ.asset };
    return c;
  });

  const currentCategory = categories.find(
    (c) => c.label == action?.asset_type?.asset
  )?.value;

  const [actionCatege, setActionCatege] = useState(currentCategory);

  const [name, setname] = useState(action.name);
  const [yearFrom, setYearFrom] = useState(action.yearFrom);
  const [yearTo, setYearTo] = useState(action.yearTo);
  const [status, setStatus] = useState(action.status);
  const [reductionValue, setReductionValue] = useState(action.reductionValue);

  const u = allAssingees
    .filter((user) => action?.userIds?.includes(parseInt(user?.value)))
    .map((u) => u.value);
  const [assignees, setAssignees] = useState(u);
  const [selectedAssignees, setSelectedAssignees] = useState([]);

  const [submit, setSubmit] = useState(false);

  const form = useForm({
    mode: "uncontrolled",
    initialValues: {
      name: action.name,
      // description: "",
    },
    validate: {
      name: isNotEmpty("title is required"),
    },
  });

  const addTargetFunc = async (values) => {
    let d = {
      name: values.name,
      // description: values.description,
      yearFrom: yearFrom,
      yearTo: yearTo,
      status: status,
      reductionValue: reductionValue,
      assetTypeId: actionCatege,
      assignees: selectedAssignees,
    };

    if (yearFrom == "" || yearTo == "") {
      showNotification({
        message: "date is required",
        color: "red",
        position: "top-center",
      });
      return;
    }

    setSubmit(true);
    try {
      const { data } = await ApiS2.put(
        `/decarbonize/update_climate_action`,
        d,
        {
          headers: {
            'action-id': action.id
          }
        }
      );
      showNotification({
        message: "Form Data Added Successfully",
        color: "green",
      });
      refetchAgain();
    } catch (er) {
      showNotification({
        message: "Error happend",
        color: "red",
      });
      //console.log(er);
    }
    setSubmit(false);
  };

  const handleYearFrom = (d) => {
    let da = d.getFullYear();
    setYearFrom(da);
  };

  const handleYearTo = (d) => {
    let da = d.getFullYear();
    setYearTo(da);
  };

  return (
    <div>
      <button onClick={open}>
        <MdEdit className="w-6 h-6 p-1.5 bg-sky-500 text-blue-700 rounded-full" />
      </button>

      <Modal opened={opened} onClose={close} title={t("Update")}>
        <form onSubmit={form.onSubmit(addTargetFunc)}>
          <div>
            <InputWrapper label="Action Title" className="py-3">
              <Input
                value={name}
                placeholder=""
                {...form.getInputProps("name")}
                key={form.key("name")}
              />
            </InputWrapper>

            <Select
              onChange={(e) => setActionCatege(e)}
              classNames={{ label: "py-2" }}
              label="Action category"
              placeholder="Pick value"
              value={actionCatege}
              data={categories}
            />
          </div>

          <Radio.Group className="py-4" name="favoriteFramework" label="Status">
            <Group mt="xs">
              <Radio
                onClick={(e) => {
                  //console.log(e.target);

                  setStatus(1);
                }}
                color="teal.9"
                value="Not planned"
                label="Not planned"
              />
              <Radio
                onClick={() => setStatus(2)}
                color="teal.9"
                value="Planned"
                label="Planned"
              />
              <Radio
                onClick={() => setStatus(5)}
                color="teal.9"
                value="Completed"
                label="Completed"
              />
              <Radio
                onClick={() => setStatus(3)}
                color="teal.9"
                value="In progress"
                label="In progress"
              />
              <Radio
                onClick={() => setStatus(4)}
                color="teal.9"
                value="On hold"
                label="On hold"
              />
            </Group>
          </Radio.Group>

          <Input.Wrapper label={t("targetsModal.emissionReductionLabel")}>
            <Input
              rightSection="%"
              value={reductionValue}
              type="number"
              onChange={(e) => setReductionValue(e.target.value)}
            />
            <Input.Description className="py-2">
              {t("targetsModal.emissionReductionDescription")}
            </Input.Description>
          </Input.Wrapper>

          <div className="grid grid-cols-1 gap-4 mt-4 sm:grid-cols-2 pb-3">
            <div>
              <YearPickerInput
                // value={action.yearForm}
                label="Start date"
                placeholder="Start date"
                onChange={handleYearFrom}
              />
            </div>
            <div>
              <YearPickerInput
                // value={action.yearTo}
                label="Due date"
                placeholder="Due date"
                onChange={handleYearTo}
              />
            </div>
          </div>
          <MultiSelect
            onChange={(e) => {
              setSelectedAssignees(e.map(Number));
              setAssignees((p) => e);
            }}
            value={assignees}
            label="Assignees"
            placeholder="Pick value"
            data={allAssingees}
          />
          <div className="flex justify-end py-5">
            {submit ? (
              <Loading />
            ) : (
              <Button
                type="submit"
                variant="filled"
                size="sm"
                radius="md"
                className="mt-auto ms-2 mb-2 bg-primary hover:bg-secondary"
                // onClick={open}
              >
                Update Action
              </Button>
            )}
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default UpdateAction;
