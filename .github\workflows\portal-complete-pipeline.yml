name: <PERSON>uild and Push Docker Image of Portal

on:
  push:
    branches:
      - staging
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to use'
        required: true
        default: 'staging'


jobs:
  build_and_push_staging:
    if: github.event_name == 'push'
    name: Build and Push Docker Image for staging
    runs-on: ubuntu-latest
    environment: 
      name: Staging 
  
    steps:
    - name: Checkout repository
      uses: actions/checkout@v2

    - name: Login to Azure Container Registry
      uses: azure/docker-login@v1
      with:
        login-server: levelupportal.azurecr.io
        username: ${{ secrets.LEVELUPPORTAL }}
        password: ${{ secrets.LEVELUPPORTALPASS }}

    - name: Build Docker image
      run: |
        docker build -t portal-github-actions ./UI

    - name: Tag Docker image
      run: |
        docker tag portal-github-actions levelupportal.azurecr.io/portal-github-actions:staging

    - name: Push Docker image to Azure Container Registry
      run: |
        docker push levelupportal.azurecr.io/portal-github-actions:staging

  build_and_push_prod:
    name: Build and Push Docker Image for production
    needs: [build_and_push_staging]
    runs-on: ubuntu-latest
    environment: 
        name: Production 
  
    steps:
    - name: Checkout repository
      uses: actions/checkout@v2

    - name: Login to Azure Container Registry
      uses: azure/docker-login@v1
      with:
        login-server: levelupportal.azurecr.io
        username: ${{ secrets.LEVELUPPORTAL }}
        password: ${{ secrets.LEVELUPPORTALPASS }}

    - name: Build Docker image
      run: |
        docker build -t portal-github-actions ./UI

    - name: Tag Docker image
      run: |
        docker tag portal-github-actions levelupportal.azurecr.io/portal-github-actions:production

    - name: Push Docker image to Azure Container Registry
      run: |
        docker push levelupportal.azurecr.io/portal-github-actions:production
