import { Button } from "@mantine/core";
import React from "react";

export default function MainSection({ icon, ButtonName ,setShowUSerProfile}) {
  return (
    <>
      <div className="bg-gradient-to-tr from-[#03E2C8] to-[#07838F] h-screen rounded-lg flex justify-center items-center flex-col">
        <div className="p-8 border-4 border-[#ffffff5a] rounded-full flex justify-center">
          {icon}
        </div>
        <div className="flex justify-center mt-5">
          <Button
            className=" bg-transparent border border-white rounded-lg hover:bg-transparent px-10"
            size="lg" onClick={setShowUSerProfile}
          >
            {ButtonName}
          </Button>
        </div>
      </div>
    </>
  );
}
