import { useEffect, useState } from "react";
import Fundamentals from "./Fundamentals";
import { useLocation, useNavigate } from "react-router-dom";
import ApiSustain360 from "@/Api/apiSustain360";
import Loading from "@/Components/Loading";
import { Button } from "@mantine/core";
import { showNotification } from "@mantine/notifications";
import { useAuth } from "@/Contexts/AuthContext";
// import { useDisclosure } from '@mantine/hooks';
import Swal from "sweetalert2";
import { useSustain360Store } from "@/Store/Assessment/useSustain360Store";
import { t } from "i18next";

function Summary({ setActive }) {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const [dataSummary, setDataSummary] = useState([]);
  const [resultReady, setResultReady] = useState(true);
  const [loading, setLoading] = useState(false);
  const [loadingSummary, setLoadingSummary] = useState(false);
  const [loadingAnswering, setLoadingAnswering] = useState(false);
  const [company_name, setCompany_name] = useState();
  const { assessmentData, setReport, setAssessmentData , setAssessmentSummery} = useSustain360Store();

  useEffect(() => {
    setCompany_name(user?.company_name);
  }, [user]);

  // Use Promise.all to handle multiple promises
  const fetchSummary = async () => {
    try{
      setLoadingSummary(true);
    const responce = await ApiSustain360.get(`/api/assessments/summery`);
    setDataSummary(
      responce?.data?.categoriesSummary.filter((scopItem) => {
        scopItem.categoryUnsolvedQuestions !== 0
          ? setResultReady(false)
          : null;
        return scopItem !== null;
      })
    );
    setAssessmentSummery(responce?.data);
  }catch(error){
    await fetchAssessmentData();
    await fetchSummary();
    console.log(error);
  }finally{
    setLoadingSummary(false);
  }
  };

  useEffect(() => {
    if (pathname === "/Insights-reporing/greensight/summary") {
      fetchSummary();
    }
  }, []);

  const get_report = async () => {
    setLoading(true);

    Swal.fire({
      title: "Please wait...",
      text: "Fetching report data...",
      icon: "info",
      allowOutsideClick: false,
      allowEscapeKey: false,
      didOpen: () => {
        Swal.showLoading();
      },
    });

    try {
      const { data } = await ApiSustain360.post(
        `/api/assessments/${assessmentData.id}/report`,
      );
      setReport(data);

      // عند نجاح العملية، تحديث `Swal` وإغلاقه
      Swal.fire({
        title: "Success!",
        text: "Data retrieved successfully",
        icon: "success",
        timer: 2000, // إغلاق تلقائي بعد ثانيتين
        showConfirmButton: false,
      });

      // navigate("/result");
      setActive("Reporting");
      //console.log(data);
    } catch (error) {
      Swal.close();

      // عند حدوث خطأ، تحديث `Swal` برسالة الخطأ
      Swal.fire({
        title: "Error!",
        text: error.response?.data?.error || "An error occurred",
        icon: "error",
        confirmButtonText: "OK",
      });

      // navigate("/result");
      setActive("Reporting");
    }finally{
      setLoading(false);
    }
  };
  const fetchAssessmentData = async () => {
      try {
        const response = await ApiSustain360.get("/api/assessments/in-progress/last")
        const assessmentData = response.data;
        setAssessmentData(assessmentData);
      } catch (error) {
        const response = await ApiSustain360.post("/api/assessments");
        const assessmentData = response.data;
        setAssessmentData(assessmentData);
      }
    };
  const answer_dummy = async () => {
    setLoadingAnswering(true);
    try {
      setLoadingAnswering(true);
      await ApiSustain360.post(`/api/questions/answers/random/${assessmentData.id}`);
      await fetchAssessmentData();
      setLoadingAnswering(false);
      setResultReady(true);
      await fetchSummary();
      // navigate("/result");
      //console.log(data);
    } catch (error) {
      setLoadingAnswering(false);
      showNotification({
        message: error.response.data.error,
        color: "red",
      });
      // navigate("/result");
      // //console.log(error);
    }
  };

  return (
    <div className="bg-[#F7F4F4] p-[.5rem] sm:p-[2rem] mb-5">
      {loadingSummary ? <Loading /> : dataSummary == null ? (
        <>
          <p className="text-center py-6">{t("Preparing the Assessment")}...</p>
          <Loading />
        </>
      ): (
        <>
      <h1 className="font-bold text-[18px] mb-5">Rapid Assessment Summary</h1>
      <div>
        {dataSummary?.length > 0 &&
          dataSummary.map((item, index) => (
            <Fundamentals
              key={index}
              title={item.categoryName}
              Total={item.categoryTotalQuestions}
              TotalNum={item.categoryTotalQuestions}
              Answered={item.categorySolvedQuestions}
              AnsweredNum={item.categorySolvedQuestions}
              Review={item.categoryUnsolvedQuestions}
              ReviewNum={item.categoryUnsolvedQuestions}
              summary={item.summary}
            />
          ))}
        {dataSummary?.length === 0 && <Loading />}

        <div className="flex md:flex-row-reverse flex-col">
          {dataSummary?.length != 0 && (
            <>
              <Button
                className={`bg-primary hover:bg-primary min-w-[100px] text-2xl text-white px-6 md:ms-3 rounded-lg 
                ${resultReady && dataSummary?.length > 0 ? "" : "opacity-50 cursor-not-allowed"}`}
                disabled={!resultReady || dataSummary?.length === 0}
                onClick={loading ? "" : () => get_report()}
              >
                {loading ? <Loading /> : "Result"}
              </Button>

              <Button
                className={`bg-primary hover:bg-primary min-w-[100px] text-xl text-white px-6  mt-5 md:mt-0
                rounded-lg ${
                  company_name === "LevelUp ESG" ? "block" : "hidden"
                } ${!resultReady ? "" : "opacity-50 cursor-not-allowed"}
              `}
                disabled={resultReady}
                onClick={loadingAnswering ? "" : () => answer_dummy()}
              >
                {loadingAnswering ? <Loading /> : "Answer Question"}
              </Button>
            </>
          )}
        </div>
      </div>
      </>
      )}
    </div>
  );
}

export default Summary;
