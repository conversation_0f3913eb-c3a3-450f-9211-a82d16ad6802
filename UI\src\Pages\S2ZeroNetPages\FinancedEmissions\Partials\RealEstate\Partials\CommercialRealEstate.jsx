import { useState } from "react";
import { Button, Select, TextInput } from "@mantine/core";

const PropertyGroup = ({ group, onRemove, onChange }) => {
  const selectOptions = {
    property: [
      "Class A Office",
      "Class B Office",
      "Class C Office",
      "Retail",
      "Warehouse/Logistics",
      "Hotel/Hospitality",
      "Healthcare",
      "Mixed-Use",
    ],
    buildingAges: ["< 5 Years", "5-15 Years", "15-30 Years", "30-50 Years", "> 50 Years"],
    heatingTypes: ["Natural Gas", "Electricity", "Oil", "Heat Pump", "District Heating"],
    dataSources: [
      "Actual Consumption (Score 1)",
      "Energy Star/Benchmarking (Score 2)",
      "Modeled Estimated (Score 3)",
      "CBECS/Regional Data (Score 4)",
      "Property Type Averages (Score 5)",
    ],
  };


  const inputFields = [
    { label: "Number of Properties", field: "vehicleType", placeholder: "e.g., 5000" },
    { label: "Total Outstanding ($M)", field: "totalOutstanding", placeholder: "e.g., 5000" },
    { label: "Avg Property Value ($)", field: "avgPropertyValue", placeholder: "e.g., 25.5" },
    {
      label: "Total Floor Area (m²)",
      field: "avgPropertySize",
      placeholder: "e.g., 50",
      description: "For EVIC calculation",
    },
  ];

  return (
    <div className="border border-[#E8E7EA] p-4 rounded-[11px]">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg text-[#272727] font-semibold">{group.name}</h3>
        <Button
          onClick={() => onRemove(group.id)}
          className="bg-[#FFE9E9] text-[#EA0B0B] hover:bg-[#EA0B0B] hover:text-white px-3 py-1 rounded-md"
        >
          Remove
        </Button>
      </div>
      <div className="grid grid-cols-5 gap-4 mb-3">
        {inputFields.map(({ label, field, placeholder, description }) => (
          <TextInput
            key={field}
            label={label}
            value={group[field] || ""}
            onChange={(e) => onChange(group.id, field, e.target.value)}
            placeholder={placeholder}
            description={description}
            inputWrapperOrder={description ? ["label", "input", "description"] : undefined}
          />
        ))}
        <Select
          label="Property Sub-type"
          placeholder="Select sub-type"
          data={selectOptions.property}
          value={group.property || ""}
          onChange={(value) => onChange(group.id, "property", value)}
        />
      </div>
      <div className="grid grid-cols-3 gap-4">
        <TextInput
          label="LEED/Green Certified (%)"
          placeholder="e.g., 50"
          value={group.avgBuildingAge || ""}
          onChange={(value) => onChange(group.id, "avgBuildingAge", value)}
          description="% of properties with green certification"
          inputWrapperOrder={["label", "input", "description"]}
        />
        <TextInput
          label="Avg Energy Intensity"
          placeholder="e.g., 50"
          value={group.heatingType || ""}
          onChange={(value) => onChange(group.id, "heatingType", value)}
          description="kWh/m²/year (if known)"
          inputWrapperOrder={["label", "input", "description"]}
        />
        <Select
          label="Data Source"
          placeholder="Select source"
          data={selectOptions.dataSources}
          value={group.dataSource || ""}
          onChange={(value) => onChange(group.id, "dataSource", value)}
        />
      </div>
    </div>
  );
};

const CommercialProperties = () => {
  const [commercialProperties, setCommercialProperties] = useState([
    {
      id: 1,
      name: "Office Building",
    },
  ]);

  const addGroup = () => {
    const newGroup = {
      id: commercialProperties.length + 1,
      name: `Office Building ${commercialProperties.length + 1}`,
    };
    setCommercialProperties([...commercialProperties, newGroup]);
  };

  const removeGroup = (id) => {
    setCommercialProperties(commercialProperties.filter((group) => group.id !== id));
  };

  const handleInputChange = (id, field, value) => {
    setCommercialProperties(
      commercialProperties.map((group) =>
        group.id === id ? { ...group, [field]: value } : group
      )
    );
  };

  return (
    <div className="bg-[#FFFFFF] p-4 rounded-[10px] border border-[#E8E7EA]">
      <h2 className="text-2xl text-[#272727] font-bold">
        Commercial Real Estate
      </h2>
      <div className="flex flex-col mt-4 gap-4">
        {commercialProperties.map((group) => (
          <PropertyGroup
            key={group.id}
            group={group}
            onRemove={removeGroup}
            onChange={handleInputChange}
          />
        ))}
      </div>
      <Button
        onClick={addGroup}
        className="mt-4 bg-[#07838F1A] hover:bg-[#07838F] text-[#07838F] px-4 py-2 rounded"
      >
        + Add Commercial Category
      </Button>
    </div>
  );
};

export default CommercialProperties;