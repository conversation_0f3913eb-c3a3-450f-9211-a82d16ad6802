import useSideBarRoute from '@/hooks/useSideBarRoute';
import MainLayout from '@/Layout/MainLayout';
import useRoutes from '@/Routes/useRoutes';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useLocation } from 'react-router-dom';
import ApiS3 from '@/Api/apiS3';
import { CgTrash } from 'react-icons/cg';
import { toast } from 'react-toastify';
import Loading from '@/Components/Loading';
import { IoMdHome } from 'react-icons/io';

const colors = {
  Sustainably: '#feeaa2',
  Governance: '#d58080',
  Environment: '#93e396',
  Data: '#7fdfd4',
  Society: '#f9bfa6',
  General: '#93c5f6',
};

export default function CommunityDashboard() {
  const [data, setData] = useState([]);
  const { t } = useTranslation();
  const { GreenHubMenu } = useSideBarRoute();
  const { GreenHubResourcesDashboard, GreenHubAcademyDashboard, GreenHubCommunityDashboard } = useRoutes().GreenHubMap;
  const location = useLocation();
  const [loading, setLoading] = useState(false);

  // Fetch topics from the API
  const getQuestions = useCallback(async () => {
    try {
      setLoading(true);
      const response = await ApiS3.get('/community/questions/all');
      if (response.status === 200) {
        setData(response.data.reverse());
      }
    } catch (error) {
      toast.error('Error fetching topics!');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    getQuestions();
  }, [getQuestions]);

  // Handle form submission

  const deleteQuestion = async (id) => {
    try {
      const response = await ApiS3.delete(`/community/questions/delete/${id}`);
      if (response.status === 200) {
        toast.success('Question Deleted Successfully!');
      }
      getQuestions(); // Refresh topics after deleting
    } catch (error) {
      toast.error('Error deleting Question!');
    }
  };

  return (
    <MainLayout menus={GreenHubMenu} navbarTitle={t('GreenHub')}
    breadcrumbItems={[
      { title: <IoMdHome size={20} />, href: "/get-started" },
      { title: "Docvault", href: "#" },
    ]}>
      <div className="overflow-y-auto">
        {/* Header */}
        <h1 className={`px-8 py-4 text-xl font-bold bg-white rounded-2xl text-blue-950 mb-7`}>{t('Manage Green Hub Resources')}</h1>

        {/* Navigation Links */}
        <div className="px-8 py-4 bg-white rounded-2xl text-blue-950 mb-7 flex justify-around">
          {[
            {
              to: GreenHubAcademyDashboard.path,
              label: t('GreenHub Academy'),
              key: 'academy',
            },
            {
              to: GreenHubResourcesDashboard.path,
              label: t('GreenHub Resources'),
              key: 'resourses',
            },
            {
              to: GreenHubCommunityDashboard.path,
              label: t('GreenHub Peers Community'),
              key: 'community',
            },
          ].map(({ to, label, key }) => {
            const isActive = location.pathname.split('/')[2] === key;
            const baseClasses = 'lg:p-3 w-[200px] lg:w-[400px] text-center rounded-xl shadow-xl lg:text-[24px] font-bold mx-2';
            const activeClasses = isActive ? 'bg-[#05808b] text-white' : 'bg-[#07838F33] text-[#05808b]';
            return <DashboardLink key={key} to={to} label={label} className={`${baseClasses} ${activeClasses}`} />;
          })}
        </div>
      </div>
      <div className="questions">
        <div className="form p-5 m-auto xl:w-[75%]">
          <div className="flex flex-col gap-4">
            {loading ? (
              <Loading />
            ) : (
              data?.map((question) => (
                <div className="flex flex-row" key={question._id}>
                  <div className="flex flex-col items-center gap-4"></div>

                  <div className="w-full p-4 bg-white rounded-lg shadow-md flex items-center justify-between gap-2">
                    <div className=" w-full">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          {question.user && question.user?.image ? (
                            <img src={question?.user?.image} alt={question?.user?.userName || 'User'} className="w-10 h-10 rounded-full" />
                          ) : (
                            <div className="w-10 h-10 rounded-full cursor-default text-center flex items-center justify-center font-bold text-sm text-primary bg-[#07838F1A]">
                              {(question?.user?.userName && question?.user?.userName[0]) || 'U'}
                            </div>
                          )}
                          <div>
                            <h3 className="text-lg font-semibold text-gray-700">{t(question?.user?.userName)}</h3>
                            <p className="text-sm text-gray-500">{t('@ ' + question?.user?.company_name)}</p>
                          </div>
                        </div>
                      </div>
                      <p className="mt-4 mb-2 text-gray-700">{t(question.question)}</p>
                      <div className="flex mb-4 space-x-2">
                        {question.tags &&
                          question?.tags?.map((tag) => (
                            <span
                              key={tag}
                              className="px-2 py-1 text-xs rounded-full text-black"
                              style={{ backgroundColor: colors[tag] || '#e2e8f0' }}
                            >
                              {tag}
                            </span>
                          ))}
                      </div>
                    </div>
                    {
                      <div
                        className="text-red-600 font-semibold cursor-pointer flex items-center hover:text-red-800 duration-300"
                        onClick={() => deleteQuestion(question._id)}
                      >
                        <CgTrash />
                        {t('Delete ')}
                      </div>
                    }
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
}

// Component for Navigation Links

function DashboardLink({ to, label, className }) {
  return (
    <Link to={to} className={className}>
      {label}
    </Link>
  );
}
// Component for Topic Cards
