import { useState } from "react";

import {
  MultiSelect,
  Select,
  Button,
  NumberInput,
  Textarea,
  Loader,
  Modal,
} from "@mantine/core";
import { showNotification } from "@mantine/notifications";
import { useDisclosure } from "@mantine/hooks";

import {
  actionStatusData,
  aiTextType,
  domains,
  impactTypes,
  ROOT_CAUSE_CATEGORIES,
  scopeChain,
  SDG_OPTIONS,
  thresholdLevels,
} from "./StaticData";

import AiA<PERSON> from "@/Api/aiApiConfig";

import { MdKeyboardArrowDown } from "react-icons/md";
import { FaRegCopy } from "react-icons/fa";
import { ThresholdSelector } from "../Helpers/ThresholdSelect";
import { AIIcon } from "@/assets/icons";

const ImpactAssessmentForm = ({
  handleChange,
  formData,
  setIsAiDone,
  type,
  loading,
  handleSubmit
}) => {
  // Destructure formData with the correct property names
  const {
    esgImpactDomain,
    valueChainScope,
    impactDescription,
    rootCaseCategory,
    impactType,
    impactOnSDG,
    exposureLevel,
    actionStatus, // Renamed to avoid conflict with imported variable
    progressTracking,
  } = formData;
  const [opened, { open, close }] = useDisclosure(false);

  const [aiText, setAiText] = useState("");

  const [aiLoad, setAiLoad] = useState(false);


  const aiFunc = async (type) => {
    if (impactDescription.length < 20) {
      showNotification({ message: `Minimum character 20`, color: "red" });
      return;
    }

    const data = {
      processor: "risk_description",
      resources: {
        prompt: impactDescription,
        "output_type": type
      },
    };
    setAiLoad(true);
    try {
      const res = await AiApi.post(`process_request`, data);
      setAiText(res.data.ai_response);
      setIsAiDone(true);
      open();
    } catch (er) {
      console.log("🚀 ~ aiFunc ~ er:", er);
    }
    setAiLoad(false);
  };

  const copyFunc = () => {
    navigator.clipboard
      .writeText(aiText)
      .then(() => {
        showNotification({
          message: "Text copied to clipboard!",
          color: "green",
        });
        close();
      })
      .catch(() => {
        showNotification({ message: "Failed to copy text", color: "red" });
      });
  };


  return (
    <div>
      <Modal size={"lg"} opened={opened} onClose={close} title="Ai suggestion">
        <p className="border border-gray-300 rounded-2xl p-2 mb-3">{aiText}</p>

        <Button className="bg-primary" onClick={copyFunc}>
          Copy <FaRegCopy />
        </Button>
      </Modal>

      <h5 className="text-primary font-bold text-center lg:text-2xl mb-10 mt-6">
        Impact Assessment
      </h5>

      <div className="grid lg:grid-cols-3 md:grid-cols-3 gap-6 mt-6">
        <Select
          disabled={type === "view"}
          rightSection={<MdKeyboardArrowDown />}
          value={esgImpactDomain}
          onChange={(value) => handleChange("esgImpactDomain", value)}
          name="impactDomain"
          label="ESG Impact Domain"
          placeholder="Pick value"
          data={domains}
        />

          <MultiSelect
          disabled={type === "view"}
          rightSection={<MdKeyboardArrowDown />}
          value={valueChainScope || []}
          onChange={(value) => {
            console.log("🚀 ~ value:", value)
            if(value.length == 0) {
              handleChange("valueChainScope", "")
            }else{
              handleChange("valueChainScope", value)
            }
          }}
          label="Value Chain Scope"
          placeholder={`${
            valueChainScope.length > 0 ? "" : "Operations, Upstream, Downstream"
          }`}
          data={scopeChain}
        />
      </div>

      <>
        <div className="relative mb-8 mt-8">
          <div className="flex justify-between items-center">
            <label
              htmlFor="imp-decs"
              className="text-base font-medium text-black"
            >
              Impact Description
            </label>
          </div>
        <div className="relative border border-gray-300 min-h-[145px] rounded py-2 bg-white">
        <div
            title="Ai Suggestion"
            disabled={aiLoad}
            className={`absolute right-2 top-2 group`}
          >
            {aiLoad ? (
              <Loader color="#07838F" size="sm" type="dots" />
            ) : (
              <AIIcon className="cursor-pointer" />
            )}
            <div className={`${aiLoad ? 'hidden': 'flex'} gap-1 absolute z-20 top-5 right-0 flex-col opacity-0 group-hover:opacity-100 transition-opacity duration-200`}>
              {aiTextType.map((type, index) => (
                <Button
                  variant="transparent"
                  key={index}
                  onClick={() => aiFunc(type)}
                  className={`base-border p-1 capitalize bg-white hover:bg-primary hover:text-white`}
                >
                  {type}
                </Button>
              ))}
            </div>
          </div>
          <Textarea
            disabled={type === "view"}
            id="imp-decs"
            value={impactDescription || ""}
            onChange={(e) => handleChange("impactDescription", e.target.value)}
            placeholder="Enter Text"
            classNames={{
              root: "w-[96%] min-h-[145px]",
              input: "min-h-[145px] border-none",
            }}
          />
          </div>

        </div>

        <div className="grid lg:grid-cols-2 gap-6">
          <Select
            disabled={type === "view"}
            rightSection={<MdKeyboardArrowDown />}
            value={rootCaseCategory}
            onChange={(value) => handleChange("rootCaseCategory", value)}
            label="The Root Cause Category"
            placeholder="Select category"
            data={ROOT_CAUSE_CATEGORIES}
          />

          <Select
            disabled={type === "view"}
            rightSection={<MdKeyboardArrowDown />}
            value={impactType}
            onChange={(value) => handleChange("impactType", value)}
            label="Impact Type"
            placeholder="Select Type"
            data={impactTypes}
          />

          <MultiSelect
            disabled={type === "view"}
            rightSection={<MdKeyboardArrowDown />}
            value={impactOnSDG || []}
            onChange={(value) => {
              if(value.length == 0) {
                handleChange("impactOnSDG", "");
              }else{
                handleChange("impactOnSDG", value)
              }
            }}
            label="Impact on SDGs"
            placeholder={`${impactOnSDG.length > 0 ? "" : "SDG 1-17 selection"}`}
            data={SDG_OPTIONS}
          />

          <ThresholdSelector
            disabled={type === "view"}
            label="ESG Exposure Level"
            value={exposureLevel || ""}
            onChange={(e) => handleChange("exposureLevel", e)}
          levels={thresholdLevels}
          type={type}
        />
        
          <Select
            disabled={type === "view"}
            rightSection={<MdKeyboardArrowDown />}
            value={actionStatus}
            onChange={(value) => handleChange("actionStatus", value)}
            label="Action Status"
            placeholder="Select Status"
            data={actionStatusData}
          />

          <NumberInput
            disabled={type === "view"}
            value={progressTracking || ""}
            onChange={(value) => handleChange("progressTracking", value)}
            label="Progress Tracking"
            clampBehavior="strict"
            min={0}
            max={100}
            placeholder="Enter 0 to 100"
          />
        </div>

        <div className="flex gap-5 mt-5">
          {
            type === "edit" &&
          <Button className="flex-1 bg-bg-lite1 text-gray-700 py-2 px-4 text-center border-r border-gray-300">
            Clear
          </Button>
          }
          {
            type === "edit" &&
          <Button disabled={loading} onClick={handleSubmit} className="flex-1 bg-teal-500 text-white py-2 px-4 text-center">
            {type === "edit" ? "Edit" : "Save"}
            {loading && <Loader size="xs" color="white" className="ml-2" />}
          </Button>
          }
              
        </div>
      </>
    </div>
  );
};

export default ImpactAssessmentForm;
