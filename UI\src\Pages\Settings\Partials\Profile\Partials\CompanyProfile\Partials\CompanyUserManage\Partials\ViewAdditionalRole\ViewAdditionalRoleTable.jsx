import ApiProfile from "@/Api/apiProfileConfig";
import Loading from "@/Components/Loading";
import { Button } from "@mantine/core";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

export default function ViewAdditionalRoleTable({
 AdditionalRoles,
 data,
 selectedUserId,
 close,
 show_company_users,
 SetSelectedUserId,
 setAdditionalRoleValue,
 addApi,
 removeApi,
 closeModel,
}) {
 //  console.log(closeModel);

 const [changeData, setChangeData] = useState([]);
 const [addLoading, setAddLoading] = useState({});
 const [removeLoading, setRemoveLoading] = useState({});
 const restModal = () => {
  close();
  setAdditionalRoleValue({});
  SetSelectedUserId();
  show_company_users().then(() => {
   closeModel();
  });
 };
 useEffect(() => {
  if (AdditionalRoles?.length) {
   const userRoleIds = AdditionalRoles.filter((item) =>
    data?.includes(item.roleName)
   ).map((item) => item.roleId);

   setChangeData(userRoleIds);
  }
 }, [AdditionalRoles, data]);

 const handleAddLoading = (id, value) => {
  setAddLoading((prev) => ({ ...prev, [id]: value }));
 };

 const handleRemoveLoading = (id, value) => {
  setRemoveLoading((prev) => ({ ...prev, [id]: value }));
 };

 const addAdditionalRole = async (roleId, userId) => {
  const addValue = {
   user_id: userId,
   additional_role_ids: [...changeData, roleId],
  };

  try {
   handleAddLoading(roleId, true);
   const { data } = await ApiProfile.post(addApi, addValue);

   // ✅ تحديث changeData عند نجاح الطلب
   setChangeData((prev) => [...prev, roleId]);

   handleAddLoading(roleId, false);
  } catch (error) {
   handleAddLoading(roleId, false);
   console.error(error);
  }
 };

 const RemoveAdditionalRole = async (roleId, userId) => {
  const removeValue = {
   user_id: userId,
   additional_role_ids: [roleId],
  };

  try {
   handleRemoveLoading(roleId, true);
   const { data } = await ApiProfile.post(removeApi, removeValue);

   // ✅ تحديث changeData عند نجاح الطلب
   setChangeData((prev) => prev.filter((id) => id !== roleId));

   handleRemoveLoading(roleId, false);
  } catch (error) {
   handleRemoveLoading(roleId, false);
   console.error(error);
  }
 };

 return !AdditionalRoles?.length ? (
  <Loading />
 ) : (
  <div className="p-5 bg-white rounded-lg">
   {AdditionalRoles?.map((item, indx) => (
    <div key={item?.roleId || indx} className="flex items-center gap-2">
     <p className="cursor-pointer mt-1">{item?.roleName}</p>

     {!changeData.includes(item.roleId) ? (
      <Button
       onClick={() => addAdditionalRole(item.roleId, selectedUserId)}
       disabled={addLoading[item.roleId]}
       size="sm"
       className="p-[2px] rounded-lg text-sm bg-primary hover:bg-primary"
      >
       {addLoading[item.roleId] ? <Loading /> : "+ Add"}
      </Button>
     ) : (
      <Button
       onClick={() => RemoveAdditionalRole(item.roleId, selectedUserId)}
       disabled={removeLoading[item.roleId]}
       size="sm"
       className="p-[2px] rounded-lg text-sm bg-red-600 hover:bg-red-600"
      >
       {removeLoading[item.roleId] ? <Loading /> : "- Remove"}
      </Button>
     )}
    </div>
   ))}

   <Button
    className="bg-primary hover:bg-primary ms-auto block"
    size="md"
    radius={10}
    onClick={restModal}
   >
    Submit
   </Button>
  </div>
 );
}
