import { useState } from "react";
import { Tabs } from "@mantine/core";
import S3Layout from "@/Layout/S3Layout";

import SectionOne from "./Components/Dashboard/SectionOne";
import SectionTow from "./Components/Dashboard/SectionTow";
import ComplianceTracker from "./Components/ComplianceTracker/ComplianceTracker";
import FinancialAntiGreenWashingView from "../financialServicesStart/Partials/FinancialAntiGreenWashing/FinancialAntiGreenWashingView";
import { IoMdHome } from "react-icons/io";
import GuideModalButton from "@/Components/Guide/GuideModalButton";
import DashboardGuide from "./DashboardGuide";
import RegulationsGuide from "./RegulationsGuide";

const CompilanceView = () => {
    const [activeTab, setActiveTab] = useState("assessment");

    return (
        <S3Layout
            breadcrumbItems={[
                { title: <IoMdHome size={20} />, href: "/get-started" },
                { title: "Anti-Greenwashing", href: "#" },
            ]}
            navbarTitle="Anti-Greenwashing Compliance Checker"
        >
            <Tabs value={activeTab}>
                <Tabs.List
                    justify="center"
                    className="mb-6 flex justify-around text-gray-500 py-3 rounded-md overflow-hidden bg-white before:border-none"
                >
                    <Tabs.Tab
                        value="dashboard"
                        className={`text-lg font-bold ${
                            activeTab == "dashboard"
                                ? "text-primary bg-opacity-100 border-secondary-400"
                                : "bg-opacity-10"
                        } `}
                        onClick={() => setActiveTab("dashboard")}
                    >
                        Dashboard
                    </Tabs.Tab>

                    <Tabs.Tab
                        value="assessment"
                        className={`text-lg font-bold ${
                            activeTab == "assessment"
                                ? "text-primary bg-opacity-100 border-secondary-400"
                                : "bg-opacity-10"
                        } `}
                        onClick={() => setActiveTab("assessment")}
                    >
                        Compliance Assessment
                    </Tabs.Tab>

                    <Tabs.Tab
                        value="tracker"
                        className={`text-lg font-bold ${
                            activeTab == "tracker"
                                ? "text-primary bg-opacity-100 border-secondary-400"
                                : "bg-opacity-10"
                        } `}
                        onClick={() => setActiveTab("tracker")}
                    >
                        Regulations Tracker
                    </Tabs.Tab>
                </Tabs.List>

                <Tabs.Panel value="dashboard">
                    <div className="w-full justify-between flex items-center py-5 px-3">
                        <GuideModalButton buttonText="Dashboard Guide">
                            <DashboardGuide />
                        </GuideModalButton>
                    </div>
                    <SectionOne />
                    <SectionTow />
                </Tabs.Panel>

                <Tabs.Panel value="assessment">
                    <FinancialAntiGreenWashingView />
                </Tabs.Panel>

                <Tabs.Panel value="tracker">
                        <GuideModalButton buttonText="Regulations Guide">
                            <RegulationsGuide />
                        </GuideModalButton>
                    <ComplianceTracker />
                </Tabs.Panel>
            </Tabs>
            <br />
            <br />
        </S3Layout>
    );
};

export default CompilanceView;
