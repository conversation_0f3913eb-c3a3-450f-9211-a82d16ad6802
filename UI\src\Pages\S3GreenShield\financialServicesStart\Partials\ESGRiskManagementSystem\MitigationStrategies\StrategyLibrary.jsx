import React, { useState, useEffect } from 'react';
import Button from '../Components/Button';
import { IoFilter } from 'react-icons/io5';
import { IoIosSearch } from 'react-icons/io';
import { SlOptionsVertical } from 'react-icons/sl';
import Edit from '@/assets/images/edit.svg';
import { MdDelete } from 'react-icons/md';
import { strategyData } from './data'; // Importing the strategy data
import { useForm } from '@mantine/form';
import Axios from 'axios';
import Cookies from 'js-cookie';
import fileAdd from '@/assets/images/file-add.svg';
import uploadComplete from '@/assets/images/uploadcomplete-icon.svg';
import {
  TextInput,
  Modal,
  createTheme,
  SemiCircleProgress,
  Radio,
  Group,
  MantineProvider,
  Slider,
  MultiSelect,
  Select,
  Textarea,
  rem,
  Popover,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IoArrowDownOutline } from 'react-icons/io5';
import { DateInput } from '@mantine/dates';
import { notifications } from '@mantine/notifications';
import { useStrategy } from './StrategyContext';

const theme = createTheme({
  cursorType: 'pointer',
});

const filterData = ['Social', 'Environmental', 'Governance'];
export default function StrategyLibrary() {
  const { savedStrategy } = useStrategy();
  const [filters, setFilters] = useState(filterData);
  const [searchQuery, setSeatchQuery] = useState('');

  const buttonTexts = ['PDF', 'DOCX', 'TXT', '> 10MB'];
  const icon = <IoArrowDownOutline style={{ width: rem(12), height: rem(12), color: '#00C0A9' }} />;

  const form = useForm({
    initialValues: {
      mitigationStrategyId: '',
      name: '',
      progress: '0',
      controlDesign: 'Adequate',
      controlOperatingEffectiveness: '0',
      category: '', // Environmental , Social, Governance
      objectives: '',
      relatedRisks: [],
      owner: '',
      targetImplementationDate: '',
      comment: '',
      tagColleagues: [],
      url: '',
    },
  });

  const [environmentalOptions, setEnvironmentalOptions] = useState([]);
  const [socialOptions, setSocialOptions] = useState([]);
  const [governanceOptions, setGovernanceOptions] = useState([]);
  const [relatedRisksOptions, setRelatedRisksOptions] = useState([]);
  const [tagColleaguesOptions, setTagColleaguesOptions] = useState(['@John Doe', '@Jane Doe']);

  const [updated, setUpdated] = useState(false);

  useEffect(() => {
    const fetchMasterData = async () => {
      setLoading(true);
      try {
        const token = Cookies.get('level_user_token');

        const response = await Axios.get(
          'https://portals3-h3bjdkhtbghye2aa.uksouth-01.azurewebsites.net/mitigation-strategy-types/user/categorized',
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
            withCredentials: true,
          }
        );

        const riskResponse = await Axios.get('https://portals3-h3bjdkhtbghye2aa.uksouth-01.azurewebsites.net/risk', {
          headers: {
            Authorization: `Bearer ${token}`,
          },
          withCredentials: true,
        });

        const relatedRisks = riskResponse.data.map((relatedRisk) => ({
          value: relatedRisk._id,
          label: relatedRisk.riskName,
        }));

        setRelatedRisksOptions(relatedRisks);

        setEnvironmentalOptions(response.data.environmental);
        setSocialOptions(response.data.social);
        setGovernanceOptions(response.data.governance);

        if (name.length > 0) {
          form.setFieldValue('name', name[0].value);
        }
      } catch (err) {
        setError(err.message);
        setLoading(false);
      }
    };

    fetchMasterData();
  }, [updated]);
  const [filteredEnvironmental, setFilteredEnvironmental] = useState(environmentalOptions);
  const [filteredSocial, setFilteredSocial] = useState(socialOptions);
  const [filteredGovernance, setFilteredGovernance] = useState(governanceOptions);

  useEffect(() => {
    if (!searchQuery) {
      setFilteredEnvironmental(environmentalOptions);
      setFilteredSocial(socialOptions);
      setFilteredGovernance(governanceOptions);
    } else {
      setFilteredEnvironmental(environmentalOptions.filter((item) => item.name.toLowerCase().includes(searchQuery.toLowerCase())));
      setFilteredSocial(socialOptions.filter((item) => item.name.toLowerCase().includes(searchQuery.toLowerCase())));
      setFilteredGovernance(governanceOptions.filter((item) => item.name.toLowerCase().includes(searchQuery.toLowerCase())));
    }
  }, [searchQuery, environmentalOptions, socialOptions, governanceOptions]);

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // State to track the visibility of icons for each individual item
  const [showIcons, setShowIcons] = useState({});

  // State to handle whether input is editable
  const [isEditable, setIsEditable] = useState({});

  // State to handle editable text for each input field
  const [editableText, setEditableText] = useState(
    strategyData.reduce((acc, strategy) => {
      strategy.items.forEach((item) => {
        acc[item.id] = item.editableText; // Initialize the text for each item
      });
      return acc;
    }, {})
  );

  // Function to toggle icon visibility for a specific item
  const toggleIcons = (id) => {
    setShowIcons((prevState) => ({
      ...prevState,
      [id]: !prevState[id], // Toggle only the specific item's visibility
    }));
  };

  // Handle editing (enable the input field to be editable)
  const handleEdit = (item) => {
    form.setValues({
      mitigationStrategyId: item.mitigationStrategyId || '',
      name: item.name || '',
      progress: item.progress ? item.progress.toString() : '0',
      controlDesign: item.controlDesign || 'Adequate',
      controlOperatingEffectiveness: item.controlOperatingEffectiveness ? item.controlOperatingEffectiveness.toString() : '0',
      category: item.category || '',
      objectives: item.objectives || '',
      relatedRisks: item.relatedRisks || [],
      owner: item.owner || '',
      targetImplementationDate: item.targetImplementationDate ? new Date(item.targetImplementationDate) : null,
      comment: item.comment || '',
      tagColleagues: item.tagColleagues || [],
      url: item.url || '',
    });
    setProgressValue(Number(item.progress));
    setUpdated(false);
    setSelectedMitigationStrategy(item);
    open();

    console.log(item);
  };

  // Handle save (save the changes to the text and disable editing)
  // const handleSave = (id) => {
  //   setIsEditable((prevState) => ({
  //     ...prevState,
  //     [id]: false, // Disable editing after saving
  //   }));
  //   // Here you can also update the data source if needed
  //   // For example, you could call an API to persist the changes or update a local state
  // };

  const validateAndConvert = (value) => {
    const num = Number(value);
    return isNaN(num) ? 0 : num; // or handle it according to your requirements
  };

  const handleUpdateModal = async () => {
    const data = form.values;

    setLoading(true);
    try {
      const token = Cookies.get('level_user_token');
      const response = await Axios.patch(
        `https://portals3-h3bjdkhtbghye2aa.uksouth-01.azurewebsites.net/mitigation-strategy-types/${selectedMitigationStrategy._id}`,
        {
          // mitigationStrategyId: data.mitigationStrategyId,
          owner: data.owner,
          targetImplementationDate: data.targetImplementationDate ? new Date(data.targetImplementationDate).toISOString() : '',
          objectives: data.objectives,
          relatedRisks: data.relatedRisks,
          url: data.url,
          tagColleagues: data.tagColleagues,
          name: data.name,
          category: data.category,
          progress: validateAndConvert(data.progress),
          controlDesign: data.controlDesign,
          controlOperatingEffectiveness: validateAndConvert(data.controlOperatingEffectiveness),
          comment: data.comment,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      setLoading(false);
      notifications.show({
        title: 'Success',
        message: 'Risk data saved successfully!',
        color: 'green',
      });

      // window.location.reload();
      setUpdated(true);
      close();
    } catch (err) {
      setError(err.message);
      setLoading(false);

      notifications.show({
        title: 'Error',
        message: `Failed to save risk data: ${error.message}`,
        color: 'red',
      });
    }
  };

  // Handle deletion (clear the input field)
  const handleDelete = (id) => {
    setEditableText((prevState) => ({
      ...prevState,
      [id]: '', // Clear the text for the specific item
    }));
  };

  const [opened, { open, close }] = useDisclosure(false);

  const [progressValue, setProgressValue] = useState(Number(form.values.progress));

  const handleProgressChange = (event) => {
    const newProgress = event.target.value;
    form.setFieldValue('progress', newProgress);
    setProgressValue(Number(newProgress));
  };

  const [selectedMitigationStrategy, setSelectedMitigationStrategy] = useState({});

  return (
    <div className="flex flex-col -mt-10">
      <div className="flex flex-row items-center justify-between shadow-lg font-semibold text-[#000] bg-[#fff] p-4 mt-6 rounded-lg">
        <p className="text-lg font-bold">Mitigation Strategy Library</p>
        <div className="flex flex-row mr-20 gap-4">
          <div className="flex flex-row items-center shadow-none px-4 rounded-lg bg-[#EBEBEB] text-[#00C0A9]">
            <IoFilter className="text-[#212121] mr-2" />
            {/* <p className="text-[#00C0A9] mr-2">Filter</p> */}
            <Popover width={300} position="bottom" withArrow shadow="md">
              <Popover.Target>
                <Button style={{ backgroundColor: 'transparent', color: '#9C9C9C' }}>Filter</Button>
              </Popover.Target>
              <Popover.Dropdown>
                <MultiSelect
                  placeholder="Select Columns "
                  data={filterData}
                  comboboxProps={{ withinPortal: false }}
                  onChange={setFilters}
                  clearable={true}
                />
              </Popover.Dropdown>
            </Popover>

            <p className="text-[#9C9C9C]">Columns</p>
          </div>
          <div className="flex flex-row items-center bg-[#F5F5F5] rounded-lg px-6 py-2">
            <input placeholder="Find" className="w-full bg-transparent" onChange={(e) => setSeatchQuery(e.target.value)} />
            <IoIosSearch className="text-[#626364] w-6 h-6 ml-10" />
          </div>
          {/* <Button
            text="+ Add New Strategy"
            className="p-4 bg-[#00C0A9] text-[#fff]"
          /> */}
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4 mt-4 ">
        <div className="px-6 mt-4">
          <p className="p-4 font-bold text-lg bg-[#00C0A9] text-[#00C0A9] text-center  border-2 cursor-default border-[#00C0A9] bg-opacity-[40%] rounded-lg">
            Environmental
          </p>
        </div>
        <div className="px-6 mt-4">
          <p className="p-4 font-bold text-lg bg-[#FFAB07] text-[#FFAB07] text-center  border-2 cursor-default border-[#FFAB07] bg-opacity-[40%] rounded-lg">
            Social
          </p>
        </div>
        <div className="px-6 mt-4">
          <p className="p-4 font-bold text-lg bg-[#298BED] text-[#298BED] text-center border-2 cursor-default border-[#298BED] bg-opacity-[40%] rounded-lg">
            Governance
          </p>
        </div>

        {/* Mapping through strategy data for each category */}

        <div className="flex flex-col px-6">
          {/* Mapping through each item within the current category */}
          {filteredEnvironmental.map((item) => (
            <label key={item._id} className="grid grid-cols-5 rounded-lg mb-4">
              <TextInput
                {...form.getInputProps('name')}
                variant={form.errors.title ? 'styled' : `unstyled`}
                key={form.key('name')}
                type="text"
                id="name"
                placeholder=""
                data={item.mitigationStrategyId}
                value={item.mitigationStrategyId}
                size="sm"
                className="font-bold w-full bg-[#EEEEEE] rounded-l-lg shadow-lg p-2"
                styles={{
                  error: { display: 'none' },
                }}
              />

              {/* Editable input and toggle icons */}
              <div className={`flex flex-row shadow-lg items-center bg-[#fff] ${showIcons[item.id] ? 'col-span-3' : 'col-span-4'} rounded-r-lg p-2`}>
                <TextInput
                  {...form.getInputProps('name')}
                  variant={form.errors.title ? 'styled' : `unstyled`}
                  key={form.key('name')}
                  type="text"
                  id="name"
                  data={item.name}
                  value={item.name}
                  placeholder=""
                  size="sm"
                  className="font-bold w-full"
                  styles={{
                    error: { display: 'none' },
                  }}
                />

                {/* Toggle visibility for this specific item */}
                <SlOptionsVertical className="text-[#626364] cursor-pointer" onClick={() => toggleIcons(item.id)} />
              </div>

              {/* Conditionally render edit and delete icons */}
              {showIcons[item.id] && (
                <div className="flex justify-center items-center space-x-2">
                  {/* {isEditable[item.id] ? (
                    <button
                      className="text-[#00C0A9]"
                      onClick={() => handleSave(item.id)}
                    >
                      Save
                    </button>
                  ) : ( */}
                  <button onClick={() => handleEdit(item)}>
                    <img src={Edit} alt="edit" />
                  </button>
                  {/* )} */}
                  <MdDelete className="text-[#00C0A9] h-6 w-6 cursor-pointer" onClick={() => handleDelete(item.id)} />
                </div>
              )}
            </label>
          ))}
        </div>

        <div className="flex flex-col px-6">
          {/* Mapping through each item within the current category */}
          {filteredSocial.map((item) => (
            <label key={item._id} className="grid grid-cols-5 rounded-lg mb-4">
              <TextInput
                {...form.getInputProps('name')}
                variant={form.errors.title ? 'styled' : `unstyled`}
                key={form.key('name')}
                type="text"
                id="name"
                placeholder=""
                data={item.mitigationStrategyId}
                value={item.mitigationStrategyId}
                size="sm"
                className="font-bold w-full bg-[#EEEEEE] rounded-l-lg shadow-lg p-2"
                styles={{
                  error: { display: 'none' },
                }}
              />

              {/* Editable input and toggle icons */}
              <div className={`flex flex-row shadow-lg items-center bg-[#fff] ${showIcons[item.id] ? 'col-span-3' : 'col-span-4'} rounded-r-lg p-2`}>
                <TextInput
                  {...form.getInputProps('name')}
                  variant={form.errors.title ? 'styled' : `unstyled`}
                  key={form.key('name')}
                  type="text"
                  id="name"
                  data={item.name}
                  value={item.name}
                  placeholder=""
                  size="sm"
                  className="font-bold w-full"
                  styles={{
                    error: { display: 'none' },
                  }}
                />

                {/* Toggle visibility for this specific item */}
                <SlOptionsVertical className="text-[#626364] cursor-pointer" onClick={() => toggleIcons(item.id)} />
              </div>

              {/* Conditionally render edit and delete icons */}
              {showIcons[item.id] && (
                <div className="flex justify-center items-center space-x-2">
                  {/* {isEditable[item.id] ? (
                    <button
                      className="text-[#00C0A9]"
                      onClick={() => handleSave(item.id)}
                    >
                      Save
                    </button>
                  ) : ( */}
                  <button onClick={() => handleEdit(item)}>
                    <img src={Edit} alt="edit" />
                  </button>
                  {/* )} */}
                  <MdDelete className="text-[#00C0A9] h-6 w-6 cursor-pointer" onClick={() => handleDelete(item.id)} />
                </div>
              )}
            </label>
          ))}
        </div>

        <div className="flex flex-col px-6">
          {/* Mapping through each item within the current category */}
          {filteredGovernance.map((item) => (
            <label key={item._id} className="grid grid-cols-5 rounded-lg mb-4">
              <TextInput
                {...form.getInputProps('name')}
                variant={form.errors.title ? 'styled' : `unstyled`}
                key={form.key('name')}
                type="text"
                id="name"
                placeholder=""
                data={item.mitigationStrategyId}
                value={item.mitigationStrategyId}
                size="sm"
                className="font-bold w-full bg-[#EEEEEE] rounded-l-lg shadow-lg p-2"
                styles={{
                  error: { display: 'none' },
                }}
              />

              {/* Editable input and toggle icons */}
              <div className={`flex flex-row shadow-lg items-center bg-[#fff] ${showIcons[item.id] ? 'col-span-3' : 'col-span-4'} rounded-r-lg p-2`}>
                <TextInput
                  {...form.getInputProps('name')}
                  variant={form.errors.title ? 'styled' : `unstyled`}
                  key={form.key('name')}
                  type="text"
                  id="name"
                  data={item.name}
                  value={item.name}
                  placeholder=""
                  size="sm"
                  className="font-bold w-full"
                  styles={{
                    error: { display: 'none' },
                  }}
                />

                {/* Toggle visibility for this specific item */}
                <SlOptionsVertical className="text-[#626364] cursor-pointer" onClick={() => toggleIcons(item.id)} />
              </div>

              {/* Conditionally render edit and delete icons */}
              {showIcons[item.id] && (
                <div className="flex justify-center items-center space-x-2">
                  {/* {isEditable[item.id] ? (
                    <button
                      className="text-[#00C0A9]"
                      onClick={() => handleSave(item.id)}
                    >
                      Save
                    </button>
                  ) : ( */}
                  <button onClick={() => handleEdit(item)}>
                    <img src={Edit} alt="edit" />
                  </button>
                  {/* )} */}
                  <MdDelete className="text-[#00C0A9] h-6 w-6 cursor-pointer" onClick={() => handleDelete(item.id)} />
                </div>
              )}
            </label>
          ))}
        </div>

        <Modal opened={opened} onClose={close} size="100%" title={<h2 className="text-[#07838F] font-bold">EditMitigation Strategy Library</h2>}>
          <div className="flex flex-col justify-center">
            <div className="flex flex-col -mt-10">
              <div className="flex flex-row items-center  gap-4 shadow-lg font-semibold text-[#000] bg-[#fff] py-2 px-6 mt-6 rounded-lg ">
                <h1 className="text-[#00C0A9] font-semibold text-xl">Control Strategy:</h1>{' '}
                <p className="text-lg">{form.values.mitigationStrategyId || ' - '} - </p>
                <TextInput
                  {...form.getInputProps('name')}
                  variant={form.errors.title ? 'styled' : `unstyled`}
                  key={form.key('name')}
                  type="text"
                  id="name"
                  placeholder="Renewable Energy Transition"
                  size="lg"
                  className="font-bold w-1/2"
                  styles={{
                    error: { display: 'none' },
                  }}
                />
              </div>

              <div className="grid grid-cols-4 gap-4 py-6">
                <div className="col-span-2 grid gap-4">
                  <div className="grid grid-rows-2 grid-flow-col gap-4">
                    <div className="row-span-2 rounded-lg shadow-lg p-6 bg-white">
                      <p className="text-start text-[#000] font-bold mb-2">Progress</p>
                      <div className="flex flex-col justify-center items-center">
                        <SemiCircleProgress
                          // {...form.getInputProps("progress")}
                          // key={form.key("progress")}
                          // id="progress"
                          // type="number"
                          fillDirection="left-to-right"
                          orientation="up"
                          filledSegmentColor="#00C0A9"
                          size={200}
                          thickness={35}
                          value={progressValue}
                          label={`${progressValue}%`}
                        />
                        <hr className="w-24 mt-8" />
                        <div className="flex flex-row gap-4 items-center">
                          <p className="text-center text-[#000] font-bold mt-2">Implementation</p>
                          <input
                            {...form.getInputProps('progress')}
                            key={form.key('progress')}
                            id="progress"
                            type="number"
                            value={form.values.progress}
                            onChange={handleProgressChange}
                            min="0"
                            max="100"
                            step="1"
                            style={{
                              marginTop: '10px',
                              width: '60px',
                              textAlign: 'center',
                            }}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="col-span-2 rounded-lg shadow-lg bg-white p-4">
                      <p className="text-center text-[#000] font-bold">Control Design</p>
                      <hr className="mt-4" />
                      <MantineProvider theme={theme}>
                        <Radio.Group {...form.getInputProps('controlDesign')} defaultValue="Adequate">
                          <Group mt="xs">
                            <Radio color="#1DC9A0" value="Adequate" label="Adequate" variant="outline" />
                            <Radio color="#1DC9A0" value="Inadequate" label="Inadequate" variant="outline" />
                          </Group>
                        </Radio.Group>
                      </MantineProvider>
                    </div>

                    <div className=" col-span-2 rounded-lg shadow-lg bg-white p-4">
                      <p className="text-center text-[#000] font-bold">Control Operating effectiveness</p>
                      <hr className="mt-2 mb-2" />
                      <Slider
                        {...form.getInputProps('controlOperatingEffectiveness')}
                        key={form.key('controlOperatingEffectiveness')}
                        id="controlOperatingEffectiveness"
                        type="number"
                        color="#00C0A9"
                        size="lg"
                        showLabelOnHover={false}
                        marks={[
                          { value: 1, label: '1' },
                          { value: 2, label: '2' },
                          { value: 3, label: '3' },
                          { value: 4, label: '4' },
                          { value: 5, label: '5' },
                        ]}
                        thumbSize={12}
                        min={1}
                        max={5}
                        step={1}
                        styles={{
                          thumb: {
                            borderWidth: 4,
                            color: '#00C0A9',
                            display: 'none',
                          },
                        }}
                      />
                    </div>
                  </div>

                  <div className="grid gap-4 shadow-lg rounded-xl bg-white p-6">
                    <Select
                      {...form.getInputProps('category')}
                      key={form.key('category')}
                      id="category"
                      name="category"
                      comboboxProps={{ withinPortal: true }}
                      data={['Environmental', 'Social', 'Governance']}
                      placeholder="Select Category"
                      label="Category"
                      radius="md"
                      rightSectionPointerEvents="none"
                      rightSection={icon}
                      className="font-semibold"
                    />

                    <TextInput
                      {...form.getInputProps('objectives')}
                      key={form.key('objectives')}
                      type="text"
                      id="objectives"
                      label="Objectives"
                      placeholder="Enter Text"
                      radius="md"
                      className="font-semibold text-gray-700 w-full "
                    />

                    <MultiSelect
                      {...form.getInputProps('relatedRisks')}
                      label="Related Risks"
                      placeholder=""
                      data={relatedRisksOptions}
                      // defaultValue={["Risk 1"]}
                      radius="md"
                      rightSectionPointerEvents="none"
                      rightSection={icon}
                      className="font-semibold"
                    />

                    <div className="grid grid-cols-2 gap-4 ">
                      <TextInput
                        {...form.getInputProps('owner')}
                        key={form.key('owner')}
                        type="text"
                        id="owner"
                        label="Owner"
                        placeholder="Enter Text"
                        radius="md"
                        className="font-semibold text-gray-700 w-full "
                      />
                      <DateInput
                        {...form.getInputProps('targetImplementationDate')}
                        key={form.key('targetImplementationDate')}
                        placeholder="DD/MM/YYYY"
                        id="targetImplementationDate"
                        name="targetImplementationDate"
                        label="Target ImplementationDate"
                        radius="md"
                        className="font-semibold"
                      />
                    </div>

                    <Textarea
                      {...form.getInputProps('comment')}
                      key={form.key('comment')}
                      size="sm"
                      label="Comments"
                      autosize
                      minRows={12}
                      radius="md"
                      className="font-semibold"
                      placeholder="enter text..."
                    />

                    <MultiSelect
                      {...form.getInputProps('tagColleagues')}
                      key={form.key('tagColleagues')}
                      type="text"
                      id="tagColleagues"
                      data={tagColleaguesOptions}
                      label="Tag Colleagues"
                      placeholder="@..."
                      radius="md"
                      className="font-semibold text-gray-700 w-full "
                      rightSectionPointerEvents="none"
                      rightSection={icon}
                    />

                    <TextInput
                      {...form.getInputProps('url')}
                      key={form.key('url')}
                      type="text"
                      id="url"
                      label="URL"
                      placeholder="https://"
                      radius="md"
                      className="font-semibold text-gray-700 w-full "
                    />
                  </div>
                </div>

                <div className="col-span-2 grid gap-4">
                  <div className="rounded-lg shadow-lg bg-white h-3/4 p-4">
                    <p className="text-start text-[#000] text-2xl font-bold">Evidence Attachments</p>
                    <div className="flex flex-col mt-4 justify-center items-center px-20 py-6 rounded-2xl shadow-lg border-2 border-[#07838F]">
                      <img src={fileAdd} alt="add a File" className="cursor-pointer w-24 h-24" />
                      <p className="text-[#9C9C9C] underline mt-4">Click to upload</p>
                      <div className="flex flex-row gap-2 mt-8">
                        {buttonTexts.map((text, index) => (
                          <Button
                            key={index}
                            text={text}
                            className={`p-2 font-normal shadow-none text-[#07838F] text-xs ${
                              text !== '> 10MB' ? 'bg-[#07838F]' : 'bg-[#fff] '
                            } bg-opacity-20 border-2 border-[#07838F] border-opacity-5`}
                          />
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="rounded-lg border-2 border-[#fff] -mt-32 h-3/4 p-4">
                    <div className="flex justify-between bg-[#fff] rounded-xl py-2 px-4">
                      <p className="flex flex-col font-bold">
                        document.pdf <span className="text-[#07838F] font-normal">Upload complete</span>
                      </p>
                      <img src={uploadComplete} alt="upload complete" />
                    </div>
                    <div className="flex justify-between bg-[#fff] mt-2 rounded-xl py-2 px-4">
                      <p className="flex flex-col font-bold">
                        document.csv <span className="text-[#07838F] font-normal">Upload complete</span>
                      </p>
                      <img src={uploadComplete} alt="upload complete" />
                    </div>
                  </div>
                  <div className="flex justify-end gap-4">
                    <button color="#00C0A9" className="border-[#00C0A9] border-2 h-10 rounded-lg text-[#00C0A9] px-4 font-bold">
                      Save as Draft
                    </button>
                    <button color="#00C0A9" onClick={handleUpdateModal} className="bg-[#00C0A9] h-10 rounded-lg text-[#fff] px-4 font-bold">
                      Submit
                    </button>
                  </div>
                </div>
              </div>
            </div>
            {/* <Button
              onClick={handleSave}
              className="mt-4 bg-[#07838F] rounded-lg"
            >
              Save
            </Button> */}
          </div>
        </Modal>
      </div>
    </div>
  );
}
