import ApiProfile from "@/Api/apiProfileConfig";
import Loading from "@/Components/Loading";
import {
 Button,
 Input,
 Pagination,
 ScrollArea,
 Table,
 TextInput,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { showNotification } from "@mantine/notifications";
import { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { CiSearch } from "react-icons/ci";
import { GoDatabase } from "react-icons/go";
import ActionCompanyModal from "./ActionCompanyModal";
import AddCompanyModel from "./AddCompanyModel";
import ViewAccessModal from "./ViewCompanyAccess/ViewCompanyAccessModal";
import ViewCompanyUsersModal from "./ViewCompanyUsers/ViewCompanyUsersModal";

export default function ManageCompaniesTable({
 data,
 getAllCompanyAccounts,
 tableLoading,
}) {
 const [addUserOpened, { open: addUserOpen, close: addUserClose }] =
  useDisclosure(false);
 const [opened, { open, close }] = useDisclosure(false);
 const [selectedCompanyId, SetSelectedCompanyId] = useState(null);
 const [selectedCompanyUsers, SetSelectedCompanyUsers] = useState(null);
 const [selectedViewAccess, setViewAccess] = useState();
 const [accessUserValue, setAccessUserValue] = useState();
 const [accessUserLoading, setAccessUserLoading] = useState(false);
 const [accessOpened, { open: accessOpen, close: accessClose }] =
  useDisclosure(false);
 const [ViewAccessOpened, { open: ViewAccessOpen, close: ViewAccessClose }] =
  useDisclosure(false);
 const { t } = useTranslation();
 const [currentPage, setCurrentPage] = useState(1);
 const rowsPerPage = 10;
 const totalPages = Math.ceil(data?.length / rowsPerPage);
 const handleCompanyUsersChange = (CompanyId, value) => {
  // Update evidenceFile with the file
  SetSelectedCompanyUsers((prev) => ({ ...prev, [CompanyId]: value }));
 };
 const handleCompanyAccessChange = (CompanyId, value) => {
  // Update evidenceFile with the file
  setViewAccess((prev) => ({ [CompanyId]: value }));
 };
 const handleAccessUserRow = (CompanyId, value) => {
  // Update evidenceFile with the file
  setAccessUserValue((prev) => ({ [CompanyId]: value }));
 };
 const update_user_access = async (company_id, accessValue) => {
  console.log(accessValue);
  const value = {
   company_id,
  };
  try {
   setAccessUserLoading(true);
   const { data } = await ApiProfile.put(
    `/admin/company/${
     accessValue === "enable"
      ? "enable"
      : accessValue === "disable" && "disable"
    }`,
    value
   );
   showNotification({
    message: data.message,
    color: "green",
   });
   setAccessUserLoading(false);
   accessClose();
   setAccessUserValue();
   getAllCompanyAccounts();
   // setData(data);
   console.log(data);
  } catch (error) {
   setAccessUserLoading(false);
   error.response?.data.message &&
    showNotification({
     message: error.response?.data.message,
     color: "red",
    });
   error.response?.data.error &&
    showNotification({
     message: error.response?.data.error,
     color: "red",
    });
   console.log(error);
  }
 };
 const currentData = useMemo(() => {
  return (
   (data &&
    data?.slice((currentPage - 1) * rowsPerPage, currentPage * rowsPerPage)) ||
   []
  );
 }, [data, currentPage, rowsPerPage]);
 const rows = currentData?.map((item, idx) => {
  console.log("🚀 ~ rows ~ item:", item,idx)
  let CompanyId = item.company_info.companyId;
  return (
   <>
    <Table.Tr
     key={`${idx + item.company_info.companyName}`} // Use the unique ID as the key
     className={` text-sm font-bold text-[#626364] text-center`}
    >
     <Table.Td>
      <TextInput
       variant={"unstyled"}
       readOnly
       classNames={{
        input: "cursor-default",
       }}
       value={item.company_info.companyName}
       //   defaultValue={item?.userName}
      />
     </Table.Td>
     <Table.Td>
      <TextInput
       variant={"unstyled"}
       readOnly
       classNames={{
        input: "cursor-default",
       }}
       //   defaultValue={item?.email}
       value={item.company_info.contactEmail}
      />
     </Table.Td>

     <Table.Td className="flex justify-center">
      <div className=" text-center  ">
       <Button
        className=" bg-gray-300 hover:bg-gray-300 text-gray-700 hover:text-gray-700 
          rounded-full"
        onClick={() => {
         open();
         SetSelectedCompanyId(CompanyId);
         handleCompanyUsersChange(CompanyId, item.users);
        }}
       >
        <GoDatabase className="me-2" /> View Users
       </Button>
      </div>
     </Table.Td>

     <Table.Td className="">
      <div className="text-center">
       <Button
        className=" bg-gray-300 hover:bg-gray-300 text-gray-700 hover:text-gray-700 
          rounded-full"
        onClick={() => {
         ViewAccessOpen();
         handleCompanyAccessChange(CompanyId, item.access);
         SetSelectedCompanyId(CompanyId);
        }}
       >
        <GoDatabase className="me-2" /> View Access
       </Button>
      </div>
     </Table.Td>
     <Table.Td>
      <div className="flex justify-center gap-3">
       <Button
        className={
         !item.company_info.enabled
          ? "bg-primary hover:bg-primary"
          : "bg-gray-500 hover:bg-gray-500 cursor-not-allowed opacity-40"
        }
        size="xs"
        disabled={item.company_info.enabled}
        onClick={() => {
         accessOpen();
         SetSelectedCompanyId(CompanyId);
         handleAccessUserRow(CompanyId, "enable");
        }}
       >
        Enable
       </Button>
       <Button
        className={
         item.company_info.enabled
          ? "bg-red-700 hover:bg-red-700"
          : "bg-gray-500 hover:bg-gray-500 cursor-not-allowed opacity-40"
        }
        size="xs"
        disabled={!item.company_info.enabled}
        onClick={() => {
         accessOpen();
         SetSelectedCompanyId(CompanyId);
         handleAccessUserRow(CompanyId, "disable");
        }}
       >
        Disable
       </Button>
      </div>
     </Table.Td>
    </Table.Tr>
   </>
  );
 });
 return (
  <>
   {tableLoading ? (
    <Loading />
   ) : (
    <>
     <div className="items-center justify-between flex mt-5">
      <Input
       type="text"
       placeholder={t("SearchPlaceholder")} // Translated placeholder
       className="border border-gray-300 rounded-md sm:w-auto focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-10/12 lg:w-1/4"
       size="md"
       leftSection={<CiSearch />}
      />
     </div>
     <div className="p-5 bg-white rounded-lg mt-7">
      <div className="w-full">
       <Button
        className="bg-[#05808b57] border-2 border-primary font-bold text-primary hover:bg-[#05808b57] hover:text-primary w-full"
        size="lg"
        onClick={() => addUserOpen()}
       >
        {t("+ Add Company")} {/* Translated button text */}
       </Button>
      </div>

      <>
       <ScrollArea>
        <Table miw={800} verticalSpacing="sm">
         <Table.Thead className="pb-6 text-base font-bold text-center border-b-2 border-primary text-primary">
          <Table.Tr>
           {/* <Table.Th className="text-start"></Table.Th>
                    <Table.Th style={{ width: rem(40) }}>
                     
                    </Table.Th> */}
           <Table.Th className="text-start">
            {t("Client")} {/* Translated table header */}
           </Table.Th>
           <Table.Th className="text-start">
            {t("Email")} {/* Translated table header */}
           </Table.Th>
           <Table.Th className="text-center">
            {t("View Users")} {/* Translated table header */}
           </Table.Th>
           <Table.Th className="text-center">
            {t("View Access")} {/* Translated table header */}
           </Table.Th>
           {/* <Table.Th className="text-center">
                        {t("Delete")} 
                      </Table.Th> */}
           <Table.Th className="text-center">
            {t("Enable/Disable")} {/* Translated table header */}
           </Table.Th>
          </Table.Tr>
         </Table.Thead>
         <Table.Tbody> {rows} </Table.Tbody>
        </Table>
       </ScrollArea>
       <div className="md:flex justify-between mt-5">
        <p
         className="text-sm text-secondary-300"
         //   hidden={data?.length}
        >
         {t("showingData", {
          start: (currentPage - 1) * rowsPerPage + 1,
          end: Math.min(currentPage * rowsPerPage, data?.length),
          total: data?.length,
         })}
        </p>
        <Pagination
         page={currentPage}
         onChange={(e) => {
          setCurrentPage(e);
         }}
         total={totalPages}
         color="#00c0a9"
        />
       </div>
      </>

      {/* <!-- Pagination --> */}
     </div>
     <AddCompanyModel
      close={addUserClose}
      opened={addUserOpened}
      show_company_users={getAllCompanyAccounts}
      SetSelectedUserId={SetSelectedCompanyId}
     />
     {opened &&
      selectedCompanyId &&
      selectedCompanyUsers[selectedCompanyId] && (
       <ViewCompanyUsersModal
        close={close}
        opened={opened}
        SetSelectedCompanyUsers={SetSelectedCompanyUsers}
        SetSelectedCompanyId={SetSelectedCompanyId}
        selectedCompanyId={selectedCompanyId}
        selectedCompanyUsers={
         selectedCompanyId && selectedCompanyUsers[selectedCompanyId]
        }
        getAllCompanyAccounts={getAllCompanyAccounts}
       />
      )}
     {ViewAccessOpened &&
      selectedCompanyId &&
      selectedViewAccess[selectedCompanyId] && (
       <ViewAccessModal
        close={ViewAccessClose}
        opened={ViewAccessOpened}
        data={selectedCompanyId && selectedViewAccess[selectedCompanyId]}
        SetSelectedViewAccess={setViewAccess}
        SetSelectedCompanyId={SetSelectedCompanyId}
        selectedCompanyId={selectedCompanyId}
        getAllCompanyAccounts={getAllCompanyAccounts}
       />
      )}
     {/* {selectedCompanyId && (
              <ActionCompanyModal
                close={DelClose}
                opened={DelOpened}
                Action_user={delete_user}
                selectedCompanyId={selectedCompanyId}
                ActionLoading={delLoading}
                SetSelectedUserId={SetSelectedUserId}
                mainTitle={"Are you sure to Delete this account"}
              />
            )} */}
     {accessOpened &&
      selectedCompanyId &&
      accessUserValue[selectedCompanyId] && (
       <ActionCompanyModal
        close={accessClose}
        opened={accessOpened}
        Action_user={update_user_access}
        ActionLoading={accessUserLoading}
        selectedCompanyId={selectedCompanyId}
        SetSelectedUserId={SetSelectedCompanyId}
        mainTitle={`Are you sure to '${accessUserValue[selectedCompanyId]}' this account`}
        ActionValue={accessUserValue[selectedCompanyId]}
        setAccessUserValue={setAccessUserValue}
       />
      )}
    </>
   )}
  </>
 );
}
