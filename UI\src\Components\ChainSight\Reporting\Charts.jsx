import {
  Radar, Line
} from 'react-chartjs-2';
import {
  Chart as ChartJS,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale
} from 'chart.js';

ChartJS.register(
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale
);

export default function Charts({ latestReport }) {
  const labels = latestReport?.sections?.map((section) => section.title);
  const data = latestReport?.sections?.map((section) => (section.percentage * 5));
  const lineLabels = latestReport?.historical_progress?.map((item) => item.createdAt.slice(0, 10));
  const scores = latestReport?.historical_progress?.map((item) => item.score);

  const radarData = {
    labels: labels,
    datasets: [
      {
        label: 'Performance',
        data: data,
        backgroundColor: 'rgba(7, 131, 143, 0.5)',
        borderColor: '#07838F',
        pointBackgroundColor: '#07838F80',
        borderWidth: 2,
      }
    ]
  };

  const radarOptions = {
    scales: {
      r: {
        angleLines: { display: true },
        suggestedMin: 0,
        suggestedMax: 5,
        ticks: {
          stepSize: 1,
          color: '#525252',
        },
        pointLabels: {
          color: '#525252',
          font: { size: 12, weight: 'bold' }
        }
      }
    },
    plugins: {
      legend: { display: false }
    },
    responsive: true,
    maintainAspectRatio: false,
  };

  const lineData = {
    labels: lineLabels,
    datasets: [
      {
        label: 'Maturity Score',
        data: scores,
        fill: true,
        backgroundColor: 'rgba(7, 131, 143, 0.3)',
        borderColor: '#07838F',
        tension: 0.3,
        pointBackgroundColor: '#07838F80',
        borderWidth: 2,
      }
    ]
  };

  const lineOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        max: 200,
        ticks: {
          color: '#6b7280',
        }
      },
      x: {
        ticks: {
          color: '#6b7280',
        }
      }
    },
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          color: '##525252'
        }
      }
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
      <div className="bg-white rounded-xl border-2 p-5">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">Category Performance</h2>
        <div className="h-96">
          <Radar data={radarData} options={radarOptions} />
        </div>
      </div>

      <div className="bg-white rounded-xl border-2 p-5">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">Historical Progress</h2>
        <div className="h-96">
          <Line data={lineData} options={lineOptions} />
        </div>
      </div>
    </div>
  );
}
