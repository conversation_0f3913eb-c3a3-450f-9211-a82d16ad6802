import useSideBarRoute from "@/hooks/useSideBarRoute";
import MainLayout from "@/Layout/MainLayout";
import { Link, useLocation } from "react-router-dom";
import useRoutes from "@/Routes/useRoutes";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { TextInput, FileInput, Group, Button, Alert } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { Modal } from "@mantine/core";
import { useEffect, useState } from "react";
import { IconFileCv } from "@tabler/icons-react";
import ViewPDF from "../S2ZeroNetPages/NetZeroReport/Components/ViewPDF";
import ApiS3 from "@/Api/apiS3";
import { IoMdHome } from "react-icons/io";

export default function ResourcesDashboard() {
  const icon = (
    <IconFileCv style={{ width: "30px", height: "30px" }} stroke={1.5} />
  );
  const { t } = useTranslation();
  const { GreenHubMenu } = useSideBarRoute();
  const {
    GreenHubResourcesDashboard,
    GreenHubAcademyDashboard,
    GreenHubCommunityDashboard,
  } = useRoutes().GreenHubMap;
  const location = useLocation();
  const [editTopic, setEditTopic] = useState(null);
  const [loading, setLoading] = useState(false);
  const [opened, { open, close }] = useDisclosure(false);
  const [data, setData] = useState([]);
  const [image, setImage] = useState(null);
  const [previewImage, setPreviewImage] = useState(null); // For image preview
  const [error, setError] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);

  // Initialize form with validation
  const form = useForm({
    initialValues: {
      title: "unknown topic",
      content: "",
      image: null,
      files: [],
    },
    validate: {
      // title: (value) => (value ? null : t('Title is required')),
      // content: (value) => (value ? null : t('Content is required')),
      // image: (value) => (value && /\.(jpg|jpeg|png)$/i.test(value.name) ? null : t('Please upload a valid image (JPG, JPEG, PNG)')),
      // files: (value) => (value && value.every((file) => /\.pdf$/i.test(file.name)) ? null : t('Please upload valid PDF files')),
    },
  });

  // Fetch topics from the API
  const getTopics = async () => {
    try {
      const response = await ApiS3.get("/greenhub-resourses");
      setData(response.data);
    } catch (error) {
      console.error("Error fetching topics:", error);
      setError(t("Error fetching topics"));
    }
  };

  useEffect(() => {
    getTopics();
  }, []);

  useEffect(() => {
    if (editTopic) {
      form.setValues({
        title: editTopic.title,
        content: editTopic.content,
        image: editTopic.image,
        files: editTopic.files,
      });
      setIsEditMode(true);
    } else {
      form.reset();
      setIsEditMode(false);
    }
  }, [editTopic]);

  const editTopicHandler = async (values) => {
    setLoading(true);
    try {
      const formData = new FormData();
      formData.append("topicName", values.title);
      formData.append("content", values.content);
      formData.append("image", values.image);
      values.files.forEach((file) => formData.append("files", file));
      const response = await ApiS3.patch(
        `/greenhub-resourses/${editTopic._id}`,
        formData
      );
      //console.log("Topic edited:", response);
      getTopics(); // Refresh topics after editing
      close(); // Close the modal
      setLoading(false);
      setIsEditMode(false); // Exit edit mode
    } catch (error) {
      console.error("Error editing topic:", error);
      setError(t("Error editing topic"));
      setLoading(false);
    }
  };

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      const formData = new FormData();
      formData.append("topicName", values.title);
      formData.append("content", values.content);
      formData.append("image", values.image);
      values.files.forEach((file) => formData.append("files", file));
      const response = await ApiS3.post("/greenhub-resourses", formData);
      //console.log("Topic added:", response);
      getTopics(); // Refresh topics after adding
      close(); // Close the modal
      setLoading(false);
    } catch (error) {
      console.error("Error adding topic:", error);
      setError(t("Error adding topic"));
      setLoading(false);
    }
  };

  const deleteTopic = async (id) => {
    try {
      const response = await ApiS3.delete(`/greenhub-resourses/${id}`);
      //console.log("Topic deleted:", response);
      getTopics(); // Refresh topics after deleting
    } catch (error) {
      console.error("Error deleting topic:", error);
      setError(t("Error deleting topic"));
    }
  };

  return (
    <MainLayout menus={GreenHubMenu} navbarTitle={t("GreenHub")}
    breadcrumbItems={[
      { title: <IoMdHome size={20} />, href: "/get-started" },
      { title: "Resources", href: "#" },
    ]}>
      <div className="h-full overflow-y-auto">
        {/* Header */}
        <h1
          className={`px-8 py-4 text-xl font-bold bg-white rounded-2xl text-blue-950 mb-7`}
        >
          {t("Manage Green Hub Resources")}
        </h1>

        {/* Navigation Links */}
        <div className="px-8 py-4 bg-white rounded-2xl text-blue-950 mb-7 flex justify-around">
          {[
            {
              to: GreenHubAcademyDashboard.path,
              label: t("GreenHub Academy"),
              key: "academy",
            },
            {
              to: GreenHubResourcesDashboard.path,
              label: t("GreenHub Resources"),
              key: "resourses",
            },
            {
              to: GreenHubCommunityDashboard.path,
              label: t("GreenHub Peers Community"),
              key: "community",
            },
          ].map(({ to, label, key }) => {
            const isActive = location.pathname.split("/")[2] === key;
            const baseClasses =
              "lg:p-3 w-[200px] lg:w-[400px] text-center rounded-xl shadow-xl lg:text-[24px] font-bold";
            const activeClasses = isActive
              ? "bg-[#05808b] text-white"
              : "bg-[#07838F33] text-[#05808b]";
            return (
              <DashboardLink
                key={key}
                to={to}
                label={label}
                className={`${baseClasses} ${activeClasses}`}
              />
            );
          })}
        </div>

        {/* Form Section */}
        <div className="resources">
          <div className="form p-5 m-auto xl:w-[75%]">
            <Button variant="default" onClick={open}>
              {t("Add Topic")}
            </Button>

            {/* Error Alert */}
            {error && (
              <Alert title={t("Error")} color="red" mt="md">
                {error}
              </Alert>
            )}

            {/* Topics List */}
            <div className="topics mt-5">
              {data.length > 0 ? (
                data.map((item, index) => (
                  <TopicCard
                    key={index}
                    item={item}
                    openEditModal={open}
                    setEditTopic={setEditTopic}
                    deleteTopic={deleteTopic}
                  />
                ))
              ) : (
                <p>{t("No Topics To show")}</p>
              )}
            </div>

            {/* Modal for Adding/Editing Topics */}
            <Modal
              opened={opened}
              onClose={() => {
                close();
                setIsEditMode(false); // Reset edit mode when modal is closed
              }}
              title={isEditMode ? t("Edit Topic") : t("Add New Topic")}
              centered
              size="xl"
            >
              <form
                onSubmit={form.onSubmit(
                  isEditMode ? editTopicHandler : handleSubmit
                )}
              >
                <TextInput
                  withAsterisk
                  label={t("Title")}
                  name="title"
                  placeholder={t("Topic Title")}
                  {...form.getInputProps("title")}
                />
                <TextInput
                  withAsterisk
                  label={t("Content")}
                  name="content"
                  placeholder={t("Write the content of the topic here")}
                  {...form.getInputProps("content")}
                />
                <FileInput
                  withAsterisk
                  label={t("Topic's Image")}
                  description={t("Choose an image for the topic")}
                  placeholder={t("Click to choose an image")}
                  accept=".jpg,.jpeg,.png"
                  name="image"
                  onChange={(e) => {
                    setImage(e);
                    form.setFieldValue("image", e);
                    // Preview image
                    const reader = new FileReader();
                    reader.onloadend = () => setPreviewImage(reader.result);
                    if (e) reader.readAsDataURL(e);
                  }}
                  {...form.getInputProps("image")}
                />
                {previewImage && (
                  <div className="preview mt-3">
                    <img src={previewImage} alt="Image preview" width="200" />
                  </div>
                )}
                <FileInput
                  leftSection={icon}
                  withAsterisk
                  label={t("Topic's Files")}
                  description={t("Choose PDF files for the topic")}
                  placeholder={t("Click to choose files")}
                  accept=".pdf"
                  name="files"
                  multiple
                  onChange={(e) => {
                    form.setFieldValue("files", e);
                  }}
                  {...form.getInputProps("files")}
                />

                <Group justify="flex-end" mt="md">
                  <Button disabled={loading} type="submit">
                    {isEditMode ? t("Update") : t("Submit")}
                  </Button>
                </Group>
              </form>
            </Modal>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}

// Component for Navigation Links
function DashboardLink({ to, label, className }) {
  return (
    <Link to={to} className={className}>
      {label}
    </Link>
  );
}

// Component for Topic Cards
function TopicCard({ item, openEditModal, setEditTopic, deleteTopic }) {
  return (
    <div className="topic bg-white p-5 rounded-xl flex gap-5 mb-5">
      <div className="image w-[400px]">
        <img src={item.image} alt={item.title} />
      </div>
      <div className="text flex flex-col justify-between gap-5 flex-1">
        <div className="flex flex-col justify-between flex-1 p-3">
          <div>
            <h1>{item.topicName}</h1>
            <p>{item.content}</p>
          </div>
          <span>
            <ul>
              {item?.files.map((file, index) => {
                return (
                  <li key={index}>
                    <ViewPDF
                      pdfUrl={file}
                      text={`view ${item.topicName} report ${index + 1}`}
                      btnStyle="text-[#07838F] text-xl flex gap- items-center flex-row-reverse gap-2"
                    />
                  </li>
                );
              })}
            </ul>
          </span>
        </div>
        <div className="flex gap-5 ">
          <Button
            variant="default"
            onClick={() => {
              setEditTopic(item);
              openEditModal();
            }}
          >
            Edit Topic
          </Button>
          <Button
            variant="default"
            onClick={() => {
              deleteTopic(item._id);
            }}
          >
            Delete Topic
          </Button>
        </div>
      </div>
    </div>
  );
}
