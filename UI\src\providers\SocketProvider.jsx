import { useEffect } from 'react';
import { useNotificationStore } from '../Store/useNotificationStore';
import { notifications } from '@mantine/notifications';
import { IconX } from '@tabler/icons-react';
import { Button } from '@mantine/core';
import { useSocket } from '@/hooks/useSocket';
import { Outlet } from 'react-router';
import { CheckCircle } from 'lucide-react';

export const SocketProvider = ({ children }) => {

  const { socket } = useSocket();
  useEffect(() => {
    if(socket == null){
      notifications.show({
        id: 'offline-notification', 
        title: 'Connection Lost',
        message: 'You have been disconnected. Please refresh to reconnect.',
        color: 'red',
        icon: <IconX />,
        withCloseButton: socket?.connected,
        autoClose: socket?.connected,
        actionButton: (
          <Button onClick={() => window.location.reload()} size="xs">
            Refresh Page
          </Button>
        ),
      });
    }
  }, [socket]);
  
  useEffect(() => {
    // Initialize socket connection
    let offlineNotificationId = null;

    // Connection handlers
    const handleConnect = () => {
      useNotificationStore.getState().setOnline();
      console.log("Connected");
      if (offlineNotificationId) {
        notifications.hide({ id: 'offline-notification' });
        notifications.show({
          title: 'Connection Restored',
          message: 'You have been reconnected. Please refresh to reconnect.',
          color: 'green',
          icon: <CheckCircle />,
          autoClose: 5000,
        });
        offlineNotificationId = null;
      }
    };

    const handleDisconnect = () => {
      useNotificationStore.getState().setOffline();
      // Show persistent offline notification
      offlineNotificationId = notifications.show({
        id: 'offline-notification', 
        title: 'Connection Lost',
        message: 'You have been disconnected. Please refresh to reconnect.',
        color: 'red',
        icon: <IconX />,
        withCloseButton: true,
        autoClose: socket?.connected,
        actionButton: (
          <Button onClick={() => window.location.reload()} size="xs">
            Refresh Page
          </Button>
        ),
      });
    };

    // Notification handler
    const handleNotification = (data) => {
      useNotificationStore.getState().addNotification(data.notification);
      notifications.show({
        title: data.title,
        message: data.notification.message,
        color: 'blue',
        autoClose: 5000,
      });
    };

    // Error handler
    const handleError = (error) => {
      console.error('Socket connection error:', error);
      notifications.show({
        title: 'Connection Error',
        message: 'An error occurred with the connection',
        color: 'red',
      });
    };

    // Setup listeners
    socket.on('connect', handleConnect);
    socket.on('disconnect', handleDisconnect);
    socket.on('notification', handleNotification);
    socket.on('connect_error', handleError);

    // Cleanup
    return () => {
      socket.off('connect', handleConnect);
      socket.off('disconnect', handleDisconnect);
      socket.off('notification', handleNotification);
      socket.off('connect_error', handleError);
      socket.disconnect();
    };
  }, [socket]);

  if(socket == null){
    return <Outlet />;
  }

  return <Outlet />;
};