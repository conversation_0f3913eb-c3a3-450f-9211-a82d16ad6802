import React from "react";
import dayjs from "dayjs";
import SPGraphkey from "./SPGraphkey";
// import <PERSON><PERSON>hart from "./SPChart";
import { RadarChart } from "@mantine/charts";
// import { forecastData, radarData } from "./data";
import { radarData } from "./data";
import Forcast from "@/assets/images/forcast.png";

export default function ScenarioPlanning() {
  const today = dayjs();
  const pastDates = [];
  for (let i = 1; i <= 5; i++) {
    pastDates.push(today.subtract(i, "day"));
  }

  const handleSwitchChange = (seriesKey) => {
    (prevState) => ({
      ...prevState,
      [seriesKey]: !prevState[seriesKey],
    });
  };

  return (
    <div className="grid grid-cols-4 gap-2 mt-4 ">
      <div className="col-span-3  p-2">
        {/* <SPChart data={forecastData} onSwitchChange={handleSwitchChange} /> */}
        <img src={Forcast} />
      </div>
      <div className="bg-[#fff] rounded-xl shadow-lg p-2">
        <SPGraphkey onSwitchChange={handleSwitchChange} />
      </div>
      <div className="col-span-4 ">
        <p className="mb-2 font-bold">Latest Results</p>
        <div className="grid grid-cols-6 gap-2 py-4 px-20 rounded-xl mb-2 shadow-lg border-2 border-[#fff]">
          <p className="text-[#07838F] cursor-pointer">Latest results</p>
          {pastDates.map((date, index) => (
            <p key={index} className="text-[#9C9C9C] cursor-pointer">
              {date.format("DD MMM, YYYY")}
            </p>
          ))}
        </div>
      </div>
      <div className="col-span-4 bg-[#fff] rounded-lg shadow-lg p-2">
        <div className="grid grid-cols-5 gap-4">
          {radarData.map((scenario, index) => (
            <div key={index} className="p">
              <RadarChart
                h={200}
                data={scenario.sales.map((sale, idx) => ({
                  product: scenario.product[idx],
                  sales: sale,
                }))}
                dataKey="product"
                withPolarAngleAxis={false}
                withPolarRadiusAxis={false}
                series={[
                  { name: "sales", color: "#07838F80", strokeColor: "#07838F" },
                ]}
              />
              <p className="mt text-center font-semibold">
                {scenario.scenario}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
