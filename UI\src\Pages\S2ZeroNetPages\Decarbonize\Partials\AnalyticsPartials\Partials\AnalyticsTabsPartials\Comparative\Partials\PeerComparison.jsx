import React from "react";
import { RadarChart } from "@mantine/charts";

const PeerComparison = () => {
  const data = [
    {
      product: "Reduced emissions",
      sales: 120,
    },
    {
      product: "Reduceed emissions",
      sales: 98,
    },
    {
      product: "Renewable energy use",
      sales: 86,
    },
    {
      product: "Reduction rate",
      sales: 99,
    },
    {
      product: "Reductioon rate",
      sales: 85,
    },
  ];

  return (
    <div>
      <div className="flex items-center justify-between p-2 mb-4">
        <h3 className="text-2xl font-semibold me-9">Peer Comparison</h3>
      </div>

      <RadarChart
        className="bg-white py-8 px-4 rounded-lg shadow-lg"
        h={350}
        w={450}
        data={data}
        dataKey="product"
        series={[
          { name: "sales", color: "blue.4", opacity: 0.2, strokeColor: "blue" },
        ]}
      />
    </div>
  );
};

export default PeerComparison;
