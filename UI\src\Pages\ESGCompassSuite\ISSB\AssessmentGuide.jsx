import {
    Box,
    Title,
    Text,
    List,
    Group,
    ThemeIcon,
    Stack,
    Paper,
} from "@mantine/core";
import { FaCheck } from "react-icons/fa";

export default function IssbReadinessGuide() {
    return (
        <Box mx="auto" maxWidth="1200px" p="md">
            <Title order={1} mb="xl">
                ISSB Readiness: Readiness Assessment User Guide
            </Title>

            {/* Overview Section */}
            <Stack spacing="lg">
                <Text size="lg" mb="sm">
                    <b>Overview</b>
                </Text>
                <Text c="dimmed">
                    ISSB Readiness assesses your organization's alignment with
                    the International Sustainability Standards Board (ISSB)
                    standards. The Readiness Assessment page is your hub for
                    evaluating your sustainability reporting practices, and The
                    Reporting page generates and shares detailed reports based
                    on your assessment.
                </Text>

                {/* Getting Started Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Getting Started</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>
                            Accessing the Readiness Assessment Page
                        </Text>
                        <List listStyleType="circle" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={400}>
                                    Login: Log into the system using your
                                    credentials
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={400}>
                                    Navigate: From the main menu, select
                                    Regulatory Readiness and then ISSB
                                    Readiness.
                                </Text>
                            </List.Item>
                        </List>
                    </List.Item>
                </List>

                {/* Page Structure Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Page Structure</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>
                            The Readiness Assessment page includes:
                        </Text>
                        <List listStyleType="circle" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={400}>
                                    Assessment Questions: Questions to evaluate
                                    your alignment with ISSB standards.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={400}>
                                    Editable Responses: To review and update
                                    previous answers.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={400}>
                                    Use Search/Filter: Use the search bar to
                                    find specific topics or filter questions by
                                    columns for targeted review.
                                </Text>
                            </List.Item>
                        </List>
                    </List.Item>
                </List>

                {/* Interacting with the Assessment Page Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Interacting with the Assessment Page</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>Answer Questions:</Text>
                        <Text size="sm">
                            Respond to questionnaires like ESRs E1 (Climate
                            Change) to evaluate readiness by selecting:
                        </Text>
                        <List listStyleType="circle" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={700}>Assessment Level</Text> for each
                                question in each topic area:
                                <List
                                    listStyleType="disc"
                                    spacing="sm"
                                    ml="1.5rem"
                                >
                                    <List.Item>
                                        <Text fw={700}>Not Started:</Text> No
                                        action has been taken.
                                    </List.Item>
                                    <List.Item>
                                        <Text fw={700}>Not Applicable:</Text>{" "}
                                        The question does not apply to your
                                        organization.
                                    </List.Item>
                                    <List.Item>
                                        <Text fw={700}>Initial Planning:</Text>{" "}
                                        Planning is underway but not yet
                                        implemented.
                                    </List.Item>
                                    <List.Item>
                                        <Text fw={700}>In Development:</Text>{" "}
                                        Active development or preparation is in
                                        progress.
                                    </List.Item>
                                    <List.Item>
                                        <Text fw={700}>
                                            Partially Implemented:
                                        </Text>{" "}
                                        Some measures are in place but not fully
                                        effective.
                                    </List.Item>
                                    <List.Item>
                                        <Text fw={700}>Fully Implemented:</Text>{" "}
                                        Complete and effective measures are in
                                        place.
                                    </List.Item>
                                </List>
                            </List.Item>
                        </List>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Priority:</Text>
                        <Text size="sm">
                            Assign a priority level to each question:
                        </Text>
                        <List listStyleType="circle" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={700}>Top:</Text> Critical and requires
                                immediate attention.
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>High:</Text> Important and needs
                                prompt action.
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Medium:</Text> Requires action
                                within a reasonable timeframe.
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Low:</Text> Can be addressed with
                                lower urgency.
                            </List.Item>
                        </List>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Evidence:</Text>
                        <Text size="sm">
                            Upload or link supporting documents, data, or
                            records (e.g., audit reports, training logs) to
                            substantiate your assessment level and priority.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Action Items:</Text>
                        <Text size="sm">
                            Define specific tasks or steps needed to address
                            gaps (e.g., "Conduct staff training on human rights
                            policies," "Review supplier contracts for
                            compliance").
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Owner:</Text>
                        <Text size="sm">
                            Assign a responsible individual or team (e.g., "HR
                            Manager," "Sustainability Team") to oversee each
                            action item.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Tags:</Text>
                        <Text size="sm">
                            Add labels to categorize questions or actions (e.g.,
                            "Labor Rights," "Supply Chain," "Urgent") for easier
                            tracking and filtering.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Due Date:</Text>
                        <Text size="sm">
                            Set a deadline for completing each action item
                            (e.g., "2025-06-15" for June 15, 2025).
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Edit Responses:</Text>
                        <Text size="sm">
                            Update answers to reflect recent changes in
                            practices.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Use Search/Filter:</Text>
                        <Text size="sm">
                            Use the search bar to find specific topics or filter
                            questions by columns for targeted review.
                        </Text>
                    </List.Item>
                </List>

                {/* Understanding Progress Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Understanding Your Progress</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>Progress Overview:</Text>
                        <List listStyleType="disc" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={700}>ISSB Alignment:</Text>
                                <Text size="sm">
                                    Questions cover your organization's current
                                    sustainability reporting practices.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Editable Insights:</Text>
                                <Text size="sm">
                                    Updated responses ensure your assessment
                                    reflects the latest data.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Focused Analysis:</Text>
                                <Text size="sm">
                                    Search and filter options help you
                                    prioritize key ISSB areas.
                                </Text>
                            </List.Item>
                        </List>
                    </List.Item>
                </List>

                {/* Next Steps Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Next Steps</b>
                </Text>
                <List listStyleType="decimal" spacing="sm">
                    <List.Item>
                        <Text>
                            Answer all questions to evaluate your ISSB readiness
                            thoroughly.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Use search/filter tools to revisit and update
                            specific topics as needed.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Save your progress by clicking Save and assess with
                            the Assess button.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Proceed to the Reporting page to view your results.
                        </Text>
                    </List.Item>
                </List>

                {/* Tip Section */}
                <Paper shadow="sm" p="md" radius="md" mt="xl" withBorder>
                    <Group position="apart">
                        <ThemeIcon variant="light" radius="xl" size="2rem">
                            <FaCheck size="1.2rem" />
                        </ThemeIcon>
                        <Text fw={500}>
                            Regularly update your answers to align with the
                            latest ISSB standards!
                        </Text>
                    </Group>
                </Paper>
            </Stack>
        </Box>
    );
}
