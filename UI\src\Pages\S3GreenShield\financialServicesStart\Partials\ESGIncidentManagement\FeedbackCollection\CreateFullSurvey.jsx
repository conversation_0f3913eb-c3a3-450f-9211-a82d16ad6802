import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import ApiS3 from "@/Api/apiS3";
import S3Layout from "@/Layout/S3Layout";
import MainLayout from "@/Layout/MainLayout";

import { notifications } from "@mantine/notifications";
import { IconCheck, IconX } from "@tabler/icons-react";

// Import tab components
import SurveyBuilder from "./SurveyBuilder";
import SurveyPreview from "./SurveyPreview";
import SurveyDistribute from "./SurveyDistribute";
// import SurveyResults from "./SurveyResults";

export default function CreateFullSurvey({ onClose, surveyData }) {
  const isEditing = !!surveyData;

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    control,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      title: "",
      description: "",
      oneTimeresponse: true,
      target: "",
      isActive: true,
    },
  });

  const [activeTab, setActiveTab] = useState("Builder");
  const [groups, setGroups] = useState([
    {
      id: Date.now(),
      groupTitle: "",
      groupDescription: "",
      questions: [
        { text: "", type: "checkbox", options: [""], required: false },
      ],
    },
  ]);

  // Initialize form with existing survey data if editing
  useEffect(() => {
    if (surveyData) {
      reset({
        title: surveyData.title || "",
        description: surveyData.description || "",
        oneTimeresponse: surveyData.oneTimeresponse ?? true,
        target: surveyData.target || "",
        isActive: surveyData.isActive ?? true,
      });

      if (surveyData.groups && surveyData.groups.length > 0) {
        // Ensure each group has an id field, using the server-provided id or generating one
        const formattedGroups = surveyData.groups.map((group) => ({
          ...group,
          id: group._id || group.id || Date.now(),
          questions: (group.questions || []).map((q) => ({
            ...q,
            options: q.options || (q.type !== "text" ? [""] : []),
          })),
        }));
        setGroups(formattedGroups);
      }
    }
  }, [surveyData, reset]);

  const oneTimeresponse = watch("oneTimeresponse");

  const onSubmit = async (data) => {
    try {
      // Make sure every group has a questions array
      const validGroups = groups.map((group) => ({
        groupTitle: group.groupTitle || "",
        groupDescription: group.groupDescription || "",
        questions: (group.questions || []).map((q) => ({
          text: q.text || "",
          type: q.type || "text",
          required: q.required || false,
          ...(q.type !== "text" && { options: q.options || [] }),
        })),
      }));

      const payload = {
        title: data.title || "",
        description: data.description || "",
        target: data.target || "",
        oneTimeresponse: !!data.oneTimeresponse,
        groups: validGroups,
      };

      let response;

      if (isEditing) {
        // Update existing survey
        console.log("Updating survey data:", payload);
        response = await ApiS3.put(`surveys/${surveyData._id}`, payload);
        console.log("Survey updated:", response.data);

        notifications.show({
          title: "Success!",
          message: "Survey updated successfully.",
          color: "teal",
          icon: <IconCheck size={18} />,
        });
      } else {
        // Create new survey
        console.log("Creating new survey:", payload);
        response = await ApiS3.post("surveys", payload);
        console.log("Survey created:", response.data);

        notifications.show({
          title: "Success!",
          message: "Survey created successfully.",
          color: "teal",
          icon: <IconCheck size={18} />,
        });
      }

      if (onClose) {
        onClose();
      }
    } catch (error) {
      notifications.show({
        title: "Error!",
        message: isEditing
          ? "Failed to update survey."
          : "Failed to create survey.",
        color: "red",
        icon: <IconX size={18} />,
      });

      console.error("Submission error:", {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
    }
  };

  const handleAddGroup = async () => {
    const newGroup = {
      id: Date.now(), // Generate a unique temporary ID
      groupTitle: "",
      groupDescription: "",
      questions: [
        { text: "", type: "checkbox", options: [""], required: false },
      ],
    };

    if (isEditing) {
      try {
        // Add group to existing survey via API
        const response = await ApiS3.post(`surveys/${surveyData._id}/groups`, {
          groupTitle: newGroup.groupTitle,
          groupDescription: newGroup.groupDescription,
          questions: newGroup.questions,
        });

        // Use the returned group with server-assigned ID
        const addedGroup = {
          ...response.data,
          id: response.data._id, // Ensure we have an id field for our UI
        };

        setGroups([...groups, addedGroup]);

        notifications.show({
          title: "Success",
          message: "Group added successfully.",
          color: "teal",
          icon: <IconCheck size={18} />,
        });
      } catch (error) {
        console.error("Error adding group:", error);
        notifications.show({
          title: "Error",
          message: "Failed to add group.",
          color: "red",
          icon: <IconX size={18} />,
        });

        // Still add the group locally in case of API failure
        setGroups([...groups, newGroup]);
      }
    } else {
      // For new surveys, just add to local state
      setGroups([...groups, newGroup]);
    }
  };

  const updateGroupOnServer = async (groupId, updatedGroupData) => {
    if (!isEditing) return;

    try {
      const serverGroupId = groups.find((g) => g.id === groupId)?._id;
      if (!serverGroupId) return;

      await ApiS3.put(
        `surveys/${surveyData._id}/groups/${serverGroupId}`,
        updatedGroupData
      );
    } catch (error) {
      console.error("Error updating group:", error);
      notifications.show({
        title: "Warning",
        message: "Group updated locally but failed to sync with server.",
        color: "yellow",
      });
    }
  };

  const handleGroupTitleChange = (groupId, value) => {
    const updatedGroups = groups.map((group) => {
      if (group.id === groupId) {
        const updatedGroup = { ...group, groupTitle: value };

        // If editing existing survey, update on server
        if (isEditing && group._id) {
          updateGroupOnServer(groupId, {
            groupTitle: value,
            groupDescription: group.groupDescription,
            questions: group.questions,
          });
        }

        return updatedGroup;
      }
      return group;
    });
    setGroups(updatedGroups);
  };

  const handleGroupDescriptionChange = (groupId, value) => {
    const updatedGroups = groups.map((group) => {
      if (group.id === groupId) {
        const updatedGroup = { ...group, groupDescription: value };

        // If editing existing survey, update on server
        if (isEditing && group._id) {
          updateGroupOnServer(groupId, {
            groupTitle: group.groupTitle,
            groupDescription: value,
            questions: group.questions,
          });
        }

        return updatedGroup;
      }
      return group;
    });
    setGroups(updatedGroups);
  };

  const handleAddQuestion = (groupId) => {
    const updatedGroups = groups.map((group) => {
      if (group.id === groupId) {
        const updatedGroup = {
          ...group,
          questions: [
            ...group.questions,
            { text: "", type: "checkbox", options: [""], required: false },
          ],
        };

        // If editing existing survey, update on server
        if (isEditing && group._id) {
          updateGroupOnServer(groupId, {
            groupTitle: group.groupTitle,
            groupDescription: group.groupDescription,
            questions: updatedGroup.questions,
          });
        }

        return updatedGroup;
      }
      return group;
    });
    setGroups(updatedGroups);
  };

  const handleQuestionChange = (groupId, questionIndex, field, value) => {
    const updatedGroups = groups.map((group) => {
      if (group.id === groupId) {
        const newQuestions = [...group.questions];
        newQuestions[questionIndex][field] = value;

        if (field === "type" && value === "text") {
          newQuestions[questionIndex].options = [];
        }
        if (
          field === "type" &&
          (value === "radio" || value === "checkbox") &&
          (!newQuestions[questionIndex].options ||
            newQuestions[questionIndex].options.length === 0)
        ) {
          newQuestions[questionIndex].options = [""];
        }

        const updatedGroup = { ...group, questions: newQuestions };

        // If editing existing survey, update on server
        if (isEditing && group._id) {
          updateGroupOnServer(groupId, {
            groupTitle: group.groupTitle,
            groupDescription: group.groupDescription,
            questions: newQuestions,
          });
        }

        return updatedGroup;
      }
      return group;
    });
    setGroups(updatedGroups);
  };

  const handleOptionChange = (groupId, questionIndex, optionIndex, value) => {
    const updatedGroups = groups.map((group) => {
      if (group.id === groupId) {
        const newQuestions = [...group.questions];
        newQuestions[questionIndex].options[optionIndex] = value;

        const updatedGroup = { ...group, questions: newQuestions };

        // If editing existing survey, update on server
        if (isEditing && group._id) {
          updateGroupOnServer(groupId, {
            groupTitle: group.groupTitle,
            groupDescription: group.groupDescription,
            questions: newQuestions,
          });
        }

        return updatedGroup;
      }
      return group;
    });
    setGroups(updatedGroups);
  };

  const handleAddOption = (groupId, questionIndex) => {
    const updatedGroups = groups.map((group) => {
      if (group.id === groupId) {
        const newQuestions = [...group.questions];
        newQuestions[questionIndex].options.push("");

        const updatedGroup = { ...group, questions: newQuestions };

        // If editing existing survey, update on server
        if (isEditing && group._id) {
          updateGroupOnServer(groupId, {
            groupTitle: group.groupTitle,
            groupDescription: group.groupDescription,
            questions: newQuestions,
          });
        }

        return updatedGroup;
      }
      return group;
    });
    setGroups(updatedGroups);
  };

  const removeQuestion = (groupId, questionIndex) => {
    const updatedGroups = groups.map((group) => {
      if (group.id === groupId) {
        const newQuestions = [...group.questions];
        newQuestions.splice(questionIndex, 1);

        const updatedQuestions = newQuestions.length
          ? newQuestions
          : [{ text: "", type: "checkbox", options: [""], required: false }];

        const updatedGroup = { ...group, questions: updatedQuestions };

        // If editing existing survey, update on server
        if (isEditing && group._id) {
          updateGroupOnServer(groupId, {
            groupTitle: group.groupTitle,
            groupDescription: group.groupDescription,
            questions: updatedQuestions,
          });
        }

        return updatedGroup;
      }
      return group;
    });
    setGroups(updatedGroups);
  };

  const removeGroup = async (groupId) => {
    if (groups.length === 1) {
      notifications.show({
        title: "Warning",
        message: "You need at least one group in your survey.",
        color: "yellow",
      });
      return;
    }

    const groupToRemove = groups.find((g) => g.id === groupId);

    if (isEditing && groupToRemove && groupToRemove._id) {
      try {
        // Delete group on server first
        await ApiS3.delete(
          `surveys/${surveyData._id}/groups/${groupToRemove._id}`
        );

        notifications.show({
          title: "Success",
          message: "Group removed successfully.",
          color: "teal",
          icon: <IconCheck size={18} />,
        });
      } catch (error) {
        console.error("Error removing group:", error);
        notifications.show({
          title: "Error",
          message: "Failed to remove group from server.",
          color: "red",
          icon: <IconX size={18} />,
        });
      }
    }

    // Remove from local state regardless of API success
    setGroups(groups.filter((group) => group.id !== groupId));
  };

  const moveQuestionUp = (groupId, questionIndex) => {
    if (questionIndex === 0) return;

    const updatedGroups = groups.map((group) => {
      if (group.id === groupId) {
        const newQuestions = [...group.questions];
        const temp = newQuestions[questionIndex];
        newQuestions[questionIndex] = newQuestions[questionIndex - 1];
        newQuestions[questionIndex - 1] = temp;

        const updatedGroup = { ...group, questions: newQuestions };

        // If editing existing survey, update on server
        if (isEditing && group._id) {
          updateGroupOnServer(groupId, {
            groupTitle: group.groupTitle,
            groupDescription: group.groupDescription,
            questions: newQuestions,
          });
        }

        return updatedGroup;
      }
      return group;
    });
    setGroups(updatedGroups);
  };

  const moveQuestionDown = (groupId, questionIndex) => {
    const groupIndex = groups.findIndex((group) => group.id === groupId);
    if (questionIndex >= groups[groupIndex].questions.length - 1) return;

    const updatedGroups = groups.map((group) => {
      if (group.id === groupId) {
        const newQuestions = [...group.questions];
        const temp = newQuestions[questionIndex];
        newQuestions[questionIndex] = newQuestions[questionIndex + 1];
        newQuestions[questionIndex + 1] = temp;

        const updatedGroup = { ...group, questions: newQuestions };

        // If editing existing survey, update on server
        if (isEditing && group._id) {
          updateGroupOnServer(groupId, {
            groupTitle: group.groupTitle,
            groupDescription: group.groupDescription,
            questions: newQuestions,
          });
        }

        return updatedGroup;
      }
      return group;
    });
    setGroups(updatedGroups);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case "Builder":
        return (
          <SurveyBuilder
            register={register}
            errors={errors}
            control={control}
            oneTimeresponse={oneTimeresponse}
            setValue={setValue}
            groups={groups}
            handleGroupTitleChange={handleGroupTitleChange}
            handleGroupDescriptionChange={handleGroupDescriptionChange}
            handleAddGroup={handleAddGroup}
            removeGroup={removeGroup}
            handleAddQuestion={handleAddQuestion}
            handleQuestionChange={handleQuestionChange}
            handleOptionChange={handleOptionChange}
            handleAddOption={handleAddOption}
            removeQuestion={removeQuestion}
            moveQuestionUp={moveQuestionUp}
            moveQuestionDown={moveQuestionDown}
            onSubmit={handleSubmit(onSubmit)}
            isEditing={isEditing}
          />
        );
      case "Preview":
        return (
          <SurveyPreview
            groups={groups}
            surveyTitle={watch("title")}
            surveyDescription={watch("description")}
          />
        );
      // case "Distribute":
      //   return <SurveyDistribute surveyId={surveyData?._id} />;
      // case "Results":
      //   return <SurveyResults surveyId={surveyData?._id} />;
      default:
        return null;
    }
  };

  return (
    
<MainLayout>
  <div className="container px-3 mx-auto my-8 flex flex-col sm:flex-row items-center justify-between">
    <h1 className="text-2xl font-semibold font-inter leading-none text-primary">
      Survey System
    </h1>
    
    <div className="flex gap-4 mt-4 sm:mt-0">
      {["Builder", "Preview"].map((tab, i) => (
        <button
          key={i}
          onClick={() => setActiveTab(tab)}
          className={`px-4 py-2 rounded border transition ${
            activeTab === tab
              ? "bg-[#e0f0f8] border-[#3b9cc4] font-bold text-[#07838F]"
              : "bg-white border-gray-300"
          }`}
        >
          {tab}
        </button>
      ))}
    </div>
  </div>

  {renderTabContent()}
</MainLayout>




  );
}
