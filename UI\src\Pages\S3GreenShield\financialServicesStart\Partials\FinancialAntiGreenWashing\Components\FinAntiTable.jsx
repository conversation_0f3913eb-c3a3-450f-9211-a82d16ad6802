import { useEffect, useState, useMemo } from "react";
import { Badge, Table, Select, FileInput } from "@mantine/core";
import { Notifications, showNotification } from "@mantine/notifications";
import { IconCheck as IconCheckNotification } from "@tabler/icons-react";

import { BsFillExclamationCircleFill } from "react-icons/bs";
import { GoDotFill } from "react-icons/go";
import { MdArrowDownward } from "react-icons/md";

import axios from "axios";
import Cookies from "js-cookie";
import { tHead, questions } from "../../../Constants/antiFinanTable";

const fetchToken = () => Cookies.get("level_user_token");
const API_BASE_URL = "https://portals3-staging-dwdwc5d6dnhyhpbb.uksouth-01.azurewebsites.net/compliance-assessement";

const FinAntiTable = () => {
  const [assessment, setAssessment] = useState([]);
  const [tempData, setTempData] = useState([]);
  const [editMode, setEditMode] = useState(false);

  useEffect(() => {
    const fetchComplianceAssessment = async () => {
      const token = fetchToken();
      if (!token) {
        console.error("No token found in localStorage");
        return;
      }

      try {
        const response = await axios.get(`${API_BASE_URL}/lastAssessment`, {
          headers: { Authorization: `Bearer ${token}` },
         
        });

        const questionsData = response.data?.questions || questions;
        setAssessment(questionsData);
        setTempData(questionsData);
      } catch (error) {
        console.error("Error fetching compliance assessment:", error);
      }
    };

    fetchComplianceAssessment();
  }, []);

  const handleChange = (id, field, newData) => {
    setTempData((prev) =>
      prev.map((item) =>
        (item._id || item.question) === id ? { ...item, [field]: newData } : item
      )
    );
  };

  const handleFileUpload = async (file, id) => {
    if (!file) {
      showNotification({
        title: "Error",
        message: "No file selected.",
        icon: <BsFillExclamationCircleFill />,
        color: "red",
      });
      return;
    }

    const token = fetchToken();
    if (!token) {
      console.error("No token found in localStorage");
      return;
    }

    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await axios.post(`${API_BASE_URL}/uploadFile`, formData, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (response.data) {
        handleChange(id, "evidenceUploaded", response.data);
        showNotification({
          title: "Success",
          message: "File uploaded successfully!",
          icon: <IconCheckNotification />,
          color: "green",
        });
      }
    } catch (error) {
      showNotification({
        title: "Error",
        message: "File upload failed.",
        icon: <BsFillExclamationCircleFill />,
        color: "red",
      });
      console.error("Error uploading file:", error);
    }
  };

  const handleSubmitAssessment = async () => {
    const token = fetchToken();
    if (!token) {
      console.error("No token found in localStorage");
      return;
    }

    try {
      const response = await axios.post(`${API_BASE_URL}`, tempData, {
        headers: { Authorization: `Bearer ${token}` },
        withCredentials: true,
      });
      console.log("Successfully sent:", response.data);
    } catch (error) {
      console.error("Error posting compliance assessment:", error);
    }
  };

  const rows = useMemo(
    () =>
      tempData.map((item) => (
        <Table.Tr
          key={item._id || item.question}
          className="bg-white text-sm text-[#626364] text-center"
        >
          <Table.Td className="w-10 px-6 text-left">{item.category}</Table.Td>
          <Table.Td className="w-[500px] text-left">{item.question}</Table.Td>
          <Table.Td className="w-[200px] text-left">
            {editMode ? (
              <Select
                placeholder={item.answer}
                data={["No", "Yes"]}
                value={item.answer||""}
                onChange={(e) => handleChange(item._id || item.question, "answer", e||"")}
              />
            ) : (
              <Badge
                className={`cursor-pointer ${item.answer === "no"
                  ? "bg-secondary-danger-200 text-secondary-danger-100"
                  : "bg-secondary-green-100 text-secondary-300"
                  }`}
                leftSection={
                  <GoDotFill
                    className={item.answer === "no"
                      ? "text-secondary-danger-100"
                      : "text-secondary-300"
                    }
                  />
                }
              >
                {item.answer.toLowerCase() === "no" ? "No" : "Yes"}
              </Badge>
            )}
          </Table.Td>
          <Table.Td className="text-left justify-start w-[200px]">
            <BsFillExclamationCircleFill
              className={`w-[20px] h-[20px] ${item.answer.toLowerCase() === "no"
                ? "text-secondary-danger-100"
                : "text-secondary-green-100"
                } mx-auto`}
            />
          </Table.Td>
          <Table.Td className="w-[200px] text-left">
            {editMode ? (
              <Select
                placeholder="weight"
                data={["1", "2", "3", "4", "5"]}
                value={item.weight || "1"}
                onChange={(e) => handleChange(item._id || item.question, "weight", e)}
              />
            ) : (
              <span>{item.weight}</span>
            )}
          </Table.Td>
          <Table.Td className="w-[200px] text-left">
            {editMode ? (
              <Select
                placeholder="Priority"
                data={["High", "Medium", "Low"]}
                value={item.priority || ""}
                onChange={(e) => handleChange(item._id || item.question, "priority", e)}
              />
            ) : (
              <Badge
                className={`${item.priority.toLowerCase() === "high"
                  ? "bg-teal-400"
                  : item.priority.toLowerCase() === "medium"
                    ? "bg-yellow-500"
                    : "bg-red-300 text-rose-800"
                  }`}
                leftSection={
                  <GoDotFill
                    className={item.priority.toLowerCase() === "high"
                      ? "text-emerald-600"
                      : item.priority.toLowerCase() === "medium"
                        ? "text-amber-500"
                        : "text-rose-500"
                    }
                  />
                }
              >
                {item.priority.toLowerCase()}
              </Badge>
            )}
          </Table.Td>
          <Table.Td className="w-[300px] text-left">{item.regulatoryRef}</Table.Td>
          <Table.Td className="w-[200px] text-left">
            {editMode ? (
              <FileInput
                className="w-50 mx-auto"
                placeholder="Upload file"
                onChange={(e) => handleFileUpload(e, item._id || item.question)}
              />
            ) : (
              <span>{item.evidenceUploaded?.length > 0 ? "Uploaded" : "Not Uploaded"}</span>
            )}
          </Table.Td>
        </Table.Tr>
      )),
    [tempData, editMode]
  );

  return (
    <div className="bg-[#F7F4F4] mt-7">
      <div className="btns flex gap-2 justify-start px-5 pb-5">
        {editMode ? (<button className="text-white bg-[#00c0a9] px-[20px] py-[10px] rounded-md " onClick={() => {
          setEditMode(!editMode)
        }}>
          Save
        </button>) : (<button className="text-white bg-[#00c0a9] px-[20px] py-[10px] rounded-md " onClick={() => {
          setEditMode(!editMode)
        }}>
          Edit
        </button>)}
        <button  disabled={editMode} className={editMode ? "bg-[#626364] text-balck px-[20px] py-[10px] rounded-md ":"text-white bg-[#00c0a9] px-[20px] py-[10px] rounded-md "} onClick={() => {
          handleSubmitAssessment()
        }}>
        {!editMode ?  "Submit":"disabled"}
        </button>
      </div>
      <div className="flex bg-white relative overflow-y-hidden">
        <Table.ScrollContainer className="scrollable-container" maw={"100%"}>
          <Table
            miw={2000}
            verticalSpacing="xs"
            horizontalSpacing="xl"
            className="scrollable-container"
          >
            <Table.Thead className="border-b-2 bg-white pb-6 text-base text-center">
              <Table.Tr>
                {tHead.map(
                  (el, i) => (
                    <Table.Th key={i} className="text-secondary-500 gap-2">
                      <div className="flex items-center justify-left gap-2">
                        <span className="font-medium">{el}</span>
                        <MdArrowDownward className="text-secondary-300" />
                      </div>
                    </Table.Th>
                  )
                )}
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>{rows}</Table.Tbody>
          </Table>
        </Table.ScrollContainer>
      </div>
    </div>
  );
};

export default FinAntiTable;
