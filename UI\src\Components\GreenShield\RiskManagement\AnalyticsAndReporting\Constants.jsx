
export const scores = [
    {
      value: "low",
      label: "Low",
      color: "#00d25b",
      bgColor: "rgba(0, 210, 91, 0.2)",
    },
    {
      value: "medium",
      label: "Medium",
      color: "#ffab00",
      bgColor: "rgba(255, 171, 0, 0.2)",
    },
    {
      value: "high",
      label: "High",
      color: "#ff5252",
      bgColor: "rgba(255, 82, 82, 0.2)",
    },
    {
      value: "critical",
      label: "Critical",
      color: "#AB0202",
      bgColor: "#AB02024D",
    },
  ];


  export const Badges = ({ level }) => {
    // We'll still need inline styles for the dynamic colors since they're custom values
    return (
      <div
        className="flex items-center gap-2 px-4 py-1 rounded-full w-fit"
        style={{ backgroundColor: level.bgColor }}
      >
        <div
          className="w-2 h-2 rounded-full"
          style={{ backgroundColor: level.color }}
        />
        <span
          className="font-semibold uppercase text-sm"
          style={{ color: level.color }}
        >
          {level.label}
        </span>
      </div>
    );
  };


  export const controlPerformanceData = [
    { title: "Effective", color: "#52F176" ,value: 100},
    { title: "Partially Effective", color: "#B2E7BE" ,value: 200},
    { title: "Ineffective", color: "#FBB90D" ,value: 300},
    { title: "Under Review", color: "#298BED" ,value: 400},
    { title: "Overdue Testing", color: "#E81E1E" ,value: 500}
  ];

  export const controlPerformanceBadges = [
    { title: "Effective", color: "#52F176" },
    { title: "Partially Effective", color: "#B2E7BE" },
    { title: "Ineffective", color: "#FBB90D" },
    { title: "Under Review", color: "#298BED" },
    { title: "Overdue Testing", color: "#E81E1E" }
  ];

  export const openIssuesBadges = [
    { title: "High Impact", color: "#0D9488" ,value: 100},
    { title: "Medium Impact", color: "#F59E0B" ,value: 200},
    { title: "Low Impact", color: "#D1D5DB" ,value: 300},
    { title: "Overdue Actions", color: "#EF4444" ,value: 400}
  ];