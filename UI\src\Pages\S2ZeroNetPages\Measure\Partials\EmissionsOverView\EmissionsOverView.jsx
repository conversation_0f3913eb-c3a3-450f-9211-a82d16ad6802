import Loading from "@/Components/Loading";
import { Bar<PERSON><PERSON> } from "@mantine/charts";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import Card from "@/Pages/S2ZeroNetPages/General/Partials/Card";
import { formatNumber } from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/StaticData";
import classNames from "classnames";
import { DonutChart } from "@mantine/charts";
import Cookies from "js-cookie";
// import { driver } from "driver.js";
// import "driver.js/dist/driver.css";
// import "@/assets/css/index.css";
// import { FaQuestionCircle } from "react-icons/fa";

const API_URL =
  "https://leveluppportals2api-staging.azurewebsites.net/carbon-factors/get-charts-data";

export default function EmissionsOverView({
  totalEmissions,
  loading,
  activeTab,
}) {
  const { t } = useTranslation();
  const [scopeEmissions, setScopeEmissions] = useState([]);
  const [locationEmissions, setLocationEmissions] = useState([]);
  const [elementsReady, setElementsReady] = useState(false);

  const scopes = [
    { key: "Scope 1", color: "#77C1EB" },
    { key: "Scope 2", color: "#d4910f" },
    { key: "Scope 3", color: "#85D37E" },
  ];

  const getScopeValue = (scope) =>
    (totalEmissions?.scope_breakdown?.[scope] || 0) / 1000;

  const data = [
    {
      month: " ",
      ...Object.fromEntries(scopes.map(({ key }) => [key, getScopeValue(key)])),
    },
  ];

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch(API_URL, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("level_user_token")}`,
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();

        // Extract NetZero Overview data
        // setNetZeroOverview({
        //   totalEmissionsCurrentMonth: data.total_emissions_current_month,
        //   totalEmissionsLastMonth: data.total_emissions_last_month,
        //   difference: data.total_emissions_current_month - data.total_emissions_last_month,
        //   percent:
        //     ((data.total_emissions_current_month - data.total_emissions_last_month) /
        //       data.total_emissions_last_month) *
        //     100,
        // });

        // Extract Scope Emissions data
        setScopeEmissions(data.scope_emissions);

        // Extract Location Emissions data and process top 2 locations
        const sortedLocations = data.location_emissions.sort(
          (a, b) => b.total_emissions - a.total_emissions
        );
        const topLocations = sortedLocations.slice(0, 2);
        const otherLocationsTotal = sortedLocations
          .slice(2)
          .reduce((sum, loc) => sum + loc.total_emissions, 0);
        if (otherLocationsTotal > 0) {
          topLocations.push({
            location: "Other Locations",
            percent:
              (otherLocationsTotal / data.total_emissions_current_month) * 100,
            total_emissions: otherLocationsTotal,
          });
        }
        setLocationEmissions(topLocations);

        // setIsLoading(false);
      } catch (error) {
        console.error("Error fetching data:", error);
        // setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // const getGuideSteps = () => [
  //   {
  //     element: ".The-Measure-stage-consists-of-two-main-parts",
  //     popover: {
  //       title: t("The Measure stage consists of two main parts"),
  //       description: t("1. Emission Overview 2. Emissions Breakdown"),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".high-level-summary",
  //     popover: {
  //       title: t("high-level summary of total emissions, broken down by Scopes and Locations."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Understand-Scope-Breakdown",
  //     popover: {
  //       title: t("Understand Scope Breakdown (T CO₂e)"),
  //       description: t(
  //         "Each scope will show the corresponding emission value (e.g., Scope 1 = 2.10K T CO₂e)."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Understand-Scope-Breakdown-Scope1",
  //     popover: {
  //       title: t("Scope 1"),
  //       description: t(
  //         "Direct emissions from owned or controlled sources (e.g., fuel combustion)."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Understand-Scope-Breakdown-Scope2",
  //     popover: {
  //       title: t("Scope 2"),
  //       description: t(
  //         "Indirect emissions from the generation of purchased electricity, steam, heating, and cooling."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Understand-Scope-Breakdown-Scope3",
  //     popover: {
  //       title: t("Scope 3"),
  //       description: t(
  //         "All other indirect emissions occurring in the value chain (e.g., supply chain, commuting, travel)."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Review-Total-Absolute-Emissions",
  //     popover: {
  //       title: t("Review Total Absolute Emissions"),
  //       description: t(
  //         "Displays the total emissions across all scopes."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Analyze-Top-Emissions-by-Scope",
  //     popover: {
  //       title: t("Analyze Top Emissions by Scope"),
  //       description: t(
  //         "View the percentage distribution of emissions by Scope through a donut chart"
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Analyze-Top-Emissions-by-Location",
  //     popover: {
  //       title: t("Analyze Top Emissions by Location"),
  //       description: t(
  //         "View the percentage distribution of emissions by Location through a donut chart"
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  // ];

  // const checkElementsExist = () => {
  //   const steps = getGuideSteps();
  //   const allElementsExist = steps.every((step) =>
  //     document.querySelector(step.element)
  //   );
  //   return allElementsExist;
  // };

  // useEffect(() => {
  //   if (activeTab === "emissions-overview") {
  //     const observer = new MutationObserver(() => {
  //       if (checkElementsExist()) {
  //         setElementsReady(true);
  //         observer.disconnect();
  //       }
  //     });

  //     observer.observe(document.body, {
  //       childList: true,
  //       subtree: true,
  //     });

  //     return () => observer.disconnect();
  //   }
  // }, [activeTab, elementsReady]);

  // const startGuide = () => {
  //   const driverObj = driver({
  //     showProgress: true,
  //     popoverClass: "my-custom-popover-class",
  //     nextBtnText: "Next",
  //     prevBtnText: "Back",
  //     doneBtnText: "Done",
  //     overlayColor: "rgba(0, 0, 0, 0)",
  //     progressText: "Step {{current}} of {{total}}",
  //     steps: getGuideSteps(),
  //     onDestroyStarted: () => {
  //       localStorage.setItem("hasSeenemissionsOverviewGuide", "true");
  //       driverObj.destroy();
  //     },
  //   });
  //   driverObj.drive();
  // };

  // useEffect(() => {
  //   const hasSeenGuide = localStorage.getItem("hasSeenemissionsOverviewGuide");
  //   if (!hasSeenGuide) {
  //     startGuide();
  //   }
  //   return () => {
  //     const driverObj = driver();
  //     if (driverObj.isActive()) {
  //       driverObj.destroy();
  //     }
  //   };
  // }, [activeTab, elementsReady]);

  if (loading) return <Loading />;

  return (
    <>
      {/* <div
        onClick={startGuide}
        style={{
          position: "fixed",
          bottom: 20,
          right: 20,
          cursor: "pointer",
          zIndex: 1000,
        }}
      >
        <div className="h-12 w-12 flex items-center justify-center bg-gradient-to-tr from-[#2C5A8C] via-[#1C889C] to-[#13B1A8] p-1  cursor-pointer rounded-full">
          <FaQuestionCircle
            size={34}
            color="#ffffff"
            className="mx-auto cursor-pointer"
          />
        </div>
      </div> */}
      <div className="grid grid-cols-1 mb-5 lg:grid-cols-3 gap-y-12 lg:gap-10 text-start font-inter">
        {/* Section: Scope Breakdown */}
        <div className="col-span-2 font-inter Understand-Scope-Breakdown">
          <h5 className="font-semibold">
            {t("ScopeBreakdown")}{" "}
            <small className="text-nowrap">(T CO2e)</small>
          </h5>
          <div className="bg-white p-5 rounded-lg mt-3 h-[150px] flex flex-col justify-center gap-y-5 shadow-md">
            <div className="items-center justify-start gap-3 md:flex">
              {scopes.map(({ key, color }) => (
                <div key={key} className={`flex items-center justify-start ${key === "Scope 1" ? "Understand-Scope-Breakdown-Scope1" : key === "Scope 2" ? "Understand-Scope-Breakdown-Scope2" : "Understand-Scope-Breakdown-Scope3"}`}>
                  <div className="flex items-center justify-start me-2">
                    <p
                      className={classNames("h-5 w-5 rounded-xl me-1")}
                      style={{ backgroundColor: color }}
                    ></p>

                    <p className="font-semibold">{t(key)}</p>
                  </div>
                  <p className="font-medium">
                    {formatNumber(getScopeValue(key))}
                  </p>
                </div>
              ))}
            </div>
            <BarChart
              h={50}
              data={data}
              className="rounded-xl"
              dataKey="month"
              type="stacked"
              tickLine="none"
              gridAxis="none"
              withXAxis={false}
              withYAxis={false}
              orientation="vertical"
              yAxisProps={{ width: 50 }}
              series={scopes.map(({ key, color }) => ({ name: key, color }))}
            />
          </div>
        </div>

        {/* Section: Total Absolute Emissions */}
        <div className="col-span-1 font-inter Review-Total-Absolute-Emissions">
          <h5 className="font-semibold">{t("TotalAbsoluteEmissions")}</h5>
          <div className="bg-white p-5 rounded-lg mt-3 h-[150px] flex flex-col justify-center shadow-md">
            <h1 className="text-2xl font-bold">
              {formatNumber((totalEmissions?.total_emissions || 0) / 1000)}{" "}
              <small className="text-nowrap text-sm">(T CO2e)</small>
            </h1>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-5 md:grid-cols-2">
        <Card
          className="my-6 bg-white shadow-md px-20 rounded-xl Analyze-Top-Emissions-by-Scope"
          cardTitle="Top Emissions by Scope"
          viewAllLink="#"
        >
          <div className="flex flex-row justify-between items-center overflow-hidden">
            <ul className="flex flex-col gap-8 list-none w-1/3">
              {scopeEmissions.map((scope, index) => (
                <li
                  key={scope.scope}
                  className={`flex flex-col relative pl-6 before:absolute before:left-0 before:top-2 before:w-3 before:h-3 before:rounded-full ${
                    index === 0
                      ? "before:bg-[#29919B]"
                      : index === 1
                      ? "before:bg-[#70D162]"
                      : "before:bg-[#D4E9EB]"
                  }`}
                >
                  {scope.scope}{" "}
                  <span className="font-bold text-xl">
                    {scope.percent.toFixed(2)}%
                  </span>
                </li>
              ))}
            </ul>

            <DonutChart
              withLabelsLine
              labelsType="percent"
              withLabels
              size={250}
              thickness={40}
              className="w-2/3"
              strokeWidth={0}
              data={scopeEmissions.map((scope, index) => ({
                value: scope.percent,
                label: scope.scope,
                color:
                  index === 0 ? "#29919B" : index === 1 ? "#70D162" : "#D4E9EB",
              }))}
            />
          </div>
        </Card>
        <Card
          className="my-6 bg-white shadow-md px-20 rounded-xl Analyze-Top-Emissions-by-Location"
          cardTitle="Top Emissions by Locations"
          viewAllLink="#"
        >
          <div className="flex flex-row justify-between items-center overflow-hidden">
            <ul className="flex flex-col gap-8 list-none w-1/3">
              {locationEmissions.map((location, index) => (
                <li
                  key={location.location}
                  className={`flex flex-col relative pl-6 before:absolute before:left-0 before:top-2 before:w-3 before:h-3 before:rounded-full ${
                    index === 0
                      ? "before:bg-[#29919B]"
                      : index === 1
                      ? "before:bg-[#70D162]"
                      : "before:bg-[#D4E9EB]"
                  }`}
                >
                  {location.location}{" "}
                  <span className="font-bold text-xl">
                    {location.percent.toFixed(2)}%
                  </span>
                </li>
              ))}
            </ul>
            <DonutChart
              withLabelsLine
              labelsType="percent"
              withLabels
              size={250}
              className="w-2/3"
              thickness={40}
              strokeWidth={0}
              data={locationEmissions.map((location, index) => ({
                value: location.percent,
                label: location.location,
                color:
                  index === 0 ? "#29919B" : index === 1 ? "#70D162" : "#D4E9EB",
              }))}
            />
          </div>
        </Card>
      </div>
    </>
  );
}
