import Axios from "@/Api/apiConfig";
import RadarChart from "@/Components/Charts/RadarChart";
import Loading from "@/Components/Loading";
import useAxios from "@/hooks/useAxios";
import React, { useEffect, useState } from "react";

const ResultGridChart = ({ categoryData }) => {
 const pos = [
  "active:translate-x-1/2", // left
  "", // 0
  "active:-translate-x-1/2", //  right
 ];

 return (
  <React.Fragment>
   {categoryData ? (
    <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 p-4">
     {categoryData?.map(
      (catData, i) =>
       catData?.labels?.length > 0 && (
        <div
         key={i}
         className="bg-gray-200/30 backdrop-blur-md cursor-zoom-in select-none hover:scale-[1.01] active:scale-150 active:z-50 transition-all shadow-md p-4 h-auto aspect-square rounded-xl text-center flex justify-center items-center w-full"
        >
         <RadarChart
          labels={catData?.labels?.map((label) => label?.name)}
          data={catData?.labels?.map((label) => label?.score)}
          label={catData?.category_name}
          backgroundColor="rgba(10,111,133, 0.2)"
          borderColor="rgb(10,111,133)"
         />
        </div>
       )
     )}
    </div>
   ) : (
    <Loading />
   )}
  </React.Fragment>
 );
};

export default ResultGridChart;
