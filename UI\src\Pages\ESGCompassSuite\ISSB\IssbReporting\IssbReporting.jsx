import useSideBarRoute from "@/hooks/useSideBarRoute";
import MainLayout from "@/Layout/MainLayout";
import AccordionDemo from "../../../../Components/MultiStepForm/Accordion";

const IssbReporting = () => {
  const { issbMenu } = useSideBarRoute();

  return (
    <MainLayout menus={issbMenu} navbarTitle={"ISSB Reporting"}>
      <div className="min-h-screen p-4 md:p-10">
        <AccordionDemo />
      </div>
    </MainLayout>
  )
}

export default IssbReporting