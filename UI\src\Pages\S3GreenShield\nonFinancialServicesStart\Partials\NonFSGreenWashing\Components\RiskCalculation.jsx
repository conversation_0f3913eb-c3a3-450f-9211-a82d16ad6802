import React from 'react'
import RiskMatrixTable from './RiskMatrixTable'
import RiskLevelMatrixTable from './RiskLevelMatrixTable'

const RiskCalculation = () => {
  return (
    <>
        <h3 className='font-bold text-2xl'>Risk Matrix</h3>
        <p className='font-semibold text-lg'>Criteria of Likelihood and Impact</p>
        {/* risk matrix table */}
        <RiskMatrixTable />

        <h3 className='font-semibold text-lg mt-3'>Risk Level Matrix</h3>
        <p className='font-semibold text-sm mb-3'>Risk Level = Impact value x Likelihood value</p>
        <RiskLevelMatrixTable />
    </>
  )
}

export default RiskCalculation