import TabsButton from "@/Components/ButtonsAndLinks/TabsButton";
import { useState } from "react";
import DownStreamTransportation from "./DownStreamTransportation/DownStreamTransportation";
import UpStreamTransportaion from "./UpStreamTransportaion/UpStreamTransportaion";
import TransportationAndDestributionAnalytic from "./TransportationAndDestributionAnalytic/TransportationAndDestributionAnalytic";

const TransportationAndDestribution = () => {
  const [activeSubTab, setActiveSubTab] = useState("Upstream");

  // name1: "Transportation",
  // name2: "Distribut",
  const mainTabs = ["Transportation", "Distribution", "Analytics"];
  const [activeMainTab, setActiveMainTab] = useState("Transportation");

  return (
    <div>
      <h5 className="text-primary text-xl font-bold">
        Transportation & Distribution Emissions
      </h5>

      <div className="mt-5 grid lg:grid-cols-3 justify-center bg-[#FFFFFF] border-2 border-[#E8E7EA] p-6 gap-5 py-3 mb-6 rounded-lg text-primary">
        {mainTabs.map((tabName) => (
          <TabsButton
            key={tabName}
            active={activeMainTab}
            setActive={setActiveMainTab}
            btnName={tabName}
          />
        ))}
      </div>

      {activeMainTab == "Analytics" ? (
        <>
        <TransportationAndDestributionAnalytic />
        </>
      ) : (
        <>
          <div
            className="grid lg:grid-cols-2 lg:gap-10 p-6 py-3 gap-5 lg:text-xl mb-5 bg-white rounded-lg border-2"
            role="tablist"
          >
            <button
              className={`p-2 rounded-xl flex items-center justify-center gap-2 font-semibold transition-colors duration-200
                    ${
                      "Upstream" === activeSubTab
                        ? "bg-primary text-white"
                        : "hover:bg-gray-100 border-2"
                    }
                    `}
              onClick={() => setActiveSubTab("Upstream")}
              role="tab"
              aria-selected={"Upstream" === activeSubTab}
              aria-controls={`tabpanel-Upstream`}
            >
              Upstream
            </button>

            <button
              className={`p-2 rounded-xl flex items-center justify-center gap-2 font-semibold transition-colors duration-200
                    ${
                      "Downstream" === activeSubTab
                        ? "bg-primary text-white"
                        : "hover:bg-gray-100 border-2"
                    }
                    `}
              onClick={() => setActiveSubTab("Downstream")}
              role="tab"
              aria-selected={"Downstream" === activeSubTab}
              aria-controls={`tabpanel-Downstream`}
            >
              Downstream
            </button>
          </div>

          {/* downstream transportation */}
          {/* <DownStreamTransportation /> */}

          {activeSubTab == "Upstream" && (
            <UpStreamTransportaion upStreamOrDown={activeSubTab} />
          )}
          {activeSubTab == "Downstream" && (
            <DownStreamTransportation upStreamOrDown={activeSubTab} />
          )}
        </>
      )}
    </div>
  );
};

export default TransportationAndDestribution;
