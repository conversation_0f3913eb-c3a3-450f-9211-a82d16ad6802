import { useTranslation } from "react-i18next";
import PublicManualForm from "./Partials/PublicManualForm";

export default function PublicManualInputs({
  assetTypeDrop,
  assetTypeAll,
  CompanyAssetAll,
  companyAssetDrop,
  AssetsError,
  loading,
  allItemSpecificities
}) {
  const { t } = useTranslation();
  return (
    <>
      <PublicManualForm
        companyAssetDrop={companyAssetDrop}
        AssetsError={AssetsError}
        CompanyAssetAll={CompanyAssetAll}
        assetTypeAll={assetTypeAll}
        assetTypeDrop={assetTypeDrop}
        loading={loading}
        allItemSpecificities={allItemSpecificities}
      />
    </>
  );
}
