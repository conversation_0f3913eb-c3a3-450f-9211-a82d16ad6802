import React, { useState } from "react";
import { Tabs } from "@mantine/core";
import ChartPanel from "./ChartPanel";

const ScopeTabs = ({ target }) => {
  const [activeTab, setActiveTab] = useState(target ? target : "Scope 1");
  return (
    <Tabs
      defaultValue={activeTab}
      onChange={setActiveTab}
      classNames={{
        tab: "outline outline-2 outline-primary my-7 mx-1 text-primary font-semibold",
      }}
      variant="pills"
      radius="md"
      color="#07838F"
    >
      <Tabs.List>
        <Tabs.Tab
          value="Scope 1"
          className={`${
            activeTab == "Scope 1"
              ? "text-white bg-opacity-100"
              : "bg-opacity-10"
          }`}
        >
          Scope 1
        </Tabs.Tab>
        <Tabs.Tab
          value="Scope 2"
          className={`${
            activeTab == "Scope 2"
              ? "text-white bg-opacity-100"
              : "bg-opacity-10"
          }`}
        >
          Scope 2
        </Tabs.Tab>
        <Tabs.Tab
          value="Scope 3"
          className={`${
            activeTab == "Scope 3"
              ? "text-white bg-opacity-100"
              : "bg-opacity-10"
          }`}
        >
          Scope 3
        </Tabs.Tab>
      </Tabs.List>

      <Tabs.Panel value="Scope 1">
        <ChartPanel />
      </Tabs.Panel>

      <Tabs.Panel value="Scope 2"><ChartPanel /></Tabs.Panel>

      <Tabs.Panel value="Scope 3"><ChartPanel /></Tabs.Panel>
    </Tabs>
  );
};

export default ScopeTabs;
