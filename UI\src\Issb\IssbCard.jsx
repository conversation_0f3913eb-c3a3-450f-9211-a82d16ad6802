import { FaCheckCircle } from "react-icons/fa";
import { Link } from "react-router-dom";
import { FaArrowRightLong } from "react-icons/fa6";


const IssbCard = ({ link, title, description, items, btnString, icon }) => {
  return (
    <Link to={link} className="rounded-3xl w-full border-2 hover:border-[#2C5A8C] bg-white p-6 shadow-sm transition-all">
      <div className="flex justify-center items-center w-16 h-16 rounded-full bg-[#07838F1A] mb-4">
        {icon}
      </div>

      <h2 className="text-[32px] max-md:text-start font-semibold text-[#272727]">{title}</h2>
      <p className="text-gray-600 max-md:text-start text-lg mt-1">{description}</p>

      <div className="flex max-md:flex-col justify-between mt-4 bg-[#07838F1A] p-4 rounded-lg">
        <div>
          {items.map((item, index) => (
            <div
              key={index}
              className="flex items-center gap-2 text-[#29919B] mb-2 last:mb-0"
            >
              <FaCheckCircle className="text-lg" />
              <span className="text-lg max-md:text-start text-gray-700">{item}</span>
            </div>
          ))}
        </div>
        <h5 className="text-[#29919B] self-end font-semibold mt-4 flex gap-2 items-center hover:underline">
          {btnString} <FaArrowRightLong />
        </h5>
      </div>
    </Link>
  );
};

export default IssbCard;
