import { But<PERSON> } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { FaPlus } from "react-icons/fa";
import AIRecommendation from "./Partials/AIRecommendation";
import TargetsBestPractice from "./Partials/TargetsBestPractice";
import TargetsModal from "./Partials/TargetsModal";
import TargetsTable from "./Partials/TargetsTable";
// import { driver } from "driver.js";
// import "driver.js/dist/driver.css";
// import "@/assets/css/index.css";
// import { FaQuestionCircle } from "react-icons/fa";

const TargetsView = ({ assignees, assetTypes, activeTab }) => {
  const { t } = useTranslation();
  const [opened, { open, close }] = useDisclosure(false);
  const [AiOpened, { open: AiO<PERSON>, close: AiClose }] = useDisclosure(false);
  const [refetch, setRefetch] = useState(false);
  // const [elementsReady, setElementsReady] = useState(false);

  // const getGuideSteps = () => [
  //   {
  //     element: ".The-Decarbonise-Section",
  //     popover: {
  //       title: t("The Decarbonise section helps you set targets, implement actions, and track AI-driven recommendations to achieve meaningful emission reductions."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Click-Add-New-Target",
  //     popover: {
  //       title: t("Click 'Add New Target'."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Click-AI-recommendation",
  //     popover: {
  //       title: t("Click 'LevelUp ESG - AI Recommendation'."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Data-Table-View-Targets",
  //     popover: {
  //       title: t("Data Table View"),
  //       description: t("Uploaded data appears in a structured table with the following actions:"),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Data-Table-Edit-Targets",
  //     popover: {
  //       title: t("Edit"),
  //       description: t("Correct or update any field."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Data-Table-Delete-Targets",
  //     popover: {
  //       title: t("Delete"),
  //       description: t("Remove any wrong entry."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  // ];

  // const checkElementsExist = () => {
  //   const steps = getGuideSteps();
  //   const allElementsExist = steps.every((step) =>
  //     document.querySelector(step.element)
  //   );
  //   return allElementsExist;
  // };

  // useEffect(() => {
  //   if (activeTab === "targets") {
  //     const observer = new MutationObserver(() => {
  //       if (checkElementsExist()) {
  //         setElementsReady(true);
  //         observer.disconnect();
  //       }
  //     });

  //     observer.observe(document.body, {
  //       childList: true,
  //       subtree: true,
  //     });

  //     return () => observer.disconnect();
  //   }
  // }, [activeTab]);

  // const startGuide = () => {
  //   const driverObj = driver({
  //     showProgress: true,
  //     popoverClass: "my-custom-popover-class",
  //     nextBtnText: "Next",
  //     prevBtnText: "Back",
  //     doneBtnText: "Done",
  //     progressText: "Step {{current}} of {{total}}",
  //     overlayColor: "rgba(0, 0, 0, 0)",
  //     steps: getGuideSteps(),
  //     onDestroyStarted: () => {
  //       localStorage.setItem("hasSeenDecarbonizeTargetsGuide", "true");
  //       driverObj.destroy();
  //     },
  //   });
  //   driverObj.drive();
  // };

  // useEffect(() => {
  //   const hasSeenGuide = localStorage.getItem("hasSeenDecarbonizeTargetsGuide");
  //   if (!hasSeenGuide) {
  //     startGuide();
  //   }
  //   return () => {
  //     const driverObj = driver();
  //     if (driverObj.isActive()) {
  //       driverObj.destroy();
  //     }
  //   };
  // }, [activeTab, elementsReady]);

  return (
    <>
      {/* <div
        onClick={startGuide}
        style={{
          position: "fixed",
          bottom: 20,
          right: 20,
          cursor: "pointer",
          zIndex: 1000,
        }}
      >
        <div className="h-12 w-12 flex items-center justify-center bg-gradient-to-tr from-[#2C5A8C] via-[#1C889C] to-[#13B1A8] p-1  cursor-pointer rounded-full">
          <FaQuestionCircle
            size={34}
            color="#ffffff"
            className="mx-auto cursor-pointer"
          />
        </div>
      </div> */}
      <div className="flex flex-col items-center justify-between md:flex-row mb-7">
        <div className="py-3">
          <h2 className="my-3 font-bold md:text-3xl Data-Table-View-Targets">
            {t("targetsView.title")}
          </h2>
          <p className="font-semibold">{t("targetsView.description")}</p>
        </div>
        <div className="mt-auto mb-2">
          <Button
            // leftSection={<FaPlus size={12} />}
            variant="filled"
            size="sm"
            radius="md"
            className="Click-AI-recommendation bg-gradient-to-r from-[#FFF4D7] via-[#DADEFB] to-[#D8FFDC] text-[#2A3153] hover:text-[#2A3153]"
            onClick={AiOpen}
          >
            {t("AI Recommendation")}
          </Button>
          <Button
            leftSection={<FaPlus size={12} />}
            variant="filled"
            size="sm"
            radius="md"
            className=" ms-2 bg-primary hover:bg-secondary Click-Add-New-Target"
            onClick={open}
          >
            {t("targetsView.newTarget")}
          </Button>
        </div>

        <TargetsModal
          assignees={assignees}
          isOpen={opened}
          onRequestClose={close}
          refetchAgain={() => setRefetch(!refetch)}
        />
        <AIRecommendation
          AiOpened={AiOpened}
          AiClose={AiClose}
          assetTypes={assetTypes}
        />
      </div>
      <TargetsTable refetch={refetch} assignees={assignees} />
      <TargetsBestPractice />
    </>
  );
};

export default TargetsView;
