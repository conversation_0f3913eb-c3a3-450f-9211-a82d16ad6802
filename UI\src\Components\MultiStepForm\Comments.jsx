import { useEffect, useRef, useState } from "react";
import { Button } from "@mantine/core";
import { toast } from "react-toastify";
import Cookies from "js-cookie";
import { FaUser } from "react-icons/fa";

const baseUrl = "https://issb-report-api-staging.azurewebsites.net";
const authBaseUrl = "https://portal-auth-main-staging.azurewebsites.net";

const createComment = async (topicId, commentText) => {
  const token = Cookies.get("level_user_token");
  if (!token) {
    throw new Error("Authorization token is missing");
  }

  const response = await fetch(`${baseUrl}/api/v1/topics/${topicId}/comments`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify({ comment_text: commentText }),
  });

  if (!response.ok) {
    throw new Error(`Failed to add comment: ${response.statusText}`);
  }

  return await response.json();
};

const getAllCompanyUsers = async () => {
  const token = Cookies.get("level_user_token");
  if (!token) {
    throw new Error("Authorization token is missing");
  }

  const response = await fetch(`${authBaseUrl}/get-all-company-users`, {
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to add comment: ${response.statusText}`);
  }

  return await response.json();
};

function processHTMLForBackend(html) {
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = html;

  // Replace each mention span with its data-mention value
  const mentions = tempDiv.querySelectorAll("span[data-mention]");
  mentions.forEach((span) => {
    const textNode = document.createTextNode(span.dataset.mention);
    span.parentNode.replaceChild(textNode, span);
  });

  return tempDiv.textContent;
}

const Comments = ({ topicId, topicCommentsAttr }) => {
  const [commentInput, setCommentInput] = useState("");
  const [topicComments, setTopicComments] = useState([]);
  const [users, setUsers] = useState([]);
  const [caretStartPos, SetCaretStartPos] = useState(0);
  const [showMentionsDropdown, setShowMentionsDropdown] = useState(false);
  const [mentionSearchTerm, setMentionSearchTerm] = useState("");
  const [dropdownPosition, setDropdownPosition] = useState({
    top: 0,
    left: 0,
  });
  const inputRef = useRef();

  useEffect(() => {
    (async () => setUsers(await getAllCompanyUsers()))();
    setTopicComments(topicCommentsAttr || []);
  }, []);

  const handleSubmitComment = async () => {
    const inputElement = inputRef.current;
    if (!inputElement) return;

    const processedText = processHTMLForBackend(inputElement.innerHTML);

    if (!processedText.trim()) return;

    try {
      let new_comments = (await createComment(topicId, processedText)).topic
        .comments;
      if (!new_comments || !Array.isArray(new_comments)) new_comments = [];
      setTopicComments(new_comments);
      inputElement.innerHTML = ""; // Clear input
    } catch (error) {
      console.error("Error adding comment:", error);
      toast.error(`Failed to add comment: ${error.message}`);
    }
  };

  const handleCommentInputKeyDown = (event) => {
    if (event.key === "Enter" && commentInput.trim()) {
      const success = handleSubmitComment(commentInput.trim());
      if (success) {
        setCommentInput("");
      }
    }

    const inputElement = inputRef.current;
    if (!inputElement) return;

    const text = inputElement.textContent;
    getCaretPosition(inputElement);
    const textBeforeCaret = text.slice(0, caretStartPos);

    if (event.key === "@") {
      const rect = inputElement.getBoundingClientRect();
      setDropdownPosition({ top: rect.bottom, left: rect.left });
      setShowMentionsDropdown(true);
      setMentionSearchTerm("");
    } else if (event.ctrlKey && event.code === "Space") {
      event.preventDefault();
      const lastAt = textBeforeCaret.lastIndexOf("@");
      if (lastAt !== -1 && caretStartPos > lastAt) {
        setShowMentionsDropdown(true);
        setMentionSearchTerm(textBeforeCaret.slice(lastAt + 1));
      }
    } else if (event.escKey) {
      setShowMentionsDropdown(false);
    }

    // Update search term if typing after @
    if (showMentionsDropdown) {
      if (event.key.length === 1 && !event.ctrlKey && !event.metaKey) {
        const lastAt = textBeforeCaret.lastIndexOf("@");
        if (lastAt !== -1) {
          const search = textBeforeCaret.slice(lastAt + 1);
          setMentionSearchTerm(search);
        }
      } else if (event.key === "Backspace") {
        const lastAt = textBeforeCaret.lastIndexOf("@");
        if (lastAt !== -1) {
          const search = textBeforeCaret.slice(lastAt + 1);
          setMentionSearchTerm(search);
        }
      }
    }
  };

  const handleCommentButtonClick = async () => {
    await handleSubmitComment();
  };

  const handleCommentInputChange = (e) => {
    const text = e.target.textContent;
    setCommentInput(text);
  };

  const getCaretPosition = (element) => {
    const sel = window.getSelection();
    if (
      sel.rangeCount > 0 &&
      sel.anchorNode &&
      element.contains(sel.anchorNode)
    ) {
      const range = sel.getRangeAt(0).cloneRange();
      range.selectNodeContents(element);
      range.setEnd(sel.anchorNode, sel.anchorOffset);
      SetCaretStartPos(range.toString().length);
    }
  };

  const handleMentionSelect = (user) => {
    const inputElement = inputRef.current;
    if (!inputElement) return;

    const selection = window.getSelection();
    if (!selection.rangeCount) return;

    const text = inputElement.textContent;
    const textBeforeCaret = text.slice(0, caretStartPos);
    const lastAtIndex = textBeforeCaret.lastIndexOf("@");
    if (lastAtIndex === -1) return;

    const mentionText = `@tag{${user.company_id}-${user.user_id}-${user.user_name}}`;

    // Use TreeWalker to find the text node containing the "@" symbol
    const walker = document.createTreeWalker(
      inputElement,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );

    let currentNode = null;
    let cumulativeLength = 0;
    let matchNode = null;
    let matchStartOffset = -1;

    while ((currentNode = walker.nextNode())) {
      const nodeLength = currentNode.textContent.length;

      // Check if "@" is within this node
      const nodeStartIndex = cumulativeLength;
      const nodeEndIndex = cumulativeLength + nodeLength;

      if (lastAtIndex >= nodeStartIndex && lastAtIndex <= nodeEndIndex) {
        matchNode = currentNode;
        matchStartOffset = lastAtIndex - nodeStartIndex;
        break;
      }

      cumulativeLength += nodeLength;
    }

    if (!matchNode) return;

    // Create mention span
    const span = document.createElement("span");
    span.setAttribute("data-mention", mentionText);
    span.setAttribute("data-user-name", user.user_name);
    span.className = "font-bold text-blue-500";
    span.textContent = user.user_name;

    // Create whitespace
    const spaceNode = document.createTextNode("\u00a0");

    // Replace text with mention and space
    const range = selection.getRangeAt(0);
    range.setStart(matchNode, matchStartOffset);
    range.setEnd(matchNode, matchNode.textContent.length);
    range.deleteContents();
    range.insertNode(spaceNode);
    range.insertNode(span);

    // Move caret after the space
    range.setStartAfter(spaceNode);
    range.setEndAfter(spaceNode);
    selection.removeAllRanges();
    selection.addRange(range);

    inputElement.focus();
    setShowMentionsDropdown(false);
  };

  const convertTextToMentions = (element) => {
    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );
    const nodes = [];

    while (walker.nextNode()) {
      nodes.push(walker.currentNode);
    }

    nodes.forEach((node) => {
      const text = node.textContent;
      const regex = /@(\w+(?:\s+\w+)*)/g;
      let match;

      while ((match = regex.exec(text)) !== null) {
        const matchedName = match[1];
        const user = users.find(
          (u) => u.user_name.toLowerCase() === matchedName.toLowerCase()
        );

        if (user) {
          const mentionText = `@tag{${user.company_id}-${user.user_id}-${user.user_name}}`;

          // Create mention span
          const span = document.createElement("span");
          span.setAttribute("data-mention", mentionText);
          span.setAttribute("data-user-name", user.user_name);
          span.className = "font-bold text-blue-500";
          span.textContent = user.user_name;

          // Create whitespace
          const spaceNode = document.createTextNode("\u00a0");

          // Replace text with mention and space
          const range = document.createRange();
          range.setStart(node, match.index);
          range.setEnd(node, match.index + match[0].length);
          range.deleteContents();
          range.insertNode(spaceNode);
          range.insertNode(span);

          // Move selection after the space
          const selection = window.getSelection();
          range.setStartAfter(spaceNode);
          range.setEndAfter(spaceNode);
          selection.removeAllRanges();
          selection.addRange(range);
        }
      }
    });
  };

  const convertEditedMentionsToText = (element) => {
    const mentions = element.querySelectorAll("span[data-mention]");
    mentions.forEach((span) => {
      const originalName = span.getAttribute("data-user-name");
      const currentText = span.textContent;

      if (currentText !== originalName) {
        const selection = window.getSelection();
        if (!selection.rangeCount) return;

        const textNode = document.createTextNode(`@${currentText}`);
        span.parentNode.replaceChild(textNode, span);
        const range = selection.getRangeAt(0);
        range.setStart(textNode, textNode.length);
        range.setEnd(textNode, textNode.length);
      }
    });
  };

  const handleCommentInput = () => {
    const inputElement = inputRef.current;
    if (!inputElement) return;

    // Step 1: Convert plain @username to mention span if matches a user
    convertTextToMentions(inputElement);

    // Step 2: Convert edited mention spans back to plain text
    convertEditedMentionsToText(inputElement);
  };

  const formatCommentText = (text) => {
    const parts = [];
    const mentionRegex = /(@tag\{\d+-\d+-(.*?)\})/g;
    let lastIndex = 0;

    let match;
    while ((match = mentionRegex.exec(text)) !== null) {
      const [fullMatch, _tagContent, userName] = match;

      // Add text before the mention
      if (text.slice(lastIndex, match.index).length > 0) {
        parts.push(text.slice(lastIndex, match.index));
      }

      // Add styled mention span
      parts.push(
        <span key={fullMatch} className="font-bold text-blue-500">
          {userName}
        </span>
      );

      lastIndex = match.index + fullMatch.length;
    }

    // Add remaining text after last mention
    if (text.slice(lastIndex).length > 0) {
      parts.push(text.slice(lastIndex));
    }

    return parts;
  };

  const formatDate = (isoDate) => {
    const options = {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    };
    return new Date(isoDate).toLocaleDateString("en-US", options);
  };

  return (
    <div className="mt-4">
      <h3 className="font-semibold mb-2">Remarks</h3>
      <label className="font-medium mb-2">Comments</label>
      <div className="w-full border rounded p-2 bg-[#F8F8F8]">
        <div className="flex items-center gap-2">
          <div className="relative w-full">
            <div
              ref={inputRef}
              className="w-full bg-white rounded px-1 py-2 focus:outline-none border focus:border-slate-400"
              contentEditable
              placeholder="Enter any additional remarks or comments here..."
              onChange={handleCommentInputChange}
              onKeyDown={handleCommentInputKeyDown}
              onInput={handleCommentInput}
              onBlur={() =>
                setTimeout(() => setShowMentionsDropdown(false), 200)
              }
            />
            {showMentionsDropdown && (
              <div
                className="absolute z-10 mt-2 w-64 bg-white border rounded shadow-lg max-h-60 overflow-y-auto"
                style={{
                  top: `36px`,
                  left: `${dropdownPosition.left}px`,
                }}
              >
                <ul>
                  {users
                    .filter((user) =>
                      user.user_name
                        .toLowerCase()
                        .includes(mentionSearchTerm.toLowerCase())
                    )
                    .map((user, index) => (
                      <li
                        key={index}
                        className="px-4 py-2 cursor-pointer hover:bg-gray-100"
                        onClick={() => handleMentionSelect(user)}
                      >
                        {user.user_name}
                      </li>
                    ))}
                </ul>
              </div>
            )}
          </div>
          <Button
            className="w-40"
            color="#07838F"
            onClick={handleCommentButtonClick}
          >
            Comment
          </Button>
        </div>
        <div className="py-2">
          {topicComments.map((comment) => (
            <div key={comment.id} className="flex gap-2 p-2 w-full">
              <div className="py-1">
                {comment.author.image ? (
                  <img
                    src={comment.author.image}
                    alt="User Avatar"
                    className="h-8 w-8 rounded-full"
                  />
                ) : (
                  <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                    <FaUser className="text-white" size={16} />
                  </div>
                )}
              </div>
              <div className="flex flex-col w-[90%] gap-2">
                <div className="flex items-center gap-2">
                  <div className="font-bold text-xl text-[#07838F]">
                    {comment.author.user_name}
                  </div>
                  <div className="text-[#272727]">
                    {formatDate(comment.created_at)}
                  </div>
                </div>
                <div className="text-[#272727]">
                  {formatCommentText(comment.comment_text)}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Comments;
