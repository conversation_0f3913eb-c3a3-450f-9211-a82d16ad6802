import React from "react";
import Sidebar from "../Navbar/Sidebar";
import Welcome from "./Welcome";
import Infrastructure from "./Infrastructure";
import Overview from "./Overview";
import Carbon from "./Carbon";
import Activity from "./Activity";
import Vendors from "./Vendors";
import TeamChat from "./TeamChat";
import { LuCalendarDays } from "react-icons/lu";

function Container() {
  return (
    <div className="relative flex gap-[1rem] bg-[#f7f4f4] w-full">
      <Sidebar />
      <div className="h-screen overflow-y-auto relative py-[3rem] px-[2rem] w-full">
        <Welcome name="Deepa" />
        <div className="mt-[4rem]">
          <div className="flex flex-col xl:flex-row  gap-[1rem] xl:gap-[2rem] mb-[2rem] ">
            <Infrastructure
              type="Construction"
              region="United Kingdom"
              ownership="Construction"
            />
            <Overview number="412,314" numMonth="34.3%" />
          </div>
          <div className="flex flex-col xl:flex-row  gap-[1rem] xl:gap-[2rem]  mb-[2rem]">
            <Carbon date="Jan-Jun 2023" icon=<LuCalendarDays /> />
            <Activity
              comment="Aleesha added a comment to E- Q.17"
              added="Lisa added a query to E- Q.10"
              report="Your environment report was accepted"
            />
          </div>
          <div className="flex flex-col xl:flex-row  gap-[1rem] xl:gap-[2rem]  mb-[2rem]">
            <Vendors />
            <TeamChat />
          </div>
        </div>
      </div>
    </div>
  );
}

export default DashboardContainer;
