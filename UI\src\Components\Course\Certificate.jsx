import { CourseContext } from '@/Contexts/CourseContext';
import useSideBarRoute from '@/hooks/useSideBarRoute';
import MainLayout from '@/Layout/MainLayout';
import { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import { Document, Page, pdfjs } from 'react-pdf';
import { IoMdHome } from "react-icons/io";

pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.10.377/pdf.worker.min.js`;

const Certificate = () => {
  const { t } = useTranslation();
  const { GreenHubMenu } = useSideBarRoute();
  const { course } = useContext(CourseContext);
  const certificateUrl = course?.certificateURL;

  return (
    <MainLayout menus={GreenHubMenu} navbarTitle={t('Green Hub')}
        breadcrumbItems={[
            { title: <IoMdHome size={20}/>, href: "/get-started" },
            { title: "Academy", href: "#" },
        ]}
    >
      <h1 className="text-center text-2xl font-semibold mb-4">Certificate</h1>
      <div className="flex items-center justify-between gap-8 flex-col sm:flex-row">
        {course?.certificateURL ? (
          <div className="p-4 w-full">
            <Document file={certificateUrl}>
              <Page
                pageNumber={1}
                width={600} // Default for larger screens
                className="max-w-full sm:w-[90%] md:w-[80%] lg:w-[600px] border border-primary p-5"
                renderAnnotationLayer={false}
                renderTextLayer={false}
              />
            </Document>
          </div>
        ) : (
          <p className="text-center text-gray-500">No certificate available</p>
        )}

        <div className="w-4/12 ">
          <div className="course bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-500 cursor-pointer flex justify-between items-center flex-col  p-6 h-full gap-10 border hover:border-[#05808b] ">
            {/* Course Image */}
            <div className="courseImage max-h-[20rem] overflow-hidden  rounded-2xl">
              <LazyLoadImage
                src={course?.courseId.courseImage}
                alt={`Cover image for ${course?.courseId.courseName}`}
                effect="blur"
                className="w-full h-full object-cover "
              />
            </div>

            {/* Course Details */}
            <div className="w-full flex-1">
              <div className="w-full">
                <h1 className="text-2xl font-bold text-[#05808b] mb-3">{course?.courseId.courseName}</h1>
                <p className="text-gray-600">
                  {course?.courseId.description.length > 200 ? course?.courseId.description.substring(0, 200) + '...' : course?.courseId.description}
                </p>
              </div>
              <div className="my-4">
                {/* <CourseInfo
                      students={course?.courseId?.students}
                      lessons={course?.courseId?.sections}
                    /> */}
              </div>
              {/* Progress Par */}
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default Certificate;
