import React from "react";

export default function <PERSON><PERSON>({ text, className,onclick }) {
  return (
    <div>
      <button
      onClick={onclick}
        className={`shadow-lg bg-[#07838F] font-semibold rounded-lg ${className}`}
      >
        {text}
      </button>
    </div>
  );
}

// import React, { useState } from "react";

// export default function Button({ text, className }) {
//   const [isActive, setIsActive] = useState(false);

//   return (
//     <div>
//       <button
//         className={`shadow-lg bg-[#07838F] text-white py-2 px-6 rounded-lg ${className} ${
//           isActive ? "bg-[#065D6E] shadow-inner" : ""
//         }`}
//         onMouseDown={() => setIsActive(true)}
//         onMouseUp={() => setIsActive(false)}
//         onMouseLeave={() => setIsActive(false)} // Reset when mouse leaves the button
//       >
//         {text}
//       </button>
//     </div>
//   );
// }
