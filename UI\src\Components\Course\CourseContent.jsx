import { ArrowLeftIcon, ArrowRightIcon, CloseIcon } from "@/assets/icons";

import { useContext, useState } from "react";
import OverviewSection from "./Sections/OverviewSection";
import CurriculumSection from "./Sections/CurriculumSection";
import ResourcesSection from "./Sections/ResourcesSection";
import { CourseContext } from "@/Contexts/CourseContext";
import VideoPlayer from "./VideoPlayer";

export default function CourseContent() {
  const {
    course,
    progress,
    isWatching,
    currentVideo,
    handleVideoPlayer,
    closeLecture,
  } = useContext(CourseContext);
  const [section, setSection] = useState(0);
  const tabClasses =
    "cursor-pointer hover:text-[#05808b]  hover:border-b-[#05808b] duration-300 border-b";
  const activeTabClasses = " text-[#05808b] border-b-[#05808b]";
  const data = course?.courseId;
  return (
    <div className="px-4 py-4 text-xl font-bold bg-white rounded-2xl text-blue-950 mb-7 shadow-lg">
      {/* Video Player */}
      {isWatching && (
        <div className="w-full rounded-xl mb-4">
          <h1 className="text-2xl md:text-4xl font-semibold text-[#272727] mb-12">
            {data?.courseName}
          </h1>
          <div className="flex justify-between items-center">
            <h2 className="text-2xl md:text-3xl font-semibold text-[#272727] mb-4">
              {currentVideo.lectureName}
            </h2>
            <div className=" duration-300 hover:text-[#05808b]">
              <CloseIcon onClick={() => closeLecture()} />
            </div>
          </div>
          <VideoPlayer />
        </div>
      )}
      {/* image */}
      {!isWatching && (
        <div className="w-full flex items-center justify-center p-5">
          <img
            src={data?.courseImage}
            alt={data?.courseName}
            className="max-w-full rounded-2xl h-[26rem] mb-2 object-contain sm:object-cover"
          />
        </div>
      )}

      {/* Course Name and Description */}
      {!isWatching && (
        <>
          <h2 className="text-2xl md:text-4xl font-semibold text-[#272727] mb-4">
            {data?.courseName}
          </h2>
          <div className="flex justify-center sm:justify-end items-center mb-8">
            {/* <CourseInfo students={data.students} lessons={data.sections} /> */}
            <button
              onClick={() => handleVideoPlayer(currentVideo?._id, "START")}
              className="text-sm bg-[#05808b] px-8 py-1 rounded text-white hover:bg-[#194b50] duration-300"
            >
              {progress.progressPercentage > 0 ? "Continue" : "Start Now"}
            </button>
          </div>
        </>
      )}
      {isWatching && (
        <div className="flex justify-between items-center text-sm mb-4 text-center">
          <button
            onClick={() => handleVideoPlayer(currentVideo?._id, "PREVIOUS")}
            className="flex items-center gap-2 text-[#05808b] border border-[#05808b] bg-white pr-4 pl-2 py-1 rounded w-fit hover:bg-[#05808b] hover:text-white duration-500"
          >
            <ArrowLeftIcon /> Previous
          </button>
          <button
            onClick={() => handleVideoPlayer(currentVideo?._id, "NEXT")}
            className="flex items-center gap-4 bg-[#05808b] text-white pl-4 pr-2 py-1 rounded hover:text-[#05808b] border border-[#05808b] hover:bg-white duration-500"
          >
            Next <ArrowRightIcon />
          </button>
        </div>
      )}

      <ul className="flex justify-start gap-24 text-base border-b-2 border-b-gray-300 w-fit text-gray-300 mb-8">
        {!isWatching && (
          <>
            {/* Curriculum Tab */}
            <li
              onClick={() => setSection(0)}
              className={tabClasses + (section === 0 && activeTabClasses)}
            >
              Overview
            </li>
          </>
        )}

        {/* Curriculum Tab */}
        <li
          onClick={() => setSection(1)}
          className={tabClasses + (section === 1 && activeTabClasses)}
        >
          Curriculum
        </li>
        {/* Resources Tab */}
        <li
          onClick={() => setSection(2)}
          className={tabClasses + (section === 2 && activeTabClasses)}
        >
          Resources
        </li>
      </ul>

      {/* Overview */}
      {section === 0 && <OverviewSection data={data} />}
      {/* Curriculum */}

      {section === 1 && <CurriculumSection />}
      {/* Resources */}
      {section === 2 && <ResourcesSection />}
    </div>
  );
}
