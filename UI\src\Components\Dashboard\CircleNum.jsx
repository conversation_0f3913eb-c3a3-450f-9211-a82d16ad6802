import React from "react";
import PropType from "prop-types";

const CircleNum = ({ CircleImg }) => {
  return (
    <div className="max-lg:hidden flex items-center justify-center bg-[#29919B] w-28 m-4 h-28 rounded-full">
      <span className="text-5xl text-center font-bold text-white">
        {CircleImg}
      </span>
    </div>
  );
};

CircleNum.propTypes = {
  CircleImg: PropType.object,
};

export default CircleNum;
