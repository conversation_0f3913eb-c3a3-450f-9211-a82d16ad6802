import React, { useState } from 'react';
import S3Layout from '@/Layout/S3Layout';
import useSideBarRoute from '@/hooks/useSideBarRoute';
import { Tabs } from '@mantine/core';
import StrategyEffectiveness from './StrategyEffectiveness';
import StrategyLibrary from './StrategyLibrary';
import { StrategyProvider } from './StrategyContext';

export default function MitigationStrategiesView() {
  const [activeTab, setActiveTab] = useState('mitigationstrategyeffectiveness');
  const { ESGRiskManagementMenu } = useSideBarRoute();

  const links = [
    {
      value: 'mitigationstrategyeffectiveness',
      label: 'Mitigation Strategy Effectiveness',
    },
    {
      value: 'mitigationstrategylibrary',
      label: 'Mitigation Strategy Library',
    },
  ];

  return (
    <StrategyProvider activeTab={activeTab} setActiveTab={setActiveTab}>
      <>
        <div className="flex justify-between items-center">
          <Tabs className="flex-1" defaultValue={activeTab} onChange={setActiveTab} value={activeTab}>
            <div className="flex flex-row justify-between items-start w-full">
              <Tabs.List
                className={`mb-8 -mt-6 py-2 static  text-[#07838F]  flex flex-row justify-between md:flex-row gap-20 ${
                  activeTab !== 'mitigationstrategyeffectiveness ' ? 'w-full' : 'w-[100%]'
                }`}
              >
                {links.map((link) => (
                  <Tabs.Tab
                    key={link.value}
                    value={link.value}
                    className={` md:w-[40%] bg-[#07838F] text-xl py-6 px-24  text-center border-b-0 rounded-lg shadow-lg ${
                      activeTab === link.value ? 'bg-[#07838F] text-[#fff] font-bold' : 'bg-opacity-50 text-[#07838F]  font-normal'
                    } `}
                  >
                    {link.label}
                  </Tabs.Tab>
                ))}
              </Tabs.List>
            </div>

            <Tabs.Panel value="mitigationstrategyeffectiveness">
              <StrategyEffectiveness />
            </Tabs.Panel>
            <Tabs.Panel value="mitigationstrategylibrary">
              <StrategyLibrary />
            </Tabs.Panel>
          </Tabs>
        </div>
      </>
    </StrategyProvider>
  );
}
