import { useEffect, useState } from 'react';
import Cookies from "js-cookie";

const API_URL = 'https://pcaf-api-staging.azurewebsites.net/profile-analysis/financed-emissions';

const PortfolioBreakdown = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [portfolioData, setPortfolioData] = useState({
    asset_class_breakdown: [],
    emissions_intensity: 0,
    total_financed_emissions: 0,
    weighted_data_quality: 0
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        // Get the token from cookies
        const token = Cookies.get("level_user_token");
        
        if (!token) {
          throw new Error("Authentication token not found");
        }
        
        const response = await fetch(API_URL, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}`);
        }

        const data = await response.json();
        setPortfolioData(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching portfolio data:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Format the numbers nicely
  const formatNumber = (number) => {
    if (number === undefined || number === null) return '0';
    
    if (number >= 1000000000) {
      return (number / 1000000000).toFixed(2) + 'B';
    } else if (number >= 1000000) {
      return (number / 1000000).toFixed(2) + 'M';
    } else if (number >= 1000) {
      return (number / 1000).toFixed(2) + 'K';
    }
    return number.toLocaleString(undefined, { maximumFractionDigits: 2 });
  };

  // Calculate column totals by summing all values
  const calculateColumnTotals = () => {
    const assets = portfolioData.asset_class_breakdown;
    
    // Calculate total portfolio value
    const totalPortfolioValue = assets.reduce((total, item) => total + (item.portfolio_value || 0), 0);
    
    // Calculate total financed emissions
    const totalFinancedEmissions = assets.reduce((total, item) => total + (item.financed_emissions || 0), 0);
    
    // Calculate total emissions intensity (simple sum)
    const totalEmissionsIntensity = assets.reduce((total, item) => total + (item.emissions_intensity || 0), 0);
    
    // Calculate total data quality (simple sum)
    const totalDataQuality = assets.reduce((total, item) => total + (item.data_quality || 0), 0);
    
    return {
      totalPortfolioValue,
      totalFinancedEmissions,
      totalEmissionsIntensity,
      totalDataQuality
    };
  };

  // Get calculated totals
  const totals = calculateColumnTotals();

  return (
    <div>
      <div className='flex flex-col gap-4 rounded-xl bg-white p-4 border-[#E8E7EA] border-2'>
        <h1 className='text-xl font-bold p-2 rounded-xl'>
          Portfolio Breakdown
        </h1>
        
        {loading ? (
          <div className="text-center p-4">Loading portfolio data...</div>
        ) : error ? (
          <div className="text-center p-4 text-red-500">Error: {error}</div>
        ) : (
          <table className="w-full">
            <thead className="border border-[#F5F4F5] bg-[#F5F4F5]">
              <tr className='font-bold'>
                <th className="text-start p-3">Asset Class</th>
                <th className="text-start p-3">Portfolio Value ($M)</th>
                <th className="text-start p-3">Financed Emissions (tCO₂e)</th>
                <th className="text-start p-3">Emissions Intensity (tCO₂e/$M)</th>
                <th className="text-start p-3">Data Quality (1-5)</th>
              </tr>
            </thead>
            <tbody>
              {portfolioData.asset_class_breakdown.map((assetClass, index) => (
                <tr key={index} className="border border-gray-300">
                  <td className="p-3 font-medium border border-gray-300">{assetClass.asset_class}</td>
                  <td className="p-3 border border-gray-300">{formatNumber(assetClass.portfolio_value)}</td>
                  <td className="p-3 border border-gray-300">{formatNumber(assetClass.financed_emissions)}</td>
                  <td className="p-3 border border-gray-300">{formatNumber(assetClass.emissions_intensity)}</td>
                  <td className="p-3 border border-gray-300">{assetClass.data_quality ? assetClass.data_quality.toFixed(1) : '0'}</td>
                </tr>
              ))}
              <tr className="border border-gray-300 font-bold">
                <td className="p-3 font-bold border border-gray-300">TOTAL</td>
                <td className="p-3 border border-gray-300">{formatNumber(totals.totalPortfolioValue)}</td>
                <td className="p-3 border border-gray-300">{formatNumber(totals.totalFinancedEmissions)}</td>
                <td className="p-3 border border-gray-300">{formatNumber(totals.totalEmissionsIntensity)}</td>
                <td className="p-3 border border-gray-300">{totals.totalDataQuality.toFixed(1)}</td>
              </tr>
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
};

export default PortfolioBreakdown;