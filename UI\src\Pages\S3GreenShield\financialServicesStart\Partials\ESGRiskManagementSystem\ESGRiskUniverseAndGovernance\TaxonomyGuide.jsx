import { useState } from "react";
import {
    Box,
    Title,
    Text,
    List,
    Group,
    Accordion,
    ThemeIcon,
    Stack,
    Paper,
} from "@mantine/core";
import { <PERSON>a<PERSON><PERSON><PERSON>, FaCircle } from "react-icons/fa";

export default function EsgRiskTaxonomyGuide() {
    const [activeAccordionItem, setActiveAccordionItem] = useState(null);

    return (
        <Box mx="auto" maxWidth="1200px" p="md">
            <Title order={1} mb="xl">
                ESG Risk Universe and Governance: ESG Risk Taxonomy User Guide
            </Title>

            {/* Overview Section */}
            <Stack spacing="lg">
                <Text size="lg" mb="sm">
                    <b>Overview</b>
                </Text>
                <Text c="dimmed">
                    Provides a detailed categorization of Environmental, Social,
                    and Governance (ESG) risk factors to guide risk assessment
                    and management.
                </Text>

                {/* Page Structure Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Page Structure</b>
                </Text>
                <Accordion
                    multiple
                    value={activeAccordionItem}
                    onChange={(value) => setActiveAccordionItem(value)}
                >
                    {/* Risk Categories */}
                    <Accordion.Item value="risk-categories">
                        <Accordion.Control>
                            <Group position="apart">
                                <Text fw={800}>Risk Categories:</Text>
                                <ThemeIcon variant="light" radius="xl">
                                    <FaCircle size="1rem" />
                                </ThemeIcon>
                            </Group>
                        </Accordion.Control>
                        <Accordion.Panel px="md">
                            <List listStyleType="disc" spacing="sm">
                                {/* Environmental Factors */}
                                <List.Item>
                                    <Text fw={700}>
                                        Environmental Factors:
                                    </Text>
                                    <List
                                        listStyleType="upper-roman"
                                        spacing="sm"
                                        ml="1.5rem"
                                    >
                                        <List.Item>
                                            <Text fw={400}>
                                                Transition Risk (e.g.,
                                                Greenhouse gas emissions, Policy
                                                and legal changes)
                                            </Text>
                                        </List.Item>
                                        <List.Item>
                                            <Text fw={400}>
                                                Physical Climate (e.g., Acute
                                                events, Chronic changes)
                                            </Text>
                                        </List.Item>
                                        <List.Item>
                                            <Text fw={400}>
                                                Natural Capital (e.g., Water
                                                usage/security, Biodiversity)
                                            </Text>
                                        </List.Item>
                                        <List.Item>
                                            <Text fw={400}>
                                                Waste Management (e.g.,
                                                Recycling processes, Air
                                                emissions)
                                            </Text>
                                        </List.Item>
                                        <List.Item>
                                            <Text fw={400}>
                                                Circular Economy (e.g.,
                                                Sustainable packaging, Waste
                                                reduction)
                                            </Text>
                                        </List.Item>
                                        <List.Item>
                                            <Text fw={400}>
                                                Environmental Compliance (e.g.,
                                                Regulatory permits, Impact
                                                assessments)
                                            </Text>
                                        </List.Item>
                                    </List>
                                </List.Item>

                                {/* Social Factors */}
                                <List.Item>
                                    <Text fw={700}>Social Factors:</Text>
                                    <List
                                        listStyleType="upper-roman"
                                        spacing="sm"
                                        ml="1.5rem"
                                    >
                                        <List.Item>
                                            <Text fw={400}>
                                                Health and Safety (e.g.,
                                                Occupational health, Consumer
                                                safety)
                                            </Text>
                                        </List.Item>
                                        <List.Item>
                                            <Text fw={400}>
                                                Social Security (e.g., Data
                                                privacy, Human rights)
                                            </Text>
                                        </List.Item>
                                        <List.Item>
                                            <Text fw={400}>
                                                Human Capital (e.g., Diversity
                                                and inclusion, Pay for
                                                performance)
                                            </Text>
                                        </List.Item>
                                        <List.Item>
                                            <Text fw={400}>
                                                Labor Practices (e.g., Fair
                                                labor standards, Collective
                                                bargaining)
                                            </Text>
                                        </List.Item>
                                        <List.Item>
                                            <Text fw={400}>
                                                Customer Welfare (e.g., Product
                                                quality, Customer satisfaction)
                                            </Text>
                                        </List.Item>
                                        <List.Item>
                                            <Text fw={400}>
                                                Community Relations (e.g., Local
                                                community impact, Indigenous
                                                rights)
                                            </Text>
                                        </List.Item>
                                    </List>
                                </List.Item>

                                {/* Governance Factors */}
                                <List.Item>
                                    <Text fw={700}>
                                        Governance Factors:
                                    </Text>
                                    <List
                                        listStyleType="upper-roman"
                                        spacing="sm"
                                        ml="1.5rem"
                                    >
                                        <List.Item>
                                            <Text fw={400}>
                                                Governance Structure (e.g.,
                                                Board diversity, Stakeholder
                                                engagement)
                                            </Text>
                                        </List.Item>
                                        <List.Item>
                                            <Text fw={400}>
                                                Risk Management (e.g., Policies
                                                and standards, Risk appetite
                                                strategy)
                                            </Text>
                                        </List.Item>
                                        <List.Item>
                                            <Text fw={400}>
                                                Transparency and Reporting
                                                (e.g., Political lobbying, Tax
                                                strategy)
                                            </Text>
                                        </List.Item>
                                        <List.Item>
                                            <Text fw={400}>
                                                Corporate Behaviour (e.g.,
                                                Business ethics, Whistleblower
                                                protection)
                                            </Text>
                                        </List.Item>
                                        <List.Item>
                                            <Text fw={400}>
                                                Cybersecurity Governance (e.g.,
                                                Data protection policies,
                                                Incident response planning)
                                            </Text>
                                        </List.Item>
                                        <List.Item>
                                            <Text fw={400}>
                                                Innovation and Adaptability
                                                (e.g., R&D investment,
                                                Technology adoption)
                                            </Text>
                                        </List.Item>
                                    </List>
                                </List.Item>
                            </List>
                        </Accordion.Panel>
                    </Accordion.Item>
                </Accordion>

                {/* Interacting with the Page Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Interacting with the ESG Risk Taxonomy Page</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>Review Categories:</Text>
                        <Text size="sm">
                            Explore the detailed sub-factors under each ESG
                            category.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Configure Data:</Text>
                        <Text size="sm">
                            Use the Configure Data button to customize or update
                            the taxonomy based on your organization's needs.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Cross-Reference:</Text>
                        <Text size="sm">
                            Use this page as a reference when completing the
                            Assessment Matrix.
                        </Text>
                    </List.Item>
                </List>

                {/* Understanding Progress Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Understanding Your Progress</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>Taxonomy Overview:</Text>
                        <List listStyleType="disc" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={700}>
                                    Comprehensive Coverage:
                                </Text>
                                <Text size="sm">
                                    Lists all relevant ESG risks across
                                    Environmental, Social, and Governance
                                    domains.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Clarity:</Text>
                                <Text size="sm">
                                    Sub-factors provide specific areas to assess
                                    and manage.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Customization:</Text>
                                <Text size="sm">
                                    Allows tailoring to organizational context.
                                </Text>
                            </List.Item>
                        </List>
                    </List.Item>
                </List>

                {/* Next Steps Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Next Steps</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text>
                            Review the taxonomy to understand applicable ESG
                            risks.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Proceed to the Assessment Matrix page to evaluate
                            risks based on these categories.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Update the taxonomy as needed using the Configure
                            Data feature.
                        </Text>
                    </List.Item>
                </List>

                {/* Tip Section */}
                <Paper shadow="sm" p="md" radius="md" mt="xl" withBorder>
                    <Group position="apart">
                        <ThemeIcon variant="light" radius="xl" size="2rem">
                            <FaCheck size="1.2rem" />
                        </ThemeIcon>
                        <Text fw={500}>
                            Regularly revisit the taxonomy to align with
                            evolving ESG standards!
                        </Text>
                    </Group>
                </Paper>
            </Stack>
        </Box>
    );
}
