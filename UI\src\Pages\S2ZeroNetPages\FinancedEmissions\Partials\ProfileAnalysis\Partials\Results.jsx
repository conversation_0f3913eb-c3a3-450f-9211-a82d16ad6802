import { useEffect, useState } from 'react';
import Cookies from "js-cookie";
// import { useTranslation } from "react-i18next";

const API_URL = 'https://pcaf-api-staging.azurewebsites.net/profile-analysis/financed-emissions';

const Results = () => {
  // const { t } = useTranslation();
  const [data, setData] = useState({
    total_financed_emissions: 0,
    emissions_intensity: 0,
    weighted_data_quality: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currency, setCurrency] = useState('USD');

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        // Get the correct token from cookies - using the same token name as in EmissionsOverView
        const token = Cookies.get("level_user_token");
        
        if (!token) {
          throw new Error("Authentication token not found");
        }
        
        const response = await fetch(API_URL, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}`);
        }

        const responseData = await response.json();
        setData({
          total_financed_emissions: responseData.total_financed_emissions || 0,
          emissions_intensity: responseData.emissions_intensity || 0,
          weighted_data_quality: responseData.weighted_data_quality || 0
        });
        setError(null);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleCurrencyChange = (e) => {
    setCurrency(e.target.value);
    // Here you could add logic to refetch or recalculate values based on currency
  };

  // Format the numbers nicely - similar to what's used in EmissionsOverView
  const formatNumber = (number) => {
    if (number >= 1000000000) {
      return (number / 1000000000).toFixed(2) + 'B';
    } else if (number >= 1000000) {
      return (number / 1000000).toFixed(2) + 'M';
    } else if (number >= 1000) {
      return (number / 1000).toFixed(2) + 'K';
    }
    return number.toLocaleString(undefined, { maximumFractionDigits: 2 });
  };

  return (
    <div>
      <div className='flex flex-col gap-4 rounded-xl bg-[#dfedf0] p-4 border-[#E8E7EA] border-2'>
        <div className='flex flex-row'>
          <div className='w-5/6'></div>
          <p className='font-bold flex flex-row w-1/6 bg-[#fff] py-2 px-4 rounded-md justify-center'>Currency:
            <select value={currency} onChange={handleCurrencyChange}>
              <option value="USD">USD</option>
              <option value="GBP">GBP</option>
              <option value="EUR">EUR</option>
            </select>
          </p>
        </div>
        {loading ? (
          <div className='p-4 text-center'>Loading data...</div>
        ) : error ? (
          <div className='p-4 text-center text-red-500'>Error: {error}</div>
        ) : (
          <div className='flex flex-row gap-6 justify-between items-center p-4 bg-[#dfedf0]'>
            <div className='flex flex-col items-center gap-4 bg-[#fff] p-4 rounded-lg w-1/3'>
              <p className='text-sm'>Financed Emissions</p>
              <p className='text-2xl font-bold'>{formatNumber(data.total_financed_emissions)} tCO₂e</p>  
            </div>
            <div className='flex flex-col items-center gap-4 bg-[#fff] p-4 rounded-lg w-1/3'>
              <p className='text-sm'>Emissions Intensity</p>
              <p className='text-2xl font-bold'>{formatNumber(data.emissions_intensity)} tCO₂e/$M</p>  
            </div>
            <div className='flex flex-col items-center gap-4 bg-[#fff] p-4 rounded-lg w-1/3'>
              <p className='text-sm'>Weighted Data Quality</p>
              <p className='text-2xl font-bold'>{data.weighted_data_quality.toFixed(1)}</p>  
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Results;