import React from "react";

export default function HeatMap({refrefnce}) {
  const s  = [10, 26];
  return (
    <div ref={refrefnce} >
      <div className="squar-chart relative h-[400px]  rounded-xl">
        {[4].map((_, i) => (
          <div
            key={i}
            className="z-20 shadow-md"
            style={{
              position: "absolute",
              width: "10px",
              height: "10px",
              borderRadius: "50%",
              backgroundColor: "white",
              top: s[i] + 40 + "px",
              left: s[i] + 45 + "px",
            }}
          />
        ))}
        {[4].map((_, i) => (
          <div
            key={i}
            className="z-20 shadow-md"
            style={{
              position: "absolute",
              width: "10px",
              height: "10px",
              borderRadius: "50%",
              backgroundColor: "white",
              top: s[i] + 140 + "px",
              left: s[i] + 65 + "px",
            }}
          />
        ))}
        {[4].map((_, i) => (
          <div
            key={i}
            className="z-20 shadow-md"
            style={{
              position: "absolute",
              width: "10px",
              height: "10px",
              borderRadius: "50%",
              backgroundColor: "white",
              top: s[i] + 70 + "%",
              left: s[i] + 15 + "px",
            }}
          />
        ))}
        {[4].map((_, i) => (
          <div
            key={i}
            className="z-20 shadow-md"
            style={{
              position: "absolute",
              width: "10px",
              height: "10px",
              borderRadius: "50%",
              backgroundColor: "white",
              top: s[i] + 50 + "%",
              left: s[i] + 250 + "px",
            }}
          />
        ))}
        {[4].map((_, i) => (
          <div
            key={i}
            className="z-20 shadow-md"
            style={{
              position: "absolute",
              width: "10px",
              height: "10px",
              borderRadius: "50%",
              backgroundColor: "white",
              top: s[i] + 20 + "%",
              left: s[i] + 350 + "px",
            }}
          />
        ))}
        {[4].map((_, i) => (
          <div
            key={i}
            className="z-20 shadow-md"
            style={{
              position: "absolute",
              width: "10px",
              height: "10px",
              borderRadius: "50%",
              backgroundColor: "white",
              top: s[i] + 25 + "%",
              right: s[i] + 250 + "px",
            }}
          />
        ))}
        {[4].map((_, i) => (
          <div
            key={i}
            className="z-20 shadow-md"
            style={{
              position: "absolute",
              width: "10px",
              height: "10px",
              borderRadius: "50%",
              backgroundColor: "white",
              top: s[i] + 45 + "%",
              right: s[i] + 350 + "px",
            }}
          />
        ))}
        {[4].map((_, i) => (
          <div
            key={i}
            className="z-20 shadow-md"
            style={{
              position: "absolute",
              width: "10px",
              height: "10px",
              borderRadius: "50%",
              backgroundColor: "white",
              top: s[i] + 450 + "%",
              right: s[i] + 150 + "px",
            }}
          />
        ))}
        {[4].map((_, i) => (
          <div
            key={i}
            className="z-20 shadow-md"
            style={{
              position: "absolute",
              width: "10px",
              height: "10px",
              borderRadius: "50%",
              backgroundColor: "white",
              top: s[i] + 45 + "%",
              right: s[i] + 100 + "px",
            }}
          />
        ))}
      </div>
    </div>
  );
}
