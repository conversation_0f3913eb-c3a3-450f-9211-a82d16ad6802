import MainLayout from "@/Layout/MainLayout";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import { useContext, useState } from "react";
import { useTranslation } from "react-i18next";

import HeaderSection from "@/Components/Academy/HeaderSection";

import PaginationSection from "@/Components/Academy/PaginationSection";
import CoursesGridSection from "@/Components/Academy/CoursesGridSection";

import { AcademyContext } from "@/Contexts/AcademyContext";
import { IoMdHome } from "react-icons/io";
function Academy() {
  const { courses } = useContext(AcademyContext);
  const { GreenHubMenu } = useSideBarRoute();
  const [page, setPage] = useState(1);
  const { t } = useTranslation();

  const ITEMS_PER_PAGE = 8;
  const firstItem = ITEMS_PER_PAGE * (page - 1);
  const currentCourses = courses.slice(firstItem, firstItem + ITEMS_PER_PAGE);

  // fetch courses function

  // set Action for the card

  // fetching courses

  return (
    <MainLayout
      menus={GreenHubMenu}
      navbarTitle="Academy"
      breadcrumbItems={[
        { title: <IoMdHome size={20}/>, href: "/get-started" },
        { title: "Academy", href: "#" },
      ]}
    >
      {/* Header Section */}
      <HeaderSection path="ACADEMY" subtitle={t("AcademySubtitle")} />
      {/* the grid of courses */}
      <CoursesGridSection courses={currentCourses} />
      {/* pages pagination navigation */}
      <PaginationSection
        totalItems={courses.length}
        itemsPerPage={ITEMS_PER_PAGE}
        currentPage={page}
        onPageChange={setPage}
      />
    </MainLayout>
  );
}

export default Academy;

// Header Section Component
