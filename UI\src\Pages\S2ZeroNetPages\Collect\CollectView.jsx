import ApiS2 from "@/Api/apiS2Config";
import { Tabs } from "@mantine/core";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import AIDataDrivenTab from "./Partials/AIDataDriven/AIDataDrivenTab";
import BatchInputsTab from "./Partials/Batches/BatchInputsTab";
import ManualInputsTab from "./Partials/Manuals/ManualInputsTab";
import { AIDataDrivenProvider } from "./Partials/AIDataDriven/AiDataDrivenContext";
import TransportationAndDestribution from "@/Pages/CarbonSystem/EmissionOverView/components/TransportationAndDestribution";

export default function CollectView({ target }) {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState(target ? target : "manual-input");
  const [assetTypeDrop, setAssetTypeDrop] = useState([]);
  const [assetTypeAll, setAssetTypeAll] = useState([]);
  // const [dataCFByAT, setDataCFByAT] = useState();

  const [AssetsError, setAssetsError] = useState();
  const [loading, setLoading] = useState(false);
  const [error, SetError] = useState();
  const [LoadingTable, setLoadingTable] = useState(false);
  const [dataBatch, SetDataBatch] = useState();
  const [dataManual, SetDataManual] = useState();
  const [allItemSpecificities, setAllItemSpecificities] = useState();
  const handelAssetType = async () => {
    setLoading(true);
    try {
      setLoading(true);
      const { data: getAllAssetTypes } = await ApiS2.get(
        "/carbon-factors/get-all-asset-types"
      );
      // const { data: getAllCustomFactor } = await ApiS2.get(
      //   "/carbon-factors/get-all-custom-factors"
      // );

      setLoading(false);
      setAssetTypeDrop(getAllAssetTypes.map((item) => item.asset));
      setAssetTypeAll(getAllAssetTypes);
      // setDataCFByAT(getAllCustomFactor);
    } catch (error) {
      setLoading(false);
      error.response?.data.error
        ? setAssetsError(error.response?.data.error)
        : "";
      error.response?.data.message
        ? setAssetsError(error.response.data.message)
        : "";
    }
  };

  const getTableData = async (inputTypes) => {
    setLoadingTable(true);
    try {
      setLoadingTable(true);
      const { data } = await ApiS2.get("/admin/list-company-input-records", {
        headers: { inputType: inputTypes },
      });
      inputTypes === "manual" &&
        data.message !== "No data sets found" &&
        SetDataManual(data);
      inputTypes === "batch" &&
        data.message !== "No data sets found" &&
        SetDataBatch(data);

      setLoadingTable(false);
    } catch (error) {
      setLoadingTable(false);
      error.response.data.error ? SetError(error.response.data.error) : "";
      error.response?.data.message ? SetError(error.response.data.message) : "";
      //console.log(error);
    }
  };

  const Status = {
    Pending: {
      bg: "#ffeecd",
      text: "#FFAB07",
      // border: "#00C0A9",
    },
    Accepted: {
      bg: "#ccf2d7",
      text: "#01BD36",
      // border: "#FF6007",
    },
    Rejected: {
      bg: "#eecccc",
      text: "#AB0202",
      // border: "#FF6007",
    },
  };

  const get_item_specificities = async () => {
    try {
      const { data } = await ApiS2.get(
        "/carbon-factors/get_item_specificities"
      );
      setAllItemSpecificities(data);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    get_item_specificities();
  }, []);


  return (
    <div className="bg-[#F7F4F4] font-inter">

      <Tabs className="" defaultValue={activeTab} onChange={setActiveTab}>
        <Tabs.List
          justify="center"
          className="mb-6 block  md:flex md:justify-around text-gray-500 py-3 rounded-md overflow-hidden bg-white before:border-none"
        >
          <Tabs.Tab
            value="manual-input"
            className={`text-lg mx-auto Enter-records
                ${
                  activeTab == "manual-input"
                    ? "text-primary border-b-2 border-primary"
                    : " border-none"
                } 
                py-3 font-semibold hover:bg-transparent hover:opacity-100`}
          >
            {t("tabs.manualInputs")}
          </Tabs.Tab>

          <Tabs.Tab
            value="batch-input"
            className={`text-lg mx-auto ${
              activeTab == "batch-input"
                ? "text-primary border-b-2 border-primary"
                : " border-none"
            } 
            py-3 font-semibold hover:bg-transparent hover:opacity-100`}
          >
            {t("tabs.batchInputs")}
          </Tabs.Tab>
          <Tabs.Tab
            value="AIDataDriven"
            className={`text-lg mx-auto bg-gray-300 
              ${
                activeTab == "AIDataDriven"
                  ? "bg-gradient-to-r from-[#FFF4D7] via-[#DADEFB] to-[#D8FFDC] text-[#2A3153]"
                  : "border-none"
              } font-semibold rounded-lg hover:bg-gray-300 hover:opacity-100 border-none`}
          >
            <p>
              {t("AI Data-Driven")}{" "}
              {activeTab == "AIDataDriven" && (
                <span className="text-[7px] text ml-1 bg-[#e09432] p-1 rounded-full text-white">
                  Setup Required
                </span>
              )}
            </p>
          </Tabs.Tab>

          <Tabs.Tab
            value="Transportation and distribution"
            className={`text-lg mx-auto
                ${
                  activeTab == "Transportation and destribution"
                    ? "text-primary border-b-2 border-primary"
                    : " border-none"
                } 
                py-3 font-semibold hover:bg-transparent hover:opacity-100`}
          >
            {t("Transportation and Distribution")}{" "}
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="manual-input">
          <ManualInputsTab
            AssetsError={AssetsError}
            error={error}
            // dataCFByAT={dataCFByAT}
            assetTypeAll={assetTypeAll}
            assetTypeDrop={assetTypeDrop}
            // companyAssetDrop={companyAssetDrop}
            handelAssetType={handelAssetType}
            loading={loading}
            getTableData={getTableData}
            loadingTable={LoadingTable}
            data={dataManual}
            Status={Status}
            allItemSpecificities={allItemSpecificities}
            activeTab={activeTab}
          />
        </Tabs.Panel>

        <Tabs.Panel value="batch-input">
          <BatchInputsTab
            assetTypeAll={assetTypeAll}
            handelAssetType={handelAssetType}
            data={dataBatch}
            getTableData={getTableData}
            error={error}
            loadingApprove={LoadingTable}
            Status={Status}
            activeTab={activeTab}
            allItemSpecificities={allItemSpecificities}
          />
        </Tabs.Panel>

        <Tabs.Panel value="AIDataDriven">
          <AIDataDrivenProvider>
            <AIDataDrivenTab activeTab={activeTab} />
          </AIDataDrivenProvider>
        </Tabs.Panel>

        <Tabs.Panel value="Transportation and destribution">
          <TransportationAndDestribution />
        </Tabs.Panel>
      </Tabs>
    </div>
  );
}
