import { useState } from "react";
import cx from "clsx";
import {
  Button,
  Checkbox,
  Input,
  MultiSelect,
  Table,
  TextInput,
} from "@mantine/core";
import { BiEdit } from "react-icons/bi";
import ApiS2 from "@/Api/apiS2Config";
import { showNotification } from "@mantine/notifications";
import Loading from "@/Components/Loading";
import axios from "axios";
import Cookies from "js-cookie";
import { HiOutlineFolderDownload } from "react-icons/hi";
import { formateTextFunction } from "@/Utils/helpersFunctions";



const CustomFactorRow = ({
  item,
  setItemId,
  itemId,
  selection,
  toggleRow,
  fetchAgain,
  emissionName,
  multiAssetSelectionsData,
}) => {


  const [isresiting, setIsresiting] = useState(false);
  const [isSubmiting, setIsSubmiting] = useState(false);

  // assets ids
  const [assetsIds, setAssetsIds] = useState([]);

  const msg = (txt, color = "green") => {
    showNotification({ message: txt, color: color, timing: 7000 });
  };

  const [isLoadingDownload, setIsLoadingDownload] = useState(false);

  const [itemErrors, setItemErrors] = useState({});

  const [formState, setFormState] = useState({
    activity: item.activity,
    eFactors: item.eFactors,
    uom: item.uom,
    emissionIntensity: "",
  });

  const handleInputChange = (key, value) => {
    setFormState((prevState) => ({
      ...prevState,
      [key]: value,
    }));
  };
  const [specificFactor, setSpecificFactor] = useState(item.specificFactor);

  const selected = selection.includes(item.id);

  const assetNamesList = multiAssetSelectionsData.map((el) => el.assetName);

  const handleAddMoreAssets = (values) => {
    const assetsItems = multiAssetSelectionsData.filter((el) =>
      values.includes(el.assetName)
    );
    const assetsIds = assetsItems.map((el) => el.id);
    setAssetsIds(assetsIds);
  };

  const updateAssets = async () => {
    if (assetsIds.length == 0) {
      msg(`Select asset`, `red`);
      return;
    }
    let newasset = {
      [item.id]: {
        companyAssetIds: assetsIds,
      },
    };
    setIsSubmiting(true);
    try {
      let res = await ApiS2.post(`admin/update-custom-factors`, newasset);
      msg(res.data.message);
      fetchAgain();
    } catch (error) {
      msg(error.response.data.message, "red");
    }
    setIsSubmiting(false);
  };

  // const source = item.source.type == 'url' && item?.source?.source?.map((s,i)=> <a key={i} href={s.file_url}> {s.file_name}</a>);



  if (isresiting) {
    return <Loading />;
  }

  let activites =
    formState.activity == null
      ? []
      : Object.entries(formState.activity).map(([key, value]) => {
          return `${formateTextFunction(key)}: ${value}`;
        });

  let efactors =
    formState.eFactors == null
      ? []
      : Object.entries(formState.eFactors).map(([key, value]) => {
          return `${formateTextFunction(key)}: ${value}`;
        });

  let uom =
    formState.uom == null
      ? []
      : Object.entries(formState.uom).map(([key, value]) => {
          return `${formateTextFunction(key)}: ${value}`;
        });

  let companyAssets = item.linked_company_assets.map((ca) => (
    <div key={ca.id}>{ca.asset}</div>
  ));

  const handleDownload = async (fileid) => {
    setIsLoadingDownload(true);
    try {
      const response = await axios.get(
        `https://docvault-staging.azurewebsites.net/api/v1/files/${fileid}/download`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("level_user_token")}`,
            "Content-Type": "application/octet-stream",
            Accept: "application/octet-stream",
            withCredentials: true,
            connection: "keep-alive",
            "Cache-Control": "no-cache",
          },
          responseType: "blob",
        }
      );

      const blobUrl = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = blobUrl;
      link.setAttribute("download", "file");
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(blobUrl);
    } catch (error) {
      console.error("Download failed", error);
    } finally {
      setIsLoadingDownload(false);
    }
  };

  return (
    <Table.Tr
      key={item.id}
      className={`${cx({
        ["bg-[#07838F1A]"]: selected,
      })} text-sm font-bold text-[#626364] text-center`}
    >
      <Table.Td>
        <Checkbox
          checked={selection.includes(item.id)}
          onChange={() => toggleRow(item.id)}
          color="#07838F"
        />
      </Table.Td>

      <Table.Td className="flex justify-center items-center h-[100px]">
        {item.id == itemId ? (
          <div className="flex gap-2 h-full items-center">
            <button
              className="cursor-pointer text-white bg-red-500 rounded-xl h-[30px] w-10 p-1 "
              onClick={() => setItemId(0)}
            >
              X
            </button>
            {item.specificFactor ? (
              <span>Edit Under development</span>
            ) : (
              <button
                onClick={() => updateAssets()}
                className="bg-primary text-white h-430px] flex justify-center items-center p-2 rounded-xl"
              >
                Update Assets {isSubmiting && <Loading />}
              </button>
            )}

            {/* <button
              disabled={true}
              onClick={() => updateCustom(item)}
              className="bg-primary text-white h-[30px] flex justify-center items-center p-2 rounded-xl"
            >
              Send {isSubmiting && <Loading />}
            </button> */}
          </div>
        ) : (
          <button
            className="h-full flex items-center"
            title="Under development"
            onClick={() => setItemId(item.id)}
            type="button"
          >
            <BiEdit title="Under development" />
          </button>
        )}
      </Table.Td>

      <Table.Td className="pl-5">
        <div className="flex items-center justify-center capitalize">
          {String(specificFactor)}
          {/* {item.id == itemId ? (
            <>
              {specificFactor ? (
                <Table.Td>
                  <Checkbox
                    checked={true}
                    onChange={() => handleSpecificF("remove")}
                    color="#07838F"
                  />
                </Table.Td>
              ) : (
                <Table.Td>
                  <Checkbox
                    checked={false}
                    onChange={() => handleSpecificF("add")}
                    color="#07838F"
                  />
                </Table.Td>
              )}
            </>
          ) : (
            <>{String(specificFactor)}</>
          )} */}
        </div>
      </Table.Td>

      <Table.Td className="pl-5 w-40">
        {/* handle adding assets to the existing one. */}
        {item.id == itemId ? (
          <Input.Wrapper label="Select Assets">
            <MultiSelect
              disabled={false}
              onChange={handleAddMoreAssets}
              placeholder="Enter Assets ..."
              data={assetNamesList}
            />
          </Input.Wrapper>
        ) : (
          ""
        )}
        <div className="w-40">

        {companyAssets}
        </div>
      </Table.Td>

      <Table.Td className="pl-5 ">
        <div className="w-52">{emissionName}</div>
      </Table.Td>

      <Table.Td className={`pl-5 w-[200px]`}>
        {item.id == itemId ? (
          <>
            {specificFactor ? (
              <TextInput
                // error={formState.activity.length < 2  && "activity must be at least 2 characters"}
                defaultValue={"item.activity"}
                onChange={(e) => handleInputChange("activity", e.target.value)}
              />
            ) : (
              <div className="flex flex-col gap-2 min-w-36">
                {activites.map((ac, idx) => (
                  <div className="w-full" key={idx}>{ac}</div>
                ))}
              </div>
            )}
            {itemErrors.activity && (
              <span className="text-red-500 text-xs mt-1">
                {itemErrors.activity}
              </span>
            )}
          </>
        ) : (
          <div className="flex min-w-32 flex-col  gap-2">
            {activites.map((ac, idx) => (
              <div className="min-w-32" key={idx}>{ac}</div>
            ))}
          </div>
        )}
      </Table.Td>

      <Table.Td className="pl-5">
        {item.id == itemId ? (
          <>
            {specificFactor ? (
              <Input
                defaultValue={item.eFactors}
                onChange={(e) => handleInputChange("eFactors", e.target.value)}
              />
            ) : (
              "update"
            )}
            {itemErrors.eFactors && (
              <span className="text-red-500 text-xs mt-1">
                {itemErrors.eFactors}
              </span>
            )}
          </>
        ) : (
          <div className="flex flex-col min-w-36 gap-2 w-52">
            {efactors.map((ac, idx) => (
              <div key={idx}>{ac}</div>
            ))}
          </div>
        )}
      </Table.Td>
      <Table.Td className="px-5">
        <>
          {item.id == itemId ? (
            <>
              {specificFactor ? (
                <Input
                  defaultValue={item.uom}
                  onChange={(e) => handleInputChange("uom", e.target.value)}
                />
              ) : (
                "update"
              )}
              {itemErrors.uom && (
                <span className="text-red-500 text-xs mt-1">
                  {itemErrors.uom}
                </span>
              )}
            </>
          ) : (
            <div className="flex flex-col gap-2  text-nowrap min-w-32">
              {uom.map((u, idx) => (
                <div key={idx}>{u}</div>
              ))}
            </div>
          )}
        </>
      </Table.Td>
      {/* source */}
      <Table.Td className="pl-5 w-40">
        {item?.source != null ? (
          <>

            {/* array of objects [{name: 'fileid'}] */}
            {item?.source.type === "url" ? 
            
            (
              <>
              {Array.isArray(item.source.source) ? 
              <>
              {console.log(item.source.source)}
              {item.source.source.map((s,i)=> 
              <Button
              disabled={isLoadingDownload}
              variant="default" className="bg-secondary-primary-lite h-10 p-2 rounded-lg flex items-center" key={i}
               onClick={() => handleDownload(s?.name)}>
                {isLoadingDownload ? <div className="submit-loader" />
                :
                <HiOutlineFolderDownload size={19} className="mx-1"/>
              }
                <span>Download</span>
                </Button>
              )}
              </>
              :
               <>
              </>
               }
              </>
            ) : (
              <> 
              {/* 'text' */}
              {item.source.source} </>
            )}
          </>
        ) : (
          ""
        )}
      </Table.Td>
    </Table.Tr>
  );
};

export default CustomFactorRow;
