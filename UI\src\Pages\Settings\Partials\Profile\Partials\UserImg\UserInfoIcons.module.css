.icon {
  color: light-dark(var(--mantine-color-gray-5), var(--mantine-color-dark-3));
}

.name {
  font-family:
    Greycliff CF,
    var(--mantine-font-family);
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}
.avatarRipple {
  animation-name: ripple; /* This now refers to the local @keyframes */
  animation-duration: 1.8s;
  animation-timing-function: ease-out;
  animation-iteration-count: infinite;
}