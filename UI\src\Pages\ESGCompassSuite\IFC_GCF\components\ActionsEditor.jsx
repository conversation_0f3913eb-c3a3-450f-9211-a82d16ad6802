// ActionsEditor.jsx - Component for managing actions
import { useEffect, useMemo, useState } from "react";
import { Button, MultiSelect, TextInput } from "@mantine/core";
import { useAssessmentStore } from "../AssessmentContext";

const ActionsEditor = ({
    actions: actionsProp,
    onAddAction,
    onDeleteAction,
}) => {
    const [actionText, setActionText] = useState("");
    const [dueDate, setDueDate] = useState("");
    const [taggedUsers, setTaggedUsers] = useState([]);
    const [actions, setActions] = useState(actionsProp);
    const { companyUsers } = useAssessmentStore();

    useEffect(() => {
        setActions(actionsProp);
    }, [actionsProp]);

    const handleSubmit = async () => {
        if (!actionText.trim() || !dueDate) return;

        try {
            // Format the action data according to your backend requirements
            const actionData = {
                content: actionText,
                due_date: dueDate || null,
                tagged_user_ids: taggedUsers
                    .map((userId) => Number(userId))
                    .filter((userId) => userId !== null),
            };
            await onAddAction(actionData);
            // Reset form fields
            setActionText("");
            setDueDate("");
            setTaggedUsers([]);
        } catch (error) {
            console.error("Failed to add action:", error);
        }
    };

    const handleDeleteAction = async (actionId) => {
        try {
            await onDeleteAction(actionId);
        } catch (error) {
            console.error("Failed to delete action:", error);
        }
    };

    // Prepare options for MultiSelect
    const userOptions = useMemo(() => {
        return companyUsers.map((user) => ({
            value: user.id.toString(),
            label: user.name,
        }));
    }, [companyUsers]);

    return (
        <div className="flex flex-wrap gap-4">
            {/* Action input */}
            <TextInput
                radius="md"
                placeholder="Enter action item"
                value={actionText}
                onChange={(e) => setActionText(e.target.value)}
                className="min-w-44 flex-grow"
            />

            {/* Due date input */}
            <TextInput
                type="date"
                radius="md"
                placeholder="Due Date"
                value={dueDate}
                onChange={(e) => setDueDate(e.target.value)}
                className="min-w-44 flex-grow"
            />

            <MultiSelect
                radius="md"
                data={userOptions}
                placeholder="Tag Owner"
                searchable
                value={taggedUsers}
                onChange={setTaggedUsers}
                className="min-w-56 flex-grow"
                styles={{ dropdown: { zIndex: 1000 } }}
            />

            {/* Add button */}
            <Button onClick={handleSubmit} radius="xl" className="w-full">
                Add Action
            </Button>

            {/* Action pills display */}
            <div className="flex flex-wrap items-center w-full gap-2 h-16 overflow-hidden overflow-y-auto">
                {actions
                    .filter((action) => action != null)
                    .map((action, index) => (
                        <div
                            key={action.id || index}
                            className="flex w-fit gap-4 rounded-full px-3 items-center bg-blue-100 hover:bg-blue-200 transition-colors"
                        >
                            <span className="text-sm py-1">
                                {action.content}
                            </span>
                            <button
                                onClick={() => handleDeleteAction(action.id)}
                                className="w-3 relative aspect-square group"
                                aria-label="Delete action"
                            >
                                <span className="w-full h-0.5 rounded-full bg-black group-hover:rotate-0 rotate-45 transition-all duration-200 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
                                <span className="w-full h-0.5 rounded-full bg-black group-hover:rotate-0 -rotate-45 transition-all duration-200 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
                            </button>
                        </div>
                    ))}
            </div>
        </div>
    );
};

export default ActionsEditor;
