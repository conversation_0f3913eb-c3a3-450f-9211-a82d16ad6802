import { useEffect, useState } from 'react';
import { Modal } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { useTranslation } from 'react-i18next';
import { MdOutlineRemoveRedEye } from 'react-icons/md';
import Loading from '@/Components/Loading';
import { Worker, Viewer } from '@react-pdf-viewer/core';
import { defaultLayoutPlugin } from '@react-pdf-viewer/default-layout';
import '@react-pdf-viewer/core/lib/styles/index.css';
import '@react-pdf-viewer/default-layout/lib/styles/index.css';
import * as pdfjsLib from 'pdfjs-dist/legacy/build/pdf';
import 'pdfjs-dist/build/pdf.worker.entry';

const ViewPDF = ({ pdfUrl, btnStyle, text, disabled }) => {
  const { t } = useTranslation();
  //console.log(pdfUrl);

  const defaultLayoutPluginInstance = defaultLayoutPlugin();

  const [opened, { open, close }] = useDisclosure(false);

  const [isBlocked, setIsBlocked] = useState(false);

  const [workerUrl, setWorkerUrl] = useState('');

  useEffect(() => {
    // Check if the PDF can be fetched and determine the worker URL
    const checkPDF = async () => {
      try {
        const response = await fetch(pdfUrl);
        if (!response.ok) {
          throw new Error('Failed to fetch PDF');
        }

        const arrayBuffer = await response.arrayBuffer();
        const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
        const version = pdf._pdfInfo.version;

        // Set worker URL based on the version
        if (version === '3.11.174') {
          setWorkerUrl('https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js');
        } else if (version === '3.4.120') {
          setWorkerUrl('https://unpkg.com/pdfjs-dist@3.4.120/build/pdf.worker.min.js');
        } else {
          // Set a default worker URL if the version is unknown
          setWorkerUrl('https://unpkg.com/pdfjs-dist/build/pdf.worker.min.js');
        }

        setIsBlocked(false);
      } catch (error) {
        console.error('Error loading PDF:', error);
        setIsBlocked(true);
      }
    };

    checkPDF();
  }, []);

  return (
    <>
      <button onClick={open} className={btnStyle} disabled={disabled}>
        <span>{t(text)}</span>
        <span>
          <MdOutlineRemoveRedEye className="text-2xl ms-1" />
        </span>
      </button>
      <Modal opened={opened} onClose={close} size={'95%'} centered>
        {isBlocked ? (
          <div className="error-message">
            <p>
              There seems to be an issue displaying the PDF. Please check if you have any browser extensions like Internet Download Manager (IDM) that
              might be blocking the PDF display.
            </p>
          </div>
        ) : !workerUrl ? (
          <Loading />
        ) : (
          <Worker workerUrl={workerUrl} className="w-full">
            <Viewer
              fileUrl={pdfUrl}
              // defaultScale={SpecialZoomLevel.PageFit}
              plugins={[defaultLayoutPluginInstance]}
            />
          </Worker>
        )}
      </Modal>
    </>
  );
};

export default ViewPDF;
