import { Label, XAxis } from "recharts";

// chartOptions.js
const getChartOptions = () => ({
  chart: {
    height: 350,
    type: "heatmap",
  },
  plotOptions: {
    heatmap: {
      shadeIntensity: 0,
      radius: 0,
      useFillColorAsStroke: false,
      colorScale: {
        ranges: [
          { from: 0, to: 25, name: "Low", color: "#86FFB0" },
          { from: 25, to: 50, name: "Medium", color: "#FEF089" },
          { from: 50, to: 75, name: "High", color: "#FFC787" },
          { from: 75, to: 100, name: "Extreme", color: "#FBC8C8" },
        ],
      },
    },
  },
  tooltip: { enabled: true },

  Xaxis: {
    position: "top",
    labels: {
      rotateAlways: true,
      rotate: -45,
    },
  },

  dataLabels: {
    enabled: false,
  },
  stroke: {
    width: 4,
  },
  title: {
    text: "",
  },
  legend: {
    position: "bottom",
  },
});

export default getChartOptions;
