import React from "react";
import { useTranslation } from "react-i18next";
import ScopeTabs from "./Partials/ScopeTabs";
import { CgSandClock } from "react-icons/cg";

const ClimateProgramView = () => {
  const { t } = useTranslation();

  return (
    <div className="relative">
      <div className="absolute inset-0 bg-[#f7f4f4f7] bg-opacity-100 flex items-start pt-20 justify-center z-10 rounded-lg">
        <h2 className="flex items-center justify-center gap-3 py-12 text-2xl text-center animate-pulse">
          <CgSandClock /> {t("Coming Soon")}
        </h2>
      </div>
      <div>
        <h1 className="my-3 font-bold">
          {t("climateProgram.yourDecarbonizationJourney")}
        </h1>
        <p className="font-semibold">{t("climateProgram.checkEmissions")}</p>
      </div>

      <ScopeTabs />
    </div>
  );
};

export default ClimateProgramView;
