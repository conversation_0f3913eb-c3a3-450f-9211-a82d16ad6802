import DueDiligenceView from "@/Pages/ESGCompassSuite/ESGDD/DueDiligenceView";
import DueReport from "@/Pages/ESGCompassSuite/ESGDD/DueReport";
import ReportPage from "@/Pages/ESGCompassSuite/GreenSightAI/ReportPage/ReportPage";
import Hrdd from "@/Pages/ESGCompassSuite/HRDD/Hrdd";
import HrddReport from "@/Pages/ESGCompassSuite/HRDD/Partials/HrddReport";
import Issb from "@/Pages/ESGCompassSuite/ISSB/Issb";
import IssbReport from "@/Pages/ESGCompassSuite/ISSB/Partials/IssbReport";
import IssbReportingSuite from "@/Pages/ESGCompassSuite/ISSB/IssbReporting/IssbReportingSuite";
import IssbReporting from "@/Pages/ESGCompassSuite/ISSB/IssbReporting/IssbReporting";
import TNFDReporting from "@/Pages/ESGCompassSuite/TNFDReporting/TNFDReporting";
import PreviousReport from "@/Issb/previousReport";
import ESGReporting from "@/Pages/ESGCompassSuite/ESGReporting";
import GRIReporting from "@/Pages/ESGCompassSuite/GRI/GRIReporting";
import SdgAlignmentView from "@/Pages/ESGCompassSuite/SDG/Partials/SdgAlignment/SdgAlignmentView";
import SdgImpactView from "@/Pages/ESGCompassSuite/SDG/Partials/SdgImpact/SdgImpactView";
import GetStart from "@/Pages/GetStart/GetStart";
import OTPVerification from "@/Pages/OTP/OTPVerification";
import GreenHubAcademy from "@/Pages/GreenHubPages/Academy";
import GreenHubStart from "@/Pages/GreenHubPages/GreenHubStart";
import PeersCommunity from "@/Pages/GreenHubPages/PeersCommunity";
import Resources from "@/Pages/GreenHubPages/Resources.jsx";
import TopicDetails from "@/Pages/GreenHubPages/TopicDetails";
import CollectView from "@/Pages/S2ZeroNetPages/Collect/CollectView";
import S2ComingSoonView from "@/Pages/S2ZeroNetPages/ComingSoon/S2ComingSoonView";
import S3ComingSoonView from "@/Pages/S2ZeroNetPages/ComingSoon/S3ComingSoonView";
import DecarbonizeView from "@/Pages/S2ZeroNetPages/Decarbonize/DecarbonizeView";
import FinancedEmissionsView from "@/Pages/S2ZeroNetPages/FinancedEmissions/FinancedEmissionsView";
import GeneralView from "@/Pages/S2ZeroNetPages/General/GeneralView";
import MeasureView from "@/Pages/S2ZeroNetPages/Measure/MeasureView";
import ReportView from "@/Pages/S2ZeroNetPages/NetZeroReport/ReportView";
import ReviewView from "@/Pages/S2ZeroNetPages/Review/ReviewView";
import SupplierView from "@/Pages/S2ZeroNetPages/Supplier/SupplierView";
import CompilanceView from "@/Pages/S3GreenShield/Compilance/CompilanceView";
import GeneralViewGreenShield from "@/Pages/S3GreenShield/General/GeneralView";
import FinancialServicesStart from "@/Pages/S3GreenShield/financialServicesStart/FinancialServicesStart";
import DashboardView from "@/Pages/S3GreenShield/financialServicesStart/Partials/ESGIncidentManagement/DashboardView";
import AnalyticsReportingView from "@/Pages/S3GreenShield/financialServicesStart/Partials/ESGRiskManagementSystem/AnalyticsAndReporting/AnalyticsReportingView";
import ESGRiskManagementSystem from "@/Pages/S3GreenShield/financialServicesStart/Partials/ESGRiskManagementSystem/ESGRiskManagementSystem";
import ESGRiskUniverseAndGovernanceView from "@/Pages/S3GreenShield/financialServicesStart/Partials/ESGRiskManagementSystem/ESGRiskUniverseAndGovernance/ESGRiskUniverseAndGovernanceView";
import MitigationStrategiesView from "@/Pages/S3GreenShield/financialServicesStart/Partials/ESGRiskManagementSystem/MitigationStrategies/MitigationStrategiesView";
import RiskIdentificationAndAssessment from "@/Pages/S3GreenShield/financialServicesStart/Partials/ESGRiskManagementSystem/RiskIdentificationAndAssessment/RiskIdentificationAndAssessment";
import FinancialAntiGreenWashingView from "@/Pages/S3GreenShield/financialServicesStart/Partials/FinancialAntiGreenWashing/FinancialAntiGreenWashingView";
import GreenWashingRiskAssessmentView from "@/Pages/S3GreenShield/financialServicesStart/Partials/GreenWashingRiskAssessment/GreenWashingRiskAssessmentView";
import NonFinancialServicesStart from "@/Pages/S3GreenShield/nonFinancialServicesStart/NonFinancialServicesStart";
import SupportView from "@/Pages/Support/SupportView";
import Settings from "../Pages/Settings/Settings";
// import AddNewIncident from "@/Pages/S3GreenShield/financialServicesStart/Partials/ESGIncidentManagement/Dashboard1/Dashboard/Partials/AddNewIncident/AddNewIncident";
import AddNewIncident from "@/Pages/S3GreenShield/financialServicesStart/Partials/ESGIncidentManagement/Dashboard/Partials/AddNewIncident/AddNewIncident";
import NonFiDashboard from "@/Pages/S3GreenShield/nonFinancialServicesStart/Partials/ESGIncidentManagement/Dashboard/NonFiDashboard";
import NonFiAddNewIncident from "@/Pages/S3GreenShield/nonFinancialServicesStart/Partials/ESGIncidentManagement/Dashboard/Partials/AddNewIncident/NonFiAddNewIncident";
import NonFiMyIncidents from "@/Pages/S3GreenShield/nonFinancialServicesStart/Partials/ESGIncidentManagement/MyIncidents/NonFiMyIncidents";
import NonFiSupIncidents from "@/Pages/S3GreenShield/nonFinancialServicesStart/Partials/ESGIncidentManagement/MyIncidents/Partials/SupIncidents/NonFiSupIncidents";
import NonFiReporting from "@/Pages/S3GreenShield/nonFinancialServicesStart/Partials/ESGIncidentManagement/Reporting/NonFiReporting";
import CompanyUserManage from "@/Pages/Settings/Partials/Profile/Partials/CompanyProfile/Partials/CompanyUserManage/CompanyUserManage";
import ConfigurationsAssets from "@/Pages/Settings/Partials/Profile/Partials/CompanyProfile/Partials/ConfigurationsAssets/ConfigurationsAssets";
import ConfigurationsCustomFactor from "@/Pages/Settings/Partials/Profile/Partials/CompanyProfile/Partials/ConfigurationsCustomFactor/ConfigurationsCustomFactor";
import DepartmentsandProjects from "@/Pages/Settings/Partials/Profile/Partials/CompanyProfile/Partials/DepartmentsandProjects/DepartmentsandProjects";
// import ManageAdminCompanies from "@/Pages/SuperAdminPages/ManageAdminCompanies/ManageAdminCompanies";
import GreenHubDashboard from "@/Pages/GreenHubPages/GreenHubDashboard";
import CompanyLogs from "@/Pages/Settings/Partials/Profile/Partials/CompanyProfile/Partials/CompanyLogs/CompanyLogs";
import SuperAdminPagesView from "@/Pages/SuperAdminPages/SuperAdminPagesView";

import CoursePage from "@/Pages/GreenHubPages/Partials/CoursePage";
import ResourcesDashboard from "@/Pages/GreenHubPages/ResourcesDashBoard";
import AcademyDashboard from "@/Pages/GreenHubPages/academydashboard";
import CommunityDashboard from "@/Pages/GreenHubPages/communityDashBoard";
import CouresDetails from "@/Pages/GreenHubPages/courseDetails";
import RiskFormsPage from "@/Pages/S3GreenShield/financialServicesStart/Partials/ESGRiskManagementSystem/RiskIdentificationAndAssessment/components/RiskFormsPage";

import AcademyContextProvider from "@/Contexts/AcademyContext";
import CourseContextProvider from "@/Contexts/CourseContext";
import ConnectorsTab from "@/Pages/S2ZeroNetPages/Collect/Partials/Connectors/ConnectorsTab";
import DataValidation from "@/Pages/S2ZeroNetPages/DataValidation/DataValidationView";
import Audit from "@/Pages/S3GreenShield/financialServicesStart/Partials/ESGRiskManagementSystem/Audit/Audit";
import AssetsDynamicTable from "@/Pages/S2ZeroNetPages/Measure/Partials/Emissions Breakdown/Partials/AssetsDynamicTable";
import EditRisk from "@/Pages/S3GreenShield/RiskAssessment/EditRisk/EditRisk";
import Certificate from "@/Components/Course/Certificate";
import DocvaultView from "@/Pages/S2ZeroNetPages/Docvalult/DocvaultView";
import RegulatoryReadinessView from "@/Pages/S3GreenShield/financialServicesStart/Partials/RegulatoryReadiness/RegulatoryReadinessView";
import FinanceGuard from "@/Pages/Insights&Reporting/FinanceGuard/FinanceGuard";
import ChainSight from "@/Pages/GRCSystem/ChainSight/ChainSight";
import Review from "@/Pages/GRCSystem/ChainSight/Partials/Review";
import DueDiligence from "@/Pages/GRCSystem/DueDiligence/DueDiligence";
import GreenSightView from "@/Pages/Insights&Reporting/GreenSight/GreenSightView";
import GreenSight from "@/Pages/Insights&Reporting/GreenSight/partials/GreenSight";
import GreenSightFinance from "@/Pages/Insights&Reporting/GreenSight/partials/GreenSightFinance";
import CarbonDashboard from "@/Pages/CarbonSystem/EmissionOverView/components/CarbonDashboard";
import GrcDashboard from "@/Pages/GRCSystem/GrcDashboard";
import InsightsReportingDashboard from "@/Pages/Insights&Reporting/InsightsReportingDashboard";
import DataFoundationDashboard from "@/Pages/DataFoundationSystem/DataFoundationDashboard";
import DocumentCenterDashbaord from "@/Pages/DocumentCenterSystem/DocumentCenterDashbaord";
import CreateFullSurvey from "@/Pages/S3GreenShield/financialServicesStart/Partials/ESGIncidentManagement/FeedbackCollection/CreateFullSurvey";
import SurveyDetails from "@/Pages/S3GreenShield/financialServicesStart/Partials/ESGIncidentManagement/FeedbackCollection/SurveyDetails";
import ESGRiskManagementSystemMenu from "@/Pages/S3GreenShield/financialServicesStart/Partials/ESGRiskManagementSystem/Components/ESGRiskManagementSystemMenu";

// import Academy from "@/Pages/GreenHubPages/Academy";
// import Resources from './../Pages/GreenHubPages/Resources.jsx';

const useRoutes = () => {
  /**
   * In this file all routes and its pages should be defined to be used around the project
   * Each object should name like <the module name>Map "GreenHupMap"
   * Then each object should hav a separated route render file like "GreenHubRoutes"
   *  - this for any modification in role permission in the future
   */

  const routesSetup = {
    OTPVerification: {
      path: "/OTPVerification",
      element: <OTPVerification />,
    },

    // General Routes:
    creatserve: {
      path: "/creatserve",
      element: <CreateFullSurvey />,
    },

    surveyDetails: {
      path: "/surveyDetails/:id",
      element: <SurveyDetails />,
    },
    Settings: {
      path: "/Settings",
      element: <Settings />,
    },

    CompanyUserManage: {
      path: "/Settings/CompanyProfile/Configurations/CompanyUserManage",
      element: <CompanyUserManage />,
    },
    CompanyLogs: {
      path: "/Settings/CompanyProfile/Configurations/CompanyLogs",
      element: <CompanyLogs />,
    },
    DepartmentsandProjects: {
      path: "/Settings/CompanyProfile/Configurations/DepartmentsandProjects",
      element: <DepartmentsandProjects />,
    },

    CompanyProfileConfigurationsAssets: {
      path: "/Settings/CompanyProfile/Configurations/Assets",
      element: <ConfigurationsAssets />,
    },
    CompanyProfileConfigurationsCustomFactor: {
      path: "/Settings/CompanyProfile/Configurations/CustomFactor",
      element: <ConfigurationsCustomFactor />,
    },
    ReportS1: {
      path: "/report-page",
      element: <ReportPage />,
    },

    // ESG Puls Route:
    getStart: {
      path: "/get-started",
      element: <GetStart />,
    },

    // ===================== admin pages

    ManageCompanies: {
      path: "/admin-pages/manage-company",
      element: <SuperAdminPagesView />,
    },

    support: {
      path: "/support",
      element: <SupportView />,
    },

    // Regulatory Readniess
    regulatoryReadnss: {
      path: "green-shield/financial/regulatory-readiness",
      element: <RegulatoryReadinessView />,
    },

    //ISSB Route
    issbStart: {
      path: "/issb",
      element: <Issb />,
    },

    tnfdReporting: {
      path: "/tnfd-reporting",
      element: <TNFDReporting />,
    },
    griReporting: {
      path: "/gri-reporting",
      element: <GRIReporting />,
    },
    issbReport: {
      path: "/issb-report",
      element: <IssbReport />,
    },
    issbReportingAssment: {
      path: "/issb-reporting-assessment",
      element: <IssbReporting />,
    },

    // SDG Routes
    sdg: {
      path: "/sdg-alignment",
      element: <SdgAlignmentView />,
    },
    impactMeasure: {
      path: "/sdg-impact-measure",
      element: <SdgImpactView />,
    },

    // Hrdd routes
    hdrrReport: {
      path: "/hrdd-report",
      element: <HrddReport />,
    },

    // due diligence routes
    dueDiligencePage: {
      path: "/due-diligence",
      element: <DueDiligenceView />,
    },
    dueDiligenceReport: {
      path: "/dueDiligence-report",
      element: <DueReport />,
    },

    // Carbon system
    CarbonDashboard: {
      path: "/Carbon/dashboard",
      element: <CarbonDashboard />,
    },

    // LevelUP GRC Routes:
    financeGard: {
      path: "/Insights-reporing/financeguard",
      element: <FinanceGuard />,
    },

    GrcDashbaord: {
      path: "/Grc/dashboard",
      element: <GrcDashboard />,
    },
    chainSightView: {
      path: "/Grc/chain-sight",
      element: <ChainSight />,
    },
    chainSightReview: {
      path: "/Grc/chain-sight/review",
      element: <Review />,
    },

    dueDiligence: {
      path: "/Grc/due-diligence",
      element: <DueDiligence />,
    },

    dueDiligenceView: {
      path: "/Grc/due-diligence/esg-due-diligence",
      element: <DueDiligenceView />,
    },

    hdrrPage: {
      path: "/Grc/due-diligence/human-rights-due-diligence",
      element: <Hrdd />,
    },

    regulatoryReadiness: {
      path: "/Grc/regulatory-readiness",
      element: <RegulatoryReadinessView />,
    },

    issb: {
      path: "/Grc/regulatory-readiness/issb",
      element: <Issb />,
    },

    GrcAntiGreenWashing: {
      path: "/Grc/anti-green-washing",
      element: <CompilanceView />,
    },

    // Insights & Reporting Routes:
    InsightsAndReportingDashboard: {
      path: "/Insights-reporing/dashboard",
      element: <InsightsReportingDashboard />,
    },

    greenSightAi: {
      path: "/Insights-reporing/green-sight-ai",
      element: <GreenSightView />,
    },

    issbReportingSuite: {
      path: "/Insights-reporing/issb-reporting-suite",
      element: <IssbReportingSuite />,
    },

    esgReporting: {
      path: "/Insights-reporing/issb-reporting-suite/esg-reporting",
      element: <ESGReporting />,
    },

    previousReport: {
      path: "/Insights-reporing/issb-reporting-suite/previous-report",
      element: <PreviousReport />,
    },

    greenSight: {
      path: "/Insights-reporing/green-sight-ai/green-sight",
      element: <GreenSight />,
    },

    greenSightFinance: {
      path: "/Insights-reporing/greensight/financial",
      element: <GreenSightFinance />,
    },

    sdgAndImpact: {
      path: "/Insights-reporing/sdg-impact",
      element: <SdgAlignmentView />,
    },

    stackHolderEngagment: {
      path: "/Insights-reporing/stakholder-engagment",
      element: <DashboardView />,
    },

    DataFoundationDashboardRoute: {
      path: "/Data-foundation/dashboard",
      element: <DataFoundationDashboard />,
    },

    dataCollection: {
      path: "/Data-foundation/data-collection",
      element: <ConnectorsTab />,
    },

    // Document Center
    DocumentCenterDash: {
      path: "/Document-Center/dashboard",
      element: <DocumentCenterDashbaord />,
    },
    docVault: {
      path: "/Document-Center/docvault",
      element: <DocvaultView />,
    },

    // Net Zero Routes:
    netZerosEmissionOverview: {
      path: "/net-zero/general",
      element: <GeneralView />,
    },
    netZerosCollect: {
      path: "/net-zero/collect",
      element: <CollectView />,
    },
    netZerosManualInput: {
      path: "/net-zero/collect/manual-input",
      element: <CollectView target="manual-input" />,
    },
    netZerosBatchInput: {
      path: "/net-zero/collect/batch-input",
      element: <CollectView target="batch-input" />,
    },
    netZerosConnectors: {
      path: "/net-zero/collect/connectors",
      element: <CollectView target="connectors" />,
    },
    dataValidation: {
      path: "/net-zero/data-validation",
      element: <DataValidation />,
    },
    netZerosReview: {
      path: "/net-zero/review",
      element: <ReviewView />,
    },
    netZerosSuppliersInputs: {
      path: "/net-zero/suppliers/Add-Supplier",
      element: <SupplierView />,
    },
    netZerosMeasure: {
      path: "/net-zero/measure",
      element: <MeasureView target="emissions-overview" />,
    },
    netZerosReport: {
      path: "/net-zero/net-zeros-report",
      element: <ReportView />,
    },
    netZerosGreenHub: {
      path: "/net-zero/green-hub",
      element: <S2ComingSoonView />,
    },
    netZerosDecarbonize: {
      path: "/net-zero/decarbonize",
      element: <DecarbonizeView />,
    },
    netZerosFinancedEmissions: {
      path: "/net-zero/financed-emissions",
      element: <FinancedEmissionsView />,
    },

    /**{ S3 greenShield Routes }*/
    greenShield: {
      path: "/green-shield",
      element: <S3ComingSoonView />,
    },
    greenShieldGeneral: {
      path: "/green-shield/general",
      element: <GeneralViewGreenShield />,
    },
    /**{ 1- S3 greenShield FinancialServices Routes }*/
    greenShieldFinancialServicesStart: {
      path: "/green-shield/financial-service-start",
      element: <FinancialServicesStart />,
    },
    greenShieldFinancialAnti: {
      path: "/green-shield/financial/green-washing",
      element: <FinancialAntiGreenWashingView />,
    },

    ESGRiskAndImpactMain: {
      path: "/green-shield/financial/ESG-risk-management/main",
      element: <ESGRiskManagementSystemMenu />,
    },
    greenShieldFinancialESGRiskManagement: {
      path: "/green-shield/financial/ESG-risk-management/main/systems/:id",
      element: <ESGRiskManagementSystem />,
    },
    greenShieldFinancialESGRiskManagementESGRiskUniverseAndGovernanceView: {
      path: "/green-shield/financial/ESG-risk-management/ESGRiskUniverseAndGovernanceView",
      element: <ESGRiskUniverseAndGovernanceView />,
    },
    greenShieldFinancialESGRiskManagementRiskIdentificationAndAssessment: {
      path: "/green-shield/financial/ESG-risk-management/RiskIdentificationAndAssessment",
      element: <RiskIdentificationAndAssessment />,
    },

    greenShieldFinancialESGRiskManagementControlEffectiveness: {
      path: "/green-shield/financial/ESG-risk-management/MitigationStrategiesView",
      element: <MitigationStrategiesView />,
    },
    greenShieldFinancialESGRiskManagementAnalyticsReportingView: {
      path: "/green-shield/financial/ESG-risk-management/AnalyticsAndReporting/AnalyticsReportingView",
      element: <AnalyticsReportingView />,
    },
    greenShieldFinancialRiskAssessment: {
      path: "/green-shield/financial/green-washing-risk-assessment",
      element: <GreenWashingRiskAssessmentView />,
    },
    greenShieldAudit: {
      path: "/green-shield/financial/ESG-risk-management/Audit",
      element: <Audit />,
    },
    greenShieldFinancialESGIncidentManagementAddNewIncident: {
      path: "/green-shield/financial/ESG-incident-management/addNewIncident/:id",
      element: <AddNewIncident />,
    },

    /**{ 2- S3 greenShield NonFinancialServices Routes }*/
    greenShieldNonFinancialServicesStart: {
      path: "/green-shield/nonFinancial-services-start",
      element: <NonFinancialServicesStart />,
    },
    greenShieldNonFinancialAnti: {
      path: "/green-shield/nonfinancial/green-washing",
      element: <FinancialAntiGreenWashingView />,
      // element: <NonFSAntiGreenWashingView />,
    },
    greenShieldNonFinancialRiskAssessment: {
      path: "/green-shield/nonfinancial/green-washing-risk-assessment",
      element: <GreenWashingRiskAssessmentView />,
      // element: <NonFSGreenWashingView />,
    },
    greenShieldNonFinancialESGIncidentManagement: {
      path: "/green-shield/nonFinancial/ESG-incident-management",
      element: <NonFiDashboard />,
    },
    greenShieldNonFinancialESGIncidentManagementAddNewIncident: {
      path: "/green-shield/nonFinancial/ESG-incident-management/addNewIncident/:id",
      element: <NonFiAddNewIncident />,
    },
    greenShieldNonFinancialESGIncidentManagementReporting: {
      path: "/green-shield/nonFinancial/ESG-incident-management/Reporting",
      element: <NonFiReporting />,
    },
    greenShieldNonFinancialESGIncidentManagementMyIncidents: {
      path: "/green-shield/nonFinancial/ESG-incident-management/MyIncidents",
      element: <NonFiMyIncidents />,
    },
    greenShieldNonFinancialESGIncidentManagementSupIncident: {
      path: "/green-shield/nonFinancial/ESG-incident-management/SupIncidents/:id",
      element: <NonFiSupIncidents />,
    },
    /**{ 3- S3 greenShield GreenHub Routes }*/
    greenShieldGreenHub: {
      path: "/green-shield/green-hub",
      element: <S3ComingSoonView />,
    },
    measure: {
      path: "/net-zero/measure/:category/:supCategory/:id",
      element: <AssetsDynamicTable />,
    },
  };

  const GreenHubMap = {

    AddRisk: {
      path: "/green-shield/financial/ESG-risk-management/main/systems/add-risk",
      element: <RiskFormsPage />,
    },
    EditRisk: {
      path: "/green-shield/financial/ESG-risk-management/main/systems/iro-assessment/:id",
      element: <EditRisk />,
    },

    // ==============  ================== \\
    // ============== admin greenhub ================== \\
    GreenHubDashboard: {
      path: "/admin-pages/green-hub/dashboard/dash",
      element: <GreenHubDashboard />,
    },
    GreenHubResourcesDashboard: {
      path: "/admin-pages/green-hub/dashboard/dash/resourses",
      element: <ResourcesDashboard />,
    },
    GreenHubAcademyDashboard: {
      path: "/admin-pages/green-hub/dashboard/dash/academy",
      element: <AcademyDashboard />,
    },

    courseDetails: {
      path: "/admin-pages/green-hub/dashboard/dash/academy/:courseId",
      element: <CouresDetails />,
    },

    GreenHubCommunityDashboard: {
      path: "/admin-pages/green-hub/dashboard/dash/community",
      element: <CommunityDashboard />,
    },

    // ==============  ================== \\
    // ============== User greenhub ================== \\
    GreenHub: {
      path: "/green-shield/main-page",
      element: <GreenHubStart />,
    },
    GreenHubPeersCommunity: {
      path: "/green-shield/peers-community",
      element: <PeersCommunity />,
    },

    GreenHubResources: {
      path: "/green-shield/resources",
      element: <Resources />,
    },
    GreenHubResourceDetails: {
      path: "/green-shield/resources/:id",
      element: <TopicDetails />,
    },
    GreenHubAcademy: {
      path: "/green-shield/academy",
      element: (
        <AcademyContextProvider>
          <GreenHubAcademy />
        </AcademyContextProvider>
      ),
    },
    GreenHubAcademyCourse: {
      path: "/green-shield/academy/mycourses/:courseId",
      element: (
        <CourseContextProvider>
          <CoursePage />
        </CourseContextProvider>
      ),
    },
    GreenHubAcademyCertificate: {
      path: "/green-shield/academy/mycourses/:courseId/certificate",
      element: (
        <CourseContextProvider>
          <Certificate />
        </CourseContextProvider>
      ),
    },
  };

  return {
    routesName: Object.keys(routesSetup),
    routesSetup,
    ...routesSetup,

    // GreenHubMap
    GreenHubMap,
  };
};

export default useRoutes;
