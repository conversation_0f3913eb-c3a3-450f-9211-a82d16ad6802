import { useTranslation } from 'react-i18next';

const TagsFilter = ({ setFilters, filters }) => {
  const { t } = useTranslation();
  const toggleFilter = (tag) => {
    setFilters((prev) =>
      prev?.tags?.includes(tag) ? { ...prev, tags: prev?.tags?.filter((t) => t !== tag) } : { ...prev, tags: [...prev.tags, tag] }
    );
  };

  return (
    <div className="flex flex-wrap gap-2 text-xs">
      {[
        { tag: 'Sustainably', color: '#FDD64780' },
        { tag: 'Governance', color: '#AB020280' },
        { tag: 'Environment', color: '#29C82F80' },
        { tag: 'Data', color: '#00C0A980' },
        { tag: 'Society', color: '#F3804E80' },
        { tag: 'General', color: '#298BED80' },
      ].map(({ tag, color }) => (
        <button
          key={tag}
          onClick={() => toggleFilter(tag)}
          className={`cursor-pointer mt-4 px-2 py-1 rounded group relative overflow-hidden  ${filters?.tags?.includes(tag) ? `` : 'bg-gray-500/10'}`}
        >
          <span
            className={`absolute inset-0 ${
              filters?.tags?.includes(tag) ? `opacity-100` : 'opacity-0 group-hover:opacity-100'
            }  transition duration-300`}
            style={{ backgroundColor: color }}
          ></span>
          <span className={`relative duration-300 `}> {t(tag)}</span>
        </button>
      ))}
    </div>
  );
};

export default TagsFilter;
