export const validateField = (field, value) => {
    switch (field) {
      case "assetName":
        if (!value?.trim()) return "Asset name is required";
        if (value.length < 3) return "Asset name must be at least 3 characters";
        return "";
  
      case "location":
        if (!value?.trim()) return "Location is required";
        if (value.length < 2) return "Location must be at least 2 characters";
        return "";
  
      // case "reference":
      //   if (!value?.trim()) return "reference is required";
      //   if (value.length < 2) return "reference must be at least 2 characters";
      //   return "";
  
      default:
        return "";
    }
  };