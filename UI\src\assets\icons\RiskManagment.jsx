export const IROIdentification = ({ className = "" }) => {
  return (
    <svg
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M21.5841 3.08594H10.4107C5.5574 3.08594 2.66406 5.97927 2.66406 10.8326V22.0059C2.66406 26.8593 5.5574 29.7526 10.4107 29.7526H21.5841C26.4374 29.7526 29.3307 26.8593 29.3307 22.0059V10.8326C29.3307 5.97927 26.4374 3.08594 21.5841 3.08594ZM13.2907 20.2859L10.2907 23.2859C10.0907 23.4859 9.8374 23.5793 9.58406 23.5793C9.33073 23.5793 9.06406 23.4859 8.8774 23.2859L7.8774 22.2859C7.4774 21.8993 7.4774 21.2593 7.8774 20.8726C8.26406 20.4859 8.89073 20.4859 9.29073 20.8726L9.58406 21.1659L11.8774 18.8726C12.2641 18.4859 12.8907 18.4859 13.2907 18.8726C13.6774 19.2593 13.6774 19.8993 13.2907 20.2859ZM13.2907 10.9526L10.2907 13.9526C10.0907 14.1526 9.8374 14.2459 9.58406 14.2459C9.33073 14.2459 9.06406 14.1526 8.8774 13.9526L7.8774 12.9526C7.4774 12.5659 7.4774 11.9259 7.8774 11.5393C8.26406 11.1526 8.89073 11.1526 9.29073 11.5393L9.58406 11.8326L11.8774 9.53927C12.2641 9.1526 12.8907 9.1526 13.2907 9.53927C13.6774 9.92594 13.6774 10.5659 13.2907 10.9526ZM23.4107 22.5793H16.4107C15.8641 22.5793 15.4107 22.1259 15.4107 21.5793C15.4107 21.0326 15.8641 20.5793 16.4107 20.5793H23.4107C23.9707 20.5793 24.4107 21.0326 24.4107 21.5793C24.4107 22.1259 23.9707 22.5793 23.4107 22.5793ZM23.4107 13.2459H16.4107C15.8641 13.2459 15.4107 12.7926 15.4107 12.2459C15.4107 11.6993 15.8641 11.2459 16.4107 11.2459H23.4107C23.9707 11.2459 24.4107 11.6993 24.4107 12.2459C24.4107 12.7926 23.9707 13.2459 23.4107 13.2459Z"
        fill="#6E6E6E"
      />
    </svg>
  );
};

export const MitigationStrategies = ({ className = "" }) => {
  return (
    <svg
      className={className}
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M25.9295 9.67344V13.0201C25.9295 13.9668 24.9695 14.5801 24.0761 14.2468C22.9561 13.8334 21.7161 13.6734 20.4095 13.8068C17.2361 14.1534 13.9828 17.2068 13.4495 20.3668C13.0095 22.9934 13.8495 25.4468 15.4628 27.1801C16.1961 27.9801 15.7028 29.2734 14.6228 29.3934C13.7028 29.5001 12.7961 29.4734 12.2895 29.1001L4.95615 23.6201C4.08948 22.9668 3.38281 21.5534 3.38281 20.4601V9.67344C3.38281 8.16677 4.52948 6.51344 5.92948 5.9801L13.2628 3.23344C14.0228 2.95344 15.2761 2.95344 16.0361 3.23344L23.3695 5.9801C24.7828 6.51344 25.9295 8.16677 25.9295 9.67344Z"
        fill="#6E6E6E"
      />
      <path
        d="M21.3359 15.7656C18.0293 15.7656 15.3359 18.459 15.3359 21.7656C15.3359 25.0723 18.0293 27.7656 21.3359 27.7656C24.6426 27.7656 27.3359 25.0723 27.3359 21.7656C27.3359 18.4456 24.6426 15.7656 21.3359 15.7656Z"
        fill="#6E6E6E"
      />
      <path
        d="M27.9974 29.7538C27.6374 29.7538 27.3041 29.6071 27.0507 29.3671C26.9974 29.3004 26.9307 29.2338 26.8907 29.1538C26.8374 29.0871 26.7974 29.0071 26.7707 28.9271C26.7307 28.8471 26.7041 28.7671 26.6907 28.6871C26.6774 28.5938 26.6641 28.5138 26.6641 28.4204C26.6641 28.2471 26.7041 28.0738 26.7707 27.9138C26.8374 27.7404 26.9307 27.6071 27.0507 27.4738C27.3574 27.1671 27.8241 27.0204 28.2507 27.1138C28.3441 27.1271 28.4241 27.1538 28.5041 27.1938C28.5841 27.2204 28.6641 27.2604 28.7307 27.3138C28.8107 27.3538 28.8774 27.4204 28.9441 27.4738C29.0641 27.6071 29.1574 27.7404 29.2241 27.9138C29.2907 28.0738 29.3307 28.2471 29.3307 28.4204C29.3307 28.7671 29.1841 29.1138 28.9441 29.3671C28.8774 29.4204 28.8107 29.4738 28.7307 29.5271C28.6641 29.5804 28.5841 29.6204 28.5041 29.6471C28.4241 29.6871 28.3441 29.7138 28.2507 29.7271C28.1707 29.7404 28.0774 29.7538 27.9974 29.7538Z"
        fill="#6E6E6E"
      />
    </svg>
  );
};


export const AnalyticsReporting = ({ className = "" }) => {
  return (
<svg className={className} viewBox="0 0 32 33" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M21.5841 3.08594H10.4107C5.5574 3.08594 2.66406 5.97927 2.66406 10.8326V21.9926C2.66406 26.8593 5.5574 29.7526 10.4107 29.7526H21.5707C26.4241 29.7526 29.3174 26.8593 29.3174 22.0059V10.8326C29.3307 5.97927 26.4374 3.08594 21.5841 3.08594ZM10.1707 24.6193C10.1707 25.1659 9.7174 25.6193 9.17073 25.6193C8.62406 25.6193 8.17073 25.1659 8.17073 24.6193V21.8593C8.17073 21.3126 8.62406 20.8593 9.17073 20.8593C9.7174 20.8593 10.1707 21.3126 10.1707 21.8593V24.6193ZM16.9974 24.6193C16.9974 25.1659 16.5441 25.6193 15.9974 25.6193C15.4507 25.6193 14.9974 25.1659 14.9974 24.6193V19.0859C14.9974 18.5393 15.4507 18.0859 15.9974 18.0859C16.5441 18.0859 16.9974 18.5393 16.9974 19.0859V24.6193ZM23.8241 24.6193C23.8241 25.1659 23.3707 25.6193 22.8241 25.6193C22.2774 25.6193 21.8241 25.1659 21.8241 24.6193V16.3259C21.8241 15.7793 22.2774 15.3259 22.8241 15.3259C23.3707 15.3259 23.8241 15.7793 23.8241 16.3259V24.6193ZM23.8241 12.1126C23.8241 12.6593 23.3707 13.1126 22.8241 13.1126C22.2774 13.1126 21.8241 12.6593 21.8241 12.1126V10.8193C18.4241 14.3126 14.1707 16.7793 9.41073 17.9659C9.33073 17.9926 9.25073 17.9926 9.17073 17.9926C8.7174 17.9926 8.3174 17.6859 8.1974 17.2326C8.06406 16.6993 8.38406 16.1526 8.93073 16.0193C13.4241 14.8993 17.4241 12.5393 20.5974 9.20594H18.9307C18.3841 9.20594 17.9307 8.7526 17.9307 8.20594C17.9307 7.65927 18.3841 7.20594 18.9307 7.20594H22.8374C22.8907 7.20594 22.9307 7.2326 22.9841 7.2326C23.0507 7.24594 23.1174 7.24594 23.1841 7.2726C23.2507 7.29927 23.3041 7.33927 23.3707 7.37927C23.4107 7.40594 23.4507 7.41927 23.4907 7.44594C23.5041 7.45927 23.5041 7.4726 23.5174 7.4726C23.5707 7.52594 23.6107 7.57927 23.6507 7.6326C23.6907 7.68594 23.7307 7.72594 23.7441 7.77927C23.7707 7.8326 23.7707 7.88594 23.7841 7.9526C23.7974 8.01927 23.8241 8.08594 23.8241 8.16594C23.8241 8.17927 23.8374 8.1926 23.8374 8.20594V12.1126H23.8241Z" fill="#6E6E6E"/>
    </svg>
  );
};


export const AntiGreenwashing = ({ className = "" }) => {
  return (
    <svg className={className} viewBox="0 0 32 33" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M24.721 5.9176L17.3877 3.17094C16.6277 2.89094 15.3877 2.89094 14.6277 3.17094L7.29437 5.9176C5.88104 6.45094 4.73438 8.10427 4.73438 9.61094V20.4109C4.73438 21.4909 5.44104 22.9176 6.30771 23.5576L13.641 29.0376C14.9344 30.0109 17.0544 30.0109 18.3477 29.0376L25.681 23.5576C26.5477 22.9043 27.2544 21.4909 27.2544 20.4109V9.61094C27.2677 8.10427 26.121 6.45094 24.721 5.9176ZM20.641 13.3843L14.9077 19.1176C14.7077 19.3176 14.4544 19.4109 14.201 19.4109C13.9477 19.4109 13.6944 19.3176 13.4944 19.1176L11.361 16.9576C10.9744 16.5709 10.9744 15.9309 11.361 15.5443C11.7477 15.1576 12.3877 15.1576 12.7744 15.5443L14.2144 16.9843L19.241 11.9576C19.6277 11.5709 20.2677 11.5709 20.6544 11.9576C21.041 12.3443 21.041 12.9976 20.641 13.3843Z" fill="#6E6E6E"/>
    </svg>
  );
};

export const Auditicon = ({ className = "" }) => {
  return (
<svg className={className} viewBox="0 0 32 33" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M19.1304 3.08594H12.8637C11.4771 3.08594 10.3438 4.20594 10.3438 5.5926V6.84594C10.3438 8.2326 11.4638 9.3526 12.8504 9.3526H19.1304C20.5171 9.3526 21.6371 8.2326 21.6371 6.84594V5.5926C21.6504 4.20594 20.5171 3.08594 19.1304 3.08594Z" fill="#6E6E6E"/>
<path d="M22.9828 6.85047C22.9828 8.97047 21.2495 10.7038 19.1295 10.7038H12.8628C10.7428 10.7038 9.00948 8.97047 9.00948 6.85047C9.00948 6.1038 8.20948 5.63713 7.54281 5.9838C5.66281 6.9838 4.38281 8.97047 4.38281 11.2505V23.7971C4.38281 27.0771 7.06281 29.7571 10.3428 29.7571H21.6495C24.9295 29.7571 27.6095 27.0771 27.6095 23.7971V11.2505C27.6095 8.97047 26.3295 6.9838 24.4495 5.9838C23.7828 5.63713 22.9828 6.1038 22.9828 6.85047ZM20.4495 17.3971L15.1161 22.7305C14.9161 22.9305 14.6628 23.0238 14.4095 23.0238C14.1561 23.0238 13.9028 22.9305 13.7028 22.7305L11.7028 20.7305C11.3161 20.3438 11.3161 19.7038 11.7028 19.3171C12.0895 18.9305 12.7295 18.9305 13.1161 19.3171L14.4095 20.6105L19.0361 15.9838C19.4228 15.5971 20.0628 15.5971 20.4495 15.9838C20.8361 16.3705 20.8361 17.0105 20.4495 17.3971Z" fill="#6E6E6E"/>
</svg>

  );
};



export const AddIcon = ()=>(
  <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path opacity="0.4" d="M16.19 2.5H7.81C4.17 2.5 2 4.67 2 8.31V16.68C2 20.33 4.17 22.5 7.81 22.5H16.18C19.82 22.5 21.99 20.33 21.99 16.69V8.31C22 4.67 19.83 2.5 16.19 2.5Z" fill="#07838F"/>
<path d="M18 11.75H12.75V6.5C12.75 6.09 12.41 5.75 12 5.75C11.59 5.75 11.25 6.09 11.25 6.5V11.75H6C5.59 11.75 5.25 12.09 5.25 12.5C5.25 12.91 5.59 13.25 6 13.25H11.25V18.5C11.25 18.91 11.59 19.25 12 19.25C12.41 19.25 12.75 18.91 12.75 18.5V13.25H18C18.41 13.25 18.75 12.91 18.75 12.5C18.75 12.09 18.41 11.75 18 11.75Z" fill="#07838F"/>
</svg>

)
