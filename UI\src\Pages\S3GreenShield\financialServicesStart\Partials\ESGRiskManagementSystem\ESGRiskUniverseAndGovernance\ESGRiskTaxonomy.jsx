import { useState } from "react";
import { ESGRiskTaxonomyData } from "./data";
import { Modal, Button, TextInput } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import GuideModalButton from "@/Components/Guide/GuideModalButton";
import Guide from "./TaxonomyGuide.jsx";

export default function ESGRiskTaxonomy() {
    const [opened, { open, close }] = useDisclosure(false);

    // State to hold updated ESG Risk Taxonomy data
    const [taxonomyData, setTaxonomyData] = useState(ESGRiskTaxonomyData);

    // Handler to update taxonomy data when fields are edited
    const handleInputChange = (e, itemId, groupIndex, pointIndex) => {
        const updatedData = taxonomyData.map((item) => {
            if (item.id === itemId) {
                const updatedGroups = item.groups.map((group, gIndex) => {
                    if (gIndex === groupIndex) {
                        const updatedPoints = group.points.map(
                            (point, pIndex) => {
                                if (pIndex === pointIndex) {
                                    return e.target.value; // Update the specific point being edited
                                }
                                return point;
                            }
                        );
                        return { ...group, points: updatedPoints };
                    }
                    return group;
                });
                return { ...item, groups: updatedGroups };
            }
            return item;
        });
        setTaxonomyData(updatedData); // Update state with the new data
    };

    const handleSave = () => {
        close();
    };

    return (
        <div className="flex flex-col items-end">
            <div className="w-full justify-between flex items-center py-5 px-3">
                <GuideModalButton buttonText="Taxonomy Guide">
                    <Guide />
                </GuideModalButton>
                <Button onClick={open} className="bg-[#07838F] rounded-lg">
                    Configure Data
                </Button>
            </div>

            <Modal
                opened={opened}
                onClose={close}
                title={
                    <h2 className="text-[#07838F] font-bold">
                        Configure ESG Risk Taxonomy
                    </h2>
                }
            >
                <div className="flex flex-col justify-center">
                    {taxonomyData.map((item) => (
                        <div key={item.id}>
                            <h2 className="font-bold mb-2">{item.name}</h2>
                            {item.groups.map((group, groupIndex) => (
                                <div key={groupIndex} className="mb-4">
                                    <h3 className="font-semibold">
                                        {group.header}
                                    </h3>
                                    {group.points.map((point, pointIndex) => (
                                        <TextInput
                                            key={pointIndex}
                                            className="mb-2"
                                            value={point}
                                            onChange={(e) =>
                                                handleInputChange(
                                                    e,
                                                    item.id,
                                                    groupIndex,
                                                    pointIndex
                                                )
                                            }
                                            placeholder={`Enter ${group.header} point`}
                                        />
                                    ))}
                                </div>
                            ))}
                        </div>
                    ))}

                    <Button
                        onClick={handleSave}
                        className="mt-4 bg-[#07838F] rounded-lg"
                    >
                        Save
                    </Button>
                </div>
            </Modal>

            <table className="table-fixed rounded-lg border-2 border-[#9C9C9C] w-full">
                <thead className="bg-[#EAEAEA] border-2 border-[#9C9C9C] p-10">
                    <tr>
                        {taxonomyData.map((item) => (
                            <th
                                key={item.id}
                                className="border-2 border-[#9C9C9C] text-center py-2"
                            >
                                {item.name}
                            </th>
                        ))}
                    </tr>
                </thead>
                <tbody>
                    {taxonomyData[0].groups.map((_, groupIndex) => (
                        <tr
                            key={groupIndex}
                            className="border-2 border-[#9C9C9C]"
                        >
                            {taxonomyData.map((item) => (
                                <td
                                    key={item.id}
                                    className="border-2 border-[#9C9C9C] px-4 py-2"
                                >
                                    <div className="font-bold">
                                        {groupIndex + 1}.{" "}
                                        {item.groups[groupIndex]?.header}
                                    </div>
                                    <ul className="list-disc list-inside">
                                        {item.groups[groupIndex]?.points.map(
                                            (point, pointIndex) => (
                                                <li key={pointIndex}>
                                                    {point}
                                                </li>
                                            )
                                        )}
                                    </ul>
                                </td>
                            ))}
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
