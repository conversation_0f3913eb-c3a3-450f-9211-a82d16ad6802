import React, { useState } from "react";
import { Checkbox, ScrollArea, Table } from "@mantine/core";

import { useTranslation } from "react-i18next";
import { useEffect } from "react";
import ApiS2 from "@/Api/apiS2Config";
import Loading from "@/Components/Loading";
import DeleteTarget from "./DeleteTarget";
import UpdateTarget from "./UpdateTarget";
import { FaTrashAlt } from "react-icons/fa";

const TargetsTable = ({ refetch, assignees = [] }) => {
  const { t } = useTranslation();
  const [data, setdata] = useState([]);
  const [loading, setLoading] = useState(true);

  const [selectedRows, setSelectedRows] = useState([]);

  const g = async () => {
    try {
      const { data, loading, error } = await ApiS2.get(
        "/decarbonize/get_all_climate_targets"
      );
      setLoading(false);
      setdata(data);
    } catch (er) {
      setLoading(false);
    }
  };

  useEffect(() => {
    g();
  }, [loading, refetch]);

  if (loading) {
    return <Loading />;
  }
//console.log(assignees);

  const rows = data?.map((target) => {
    //console.log(target);
    
    const u = assignees
      .filter((user) => target?.userIds?.includes(parseInt(user?.value)))
      .map((us) => <h3 key={us.value}>{us?.label?.name}</h3>);

    return (
      <Table.Tr key={target.id}>
        <Table.Td className="w-[70px]">
          <div className="d flex justify-center items-center">
            <Checkbox
              aria-label="Select row"
              checked={selectedRows.includes(target.id)}
              onChange={(event) =>
                setSelectedRows(
                  event.currentTarget.checked
                    ? [...selectedRows, target.id]
                    : selectedRows.filter((position) => position !== target.id)
                )
              }
            />
          </div>
        </Table.Td>
        <Table.Td className="w-[200px]">{u}</Table.Td>
        <Table.Td>{target.name}</Table.Td>
        <Table.Td>company</Table.Td>
        <Table.Td>{target.reductionPct}</Table.Td>
        <Table.Td>{target.scope}</Table.Td>
        <Table.Td>{target.yearFrom}</Table.Td>
        <Table.Td className="flex justify-around gap-2">
          <UpdateTarget
            fetetchAgain={() => setLoading(!loading)}
            data={target}
            allAssingees={assignees}
          />
        </Table.Td>
      </Table.Tr>
    );
  });

  // const toggleRow = (rowId) => {
  //   setSelectedRows((prevSelection) => {
  //     const isSelected = prevSelection.includes(rowId);
  //     return isSelected
  //       ? prevSelection.filter((id) => id !== rowId)
  //       : [...prevSelection, rowId];
  //   });
  // };

  return (
    <>
      {selectedRows.length > 0 && (
        <DeleteTarget
          title={"Delete All"}
          keyName={'target_ids'}
          ids={selectedRows}
          refetchAgain={() => setLoading(!loading)}
          url={"decarbonize/delete_climate_target"}
        />
      )}
      <ScrollArea>
        <Table
          miw={900}
          highlightOnHover
          withTableBorder
          withColumnBorders
          classNames={{
            table:
              "rounded-xl overflow-hidden border border-gray-300 bg-white font-semibold",
            tbody: "rounded-xl",
            th: "p-4 bg-primary text-white text-left",
            td: "p-4 text-left",
            tr: "hover:bg-gray-50",
          }}
        >
          <Table.Thead>
            <Table.Tr>
              <Table.Th className="Data-Table-Delete-Targets">
                <FaTrashAlt className="w-4 h-4 mx-auto text-white" />
              </Table.Th>
              <Table.Th>{t("Assignees")}</Table.Th>
              <Table.Th>{t("table.targetName")}</Table.Th>
              <Table.Th>{t("table.targetType")}</Table.Th>
              <Table.Th>{t("table.reductionAmbition")}</Table.Th>
              <Table.Th>{t("table.targetScopes")}</Table.Th>
              <Table.Th>{t("table.baseTargetYears")}</Table.Th>
              <Table.Th className="Data-Table-Edit-Targets">{t("Edit")}</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>{rows}</Table.Tbody>
        </Table>
      </ScrollArea>
    </>
  );
};

export default TargetsTable;
