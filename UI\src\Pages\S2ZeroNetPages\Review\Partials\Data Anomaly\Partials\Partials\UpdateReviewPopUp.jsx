import ApiS2 from "@/Api/apiS2Config";
import { <PERSON>ton, Modal, NumberInput } from "@mantine/core";
import { useEffect, useState } from "react";

export default function UpdateReviewPopUp({
 opened,
 onClose,
 data,
 getTableData,
 setSelectedItems,
 selectedItemsId,
 closePopup,
 item = "quantityKeys",
}) {
 const [inputValues, setInputValues] = useState({});
 const [PopUpInputs, SetPopUpInputs] = useState({});
 const [SubmitLoading, setSubmitLoading] = useState(false);
 useEffect(() => {
  SetPopUpInputs(data.items[selectedItemsId]);
 }, [data]);

 //console.log(PopUpInputs);
 //console.log(inputValues);

 const handleInputChange = (key, value, section) => {
  setInputValues((prev) => ({
   ...prev,
   [section]: {
    ...prev[section],
    [key]: value,
   },
  }));
 };
 const handledFormSubmit = async (inputValues) => {
  // //console.log(value);

  setSubmitLoading(true);
  // const currentItem = data?.items[selectedItemsId];
  const newJsonValues = {
   [data?.items[selectedItemsId]?.id]: {
    fromDate: PopUpInputs.fromDate,
    toDate: PopUpInputs.toDate,
    activity: PopUpInputs.customFactor.activity || null,
    eFactor: PopUpInputs.customFactor.eFactors || null,
    uom: PopUpInputs.customFactor.uom || null,
    asset_type_id: PopUpInputs.customFactor.emissionSourceId,
    companyAssetId: PopUpInputs.companyAsset?.id,
    // customFactorId: dataByActivity[0]?.id,
    quantity: inputValues.quantityKeys || PopUpInputs.quantity,
    reportingYear: PopUpInputs.reportingYear || null,
    SourcePhysicalId: PopUpInputs.SourcePhysicalId || "",
    SourceServiceId: PopUpInputs.SourceServiceId || "",
    LocationSpecificityId: PopUpInputs.LocationSpecificityId || "",
   },
  };
  //console.log(newJsonValues);
  ApiS2.put("/batch_inputs/update_inputs", newJsonValues)
   .then(({ data }) => {
    setSubmitLoading(false);
    onClose();
    getTableData("manual");
    getTableData("batch");
    setSelectedItems(null);
    data?.inputType === "batch" && closePopup();
    showNotification({
     message: "Form Data Added Successfully",
     color: "green",
    });
   })
   .catch((error) => {
    setSubmitLoading(false);
    //console.log(error);
    error.response.data.message &&
     showNotification({
      message: error.response.data.message,
      color: "red",
     });
    error.response.data.error &&
     showNotification({
      message: error.response.data.error,
      color: "red",
     });
    error.response.data.validation_errors?.[0].message &&
     showNotification({
      message: error.response.data.validation_errors?.[0].message,
      color: "red",
     });
   });
 };

 return (
  <Modal
   opened={opened}
   onClose={onClose}
   centered
   size={"100%"}
   withCloseButton
  >
   {item === "quantityKeys" && (
    <div className="flex gap-5">
     {Object.entries(PopUpInputs?.quantity || {}).map(([key, value], i) => (
      <NumberInput
       key={key}
       label={key}
       placeholder="Enter Number..."
       value={inputValues?.[item]?.[key] || value}
       onChange={(e) => handleInputChange(key, e, item)}
       radius={10}
       rightSection={" "}
       size="md"
      />
     ))}
    </div>
   )}

   <Button
    disabled={!inputValues?.[item] || SubmitLoading}
    loading={SubmitLoading}
    className={
     !inputValues?.[item]
      ? "bg-gray-500 mt-4 cursor-not-allowed"
      : "mt-4 bg-primary hover:bg-primary"
    }
    onClick={() => {
     handledFormSubmit(inputValues);
    }}
    radius={10}
    size="md"
   >
    save
   </Button>
  </Modal>
 );
}
