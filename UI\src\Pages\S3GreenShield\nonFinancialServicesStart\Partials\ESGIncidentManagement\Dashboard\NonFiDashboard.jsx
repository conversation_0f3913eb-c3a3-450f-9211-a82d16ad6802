import S3Layout from "@/Layout/S3Layout";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import React, { useEffect, useState } from "react";
import { LuSigma } from "react-icons/lu";
import StatisticsCard from "./Partials/StatisticsCard";
import { MdNotificationsActive } from "react-icons/md";
import { RiFileCloseFill } from "react-icons/ri";
import { Button } from "@mantine/core";
import { GrAnalytics } from "react-icons/gr";
import { IoIosAddCircleOutline } from "react-icons/io";
import { FaCalendar } from "react-icons/fa";
import IncidentTable from "./Partials/IncidentTable";
import { useNavigate } from "react-router";
import { customAlphabet } from "nanoid";
import Loading from "@/Components/Loading";
import ApiS3 from "@/Api/apiS3";

export default function NonFiDashboard() {
  const { greenShieldNonFiESGIncidentManagement } = useSideBarRoute();
  const [statisticsData, setStatisticsData] = useState();
  const [TableData, setTableData] = useState();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const generateNumericID = customAlphabet("0123456789", 8);
  const statistics = async () => {
    setLoading(true);
    try {
      setLoading(true);

      const { data } = await ApiS3.get(
        "/incident-management/incidents/statistics"
      );
      if (data) {
        setLoading(false);
        setStatisticsData(data);
      }
    } catch (error) {
      setLoading(false);
      console.log(error);
    }
  };
  useEffect(() => {
    statistics();
  }, [TableData]);
  const id = generateNumericID();
  // console.log(statisticsData);
  return (
    <>
      <S3Layout menus={greenShieldNonFiESGIncidentManagement}>
        {/* Statistics Cards */}
        {loading ? (
          <Loading />
        ) : (
          <div className="w-full  grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10">
            <StatisticsCard
              icon={<LuSigma className="text-5xl font-bold" />}
              title={statisticsData?.totalIncidentsNo}
              paragraph={"Total Incidents"}
              style={`md:relative md:after:border 
         after:border-[#000000] after:border-opacity-20 after:absolute after:right-[-5%] after:top-1/3 after:h-1/4`}
            />
            <StatisticsCard
              icon={<MdNotificationsActive className="text-5xl font-bold" />}
              title={statisticsData?.activeIncidentsNo}
              paragraph={"Active incidents"}
              style={`lg:relative lg:after:border 
         after:border-[#000000] after:border-opacity-20 after:absolute after:right-[-5%] after:top-1/3 after:h-1/4`}
            />
            <StatisticsCard
              icon={<RiFileCloseFill className="text-5xl font-bold me-2" />}
              title={statisticsData?.closedIncidentsNo}
              paragraph={"Total Incidents closed"}
              style={`md:relative md:after:border 
       after:border-[#000000] after:border-opacity-20 after:absolute after:right-[-5%] after:top-1/3 after:h-1/4`}
            />
            <StatisticsCard
              icon={<FaCalendar className="text-5xl font-bold me-2" />}
              title={statisticsData?.last30DaysIncidentsNo}
              paragraph={"Active incidents>30 days"}
              style={``}
            />
          </div>
        )}

        {/* Buttons */}
        <div className="mt-5  md:flex justify-between">
          <Button
            className="bg-transparent text-primary border-2 border-primary rounded-lg hover:bg-primary hover:text-white block mx-auto md:mx-0"
            size="md"
          >
            <GrAnalytics className="text-2xl me-1" /> Reporting/Analytics
          </Button>
          <Button
            className="bg-primary  text-white border-2 border-primary rounded-lg hover:bg-transparent hover:text-primary mt-3 md:mt-0"
            size="md"
            onClick={() =>
              navigate(
                `/green-shield/nonFinancial/ESG-incident-management/addNewIncident/${id}`
              )
            }
          >
            <IoIosAddCircleOutline className="text-2xl me-1" /> Add new incident
          </Button>
        </div>
        <div className="mt-8">
          <div>
            <IncidentTable SETData={statistics} />
          </div>
        </div>
      </S3Layout>
    </>
  );
}
