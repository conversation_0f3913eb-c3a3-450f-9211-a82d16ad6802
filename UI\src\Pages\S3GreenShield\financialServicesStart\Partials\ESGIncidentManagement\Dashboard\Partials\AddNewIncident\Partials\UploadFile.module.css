.wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%; /* أو تحديد ارتفاع معين حسب الحاجة */
    box-sizing: border-box;
}

.dropzone {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    border-radius: 8px;
    border: 2px solid #05808b;
    padding: 20px;
    box-sizing: border-box;
    transition: height 0.3s ease-in-out; /* لإضافة تأثير انتقال ناعم */
}

.dropzone.hidden {
    height: 0; /* إخفاء DropBox مع الحفاظ على البوردر */
    padding: 0; /* إزالة الحشو عند اختفاء DropBox */
    overflow: hidden; /* منع ظهور أي محتوى */
}

.control {
    margin-top: 20px; /* أو حسب الحاجة */
}
