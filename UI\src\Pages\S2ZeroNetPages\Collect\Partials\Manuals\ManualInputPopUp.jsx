import { Button, Modal, NumberInput, Select } from "@mantine/core";
import { useState } from "react";
import { IoIosArrowDown } from "react-icons/io";

export default function ManualInputPopUp({
 opened,
 close,
 PopUpInputs,
 handleSaveAdditionalPayload,
 t,
 handleInputChange,
 title,
 item,
 inputValues,
 customFactors = [],
 assignDataBasedOnActivity,
 handleModalChange,
}) {
 const [selectedId, setSelectedId] = useState(0);

 const myListQuantity =
  item == "quantityKeys" && customFactors[0]?.quantityKeys;
 const myObjQuantity =
  item == "quantityKeys" &&
  myListQuantity?.reduce((obj, key) => {
   obj[key] = null;
   return obj;
  }, {});
 const myListPayload =
  item == "additional_payload" && customFactors[0]?.additionalPayload;

 const [quantity, setquantity] = useState(myObjQuantity);

 return (
  <Modal opened={opened} onClose={close} title={title} size={"70rem"} centered>
   {
    <>
     {item === "quantityKeys" ? (
      <div className="flex gap-5">
       {Object.entries(quantity || {}).map(([key, value], i) => (
        <NumberInput
         key={key}
         label={key}
         placeholder="Enter Number..."
         value={inputValues?.[item]?.[key] || ""}
         onChange={(e) => handleInputChange(key, e, item)}
         radius={10}
         rightSection={" "}
         size="md"
        />
       ))}
      </div>
     ) : (
      item === "additional_payload" && (
       <div className="grid lg:grid-cols-3 gap-5">
        {Object.entries(myListPayload || {}).map(([key, value], indx) => (
         <>
          {Array.isArray(value) ? (
           <Select
            key={key}
            label={key}
            placeholder="Select item..."
            data={value}
            rightSection={!inputValues?.[item]?.[key] && <IoIosArrowDown />}
            value={inputValues?.[item]?.[key] || ""}
            onChange={(e) => handleInputChange(key, e, item)}
            radius={10}
            size="md"
            clearable
           />
          ) : value === null ? (
           <NumberInput
            key={key}
            label={key}
            placeholder="Enter Number..."
            value={inputValues?.[item]?.[key] || ""}
            onChange={(e) => handleInputChange(key, e, item)}
            radius={10}
            rightSection={" "}
            size="md"
           />
          ) : null}
         </>
        ))}
       </div>
      )
     )}
    </>
   }

   <Button
    onClick={() => {
     handleSaveAdditionalPayload(item);
    }}
    radius={10}
    size="md"
    className="mt-4"
   >
    {t("save")}
   </Button>
  </Modal>
 );
}
