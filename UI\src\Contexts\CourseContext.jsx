/* eslint-disable react-refresh/only-export-components */
import ApiS3 from '@/Api/apiS3';
import { createContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router';

export const CourseContext = createContext({
  course: [],
  loading: false,
  error: '',
  progress: 0,
  isWatching: false,
  currentVideo: {},
  expandedSection: false,
  setCurrentVideo: () => {},
  playLecture: () => {},
  toggleSection: () => {},
  handleVideoPlayer: () => {},
  closeLecture: () => {},
  getMyCertification: () => {},
});

function CourseContextProvider({ children }) {
  const [data, setData] = useState(null);

  const [error, setError] = useState(null);
  const [currentVideo, setCurrentVideo] = useState({});
  const [isWatching, setIsWatching] = useState(false);
  const [loading, setLoading] = useState(false);
  const [expandedSection, setExpandedSection] = useState(null);
  const { t } = useTranslation();

  const { courseId } = useParams();

  useEffect(() => {
    const fetchCourseDetails = async () => {
      setLoading(true);
      try {
        const response = await ApiS3.get(`/academy/portal/${courseId}`);
        setData(response.data);
        setCurrentVideo(response.data?.courseId?.sections[0]?.lectures[0] || null);
      } catch (err) {
        setError(t('Failed to load course details. Please try again later.'));
        console.error('Error fetching course details:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchCourseDetails();
  }, [courseId, t]);

  const toggleSection = (index) => {
    setExpandedSection(expandedSection === index ? null : index);
  };

  const playLecture = (lecture) => {
    setCurrentVideo(lecture);
    setIsWatching(true);
  };
  const closeLecture = () => {
    setCurrentVideo('');
    setIsWatching(false);
  };
  const handleVideoPlayer = async (lectureId, action) => {
    try {
      await ApiS3.get(`/academy/markComplete/${courseId}/${lectureId}`);

      let currentLectureIndex, nextLecture;
      const allLectures = data.courseId.sections.map((section) => section.lectures.map((lecture) => lecture)).flat(1);

      const notWatchedLectures = allLectures.filter((lecture) => !data.progress[lecture._id]);

      currentLectureIndex = allLectures.findIndex((lec) => lec._id === lectureId);
      if (currentLectureIndex !== -1) {
        if (action === 'NEXT') {
          if (currentLectureIndex < allLectures.length - 1) {
            nextLecture = allLectures[currentLectureIndex + 1];
          }
        }
        if (action === 'PREVIOUS') {
          if (currentLectureIndex !== 0) {
            nextLecture = allLectures[currentLectureIndex - 1];
          }
        }

        if (action === 'START') {
          if (notWatchedLectures.length > 0) {
            nextLecture = notWatchedLectures[0];
          } else {
            nextLecture = allLectures[0];
          }
        }
      }

      // Set the next lecture if available
      if (nextLecture) {
        setCurrentVideo(nextLecture);
        setIsWatching(true);
      } else {
        setIsWatching(false);
        console.log('No more lectures available.');
      }
    } catch (err) {
      setError(t('Failed to mark lecture as completed. Please try again later.'));
      console.error('Error marking lecture as completed:', err);
    }
  };

  const Allprogress = {
    ...data?.progress,
    progressPercentage: data?.progressPercentage || 0,
  };

  const contextValue = {
    course: data,
    loading,
    error,
    progress: Allprogress,
    isWatching,
    currentVideo,
    expandedSection,
    setCurrentVideo,
    playLecture,
    toggleSection,
    handleVideoPlayer,
    closeLecture,
  };

  return <CourseContext.Provider value={contextValue}>{children}</CourseContext.Provider>;
}

export default CourseContextProvider;
