import { useState, useEffect } from "react";
import ApiProfile from "@/Api/apiProfileConfig";
import Loading from "@/Components/Loading";
import { Button, Modal, Accordion, Pagination, Card, Group, Title, Text } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useDisclosure } from "@mantine/hooks";
import AddGroupModal from "./AddGroupModal";
import AddComponentModal from "./AddComponentModal";
import { showNotification } from "@mantine/notifications";
import { LuChevronDown } from "react-icons/lu";

const ToolsCard = () => {
  const { t } = useTranslation();
  const [addGroupOpened, { open: addGroupOpen, close: addGroupClose }] = useDisclosure(false);
  const [addComponentOpened, { open: addComponentOpen, close: addComponentClose }] = useDisclosure(false);
  const [deleteModalOpened, { open: deleteModalOpen, close: deleteModalClose }] = useDisclosure(false);
  const [groups, setGroups] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedGroupId, setSelectedGroupId] = useState(null);
  const [selectedGroupName, setSelectedGroupName] = useState("");
  const [selectedComponentId, setSelectedComponentId] = useState(null);
  const [selectedComponentName, setSelectedComponentName] = useState("");
  const [deleteType, setDeleteType] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const rowsPerPage = 1; // One group card per page
  const [totalPages, setTotalPages] = useState(1);

  // Fetch groups on component mount
  useEffect(() => {
    getAllGroups();
  }, []);

  // Update total pages when groups change
  useEffect(() => {
    setTotalPages(Math.ceil(groups.length / rowsPerPage));
  }, [groups]);

  const getAllGroups = async () => {
    try {
      setLoading(true);
      const { data } = await ApiProfile.get("https://portal-auth-main.azurewebsites.net/admin/system-components");
      
      if (data && data.grouped_components) {
        const transformedGroups = Object.entries(data.grouped_components).map(([name, group]) => ({
          id: group.componentGroupId,
          name: name,
          componentsCount: group.number_of_group_elements,
          components: group.components || []
        }));
        setGroups(transformedGroups);
      } else {
        setGroups([]);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      showNotification({
        message: error.response?.data?.message || "Failed to fetch groups",
        color: "red",
      });
      console.error(error);
    }
  };

  // Get current page group
  const getCurrentGroup = () => {
    const startIndex = (currentPage - 1) * rowsPerPage;
    return groups[startIndex];
  };

  const handleAddComponent = (groupId, groupName, parentComponentId = null, parentComponentName = "") => {
    setSelectedGroupId(groupId);
    setSelectedGroupName(groupName);
    setSelectedComponentId(parentComponentId);
    setSelectedComponentName(parentComponentName);
    addComponentOpen();
  };

  const handleDeleteGroup = (groupId) => {
    setSelectedGroupId(groupId);
    setDeleteType('group');
    deleteModalOpen();
  };

  const handleDeleteComponent = (componentId) => {
    setSelectedComponentId(componentId);
    setDeleteType('component');
    deleteModalOpen();
  };

  const confirmDelete = async () => {
    try {
      setLoading(true);
      if (deleteType === 'group') {
        await ApiProfile.delete('https://portal-auth-main.azurewebsites.net/admin/component-groups/delete', {
          headers: {
            'componentGroupId': selectedGroupId
          }
        });
        showNotification({
          message: "Group deleted successfully",
          color: "green",
        });
      } else if (deleteType === 'component') {
        await ApiProfile.delete('https://portal-auth-main.azurewebsites.net/admin/delete-system-component', {
          headers: {
            'systemComponentId': selectedComponentId
          }
        });
        showNotification({
          message: "Component deleted successfully",
          color: "green",
        });
      }
      getAllGroups();
      deleteModalClose();
      setLoading(false);
    } catch (error) {
      setLoading(false);
      showNotification({
        message: error.response?.data?.message || "Delete operation failed",
        color: "red",
      });
      console.error(error);
    }
  };

  // Render component accordions recursively
  const renderComponentAccordions = (components, groupId, groupName) => {
    if (!components || components.length === 0) {
      return <Text className="text-gray-500 italic py-4 text-center">No components found in this group</Text>;
    }

    return components.map((component) => (
      <Accordion.Item key={component.systemComponentId} value={component.systemComponentId.toString()}>
        <Group position="apart" className="w-full">
          <Accordion.Control
            className="flex-grow"
          >
            {component.componentName}
          </Accordion.Control>
          <Group className="mr-4 z-10" spacing="xs" onClick={(e) => e.stopPropagation()}>
            <Button
              className="bg-gray-300 hover:bg-gray-300 text-gray-700 hover:text-gray-700"
              size="xs"
              onClick={() => handleAddComponent(groupId, groupName, component.systemComponentId, component.componentName)}
            >
              + Add Subcomponent
            </Button>
            <Button 
              className="bg-red-700 hover:bg-red-700" 
              size="xs"
              onClick={() => handleDeleteComponent(component.systemComponentId)}
            >
              Delete Component
            </Button>
          </Group>
        </Group>
        <Accordion.Panel>
          {component.children && component.children.length > 0 ? (
            <Accordion
              chevron={<LuChevronDown size={16} />}
              styles={{
                item: {
                  marginBottom: 8,
                  border: '1px solid #eaeaea',
                  borderRadius: 8,
                  overflow: 'hidden',
                }
              }}
            >
              {renderSubComponentAccordions(component.children, groupId, groupName, component.componentName)}
            </Accordion>
          ) : (
            <Text size="sm" color="dimmed" className="py-2">
              This component has no subcomponents yet.
            </Text>
          )}
        </Accordion.Panel>
      </Accordion.Item>
    ));
  };

  // Render subcomponents recursively
  const renderSubComponentAccordions = (subcomponents, groupId, groupName, parentComponentName) => {
    if (!subcomponents || subcomponents.length === 0) {
      return <Text className="text-gray-500 italic py-4 text-center">No subcomponents found</Text>;
    }

    return subcomponents.map((subcomponent) => (
      <Accordion.Item key={subcomponent.systemComponentId} value={subcomponent.systemComponentId.toString()}>
        <Group position="apart" className="w-full">
          <Accordion.Control
            className="flex-grow"
          >
            {subcomponent.componentName}
          </Accordion.Control>
          <Group className="mr-4 z-10" spacing="xs" onClick={(e) => e.stopPropagation()}>
            <Button
              className="bg-gray-300 hover:bg-gray-300 text-gray-700 hover:text-gray-700"
              size="xs"
              onClick={() => handleAddComponent(groupId, groupName, subcomponent.systemComponentId, subcomponent.componentName)}
            >
              + Add Subcomponent
            </Button>
            <Button 
              className="bg-red-700 hover:bg-red-700" 
              size="xs"
              onClick={() => handleDeleteComponent(subcomponent.systemComponentId)}
            >
              Delete Component
            </Button>
          </Group>
        </Group>
        <Accordion.Panel>
          {subcomponent.children && subcomponent.children.length > 0 ? (
            <Accordion
              chevron={<LuChevronDown size={16} />}
              styles={{
                item: {
                  marginBottom: 8,
                  border: '1px solid #eaeaea',
                  borderRadius: 8,
                  overflow: 'hidden',
                }
              }}
            >
              {renderSubComponentAccordions(
                subcomponent.children, 
                groupId, 
                groupName, 
                subcomponent.componentName
              )}
            </Accordion>
          ) : (
            <Text size="sm" color="dimmed" className="py-2">
              This subcomponent has no further subcomponents.
            </Text>
          )}
        </Accordion.Panel>
      </Accordion.Item>
    ));
  };

  const currentGroup = getCurrentGroup();

  return (
    <div>
      {/* Add Group Button */}
      <div className="w-full mb-4">
        <Button
          className="bg-[#05808b57] border-2 border-primary rounded-lg font-bold text-primary hover:bg-[#05808b57] hover:text-primary w-full"
          size="lg"
          onClick={addGroupOpen}
        >
          {t("+ Add Group")}
        </Button>
      </div>
      
      {/* Group Card Display */}
      {loading ? (
        <Loading />
      ) : (
        <div>
          {groups.length > 0 ? (
            <div>
              {currentGroup && (
                <Card shadow="sm" p="lg" radius="md" withBorder className="mb-4">
                  <Group position="apart" className="mb-4 border-b pb-2 flex flex-row justify-between">
                    <Title order={3}>{currentGroup.name}</Title>
                    <Group>
                      <Button
                        className="bg-gray-300 hover:bg-gray-300 text-gray-700 hover:text-gray-700 rounded-full"
                        onClick={() => handleAddComponent(currentGroup.id, currentGroup.name)}
                      >
                        + Add Component
                      </Button>
                      <Button 
                        className="bg-red-700 hover:bg-red-700" 
                        size="sm"
                        onClick={() => handleDeleteGroup(currentGroup.id)}
                      >
                        Delete Group
                      </Button>
                    </Group>
                  </Group>
                  
                  <Accordion
                    chevron={<LuChevronDown size={16} />}
                    styles={{
                      item: {
                        marginBottom: 8,
                        border: '1px solid #eaeaea',
                        borderRadius: 8,
                        overflow: 'hidden',
                      }
                    }}
                  >
                    {renderComponentAccordions(
                      currentGroup.components, 
                      currentGroup.id, 
                      currentGroup.name
                    )}
                  </Accordion>
                </Card>
              )}

              {/* Pagination */}
              <div className="md:flex justify-between mt-4 mb-4">
                <p className="text-sm text-secondary-300">
                  {t("showingData", {
                    start: currentPage,
                    end: currentPage,
                    total: groups.length,
                  })}
                </p>
                <Pagination
                  page={currentPage}
                  onChange={(e) => {
                    setCurrentPage(e);
                  }}
                  total={totalPages}
                  color="#00c0a9"
                />
              </div>
            </div>
          ) : (
            <div className="text-center py-8 bg-gray-100 rounded-lg">
              <Text size="lg" color="dimmed">No groups found. Add a new group to get started.</Text>
            </div>
          )}
        </div>
      )}

      {/* Add Group Modal */}
      <AddGroupModal
        close={addGroupClose}
        opened={addGroupOpened}
        refreshGroups={getAllGroups}
      />
      
      {/* Add Component Modal */}
      <AddComponentModal
        close={addComponentClose}
        opened={addComponentOpened}
        groupId={selectedGroupId}
        groupName={selectedGroupName}
        parentComponentId={selectedComponentId}
        parentComponentName={selectedComponentName}
        refreshGroups={getAllGroups}
      />

      {/* Delete Confirmation Modal */}
      <Modal 
        opened={deleteModalOpened} 
        onClose={deleteModalClose} 
        title={`Delete ${deleteType === 'group' ? 'Group' : 'Component'}`}
        centered
      >
        <p>Are you sure you want to delete this {deleteType}?</p>
        <div className="flex justify-end gap-3 mt-4">
          <Button onClick={deleteModalClose} variant="outline">
            Cancel
          </Button>
          <Button 
            className="bg-red-700 hover:bg-red-700"
            onClick={confirmDelete}
          >
            {loading ? <Loading /> : "Yes, Delete"}
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default ToolsCard;