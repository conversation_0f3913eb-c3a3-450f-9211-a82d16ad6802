import axios from "axios";
import Cookies from "js-cookie";

export async function fetchAssessmentData({ previousReportid }) {
  if (!previousReportid) {
    throw new Error("No report ID provided");
  }

  const API_ENDPOINT = `https://issb-report-api-staging.azurewebsites.net/api/v1/reports/${previousReportid}`;
  const token = Cookies.get("level_user_token");
  try {
    const response = await axios.get(API_ENDPOINT, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data.report;
  } catch (error) {
    console.error("Error fetching assessment data:", error);
    throw error;
  }
}
