import { But<PERSON>, Checkbox, ScrollArea, Table } from "@mantine/core";

import { useTranslation } from "react-i18next";
import AssetRow from "./AssetRow";
import { useState } from "react";
import Loading from "@/Components/Loading";
import ApiS2 from "@/Api/apiS2Config";
import { showNotification } from "@mantine/notifications";
import { BiTrash } from "react-icons/bi";

const ConfigurationDataTable = ({
  tableData,
  assetTypeAll,
  fetchAgain,
  assetTypeDrop,
}) => {
  const { t } = useTranslation();

  const msg = (txt, color = "green") => {
    showNotification({ message: txt, color: color });
  };
  const [selection, setSelection] = useState([]);
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [itemId, setItemId] = useState(0);

  const toggleRow = (id) =>
    setSelection((current) =>
      current.includes(id)
        ? current.filter((item) => item !== id)
        : [...current, id]
    );
  const toggleAll = () =>
    setSelection((current) => {
      if (tableData.length === 0) return [];
      return current.length === tableData.length
        ? []
        : tableData.map((item) => item.id);
    });

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      const { data } = await ApiS2.post("/admin/delete-company-assets", {
        asset_ids: selection,
      });
      if (data) {
        msg(data.message);
      }
      setIsDeleting(false);
      setIsDeleteOpen(false);
      fetchAgain();
    } catch (err) {
      console.log(err);
      setIsDeleteOpen(false);
      setIsDeleting(false);
      msg(err.response.data.message, "red");
    }
  };

  const rows = tableData?.map((item) => {
    return (
      <AssetRow
        toggleRow={toggleRow}
        key={item.id}
        item={item}
        selection={selection}
        assetTypeDrop={assetTypeDrop}
        assetTypeAll={assetTypeAll}
        fetchAgain={fetchAgain}
        itemId={itemId}
        setItemId={setItemId}
      />
    );
  });

  return (
    <div className="my-8">
      <ScrollArea>
        <Table
          miw={800}
          verticalSpacing="sm"
          border={2}
          borderColor=""
          withTableBorder
        >
          <Table.Thead className="bg-secondary-lite-gray pb-6 text-base font-medium text-center ">
            <Table.Tr>
              <Table.Th>
                <Checkbox
                  onChange={toggleAll}
                  checked={
                    tableData.length > 0 &&
                    selection.length === tableData.length
                  }
                  color="#07838F"
                  className="manage-asset"
                />
              </Table.Th>
              <Table.Th className="text-left">{t("Action")}</Table.Th>
              <Table.Th className="text-left">{t("Name")}</Table.Th>
              <Table.Th className="text-left pl-9 w-38">
                {t("Emission Source")}
              </Table.Th>
              <Table.Th className="pl-7 text-left">{t("Site Name")}</Table.Th>
              <Table.Th className="">
                {t("AssetName")} {/* Translated table header */}
              </Table.Th>
              <Table.Th className="text-left">
                {t("Country")} {/* Translated table header */}
              </Table.Th>
              <Table.Th className="text-left">
                {t("AssetReference")} {/* Translated table header */}
              </Table.Th>
              <Table.Th className="text-left">
                {t("AdditionalNotes")} {/* Translated table header */}
              </Table.Th>
              {/* <Table.Th className="text-center">
                  {t("Action")} 
                </Table.Th> */}
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>{rows}</Table.Tbody>
        </Table>
      </ScrollArea>

      {isDeleteOpen ? (
        <Button
          disabled={isDeleting}
          title="Confirm Delete Selected"
          className={`font-bold mt-5 text-xs bg-red-500 text-white hover:bg-red-400
              hover:text-white px-5 
              ${
                isDeleting.length == 0
                  ? "opacity-50 cursor-not-allowed"
                  : "opacity-100"
              }
              `}
          onClick={handleDelete}
          type="submit"
        >
          Confirm Delete Selected {isDeleting && <Loading />}
        </Button>
      ) : (
        <Button
          disabled={selection.length == 0}
          title="Select to delete"
          className={`font-bold mt-5 text-xs bg-gray-100 text-red-500
              hover:border-red-500 px-2 
              ${
                selection.length == 0
                  ? "opacity-50 cursor-not-allowed"
                  : "opacity-100"
              }
              `}
          onClick={() => setIsDeleteOpen(true)}
          type="submit"
        >
          <BiTrash size={20} />
        </Button>
      )}
    </div>
  );
};

export default ConfigurationDataTable;
