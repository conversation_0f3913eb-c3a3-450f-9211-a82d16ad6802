import React from "react";
import S3Layout from "@/Layout/S3Layout";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import MainCard from "./Partials/MainCard";
import firstImg from "@/assets/images/2c89ca2e8c40bb2d4c0ad0f932f1c2fc.png";
import secImg from "@/assets/images/16e9ff51601a31dd3a22146ae9d741e3.png";
import thirdImg from "@/assets/images/f7e2894ad79ac7679fc91bda0b84716a.png";

export default function FinancialStart() {
  const { MainCardMenu } = useSideBarRoute();
  return (
    <>
      <S3Layout
        menus={MainCardMenu}
        navbarTitle="GreenShield"
        // breadcrumbItems={[
        //   { title: "Launchpad", href: "/get-started" },
        //   { title: "GreenShield", href: "/green-shield/general" },
        //   {
        //     title: "Financial Services",
        //     href: "/green-shield/financial-service-start",
        //   },
        // ]}
      >
        <div className="bg-[#EAECF0] font-semibold text-2xl shadow-lg rounded-lg p-4 -mt-6 mb-4 flex justify-center items-center text-[#07838F] ">
          Drive Sustainable Success with Intelligent Risk Management and
          Compliance Solutions
        </div>
        <div className="grid grid-cols-1  md:grid-cols-2 xl:grid-cols-3 gap-10">
          <div className="w-full rounded-lg overflow-hidden">
            <MainCard
              link={"/green-shield/compilance/CompilanceView"}
              topColor={"bg-[#ade5de]"}
              bodyColor={"bg-[#00c0a9]"}
              borderColor={"border-[#00c0a9]"}
              img={firstImg}
              headTittle={"Anti-GreenWashing Compliance Assessment"}
              mt={"mt-10"}
            />
          </div>
          <div className="w-full rounded-lg overflow-hidden">
            <MainCard
              link={"/green-shield/financial/ESG-risk-management"}
              topColor={"bg-[#b7c4ce]"}
              bodyColor={"bg-[#205374]"}
              borderColor={"border-[#205374]"}
              img={secImg}
              headTittle={
                <span>
                  ESG
                  <p>Integrated Risk & Impact Management</p>
                </span>
              }
              mt={"mt-10"}
            />
          </div>
          <div className="w-full rounded-lg overflow-hidden">
            <MainCard
              link={"/green-shield/financial/ESG-incident-management"}
              topColor={"bg-[#cfeac8]"}
              bodyColor={"bg-[#70d162]"}
              borderColor={"border-[#70d162]"}
              img={thirdImg}
              headTittle={
                <span>
                  Stakeholder Engagement
                  <p className="text-lg font-light">
                    ESG Opportunities and <br />
                    Incident Management
                  </p>
                </span>
              }
              mt={"mt-10"}
            />
          </div>
        </div>
      </S3Layout>
    </>
  );
}
