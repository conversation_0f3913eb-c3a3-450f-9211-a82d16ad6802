import {
    Box,
    Title,
    Text,
    List,
    Group,
    ThemeIcon,
    Stack,
    Paper,
} from "@mantine/core";
import { FaCheck } from "react-icons/fa";

export default function EsgRolesResponsibilitiesGuide() {
    return (
        <Box mx="auto" maxWidth="1200px" p="md">
            <Title order={1} mb="xl">
                ESG Risk Universe and Governance: Roles & Responsibilities User
                Guide
            </Title>

            {/* Overview Section */}
            <Stack spacing="lg">
                <Text size="lg" mb="sm">
                    <b>Overview</b>
                </Text>
                <Text c="dimmed">
                    Outlines the roles and duties of different organizational
                    units in managing ESG risks.
                </Text>

                {/* Page Structure Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Page Structure</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>Responsibility Matrix:</Text>
                        <List listStyleType="circle" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={400}>
                                    Columns: Same as Assessment Matrix (Board of
                                    Directors, C-Suite, Risk Management Team,
                                    ESG Committee, Department Heads).
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={400}>
                                    Rows: Key Activities (Strategy Approval,
                                    Risk Identification, Risk Assessment,
                                    Mitigation Strategy, Monitoring &
                                    Reporting).
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={400}>
                                    Cells: Detailed descriptions or tasks
                                    assigned to each role (to be populated).
                                </Text>
                            </List.Item>
                        </List>
                    </List.Item>
                </List>

                {/* Interacting with the Page Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Interacting with the Roles & Responsibilities Page</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>Define Roles:</Text>
                        <Text size="sm">
                            Input specific duties or tasks for each
                            role-activity pair (e.g., "C-Suite oversees Risk
                            Assessment implementation").
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Edit Responsibilities:</Text>
                        <Text size="sm">
                            Update roles as organizational structures or
                            priorities change.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Align with Matrix:</Text>
                        <Text size="sm">
                            Ensure consistency with the Assessment Matrix
                            assignments.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Configure Data:</Text>
                        <Text size="sm">
                            Use the Configure Data button to modify the matrix
                            layout or content.
                        </Text>
                    </List.Item>
                </List>

                {/* Understanding Progress Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Understanding Your Progress</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>Roles Overview:</Text>
                        <List listStyleType="disc" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={700}>Accountability:</Text>
                                <Text size="sm">
                                    Clearly delineates who is responsible for
                                    each ESG task.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Collaboration:</Text>
                                <Text size="sm">
                                    Encourages coordination across different
                                    organizational levels.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Flexibility:</Text>
                                <Text size="sm">
                                    Allows adjustments to reflect current
                                    governance needs.
                                </Text>
                            </List.Item>
                        </List>
                    </List.Item>
                </List>

                {/* Next Steps Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Next Steps</b>
                </Text>
                <List listStyleType="decimal" spacing="sm">
                    <List.Item>
                        <Text>
                            Populate the matrix with specific responsibilities
                            for each role.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Cross-check with the Assessment Matrix to ensure
                            alignment.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Regularly review and update roles to reflect changes
                            in ESG priorities.
                        </Text>
                    </List.Item>
                </List>

                {/* Tip Section */}
                <Paper shadow="sm" p="md" radius="md" mt="xl" withBorder>
                    <Group position="apart">
                        <ThemeIcon variant="light" radius="xl" size="2rem">
                            <FaCheck size="1.2rem" />
                        </ThemeIcon>
                        <Text fw={500}>
                            Engage all stakeholders to clarify and agree on
                            responsibilities!
                        </Text>
                    </Group>
                </Paper>
            </Stack>
        </Box>
    );
}
