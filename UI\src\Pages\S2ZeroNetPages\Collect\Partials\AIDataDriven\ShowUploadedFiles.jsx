import { useDisclosure } from "@mantine/hooks";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@mantine/core";
import { IoMdClose } from "react-icons/io";
import { CgSandClock } from "react-icons/cg";
import ViewPDF from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/ViewPDF";
import { MdOutlineRemoveRedEye } from "react-icons/md";

export default function ShowUploadedFiles({ files, handleRemove }) {
  const [opened, { open, close }] = useDisclosure(false);

  return (
    <>
      <button
        onClick={open}
        disabled={files.length === 0}
        className="px-4 py-[5px] rounded-lg text-[#00C0A9] border-2 border-[#00C0A9] hover:bg-[#00C0A9] hover:text-white flex items-center font-bold disabled:cursor-not-allowed"
      >
        <MdOutlineRemoveRedEye className="pr-2 w-fit text-xl" />
        <span>View</span>
      </button>

      <Modal opened={opened} onClose={close} size="lg" title="Uploaded Files">
        <div className="space-y-4">
          {files ? (
            files.map((file, idx) => (
              <div
                key={idx}
                className="mt-3 flex flex-col md:flex-row justify-between items-center w-full bg-[#e7f3f4] p-4 rounded-lg"
              >
                <h1 className="text-sm font-bold text-wrap md:max-w-[30%]">
                  {file.name > 10 ? file.name.slice(0, 10) + "..." : file.name}
                </h1>
                <div className="flex gap-2 items-center md:mt-0 mt-4">
                  <ViewPDF
                    btnStyle={
                      "text-[#07838F] flex gap- items-center flex-row-reverse text-sm gap-1"
                    }
                    pdfUrl={URL.createObjectURL(file)}
                    text="View Report"
                  />

                  <Button
                    className="bg-white text-red-600 rounded-2xl hover:bg-white hover:text-red-600 mt-0"
                    onClick={() => handleRemove(idx)}
                  >
                    Remove <IoMdClose className="text-neutral-700" />
                  </Button>
                </div>
              </div>
            ))
          ) : (
            <h2 className="flex  items-center justify-center gap-3 text-center py-12 text-2xl animate-pulse">
              <CgSandClock /> No files uploaded
            </h2>
          )}
        </div>
      </Modal>
    </>
  );
}
