{"$defs": {"QuestionObject": {"properties": {"questionId": {"title": "<PERSON><PERSON>", "type": "string"}, "scopeId": {"title": "<PERSON><PERSON><PERSON>", "type": "string"}, "categoryId": {"title": "Categoryid", "type": "string"}, "questionText": {"title": "Questiontext", "type": "string"}, "questionOption": {"items": {"additionalProperties": {"type": "string"}, "type": "object"}, "title": "Questionoption", "type": "array"}, "questionDescription": {"title": "Questiondescription", "type": "string"}, "questionMandatory": {"title": "Questionmandatory", "type": "boolean"}, "questionOrder": {"title": "Questionorder", "type": "integer"}, "responseOption": {"title": "Responseoption", "type": "integer"}, "responseNote": {"title": "Responsenote", "type": "string"}, "responseDT": {"title": "Responsedt", "type": "string"}}, "required": ["questionId", "scopeId", "categoryId", "questionText", "questionOption", "questionDescription", "questionMandatory", "questionOrder", "responseOption", "responseNote", "responseDT"], "title": "QuestionObject", "type": "object"}, "QuestionSet": {"properties": {"setId": {"title": "<PERSON><PERSON>", "type": "string"}, "questionObjects": {"items": {"$ref": "#/$defs/QuestionObject"}, "title": "Questionobjects", "type": "array"}, "scopeText": {"title": "Scopetext", "type": "string"}, "categoryText": {"title": "Categorytext", "type": "string"}, "scopeOrder": {"title": "Scopeorder", "type": "integer"}, "categoryOrder": {"title": "Categoryorder", "type": "integer"}}, "required": ["setId", "questionObjects", "scopeText", "categoryText", "scopeOrder", "categoryOrder"], "title": "QuestionSet", "type": "object"}}, "properties": {"responseId": {"title": "Responseid", "type": "string"}, "questionSet": {"items": {"$ref": "#/$defs/QuestionSet"}, "title": "Questionset", "type": "array"}}, "required": ["responseId", "questionSet"], "title": "UIResponse", "type": "object"}