import { ScrollArea, Table } from '@mantine/core'
import { useTranslation } from 'react-i18next';

const DistanceBasedMethod = () => {
  const { t } = useTranslation();

  return (
    <>
          <div className="bg-white p-4 mt-5 rounded-xl border-2">
      <ScrollArea miw={600}>
        <Table
          verticalSpacing="sm"
          className="p-2 bg-white"
          withTableBorder
          highlightOnHover
        >
          <Table.Thead className="border-b-2 border pb-4 bg-[#f5f4f5] font-bold text-[15px] text-left">
            <Table.Tr>
              <Table.Th>{t("Parameter")}</Table.Th>
              <Table.Th>{t("Value")}</Table.Th>
              <Table.Th>{t("AI Assistant")}</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody className="text-[15px]">
            <Table.Tr>
              <Table.Td className="font-medium">{t("Distance (km)")}</Table.Td>
              <Table.Td>
                <input
                  type="text"
                  placeholder={t("e.g., 5000")}
                  className="w-full border rounded px-2 py-2 bg-[#F7F7F7] text-gray-500"
                  disabled
                />
              </Table.Td>
              <Table.Td className="align-middle">
                <span className="text-gray-500">{t("Required")}</span>
              </Table.Td>
            </Table.Tr>
            <Table.Tr>
              <Table.Td className="font-medium">{t("Distance Traveled (km)")}</Table.Td>
              <Table.Td>
                <input
                  type="text"
                  placeholder={t("e.g., 1200")}
                  className="w-full outline-none border rounded px-2 py-2"
                />
              </Table.Td>
              <Table.Td className="align-middle">
                <button
                  className="bg-[#07838F] w-full text-white px-4 py-2 rounded font-semibold"
                  type="button"
                >
                  {t("Estimate")}
                </button>
              </Table.Td>
            </Table.Tr>
            <Table.Tr>
              <Table.Td className="font-medium">{t("Transport Mode")}</Table.Td>
              <Table.Td>
                <select
                  className="w-full border rounded px-2 py-2 bg-white"
                  defaultValue="Heavy-duty truck"
                >
                  <option value="Heavy-duty truck">{t("Heavy-duty truck")}</option>
                  <option value="Light-duty truck">{t("Light-duty truck")}</option>
                  <option value="Rail">{t("Rail")}</option>
                  <option value="Ship">{t("Ship")}</option>
                  <option value="Air">{t("Air")}</option>
                </select>
              </Table.Td>
              <Table.Td className="align-middle">
                <span className="text-gray-500">{t("Required")}</span>
              </Table.Td>
            </Table.Tr>
            <Table.Tr>
              <Table.Td className="font-medium">{t("Reporting Year")}</Table.Td>
              <Table.Td>
                <select
                  className="w-full border rounded px-2 py-2 bg-white"
                  defaultValue="2025"
                >
                  <option value="2025">2025</option>
                  <option value="2024">2024</option>
                  <option value="2023">2023</option>
                  <option value="2022">2022</option>
                </select>
              </Table.Td>
              <Table.Td className="align-middle">
                <span className="text-gray-500">{t("Auto-updates")}</span>
              </Table.Td>
            </Table.Tr>
          </Table.Tbody>
        </Table>
        <button
          className="bg-[#07838F] mt-5 w-full text-white px-4 py-2 rounded font-semibold"
          type="button"
        >
          {t("Calculate Transportation Emissions")}
        </button>
      </ScrollArea>
    </div>

    <div className="bg-white border-2 h-full border-[#E8E7EA] rounded-lg p-4 mt-8 mb-2">
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1 h-full">
          <div className="font-semibold text-gray-700 mb-2">
            {t("Transport Mode Info")}
          </div>
          <div className="bg-[#F5F4F5] rounded p-3 h-[100px]">
            <div className="font-bold text-gray-800 mb-1">{t("Heavy-duty Truck")}</div>
            <div className="text-sm text-gray-600">
              {t("Emission Factor")}: 0.132 kgCO₂e/tonne-km
            </div>
            <div className="text-sm text-gray-600">
              {t("Typical Load")}: 20-25 {t("tonnes")}
            </div>
          </div>
        </div>
        <div className="flex-1 h-full">
          <div className="font-semibold text-gray-700 mb-2">
            {t("Distance-Based Method Tips")}
          </div>
          <div className="bg-[#F5F4F5] h-[100px] rounded p-3 text-sm text-gray-700 space-y-2">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-[#07838F]"></div>
              <span>{t("Use actual route distances when available")}</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-[#07838F]"></div>
              <span>{t("Consider vehicle load capacity utilization")}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    </>
  )
}

export default DistanceBasedMethod
