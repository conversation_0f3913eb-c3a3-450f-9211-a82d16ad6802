import ApiS3 from "@/Api/apiS3";
import Edit from "@/assets/images/edit.svg";
import {
  Button,
  MultiSelect,
  Select,
  TagsInput,
  TextInput,
  rem,
} from "@mantine/core";
import { DateInput } from "@mantine/dates";
import { isNotEmpty, useForm } from "@mantine/form";
import axios from "axios";
import { useEffect, useState } from "react";
import { IoArrowDownOutline } from "react-icons/io5";

const InteractionDetails = ({ incident, onClose }) => {
  const [isEditing, setIsEditing] = useState(false); // Manage editable state
  // console.log(CurrentRow);
  const handleEdit = () => {
    setIsEditing(!isEditing);
  };
  const form = useForm({
    initialValues: {
      remarks: "",
      status: "",
      // closureDate: "",
    },
    validate: {
      remarks: isNotEmpty(),
      status: isNotEmpty("Enter Your Status"),
      // closureDate: isNotEmpty("enter the closure date"),
    },
  });
  useEffect(() => {
    if (incident) {
      form.setValues({
        remarks: incident?.remarks || "",
        status: incident?.status || "",
      });
    }
  }, [incident]);

  const handleSave = async (value) => {
    console.log(value);
    console.log(incident._id);
    try {
      const { data } = await axios.patch(
        `http://localhost:3000/stakeholder-interactions/${incident._id}`,
        value,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      console.log(data);
      onClose();
      // updateIncident(formData);
      // Close modal after save
    } catch (error) {
      console.error("Error saving incident data:", error);
    }
  };

  // const handleSelectChange = (name, value) => {
  //   setFormData((prevData) => ({
  //     ...prevData,
  //     [name]: value,
  //   }));
  // };

  // Adjust background color based on priority
  // const bgColor =
  //   incident.priority === "High"
  //     ? "red"
  //     : incident.priority === "Medium"
  //     ? "green"
  //     : incident.priority === "Low"
  //     ? "yellow"
  //     : "gray";

  return (
    <div className="p-3 bg-white rounded-lg mt-3 yall">
      <div
        className="flex flex-row gap-4 font-bold text-2xl justify-center items-center mb-4 p-4 rounded-lg shadow-lg"
        // style={{ backgroundColor: bgColor }}
      >
        <div className="flex flex-col gap-2 w-full">
          <TextInput
            radius="md"
            label="interactionDescription"
            placeholder=""
            value={incident?.interactionDescription || "Not Found"}
            variant="unstyled"
            className="bg-[#fff] text-[#000] text-lg font-semibold shadow-lg p-4 rounded-lg"
            readOnly
          />
          <br />
          <TextInput
            key={form.key("remarks")}
            {...form.getInputProps("remarks")}
            label="stakeholder Response"
            name="remarks"
            placeholder="Enter you comment "
            className="bg-[#fff] text-[#000] text-lg font-semibold shadow-lg p-4 rounded-lg"
            readOnly={!isEditing}
          />
        </div>
        <button onClick={handleEdit}>
          <img src={Edit} alt="edit" />
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 items-center w-full gap-x-10 gap-y-3 text-start">
        <TextInput
          radius="md"
          label="Date"
          placeholder=""
          value={incident?.closureDate?.split("T")[0] || "Not Found"}
          variant="unstyled"
          className="bg-[#fff] text-[#000] text-lg font-semibold shadow-lg p-4 rounded-lg"
          readOnly
        />

        <Select
          key={form.key("status")}
          {...form.getInputProps("status")}
          label="Status"
          name="status"
          defaultSearchValue={incident.status || "Not Found"}
          data={[
            { value: "New", label: "New" },
            { value: "In Progress", label: "In Progress" },
            { value: "Resolved", label: "Resolved" },
            { value: "Closed", label: "Closed" },
          ]}
          variant="unstyled"
          radius="md"
          className="bg-[#fff] text-[#000] text-lg font-semibold shadow-lg p-4 rounded-lg"
          disabled={!isEditing}
        />

        <Select
          label="Priority"
          name="priority"
          defaultSearchValue={incident?.priority || "Not Found"}
          variant="unstyled"
          radius="md"
          className="bg-[#fff] text-[#000] text-lg font-semibold shadow-lg p-4 rounded-lg"
          readOnly
        />

        <TextInput
          key={form.key("interactionLcation")}
          {...form.getInputProps("interactionLcation")}
          label="Location"
          name="location"
          defaultValue={incident.interactionLocation || "Not Found"}
          // onChange={handleChange}
          variant="unstyled"
          radius="md"
          className="bg-[#fff] text-[#000] text-lg font-semibold shadow-lg p-4 rounded-lg"
          readOnly
        />

        <Select
          key={form.key("interactionType")}
          {...form.getInputProps("interactionType")}
          label="interaction Type"
          name="interactionType"
          defaultSearchValue={incident.interactionType || "Not Found"}
          variant="unstyled"
          radius="md"
          className="bg-[#fff] text-[#000] text-lg font-semibold shadow-lg p-4 rounded-lg"
          disabled
          readOnly
        />

        {/* <DateInput
          key={form.key("closureDate")}
          {...form.getInputProps("closureDate")}
          label="Closure Date"
          defaultValue={
            incident?.closureDate ? Date(incident.closureDate) : "Not Found"
          }
          valueFormat="DD/MM/YYYY"
          variant="unstyled"
          radius="md"
          className="bg-[#fff] text-[#000] text-lg font-semibold shadow-lg p-4 rounded-lg"
          disabled
          readOnly
        /> */}
      </div>
      <div className="flex justify-end mt-4 p-4 ">
        <Button
          type="submit"
          onClick={form.onSubmit(handleSave)}
          className={`bg-[#00C0A9] hover:bg-[#00C0A9] ${
            !isEditing && "cursor-not-allowed"
          }`}
          // onClick={handleSave}
          // disabled={!isEditing}
        >
          Save
        </Button>
      </div>
    </div>
  );
};

export default InteractionDetails;
