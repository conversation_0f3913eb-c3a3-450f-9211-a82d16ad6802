import { IconSwimming } from "@tabler/icons-react";
import { Badge, Group, Paper, Progress, Text, ThemeIcon } from "@mantine/core";
import { useState } from "react";
import DataTab from "./Partials/DataTab";
import AnalysisTab from "./Partials/AnalysisTab";

export function StatsCard() {
    const [completedSteps, setCompletedSteps] = useState(4);
    const [allSteps, setAllSteps] = useState(7);

    const getPres = () => {
        return Math.round((completedSteps / allSteps) * 100);
    };

    return (
        <Paper
            radius="md"
            withBorder
            className="relative p-4 w-full rounded-xl flex gap-4 flex-col"
            mt={20}
        >
            <Text fw={700} fs={22} c="#939393">
                Data Collection: {getPres()}% Complete
            </Text>

            <div className="flex items-center w-full gap-2">
                <Progress
                    w="100%"
                    value={getPres()}
                    radius="xl"
                    size="lg"
                    classNames={{
                        root: "bg-[#C7DDE0] rounded-md",
                        section: "bg-[#07838F] rounded-md",
                    }}
                />
                <Text c="#939393" fz="sm" fw={600} w="84">
                    {completedSteps} / {allSteps} Steps
                </Text>
            </div>
        </Paper>
    );
}

export default function PowerBIAnalyticsView() {
    const [activeTabIndex, setActiveTabIndex] = useState(0);
    const tabs = [
        { name: "Data", component: <DataTab /> },
        { name: "Analysis", component: <AnalysisTab /> },
    ];

    const selectTab = (index) => {
        setActiveTabIndex(index);
    };

    return (
        <div className="w-full gap-4 overflow-hidden overflow-y-auto flex flex-col">
            <StatsCard />
            <div className="w-full items-center justify-center gap-4 flex">
                {tabs.map((tab, index) => (
                    <div
                        onClick={() => selectTab(index)}
                        key={index}
                        className={`w-1/2 text-lg py-2 text-center font-semibold cursor-pointer select-none rounded-lg transition-all  ${
                            index == activeTabIndex
                                ? "border-[#07838F] text-[#07838F] bg-[#07838F1A] border-0 border-l-8"
                                : "border-2 bg-transparent border-gray-300"
                        }`}
                    >
                        {tab.name}
                    </div>
                ))}
            </div>

            {tabs[activeTabIndex].component}
        </div>
    );
}
