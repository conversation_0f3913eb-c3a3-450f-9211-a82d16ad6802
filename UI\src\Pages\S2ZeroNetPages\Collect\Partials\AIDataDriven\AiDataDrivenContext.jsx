import { createContext, useState } from "react";

// eslint-disable-next-line react-refresh/only-export-components
export const AIDataDriven = createContext();

export function AIDataDrivenProvider({ children }) {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);

  return (
    <AIDataDriven.Provider value={{ data, setData, loading, setLoading }}>
      {children}
    </AIDataDriven.Provider>
  );
}
