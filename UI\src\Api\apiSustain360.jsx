// src/axiosConfig.js

import axios from "axios";
import Cookies from "js-cookie";

const baseURL = "https://sustain--360-staging.azurewebsites.net/";

// const baseURL = "http://localhost:9000";

const ApiS1Config = axios.create({
  baseURL,
  headers: {
    "Content-Type": "application/json",
    withCredentials: true,
    // Authorization: `Bearer ${Cookies.get("level_user_token")}`,
  },
});

ApiS1Config.interceptors.request.use(
  (config) => {
    // Modify config before sending the request
    config.headers["Authorization"] = `Bearer ${Cookies.get(
      "level_user_token"
    )}`;
    return config;
  },
  (error) => {
    // Handle request error
    return Promise.reject(error);
  }
);

ApiS1Config.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response && error.response.status === 401) {
      const errorMessage = error.response.data.message.toLowerCase();
      const currentPath = window.location.pathname;

      if (
        (errorMessage.includes("token is expired") ||
          errorMessage.includes("token is invalid")) &&
        currentPath !== "/login"
      ) {
        console.log("Token is invalid. Redirecting to login...");
        Cookies.remove("level_user_token");
        localStorage.clear();
        window.location.href = "/login"; // Redirect to login page
      }
    }
    return Promise.reject(error);
  }
);

// let isRefreshing = false;
// let failedRequestsQueue = [];

// ApiS1Config.interceptors.response.use(
//   (response) => response,
//   async (error) => {
//     const originalRequest = error.config;

//     if (
//       (error.response?.status === 401 &&
//         (error.response.data.message === "Token is expired." ||
//           error.response.data.message === "Invalid token." ||
//           error.response.data.message === "Token is invalid.")) ||
//       error.response.data.message === "Authorization failed"
//     ) {
//       if (!isRefreshing) {
//         isRefreshing = true;

//         try {
//           Cookies.remove("level_user_token");
//           if (Cookies.get("refreshToken") != null) {
//             const refreshToken = Cookies.get("refreshToken");
//             const response = await axios.post(
//               "https://portal-auth-main-staging.azurewebsites.net/refresh-token",
//               { refresh_token: refreshToken },
//               { withCredentials: true }
//             );
//             const { access_token: token } = response.data; // Assuming response includes new access token
//             Cookies.set("level_user_token", token);
//             Cookies.set("refreshToken", response.data.refresh_token);

//             // Retry failed requests
//             failedRequestsQueue.forEach((req) => req.onSuccess(token));
//             failedRequestsQueue = [];
//             console.log("token", token);

//             // Update original request
//             originalRequest.headers["Authorization"] = `Bearer ${token}`;
//             return ApiS1Config(originalRequest);
//           }
//         } catch (refreshError) {
//           if (
//             Cookies.get("refreshToken") != null &&
//             Cookies.get("level_user_token") == null
//           ) {
//             console.log(refreshError);
//             failedRequestsQueue.forEach((req) => req.onFailure(refreshError));
//             failedRequestsQueue = [];
//             Cookies.remove("level_user_token");
//             Cookies.remove("refreshToken");
//             localStorage.clear();
//             window.location.href = "/login";
//           }
//         } finally {
//           isRefreshing = false;
//         }
//       }

//       return new Promise((resolve, reject) => {
//         failedRequestsQueue.push({
//           onSuccess: (token) => {
//             originalRequest.headers["Authorization"] = `Bearer ${token}`;
//             resolve(ApiS1Config(originalRequest));
//           },
//           onFailure: (err) => reject(err),
//         });
//       });
//     }

//     return Promise.reject(error);
//   }
// );

export default ApiS1Config;
