import {
    Box,
    Title,
    Text,
    List,
    Group,
    ThemeIcon,
    Stack,
    Paper,
} from "@mantine/core";
import { FaCheck } from "react-icons/fa";

export default function DoubleMaterialityGuide() {
    return (
        <Box mx="auto" maxWidth="1200px" p="md">
            <Title order={1} mb="xl">
                Double Materiality Readiness User Guide
            </Title>

            {/* Overview Section */}
            <Stack spacing="lg">
                <Text size="lg" mb="sm">
                    <b>Overview</b>
                </Text>
                <Text c="dimmed">
                    Materiality Assessment helps you evaluate the financial and
                    societal impacts of ESG (Environmental, Social, Governance)
                    factors.
                </Text>

                {/* Getting Started Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Getting Started</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>Accessing the Readiness Page</Text>
                        <List listStyleType="circle" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={400}>
                                    Login: Log into the system using your
                                    credentials
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={400}>
                                    Navigate: From the main dashboard, select
                                    Materiality Assessment to access the
                                    Readiness dashboard.
                                </Text>
                            </List.Item>
                        </List>
                    </List.Item>
                </List>

                {/* Page Structure Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Page Structure</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>The Readiness page includes:</Text>
                        <List listStyleType="circle" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={400}>
                                    Assessment Questions: Questions to evaluate
                                    financial and societal impacts.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={400}>
                                    Editable Responses: To review and update
                                    previous answers.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={400}>
                                    Search and Filter: Tools to locate specific
                                    topics or filter questions by columns
                                    (Evidence, Action Items, Owner and Due
                                    Date).
                                </Text>
                            </List.Item>
                        </List>
                    </List.Item>
                </List>

                {/* Interacting with the Readiness Page Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Interacting with the Readiness Page</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>Answer Questions:</Text>
                        <Text size="sm">
                            Respond to questions assessing Double Materiality
                            Readiness by selecting:
                        </Text>
                        <List
                            listStyleType="lower-alpha"
                            spacing="sm"
                            ml="1.5rem"
                        >
                            <List.Item>
                                <Text fw={700}>Assessment Levels:</Text>
                                <List
                                    listStyleType="decimal"
                                    spacing="sm"
                                    ml="1.5rem"
                                >
                                    <List.Item>
                                        <Text fw={700}>Not Started</Text>
                                    </List.Item>
                                    <List.Item>
                                        <Text fw={700}>Not Applicable</Text>
                                    </List.Item>
                                    <List.Item>
                                        <Text fw={700}>Initial Planning</Text>
                                    </List.Item>
                                    <List.Item>
                                        <Text fw={700}>In Development</Text>
                                    </List.Item>
                                    <List.Item>
                                        <Text fw={700}>
                                            Partially Implemented
                                        </Text>
                                    </List.Item>
                                    <List.Item>
                                        <Text fw={700}>Fully Implemented</Text>
                                    </List.Item>
                                </List>
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Priority:</Text>
                                <List
                                    listStyleType="decimal"
                                    spacing="sm"
                                    ml="1.5rem"
                                >
                                    <List.Item>
                                        <Text fw={700}>Top</Text>
                                    </List.Item>
                                    <List.Item>
                                        <Text fw={700}>High</Text>
                                    </List.Item>
                                    <List.Item>
                                        <Text fw={700}>Medium</Text>
                                    </List.Item>
                                    <List.Item>
                                        <Text fw={700}>Low</Text>
                                    </List.Item>
                                </List>
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Evidence/Action Item:</Text>
                                <Text size="sm">
                                    Upload or link supporting documents (e.g.,
                                    project plans, vendor agreements) to
                                    validate the initiative.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Owner:</Text>
                                <Text size="sm">
                                    Assign a responsible individual or team
                                    (e.g., "Sustainability Manager," "Operations
                                    Team").
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Due Date:</Text>
                                <Text size="sm">
                                    Set a deadline for completion (e.g.,
                                    "2025-07-31" for July 31, 2025).
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Edit Responses:</Text>
                                <Text size="sm">
                                    Update answers to reflect recent changes in
                                    practices.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Use Search/Filter:</Text>
                                <Text size="sm">
                                    Use the search bar to find specific topics
                                    or filter questions by columns (Evidence,
                                    Action Items, Owner and Due Date).
                                </Text>
                            </List.Item>
                        </List>
                    </List.Item>
                </List>

                {/* Understanding Your Progress Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Understanding Your Progress</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>Progress Overview:</Text>
                        <List listStyleType="disc" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={700}>Question Coverage:</Text>
                                <Text size="sm">
                                    Questions assess both financial impact on
                                    your business and your impact on
                                    society/environment.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Editable Insights:</Text>
                                <Text size="sm">
                                    Edited responses ensure your assessment
                                    remains accurate and up-to-date.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Focused Analysis:</Text>
                                <Text size="sm">
                                    Search and filter options help you
                                    prioritize specific areas for review.
                                </Text>
                            </List.Item>
                        </List>
                    </List.Item>
                </List>

                {/* Next Steps Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Next Steps</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>
                            Completing Your Readiness Assessment:
                        </Text>
                        <List listStyleType="decimal" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text>
                                    Answer all questions thoughtfully to capture
                                    a holistic view of materiality.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text>
                                    Use the search/filter tools to revisit and
                                    update specific topics as needed.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text>
                                    Save your progress by clicking Save to
                                    ensure your responses are stored.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text>
                                    Proceed to the Assessment page to visualize
                                    your materiality impacts.
                                </Text>
                            </List.Item>
                        </List>
                    </List.Item>
                </List>

                {/* Tip Section */}
                <Paper shadow="sm" p="md" radius="md" mt="xl" withBorder>
                    <Group position="apart">
                        <ThemeIcon variant="light" radius="xl" size="2rem">
                            <FaCheck size="1.2rem" />
                        </ThemeIcon>
                        <Text fw={500}>
                            Regularly update your answers to keep your
                            assessment aligned with current practices!
                        </Text>
                    </Group>
                </Paper>
            </Stack>
        </Box>
    );
}
