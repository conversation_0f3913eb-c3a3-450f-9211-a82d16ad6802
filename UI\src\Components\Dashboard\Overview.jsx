import React from "react";
import { FaArrowDown } from "react-icons/fa6";
import PropType from "prop-types";

function Overview({ number, numMonth }) {
  return (
    <div className="bg-white py-[1rem] px-[1.5rem] rounded-2xl shadow-2xl xl:w-[37%] w-[90%]  mx-auto">
      <h1 className="font-bold text-[18px] mb-5">Consumption Overview</h1>
      <div className="flex flex-wrap justify-between items-center">
        <div>
          <p>CO2 E</p>
          <span className="font-bold text-[30px]">{number}</span>
        </div>
        <div>
          <p>From last month</p>
          <div className="flex items-center gap-[.5rem] text-[#70D162] ">
            <span className="font-bold text-[30px]  ">{numMonth}</span>
            <FaArrowDown className="text-lg" />
          </div>
        </div>
      </div>
    </div>
  );
}

Overview.propTypes = {
  number: PropType.number,
  numMonth: PropType.number,
};

export default Overview;
