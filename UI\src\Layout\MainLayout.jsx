import Chatbot from "@/Components/Chatbot";
import Navbar from "@/Components/NavBar/Navbar";
import Sidebar from "@/Components/SideBar/Sidebar";

import useSideBarRoute from "@/hooks/useSideBarRoute";
import { Breadcrumbs } from "@mantine/core";
import { useState } from "react";
import { Link } from "react-router-dom";
import { IoIosArrowForward } from "react-icons/io";
const MainLayout = ({
  menus,
  children,
  navbarTitle,
  subTitle,
  breadcrumbItems = [],
}) => {
  const { getStartMenu } = useSideBarRoute();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const [isChatBotOpen, setIsChatBotOpen] = useState(false)

  menus = menus ? menus : getStartMenu;

  const items = breadcrumbItems.map((item, index) => (
    <Link
      className={`${
        index !== breadcrumbItems.length - 1
          ? "text-gray-400"
          : "text-primary font-[500]"
      }`}
      to={item.href}
      key={index}
    >
      {item.title}
    </Link>
  ));
  return (
    <div className="flex flex-col ">
      <Navbar
        navbarSubtitle={subTitle}
        isSidebarOpen={isSidebarOpen}
        setIsSidebarOpen={setIsSidebarOpen}
      />

      <div className="flex-grow flex h-screen overflow-x-hidden overflow-y-auto max-md:text-center z-10">
        <Sidebar
          menus={menus}
          isOpen={isSidebarOpen}
          setIsOpen={setIsSidebarOpen}
          openChatBot={()=> setIsChatBotOpen(!isChatBotOpen)}
          //  sidebarStyle={sidebarStyle}
        />
        <div className={"px-3 w-full  mt-16 block mx-auto pb-20 overflow-y-auto"}>
          <div className=" text-2xl font-semibold font-inter leading-none text-primary mx-auto my-8 px-2 flex flex-col sm:flex-row items-center justify-between">
            <div>{navbarTitle}</div>

            <p className="text-sm font-normal mt-5 sm:mt-0">
              {items?.length > 0 && (
                <Breadcrumbs className="" separator={<IoIosArrowForward color="gray" size={20} />}>
                  {items}
                </Breadcrumbs>
              )}
            </p>
          </div>
          {children}
        </div>
      </div>
      {
        isChatBotOpen &&
      <Chatbot isChatBotOpen={isChatBotOpen} closeChatFunc={()=> setIsChatBotOpen(false)} />
      }
    </div>
  );
};

export default MainLayout;
