import { useEffect, useState } from "react";

import useSideBarRoute from "@/hooks/useSideBarRoute";
import MainLayout from "@/Layout/MainLayout";
import DueDiligenceTable from "./Components/DueDiligenceTable";

import { Button } from "@mantine/core";
import { useTranslation } from "react-i18next";

import { GoShare } from "react-icons/go";

import ApiS1Config from "@/Api/apiS1Config";
import Loading from "@/Components/Loading";
import { notifications, showNotification } from "@mantine/notifications";
import { useNavigate } from "react-router";
import { FaCheckCircle } from "react-icons/fa";
import DueReport from "./DueReport";
import { IoMdHome } from "react-icons/io";

const DueDiligenceView = () => {
  const navigate = useNavigate();
  const { dueDiligence } = useSideBarRoute();
  const { t } = useTranslation();
  const [active, setActive] = useState("Assessment");

  const [postReport, setPostReport] = useState(false);
  const [loading, setLoading] = useState(true);
  const [tabelData, setTabelData] = useState([]);

  const getDueData = async () => {
    try {
      const { data } = await ApiS1Config.get("/get_assessment_data", {
        headers: { assessmentName: "ESG Due Diligence" },
      });
      // let currentScope = data['ESG Due Diligence'].find(el=> el.categoryName == selectedScope)
      setTabelData(data);
      setLoading(false);
    } catch (error) {
      if (error.response.data.Message === "There is no active assessment") {
        navigate("/dueDiligence-report");
        showNotification({
          message: error.response.data.Message,
          color: "red",
        });
      }

      // navigate("/dueDiligence-report")
      // setLoading(false)
    }
  };

  const getReport = async () => {
    setPostReport(true);

    try {
      const { data } = await ApiS1Config.post(
        `/get_report`,
        {},
        {
          headers: {
            assessmentType: "ESG Due Diligence",
            // categoryName: selectedScope,
            status: true,
          },
        }
      );
      notifications.show({
        title: data?.Message,
        color: "green",
        icon: <FaCheckCircle />,
      });
      navigate("/dueDiligence-report");

      setPostReport(false);
    } catch (error) {
      setPostReport(false);
      //console.log(error);
      navigate("/dueDiligence-report");
      // notifications.show({
      //   title: error.response.data.Message,
      //   color: "red",
      // });
    }
  };
  useEffect(() => {
    getDueData();
  }, [loading]);

  const refetch = (value) => {
    // setLoading(value)
    getDueData();
  };

  let isAllSolved = tabelData?.solved;

  return (
    <MainLayout
      menus={dueDiligence}
      navbarTitle={t("esgDueDiligence")}
      breadcrumbItems={[
        { title: <IoMdHome size={20}/>, href: "/get-started" },
        { title: "Due Diligence", href: "/Grc/due-diligence" },
        { title: "ESG Due Diligence", href: "#" },
      ]}
    >
      <div className="grid lg:grid-cols-2 justify-center bg-[#FFFFFF] border-2 border-[#E8E7EA] p-6 gap-5 py-3 mb-6 rounded-lg text-primary">
        <button
          className={`relative text-lg font-semibold py-3 px-6 transition-all ${
            active === "Assessment"
              ? "active-tab rounded"
              : "text-[#5A5A5A] rounded-lg border-2"
          }`}
          onClick={() => setActive("Assessment")}
        >
          {t("Assessment")}
        </button>

        <button
          className={`relative text-lg font-semibold py-3 px-6 transition-all ${
            active === "Report"
              ? "active-tab rounded"
              : "text-[#5A5A5A] rounded-lg border-2"
          }`}
          onClick={() => setActive("Report")}
        >
          {t("Report")}
        </button>
      </div>

      {active == "Assessment" ? (
        <>
          {loading ? (
            <Loading />
          ) : (
            <div>
              <div className="items-center justify-between w-full px-4 py-5 bg-white rounded-lg shadow-md mb-7 sm:flex">
                <div>
                  <h1 className="text-lg font-bold text-black">
                    {t("esgDueDiligence")}
                  </h1>
                  <p className="font-normal text-sm text-[#667085]">
                    {t("descriptionText")}
                  </p>
                </div>

                <div className="mt-5 sm:mt-0 grid md:grid-cols-2  gap-5">
                  <Button
                    className={`text-white rounded-md bg-primary  ${
                      !isAllSolved
                        ? "cursor-not-allowed opacity-50"
                        : "hover:bg-primary hover:opacity-90 "
                    }`}
                    // className="duration-200 ease-out rounded-lg bg-primary border-secondary-300 hover:-translate-y-1 hover:bg-secondary-400 hover:bg-transparent hover:text-primary outline-none focus:outline-none"
                    size="md"
                    disabled={isAllSolved == true ? false : true}
                    onClick={getReport}
                  >
                    {postReport ? <Loading /> : "Assess"}
                  </Button>
                  <Button
                    className="duration-200 ease-out bg-transparent border-2 rounded-lg text-primary border-secondary-300 hover:-translate-y-2 hover:bg-secondary-400 hover:bg-transparent hover:text-primary outline-none focus:outline-none"
                    size="md"
                  >
                    <GoShare className="me-2" />
                    {t("export")}
                  </Button>
                  {/* <Button
                     className="text-indigo-950 rounded-lg bg-gradient-to-r from-[#FFF4D7] via-[#DADEFB] to-[#D8FFDC]  hover:text-indigo-950 hover:-translate-y-2 duration-200 ease-out"
                     size="md"
                   >
                     AI Suggestion
                     <TbSquarePlus2 className="ms-2 -rotate-90" />
                   </Button> */}
                </div>
              </div>
              <DueDiligenceTable
                data={tabelData}
                isAllSolved={isAllSolved}
                refetch={refetch}
              />
            </div>
          )}
        </>
      ) : (
        <>
          <DueReport />
        </>
      )}
    </MainLayout>
  );
};

export default DueDiligenceView;
