// useAutoLoanStore.js
import { create } from "zustand";

export const useRealEstateStore = create((set) => ({
  loan_amount: "",  // $1,000,000
  property_value: "",  // $1,500,000
  building_emissions: "",  // 50 tCO₂e/year
  property_type: "Commercial-Office",
  property_location: "",
  property_size: "",  // 5,000 square meters
  energy_performance_certificate: "C",
  data_quality_score: "",

  // results
  attribution_percentage: 0,
  building_emissions_intensity: 0,
  financed_emissions: 0,

  loading: false,
  estimate_building_emissions_loading:false,
  // Setters
  setLoanAmount: (v) => set({ loan_amount: v }),
  setPropertyValue: (v) => set({ property_value: v }),
  setBuildingEmissions: (v) => set({ building_emissions: v }),
  setPropertyType: (v) => set({ property_type: v }),
  setPropertyLocation: (v) => set({ property_location: v }),
  setPropertySize: (v) => set({ property_size: v }),
  setEnergyPerformanceCertificate: (v) => set({ energy_performance_certificate: v }),
  setDataQualityScore: (v) => set({ data_quality_score: v }),
  setLoading: (v) => set({ loading: v }),
  setEstimateBulidingEmissionLoading: (v)=> set({estimate_building_emissions_loading:v}),

  setResults: (results) => set({
    attribution_percentage: results.attribution_percentage,
    building_emissions_intensity: results.building_emissions_intensity,
    financed_emissions: results.financed_emissions
  }),
}));
