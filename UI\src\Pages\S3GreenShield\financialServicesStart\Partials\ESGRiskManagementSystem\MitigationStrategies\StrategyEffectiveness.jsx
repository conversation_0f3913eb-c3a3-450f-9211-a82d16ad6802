import fileAdd from "@/assets/images/file-add.svg";
import {
  Group,
  MantineProvider,
  MultiSelect,
  Radio,
  Select,
  SemiCircleProgress,
  Slider,
  TextInput,
  Textarea,
  createTheme,
  rem,
} from "@mantine/core";
import { DateInput } from "@mantine/dates";
import { useForm } from "@mantine/form";
import { useEffect, useRef, useState } from "react";
import Button from "../Components/Button";
import uploadComplete from "@/assets/images/uploadcomplete-icon.svg";
import { notifications } from "@mantine/notifications";
import axios from "axios";
import Cookies from "js-cookie";
import { IoArrowDownOutline } from "react-icons/io5";
import { useStrategy } from "./StrategyContext";
import authConfig from "@/Api/authConfig";
import ApiS3 from "@/Api/apiS3";

const theme = createTheme({
  cursorType: "pointer",
});

export default function StrategyEffectiveness() {
  const buttonTexts = ["PDF", "DOCX", "TXT"];
  const { setActiveTab, setSavedStrategy } = useStrategy();

  const icon = (
    <IoArrowDownOutline
      style={{ width: rem(12), height: rem(12), color: "#00C0A9" }}
    />
  );

  const form = useForm({
    initialValues: {
      name: "",
      progress: "0",
      controlDesign: "Adequate",
      controlOperatingEffectiveness: "0",
      category: "",
      objectives: "",
      relatedRisks: [],
      owner: "",
      targetImplementationDate: "",
      comment: "",
      tagColleagues: [],
      url: "",
    },
    // Remove any validation rules that make fields required
    validate: {},
  });

  const [relatedRisksOptions, setRelatedRisksOptions] = useState([]);
  const [tagColleaguesOptions, setTagColleaguesOptions] = useState([]);

  useEffect(() => {
    const tagColleaguesOptions = async () => {
      try {
        const response = await authConfig.get("/get_all_user_by_token");
        console.log("Full response", response.data);

        const users = response.data.companyUsers;

        if (Array.isArray(users)) {
          const userAuthId = users
            .filter((user) => !!user.userAuthId)
            .map((user) => ({
              value: user.userAuthId,
              label: user.userName,
            }));
          setTagColleaguesOptions(userAuthId);
        } else {
          console.error("Error:", response.data);
        }
      } catch (error) {
        console.error("Error tagColleaguesOptions:", error);
      }
    };

    tagColleaguesOptions();
  }, []);

  useEffect(() => {
    const fetchMasterData = async () => {
      setLoading(true);
      try {
        const token = Cookies.get("level_user_token");

        const response = await axios.get(
          "https://portals3-h3bjdkhtbghye2aa.uksouth-01.azurewebsites.net/risk",
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
            withCredentials: true,
          }
        );
        console.log(response);
        const relatedRisks = response.data.map((relatedRisk) => ({
          value: relatedRisk._id,
          label: relatedRisk.companyName,
        }));

        setRelatedRisksOptions(relatedRisks);

        if (relatedRisks.length > 0) {
          form.setFieldValue("relatedRisksId", relatedRisks[0].value);
        }
      } catch (err) {
        setError(err.message);
        setLoading(false);
      }
    };

    fetchMasterData();
  }, []);

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [file, setFile] = useState(null);
  const chooseFileRef = useRef();

  const validateAndConvert = (value) => {
    const num = Number(value);
    return isNaN(num) ? 0 : num;
  };

  const handleSave = async () => {
    const data = form.values;
    console.log("Data collected from form:", data);

    const formData = new FormData();
    // Only append fields that have values
    if (data.name) formData.append("name", data.name);
    if (data.objectives) formData.append("objectives", data.objectives);
    if (data.owner) formData.append("owner", data.owner);
    if (data.relatedRisks.length > 0)
      formData.append("relatedRisks", data.relatedRisks);
    if (data.tagColleagues.length > 0)
      formData.append("tagColleagues", JSON.stringify(data.tagColleagues));
    if (data.category) formData.append("category", data.category);
    formData.append("progress", validateAndConvert(data.progress));
    if (data.controlDesign)
      formData.append("controlDesign", data.controlDesign);
    formData.append(
      "controlOperatingEffectiveness",
      validateAndConvert(data.controlOperatingEffectiveness)
    );
    if (data.comment) formData.append("comment", data.comment);
    if (data.targetImplementationDate)
      formData.append(
        "targetImplementationDate",
        new Date(data.targetImplementationDate).toISOString()
      );
    if (data.url) formData.append("url", data.url);
    if (file) formData.append("file", file);

    console.log("FormData contents:");
    for (const [key, value] of formData.entries()) {
      console.log(`${key}: ${value}`);
    }

    const token = Cookies.get("level_user_token");
    setLoading(true);
    try {
      const response = await ApiS3.post("mitigation-strategy-types", formData);
      console.log("API Response:", response);

      setLoading(false);
      notifications.show({
        title: "Success",
        message: "Risk data saved successfully!",
        color: "green",
      });

      setActiveTab("mitigationstrategylibrary");
      setSavedStrategy(data);
    } catch (err) {
      console.error("Error saving data:", err);
      setError(err.message);
      setLoading(false);

      notifications.show({
        title: "Error",
        message: `Failed to save risk data: ${err.message}`,
        color: "red",
      });
    }
  };

  const [progressValue, setProgressValue] = useState(
    Number(form.values.progress)
  );

  const handleProgressChange = (event) => {
    const newProgress = event.target.value;
    form.setFieldValue("progress", newProgress);
    setProgressValue(Number(newProgress));
  };

  const handleUploadFile = () => {
    console.log("upload file....");
    chooseFileRef.current.click();
  };

  return (
    <div className="flex flex-col -mt-10">
      <div className="flex flex-row items-center gap-4 shadow-lg font-semibold text-[#000] bg-[#fff] py-2 px-6 mt-6 rounded-lg">
        <h1 className="text-[#00C0A9] font-semibold text-xl">
          Control Strategy:
        </h1>
        <TextInput
          {...form.getInputProps("name")}
          variant="unstyled"
          key={form.key("name")}
          type="text"
          id="name"
          placeholder="Renewable Energy Transition"
          size="lg"
          className="font-bold w-1/2"
          styles={{
            error: { display: "none" },
          }}
        />
      </div>

      <div className="grid grid-cols-4 gap-4 py-6">
        <div className="col-span-2 grid gap-4">
          <div className="grid grid-rows-2 grid-flow-col gap-4">
            <div className="row-span-2 rounded-lg shadow-lg p-6 bg-white">
              <p className="text-start text-[#000] font-bold mb-2">Progress</p>
              <div className="flex flex-col justify-center items-center">
                <SemiCircleProgress
                  {...form.getInputProps("progress")}
                  key={form.key("progress")}
                  id="progress"
                  type="number"
                  fillDirection="left-to-right"
                  orientation="up"
                  filledSegmentColor="#00C0A9"
                  size={200}
                  thickness={35}
                  value={progressValue}
                  label={`${progressValue}%`}
                />
                <hr className="w-24 mt-8" />
                <div className="flex flex-row gap-4 items-center">
                  <p className="text-center text-[#000] font-bold mt-2">
                    Implementation
                  </p>
                  <input
                    {...form.getInputProps("progress")}
                    key={form.key("progress")}
                    id="progress"
                    type="number"
                    value={form.values.progress}
                    onChange={handleProgressChange}
                    min="0"
                    max="100"
                    step="1"
                    style={{
                      marginTop: "10px",
                      width: "60px",
                      textAlign: "center",
                    }}
                  />
                </div>
              </div>
            </div>

            <div className="col-span-2 rounded-lg shadow-lg bg-white p-4">
              <p className="text-center text-[#000] font-bold">
                Control Design
              </p>
              <hr className="mt-4" />
              <MantineProvider theme={theme}>
                <Radio.Group
                  {...form.getInputProps("controlDesign")}
                  defaultValue="Adequate"
                >
                  <Group mt="xs">
                    <Radio
                      color="#1DC9A0"
                      value="Adequate"
                      label="Adequate"
                      variant="outline"
                    />
                    <Radio
                      color="#1DC9A0"
                      value="Inadequate"
                      label="Inadequate"
                      variant="outline"
                    />
                  </Group>
                </Radio.Group>
              </MantineProvider>
            </div>

            <div className="col-span-2 rounded-lg shadow-lg bg-white p-4">
              <p className="text-center text-[#000] font-bold">
                Control Operating effectiveness
              </p>
              <hr className="mt-2 mb-2" />
              <Slider
                {...form.getInputProps("controlOperatingEffectiveness")}
                key={form.key("controlOperatingEffectiveness")}
                id="controlOperatingEffectiveness"
                type="number"
                color="#00C0A9"
                size="lg"
                showLabelOnHover={false}
                marks={[
                  { value: 1, label: "1" },
                  { value: 2, label: "2" },
                  { value: 3, label: "3" },
                  { value: 4, label: "4" },
                  { value: 5, label: "5" },
                ]}
                thumbSize={12}
                min={1}
                max={5}
                step={1}
                styles={{
                  thumb: {
                    borderWidth: 4,
                    color: "#00C0A9",
                    display: "none",
                  },
                }}
              />
            </div>
          </div>

          <div className="grid gap-4 shadow-lg rounded-xl bg-white p-6">
            <Select
              {...form.getInputProps("category")}
              key={form.key("category")}
              id="category"
              name="category"
              label="Category"
              comboboxProps={{ withinPortal: true }}
              data={["Environmental", "Social", "Governance"]}
              placeholder="Select Category"
              radius="md"
              rightSectionPointerEvents="none"
              rightSection={icon}
              className="font-semibold"
            />

            <TextInput
              {...form.getInputProps("objectives")}
              key={form.key("objectives")}
              type="text"
              id="objectives"
              label="Objectives"
              placeholder="Enter Text"
              radius="md"
              className="font-semibold text-gray-700 w-full"
            />

            <MultiSelect
              {...form.getInputProps("relatedRisks")}
              label="Related Risks"
              placeholder=""
              data={relatedRisksOptions}
              radius="md"
              rightSectionPointerEvents="none"
              rightSection={icon}
              className="font-semibold"
            />

            <div className="grid grid-cols-2 gap-4">
              <TextInput
                {...form.getInputProps("owner")}
                key={form.key("owner")}
                type="text"
                id="owner"
                label="Owner"
                placeholder="Enter Text"
                radius="md"
                className="font-semibold text-gray-700 w-full"
              />
              <DateInput
                {...form.getInputProps("targetImplementationDate")}
                key={form.key("targetImplementationDate")}
                placeholder="DD/MM/YYYY"
                id="targetImplementationDate"
                name="targetImplementationDate"
                label="Target ImplementationDate"
                radius="md"
                className="font-semibold"
              />
            </div>

            <Textarea
              {...form.getInputProps("comment")}
              key={form.key("comment")}
              size="sm"
              label="Comments"
              autosize
              minRows={12}
              radius="md"
              className="font-semibold"
              placeholder="enter text..."
            />

            <MultiSelect
              {...form.getInputProps("tagColleagues")}
              key={form.key("tagColleagues")}
              type="text"
              id="tagColleagues"
              data={tagColleaguesOptions}
              label="Tag Colleagues"
              placeholder="@..."
              radius="md"
              className="font-semibold text-gray-700 w-full"
              rightSectionPointerEvents="none"
              rightSection={icon}
            />

            <TextInput
              {...form.getInputProps("url")}
              key={form.key("url")}
              type="text"
              id="url"
              label="URL"
              placeholder="https://"
              radius="md"
              className="font-semibold text-gray-700 w-full"
            />
          </div>
        </div>

        <div className="col-span-2 grid gap-4">
          <div className="rounded-lg shadow-lg bg-white h-3/4 p-4">
            <p className="text-start text-[#000] text-2xl font-bold">
              Evidence Attachments
            </p>
            <div className="flex flex-col mt-4 justify-center items-center px-20 py-6 rounded-2xl shadow-lg border-2 border-[#07838F]">
              <img
                src={fileAdd}
                alt="add a File"
                className="cursor-pointer w-24 h-24"
              />
              <input
                type="file"
                hidden
                ref={chooseFileRef}
                onChange={(e) => setFile(e.target.files[0])}
              />
              <p
                className="text-[#9C9C9C] underline mt-4"
                onClick={() => handleUploadFile()}
              >
                Click to upload
              </p>
              <div className="flex flex-row gap-2 mt-8">
                {buttonTexts.map((text, index) => (
                  <Button
                    onclick={handleUploadFile}
                    key={index}
                    text={text}
                    className={`p-2 font-normal shadow-none text-[#07838F] text-xs ${
                      text !== "> 10MB" ? "bg-[#07838F]" : "bg-[#fff] "
                    } bg-opacity-20 border-2 border-[#07838F] border-opacity-5`}
                  />
                ))}
              </div>
            </div>
          </div>

          <div className="rounded-lg border-2 border-[#fff] -mt-32 h-3/4 p-4">
            <div className="flex justify-between bg-[#fff] rounded-xl py-2 px-4">
              <p className="flex flex-col font-bold">
                document.pdf{" "}
                <span className="text-[#07838F] font-normal">
                  Upload complete
                </span>
              </p>
              <img src={uploadComplete} alt="upload complete" />
            </div>
            <div className="flex justify-between bg-[#fff] mt-2 rounded-xl py-2 px-4">
              <p className="flex flex-col font-bold">
                document.csv{" "}
                <span className="text-[#07838F] font-normal">
                  Upload complete
                </span>
              </p>
              <img src={uploadComplete} alt="upload complete" />
            </div>
          </div>
          <div className="flex justify-end gap-4">
            <button className="border-[#00C0A9] border-2 h-10 rounded-lg text-[#00C0A9] px-4 font-bold">
              Save as Draft
            </button>
            <button
              onClick={handleSave}
              className="bg-[#00C0A9] h-10 rounded-lg text-[#fff] px-4 font-bold"
            >
              Submit
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
