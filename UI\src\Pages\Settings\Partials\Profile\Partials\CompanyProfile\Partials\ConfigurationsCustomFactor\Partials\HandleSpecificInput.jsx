import { Button, Input, Select } from "@mantine/core";
import { useState } from "react";
import { BiTrash } from "react-icons/bi";
import { RiExchangeFundsLine } from "react-icons/ri";


const HandleSpecificInput = ({
  handleActivityList,
  handleValueChange,
  handleRemoveItem,
  idx,
  object,
  uniqueKeysList,
  uniqueValueList,
  target,
}) => {
  // handle is select or normal text
  const [activiyType, setactiviyType] = useState("select");

  return (
    <div
      key={idx}
      className="mt-4 pb-2 px-3 border-t-2"
    >
      <div className="flex items-center justify-end gap-2 mb-3 pt-2">
      <Button
          className="bg-red-500/60 lg:text-sm text-xs  hover:bg-red-400 py-0 px-2 "
          onClick={() => handleRemoveItem(idx, target)}
        >
          <BiTrash />
        </Button>
      <Button
        onClick={() => {
          if (activiyType == "text") setactiviyType("select");
          if (activiyType !== "text") setactiviyType("text");
        }}
        className="bg-primary/30 hover:bg-primary lg:text-sm text-xs flex gap-2 items-center p-0 px-2 text-primary"
      >
        <RiExchangeFundsLine className="mr-1" />{" "}
        <span>

        {activiyType == "select" ? "Change to Text" : "Change to Select"}
        </span>
      </Button>
      </div>
      {/* select */}
        {activiyType == "select" ? (
          <div className="grid lg:grid-cols-2 gap-4">
            <Select
            nothingFoundMessage="Nothing found..."
            className="lg:text-xl text-xs"
              label={`Your current ${target} keys`}
              placeholder={`${
                uniqueKeysList.length > 0
                  ? `Select ${target} key`
                  : `No ${target} found`
              }`}
              data={uniqueKeysList || []}
              onChange={(e) => handleActivityList(e, idx, target)}
              searchable
            />
            <Select
            nothingFoundMessage="Nothing found..."
            className="lg:text-xl text-xs"
              label={`Your current ${target} values`}
              placeholder={`${
                uniqueValueList.length > 0
                  ? `Select ${target} value`
                  : `No ${target} found`
              }`}
              data={uniqueValueList || []}
              onChange={(e) => handleValueChange(e, idx, target)}
              searchable
            />
          </div>
        ) : (
          <div className="grid grid-cols-2 place-items-center gap-4">
            {/* user text */}
            <Select
            nothingFoundMessage="Nothing found..."
            className="lg:text-xl text-xs"
              label={`Your current ${target} keys`}
              placeholder={`${
                uniqueKeysList.length > 0
                  ? `Select ${target} key`
                  : `No ${target} found`
              }`}
              data={uniqueKeysList || []}
              onChange={(e) => handleActivityList(e, idx, target)}
              searchable
            />
            <Input.Wrapper label={"value"} className="mt-1">
              <Input
                value={Object.values(object)[0]}
                onChange={(e) => handleValueChange(e.target.value, idx, target)}
                className="w-full lg:text-xl text-xs"
              />
            </Input.Wrapper>
          </div>
        )}

    </div>
  );
};

export default HandleSpecificInput;
