
import { Checkbox, ScrollArea, Table, rem } from "@mantine/core";
import { useState } from "react";

import { MdArrowDownward } from "react-icons/md";

const head = [
  "Type of greenwashing",
  "Description",
  "GreenwashingRisk Area",
  "Potential Risks",
  "Examples",
  "Likelihood",
];


const data = [
  {
    id: "1",
    type: "Regular text column",
    description: "Regular text column",
    riskArea: "Regular text column",
    potenialRisk: "Regular text column",
    example: "Regular text column",
    hood: "Regular text column",
  },
  {
    id: "2",
    type: "Regular text column",
    description: "Regular text column",
    riskArea: "Regular text column",
    potenialRisk: "Regular text column",
    example: "Regular text column",
    hood: "Regular text column",
  },
  {
    id: "3",
    type: "Regular text column",
    description: "Regular text column",
    riskArea: "Regular text column",
    potenialRisk: "Regular text column",
    example: "Regular text column",
    hood: "Regular text column",
  },
  {
    id: "4",
    type: "Regular text column",
    description: "Regular text column",
    riskArea: "Regular text column",
    potenialRisk: "Regular text column",
    example: "Regular text column",
    hood: "Regular text column",
  },
];

export default function GreenRiskAssessmentTable() {
  const [selection, setSelection] = useState(["1"]);
  const toggleRow = (id) =>
    setSelection((current) =>
      current.includes(id)
        ? current.filter((item) => item !== id)
        : [...current, id]
    );
  const toggleAll = () =>
    setSelection((current) =>
      current.length === data.length ? [] : data.map((item) => item.id)
    );

  const rows = data.map((item) => {
    return (
      <Table.Tr
        key={item.id}
        className={`bg-white text-sm text-[#626364] text-center`}
      >
        <Table.Td>
          <Checkbox
            checked={selection.includes(item.id)}
            onChange={() => toggleRow(item.id)}
            color="#00C0A9"
          />
        </Table.Td>
        <Table.Td>{item.type}</Table.Td>
        <Table.Td>{item.description}</Table.Td>
        <Table.Td>{item.riskArea}</Table.Td>
        <Table.Td>{item.potenialRisk}</Table.Td>
        <Table.Td>{item.example}</Table.Td>
        <Table.Td>{item.hood}</Table.Td>
      </Table.Tr>
    );
  });

  return (
    <div className="bg-[#F7F4F4] mt-7">
      <ScrollArea>
        <Table miw={800} verticalSpacing="sm">
          <Table.Thead className="border-b-2 bg-white pb-6 text-base text-center">
            <Table.Tr>
              <Table.Th style={{ width: rem(40) }}>
                <Checkbox
                  onChange={toggleAll}
                  checked={selection.length === data.length}
                  indeterminate={
                    selection.length > 0 && selection.length !== data.length
                  }
                  color="#00C0A9"
                />
              </Table.Th>

              {head.map((el, i) => (
                <Table.Th key={i} className="text-secondary-500">
                  <div className="flex items-center justify-center gap-2">
                    <span className="font-medium">{el}</span>
                    <MdArrowDownward className="text-secondary-300" />
                  </div>
                </Table.Th>
              ))}
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>{rows}</Table.Tbody>
        </Table>
      </ScrollArea>
    </div>
  );
}
