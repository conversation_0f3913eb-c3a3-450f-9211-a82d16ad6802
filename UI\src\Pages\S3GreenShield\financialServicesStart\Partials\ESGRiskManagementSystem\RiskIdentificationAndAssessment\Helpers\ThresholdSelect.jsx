import { useState } from "react";
import { MdKeyboardArrowDown } from "react-icons/md";

const ThresholdBadge = ({ level }) => {
  // We'll still need inline styles for the dynamic colors since they're custom values
  return (
    <div
      className="flex items-center gap-2 px-4 py-1 rounded-full"
      style={{ backgroundColor: level.bgColor }}
    >
      <div
        className="w-2 h-2 rounded-full"
        style={{ backgroundColor: level.color }}
      />
      <span
        className="font-semibold uppercase text-sm"
        style={{ color: level.color }}
      >
        {level.label}
      </span>
    </div>
  );
};

export const ThresholdSelector = ({
  label = "Threshold Level",
  value = "",
  onChange,
  levels,
  className,
  type,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const selectedLevel =
    levels.find((level) => level.value === value) || levels[0];

  const toggleDropdown = () => setIsOpen(!isOpen);

  const handleSelect = (levelValue) => {
    onChange(levelValue);
    setIsOpen(false);
  };

  const closeAndReset = () => {
    setIsOpen(false);
    onChange("");
  };


  return (
    <div className={`${className} relative`}>
      {label && (
        <label className="block text-sm font-medium mb-1">{label}</label>
      )}

      <button
        disabled={type === "view"}
        className={`relative w-full border-[1px] border-gray-300 rounded-md ${type === "view" ? "cursor-not-allowed" : "cursor-pointer bg-white"}`}
        onClick={toggleDropdown}
      >
        <div className="flex items-center justify-between px-3 py-1 relative h-[35px]">
          {value.length == 0 ? "" : <ThresholdBadge level={selectedLevel} />}
          <MdKeyboardArrowDown
            className={`absolute right-0 w-5 h-5 text-gray-400 transition-transform duration-200 ${
              isOpen && "transform rotate-180"
            }`}
          />
        </div>
      </button>

      {isOpen && (
        <div className="absolute mt-1 w-full z-10 bg-white border border-gray-200 rounded-lg shadow-lg py-2 max-h-60 overflow-auto">
          <button
            title="close"
            onClick={() => closeAndReset()}
            className="ml-1 w-5 h-5 bg-red-400 rounded-full full-center"
          >
            X
          </button>

          {levels.map((level) => (
            <div
              key={level.value}
              className="px-3 py-2 hover:bg-gray-100 cursor-pointer"
              onClick={() => handleSelect(level.value)}
            >
              <ThresholdBadge level={level} />
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
