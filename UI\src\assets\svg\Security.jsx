function Security() {
  return (
    <svg
      width="22"
      height="24"
      viewBox="0 0 22 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11 0C9.96501 0 8.23551 0.3975 6.60801 0.84C4.94301 1.29 3.26451 1.8225 2.27751 2.145C1.86485 2.28128 1.49899 2.5312 1.22198 2.86605C0.944973 3.20091 0.768039 3.60711 0.711508 4.038C-0.182492 10.7535 1.89201 15.7305 4.40901 19.023C5.47677 20.4311 6.74938 21.6713 8.18451 22.7025C8.76351 23.112 9.30051 23.4255 9.75651 23.64C10.1765 23.838 10.628 24 11 24C11.372 24 11.822 23.838 12.2435 23.64C12.7938 23.3739 13.3198 23.0602 13.8155 22.7025C15.2506 21.6713 16.5232 20.4311 17.591 19.023C20.108 15.7305 22.1825 10.7535 21.2885 4.038C21.2322 3.60684 21.0554 3.20032 20.7784 2.86518C20.5014 2.53004 20.1354 2.27989 19.7225 2.1435C18.2896 1.67401 16.8457 1.23889 15.392 0.8385C13.7645 0.399 12.035 0 11 0ZM11 7.5C11.5314 7.49921 12.0459 7.6865 12.4524 8.02869C12.8589 8.37089 13.1311 8.84591 13.221 9.36962C13.3108 9.89333 13.2124 10.4319 12.9432 10.89C12.6739 11.3481 12.2513 11.6962 11.75 11.8725L12.3275 14.8575C12.3485 14.9661 12.3453 15.0779 12.318 15.1851C12.2907 15.2923 12.24 15.3921 12.1696 15.4774C12.0993 15.5627 12.0109 15.6313 11.9108 15.6785C11.8108 15.7256 11.7016 15.75 11.591 15.75H10.409C10.2986 15.7498 10.1895 15.7252 10.0897 15.678C9.9898 15.6307 9.90162 15.562 9.8314 15.4768C9.76118 15.3915 9.71066 15.2918 9.68345 15.1847C9.65625 15.0777 9.65302 14.9659 9.67401 14.8575L10.25 11.8725C9.74876 11.6962 9.32609 11.3481 9.05686 10.89C8.78763 10.4319 8.68921 9.89333 8.77904 9.36962C8.86887 8.84591 9.14115 8.37089 9.54765 8.02869C9.95416 7.6865 10.4686 7.49921 11 7.5Z"
        fill="#07838F"
      />
    </svg>
  );
}

export default Security;
