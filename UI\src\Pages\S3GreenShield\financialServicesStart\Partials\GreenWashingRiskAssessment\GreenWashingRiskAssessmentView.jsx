import GreenRiskAssessmentTable from "./Components/GreenRiskAssessmentTable";
import { useState } from "react";

const GreenWashingRiskAssessmentView = () => {
  const [addCategory, setAddCategory] = useState(false);
  const [refreshTable, setRefreshTable] = useState(false);

  return (
    <>
      <GreenRiskAssessmentTable
        addCategory={addCategory}
        refreshTable={refreshTable}
        setRefreshTable={setRefreshTable}
      />
    </>
  );
};

export default GreenWashingRiskAssessmentView;
