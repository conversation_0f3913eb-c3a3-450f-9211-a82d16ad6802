import PublicSupplyChainConfig from "@/Api/PublicSupplyChainConfig";
import Loading from "@/Components/Loading";
import { Button, Select, Tooltip } from "@mantine/core";
import { showNotification } from "@mantine/notifications";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { FiUpload } from "react-icons/fi";


export default function PublicDownloadTemplate() {
  const { t } = useTranslation();
  const [SelectedTemplate, setSelectedTemplate] = useState();
  const [loading, setLoading] = useState(false);
  const downloadSelectedTemplate = async () => {
    setLoading(true);
    try {
      setLoading(true);

      const { data } = await PublicSupplyChainConfig.get(
        "/batch_inputs/supplier/get_template", {
        headers: {
          "template-name": SelectedTemplate,
        },
        responseType: "arraybuffer",
      });

      const blob = new Blob([data], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `${SelectedTemplate}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      setLoading(false);
      showNotification({
        message: "File downloaded successfully",
        color: "green",
      });
    } catch (error) {
      setLoading(false);
      showNotification({
        message: "File not downloaded successfully",
        color: "red",
      });
      console.error(error);
    }
  };
  return (
    <>
    <div className="md:flex items-center justify-between mb-3">
      <p className="font-semibold font-inter text-[16px] leading-[24px] capitalize">
        <span className="text-secondary-300 capitalize me-2">
          {t("step1 :")}
        </span>
        {t("Select and download the template.")}
      </p>
      <div className="md:flex justify-center items-center 1/2">
        <Select
          id="Template"
          name="Template"
          data={[
            "Refrigerants",
            "Heat_and_Steam",
            "Other_Stationary",
            "Purchased_Electricity",
            "Company_Vehicles_Distance_based",
            "Company_Vehicles_Fuel_based",
            "Natural_Gas",
          ]}
          placeholder="Select the template"
          className="md:me-10 w-60 md:w-72 mx-auto"
          radius={10}
          value={SelectedTemplate}
          onChange={setSelectedTemplate}
        />
        <Tooltip
          multiline
          w={220}
          radius={"md"}
          withArrow
          transitionProps={{ duration: 200 }}
          className={`${SelectedTemplate ? "hidden" : ""}`}
          label={
            !SelectedTemplate && (
              <span className="capitalize flex justify-center">
                please Select Template first
              </span>
            )
          }
        >
          <Button
            className={` px-[25px] bg-transparent flex justify-center items-center gap-x-2  mx-auto md:mx-0 mt-5 md:mt-0 rounded-xl shadow-md border-2   ${!SelectedTemplate
                ? "cursor-not-allowed opacity-20 border-slate-800 text-slate-800 "
                : "border-secondary-300 text-secondary-300 hover:bg-white hover:text-secondary-300"
              }`}
            onClick={!loading && downloadSelectedTemplate}
            disabled={!SelectedTemplate}
            size="md"
          >
            {loading ? (
              <Loading />
            ) : (
              <>
                <span className="rotate-180">
                  <FiUpload className="ms-2" />
                </span>
                <span>{t("downloadTemplate.downloadTemplate")}</span>
              </>
            )}
          </Button>
        </Tooltip>
      </div>
    </div>
    <hr className="h-[2px] bg-gray-300  mx-auto" />
  </>
  );
}
