import MainLayout from "@/Layout/MainLayout";
import AskedQuestions from "./Components/AskedQuestions";
import Guides from "./Components/Guides";
import SupportRequest from "./Components/SupportRequest";
import ContactUs from "./Components/ContactUs";
import { IoMdHome } from "react-icons/io";


const SupportView = () => {
  return (
    <MainLayout
        navbarTitle="Support"
        breadcrumbItems={[
            { title: <IoMdHome size={20}/>, href: "/get-started" },
            { title: "Support", href: "#" },
        ]}
    >
      <h3 className="text-center mb-6 text-3xl font-semibold">
        How can we help you?
      </h3>

      <section className="grid lg:grid-cols-2 gap-5">
        <AskedQuestions />

        <Guides />

        <SupportRequest />

        <ContactUs />
      </section>
    </MainLayout>
  );
};

export default SupportView;
