const GreenRiskRow = ({ risk,filters }) => {
  return (
    <tr className="bg-white text-sm text-[#626364] ">
    {filters.includes("Risk Id") && <td style={{minWidth: 100}}>{risk.riskId || "N/A"}</td>}

    {/* {filters.includes("Risk Name") && <td>{risk.riskName || "N/A"}</td>} */}

    {filters.includes("Type Of Greenwashing") && (
      <td style={{minWidth: 250}}>{risk.greenwashingTypeId?.name || "N/A"}</td>
    )}
    
    {filters.includes("Description") && (
      <td style={{minWidth: 250}}>{risk.riskDescription || "N/A"}</td>
    )}
    
    {filters.includes("GreenWashing Risk Area") && (
      <td style={{minWidth: 250}}>{risk.greenwashingRiskArea || "N/A"}</td>
    )}
    
    {filters.includes("Likelihood") && (
      <td style={{minWidth: 250}}>{risk.likelihoodId?.name || "N/A"}</td>
    )}
    
    {filters.includes("Impact") && (
      <td style={{minWidth: 250}}>{risk.impactId?.name || "N/A"}</td>
    )}
{/*     
    {filters.includes("RiskOwner") && (
      <td>{risk.ownerId || "N/A"}</td>
    )} */}
    
    {filters.includes("Category") && (
      <td style={{minWidth: 250}}>{risk.esgCategoryId?.name || "N/A"}</td>
    )}
    
    {filters.includes("inherent Risk Score") && (
      <td style={{minWidth: 250}}>
        {risk.inherentRiskScore || "N/A"}
      </td>
    )}
    
    {filters.includes("Current Control Effectiveness") && (
      <td style={{minWidth: 250}}>
        {risk.currentControlEffectiveness || "N/A"}
      </td>
    )}
    
    {filters.includes("Residual Risk Score") && (
      <td style={{minWidth: 250}}>
        {risk.residualRiskScore || "N/A"}
      </td>
    )}

    
    {filters.includes("Created At") && (
      <td style={{minWidth: 250}}>
        {risk.createdAt?.split("T")[0] || "N/A"}
      </td>
    )}
    
  </tr>
  );
};

export default GreenRiskRow;
