import ApiProfile from "@/Api/apiProfileConfig";
import Loading from "@/Components/Loading";
import { Button, ScrollArea, Table } from "@mantine/core";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

/**
 * Recursively toggles the `enabled` property for a nested item.
 * @param {object} obj - The object to search in.
 * @param {string[]} keysPath - Array of keys representing the path to the item.
 * @returns {object} The updated object.
 */
function toggleNestedEnabled(obj, keysPath) {
  if (!obj || keysPath.length === 0) return obj;

  const [currentKey, ...restKeys] = keysPath;

  // If we're at the last key, toggle enabled
  if (restKeys.length === 0) {
    // If it's a direct property
    if (obj[currentKey]) {
      obj[currentKey].enabled = !obj[currentKey].enabled;
    } else if (obj.children) {
      // If it's inside children array
      const child = obj.children.find((c) => c.componentName === currentKey);
      if (child) child.enabled = !child.enabled;
    }
    return obj;
  }

  // Otherwise, keep traversing
  if (obj[currentKey]) {
    obj[currentKey] = toggleNestedEnabled(obj[currentKey], restKeys);
  } else if (obj.children) {
    const child = obj.children.find((c) => c.componentName === currentKey);
    if (child) {
      toggleNestedEnabled(child, restKeys);
    }
  }
  return obj;
}

/**
 * Recursive component to render nested items dynamically
 */
const NestedItem = ({
  section,
  item,
  keysPath,
  changeData,
  handleCheckboxChange,
  t,
  depth = 0,
}) => {
  const key = item.componentName;
  const isParent = item.children && item.children.length > 0;
  const uniqueId = `Checkbox-${section}-${keysPath.join("-")}`;

  // Find the current item's enabled state in changeData
  let isEnabled = false;
  let current = changeData[section];
  for (const pk of keysPath) {
    if (!current) break;
    if (current[pk]) {
      current = current[pk];
    } else if (current.children) {
      current = current.children.find((child) => child.componentName === pk);
    }
  }
  if (current && typeof current.enabled !== "undefined") {
    isEnabled = !!current.enabled;
  }

  return (
    <div
      className={isParent ? "mb-2" : "flex items-center gap-2 mb-2"}
      style={{ paddingLeft: `${depth * 1.5}rem` }}
    >
      {isParent ? (
        <div className="font-bold">{t(key)}</div>
      ) : (
        <>
          <input
            type="Checkbox"
            checked={isEnabled}
            onChange={() => handleCheckboxChange(section, [...keysPath])}
            className="cursor-pointer"
            id={uniqueId}
          />
          <label htmlFor={uniqueId} className="cursor-pointer">
            {t(key)}
          </label>
        </>
      )}
      {item.children && (
        <div>
          {item.children.map((child) => (
            <NestedItem
              key={child.componentName}
              section={section}
              item={child}
              keysPath={[...keysPath, child.componentName]}
              changeData={changeData}
              handleCheckboxChange={handleCheckboxChange}
              t={t}
              depth={depth + 1}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default function ViewAccessTable({
  data,
  selectedCompanyId,
  close,
  getAllCompanyAccounts,
}) {
  const [changeData, setChangeData] = useState({});
  const [loading, setLoading] = useState(false);
  const { t } = useTranslation();

  // Initialize state with deep copy of data
  useEffect(() => {
    setChangeData(JSON.parse(JSON.stringify(data)));
  }, [data]);

  // Handle Checkbox change at any nesting level
  const handleCheckboxChange = (section, keysPath) => {
    setChangeData((prevData) => {
      const newData = JSON.parse(JSON.stringify(prevData));
      toggleNestedEnabled(newData[section], keysPath);
      return newData;
    });
  };

  // Submit updated access data to API
  const companyCustomAccess = async (updatedData, Id) => {
    const value = { company_id: Id, access: updatedData };
    try {
      setLoading(true);
      await ApiProfile.put("/admin/company-custom-access", value);
      setLoading(false);
      close();
      getAllCompanyAccounts();
    } catch (error) {
      setLoading(false);
      console.error("API error:", error);
    }
  };

  return (
    <div className="p-5 bg-white rounded-lg mt-7">
      <ScrollArea>
        <Table miw={800} verticalSpacing="sm">
          <Table.Thead className="pb-6 text-base font-bold text-center border-b-2 border-primary text-primary">
            <Table.Tr>
              {Object.keys(data).map((section) => (
                <Table.Th key={section} className="text-start">
                  {t(section)}
                </Table.Th>
              ))}
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            <Table.Tr className="text-sm font-bold text-[#626364] text-center">
              {Object.entries(data).map(([section, values]) => (
                <Table.Td key={section} className="section-container">
                  {Object.entries(values).map(([key, item]) => (
                    <NestedItem
                      key={key}
                      section={section}
                      item={item}
                      keysPath={[key]}
                      changeData={changeData}
                      handleCheckboxChange={handleCheckboxChange}
                      t={t}
                      depth={0}
                    />
                  ))}
                </Table.Td>
              ))}
            </Table.Tr>
          </Table.Tbody>
        </Table>
      </ScrollArea>
      <Button
        className="bg-primary hover:bg-primary ms-auto block"
        size="md"
        radius={10}
        disabled={loading}
        onClick={() => companyCustomAccess(changeData, selectedCompanyId)}
      >
        {loading ? <Loading /> : t("Submit")}
      </Button>
    </div>
  );
}
