import S2Layout from "@/Layout/S2Layout";
import { useEffect, useState } from "react";

// icons & imgs
import reportImg from "@/assets/images/mountain-logo.png";

import Share from "./Components/Share";
import ViewPDF from "./Components/ViewPDF";

import { YearPickerInput } from "@mantine/dates";
import { useTranslation } from "react-i18next";
import { IoIosArrowDown, IoMdClose } from "react-icons/io";
import { btnStyle, linkStyle } from "./Components/StaticData";

import ApiS2 from "@/Api/apiS2Config";
import { defaultLayoutPlugin } from "@react-pdf-viewer/default-layout";
import dayjs from "dayjs";
import { FiDownload } from "react-icons/fi";
import { HiOutlineDocumentReport } from "react-icons/hi";
import Swal from "sweetalert2";
import ReportHistory from "./Components/ReportHistory";
import { useDisclosure } from "@mantine/hooks";
import { driver } from "driver.js";
import "driver.js/dist/driver.css";
import "@/assets/css/index.css";
import { FaQuestionCircle } from "react-icons/fa";

const ReportView = () => {
  const { t } = useTranslation();
  const [opened, { open, close }] = useDisclosure(false);
  const [year, setYear] = useState(null);
  const [data, setData] = useState(null);
  const [ReportHistoryData, setReportHistoryData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const defaultLayoutPluginInstance = defaultLayoutPlugin();
  const activeTab = "Report";
  const [elementsReady, setElementsReady] = useState(false);

  const generateReport = async (year) => {
    const value = {
      reporting_year: dayjs(year).format("YYYY"),
    };

    Swal.fire({
      title: "Please wait...",
      text: "Generating report data...",
      icon: "info",
      allowOutsideClick: false,
      allowEscapeKey: false,
      didOpen: () => {
        Swal.showLoading();
      },
    });

    try {
      const { data } = await ApiS2.post("/admin/generate_report", value);
      setData(data);

      Swal.fire({
        title: "Success!",
        text: "Report Generated successfully",
        icon: "success",
        timer: 2000,
        showConfirmButton: false,
      });

      // Start second guide after 3 seconds
      if (
        checkElementsExist(true) &&
        !localStorage.getItem("hasSeenSecondGuide")
      ) {
        setTimeout(() => {
          startSecondGuide();
        }, 3000);
      }
    } catch (error) {
      Swal.close();
      Swal.fire({
        title: "Error!",
        text: error.response?.data?.message || "An error occurred",
        icon: "error",
        confirmButtonText: "OK",
      });
      setError(error.response?.data?.message || "Unknown error");
    }
  };

  const getReportHistory = async () => {
    try {
      setLoading(true);
      const { data } = await ApiS2.get("/admin/get_report_history");
      setReportHistoryData(data);
    } catch (error) {
      console.error("Error fetching report history:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getReportHistory();
  }, []);

  const getGuideSteps = () => [
    {
      element: ".The-Emissions-Report-section",
      popover: {
        title: t(
          "The Emissions Report section enables you to generate, view, share, and download emissions reports for a selected reporting year."
        ),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".The-Emissions-Report-section-Additional",
      popover: {
        title: t(
          "Additionally, you can access the history of previously generated reports at any time."
        ),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".The-Emissions-Report-section-Select-Reporting-Year",
      popover: {
        title: t("Select Reporting Year"),
        description: t("Choose the desired reporting year"),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".The-Emissions-Report-section-Processess",
      popover: {
        title: t(
          "The system processes the emissions data and compiles it into a full report based on the selected year."
        ),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".The-Emissions-Report-note1",
      popover: {
        title: t(
          "NOTE 1"
        ),
        description: t(
          "Generating reports may take a few seconds depending on the data size."
        ),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".The-Emissions-Report-note2",
      popover: {
        title: t(
          "NOTE 2"
        ),
        description: t(
          "Make sure all relevant data for the selected year is collected and reviewed before generating the final report."
        ),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".The-Emissions-Report-note3",
      popover: {
        title: t(
          "NOTE 3"
        ),
        description: t(
          "Use the Share feature to collaborate efficiently with stakeholders."
        ),
        side: "left",
        align: "center",
      },
    },
  ];

  const getSecondGuideSteps = () => [
    {
      element: ".The-Emissions-View-Report",
      popover: {
        title: t("View Report"),
        description: t("Open and view the generated report."),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".The-Emissions-Share-Report",
      popover: {
        title: t("Share Report"),
        description: t("Share the report link with others"),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".The-Emissions-Download-Report",
      popover: {
        title: t("Download Report"),
        description: t("Download the report for offline use."),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".Have-look-Report-History",
      popover: {
        title: t("Have a look on Report History"),
        description: t("You can revisit or  view  any historical reports at any time without the need to regenerate."),
        side: "left",
        align: "center",
      },
    },
  ];

  const checkElementsExist = (isSecondGuide = false) => {
    const steps = isSecondGuide ? getSecondGuideSteps() : getGuideSteps();
    const allElementsExist = steps.every((step) => {
      const exists = document.querySelector(step.element);
      return exists;
    });
    return allElementsExist;
  };

  useEffect(() => {
    if (activeTab === "Report") {
      const observer = new MutationObserver(() => {
        if (checkElementsExist(data)) {
          setElementsReady(true);
          observer.disconnect();
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true,
      });

      return () => observer.disconnect();
    }
  }, [activeTab, data]);

  const startGuide = () => {
    if (!checkElementsExist()) {
      return;
    }

    const driverObj = driver({
      showProgress: true,
      popoverClass: "my-custom-popover-class",
      nextBtnText: "Next",
      prevBtnText: "Back",
      doneBtnText: "Done",
      overlayColor: "rgba(0, 0, 0, 0)",
      progressText: "Step {{current}} of {{total}}",
      steps: getGuideSteps(),
      onDestroyStarted: () => {
        localStorage.setItem("hasSeenReportGuide", "true");
        driverObj.destroy();
      },
    });
    driverObj.drive();
  };

  const startSecondGuide = () => {
    if (!checkElementsExist(true)) {
      return;
    }

    const driverObj = driver({
      showProgress: true,
      popoverClass: "my-custom-popover-class",
      nextBtnText: "Next",
      prevBtnText: "Back",
      doneBtnText: "Done",
      progressText: "Step {{current}} of {{total}}",
      steps: getSecondGuideSteps(),
      onDestroyStarted: () => {
        localStorage.setItem("hasSeenSecondGuide", "true");
        driverObj.destroy();
      },
    });
    driverObj.drive();
  };

  useEffect(() => {
    if (!localStorage.getItem("hasSeenReportGuide") && checkElementsExist()) {
      startGuide();
    }

    return () => {
      const driverObj = driver();
      if (driverObj.isActive()) {
        driverObj.destroy();
      }
    };
  }, []);

  const handleGuideButtonClick = () => {
    if (data) {
      startSecondGuide();
    } else {
      startGuide();
    }
  };

  return (
    <>
      <div className="">
        <div
          onClick={handleGuideButtonClick}
          style={{
            position: "fixed",
            bottom: 20,
            right: 20,
            cursor: "pointer",
            zIndex: 1000,
          }}
        >
          <div className="h-12 w-12 flex items-center justify-center bg-gradient-to-tr from-[#2C5A8C] via-[#1C889C] to-[#13B1A8] p-1 cursor-pointer rounded-full">
            <FaQuestionCircle
              size={34}
              color="#ffffff"
              className="mx-auto cursor-pointer"
            />
          </div>
        </div>
        <div className="bg-white p-5 my-5 rounded-lg md:flex justify-between items-center">
          <p className="md:flex items-start text-2xl font-bold The-Emissions-Report-section The-Emissions-Report-note1 The-Emissions-Report-note2 The-Emissions-Report-note3">
            Emissions Report
          </p>
          <span className="text-xs text ml-1 bg-[#e09432] py-1 px-2 rounded-full text-white md:hidden">
            Setup Required
          </span>
          <div className="The-Emissions-Report-section-Select-Reporting-Year bg-secondary-300 overflow-hidden rounded-xl border-[1px] text-white mt-5 lg:mt-0">
            <YearPickerInput
              w={"100%"}
              bg={"#00C0A9"}
              radius={"sm"}
              variant="unstyled"
              size="lg"
              placeholder={t("reportingYear")}
              classNames={{
                placeholder: "text-white ps-3",
                input: "text-white",
              }}
              value={year}
              onChange={(value) => {
                setYear(value);
                if (value) {
                  generateReport(value);
                }
                setData(null);
                setError(null);
              }}
              id="year"
              rightSection={
                !year ? (
                  <label
                    htmlFor="year"
                    className="cursor-pointer w-full h-full text-center flex items-center justify-center"
                  >
                    <IoIosArrowDown
                      size={14}
                      className="text-white cursor-pointer"
                    />
                  </label>
                ) : (
                  <IoMdClose
                    size={18}
                    className="text-white cursor-pointer"
                    onClick={() => {
                      setData(null);
                      setYear(null);
                      setError(null);
                    }}
                  />
                )
              }
            />
          </div>
        </div>
        <div className="lg:flex justify-between">
          <div className="flex justify-center mb-5">
            <a
              className={`${linkStyle} cursor-pointer The-Emissions-Report-section-Additional Have-look-Report-History`}
              onClick={open}
            >
              Report History
              <span>
                <HiOutlineDocumentReport size={20} />
              </span>
            </a>
          </div>
          {data ? (
            <div className="report-page flex justify-end gap-y-3 flex-wrap mb-[38px]">
              <div className="flex flex-wrap items-center justify-center gap-6">
                <div className="The-Emissions-View-Report">
                  <ViewPDF
                    btnStyle={btnStyle}
                    pdfUrl={data?.report_url}
                    text={"View Report"}
                  />
                </div>
                <div className="The-Emissions-Share-Report">
                  <Share link={data?.report_url} />
                </div>
                <div className="The-Emissions-Download-Report">
                  <a className={linkStyle} href={data?.report_url} download>
                    Download Report
                    <span>
                      <FiDownload className="text-lg" />
                    </span>
                  </a>
                </div>
              </div>
            </div>
          ) : (
            error && <p className="text-red-500 text-center">{error}</p>
          )}
        </div>
        <span className="The-Emissions-Report-section-Processess inline-block text-center w-1 h-1 "></span>
        <div className="flex items-center justify-center w-full mx-auto mt-32 img-wrapper max-w-7xl">
          <img src={reportImg} className="object-center w-full" alt="Report" />
        </div>
      </div>
      <ReportHistory
        opened={opened}
        onClose={close}
        data={ReportHistoryData}
        loading={loading}
      />
    </>
  );
};

export default ReportView;
