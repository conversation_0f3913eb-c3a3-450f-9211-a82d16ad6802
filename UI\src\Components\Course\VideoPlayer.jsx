import { CourseContext } from "@/Contexts/CourseContext";
import { useContext } from "react";
import ReactPlayer from "react-player";

function VideoPlayer() {
  const { currentVideo, handleVideoPlayer } = useContext(CourseContext);
  return (
    <ReactPlayer
      url={currentVideo?.lectureUrl || ""}
      controls={true}
      width="100%"
      height={500}
      playing={true}
      muted={true}
      onEnded={() => handleVideoPlayer(currentVideo?._id, "NEXT")}
    />
  );
}

export default VideoPlayer;
