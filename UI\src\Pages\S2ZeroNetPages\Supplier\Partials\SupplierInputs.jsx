import { useTranslation } from "react-i18next";
import SupplierInputTable from "./SupplierInputsPartials/SupplierInputTable";
import SupplierInputsGroup from "./SupplierInputsPartials/SupplierInputsGroup";
import { MdOutlineFileUpload } from "react-icons/md";
import { Button } from "@mantine/core";
import ApiS2 from "@/Api/apiS2Config";
import { useEffect, useState } from "react";

const SupplierInputs = () => {
  const { t } = useTranslation();
  const [data, setData] = useState();
  const [loading, setLoading] = useState(false);
  const getTableData = async () => {
    try {
      setLoading(true);
      const { data } = await ApiS2.get("/suppliers/list-company-suppliers");
      setLoading(false);
      const reversedData = data ? [...data].reverse() : [];
      setData(data ? reversedData : []);
      // //console.log(data);
    } catch (error) {
      setLoading(false);
      //console.log(error);
    }
  };
  useEffect(() => {
    getTableData();
  }, []);
  return <>
    <div className="SupplierInputs font-inter">
      <SupplierInputsGroup getTableData={getTableData}/>
      <div className="md:flex items-center justify-between p-3 mt-5 bg-[#e0e9ea] border-2 border-primary rounded-lg shadow-md">
          <div>
            <h1 className="font-bold text-lg text-primary">
              {t("Recent upload manual input data")}
            </h1>
          </div>
          <Button className="bg-white border-2 border-[#00C0A9] text-[#00C0A9] hover:bg-white hover:text-[#00C0A9] rounded-lg">
            <MdOutlineFileUpload className="me-2" />
            {t("export")}
          </Button>
        </div>
      <SupplierInputTable data={data} loading={loading}/>
    </div>
  </>
};

export default SupplierInputs;
