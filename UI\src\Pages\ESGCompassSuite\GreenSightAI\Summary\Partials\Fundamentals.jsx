import React from "react";
import { FaRegCheckCircle } from "react-icons/fa";
import { MdErrorOutline } from "react-icons/md";
import ReviewQuiz from "./ReviewQuiz";
import PropType from "prop-types";
import BtnSummary from "./BtnSummary";
import { ScrollArea } from "@mantine/core";

function Fundamentals({ title, TotalNum, AnsweredNum, ReviewNum, summary }) {

  // Safety check for summary
  const safeToRender = Array.isArray(summary) && summary.length > 0;

  return (
    <section>
      <h2 className="font-bold">{title || 'Unnamed Category'}</h2>
      <div className="flex lg:gap-[1rem] mb-10 flex-col lg:flex-row">
        <div className="lg:w-[20%]">
          <div className="lg:mb-[3rem] p-2 ">
            <BtnSummary
              TotalNum={TotalNum || 0}
              AnsweredNum={AnsweredNum || 0}
              ReviewNum={ReviewNum || 0}
            />
          </div>
        </div>
        <div className="bg-white mb-[3rem] rounded-lg shadow-2xl mt-2 w-full">
          <ScrollArea>
            <div className="flex min-w-[500px] lg:min-w-full border-b-[1px] text-[#57595A] text-[12px]">
              <div className="w-[10%] border-r-[1px] text-center p-2">No.</div>
              <div className="w-[60%] border-r-[1px] text-center p-2 text-wrap">
                Questions Marked for Review
              </div>
              <div className="w-[20%] border-r-[1px] text-center p-2">
                Comments Status
              </div>
              <div className="w-[10%] text-center p-2">Review</div>
            </div>
            
            {safeToRender ? (
              <div>
                {summary.map((question, index) => (
                  <ReviewQuiz
                    key={index}
                    Id={question.questionId}
                    QuestionNumber={question.questionNumber}
                    setId={Number(question.set_id) + 1}
                    quiz={question.questionText}
                    icon={
                      question.status === "solved" ? (
                        <FaRegCheckCircle className="text-[#3FA327]" />
                      ) : (
                        <MdErrorOutline className="text-[#AB0202] text-[15px]" />
                      )
                    }
                    solved={question.status}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500">
                No questions to review for this category
              </div>
            )}
          </ScrollArea>
        </div>
      </div>
    </section>
  );
}

Fundamentals.propTypes = {
  title: PropType.string,
  TotalNum: PropType.number,
  AnsweredNum: PropType.number,
  ReviewNum: PropType.number,
  summary: PropType.array,
};

export default Fundamentals;