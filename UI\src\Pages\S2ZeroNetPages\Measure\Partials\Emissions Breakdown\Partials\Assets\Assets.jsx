import { BreakdownAssetIcon } from "@/assets/svg/ImageSVG";
import Loading from "@/Components/Loading";
import { formatNumber } from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/StaticData";
import { FaRegEye } from "react-icons/fa";
import { Link } from "react-router-dom";

export default function Assets({ AssetData, loading }) {

  return (
    <>
      {loading ? (
        <Loading />
      ) : (
        <div>
          <div className="flex justify-between items-center">
            <h1 className="font-medium text-3xl font-inter Example-from-Assets-view">
              Assets
            </h1>
            <p className="p-2 bg-white rounded-lg font-medium text-base">
              Total Emission:
              <span className="text-primary font-bold">
                {formatNumber(AssetData?.total_emissions)}
              </span>
              <small>(T CO₂e)</small>
            </p>
          </div>
          {AssetData &&
            Object.entries(AssetData)?.map(
              ([item, value], index) =>
                typeof value === "object" && (
                  <div
                    key={index}
                    className="p-3 bg-white rounded-lg shadow-md mt-3 flex flex-col md:flex-row justify-between items-center gap-5"
                  >
                    <div className="flex items-center gap-3">
                      <p className="p-3 bg-[#07838F1A] rounded-lg">
                        <BreakdownAssetIcon color={"#07838F"} />
                      </p>
                      <div className="">
                        <p className="text-base font-medium">{item}</p>
                        <p className="text-sm font-normal">
                          {formatNumber(value?.total_emission)}
                        </p>
                      </div>
                    </div>
                    <Link
                      className="bg-[#07838F1A] text-primary Example-from-Assets-view-Button hover:bg-[#07838F1A] hover:text-primary flex items-center
                      rounded-3xl border-2 border-primary px-12 py-2"
                      to={`/net-zero/measure/asset_breakdown/${item}/${index}`}
                    >
                      <FaRegEye className="me-1" />
                      View Details
                    </Link>
                  </div>
                )
            )}
        </div>
      )}
    </>
  );
}
