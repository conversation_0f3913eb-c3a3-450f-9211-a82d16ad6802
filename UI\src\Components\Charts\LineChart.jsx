import React, { useState } from "react";
import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  LineElement,
  CategoryScale,
  LinearScale,
  PointElement,
  plugins,
} from "chart.js";
import PropTypes from "prop-types";

// Register the components needed by ChartJS
ChartJS.register(LineElement, CategoryScale, LinearScale, PointElement, plugins,);

const LineChart = ({labels, label, data}) => {
  const [chartData] = useState({
    labels: labels,
    datasets: [
      {
        label: label,
        data: data,
        fill: false,
        borderColor: "rgb(75, 192, 192)",
        tension: 0.1,
      },
    ],
  });

  const options = {
    scales: {
      y: {
        beginAtZero: true,
        max: 100  ,
      },
    },
  };

  return <Line data={chartData} options={options} />;
};

LineChart.propTypes = {
  labels: PropTypes.array.isRequired,
  label: PropTypes.string.isRequired,
  data: PropTypes.array.isRequired,
};

export default LineChart;
