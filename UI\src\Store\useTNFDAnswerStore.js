import { create } from "zustand";

export const useTNFDAnswersStore = create((set, get) => ({
  answers: [],
  remarks: {},
  lastUpdated: {},
  companyInfo: {
    companyName: "",
    countryIncorp: "",
    countryOperations: "",
    revenue: "",
    numEmployees: "",
    industryClassification: "",
    issuerIdentifier: "",
  },

  setAnswer: (questionId, answerText) =>
    set((state) => {
      const existingIndex = state.answers.findIndex(
        (a) => a.requirement_id === questionId
      );

      if (existingIndex >= 0) {
        const newAnswers = [...state.answers];
        newAnswers[existingIndex].disclosure_text = answerText;
        return { answers: newAnswers };
      } else {
        return {
          answers: [
            ...state.answers,
            {
              requirement_id: questionId,
              disclosure_text: answerText,
            },
          ],
        };
      }
    }),

    setRemark: (subsectionId, text) =>
      set((state) => ({
        remarks: {
          ...state.remarks,
          [subsectionId]: text,
        },
      })),
  
    setLastUpdated: (key, timestamp) =>
      set((state) => ({
        lastUpdated: {
          ...state.lastUpdated,
          [key]: timestamp,
        },
      })),

  setCompanyInfo: (name, value) =>
    set((state) => ({
      companyInfo: {
        ...state.companyInfo,
        [name]: value,
      },
    })),
}));
