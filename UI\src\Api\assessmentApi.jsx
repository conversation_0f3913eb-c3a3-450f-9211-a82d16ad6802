import axios from "axios";
import Cookies from "js-cookie";

const API_ENDPOINT =
  "https://issb-report-api-staging.azurewebsites.net/api/v1/reports";

export async function fetchAssessmentData({ System }) {
  const token = Cookies.get("level_user_token");
  try {
    const response = await axios.post(
      API_ENDPOINT,
      {
        framework: System,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data.report;
  } catch (error) {
    console.error("Error fetching assessment data:", error);
    throw error;
  }
}

export async function fetchUpdatedAssessmentData({ reportId }) {
  const token = Cookies.get("level_user_token");
  try {
    const response = await axios.get(
      `${API_ENDPOINT}/${reportId}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data.report;
  } catch (error) {
    console.error("Error fetching updated assessment data:", error);
    throw error;
  }
}
