import QuizV03 from "./QuizLayout";
import MainLayout from "@/Layout/MainLayout";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import { useEffect } from "react";
import { useSustain360Store } from "@/Store/Assessment/useSustain360Store";

const Diagnosis = () => {
  const { esgAssessmentMenu } = useSideBarRoute();
  const { assessmentData,loadAssessmentData } = useSustain360Store();
  

  
  useEffect(() => {
    if(assessmentData == null || assessmentData == undefined || assessmentData == {} || assessmentData?.id == null){
      loadAssessmentData();
    }
  }, [assessmentData]);

  return (
    <MainLayout menus={esgAssessmentMenu} navbarTitle={"Sustain360 AI"}>
        <div className="flex">
          <div className="w-full ">
            <QuizV03 />
          </div>
        </div>
    </MainLayout>
  );
};

export default Diagnosis;
