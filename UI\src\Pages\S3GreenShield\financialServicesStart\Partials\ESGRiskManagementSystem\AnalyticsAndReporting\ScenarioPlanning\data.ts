// Random data generator function
const generateRandomData = () =>
  Array.from({ length: 7 }, () => Math.floor(Math.random() * 100) + 1);

export const graphKey = [
  "Scenario no.1",
  "Scenario no.2",
  "Scenario no.3",
  "Scenario no.4",
  "Scenario no.5",
];

export const radarData = [
  {
    scenario: "Scenario1",
    product: [
      "Eating",
      "Drinking",
      "Sleeping",
      "Designing",
      "Coding",
      "Cycling",
      "Running",
    ],
    sales: generateRandomData(),
  },
  {
    scenario: "Scenario2",
    product: [
      "Eating",
      "Drinking",
      "Sleeping",
      "Designing",
      "Coding",
      "Cycling",
      "Running",
    ],
    sales: generateRandomData(),
  },
  {
    scenario: "Scenario3",
    product: [
      "Eating",
      "Drinking",
      "Sleeping",
      "Designing",
      "Coding",
      "Cycling",
      "Running",
    ],
    sales: generateRandomData(),
  },
  {
    scenario: "Scenario4",
    product: [
      "Eating",
      "Drinking",
      "Sleeping",
      "Designing",
      "Coding",
      "Cycling",
      "Running",
    ],
    sales: generateRandomData(),
  },
  {
    scenario: "Scenario5",
    product: [
      "Eating",
      "Drinking",
      "Sleeping",
      "Designing",
      "Coding",
      "Cycling",
      "Running",
    ],
    sales: generateRandomData(),
  },
];

export const forecastData = [
  {
    date: "2024",
    Scope1: 1900,
    Scope2: 1750,
    Scope3: 1821,
  },
  {
    date: "2025",
    Scope1: 1950,
    Scope2: 1700,
    Scope3: 2809,
  },

  {
    date: "2026",
    Scope1: 2000,
    Scope2: 1650,
    Scope3: 2290,
  },
  { date: "2027", Scope1: 2050, Scope2: 1600, Scope3: 2402 },
  { date: "2028", Scope1: 2100, Scope2: 1550, Scope3: 1821 },
  { date: "2029", Scope1: 2150, Scope2: 1500, Scope3: 2809 },
  { date: "2030", Scope1: 2200, Scope2: 1450, Scope3: 2290 },
  { date: "2031", Scope1: 2250, Scope2: 1400, Scope3: 2402 },
  { date: "2032", Scope1: 2300, Scope2: 1350, Scope3: 1821 },
  { date: "2033", Scope1: 2350, Scope2: 1300, Scope3: 2809 },
  { date: "2034", Scope1: 2400, Scope2: 1250, Scope3: 2290 },
  { date: "2035", Scope1: 2450, Scope2: 1200, Scope3: 2402 },
  { date: "2036", Scope1: 2500, Scope2: 1150, Scope3: 1821 },
  { date: "2037", Scope1: 2550, Scope2: 1100, Scope3: 2809 },
  { date: "2038", Scope1: 2600, Scope2: 1050, Scope3: 2290 },
  { date: "2039", Scope1: 2650, Scope2: 1000, Scope3: 2402 },
  { date: "2040", Scope1: 2700, Scope2: 950, Scope3: 1821 },
  { date: "2041", Scope1: 2750, Scope2: 900, Scope3: 2809 },
  { date: "2042", Scope1: 2800, Scope2: 850, Scope3: 2290 },
  { date: "2043", Scope1: 2850, Scope2: 800, Scope3: 2402 },
  { date: "2044", Scope1: 2900, Scope2: 750, Scope3: 1821 },
  { date: "2045", Scope1: 2950, Scope2: 700, Scope3: 2809 },
  { date: "2046", Scope1: 3000, Scope2: 650, Scope3: 2290 },
  { date: "2047", Scope1: 3050, Scope2: 600, Scope3: 2402 },
  { date: "2048", Scope1: 3100, Scope2: 550, Scope3: 1821 },
  { date: "2049", Scope1: 3150, Scope2: 500, Scope3: 2809 },
  { date: "2050", Scope1: 3200, Scope2: 450, Scope3: 2290 },
];
