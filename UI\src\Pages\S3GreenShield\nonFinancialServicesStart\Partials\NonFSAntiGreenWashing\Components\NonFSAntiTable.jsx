import React from "react";

import { useState } from "react";
import {
  Table,
  Checkbox,
  ScrollArea,
  rem,
  Badge,
} from "@mantine/core";


import { MdArrowDownward } from "react-icons/md";
import { BsFillExclamationCircleFill } from "react-icons/bs";
import { GoDotFill } from "react-icons/go";



// Category
// Question
// YES / NO
// Evidences
// Red Flag
const data = [
  {
    id: "1",
    category: "lorem, ipsum",
    question: "lorem ipsum",
    yesOrNo: "yes",
    evidences: "lorem ipsum",
    redflag: "lorem ipsum",
    weight: "2",
    reqReference: "lorem ipsum",
    comments: "lorem ipsum",
  },
  {
    id: "2",
    category: "lorem, ipsum",
    question: "lorem ipsum",
    yesOrNo: "no",
    evidences: "lorem ipsum",
    redflag: "lorem ipsum",
    weight: "2",
    reqReference: "lorem ipsum",
    comments: "lorem ipsum",
  },
  {
    id: "3",
    category: "lorem, ipsum",
    question: "lorem ipsum",
    yesOrNo: "yes",
    evidences: "lorem ipsum",
    redflag: "lorem ipsum",
    weight: "2",
    reqReference: "lorem ipsum",
    comments: "lorem ipsum",
  },
  {
    id: "4",
    category: "lorem, ipsum",
    question: "lorem ipsum",
    yesOrNo: "no",
    evidences: "lorem ipsum",
    redflag: "lorem ipsum",
    weight: "2",
    reqReference: "lorem ipsum",
    comments: "lorem ipsum",
  },
];

export default function NonFSAntiTable() {
  const [selection, setSelection] = useState(["1"]);
  const toggleRow = (id) =>
    setSelection((current) =>
      current.includes(id)
        ? current.filter((item) => item !== id)
        : [...current, id]
    );
  const toggleAll = () =>
    setSelection((current) =>
      current.length === data.length ? [] : data.map((item) => item.id)
    );

  const rows = data.map((item) => {

    return (
      <Table.Tr
        key={item.id}
        className={`bg-white text-sm text-[#626364] text-center`}
      >
        <Table.Td>
          <Checkbox
            checked={selection.includes(item.id)}
            onChange={() => toggleRow(item.id)}
            color="#00C0A9"
          />
        </Table.Td>
        <Table.Td>{item.category}</Table.Td>
        <Table.Td>{item.question}</Table.Td>
        <Table.Td>
          <Badge className={`${item.yesOrNo == 'no'? 'bg-secondary-danger-200 text-secondary-danger-100':"bg-secondary-green-100 text-secondary-300"} `} 
          leftSection={<GoDotFill className={`${item.yesOrNo == 'no'? 'text-secondary-danger-100':'text-secondary-300'}`} />}>
            {item.yesOrNo}
          </Badge>  
        </Table.Td>
        <Table.Td>{item.evidences}</Table.Td>
        <Table.Td className="flex justify-center">
          {
            item.yesOrNo == 'no'?
            <BsFillExclamationCircleFill className="w-[20px] h-[20px] text-secondary-danger-100" />
            :'-'
          }
          </Table.Td>
        <Table.Td>{item.weight}</Table.Td>
        <Table.Td>{item.reqReference}</Table.Td>
        <Table.Td>{item.comments}</Table.Td>
      </Table.Tr>
    );
  });

  return (
    <div className="bg-[#F7F4F4] mt-7">
      <ScrollArea>
        <Table miw={800} verticalSpacing="sm">
          <Table.Thead className="border-b-2 bg-white pb-6 text-base text-center">
            <Table.Tr>
              <Table.Th style={{ width: rem(40) }}>
                <Checkbox
                  onChange={toggleAll}
                  checked={selection.length === data.length}
                  indeterminate={
                    selection.length > 0 && selection.length !== data.length
                  }
                  color="#00C0A9"
                />
              </Table.Th>
              <Table.Th className="text-secondary-500">
                <div className="flex items-center justify-center gap-2">
                    <span className="font-medium">Category</span>
                    <MdArrowDownward className="text-secondary-300" />
                </div>
              </Table.Th>
              <Table.Th className="text-secondary-500">
                <div
                className="flex items-center justify-center gap-2">
                <span className="font-medium">Question</span>
                <MdArrowDownward className="text-secondary-300" />
                </div>
              </Table.Th>
              <Table.Th className="text-secondary-500">
                <div className="flex items-center justify-center gap-2">
                    <span className="font-medium">YES / NO</span>
                    <MdArrowDownward className="text-secondary-300" />
                </div>
              </Table.Th>
              <Table.Th className="text-secondary-500">
                <div className="flex items-center justify-center gap-2">
                    <span className="font-medium">Evidences</span>
                    <MdArrowDownward className="text-secondary-300" />
                </div>
              </Table.Th>
              <Table.Th className="text-secondary-500">
                <div className="flex items-center justify-center gap-2">
                    <span className="font-medium">Red Flag</span>
                    <MdArrowDownward className="text-secondary-300" />
                </div>
              </Table.Th>
              <Table.Th className="text-secondary-500">
                <div className="flex items-center justify-center gap-2">
                    <span className="font-medium">Weight</span>
                    <MdArrowDownward className="text-secondary-300" />
                </div>
              </Table.Th>
              <Table.Th className="text-secondary-500">
                <div className="flex items-center justify-center gap-2">
                    <span className="font-medium">Regulatory Reference</span>
                    <MdArrowDownward className="text-secondary-300" />
                </div>
              </Table.Th>
              <Table.Th className="text-secondary-500">
                <div className="flex items-center justify-center gap-2">
                    <span className="font-medium">Comments</span>
                    <MdArrowDownward className="text-secondary-300" />
                </div>
              </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>{rows}</Table.Tbody>
        </Table>
      </ScrollArea>
    </div>
  );
}
