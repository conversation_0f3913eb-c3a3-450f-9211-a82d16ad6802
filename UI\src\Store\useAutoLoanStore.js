// useAutoLoanStore.js
import { create } from "zustand";

export const useAutoLoanStore = create((set) => ({
  loan_amount: "",
  total_vehicle_value: "",
  vehicle_make_model: "",
  vehicle_year: "",
  fuel_type: "Electric",
  loan_term: "5",
  annual_mileage: "",
  data_quality_score: "",
  fuel_efficiency:"",
  grid_emission_factor: "",

  // results
  total_financed_emissions: 0,
  attribution_percentage: 0,
  emissions_breakdown: "",
  manufacturing_emissions: 0,
  use_phase_emissions: 0,
  end_of_use_emissions: 0,
  loading:false,
  // Setters
  setLoanAmount: (v) => set({ loan_amount: v }),
  setTotalVehicleValue: (v) => set({ total_vehicle_value: v }),
  setVehicleMakeModel: (v) => set({ vehicle_make_model: v }),
  setVehicleYear: (v) => set({ vehicle_year: v }),
  setFuelType: (v) => set({ fuel_type: v }),
  setLoanTerm: (v) => set({ loan_term: v }),
  setAnnualMileage: (v) => set({ annual_mileage: v }),
  setDataQualityScore: (v) => set({ data_quality_score: v }),
  setFuelEfficiency: (v) => set({ fuel_efficiency: v }),
  setGridEmissionFactor: (v) => set({ grid_emission_factor: v }),
  setLoading: (v) => set({ loading: v }),

  setResults: (results) => set({ 
    total_financed_emissions: results.total_financed_emissions,
    attribution_percentage: results.attribution_percentage,
    emissions_breakdown: results.emissions_breakdown,
    manufacturing_emissions: results.manufacturing_emissions,
    use_phase_emissions: results.use_phase_emissions,
    end_of_use_emissions: results.end_of_use_emissions
 }),
}));
