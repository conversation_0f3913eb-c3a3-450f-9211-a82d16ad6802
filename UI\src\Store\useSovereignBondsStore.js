// useAutoLoanStore.js
import { create } from "zustand";

export const useSovereignBondsStore = create((set) => ({
  value_of_bond_investment: "",  // $5,000,000
  total_government_debt: "", // $2,000,000,000,000 (same as government debt)
  country_total_ghg_emissions: "", // 5,000,000,000 tCO₂e
  emission_source: "National GHG Inventory",
  data_quality_score: "",
  country: "United Kingdom",

  // results
  attribution_percentage: 0,
  emissions_per_sm_invested: 0,
  financed_emissions: 0,

  loading: false,
  estimate_total_government_debt_loading:false,
  estimate_country_total_ghg_emissions_loading:false,
  // Setters
  setValueOfBondInvestment: (v) => set({ value_of_bond_investment: v }),
  setGovernmentDebt: (v) => set({ government_debt: v }),
  setCountryEmissions: (v) => set({ country_emissions: v }),
  setTotalGovernmentDebt: (v) => set({ total_government_debt: v }),
  setCountryTotalGHGEmissions: (v) => set({ country_total_ghg_emissions: v }),
  setEmissionSource: (v) => set({ emission_source: v }),
  setDataQualityScore: (v) => set({ data_quality_score: v }),
  setCountry: (v) => set({ country: v }),
  setLoading: (v) => set({ loading: v }),

  setEstimateTotalGovernmentDeptLoading: (v) => set({ estimate_total_government_debt_loading: v }),
  setEstimateCountryTotalGHGEmissionLoading: (v) => set({ estimate_country_total_ghg_emissions_loading: v }),

  setResults: (results) => set({
    attribution_percentage: results.attribution_percentage,
    emissions_per_sm_invested: results.emissions_per_sm_invested,
    financed_emissions: results.financed_emissions
  }),
}));
