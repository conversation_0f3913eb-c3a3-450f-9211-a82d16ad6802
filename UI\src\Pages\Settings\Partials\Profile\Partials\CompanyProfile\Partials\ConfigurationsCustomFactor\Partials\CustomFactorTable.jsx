import ApiS2 from "@/Api/apiS2Config";
import Loading from "@/Components/Loading";
import {
  Button,
  Checkbox,
  Input,
  NumberInput,
  ScrollArea,
  Table,
  rem,
} from "@mantine/core";
import { showNotification } from "@mantine/notifications";
import cx from "clsx";
import {  useState } from "react";
import { useTranslation } from "react-i18next";
import { CiSearch } from "react-icons/ci";
import { RiDeleteBin6Line } from "react-icons/ri";
import { AsyncSelectOption } from "./AsyncSelectOption";

const validateField = (field, value) => {
  console.log(field);
  console.log(value);

  switch (field) {
    // case "assetType":
    //   if (!value?.trim()) return "Emission Source is required";
    //   if (value.length < 3) return "assetType must be at least 3 characters";
    //   return "";

    case "activity":
      if (!value?.trim()) return "activity is required";
      if (value.length < 2) return "activity must be at least 2 characters";
      return "";

    case "EmissionIntensity":
      if (!value) return "Emission Intensity is required";
      // if (value < 0)
      // return "Emission Intensity must be at least 2 characters";

      return "";

    default:
      return "";
  }
};

export default function CustomFactorTable({ fetchAgain }) {
  const [inputs, setInputs] = useState([]);
  const [selection, setSelection] = useState([]);

  // const [assetTypeAll, setAssetTypeAll] = useState([]);
  const [loading, setLoading] = useState({});
  const [errors, setErrors] = useState({});

  // console.log(inputs);

  const msg = (txt, color = "green") => {
    showNotification({ message: txt, color: color });
  };

  const toggleRow = (id) =>
    setSelection((current) =>
      current.includes(id)
        ? current.filter((item) => item !== id)
        : [...current, id]
    );

  const toggleAll = () =>
    setSelection((current) => {
      if (inputs.length === 0) return [];
      return current.length === inputs.length
        ? []
        : inputs.map((item) => item.id);
    });

  const addNewRow = () => {
    setInputs([
      ...inputs,
      {
        id: Date.now().toString(),
        // assetType: "",
        activity: "",
        eFactors: "",
        uom: "",
        EmissionIntensity: null,
        specificFactor: false,
        baseFactorId: "",
        isNew: true,
        isEditing: true,
      },
    ]);
  };

  const deleteSelectedRows = () => {
    setInputs(inputs.filter((item) => !selection.includes(item.id)));
    setSelection([]);
  };

  const toggleEditRow = (id) => {
    setInputs((currentData) =>
      currentData.map((item) =>
        item.id === id ? { ...item, isEditing: !item.isEditing } : item
      )
    );
  };

  const handleInputChange = (field, value, item) => {
    const error = validateField(field, value);

    // Update errors state
    setErrors((prev) => ({
      ...prev,
      [item.id]: {
        ...prev[item.id],
        [field]: error,
      },
    }));
    const updatedData = inputs.map((dataItem) =>
      dataItem.id === item.id ? { ...dataItem, [field]: value } : dataItem
    );
    setInputs(updatedData); // Assuming you have a setData function from useState
  };
  const rows = inputs?.map((item, idx) => {
    // console.log(item);

    const selected = selection.includes(item.id);
    const isEditable = item.isEditing;
    const itemErrors = errors[item.id] || {};

    return (
      <Table.Tr
        key={item.id} // Use the unique ID as the key
        className={`${cx({
          ["bg-[#07838F1A]"]: selected,
        })} text-sm font-bold text-[#626364] text-center`}
      >
        <Table.Td>
          <Checkbox
            checked={selection.includes(item.id)}
            onChange={() => toggleRow(item.id)}
            color="#07838F"
          />
        </Table.Td>
        <Table.Td>
          <div className="flex justify-center items-center">
            <Checkbox
              // checked={selection.includes(item.id)}
              onChange={(e) =>
                handleInputChange("specificFactor", e.target.checked, item)
              }
              color="#07838F"
            />
          </div>
        </Table.Td>
        <Table.Td>

          <AsyncSelectOption api={'https://etl-pipeline1-staging.azurewebsites.net/get_activities'} type={'activity'} selectedActivity={inputs[idx]?.activity} selectedEFactor={inputs[idx]?.eFactors} selectedUOM={inputs[idx]?.uom} handleInputChange={handleInputChange}
            item={item} placeholder={item.specificFactor ? 'Enter Activity ...' : 'Select Activity ...'} specificFactor={item.specificFactor} />
          {itemErrors.activity && (
            <span className="text-red-500 text-xs mt-1">
              {itemErrors.activity}
            </span>
          )}
        </Table.Td>
        <Table.Td>

          <AsyncSelectOption api={'https://etl-pipeline1-staging.azurewebsites.net/get_efactors'} type={'eFactors'}
            selectedActivity={inputs[idx]?.activity} selectedEFactor={inputs[idx]?.eFactors} selectedUOM={inputs[idx]?.uom} handleInputChange={handleInputChange}
            item={item} placeholder={item.specificFactor ? 'Enter EFactors ...' : 'Select EFactors ...'} specificFactor={item.specificFactor} />

          {itemErrors.eFactors && (
            <span className="text-red-500 text-xs mt-1">
              {itemErrors.eFactors}
            </span>
          )}
        </Table.Td>
        <Table.Td>
          <AsyncSelectOption api={'https://etl-pipeline1-staging.azurewebsites.net/get_uom'} type={'uom'}
            selectedActivity={inputs[idx]?.activity} selectedEFactor={inputs[idx]?.eFactors} selectedUOM={inputs[idx]?.uom} handleInputChange={handleInputChange}
            item={item} placeholder={item.specificFactor ? 'Enter UOM ...' : 'Select UOM ...'} specificFactor={item.specificFactor} />

          {itemErrors.uom && (
            <span className="text-red-500 text-xs mt-1">{itemErrors.uom}</span>
          )}
        </Table.Td>
        <Table.Td>
          <>
            <NumberInput
              onChange={(e) => handleInputChange("EmissionIntensity", e, item)}
              placeholder="Enter Number Of Emission Intensity ..."
              disabled={!isEditable}
              rightSection={" "}
              hidden={!item?.specificFactor}
            />
            {item.specificFactor && itemErrors.EmissionIntensity && (
              <span className="text-red-500 text-xs mt-1">
                {itemErrors.EmissionIntensity}
              </span>
            )}
          </>
        </Table.Td>

        <Table.Td>
          <Button
            disabled={loading[item.id]}
            className={`font-bold text-xs bg-primary text-white hover:bg-primary
             hover:text-white px-5 `}
            onClick={() => handleCustomFactorTable(item)}
            type="submit"
          >
            {loading[item.id] ? <Loading /> : 'Add +'}

          </Button>
        </Table.Td>
      </Table.Tr>
    );
  });
  const handleCustomFactorTable = async (item) => {
    let { activity, eFactors, uom, EmissionIntensity } = item;

    if (activity == "" || (item.specificFactor && EmissionIntensity == "")) {
      msg(
        `${!activity ? "You have to fill (activity)" : ""} ${
          item.specificFactor
            ? " when you select Specific Factor you must Enter (Emission Intensity)"
            : ""
        } input`,
        "red"
      );
      return;
    }
    console.log(EmissionIntensity);

    let dataWithId = {
      activity,
      eFactors,
      uom,
      emissionIntensity: item.specificFactor ? EmissionIntensity : null,
      "specificFactor": item.specificFactor,
      "baseFactorId": item.specificFactor ? null : "-",

    };

    toggleLoading(item.id, true);
    try {
      const { data } = await ApiS2.post("/admin/create-custom-factors", [
        dataWithId,
      ]);
      let newInputs = inputs.filter((el) => el.id !== item.id);
      console.log(newInputs, "newInputs");
      setInputs((p) => newInputs);
      if (data) {
        msg("Add Custom Factor Successfully");
      }
      toggleLoading(item.id, false);
      fetchAgain();
    } catch ({ response }) {
      console.log(response);
      toggleLoading(item.id, false);
      msg(response.data.message, "red");

      response?.data?.created_factors
        ?.map((item, idx) => {
          msg(` ${item.message}`, 'red')
        })
      response?.data?.validation_errors?.map((item, idx) => {
        msg(` ${item.message}`, 'red')
      })
      response?.data?.existing_factors?.map((item, idx) => {
        msg(` ${item.message}`, 'red')
      })

    }
  };
  const toggleLoading = (id, value) => {
    setLoading((prevState) => ({
      ...prevState,
      [id]: value,
    }));
  };
  const { t } = useTranslation();
  return (
    <>
      <div className="items-center justify-between flex">
        <Input
          type="text"
          placeholder={t("SearchPlaceholder")} // Translated placeholder
          className="border border-gray-300 rounded-md sm:w-auto focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-10/12 lg:w-1/4"
          size="md"
          leftSection={<CiSearch />}
        />

        <p
          className="p-2 text-xl text-white rounded-lg cursor-pointer bg-primary"
          onClick={deleteSelectedRows}
        >
          <RiDeleteBin6Line />
        </p>
      </div>
      <div className="bg-white p-5 rounded-lg mt-7">
        <div className="w-full">
          <Button
            className="bg-[#05808b57] border-2 border-primary font-bold text-primary hover:bg-[#05808b57] hover:text-primary  w-full"
            size="lg"
            onClick={addNewRow}
          >
            + Add Custom Factor
          </Button>
        </div>
        <ScrollArea>
          <Table miw={800} verticalSpacing="sm">
            <Table.Thead className="border-b-2 border-primary pb-6 text-primary font-bold text-base text-center">
              <Table.Tr>
                {/* <Table.Th className="text-start"></Table.Th> */}
                <Table.Th style={{ width: rem(40) }}>
                  <Checkbox
                    onChange={toggleAll}
                    checked={
                      inputs.length > 0 && selection.length === inputs.length
                    }
                    indeterminate={
                      inputs.length > 0 &&
                      selection.length > 0 &&
                      selection.length !== inputs.length
                    }
                    color="#07838F"
                  />
                </Table.Th>
                {/* <Table.Th className="text-start">Emission Source</Table.Th> */}
                <Table.Th className="text-center">Specific Factor</Table.Th>
                <Table.Th className="text-center">Activity</Table.Th>
                <Table.Th className="text-center">eFactors</Table.Th>
                <Table.Th className="text-center">UOM</Table.Th>
                <Table.Th className="text-center">Emission Intensity</Table.Th>
                {/* <Table.Th className="text-start">Additional Payload</Table.Th> */}
                <Table.Th className="text-center">Action</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>{rows}</Table.Tbody>
          </Table>
        </ScrollArea>
        {/* <!-- Pagination --> */}
      </div>
    </>
  );
}
