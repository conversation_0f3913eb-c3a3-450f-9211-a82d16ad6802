import React,    { useState } from "react";
import PropTypes from "prop-types";
import { Input, CloseButton } from "@mantine/core";
import { IoSearchSharp } from "react-icons/io5";

function SearchBox({ placeholder, iconSize, ...props }) {
  const [value, setValue] = useState("");

  const clearInput = () => {
    setValue("");
  };

  return (
    <Input
      classNames={{ input: "", wraper: "", section: "" }} // Add any custom classnames here by passing an object to itt
      placeholder={placeholder}
      leftSection={<IoSearchSharp size={iconSize} />}
      value={value}
      onChange={(event) => setValue(event.currentTarget.value)}
      rightSectionPointerEvents="all"
      rightSection={
        <CloseButton
          aria-label="Clear input"
          onClick={clearInput}
          style={{ display: value ? undefined : "none" }}
        />
      }
      {...props} // Spread any other props to Input component
    />
  );
}

SearchBox.propTypes = {
  placeholder: PropTypes.string,
  classNames: PropTypes.object,
  iconSize: PropTypes.number,
};

SearchBox.defaultProps = {
  placeholder: "Search",
  classNames:"",
  iconSize: 16,
};

export default SearchBox;
