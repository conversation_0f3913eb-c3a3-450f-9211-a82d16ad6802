import IssbCard from "@/Issb/IssbCard";
import S3Layout from "@/Layout/S3Layout";

import logo from "@/assets/svg/Layer2.svg";
import { IoMdHome } from "react-icons/io";

const RegulatoryReadinessView = () => {
  const csrdReadinessMenu = [
    "Guided alignment self-assessment",
    "Cross-functional collaboration tools",
    "Document management capabilities",
    "Automated Action Tracking",
  ];
  const ISSBReadness = [
    "Guided alignment self-assessment",
    "Collaborative environment for cross-functional teams",
    "Document management capabilities",
    "Automated Action Tracking",
  ];
  return (
    <S3Layout 
    breadcrumbItems={[
      { title: <IoMdHome size={20} />, href: "/get-started" },
      { title: "Regulatory Readiness", href: "#" },
    ]} navbarTitle="Regulatory Readiness">
      <div className="flex flex-col gap-5 w-full h-full px-3">
        <IssbCard
          icon={
            <>
              <svg
                width="86"
                height="86"
                viewBox="0 0 86 86"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect
                  width="86"
                  height="86"
                  rx="43"
                  fill="#07838F"
                  fillOpacity="0.1"
                />
                <path
                  d="M47.8991 22.1641H38.1074C35.9408 22.1641 34.1699 23.9141 34.1699 26.0807V28.0391C34.1699 30.2057 35.9199 31.9557 38.0866 31.9557H47.8991C50.0658 31.9557 51.8158 30.2057 51.8158 28.0391V26.0807C51.8366 23.9141 50.0658 22.1641 47.8991 22.1641Z"
                  fill="url(#paint0_linear_40007900_95131)"
                />
                <path
                  d="M53.9141 28.0442C53.9141 31.3567 51.2057 34.065 47.8932 34.065H38.1016C34.7891 34.065 32.0807 31.3567 32.0807 28.0442C32.0807 26.8775 30.8307 26.1484 29.7891 26.69C26.8516 28.2525 24.8516 31.3567 24.8516 34.9192V54.5234C24.8516 59.6484 29.0391 63.8359 34.1641 63.8359H51.8307C56.9557 63.8359 61.1432 59.6484 61.1432 54.5234V34.9192C61.1432 31.3567 59.1432 28.2525 56.2057 26.69C55.1641 26.1484 53.9141 26.8775 53.9141 28.0442ZM43.7891 53.315H34.6641C33.8099 53.315 33.1016 52.6067 33.1016 51.7525C33.1016 50.8984 33.8099 50.19 34.6641 50.19H43.7891C44.6432 50.19 45.3516 50.8984 45.3516 51.7525C45.3516 52.6067 44.6432 53.315 43.7891 53.315ZM49.2474 44.9817H34.6641C33.8099 44.9817 33.1016 44.2734 33.1016 43.4192C33.1016 42.565 33.8099 41.8567 34.6641 41.8567H49.2474C50.1016 41.8567 50.8099 42.565 50.8099 43.4192C50.8099 44.2734 50.1016 44.9817 49.2474 44.9817Z"
                  fill="url(#paint1_linear_40007900_95131)"
                />
                <defs>
                  <linearGradient
                    id="paint0_linear_40007900_95131"
                    x1="33.8382"
                    y1="33.3994"
                    x2="55.0478"
                    y2="30.0667"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#2C5A8C" />
                    <stop offset="0.46" stopColor="#1C889C" />
                    <stop offset="1" stopColor="#13B1A8" />
                  </linearGradient>
                  <linearGradient
                    id="paint1_linear_40007900_95131"
                    x1="24.1694"
                    y1="69.3393"
                    x2="68.5482"
                    y2="65.5774"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#2C5A8C" />
                    <stop offset="0.46" stopColor="#1C889C" />
                    <stop offset="1" stopColor="#13B1A8" />
                  </linearGradient>
                </defs>
              </svg>
            </>
          }
          title="CSRD Readiness"
          description="Comprehensive platform to assess and prepare for EU CSRD compliance requirements."
          items={csrdReadinessMenu}
          btnString="Start Reporting"
          link="csrd-dashboard"
        />

        <IssbCard
          icon={
            <>
              <img src={logo} alt="logo" />
            </>
          }
          title="ISSB Readiness"
          description="Comprehensive platform to assess and prepare for ISSB compliance requirements."
          items={ISSBReadness}
          btnString="Start Assessment"
          link="/Grc/regulatory-readiness/issb"
        />
      </div>

      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
    </S3Layout>
  );
};

export default RegulatoryReadinessView;
