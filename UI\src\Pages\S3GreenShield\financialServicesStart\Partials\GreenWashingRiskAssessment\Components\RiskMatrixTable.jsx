import React from "react";
import { ScrollArea, Table } from "@mantine/core";

const head = [
  "Criteria",
  "Level 1",
  "Level 2",
  "Level 3",
  "Level 4",
  "Level 5",
];

const RiskMatrixTable = ({ likelihood, impact }) => {
  const data = [
    {
      id: "1",
      criteria: "Likelihood",
      levels: likelihood,
    },
    {
      id: "2",
      criteria: "Impact",
      levels: impact,
    },
  ];

  const rows = data.map((item) => (
    <tr key={item.id} className="risk-matrix-table">
      <td className="border border-gray-300 p-2 bg-secondary-gray-100">
        {item.criteria}
      </td>
      {Object.values(item.levels).map((level, index) => (
        <td key={index} className="border border-gray-300 p-2 ">
          <ul className="list-disc ml-4">
            {level.map((text, i) => (
              <li key={i} className="mb">
                {text}
              </li>
            ))}
          </ul>
        </td>
      ))}
    </tr>
  ));

  return (
    <div>
      <ScrollArea>
        <Table
          withTableBorder
          withColumnBorders
          className="border-collapse border border-gray-300"
          verticalSpacing="lg"
        >
          <thead className="bg-white text-base text-center">
            <tr>
              {head.map((el, i) => (
                <th
                  key={i}
                  className="border border-gray-300 bg-secondary-gray-100 p-2"
                >
                  <div className="flex items-center justify-center gap-2">
                    <span className="font-medium">{el}</span>
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>{rows}</tbody>
        </Table>
      </ScrollArea>
    </div>
  );
};

export default RiskMatrixTable;
