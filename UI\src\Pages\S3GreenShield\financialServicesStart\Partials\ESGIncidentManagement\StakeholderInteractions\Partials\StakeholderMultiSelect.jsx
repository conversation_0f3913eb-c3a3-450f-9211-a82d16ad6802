import { Button, MultiSelect, Popover, rem } from "@mantine/core";
import React from "react";
import { IoArrowDownOutline } from "react-icons/io5";
const Data = [
  "Report Date",
  "People involved",
  "Location",
  "Assigned to",
  "Source",
];
export default function StakeholderMultiSelect({
  setSelectedColumns,
  selectedColumns,
}) {
  return (
    <Popover width={300} position="bottom" withArrow shadow="md">
      <Popover.Target>
        <Button className="bg-[#FFFFFF] hover:bg-[#fff] hover:text-[#000] text-[#000000]">
          Filter Columns
        </Button>
      </Popover.Target>
      <Popover.Dropdown>
        <MultiSelect
          variant={`unstyled`}
          clearable
          label=""
          placeholder="Select Column"
          data={Data}
          rightSectionPointerEvents="none"
          rightSection={
            <IoArrowDownOutline
              style={{ width: rem(12), height: rem(12), color: "#00C0A9" }}
            />
          }
          comboboxProps={{ withinPortal: false }}
          value={selectedColumns}
          onChange={setSelectedColumns}
        />
      </Popover.Dropdown>
    </Popover>
  );
}
