import { Modal } from "@mantine/core";
import CampanyUserManageTable from "./Partials/ViewCompanyUsesModal/CampanyUserManageTable";

export default function ViewCompanyUsersModal({
  opened,
  close,
  selectedCompanyUsers,
  selectedCompanyId,
  SetSelectedCompanyUsers,
  SetSelectedCompanyId,
  getAllCompanyAccounts,
}) {
  return (
    <Modal
      opened={opened}
      onClose={() => {
        close();
        SetSelectedCompanyUsers(null);
        SetSelectedCompanyId(null);
      }}
      size={"100%"}
      centered
    >
      <CampanyUserManageTable
        Data={selectedCompanyUsers}
        selectedCompanyId={selectedCompanyId}
        getAllCompanyAccounts={getAllCompanyAccounts}
        closeModel={close}
      />
    </Modal>
  );
}
