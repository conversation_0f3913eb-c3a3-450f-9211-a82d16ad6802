import ApiS2 from "@/Api/apiS2Config";
import Loading from "@/Components/Loading";
import {
  Button,
  Checkbox,
  Input,
  Modal,
  MultiSelect,
  Textarea,
  TextInput,
} from "@mantine/core";
import { YearPickerInput } from "@mantine/dates";
import { isNotEmpty, useForm } from "@mantine/form";
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { IoIosArrowForward } from "react-icons/io";
import { showNotification } from "@mantine/notifications";
// import { driver } from "driver.js";
// import "driver.js/dist/driver.css";
// import "@/assets/css/index.css";
// import { FaQuestionCircle } from "react-icons/fa";

const TargetsModal = ({
  isOpen,
  onRequestClose,
  refetchAgain,
  assignees = [],
}) => {
  const { t } = useTranslation();

  const [yearFrom, setYearFrom] = useState("");
  const [yearTo, setYearTo] = useState("");
  const [scope, setScope] = useState([]);
  const [sbtCompatible, setsbtCompatible] = useState(false);
  const [selectedAssingess, setSelectedAssingess] = useState([]);
  const [submit, setSubmit] = useState(false);
    const [elementsReady, setElementsReady] = useState(false);
  const activeTab = "modal";

  const form = useForm({
    mode: "uncontrolled",
    initialValues: {
      targetName: "",
      yearFrom: "",
      yearTo: "",
      reductionPct: "",
      note: "",
    },
    validate: {
      targetName: isNotEmpty("targetName is required"),
      reductionPct: isNotEmpty("End Date is required"),
    },
  });

  const handleYearFrom = (d) => {
    let da = d.getFullYear();
    setYearFrom(da);
  };

  const handleYearTo = (d) => {
    let da = d.getFullYear();
    setYearTo(da);
  };

  const handleScope = (e, value) => {
    const isChecked = e.target.checked;
    const newScope = [...scope]; // Create a copy of the current scope state

    if (isChecked) {
      // Add the selected value to the scope array if it's not already present
      if (!newScope.includes(value)) {
        newScope.push(value);
      }
    } else {
      // Remove the deselected value from the scope array
      const index = newScope.indexOf(value);
      if (index !== -1) {
        newScope.splice(index, 1);
      }
    }
    setScope(newScope);
  };

  const addTargetFunc = async (values) => {
    let d = {
      name: values.targetName,
      yearFrom: yearFrom,
      yearTo: yearTo,
      sbtCompatible: sbtCompatible,
      scope: scope.join(","),
      reductionPct: values.reductionPct,
      notes: values.note,
      assignees: selectedAssingess,
    };

    if (yearFrom == "" || yearTo == "") {
      showNotification({
        message: "date is required",
        color: "red",
        position: "top-center",
      });
      return;
    }

    if (scope.length == 0) {
      showNotification({
        message: "Scope required",
        color: "red",
        position: "top-center",
      });
      return;
    }

    setSubmit(true);
    try {
      const { data } = await ApiS2.post(
        "/decarbonize/create_climate_target",
        d
      );
      showNotification({
        message: "Form Data Added Successfully",
        color: "green",
      });
      refetchAgain();
    } catch (er) {
      showNotification({
        message: "Error happend",
        color: "red",
      });
      //console.log(er);
    }
    setSubmit(false);
  };
  const uniqueAssignees = [
    ...new Map(assignees.map((item) => [item.label.name, item])).values(),
  ];

  // const getGuideSteps = () => [
  //   {
  //     element: ".Fill-in-the-new-target-details",
  //     popover: {
  //       title: t("Fill in the new target details"),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".The-Decarbonise-form-Target-Name",
  //     popover: {
  //       title: t("Target Name"),
  //       description: t("Enter a short, clear name that describes the goal (e.g., 'Reduce Office Energy Use')."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".The-Decarbonise-form-Assignees",
  //     popover: {
  //       title: t("Assignees (choose who is responsible)"),
  //       description: t("Select the team members who will manage and track this target."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".The-Decarbonise-form-Baseline-Year",
  //     popover: {
  //       title: t("Baseline Year"),
  //       description: t("The starting point you will measure improvements from."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".The-Decarbonise-form-Target-Year",
  //     popover: {
  //       title: t("Target Year"),
  //       description: t("When you aim to achieve the emission reduction goal."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".The-Decarbonise-form-Scopes",
  //     popover: {
  //       title: t("Select Scope(s) (Scope 1, Scope 2, Scope 3)"),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".The-Decarbonise-form-Emission-Reduction",
  //     popover: {
  //       title: t("Enter the Reduction Percentage (% from 1 to 100)"),
  //       description: t("How much you want to reduce emissions, compared to the baseline (e.g., 15% by 2026)."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".The-Decarbonise-form-Add-notes",
  //     popover: {
  //       title: t("Add Notes (optional)"),
  //       description: t("Write any extra details or assumptions about the target (e.g., 'Based on energy efficiency projects')."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".The-Decarbonise-form-Add-Target",
  //     popover: {
  //       title: t("Click \"Add Target\" to save your target."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".The-Decarbonise-form-Add-note",
  //     popover: {
  //       title: t("NOTE: You can also mark your target as SBT Compatible (aligning with Science Based Targets)."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  // ];

  // const checkElementsExist = () => {
  //   const steps = getGuideSteps();
  //   const allElementsExist = steps.every((step) =>
  //     document.querySelector(step.element)
  //   );
  //   return allElementsExist;
  // };

  // useEffect(() => {
  //   if (activeTab === "modal") {
  //     const observer = new MutationObserver(() => {
  //       if (checkElementsExist()) {
  //         setElementsReady(true);
  //         observer.disconnect();
  //       }
  //     });

  //     observer.observe(document.body, {
  //       childList: true,
  //       subtree: true,
  //     });

  //     return () => observer.disconnect();
  //   }
  // }, [activeTab]);

  // const startGuide = () => {
  //   const driverObj = driver({
  //     showProgress: true,
  //     popoverClass: "my-custom-popover-class",
  //     nextBtnText: "Next",
  //     prevBtnText: "Back",
  //     doneBtnText: "Done",
  //     progressText: "Step {{current}} of {{total}}",
  //     overlayColor: "rgba(0, 0, 0, 0)",
  //     steps: getGuideSteps(),
  //     onDestroyStarted: () => {
  //       localStorage.setItem("hasSeenDecarbonizeTargetModalGuide", "true");
  //       driverObj.destroy();
  //     },
  //   });
  //   driverObj.drive();
  // };

  // useEffect(() => {
  //   const hasSeenGuide = localStorage.getItem(
  //     "hasSeenDecarbonizeTargetModalGuide"
  //   );
  //   if (!hasSeenGuide) {
  //     startGuide();
  //   }
  //   return () => {
  //     const driverObj = driver();
  //     if (driverObj.isActive()) {
  //       driverObj.destroy();
  //     }
  //   };
  // }, [activeTab, elementsReady]);

  return (
    <Modal
      opened={isOpen}
      onClose={onRequestClose}
      title={t("targetsModal.title")}
    >
      {/* <div
        onClick={startGuide}
        style={{
          position: "absolute",
          bottom: -160,
          left: 20,
          cursor: "pointer",
          zIndex: 1000,
        }}
      >
        <div className="h-12 w-12 flex items-center justify-center bg-gradient-to-tr from-[#2C5A8C] via-[#1C889C] to-[#13B1A8] p-1  cursor-pointer rounded-full">
          <FaQuestionCircle
            size={34}
            color="#ffffff"
            className="mx-auto cursor-pointer"
          />
        </div>
      </div> */}
      <form
        // className="w-full px-10 py-4 bg-white shadow-md rounded-xl"
        onSubmit={form.onSubmit(addTargetFunc)}
        className="Fill-in-the-new-target-details"
      >
        <div className="The-Decarbonise-form-Target-Name">
          <label
            htmlFor="targetName"
            className="block mb-3 text-sm font-semibold text-gray-700 "
          >
            {t("targetsModal.targetNameLabel")}
          </label>
          <TextInput
            {...form.getInputProps("targetName")}
            key={form.key("targetName")}
            // value={targetName}
            id="targetName"
            name="targetName"
            // onChange={(e) => setTargetName((p) => e.target.value)}
            type="text"
          />
        </div>

        <div>
          <Checkbox
            className="my-4 font-semibold The-Decarbonise-form-Add-note"
            label={t("targetsModal.sbtCompatibleLabel")}
            color="teal"
            variant="outline"
            id="sbtCompatible"
            name="sbtCompatible"
            onChange={(e) => setsbtCompatible(e.target.checked)}
          />
          <p className="text-sm font-light">
            {t("targetsModal.sbtDescription")}{" "}
            <span className="text-sky-700">{t("targetsModal.helpCenter")}</span>
            .
          </p>

          <div className="flex items-center justify-start my-4">
            <IoIosArrowForward />
            <p>{t("targetsModal.additionalInfo")}</p>
          </div>

          {assignees.length == 0 ? (
            <>
              <Loading /> Loading Assignees{" "}
            </>
          ) : (
            <MultiSelect
              onChange={(selectedNames) => {
                //console.log(selectedNames);

                const selectedIds = selectedNames.map(
                  (name) =>
                    uniqueAssignees.find((item) => item.label.name === name)
                      ?.value
                );

                // //console.log(selectedIds);
                setSelectedAssingess(selectedIds); // Set selected IDs
              }}
              label="Assignees"
              placeholder="Pick value"
              data={uniqueAssignees.map((item) => item.label.name)}
              className="The-Decarbonise-form-Assignees"
            />
          )}
        </div>

        <div className="grid grid-cols-1 gap-4 mt-4 sm:grid-cols-2">
          <div               className="The-Decarbonise-form-Baseline-Year">
            <YearPickerInput
              // {...form.getInputProps("yearFrom")}
              // key={form.key("yearFrom")}
              label={t("targetsModal.baselineYearLabel")}
              placeholder={t("targetsModal.baselineYearPlaceholder")}
              onChange={handleYearFrom}
              // name="yearFrom"
              // id="yearFrom"
              // {...form.getInputProps("yearFrom")}
            />
          </div>
          <div className="The-Decarbonise-form-Target-Year">
            <YearPickerInput
              label={t("targetsModal.targetYearLabel")}
              placeholder={t("targetsModal.targetYearPlaceholder")}
              // {...form.getInputProps("yearTo")}
              // key={form.key("yearTo")}
              onChange={handleYearTo}
            />
          </div>
        </div>

        <div className="my-4 The-Decarbonise-form-Scopes">
          <h3 className="text-sm font-semibold">
            {t("targetsModal.scopeSelection")}
          </h3>
          <div className="flex justify-start gap-5 py3">
            <Checkbox
              onChange={(e) => handleScope(e, "S1")}
              className="my-4 font-semibold"
              label={t("targetsModal.scope1Label")}
              color="teal"
              variant="outline"
            />
            <Checkbox
              onChange={(e) => handleScope(e, "S2")}
              className="my-4 font-semibold"
              label={t("targetsModal.scope2Label")}
              color="teal"
              variant="outline"
            />
            <Checkbox
              onChange={(e) => handleScope(e, "S3")}
              className="my-4 font-semibold"
              label={t("targetsModal.scope3Label")}
              color="teal"
              variant="outline"
            />
          </div>
        </div>

        <div className="mt-4 The-Decarbonise-form-Emission-Reduction">
          <Input.Wrapper label={t("targetsModal.emissionReductionLabel")}>
            <Input
              placeholder={t("targetsModal.emissionReductionPlaceholder")}
              rightSection="%"
              // value={reductionPct}
              {...form.getInputProps("reductionPct")}
              key={form.key("reductionPct")}
              type="number"
              // onChange={(e) => setReductionPct(e.target.value)}
            />
            <Input.Description className="py-2">
              {t("targetsModal.emissionReductionDescription")}
            </Input.Description>
          </Input.Wrapper>
        </div>

        <Textarea
          className="py-3 The-Decarbonise-form-Add-notes"
          label={t("targetsModal.notesLabel")}
          placeholder={t("targetsModal.notesPlaceholder")}
          onChange={(e) => setnote(e.target.value)}
        />

        <div className="flex justify-end">
          {submit ? (
            <Loading />
          ) : (
            <Button
              // onClick={addTargetFunc}
              type="submit"
              variant="filled"
              size="sm"
              radius="md"
              className="mt-auto mb-2 ms-2 bg-primary hover:bg-secondary The-Decarbonise-form-Add-Target"
            >
              {t("targetsModal.addTarget")}
            </Button>
          )}
        </div>
      </form>
    </Modal>
  );
};

export default TargetsModal;
