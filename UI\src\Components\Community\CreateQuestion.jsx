import { Input, Button, MultiSelect } from '@mantine/core';
import { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { CheckListIcon } from '@/assets/icons';
import UploadFile from './UploadFile';
import ApiS3 from '@/Api/apiS3';
import { toast } from 'react-toastify';

export default function CreateQuestion({ close, fetchQuestions }) {
  const { t } = useTranslation();
  const [question, setQuestion] = useState('');
  const [files, setFiles] = useState([]);
  const [tagValue, setTagValue] = useState([]);
  const [error, setError] = useState('');
  const questionRef = useRef();
  const [submitting, setSubmitting] = useState(false);

  const resetForm = () => {
    setTagValue([]);
    setQuestion('');
    setFiles([]);
  };
  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);

    if (!question) return setError('Please Type Your Question!');
    if (tagValue.length < 1) return setError('Please Choose at least one tag!');
    const newPost = {
      question: question,
      tags: JSON.stringify(tagValue),
    };

    const formData = new FormData();
    Object.keys(newPost).forEach((key) => formData.append(key, newPost[key]));
    files.forEach((file) => {
      formData.append('attachments', file);
    });
    try {
      const response = await ApiS3.post('/community/questions/text', formData);
      if (response.status === 201) {
        toast.success('Question added Successfully.');
        fetchQuestions();
      }
    } catch (error) {
      toast.error('Please Try Again Later!');
    } finally {
      setSubmitting(false);
      resetForm();
      close();
    }
  };

  return (
    <>
      <div className="px-5 py-4 bg-transparent rounded-lg w-full mb-6 text-gray-800">
        <div className="mb-2">
          <h5 className="font-bold text-[#494949]">{t('writingAGoodQuestion')}</h5>
          <p>{t('writingGuidance')}</p>
        </div>
        <div className="mb-2">
          <h5 className="font-bold text-[#494949]">{t('businessQuestion')}</h5>
          <p>{t('businessQuestionGuidance')}</p>
        </div>
        <div className="mb-2">
          <h5 className="font-bold text-[#494949]">{t('steps')}</h5>
          <ol>
            {t('stepsList', { returnObjects: true }).map((step, index) => (
              <li key={index} className="text-base font-medium flex items-center gap-1 my-1">
                <CheckListIcon /> {step}
              </li>
            ))}
          </ol>
        </div>
      </div>
      <form onSubmit={handleSubmit} className="flex flex-col gap-5">
        {error && (
          <div className="flex items-center justify-between mt-4 p-5 border border-red-600  bg-transparent text-red-800 rounded-lg truncate w-full">
            <p>{error}</p>
          </div>
        )}
        <div className="px-5 py-4 bg-white rounded-lg w-full shadow-[0px_0px_8.91px_0px_#0000001A]">
          <h3 className="text-base font-bold">{t('detailsQuestion')}</h3>
          <p className="mb-4 text-sm font-medium">{t('detailsGuidance')}</p>
          <Input
            value={question}
            onChange={(e) => {
              setQuestion(e.target.value);
              setError('');
            }}
            placeholder={t('detailsPlaceholder')}
            ref={questionRef}
            name="question"
            required
          />
        </div>
        <div className="px-5 py-4 bg-white rounded-lg w-full shadow-[0px_0px_8.91px_0px_#0000001A]">
          <h3 className="text-base font-bold">{t('tags')}</h3>
          <p className="mb-4 text-sm font-medium">{t('tagsGuidance')}</p>
          <MultiSelect
            placeholder={t('tagsPlaceholder')}
            nothingFoundMessage={t('nothingFound')}
            maxValues={5}
            value={tagValue}
            onChange={(value) => {
              setTagValue(value);
              setError('');
            }}
            data={['Sustainably', 'Governance', 'Environment', 'Data', 'Society', 'General']}
            searchable
            name="tags"
            comboboxProps={{ zIndex: 1000, withinPortal: false }}
          />
        </div>
        <div className="px-5 py-4 bg-white rounded-lg w-full shadow-[0px_0px_8.91px_0px_#0000001A]">
          <h3 className="text-base font-bold">{t('Attachment')}</h3>
          <p className="mb-4 text-sm font-medium">{t('Add an attachment to describe your question.')}</p>
          <UploadFile onFilesChange={setFiles} files={files} />
        </div>

        <div className="flex items-center gap-4">
          <Button
            type="button"
            onClick={resetForm}
            variant="outline"
            className="border-secondary-100 hover:border-primary text-secondary-100 hover:text-primary duration-300"
          >
            Reset
          </Button>
          <Button
            disabled={submitting}
            type="submit"
            variant="filled"
            className={`${submitting ? 'bg-secondary-100/90' : 'bg-secondary-100'} hover:bg-primary duration-300`}
          >
            {submitting ? 'Submitting...' : 'Submit'}
          </Button>
        </div>
      </form>
    </>
  );
}
