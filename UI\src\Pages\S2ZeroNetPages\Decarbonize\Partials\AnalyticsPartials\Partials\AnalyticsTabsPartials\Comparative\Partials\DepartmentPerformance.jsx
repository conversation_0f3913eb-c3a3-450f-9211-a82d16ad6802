import { Bar<PERSON>hart } from "@mantine/charts";
import React from "react";

const DepartmentPerformance = () => {
  const data = [
    {
      month: "Department 1",
      Emissions: 1200,
      EnergyUse: 900,
      Rate: 200,
    },
    {
      month: "Department 2",
      Emissions: 1900,
      EnergyUse: 1200,
      Rate: 400,
    },
    {
      month: "Department 3",
      Emissions: 400,
      EnergyUse: 1000,
      Rate: 200,
    },
  ];
  return (
    <div>
      <div className="flex items-center justify-between p-2 mb-4">
        <h3 className="text-2xl font-semibold me-9">Department Performance</h3>
      </div>
      <BarChart
        className="bg-white py-8 px-4 rounded-lg shadow-lg"
        h={350}
        w={450}
        data={data}
        dataKey="month"
        series={[
          { name: "Emissions", color: "violet.6" },
          { name: "EnergyUse", color: "blue.6" },
          { name: "Rate", color: "teal.6" },
        ]}
        tickLine="y"
      />
    </div>
  );
};

export default DepartmentPerformance;
