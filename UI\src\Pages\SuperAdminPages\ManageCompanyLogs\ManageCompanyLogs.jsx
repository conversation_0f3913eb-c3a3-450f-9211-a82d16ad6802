import { Tabs } from '@mantine/core'
import React, { useState } from 'react'
import { useTranslation } from 'react-i18next';
import CompanyAdminLogs from './CompanyAdmin/CompanyAdminLogs';
import SuperAdminLogs from './SuperAdmin/SuperAdminLogs';

export default function ManageCompanyLogs() {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState("Company Admin");

  return (
    <Tabs className="" defaultValue={activeTab} onChange={setActiveTab}>
      <Tabs.List
        // justify="center"
        className="static flex flex-wrap justify-center gap-5 py-5 mb-6 rounded-md text-primary"
      >
        <Tabs.Tab
          value="Company Admin"
          className={`text-lg md:w-[23%] ${activeTab === "Company Admin"
              ? "text-white bg-opacity-100"
              : "bg-opacity-10"
            } hover:opacity-70 rounded-lg py-5 font-semibold bg-primary focus:border-b-primary`}
        >
          {t("Company Admin")}
        </Tabs.Tab>

        <Tabs.Tab
          value="Super Admin"
          className={`text-lg md:w-[23%] ${activeTab === "Super Admin"
              ? "text-white bg-opacity-100"
              : "bg-opacity-10"
            } hover:opacity-70 rounded-lg py-5 font-semibold bg-primary focus:border-b-primary`}
        >
          {t("Super Admin")}
        </Tabs.Tab>
      </Tabs.List>

      <Tabs.Panel value="Company Admin">
      <CompanyAdminLogs/>
      </Tabs.Panel>

      <Tabs.Panel value="Super Admin">
      <SuperAdminLogs/>

      </Tabs.Panel>
    </Tabs>
  )
}
