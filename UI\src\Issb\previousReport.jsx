import AccordionDemo from "./LastReport/Accordion";
import { useLocation } from "react-router-dom";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import MainLayout from "@/Layout/MainLayout";

const PreviousReport = () => {
  const { issbMenu } = useSideBarRoute();
  const location = useLocation();
  const { previousReportid, framework, status } = location.state || {};

  if (!previousReportid) {
    console.warn("No report ID found in state, redirecting to home...");
    return (
      <div className="text-center mt-10">
        No report selected. Please select a report from the list.
      </div>
    );
  }

  return (
    <MainLayout menus={issbMenu} navbarTitle={`${framework} Reporting`}>
      <div className="min-h-screen p-4 md:p-10">
        <AccordionDemo
          previousReportid={previousReportid}
          framework={framework}
          status={status}
        />
      </div>
    </MainLayout>
  );
};
export default PreviousReport;
