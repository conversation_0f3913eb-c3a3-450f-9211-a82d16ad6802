import apiCustomFactor from "@/Api/apiCustomFactor";
import ApiS2 from "@/Api/apiS2Config";
import Loading from "@/Components/Loading";
import useFormatTextKey from "@/hooks/useFormatTextKey";
import { Button, Input, NumberInput, Select } from "@mantine/core";
import { showNotification } from "@mantine/notifications";
import { driver } from "driver.js";
import "driver.js/dist/driver.css";
import "@/assets/css/index.css";
import { FaQuestionCircle } from "react-icons/fa";
import { useEffect, useState } from "react";
import { IoIosArrowDown } from "react-icons/io";
import { useTranslation } from "react-i18next";

const CustomFactorApi = ({
  assetType,
  fetchAgain,
  setAllValues,
  stobj,
  setStoreKeys,
  setFormState,
  setResponse,
  response,
  formState,
  storeKeys,
  allValues,
  setSelectedAssetType,
  assetNamesIds,
  selctedEmissionId,
}) => {
  const { t } = useTranslation(); // Use translation function
  const msg = (txt, color = "green") => {
    showNotification({ message: txt, color: color });
  };

  const [loading, setLoading] = useState("");
  
  const [enableTier2, setEnableTier2] = useState(false);
  const [editEmByYear, setEditEmByYear] = useState(true);
  const [yearValues, setYearValues] = useState([]);

  // تعريف الـ steps للـ guide
  const getGuideSteps = () => [
    {
      element: ".Activity-Method",
      popover: {
        title: t("Define Activity Method"),
        description: t(
          "Each custom factor is linked to a specific activity and its calculation method. Write or select the method used to calculate the emissions for the selected activity (e.g., Purchased Electricity, Fuel Combustion)."
        ),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".Select-Effactors",
      popover: {
        title: t("Select E-Factors (Country & Tariff)"),
        description: t(
          "Emission factors can vary by country and sometimes by tariff. Country: Dropdown to select the country related to the emission factor (e.g., United Kingdom, USA). Tariff: Dropdown to select a specific tariff if applicable (e.g., Peak, Off-Peak Electricity Rates)."
        ),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".Enter-Emission",
      popover: {
        title: t("Enter Emission Value by Year"),
        description: t(
          "Add the emission value for one or multiple years (e.g., 2023: 0.233 kg CO₂/kWh). This helps you track any changes in emission factors over time."
        ),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".Set-UOM",
      popover: {
        title: t("Set UOM (Unit of Measurement)"),
        description: t(
          "Select the Unit of Measurement (e.g., kg CO₂/kWh) for the emission factor."
        ),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".Review-Source",
      popover: {
        title: t("Review Source"),
        description: t(
          "Check the data source provided for the emission factor (e.g., carbondi.com, iea.org). Note: No input needed here — it’s automatically filled."
        ),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".Emission-Submit",
      popover: {
        title: t("Click “Submit” Button"),
        description: t(
          "To save the custom factor and add it to your Custom factor List."
        ),
        side: "top",
        align: "center",
      },
    },
  ];

  // دالة لتشغيل الـ guide
  const startFormGuide = () => {
    const driverObj = driver({
      showProgress: true,
      popoverClass: "my-custom-popover-class",
      nextBtnText: t("Next"),
      prevBtnText: t("Back"),
      doneBtnText: t("Done"),
      overlayColor: "rgba(0, 0, 0, 0)",
      progressText: t("Step {{current}} of {{total}}"),
      steps: getGuideSteps(),
      onDestroyStarted: () => {
        localStorage.setItem("hasSeenEmissionFormGuide", "true");
        driverObj.destroy();
      },
    });
    driverObj.drive();
  };

  useEffect(() => {
    const hasSeenGuide = localStorage.getItem("hasSeenEmissionFormGuide");
    if (!hasSeenGuide) {
      startFormGuide();
    }
    return () => {
      const driverObj = driver();
      if (driverObj.isActive()) {
        driverObj.destroy();
      }
    };
  }, []);

  const getFactors = async () => {
    setLoading(true);
    try {
      const res = await apiCustomFactor.post("/emission_resource", allValues);
      setResponse([res.data]);
    } catch (error) {
      console.log("🚀 ~ getFactors ~ error:", error);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (assetType) {
      getFactors();
    }
  }, [allValues, assetType]);

  useEffect(() => {
    if (response != undefined) {
      setYearValues(response[0]?.emission_value_by_year || []);
    }
  }, [response]);

  const handleChange = (mainKey, nestedKey, value, mainObject) => {
    if (Object.entries(storeKeys).length > 0) {
      stobj[nestedKey] = value;
    } else {
      Object.entries(mainObject).map(([mainKey, nestedObject]) => {
        Object.entries(nestedObject).map(([key, nvalue]) => {
          stobj[key] = key == nestedKey ? value : "";
        });
      });
    }

    setStoreKeys(stobj);

    let d = allValues[0];
    setAllValues((p) => (p = [{ ...d, ...stobj }]));

    setFormState((prevState) => ({
      ...prevState,
      [mainKey]: {
        ...(prevState[mainKey] || {}),
        [nestedKey]: value,
      },
    }));
  };

  const sendData = async () => {
    setLoading(true);
    let staticObject = {
      quantityKeys: response[0].qty_inputs,
      acceptableYears: response[0].year,
      additionalPayload: response[0].additional_payload,
      emissionSourceId: selctedEmissionId,
      
    };
    const cleanFormState = (formState, response) => {
      const updatedFormState = { ...formState };

      const compareAndClean = (stateObj, responseObj) => {
        for (const key in stateObj) {
          if (
            typeof stateObj[key] === "object" &&
            !Array.isArray(stateObj[key])
          ) {
            compareAndClean(stateObj[key], responseObj[key] || {});
          } else if (
            responseObj &&
            Array.isArray(responseObj[key]) &&
            responseObj[key].length === 0
          ) {
            stateObj[key] = "";
          }
        }
      };

      compareAndClean(updatedFormState, response && response[0]);
      return updatedFormState;
    };

    const cleanedFormState = cleanFormState(formState, response);
    // ...userSelfInput,
    let customData = {
      ...cleanedFormState,
      ...staticObject,
      emissionIntensity: null,
      specificFactor: enableTier2,
      companyAssetIds: assetNamesIds,
      source: response[0].source,
      source_type: "url",
      // emissionValueByYear: enableTier2 == true ? yearValues :  response.emission_value_by_year
      emissionValueByYear: response[0].emission_value_by_year
      
    };

    try {
      const { data } = await ApiS2.post("/admin/create-custom-factors", [
        customData,
      ]);
      msg(`${data.message}`);
      setResponse(null);
      setSelectedAssetType(null);
      fetchAgain();
    } catch ({ response }) {
      msg(response?.data?.message || "An error occurred", "red");

      const handleErrorMessages = (errors, label) => {
        errors?.forEach((item) => msg(`${label}: ${item.message}`, "red"));
      };

      handleErrorMessages(response?.data?.created_factors, "Created Factors");
      handleErrorMessages(
        response?.data?.validation_errors,
        "Validation Errors"
      );
      handleErrorMessages(response?.data?.existing_factors, "Existing Factors");
    } finally {
      setLoading(false);
    }
  };

  const handleTextChange = (mainKey,key,value) => {
    // const mainwithnested = {[mainKey]: {[key]:value}}
    // setuserSelfInput(p=> p = {...p,...mainwithnested})
    
    setFormState((prevState) => ({
      ...prevState,
      [mainKey]: {
        ...(prevState[mainKey] || {}),
        [key]: value,
      },
    }));
  }



  const handleEmissionByYear = (item, index, inputValue) => {
    setYearValues(prevValues => {
      return prevValues.map((value, i) => {
        if (i === index) {
          return { ...value, year: inputValue };
        }
        return value;
      });
    });
  };


  const handleEmissionByYearValues = (item, index, inputValue) => {
    setYearValues(prevValues => {
      return prevValues.map((value, i) => {
        if (i === index) {
          return { ...value, emission_value: inputValue };
        }
        return value;
      });
    });
  };
  
  
  const enableTier2Func = () => {
    setEnableTier2(true)
    setEditEmByYear(false)
  }
  
  

  return (
    <>
      {response === undefined && loading && (
        <div className="bg-primary/40 h-full z-50 rounded-2xl flex justify-center items-center w-full">
          <Loading />
        </div>
      )}
      <div
        onClick={startFormGuide}
        style={{
          position: "fixed",
          top: 80,
          right: 30,
          cursor: "pointer",
          zIndex: 1000,
        }}
      >
        <div className="h-12 w-12 flex items-center justify-center bg-gradient-to-tr from-[#2C5A8C] via-[#1C889C] to-[#13B1A8] p-1 cursor-pointer rounded-full">
          <FaQuestionCircle size={34} color="#ffffff" className="mx-auto cursor-pointer" />
        </div>
      </div>
      <div className="form-container relative">
        {response != "" &&
          response?.map((mainObject, index) => (
            <div
              key={index}
              className="main-object mt-7 relative gap-5" //or flex col
            >
              {loading && (
                <div className="bg-primary/40 h-full z-50 rounded-2xl flex justify-center items-center absolute w-full">
                  <Loading />
                </div>
              )}
              {Object.entries(mainObject).map(([mainKey, nestedObject]) => (
                <div key={mainKey} className="nested-section flex w-full mt-1">
                  {mainKey === "additional_payload" ||
                  mainKey === "qty_inputs" ||
                  mainKey === "source" ||
                  mainKey === "year" ||
                  mainKey === ""
                  ? (
                    ""
                  ) : (
                    <div className="w-full">
                      {mainKey === "source" ? (
                        <>
                          <h3 className="text-primary font-bold mt-5">
                            {mainKey.charAt(0).toUpperCase() + mainKey.slice(1)}
                          </h3>

                          {nestedObject.length > 0
                            ? nestedObject.map((link, i) => (
                                <div key={i}>
                                  <a
                                    className="text-blue-500 underline underline-offset-1"
                                    href={link.link}
                                    target="_blank"
                                    download={link.link}
                                  >
                                    {" "}
                                    File Name : {link.name}{" "}
                                  </a>
                                </div>
                              ))
                            : ""}
                        </>
                      ) : (
                        <div>
                          {
                            mainKey == "emission_value_by_year" ? (
                              <>
                                <h3 className="text-primary font-bold">Emission Value by Year 
                                  
                                    {/* // yearValues.length > 0 && <Button onClick={enableTier2Func} className="mx-1 bg-bg-lite2 text-sm text-primary between rounded-xl p-3 gap-2">Edit</Button> */}
                                
                                  </h3>
                                <div className="flex flex-wrap gap-4">
                                {
                                 yearValues && yearValues?.map((item,i) => (
                                    <div className="grid grid-cols-2 w-fit gap-4 border-b-2 pb-2" key={i}>

                                      <Input.Wrapper
                                        label={'Year'}
                                      >
                                        <NumberInput
                                        disabled={editEmByYear}
                                        onChange={(e)=> handleEmissionByYear(item,i,e)}
                                          type="number"
                                          value={item.year}
                                        />
                                      </Input.Wrapper>
                                      <Input.Wrapper
                                        label={'Emission Value'}
                                      >
                                        <NumberInput
                                        disabled={editEmByYear}
                                        onChange={(e)=> handleEmissionByYearValues(item,i,e)}
                                          value={item.emission_value}
                                        />
                                      </Input.Wrapper>
                                    </div>
                                  ))
                                }

                                </div>
                              </>
                            ) : (
                              <>
                              <h3 className="text-primary font-bold">
                                {mainKey.charAt(0).toUpperCase() + mainKey.slice(1)}
                              </h3>
                              <div className="grid lg:grid-cols-2 gap-4">
                                {Object.entries(nestedObject).map(
                                  ([nestedKey, nestedValues]) => (
                                    <div key={nestedKey} className="nested-item">
                                      {Array.isArray(nestedValues) ? (
                                        <>
                                          {nestedValues.length > 0 ? (
                                            <Select
                                              label={useFormatTextKey(nestedKey)}
                                              placeholder={`Write or Select ${useFormatTextKey(
                                                nestedKey
                                              )}`}
                                              allowDeselect={false}
                                              data={
                                                Array.isArray(nestedValues)
                                                  ? nestedValues.filter(
                                                      (v) => v != null
                                                    )
                                                  : []
                                              }
                                              rightSection={
                                                !allValues[0]?.[nestedKey] && (
                                                  <IoIosArrowDown />
                                                )
                                              }
                                              value={allValues[0]?.[nestedKey]}
                                              onChange={(value) => {
                                                handleChange(
                                                  mainKey,
                                                  nestedKey,
                                                  value,
                                                  mainObject
                                                );
                                              }}
                                              clearable
                                              searchable
                                              nothingFoundMessage="Nothing found..."
                                            />
                                          ) : (
                                            <Input.Wrapper
                                              label={useFormatTextKey(nestedKey)}
                                            >
                                              <Input
                                                disabled
                                                label={useFormatTextKey(nestedKey)}
                                                placeholder={`No options`}
                                                type="text"
                                              />
                                            </Input.Wrapper>
                                          )}
                                        </>
                                      ) : (
                                        <>
                                          <Input.Wrapper
                                            label={useFormatTextKey(nestedKey)}
                                          >
                                            <Input
                                            onChange={(value) => {
                                              handleTextChange(
                                                mainKey,
                                                nestedKey,
                                                value.target.value,
                                              );
                                            }}
                                              label={useFormatTextKey(nestedKey)}
                                              placeholder={`Enter ${useFormatTextKey(nestedKey)}`}
                                              type="text"
                                            />
                                          </Input.Wrapper>
                                        </>
                                      )}
                                    </div>
                                  )
                                )}
                              </div>
                              </>
                            )
                          }
                        </div>
                      )}
                      <hr className="my-3" />
                    </div>
                  )}
                </div>
              ))}
            </div>
          ))}
        {/* source */}
        {response?.map((mainObject, index) => (
          <div key={index}>
            {Object.entries(mainObject).map(([mainKey, nestedObject]) => (
              <div key={mainKey} className="nested-section flex w-full">
                {mainKey === "source" && (
                  <div>
                    <h3 className="text-primary font-bold">
                      {mainKey.charAt(0).toUpperCase() + mainKey.slice(1)}
                    </h3>

                    {nestedObject.length > 0
                      ? nestedObject.map((link, i) => (
                          <div key={i}>
                            <a
                              className="text-blue-500 underline underline-offset-1"
                              href={link.link}
                              target="_blank"
                              download={link.link}
                            >
                              {" "}
                              File Name : {link.name}{" "}
                            </a>
                          </div>
                        ))
                      : ""}
                  </div>
                )}
              </div>
            ))}
          </div>
        ))}

      </div>
      <Button
        onClick={sendData}
        disabled={assetType == "" || loading}
        className={`mt-4 ${
          assetType == "" || loading
            ? `bg-secondary-primary-lite cursor-not-allowed`
            : `bg-primary`
        }`}
      >
        Submit
        { loading && <Loading />}
      </Button>
    </>
  );
};

export default CustomFactorApi;
