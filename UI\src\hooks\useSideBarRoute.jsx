import { useAuth } from "@/Contexts/AuthContext";
import useRoutes from "@/Routes/useRoutes";
import {
  AdminIcons,
  CarbonIcon,
  DataFoundationIcon,
  DocumentCenterIcon,
  GrcIcon,
  GreenHubIcons,
  ReportingIcon,
  UserDashboard,
} from "@/assets/icons/MainSideBarIcons";
import {
  AnalyticsReporting,
  AntiGreenwashing,
  Auditicon,
  IROIdentification,
  MitigationStrategies,
} from "@/assets/icons/RiskManagment";
import Exit2 from "@/assets/images/Exit2.svg";
import {
  AntigreenwashingIcon,
  ConnectorsIcon,
  CSRDReadinessIcon,
  DashboardsDecisionSightIcon,
  DataQualityDecisionSightIcon,
  DecarbonisationIcon,
  DocVaultIcon,
  DoubleMaterialityIcon,
  EmissionsCalculationIcon,
  GreenHubIcon,
  GreenSightIcon,
  ISSBReadinessIcon,
  IssbReportingSuiteIcon,
  NewIssbReportingSuiteIcon,
  LaunchpadIcon,
  RecommendationsDecisionSightIcon,
  SDGAlignmentIcon,
  StakeholderEngagementIcon,
  SupplyChainIcon,
  FinancedEmissionsIcon,
  DataValidationIcon,
} from "@/assets/svg/ImageSVG.jsx";

import { IoIosWarning } from "react-icons/io";
import {
  IoSettingsSharp,
} from "react-icons/io5";

import { UserDashboardIcon } from "@/assets/icons/MainSideBarIcons.jsx";


// Todo: admin icon to the page directly
// Todo: settings side bar
const useSideBarRoute = () => {

  const GetStart = [
    {
      menuTitle: "Dashboard",
      systemIcon: <UserDashboardIcon />,

      menuLinks: [
        {
          // Icon: EmissionsCalculationIcon,
          title: "Your Dashboard",
          url: "/dashboard/notifications",
          // systemName: "Emissions Calculation",
          svg: true,
        },

        {
          Icon: SDGAlignmentIcon,
          svg: true,
          title: "Ex. Dashboards",
          url: "/dashboard/DecisionSight",
          systemName: "DecisionSight",
          S1Name: "SDG Assessment",
        },
      ],
    },
    {
      menuTitle: "Carbon",
      systemIcon: <CarbonIcon />,

      menuLinks: [
        {
          Icon: EmissionsCalculationIcon,
          title: "Emissions Calculation",
          url: "/net-zero/general",
          systemName: "Emissions Calculation",
          svg: true,
        },
        {
          Icon: SupplyChainIcon,
          title: "Scope3Connect",
          url: "/net-zero/suppliers/Add-Supplier",
          systemName: "Supply Chain",
          svg: true,
        },
        {
          Icon: DecarbonisationIcon,
          title: "Decarbonisation",
          url: "/net-zero/decarbonize",
          systemName: "Decarbonisation",
          svg: true,
        },
        {
          Icon: FinancedEmissionsIcon,
          title: "Financed Emissions",
          url: "/net-zero/financed-emissions",
          systemName: "",
          svg: true,
        },
      ],
    },

    //Levelup GRC
    {
      menuTitle: "LevelUp GRC",
      systemIcon: <GrcIcon />,

      menuLinks: [
        {
          Icon: <IoIosWarning className="text-2xl text-[#6E6E6E]" />,
          title: "Integrated Risk & Audit",
          url: "/green-shield/financial/ESG-risk-management/main",
        },

        {
          Icon: <MitigationStrategies className="text-sm w-6 h-6 " />,
          title: "Due Diligence",
          url: "/Grc/due-diligence",
        },
        // csrd readniess + issb readniess
        {
          Icon: <IROIdentification className="text-sm w-6 h-6 " />,
          title: "Regulatory Readiness",
          url: "/Grc/regulatory-readiness",
        },
        {
          Icon: <Auditicon className="text-sm w-6 h-6 " />,
          title: "ChainSight",
          url: "/Grc/chain-sight",
        },
        {
          Icon: <AnalyticsReporting className="text-sm w-6 h-6 " />,
          title: "Anti-greenwashing Rule",
          url: "/Grc/anti-green-washing",
        },
      ],
    },

    // Reporting
    {
      menuTitle: "Insights & Reporting",
      systemIcon: <ReportingIcon />,

      menuLinks: [
        {
          Icon: GreenSightIcon,
          svg: true,
          title: "Sustain360",
          url: "/Insights-reporing/greensight",
          systemName: "GreenSight AI",
          S1Name: "GreenSight AI",
        },
        {
          Icon: StakeholderEngagementIcon,
          title: "Stakeholder Engagement",
          svg: true,
          url: "/Insights-reporing/stakholder-engagment",
          systemName: "Stakeholder Engagement",
        },
        {
          Icon: DoubleMaterialityIcon,
          svg: true,
          title: "Materiality Assessment",
          url: "/Insights-reporing/materiality-assessment",
          systemName: "Double Materiality",
          S1Name: "Double Materiality",
        },
        {
          Icon: NewIssbReportingSuiteIcon,
          svg: true,
          title: "ESG Reporting Suite",
          url: "/Insights-reporing/issb-reporting-suite",
          systemName: "ISSB Readiness",
          S1Name: "ISSB Readiness",
        },
        {
          Icon: CSRDReadinessIcon,
          svg: true,
          title: "FinanceGuard",
          url: "/Insights-reporing/financeguard",
          // systemName: "SDG Assessment",
          // S1Name: "SDG Assessment",
        },
        {
          Icon: CSRDReadinessIcon,
          svg: true,
          title: "SDG & Impact",
          url: "/Insights-reporing/sdg-impact",
          // systemName: "SDG Assessment",
          // S1Name: "SDG Assessment",
        },
      ],
    },

    // Data Foundation
    {
      menuTitle: "Data Foundation",
      systemIcon: <DataFoundationIcon />,
      menuLinks: [

        {
          Icon: ConnectorsIcon,
          svg: true,
          title: "Data Collection",
          url: "/Data-foundation/data-collection",
          systemName: "Connectors",
        },
        {
          Icon: DataValidationIcon,
          svg: true,
          title: "Data Validation",
          url: "/net-zero/data-validation",
          systemName: "Connectors",
        },
      ],
    },

    // Document Center
    {
      menuTitle: "Document Center",
      systemIcon: <DocumentCenterIcon />,
      menuLinks: [
        {
          Icon: DocVaultIcon,
          svg: true,
          title: "DocVault",
          url: "/Document-Center/docvault",
          systemName: "DocVault",
        },
      ],
    },

    // Green Hub
    {
      menuTitle: "GreenHub",
      systemIcon: <GreenHubIcons />,
      menuLinks: [  
        {
          svg: true,
          title: "Your GreenHub",
          url: "/green-shield/main-page",
        },
        {
          svg: true,
          title: "Peer Community",
          url: "/green-shield/peers-community",
        },
        {
          svg: true,
          title: "Academy",
          url: "/green-shield/academy",
        },
        {
          svg: true,
          title: "Resources",
          url: "/green-shield/resources",
        },
      ],
    },
  ];

  const adminPages = [
    {
      menuTitle: "Admin Pages",
      systemIcon: <AdminIcons />,
      menuLinks: [
        {
          Icon: <IoSettingsSharp className="text-xl" />,
          title: "Admin Pages",
          url: "/admin-pages/manage-company",
        },
        {
          Icon: <IoSettingsSharp className="text-xl" />,
          title: "Academy Dashbaord",
          url: "/admin-pages/green-hub/dashboard/dash",
        },
      ],
    },
  ];

  // const generalMenu = {
  //   menuTitle: "Preferences",
  //   menuLinks: [
  //     {
  //       Icon: <IoSettingsSharp className="text-xl" />,
  //       title: "Settings",
  //       url: "/Settings",
  //     },
  //     {
  //       Icon: <PiQuestionFill className="text-xl" />,
  //       title: "Support",
  //       url: "/support",
  //     },
  //     {
  //       Icon: (
  //         <Image
  //           loading="lazy"
  //           src={Exit2}
  //           alt="Logout Icon"
  //           className="text-xs mr-2 w-5"
  //         />
  //       ),
  //       title: "Logout",
  //       url: "logout",
  //     },
  //   ],
  // };

  // const MainMenu = [
  //   {
  //     menuTitle: "",
  //     systemIcon: <UserDashboard />,
  //     menuLinks: [
  //       {
  //         Icon: LaunchpadIcon,
  //         svg: true,
  //         title: "Launchpad",
  //         url: "/get-started",
  //       },
  //     ],
  //   },
  //   {
  //     menuTitle: "LevelUp Net Zero",
  //     menuLinks: [
  //       {
  //         Icon: EmissionsCalculationIcon,
  //         title: "Emissions Calculation",
  //         url: "/net-zero/general",
  //         systemName: "Emissions Calculation",
  //         svg: true,
  //       },
  //       {
  //         Icon: SupplyChainIcon,
  //         title: "Supply Chain",
  //         url: "/net-zero/suppliers/Add-Supplier",
  //         systemName: "Supply Chain",
  //         svg: true,
  //       },
  //       {
  //         Icon: DecarbonisationIcon,
  //         title: "Decarbonisation",
  //         url: "/net-zero/decarbonize",
  //         systemName: "Decarbonisation",
  //         svg: true,
  //       },
  //       {
  //         Icon: FinancedEmissionsIcon,
  //         title: "Financed Emissions",
  //         url: "/net-zero/financed-emissions",
  //         systemName: "",
  //         svg: true,
  //       },
  //     ],
  //   },
  //   {
  //     menuTitle: "Compass Suite",
  //     menuLinks: [
  //       {
  //         Icon: GreenSightIcon,
  //         svg: true,
  //         title: "GreenSight",
  //         url: "/diagnosis/General/question/1",
  //         systemName: "GreenSight AI",
  //         S1Name: "GreenSight AI",
  //       },
  //       {
  //         Icon: StakeholderEngagementIcon,
  //         title: "Stakeholder Engagement",
  //         svg: true,
  //         url: "/green-shield/financial/ESG-incident-management",
  //         systemName: "Stakeholder Engagement",
  //       },
  //       {
  //         Icon: DoubleMaterialityIcon,
  //         svg: true,
  //         title: "Double Materiality",
  //         url: "/doubleMateriality",
  //         systemName: "Double Materiality",
  //         S1Name: "Double Materiality",
  //       },
  //       {
  //         Icon: NewIssbReportingSuiteIcon,
  //         svg: true,
  //         title: "ESG Reporting",
  //         url: "/issb-reporting-suite",
  //         systemName: "ISSB Readiness",
  //         S1Name: "ISSB Readiness",
  //       },
  //       {
  //         Icon: CSRDReadinessIcon,
  //         svg: true,
  //         title: "CSRD Readiness",
  //         url: "/csrd-dashboard",
  //         systemName: "CSRD Readiness",
  //         S1Name: "CSRD Readiness",
  //       },
  //       {
  //         Icon: SDGAlignmentIcon,
  //         svg: true,
  //         title: "SDG Measurement",
  //         url: "/net-zero/decarbonize",
  //         systemName: "SDG Alignment & Impact Measurement",
  //         S1Name: "SDG Assessment",
  //       },
  //       {
  //         Icon: AntigreenwashingIcon,
  //         svg: true,
  //         title: "Anti-GreenWashing",
  //         url: "/green-shield/compilance/CompilanceView",
  //         systemName: "Anti-greenwashing Compliance",
  //       },
  //     ],
  //   },
  //   generalMenu,
  // ];

  // // const GetStart = [
  // //   {
  // //     menuTitle: "User Dashboard",
  // //     systemIcon: <UserDashboard />,
  // //     menuLinks: [
  // //       {
  // //         Icon: LaunchpadIcon,
  // //         svg: true,
  // //         title: "Launchpad",
  // //         url: "/get-started",
  // //       },
  // //     ],
  // //   },
  // //   {
  // //     menuTitle: "LevelUp Net Zero",
  // //     systemIcon: <CarbonIcon />,

  // //     menuLinks: [
  // //       {
  // //         Icon: EmissionsCalculationIcon,
  // //         title: "Emissions Calculation",
  // //         url: "/net-zero/general",
  // //         systemName: "Emissions Calculation",
  // //         svg: true,
  // //       },
  // //       {
  // //         Icon: SupplyChainIcon,
  // //         title: "Supply Chain",
  // //         url: "/net-zero/suppliers/Add-Supplier",
  // //         systemName: "Supply Chain",
  // //         svg: true,
  // //       },
  // //       {
  // //         Icon: DecarbonisationIcon,
  // //         title: "Decarbonisation",
  // //         url: "/net-zero/decarbonize",
  // //         systemName: "Decarbonisation",
  // //         svg: true,
  // //       },
  // //       {
  // //         Icon: FinancedEmissionsIcon,
  // //         title: "Financed Emissions",
  // //         url: "/net-zero/financed-emissions",
  // //         systemName: "",
  // //         svg: true,
  // //       },
  // //     ],
  // //   },
  // //   {
  // //     menuTitle: "Compass Suite",
  // //     menuLinks: [
  // //       {
  // //         Icon: GreenSightIcon,
  // //         svg: true,
  // //         title: "GreenSight",
  // //         url: "/diagnosis/General/question/1",
  // //         systemName: "GreenSight AI",
  // //         S1Name: "GreenSight AI",
  // //       },
  // //       {
  // //         Icon: StakeholderEngagementIcon,
  // //         title: "Stakeholder Engagement",
  // //         svg: true,
  // //         url: "/green-shield/financial/ESG-incident-management",
  // //         systemName: "Stakeholder Engagement",
  // //       },
  // //       {
  // //         Icon: DoubleMaterialityIcon,
  // //         svg: true,
  // //         title: "Double Materiality",
  // //         url: "/doubleMateriality",
  // //         systemName: "Double Materiality",
  // //         S1Name: "Double Materiality",
  // //       },
  // //       {
  // //         Icon: NewIssbReportingSuiteIcon,
  // //         svg: true,
  // //         title: "ESG Reporting",
  // //         url: "/issb-reporting-suite",
  // //         systemName: "ISSB Readiness",
  // //         S1Name: "ISSB Readiness",
  // //       },
  // //       {
  // //         Icon: CSRDReadinessIcon,
  // //         svg: true,
  // //         title: "CSRD Readiness",
  // //         url: "/csrd-dashboard",
  // //         systemName: "CSRD Readiness",
  // //         S1Name: "CSRD Readiness",
  // //       },
  // //       {
  // //         Icon: SDGAlignmentIcon,
  // //         svg: true,
  // //         title: "SDG Measurement",
  // //         url: "/net-zero/decarbonize",
  // //         systemName: "SDG Alignment & Impact Measurement",
  // //         S1Name: "SDG Assessment",
  // //       },
  // //       {
  // //         Icon: AntigreenwashingIcon,
  // //         svg: true,
  // //         title: "Anti-GreenWashing",
  // //         url: "/green-shield/compilance/CompilanceView",
  // //         systemName: "Anti-greenwashing Compliance",
  // //       },
  // //     ],
  // //   },
  // //   {
  // //     menuTitle: "Green Shield",
  // //     menuLinks: [
  // //       {
  // //         Icon: IntegratedRiskIcon,
  // //         svg: true,
  // //         title: "Integrated Risk",
  // //         url: "/green-shield/financial/ESG-risk-management",
  // //         systemName: "Integrated Risk Management",
  // //       },

  // //       {
  // //         Icon: ESGDueDiligenceIcon,
  // //         svg: true,
  // //         title: "ESG DD",
  // //         url: "/due-diligence",
  // //         systemName: "ESG Due Diligence",
  // //         S1Name: "ESG Due Diligence",
  // //       },
  // //       {
  // //         Icon: HumanRightsIcon,
  // //         svg: true,
  // //         title: "Human Rights DD",
  // //         url: "/hrdd",
  // //         systemName: "Human Rights Due Diligence",
  // //         S1Name: "Human Rights Due Diligence (HRDD)",
  // //       },
  // //       {
  // //         Icon: DecisionSightIcon,
  // //         svg: true,
  // //         title: "DecisionSight",
  // //         url: "/DecisionSight/Dashboard",
  // //         systemName: "DecisionSight",
  // //       },
  // //     ],
  // //   },
  // //   {
  // //     menuTitle: "Enterprise Backbone",
  // //     menuLinks: [
  // //       {
  // //         Icon: ConnectorsIcon,
  // //         svg: true,
  // //         title: "Data Collection",
  // //         url: "/net-zero/Connectors",
  // //         systemName: "Connectors",
  // //       },
  // //       {
  // //         Icon: DataValidationIcon,
  // //         svg: true,
  // //         title:"Data Validation",
  // //         url: "/net-zero/data-validation",
  // //         systemName: "Connectors",
  // //       },
  // //       {
  // //         Icon: DocVaultIcon,
  // //         svg: true,
  // //         title: "DocVault",
  // //         url: "/docvault",
  // //         systemName: "DocVault",
  // //       },
  // //     ],
  // //   },
  // //   {
  // //     menuTitle: "Menu",
  // //     menuLinks: [
  // //       {
  // //         Icon: GreenHubIcon,
  // //         svg: true,
  // //         title: "GreenHub",
  // //         url: GreenHub.path,
  // //       },
  // //       {
  // //         Icon: GreenHubIcon,
  // //         svg: true,
  // //         title: "GreenHubDashboard",
  // //         url: GreenHubDashboard.path,
  // //       },
  // //       superAdmin && {
  // //         Icon: <MdManageAccounts className={`text-xl`} />,
  // //         title: "Super Admin",
  // //         url: ManageCompanies.path,
  // //       },
  // //     ].filter(Boolean),
  // //   },
  // //   generalMenu,
  // // ];

  // // const getStartMenu = [
  // //   {
  // //     menuTitle: "",
  // //     menuLinks: [
  // //       {
  // //         Icon: LaunchpadIcon,
  // //         title: "Launchpad",
  // //         url: "/get-started",
  // //         svg: true,
  // //       },
  // //     ],
  // //   },
  // //   {
  // //     menuTitle: "Menu",
  // //     menuLinks: [
  // //       {
  // //         Icon: <FaLeaf className="text-xl" />,
  // //         title: "Green Hub",
  // //         url: GreenHub.path,
  // //       },
  // //       {
  // //         Icon: <FaLeaf className="text-xl" />,
  // //         title: "Green Hub Dashboard",
  // //         url: GreenHubDashboard.path,
  // //       },
  // //       superAdmin && {
  // //         Icon: <MdManageAccounts className={`text-xl`} />,
  // //         title: "Super Admin",
  // //         url: ManageCompanies.path,
  // //       },
  // //     ].filter(Boolean),
  // //   },
  // //   generalMenu,
  // // ];

  // const assessmentMenu = [
  //   {
  //     menuTitle: "",
  //     menuLinks: [
  //       {
  //         Icon: LaunchpadIcon,
  //         svg: true,
  //         title: "Launchpad",
  //         url: "/get-started",
  //       },
  //     ],
  //   },
  //   {
  //     menuTitle: "Menu",
  //     menuLinks: [
  //       // {
  //       //  Icon: <IoDocumentsOutline className="text-xl" />,
  //       //  title: "ESG Compass Suite",
  //       //  url: "/select-assessment",
  //       // },
  //       {
  //         Icon: <FaLeaf className="text-xl" />,
  //         title: "Green Hub",
  //         url: GreenHub.path,
  //       },
  //       {
  //         Icon: <FaLeaf className="text-xl" />,
  //         title: "Green Hub Dashoard",
  //         url: GreenHubDashboard.path,
  //       },
  //     ],
  //   },
  //   generalMenu,
  // ];

  // const esgAssessmentMenu = [
  //   {
  //     menuTitle: "",
  //     menuLinks: [
  //       {
  //         Icon: LaunchpadIcon,
  //         svg: true,
  //         title: "Launchpad",
  //         url: "/get-started",
  //       },
  //     ],
  //   },
  //   {
  //     menuTitle: "Menu",
  //     menuLinks: [
  //       // {
  //       //  Icon: <IoDocumentsOutline className="text-xl" />,
  //       //  title: "ESG Compass Suite",
  //       //  url: "/select-assessment",
  //       // },

  //       {
  //         Icon: <MdOutlineAssessment className="text-xl" />,
  //         title: "Dashboard",
  //         url: "/dashboard",
  //       },
  //       {
  //         Icon: <FaRegCheckCircle className="text-xl" />,
  //         title: "Reporting",
  //         url: "/result",
  //       },
  //       {
  //         Icon: <FaLeaf className="text-xl" />,
  //         title: "Green Hub",
  //         url: GreenHub.path,
  //       },
  //     ],
  //   },
  //   generalMenu,
  // ];

  // const DoubleMaterialityAssessment = [
  //   {
  //     menuTitle: "",
  //     menuLinks: [
  //       {
  //         Icon: LaunchpadIcon,
  //         svg: true,
  //         title: "Launchpad",
  //         url: "/get-started",
  //       },
  //     ],
  //   },
  //   {
  //     menuTitle: "Menu",
  //     menuLinks: [
  //       // {
  //       //  Icon: <IoDocumentsOutline className="text-xl" />,
  //       //  title: "ESG Compass Suite",
  //       //  url: "/select-assessment",
  //       // },
  //       {
  //         Icon: <PiNewspaperFill className="text-xl" />,
  //         title: "DMR",
  //         url: "/doubleMateriality",
  //       },
  //       {
  //         Icon: <RiFilePaperFill className="text-xl" />,
  //         title: "DMS",
  //         url: "/doubleMateriality/Assessment",
  //       },
  //       {
  //         Icon: <PiNewspaperClippingFill className="text-xl" />,
  //         title: "Reporting",
  //         url: "/doubleMateriality/Reporting",
  //       },
  //       {
  //         Icon: <FaLeaf className="text-xl" />,
  //         title: "Green Hub",
  //         url: GreenHub.path,
  //       },
  //     ],
  //   },
  //   generalMenu,
  // ];

  // const csrdReadinessMenu = [
  //   {
  //     menuTitle: "",
  //     menuLinks: [
  //       {
  //         Icon: LaunchpadIcon,
  //         svg: true,
  //         title: "Launchpad",
  //         url: getStart.path,
  //       },
  //     ],
  //   },
  //   {
  //     menuTitle: "Menu",
  //     menuLinks: [
  //       // {
  //       //  Icon: <IoDocumentsOutline className="text-xl" />,
  //       //  title: "ESG Compass Suite",
  //       //  url: "/select-assessment",
  //       // },
  //       {
  //         Icon: <MdHomeFilled className="text-xl" />,
  //         title: "CSRD Dashboard",
  //         url: "/csrd-dashboard",
  //       },
  //       {
  //         Icon: <PiNewspaperClippingFill className="text-xl" />,
  //         title: "Report ",
  //         url: "/csrd-report",
  //       },
  //       {
  //         Icon: <FaLeaf className="text-xl" />,
  //         title: "Green Hub",
  //         url: GreenHub.path,
  //       },
  //     ],
  //   },
  //   generalMenu,
  // ];
  // const issbMenu = [
  //   {
  //     menuTitle: "",
  //     menuLinks: [
  //       {
  //         Icon: LaunchpadIcon,
  //         svg: true,
  //         title: "Launchpad",
  //         url: getStart.path,
  //       },
  //     ],
  //   },
  //   {
  //     menuTitle: "ESG Reporting",
  //     menuLinks: [
  //       // {
  //       //  Icon: <IoDocumentsOutline className="text-xl" />,
  //       //  title: "ESG Compass Suite",
  //       //  url: "/select-assessment",
  //       // },
  //       {
  //         Icon: <MdHomeFilled className="text-xl" />,
  //         title: "ESG Home",
  //         url: "/issb-reporting-suite",
  //       },
  //       {
  //         Icon: <IssbReportingSuiteIcon className="text-xl" />,
  //         title: "Reporting",
  //         url: "/esg-reporting",
  //       },
  //       {
  //         Icon: <ISSBReadinessIcon className="text-xl" />,
  //         title: "ISSB Readiness",
  //         url: issbStart.path,
  //       },
  //       {
  //         Icon: <IoIosPaper className="text-xl" />,
  //         title: "Report",
  //         url: issbReport.path,
  //       },
  //       {
  //         Icon: <FaLeaf className="text-xl" />,
  //         title: "Green Hub",
  //         url: GreenHub.path,
  //       },
  //     ],
  //   },
  //   generalMenu,
  // ];

  // const sdgMenu = [
  //   {
  //     menuTitle: "",
  //     menuLinks: [
  //       {
  //         Icon: LaunchpadIcon,
  //         svg: true,
  //         title: "Launchpad",
  //         url: getStart.path,
  //       },
  //     ],
  //   },
  //   {
  //     menuTitle: "Menu",
  //     menuLinks: [
  //       // {
  //       //  Icon: <IoDocumentsOutline className="text-xl" />,
  //       //  title: "ESG Compass Suite",
  //       //  url: "/select-assessment",
  //       // },
  //       {
  //         Icon: <MdHomeFilled className="text-xl" />,
  //         title: "SDGs Alignment Assessment",
  //         url: "/sdg",
  //       },
  //       {
  //         Icon: <PiNewspaperFill className="text-xl" />,
  //         title: "Impact Measurement Assessment",
  //         url: "/sdg/impact-measure",
  //       },
  //       {
  //         Icon: <PiNewspaperClippingFill className="text-xl" />,
  //         title: "Report",
  //         url: "/sdg/report",
  //       },
  //       {
  //         Icon: <FaLeaf className="text-xl" />,
  //         title: "Green Hub",
  //         url: GreenHub.path,
  //       },
  //     ],
  //   },
  //   generalMenu,
  // ];

  // const hrdd = [
  //   {
  //     menuTitle: "",
  //     menuLinks: [
  //       {
  //         Icon: LaunchpadIcon,
  //         svg: true,
  //         title: "Launchpad",
  //         url: getStart.path,
  //       },
  //     ],
  //   },
  //   {
  //     menuTitle: "Menu",
  //     menuLinks: [
  //       // {
  //       //  Icon: <IoDocumentsOutline className="text-xl" />,
  //       //  title: "ESG Compass Suite",
  //       //  url: "/select-assessment",
  //       // },
  //       {
  //         Icon: <MdHomeFilled className="text-xl" />,
  //         title: "HRDD Home",
  //         url: hdrrPage.path,
  //       },
  //       {
  //         Icon: <PiNewspaperFill className="text-xl" />,
  //         title: "Report",
  //         url: "/hrdd-report",
  //       },
  //       {
  //         Icon: <FaLeaf className="text-xl" />,
  //         title: "Green Hub",
  //         url: GreenHub.path,
  //       },
  //     ],
  //   },
  //   generalMenu,
  // ];

  // const dueDiligence = [
  //   {
  //     menuTitle: "",
  //     menuLinks: [
  //       {
  //         Icon: LaunchpadIcon,
  //         svg: true,
  //         title: "Launchpad",
  //         url: getStart.path,
  //       },
  //     ],
  //   },
  //   {
  //     menuTitle: "Menu",
  //     menuLinks: [
  //       // {
  //       //  Icon: <IoDocumentsOutline className="text-xl" />,
  //       //  title: "ESG Compass Suite",
  //       //  url: "/select-assessment",
  //       // },
  //       {
  //         Icon: <MdHomeFilled className="text-xl" />,
  //         title: "Due Diligence Home",
  //         url: dueDiligencePage.path,
  //       },
  //       {
  //         Icon: <PiNewspaperFill className="text-xl" />,
  //         title: "Report",
  //         url: "/dueDiligence-report",
  //       },
  //       {
  //         Icon: <FaLeaf className="text-xl" />,
  //         title: "Green Hub",
  //         url: GreenHub.path,
  //       },
  //     ],
  //   },
  //   generalMenu,
  // ];
  // const DecisionSightMenu = [
  //   {
  //     menuTitle: "",
  //     menuLinks: [
  //       {
  //         Icon: LaunchpadIcon,
  //         svg: true,
  //         title: "Launchpad",
  //         url: getStart.path,
  //       },
  //     ],
  //   },
  //   {
  //     menuTitle: "Decision Sight",
  //     menuLinks: [
  //       {
  //         Icon: DashboardsDecisionSightIcon,
  //         title: "Dashboards",
  //         url: "/DecisionSight/Dashboard",
  //         svg: true,
  //       },
  //       {
  //         Icon: RecommendationsDecisionSightIcon,
  //         title: "Recommendations",
  //         url: "/DecisionSight/DSRecommendations",
  //         svg: true,
  //       },
  //       {
  //         Icon: DataQualityDecisionSightIcon,
  //         title: "Data Quality",
  //         url: "/DecisionSight/DataQuality",
  //         svg: true,
  //       },
  //       {
  //         Icon: DataQualityDecisionSightIcon,
  //         title: "Scenario Planning",
  //         url: "/DecisionSight/ScenarioPlanning",
  //         svg: true,
  //       },
  //     ],
  //   },
  //   {
  //     menuTitle: "Enterprise Backbone",
  //     menuLinks: [
  //       {
  //         Icon: ConnectorsIcon,
  //         svg: true,
  //         title: "Data Collection",
  //         url: "/net-zero/Connectors",
  //         systemName: "Connectors",
  //       },
  //       {
  //         Icon: DataValidationIcon,
  //         svg: true,
  //         title: "Data Validation",
  //         url: "/net-zero/data-validation",
  //         systemName: "Connectors",
  //       },
  //       {
  //         Icon: DocVaultIcon,
  //         svg: true,
  //         title: "DocVault",
  //         url: "/docvault",
  //         systemName: "DocVault",
  //       },
  //       {
  //         Icon: GreenHubIcon,
  //         svg: true,
  //         title: "GreenHub",
  //         url: GreenHub.path,
  //       },
  //     ],
  //   },
  //   generalMenu,
  // ];

  // const netZeroMenu = [
  //   {
  //     menuTitle: "",
  //     menuLinks: [
  //       {
  //         Icon: LaunchpadIcon,
  //         svg: true,
  //         title: "Launchpad",
  //         url: getStart.path,
  //       },
  //     ],
  //   },
  //   {
  //     menuTitle: "LevelUp Net Zero",
  //     menuLinks: [
  //       {
  //         Icon: <MdDashboard className="text-xl" />,
  //         title: "Emission Overview",
  //         url: netZerosEmissionOverview.path,
  //       },

  //       {
  //         Icon: <IoPieChartOutline className="text-xl" />,
  //         title: "Collect",
  //         url: netZerosCollect.path,
  //       },
  //       {
  //         Icon: <CgSearchFound className="text-xl" />,
  //         title: "Review",
  //         url: netZerosReview.path,
  //       },
  //       {
  //         Icon: <IoStatsChart className="text-xl" />,
  //         title: "Measure",
  //         url: netZerosMeasure.path,
  //       },
  //       {
  //         Icon: <TbReportAnalytics className="text-xl" />,
  //         title: "Net Zero’s Report",
  //         url: netZerosReport.path,
  //       },
  //       {
  //         Icon: <CgCodeClimate className="text-2xl" />,
  //         title: "Decarbonise",
  //         url: netZerosDecarbonize.path,
  //       },
  //       {
  //         Icon: <FinancedEmissionsIcon className="text-xl" />,
  //         title: "Financed Emissions",
  //         url: netZerosFinancedEmissions.path,
  //       },
  //       {
  //         Icon: <FiBox className="text-xl" />,
  //         title: "Supply Chain",
  //         url: netZerosSuppliersInputs.path,
  //       },
  //       {
  //         Icon: <FaLeaf className="text-xl" />,
  //         title: "Green Hub",
  //         url: GreenHub.path,
  //       },
  //     ],
  //   },
  //   {
  //     menuTitle: "Connectors",
  //     menuLinks: [
  //       {
  //         Icon: <PiPlugsConnectedBold className="text-xl" />,
  //         title: "Connectors",
  //         // url: netZerosConnector.path,
  //       },
  //     ],
  //   },
  //   {
  //     menuTitle: "Company Configuration",
  //     menuLinks: [
  //       {
  //         Icon: <BiSolidCustomize className="text-xl" />,
  //         title: "Custom Factor",
  //         url: "/Settings/CompanyProfile/Configurations/CustomFactor",
  //       },
  //       {
  //         Icon: <MdOutlineWebAsset className="text-xl" />,
  //         title: "Assets",
  //         url: "/Settings/CompanyProfile/Configurations/Assets",
  //       },
  //     ],
  //   },
  //   generalMenu,
  // ];

  // const greenShieldMenu = [
  //   {
  //     menuTitle: "",
  //     menuLinks: [
  //       {
  //         Icon: LaunchpadIcon,
  //         svg: true,
  //         title: "Launchpad",
  //         url: getStart.path,
  //       },
  //       // {
  //       //   Icon: "",
  //       //   title: "",
  //       //   url: "",
  //       // },
  //     ],
  //   },
  //   {
  //     menuTitle: "Menu",
  //     menuLinks: [
  //       {
  //         Icon: <GoHomeFill className="text-xl" />,
  //         title: "Home",
  //         url: "/green-shield/financial-service-start",
  //       },
  //       {
  //         Icon: <FaLeaf className="text-xl" />,
  //         title: "Green Hub",
  //         url: GreenHub.path,
  //       },
  //     ],
  //   },
  //   generalMenu,
  // ];
  // const compilanceMenu = [
  //   {
  //     menuTitle: "",
  //     menuLinks: [
  //       {
  //         Icon: LaunchpadIcon,
  //         svg: true,
  //         title: "Launchpad",
  //         url: getStart.path,
  //       },
  //       // {
  //       //   Icon: "",
  //       //   title: "",
  //       //   url: "",
  //       // },
  //     ],
  //   },
  //   {
  //     menuTitle: "Menu",
  //     menuLinks: [
  //       {
  //         Icon: <IoNewspaperSharp className="text-xl" />,
  //         title: "Compilance",
  //         url: `/green-shield/compilance/CompilanceView`,
  //       },
  //       {
  //         Icon: <BiBookContent className="text-xl" />,
  //         title: "Dashboard",
  //         url: `/green-shield/compilance/CompilanceView?tab=dashboard`,
  //       },
  //       {
  //         Icon: <BiListCheck className="text-xl" />,
  //         title: "Compliance Assessment",
  //         url: `/green-shield/compilance/CompilanceView?tab=assessment`,
  //       },
  //       {
  //         Icon: <BsArrowRepeat className="text-xl" />,
  //         title: "Regulations Tracker",
  //         url: `/green-shield/compilance/CompilanceView?tab=tracker`,
  //       },
  //       {
  //         Icon: <FaLeaf className="text-xl" />,
  //         title: "Green Hub",
  //         url: GreenHub.path,
  //       },
  //     ],
  //   },
  //   generalMenu,
  // ];

  // const MainCardMenu = [
  //   {
  //     menuTitle: "",
  //     menuLinks: [
  //       {
  //         Icon: LaunchpadIcon,
  //         svg: true,
  //         title: "Launchpad",
  //         url: getStart.path,
  //       },
  //     ],
  //   },
  //   {
  //     menuTitle: "Menu",
  //     menuLinks: [
  //       {
  //         Icon: <GoHomeFill className="text-xl" />,
  //         title: "Home",
  //         url: "/green-shield/general",
  //       },
  //       {
  //         Icon: <FaLeaf className="text-xl" />,
  //         title: "Green Hub",
  //         url: GreenHub.path,
  //       },
  //     ],
  //   },
  //   generalMenu,
  // ];

  // const ESGRiskManagementMenu = [
  //   {
  //     menuTitle: "",
  //     menuLinks: [
  //       {
  //         Icon: LaunchpadIcon,
  //         svg: true,
  //         title: "Launchpad",
  //         url: getStart.path,
  //       },
  //     ],
  //   },
  //   {
  //     menuTitle: "Menu",
  //     menuLinks: [
  //       {
  //         Icon: <GoHomeFill className="text-xl" />,
  //         title: "Home",
  //         url: "/green-shield/financial/ESG-risk-management",
  //       },
  //       {
  //         Icon: <IoIosWarning className="text-2xl text-[#6E6E6E]" />,
  //         title: "ESG Risk Universe and Governance",
  //         url: "/green-shield/financial/ESG-risk-management/ESGRiskUniverseAndGovernanceView",
  //       },
  //       {
  //         Icon: <IROIdentification className="text-sm w-6 h-6 " />,
  //         title: "IRO Identification & Assessment",
  //         url: "/green-shield/financial/ESG-risk-management/RiskIdentificationAndAssessment",
  //       },
  //       {
  //         Icon: <MitigationStrategies className="text-sm w-6 h-6 " />,
  //         title: "Mitigation Strategies",
  //         url: "/green-shield/financial/ESG-risk-management/MitigationStrategiesView",
  //       },
  //       {
  //         Icon: <AnalyticsReporting className="text-sm w-6 h-6 " />,
  //         title: "Analytics & Reporting",
  //         url: "/green-shield/financial/ESG-risk-management/AnalyticsAndReporting/AnalyticsReportingView",
  //       },
  //       {
  //         Icon: <Auditicon className="text-sm w-6 h-6 " />,
  //         title: "Audit",
  //         url: "/green-shield/financial/ESG-risk-management/Audit",
  //       },
  //       {
  //         Icon: <AntiGreenwashing className="text-sm w-6 h-6 " />,
  //         title: "Anti-Greenwashing",
  //         url: "/green-shield/financial/green-washing-risk-assessment",
  //       },
  //     ],
  //   },
  //   generalMenu,
  // ];

  // const GreenHubMenu = [
  //   {
  //     menuTitle: "",
  //     menuLinks: [
  //       {
  //         Icon: LaunchpadIcon,
  //         svg: true,
  //         title: "Launchpad",
  //         url: getStart.path,
  //       },
  //     ],
  //   },
  //   {
  //     menuTitle: "Menu",
  //     menuLinks: [
  //       {
  //         Icon: <IoMdCompass className="text-xl" />,
  //         title: "Compass",
  //         url: "#",
  //       },
  //       {
  //         Icon: <MdOutlineShield className="text-xl" />,
  //         title: "Green Hub",
  //         url: GreenHub.path,
  //       },
  //     ],
  //   },
  //   generalMenu,
  // ];

  // const greenShieldESGIncidentManagement = [
  //   {
  //     menuTitle: "",
  //     menuLinks: [
  //       {
  //         Icon: LaunchpadIcon,
  //         svg: true,
  //         title: "Launchpad",
  //         url: getStart.path,
  //       },
  //     ],
  //   },
  //   {
  //     menuTitle: "Menu",
  //     menuLinks: [
  //       {
  //         Icon: <GoHomeFill className="text-xl" />,
  //         title: "Home",
  //         url: "/green-shield/financial-service-start",
  //       },
  //       {
  //         Icon: <RiDashboardFill className="text-xl" />,
  //         title: "Dashboard",
  //         url: "/green-shield/financial/ESG-incident-management",
  //       },
  //       {
  //         Icon: <RiDashboardFill className="text-xl" />,
  //         title: "Incident Management",
  //         url: "/green-shield/financial/ESG-incident-management/IncidentManagement",
  //       },
  //       // {
  //       //   Icon: <IoIosPaper className="text-xl" />,
  //       //   title: "Reporting",
  //       //   url: "/green-shield/financial/ESG-incident-management/Reporting",
  //       // },
  //       // {
  //       //   Icon: <RiFolderWarningFill className="text-xl" />,
  //       //   title: "My Incidents",
  //       //   url: "/green-shield/financial/ESG-incident-management/MyIncidents",
  //       // },
  //       {
  //         Icon: <FaLeaf className="text-xl" />,
  //         title: "Green Hub",
  //         url: GreenHub.path,
  //       },
  //     ],
  //   },
  //   generalMenu,
  // ];

  // const greenShieldNonFiESGIncidentManagement = [
  //   {
  //     menuTitle: "",
  //     menuLinks: [
  //       {
  //         Icon: LaunchpadIcon,
  //         svg: true,
  //         title: "Launchpad",
  //         url: "/green-shield/nonFinancial-services-start",
  //       },
  //     ],
  //   },
  //   {
  //     menuTitle: "Menu",
  //     menuLinks: [
  //       {
  //         Icon: <RiDashboardFill className="text-xl" />,
  //         title: "Dashboard",
  //         url: "/green-shield/nonFinancial/ESG-incident-management",
  //       },
  //       // {
  //       //   Icon: <IoIosPaper className="text-xl" />,
  //       //   title: "Reporting",
  //       //   url: "/green-shield/nonFinancial/ESG-incident-management/Reporting",
  //       // },
  //       // {
  //       //   Icon: <RiFolderWarningFill className="text-xl" />,
  //       //   title: "My Incidents",
  //       //   url: "/green-shield/nonFinancial/ESG-incident-management/MyIncidents",
  //       // },
  //       {
  //         Icon: <FaLeaf className="text-xl" />,
  //         title: "Green Hub",
  //         url: GreenHub.path,
  //       },
  //     ],
  //   },
  //   generalMenu,
  // ];

  // const docvaultMenu = [
  //   {
  //     menuTitle: "",
  //     menuLinks: [
  //       {
  //         Icon: LaunchpadIcon,
  //         svg: true,
  //         title: "Launchpad",
  //         url: getStart.path,
  //       },
  //     ],
  //   },
  //   {
  //     menuTitle: "Enterprise Backbone",
  //     menuLinks: [
  //       {
  //         Icon: <IoMdCompass className="text-xl" />,
  //         title: "Compass",
  //         url: "#",
  //       },
  //       {
  //         Icon: <PiPlugsConnectedBold className="text-xl" />,
  //         title: "Connectors",
  //         // url: netZerosConnector.path,
  //       },
  //       {
  //         Icon: <FaLeaf className="text-xl" />,
  //         title: "Green Hub",
  //         url: GreenHub.path,
  //       },
  //     ],
  //   },
  //   generalMenu,
  // ];

  return {
    GetStart,
    adminPages,
  };
};

export default useSideBarRoute;

//  {
//           Icon: SDGAlignmentIcon,
//           svg: true,
//           title: "DecisionSight",
//           url: "/Grc/DecisionSight",
//           systemName: "DecisionSight",
//           S1Name: "SDG Assessment",
//         },
