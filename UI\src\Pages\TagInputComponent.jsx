import AuthConfig from "@/Api/authConfig";
import { Loader, TagsInput } from "@mantine/core";
import { useEffect, useState } from "react";

function TagInputComponent({ListOfTagsUsers}) {
 const [loading, setLoading] = useState(true);
 const [users, setUsers] = useState([]);

 const handleTagsUsers = (tags) => {
  // console.log(tags);

  const handleTags = tags && tags.map((item) => item.replace(/^@/, ""));
  const selectedTagUser = users.filter((el) =>
   handleTags.includes(el.user_name)
  );
  ListOfTagsUsers(selectedTagUser);
 };
 const optionsFilter = ({ options, search }) => {
  const filtered = options.filter((option) =>
   option.label.toLowerCase().trim().includes(search.toLowerCase().trim())
  );

  filtered.sort((a, b) => a.label.localeCompare(b.label));
  return filtered;
 };
 useEffect(() => {
  const getCompanyUsers = async () => {
   try {
    setLoading(true);
    const { data } = await AuthConfig.get("/get-all-company-users");
    console.log("Fetched users:", data);
    setUsers(data || []);
   } catch (error) {
    console.error("Error fetching users:", error);
   } finally {
    setLoading(false);
   }
  };
  getCompanyUsers();
 }, []);
 return (
  <TagsInput
   label="Your favorite libraries"
   placeholder="Pick value or enter anything"
   data={[...new Set(users.map((item) => `@${item?.user_name}`))]}
   filter={optionsFilter}
   onChange={handleTagsUsers}
   nothingFoundMessage="Nothing found..."
   clearable
   rightSection={loading && <Loader size={18} />}
  />
 );
}
export default TagInputComponent;
