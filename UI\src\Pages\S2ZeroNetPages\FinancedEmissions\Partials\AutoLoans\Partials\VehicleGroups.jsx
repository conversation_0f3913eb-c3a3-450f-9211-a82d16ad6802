import { useState } from "react";
import { Button, Select, TextInput } from "@mantine/core";

const VehicleGroup = ({ group, onRemove, onChange }) => {
  const selectOptions = {
    vehicleCategories: ["Sedan", "SUV", "Truck", "Coupe"],
    fuelTypes: ["Petrol", "Diesel", "Electric", "Hybrid"],
    yearRanges: ["2020-2021", "2021-2022", "2022-2023", "2023-2024"],
    dataSources: ["Bank", "Credit Union", "Online Lender", "Other"],
  };

  const inputFields = [
    {
      label: "Number of Loans",
      field: "vehicleType",
      placeholder: "e.g., 500",
      description: "Loans in this group",
    },
    {
      label: "Total Outstanding ($M)",
      field: "totalOutstanding",
      placeholder: "e.g., 12.5",
      description: "Million USD",
    },
    {
      label: "Avg Vehicle Value ($)",
      field: "avgVehicleValue",
      placeholder: "e.g., 12.5",
      description: "At origination",
    },
    {
      label: "Avg Annual Mileage",
      field: "avgAnnualMileage",
      placeholder: "12000",
      description: "Miles/year",
    },
    {
      label: "Avg Loan Term (years)",
      field: "avgLoanTerm",
      placeholder: "5",
    },
    {
      label: "Emission Factor",
      field: "emissionFactor",
      placeholder: "Auto-Calculated",
      description: "g CO₂/km or kWh/100km",
    },
  ];

  const selectFields = [
    { label: "Vehicle Category", field: "vehicleCategory", options: selectOptions.vehicleCategories, placeholder: "Select category" },
    { label: "Fuel Type", field: "fuelType", options: selectOptions.fuelTypes, placeholder: "Select fuel" },
    { label: "Year Range", field: "yearRange", options: selectOptions.yearRanges, placeholder: "Select range" },
    { label: "Data Source", field: "dataSource", options: selectOptions.dataSources, placeholder: "Select source" },
  ];

  return (
    <div className="border border-[#E8E7EA] p-4 rounded-[11px]">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg text-[#272727] font-semibold">{group.name}</h3>
        <Button
          onClick={() => onRemove(group.id)}
          className="bg-[#FFE9E9] text-[#EA0B0B] hover:bg-[#EA0B0B] hover:text-white px-3 py-1 rounded-md"
        >
          Remove
        </Button>
      </div>
      <div className="grid grid-cols-5 gap-4 mb-3">
        {inputFields.map(({ label, field, placeholder, description }) => (
          <TextInput
            key={field}
            label={label}
            value={group[field] || ""}
            onChange={(e) => onChange(group.id, field, e.target.value)}
            placeholder={placeholder}
            description={description}
            inputWrapperOrder={description ? ["label", "input", "description"] : ["label", "input"]}
          />
        ))}
        {selectFields.map(({ label, field, options, placeholder }) => (
          <Select
            key={field}
            label={label}
            placeholder={placeholder}
            data={options}
            value={group[field] || ""}
            onChange={(value) => onChange(group.id, field, value)}
          />
        ))}
      </div>
      <p className="text-[#272727] bg-[#07838F1A] py-2 px-4 rounded-xl font-medium">
        <span className="font-bold">Group Emissions Estimate:</span> - tCO₂e
      </p>
    </div>
  );
};

const RegionalSettings = ({ gridEmissionFactor, setGridEmissionFactor }) => {
  const selectOptions = {
    regions: ["United States", "North America", "Europe", "Asia", "Other"],
    scope3Options: ["Yes", "No"],
  };

  return (
    <div className="bg-[#FFFFFF] border border-[#E8E7EA] p-4 mt-4 rounded-[10px]">
      <h2 className="text-2xl font-bold text-[#272727]">Regional Settings</h2>
      <div className="grid grid-cols-3 gap-4 mt-4">
        <Select
          label="Primary Market Region"
          placeholder="United States"
          data={selectOptions.regions}
          inputWrapperOrder={["label", "input", "description"]}
        />
        <TextInput
          label="Grid Emission Factor (for EVs)"
          value={gridEmissionFactor}
          onChange={(e) => setGridEmissionFactor(e.target.value)}
          placeholder="386"
          description="g CO₂/kWh"
          inputWrapperOrder={["label", "input", "description"]}
        />
        <Select
          label="Include Scope 3 Emissions?"
          placeholder="Yes (Recommended)"
          data={selectOptions.scope3Options}
          inputWrapperOrder={["label", "input", "description"]}
        />
      </div>
    </div>
  );
};

const VehicleGroups = () => {
  const [vehicleGroups, setVehicleGroups] = useState([
    {
      id: 1,
      name: "Group 1",
      vehicleType: "",
      averageEmissions: "",
    },
  ]);
  const [gridEmissionFactor, setGridEmissionFactor] = useState("");

  const addGroup = () => {
    const newGroup = {
      id: vehicleGroups.length + 1,
      name: `Group ${vehicleGroups.length + 1}`,
      vehicleType: "",
      averageEmissions: "",
    };
    setVehicleGroups([...vehicleGroups, newGroup]);
  };

  const removeGroup = (id) => {
    setVehicleGroups(vehicleGroups.filter((group) => group.id !== id));
  };

  const handleInputChange = (id, field, value) => {
    setVehicleGroups(
      vehicleGroups.map((group) =>
        group.id === id ? { ...group, [field]: value } : group
      )
    );
  };

  return (
    <div>
      <div className="bg-[#FFFFFF] p-4 rounded-[10px] border border-[#E8E7EA]">
        <h2 className="text-2xl text-[#272727] font-bold">Vehicle Groups</h2>
        <p className="text-[#999999]">
          Group similar vehicles together (e.g., by type, fuel, year range) for
          efficient calculation
        </p>
        <div className="flex flex-col mt-4 gap-4">
          {vehicleGroups.map((group) => (
            <VehicleGroup
              key={group.id}
              group={group}
              onRemove={removeGroup}
              onChange={handleInputChange}
            />
          ))}
        </div>
        <Button
          onClick={addGroup}
          className="mt-4 bg-[#07838F1A] hover:bg-[#07838F] text-[#07838F] px-4 py-2 rounded"
        >
          + Add Vehicle Group
        </Button>
      </div>
      <RegionalSettings
        gridEmissionFactor={gridEmissionFactor}
        setGridEmissionFactor={setGridEmissionFactor}
      />
      <div className="mt-4">
        <Button
          w="100%"
          className="bg-[#07838F] text-white hover:bg-[#066B7A] px-6 py-2 rounded"
        >
          Calculate Portfolio Emissions
        </Button>
      </div>
    </div>
  );
};

export default VehicleGroups;