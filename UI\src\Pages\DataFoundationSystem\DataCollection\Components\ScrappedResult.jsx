import { Checkbox, Modal, Pagination, ScrollArea, Table } from "@mantine/core";
import { useState } from "react";
import { LuEye } from "react-icons/lu";
import { useDisclosure } from "@mantine/hooks";

const ScrappedResult = ({ urls = [], keyWords= [] ,metadata}) => {
  const [opened, { open, close }] = useDisclosure(false);
  const [selectedUrl, setSelectedUrl] = useState(null);
  
  const PAGE_SIZE = 5;
  const [page, setPage] = useState(1);
  const normalizedUrls = urls;
  const paginatedData = normalizedUrls.slice(
    (page - 1) * PAGE_SIZE,
    page * PAGE_SIZE
  );

  // Selected URLs management
  const [selected, setSelected] = useState([]);
  const [selectAll, setSelectAll] = useState(false);

  // Check if all rows on current page are selected
  const allCurrentPageSelected = paginatedData.length > 0 && 
    paginatedData.every(row => selected.includes(row.url));

  const handleSelectAll = (checked) => {
    setSelectAll(checked);
    const currentPageUrls = paginatedData.map(row => row.url);
    
    if (checked) {
      setSelected(prev => Array.from(new Set([...prev, ...currentPageUrls])));
    } else {
      setSelected(prev => prev.filter(url => !currentPageUrls.includes(url)));
    }
  };

  const handleSelectRow = (url, checked) => {
    if (checked) {
      setSelected(prev => [...prev, url]);
    } else {
      setSelected(prev => prev.filter(item => item !== url));
    }
  };

  const handleViewDetails = (urlData) => {
    setSelectedUrl(urlData);
    open();
  };



  return (
    <>
      <div className="mt-8 bg-white rounded-xl border-2 p-6">
        <h2 className="text-xl font-semibold mb-4">Scraping Results</h2>
        <ScrollArea>
          <Table miw={800} highlightOnHover verticalSpacing="sm">
            <Table.Thead bg={"#F5F4F5"}>
              <Table.Tr>
                {/* <Table.Th>
                  <Checkbox
                    checked={allCurrentPageSelected}
                    indeterminate={
                      selected.some(url => 
                        paginatedData.some(row => row.url === url)
                      ) && !allCurrentPageSelected
                    }
                    onChange={(event) => handleSelectAll(event.currentTarget.checked)}
                    aria-label="Select all URLs"
                  />
                </Table.Th> */}
                <Table.Th>URLs</Table.Th>
                <Table.Th className="text-center">Actions</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {paginatedData.map((row,i) => (
                <Table.Tr key={i}>
                  {/* <Table.Td>
                    <Checkbox
                      checked={selected.includes(row.url)}
                      onChange={(event) =>
                        handleSelectRow(row.url, event.currentTarget.checked)
                      }
                      aria-label={`Select URL ${row.url}`}
                    />
                  </Table.Td> */}
                  <Table.Td className="max-w-xs" style={{ maxWidth: 400 }}>
                    <div style={{ wordBreak: "break-all" }}>{row.url}</div>
                  </Table.Td>
                  <Table.Td>
                    <div className="flex gap-3 justify-center">
                      <button onClick={() => handleViewDetails(row)}>
                        <LuEye className="w-5 h-5 text-gray-500 hover:text-[#05808b] transition-colors duration-200" />
                      </button>
                    </div>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </ScrollArea>
        <div className="flex items-center justify-between mt-4">
          <span className="text-sm text-gray-500">
            Showing {(page - 1) * PAGE_SIZE + 1} to{" "}
            {Math.min(page * PAGE_SIZE, normalizedUrls.length)} of{" "}
            {normalizedUrls.length}
          </span>
          <Pagination
            total={Math.ceil(normalizedUrls.length / PAGE_SIZE)}
            value={page}
            onChange={setPage}
            size="sm"
            radius="xl"
          />
        </div>
        {metadata != null && metadata.file_url && (
        <div className="mt-4">
          <a 
            href={metadata.file_url}
            download
            className="bg-primary h-[50px] flex items-center justify-center gap-2 text-white text-lg font-semibold rounded-r-lg px-12 py-2 border-1 border-l-0 border-primary hover:bg-opacity-80 transition duration-300"
          >
            Download Scraped Data
          </a>
        </div>
      )}
      </div>

      {/* URL Details Modal */}
      <Modal opened={opened} onClose={close} title="View Url Data" size="lg">
        {selectedUrl && (
          <div>
            <div className="pb-4 mb-4">
              <div>
                <span className="font-medium">URL: </span>
                <span>{selectedUrl.url}</span>
              </div>
              {selectedUrl.date && (
                <div>
                  <span className="font-medium">Date Scraped: </span>
                  <span>{selectedUrl.date}</span>
                </div>
              )}
            </div>
              {keyWords.length > 0 && (
                  <div className="flex items-center flex-wrap gap-2 mb-4">
                  <span className="font-medium">Key Words: </span>
                    {keyWords.map((keyword, index) => (
                      <span key={index} className="px-2 py-1 rounded" style={{ backgroundColor: '#07838F33' }}>
                        {keyword}
                      </span>
                    ))}
                  </div>
              )}
            
            <div className="mb-4">
              <h3 className="font-semibold border-b mb-1">Summary</h3>
              <p className="text-gray-800">
                {selectedUrl.summary || "No summary available."}
              </p>
            </div>
            
            <div className="mb-6 pt-2">
              <h3 className="font-semibold mb-1 border-b">Content</h3>
              <div>{selectedUrl.content || "No content available."}</div>
            </div>
            
            {selectedUrl.keywords && (
              <div className="mb-4">
                <h3 className="font-semibold border-b mb-1">Keywords Found</h3>
                <div className="flex flex-wrap gap-2">
                  {selectedUrl.keywords.map((keyword, index) => (
                    <span key={index} className="bg-blue-100 px-2 py-1 rounded">
                      {keyword}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>
    </>
  );
};

export default ScrappedResult;