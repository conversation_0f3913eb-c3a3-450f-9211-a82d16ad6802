import Loading from "@/Components/Loading";
import { useSelectAssessment } from "@/Contexts/SelectAssessmentContext";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import MainLayout from "@/Layout/MainLayout";
import { useEffect } from "react";
import TypeItem from "./Partials/TypeItem";

function SelectAssessmentType() {
 const { assessmentMenu } = useSideBarRoute();
 const { assessmentTypes, checkProgramStatus, activeAssessment } =
  useSelectAssessment();
 useEffect(() => {
  checkProgramStatus();
 }, []);
 return (
  <>
   <MainLayout menus={assessmentMenu} navbarTitle={"ESG Compass Suite"}>
    <div className="relative bg-white w-full p-2 h-12 text-primary rounded-lg shadow-md before:content-[''] before:absolute before:inset-0 before:rounded-lg before:shadow-[inset_0_0_20px_#00C0A999]">
     <h1 className="font-bold text-lg text-center">
      Select Your Respective System
     </h1>
    </div>

    {!assessmentTypes.length > 0 ||
    !Object.keys(activeAssessment).length > 0 ? (
     <Loading />
    ) : (
     <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-8 px-2">
      {assessmentTypes.map((assessment, indx) => (
       <TypeItem {...assessment} key={indx} />
      ))}
     </div>
    )}
   </MainLayout>
  </>
 );
}

export default SelectAssessmentType;
