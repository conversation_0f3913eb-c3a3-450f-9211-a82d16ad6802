const DataQualityJuid = () => {
  const terms = [
    { term: "Activity Data (AD)", definition: "Quantitative information about a business activity that results in emissions (e.g., fuel consumption, electricity usage)." },
    { term: "Emission Factor (EF)", definition: <>A coefficient that converts activity data into greenhouse gas emissions (e.g., kgCO<sub>2</sub>e per kWh).</> },
    { term: "Data Quality Index (DQI)", definition: "A score between 0-1 that represents overall data quality (higher is better)." },
    { term: "Match Score", definition: "How well Activity Data and Emission Factors align (0.7-1.0 scale)." },
    { term: "Final DQI", definition: "Base DQI adjusted by the Match Score, representing overall quality." },
    { term: "PCAF Score", definition: "Industry-standard quality rating on a 1-5 scale (lower is better)." },
  ];

  const dimensions = [
    { dimension: "Source", definition: "Origin and reliability of data", examples: ["Excellent (1.0): Direct measurements", "Good (0.75): Published database", "Poor (0.4): Estimated values"] },
    { dimension: "Location", definition: "Geographical specificity", examples: ["Excellent (1.0): Facility-specific", "Good (0.75): National", "Poor (0.4): Global"] },
    { dimension: "Age", definition: "Temporal relevance", examples: ["Excellent (1.0): Current year", "Good (0.75): <1 year old", "Poor (0.4): 1-3 years old"] },
    { dimension: "Technology", definition: "Tech-specific relevance", examples: ["Excellent (1.0): Specific technology", "Good (0.75): Industry average", "Poor (0.4): Generic category"] },
    { dimension: "Completeness", definition: "Data coverage", examples: ["Excellent (1.0): Complete data", "Good (0.75): Minor gaps", "Poor (0.4): Significant estimates"] },
    { dimension: "Verification", definition: "Level of external validation", examples: ["Excellent (1.0): Third-party verified", "Good (0.75): Internally verified", "Poor (0.4): Unverified"] },
  ];

  const outputs = [
    { value: "Final DQI ≥ 0.8", interpretation: "Excellent: Highly reliable data", pcaf: "PCAF Score 1: Verified emissions data" },
    { value: "Final DQI 0.6-0.8", interpretation: "Good: Reliable with minor limitations", pcaf: "PCAF Score 2-3: Unverified or specific proxy data" },
    { value: "Final DQI 0.4-0.6", interpretation: "Acceptable: Usable with limitations", pcaf: "PCAF Score 3-4: Proxy data with some assumptions" },
    { value: "Final DQI 0.2-0.4", interpretation: "Poor: Significant limitations", pcaf: "PCAF Score 4: Proxy with significant assumptions" },
    { value: "Final DQI < 0.2", interpretation: "Very Poor: Use with extreme caution", pcaf: "PCAF Score 5: Highly estimated data" },
  ];

  return (
    <div className="max-w-7xl mx-auto p-5 font-sans">
      <h1 className="text-2xl font-bold  pb-2 mb-2 text-[#272727]">
        Data Quality Assessment: User Guide
      </h1>

      <section className="mb-8">
        <h2 className="text-xl font-semibold text-[#272727] mb-4">Key Terms & Definitions</h2>
        <dl className="ml-5">
          {terms.map((item, index) => (
            <div key={index} className="gap-2 flex">
              <dt className="font-bold text-[#272727]">{item.term}:</dt>
              <dd className="text-[#272727]">{item.definition}</dd>
            </div>
          ))}
        </dl>
      </section>

      <section className="p-5 rounded-xl border-2 border-[#E8E7EA] mb-8">
        <h2 className="text-xl font-semibold text-[#272727] mb-4">Quality Dimensions</h2>
        <table className="w-full border-collapse my-5">
          <thead>
            <tr>
              <th className="border border-[#E8E7EA] p-3 text-left bg-[#F5F4F5] text-[#272727]">Dimension</th>
              <th className="border border-[#E8E7EA] p-3 text-left bg-[#F5F4F5] text-[#272727]">Definition</th>
              <th className="border border-[#E8E7EA] p-3 text-left bg-[#F5F4F5] text-[#272727]">Example Ratings</th>
            </tr>
          </thead>
          <tbody>
            {dimensions.map((dim, index) => (
              <tr key={index}>
                <td className="border border-[#E8E7EA] text-[#272727] p-3 font-semibold">{dim.dimension}</td>
                <td className="border border-[#E8E7EA] text-[#272727] p-3">{dim.definition}</td>
                <td className="border border-[#E8E7EA] text-[#272727] p-3">
                  {dim.examples.map((ex, i) => (
                    <div key={i} className="flex items-center gap-2 py-1 text-[#272727]">
                      {ex.includes("Excellent")}
                      {ex.includes("Good")}
                      {ex.includes("Poor")}
                      <span>{ex}</span>
                    </div>
                  ))}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </section>

      <section className="p-5 rounded-xl border-2 border-[#E8E7EA] mb-8">
        <h2 className="text-xl font-semibold mb-4 text-[#272727]">Quality Outputs & Interpretation</h2>
        <table className="w-full border-collapse my-5">
          <thead>
            <tr>
              <th className="border border-[#E8E7EA] p-3 text-left bg-[#F5F4F5] text-[#272727]">Output Value</th>
              <th className="border border-[#E8E7EA] p-3 text-left bg-[#F5F4F5] text-[#272727]">Interpretation</th>
              <th className="border border-[#E8E7EA] p-3 text-left bg-[#F5F4F5] text-[#272727]">PCAF Equivalent</th>
            </tr>
          </thead>
          <tbody>
            {outputs.map((out, index) => (
              <tr key={index}>
                <td className="border border-[#E8E7EA] text-[#272727] p-3 font-semibold">{out.value}</td>
                <td className="border border-[#E8E7EA] text-[#272727] p-3">{out.interpretation}</td>
                <td className="border border-[#E8E7EA] text-[#272727] p-3">{out.pcaf}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </section>
    </div>
  );
};

export default DataQualityJuid;