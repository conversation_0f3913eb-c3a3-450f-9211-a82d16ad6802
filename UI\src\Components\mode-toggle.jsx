"use client";

import { <PERSON>, Sun } from "lucide-react";
import { useTheme } from "next-themes";
import { Button } from "@mantine/core";

export function ModeToggle() {
  const { setTheme, theme } = useTheme();

  return (
    <Button
      variant="light"
      onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
      leftSection={theme === "dark" ? <Sun size={16} /> : <Moon size={16} />}
    >
      {theme === "dark" ? "Light" : "Dark"}
    </Button>
  );
}
