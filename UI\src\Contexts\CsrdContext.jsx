import React, { createContext, useState, useContext, useEffect } from "react";
import ApiS1Config from "@/Api/apiS1Config";
import { notifications, showNotification } from "@mantine/notifications";
import { FaCheckCircle } from "react-icons/fa";
import { useNavigate } from "react-router";

// Create a Context for the selected Assessment
const CsrdContext = createContext();

// Create a Provider component
export default function CsrdContextProvider({ children }) {
  const [loading, setLoading] = useState(false);
  const [CSRDScopesData, setCSRDScopesData] = useState(null);
  const [selectedScope, setSelectedScope] = useState(null);
  const [postAnswersState, setPostAnswersState] = useState();
  const [postLoading, setPostLoading] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [reportLoading, setReportLoading] = useState(false);

  const navigate = useNavigate();

  const PriorityLevels = {
    1: "Low",
    2: "Medium",
    3: "High",
    4: "Top",
  };
  const ReadinessLevel = {
    1: "Not Started",
    2: "Initial Planning",
    3: "In Development",
    4: "Partially Implemented",
    5: "Fully Implemented",
    6: "Not Applicable",
  };
  const readinessColorMap = {
    "Not Started": {
      bg: "#AB020233",
      border: "#AB0202",
      text: "#AB0202",
    },
    "Not Applicable": {
      bg: "#e0e2e7",
      border: "#667085",
      text: "#667085",
    },
    "Initial Planning": {
      bg: "#e9dff3",
      border: "#9160C1",
      text: "#9160C1",
    },
    "In Development": {
      bg: "#ffeecd",
      border: "#FFAB07",
      text: "#FFAB07",
    },
    "Partially Implemented": {
      bg: "#d4e8fb",
      border: "#298BED",
      text: "#298BED",
    },
    "Fully Implemented": {
      bg: "#e2f6e7",
      border: "#01BD36",
      text: "#01BD36",
    },
  };

  const prioritySelectColorMap = {
    Top: {
      bg: "#AB02024D",
      border: "",
      text: "#AB0202",
    },
    High: {
      bg: "#ffedca",
      border: "#ffedca",
      text: "#FFAB07",
    },
    Medium: {
      bg: "#ccf2ee",
      border: "#00C0A9",
      text: "#00C0A9",
    },
    low: {
      bg: "#c7f1d3",
      border: "#01BD36",
      text: "#01BD36",
    },
  };
  const get_csrd_data = async () => {
    setLoading(true);
    try {
      setLoading(false);
      const { data } = await ApiS1Config.get("/get_assessment_data", {
        headers: { assessmentName: "CSRD Readiness" },
      });
      setCSRDScopesData(data["CSRD Readiness"]); // Get the data and update the state
    } catch (error) {
      setLoading(false);
      error.response.data.Message === "Company assessment not found"
        ? navigate("/select-assessment")
        : "";
        if (error.response.data.Message === "There is no active assessment") {
          navigate("/csrd-report");
          showNotification({
            message: (
              <span className="capitalize">{error.response.data.Message}</span>
            ),
            color: "red",
          });
        }
      //console.log(error); // Log any error
    } finally {
      setLoading(false); // Always stop loading regardless of success or error
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      await get_csrd_data();
    };

    fetchData();
  }, []);

  const postAnswers = async (edata) => {
    setPostLoading(true);
    //console.log(selectedScope);
    try {
      setPostLoading(true);
      // if (postAnswersState && extractedData) {
      const { data } = await ApiS1Config.post(
        `/post_scope`,

        edata,

        {
          headers: {
            assessmentName: "CSRD Readiness",
            categoryName: selectedCategory,
            scopeName: selectedScope.name,
          },
        }
      );
      setPostLoading(false);
      //console.log(data);
      notifications.show({
        title: "Updated successfully!",
        color: "green",
        icon: <FaCheckCircle />,
        // loading,
      });
      setPostAnswersState(false);
      get_csrd_data();
      // }
    } catch (error) {
      setPostLoading(false);
      //console.log(error);
      notifications.show({
        title: error.response.data.Message,
        color: "red",
      });
    }
  };
  const getReport = async () => {
    setReportLoading(true);
    try {
      setReportLoading(true);
      const { data } = await ApiS1Config.post(
        "/get_report",
        {},
        {
          headers: {
            assessmentType: "CSRD Readiness",
            categoryName: selectedCategory,
            // scopeName: selectedScope.name,
          },
        }
      );
      setReportLoading(false);
      navigate("/csrd-report");
      //console.log(data);
    } catch (error) {
      setReportLoading(false);
      //console.log(error);
    }
  };

  return (
    <CsrdContext.Provider
      value={{
        CSRDScopesData,
        selectedScope,
        setSelectedScope,
        PriorityLevels,
        ReadinessLevel,
        postAnswers,
        setPostAnswersState,
        postAnswersState,
        setSelectedCategory,
        postLoading,
        reportLoading,
        readinessColorMap,
        prioritySelectColorMap,
        getReport,
       
      }}
    >
      {children}
    </CsrdContext.Provider>
  );
}

// Custom hook to use the CsrdContext
export const useCsrdContext = () => useContext(CsrdContext);
