import ApiS1Config from "@/Api/apiS1Config";
import Loading from "@/Components/Loading";
import Share from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/Share";
import { linkStyle } from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/StaticData";
import ViewPDF from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/ViewPDF";
import reportImg from "@/assets/images/mountain-logo.png";
import { AreaChart } from "@mantine/charts";
import { Button, RingProgress, Text } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { FiDownload } from "react-icons/fi";
import { MdHistory } from "react-icons/md";
import HistoryPopUp from "../../AssessmentType/Partials/HistoryPopUp";
import GuideModalButton from "@/Components/Guide/GuideModalButton";
import Guide from "./ReportGuide";

export default function CsrdReport() {
    const [GetDynamicData, setDynamicData] = useState();
    const [lineData, setLineData] = useState([]);
    const [historyOpened, { open: historyOpen, close: historyClose }] =
        useDisclosure(false);
    const getDynamicData = async () => {
        try {
            const { data: dynamic } = await ApiS1Config.get("/dynamic_data", {
                headers: {
                    assessmentType: "CSRD Readiness",
                },
            });
            await ApiS1Config.get("/dashboard", {
                headers: { assessmentType: "CSRD Readiness" },
            })
                .then((response) => response.data.all_assessment_summaries)
                .then((assessmentData) => {
                    // setAssessments(assessmentData);
                    const data = assessmentData.map((item) => {
                        let name = item?.name
                            ?.split(" ")
                            .slice(0, -1)
                            .join(" ");
                        const Day = name?.split(" ")[0];
                        const Month = name?.split(" ")[1].slice(0, 3);
                        return {
                            Month: item?.total_score,
                            date: `${Day} ${Month}`,
                        };
                    });

                    setLineData(data);
                });
            setDynamicData(dynamic);
        } catch (error) {
            //console.log(error);
        }
    };
    const { t } = useTranslation();
    useEffect(() => {
        getDynamicData();
    }, []);
    return (
        <>
            <div className="w-full justify-between flex items-center py-5 px-3">
                <GuideModalButton buttonText="Dashboard Guide">
                    <Guide />
                </GuideModalButton>
            </div>
            <>
                {!GetDynamicData ? (
                    <Loading />
                ) : (
                    <div className="flex flex-col w-full left-section min-h-svh">
                        <div className="report-page flex flex-col lg:flex-row items-center lg:justify-between gap-y-3 flex-wrap mb-[38px]">
                            <div>
                                <Button
                                    className={linkStyle}
                                    size="md"
                                    onClick={historyOpen}
                                >
                                    <MdHistory className="me-1" />
                                    Assessment History
                                </Button>
                            </div>
                            <div className="flex flex-col lg:flex-row items-center justify-center gap-6">
                                <ViewPDF
                                    btnStyle={
                                        "border-[1px] flex items-center px-4 justify-between w-[195px] h-[56px] text-[17px] font-bold rounded-lg text-secondary-300 border-secondary-300 bg-white hover:text-white hover:bg-secondary-300"
                                    }
                                    pdfUrl={GetDynamicData?.pdf_url}
                                    text={"View Report"}
                                />
                                <Share link={GetDynamicData?.pdf_url} />
                                <a
                                    className={
                                        "border-[1px] flex items-center px-4 justify-between w-[195px] h-[56px] text-sm font-bold rounded-lg text-white bg-secondary-300 ease-out duration-200 hover:translate-y-2 hover:bg-secondary-400"
                                    }
                                    href={GetDynamicData?.pdf_url}
                                    download
                                >
                                    downloadReport
                                    <span>
                                        <FiDownload className="text-lg" />
                                    </span>
                                </a>
                            </div>
                        </div>

                        <div className="flex flex-wrap mt-5 mb-7 gap-9 md:flex-nowrap">
                            {/* donut chart */}
                            <div className="flex bg-white p-5 flex-grow md:w-1/2 justify-center flex-col items-center rounded-lg shadow-[0px_0px_10px_0px_#0000001A]">
                                <h5 className="font-bold">
                                    {t("overallReadinessScore")}
                                </h5>
                                <span className="flex items-center justify-center w-full">
                                    <RingProgress
                                        size={180}
                                        thickness={20}
                                        roundCaps
                                        className="w-full aspect-square text-center flex justify-center items-center scale-x-[-1] rotate-90"
                                        sections={[
                                            {
                                                value:
                                                    (GetDynamicData?.overall_score ||
                                                        0) * 20,
                                                color: "#29919B",
                                            },
                                        ]}
                                        rootColor="#D4E9EB"
                                        label={
                                            <Text
                                                c="black"
                                                fw={700}
                                                ta="center"
                                                size="xl"
                                                className="rotate-90 scale-x-[-1] text-3xl flex justify-center items-center"
                                            >
                                                {GetDynamicData?.overall_score ||
                                                    0}
                                            </Text>
                                        }
                                    />
                                </span>
                            </div>

                            {/* line chart */}
                            {lineData?.length > 0 ? (
                                <div className="bg-white flex-grow md:w-1/2 text-center p-5 rounded-lg shadow-[0px_0px_10px_0px_#0000001A]">
                                    <h5 className="mb-6 font-bold">
                                        {t("progressTracker")}
                                    </h5>
                                    <AreaChart
                                        h={200}
                                        data={lineData}
                                        dataKey="date"
                                        series={[
                                            { name: "Month", color: "blue.6" },
                                        ]}
                                        curveType="natural"
                                        withDots={false}
                                    />
                                </div>
                            ) : (
                                <Loading />
                            )}
                        </div>
                        <div className="flex items-center justify-center w-full mx-auto mt-auto img-wrapper max-w-7xl">
                            <img
                                src={reportImg}
                                className="object-center w-full"
                                alt="Report"
                            />
                        </div>
                    </div>
                )}
                {historyOpened && (
                    <HistoryPopUp
                        opened={historyOpened}
                        close={historyClose}
                        assessmentType={"CSRD Readiness"}
                    />
                )}
            </>
        </>
    );
}
