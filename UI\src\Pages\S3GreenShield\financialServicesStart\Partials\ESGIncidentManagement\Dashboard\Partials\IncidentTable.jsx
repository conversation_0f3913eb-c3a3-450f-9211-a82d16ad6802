import Loading from "@/Components/Loading";
import { Button, Checkbox, Input, ScrollArea, Table } from "@mantine/core";
import { customAlphabet } from "nanoid";
import { useState } from "react";
import { CiSearch } from "react-icons/ci";
import { FaPlus } from "react-icons/fa";
import { useNavigate } from "react-router-dom";

export default function IncidentTable({ filteredData, loading }) {
  const [selection, setSelection] = useState([]);

  const navigate = useNavigate();
    const generateNumericID = customAlphabet("0123456789", 8);
  
  const id = generateNumericID();

  const recentData = filteredData
  ?.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
  .slice(0, 3);
  
    const toggleRow = (id) =>
      setSelection((current) =>
        current.includes(id)
          ? current.filter((item) => item !== id)
          : [...current, id]
      );
      const toggleAll = () =>
        setSelection((current) => {
          if (recentData.length === 0) return [];
          return current.length === recentData.length
            ? []
            : recentData.map((item) => item._id);
        });

  const rows = recentData.map((item, index) => (
    <Table.Tr
      key={item._id}
      className="text-sm font-bold text-[#626364] text-center"
    >
      <Table.Td>
        <Checkbox
          checked={selection.includes(item._id)}
          onChange={() => toggleRow(item._id)}
          color="#07838F"
        />
      </Table.Td>
      <Table.Td>{index + 1}</Table.Td>
      <Table.Td>{item.title}</Table.Td>
      {/* <Table.Td>
        {item.createdAt ? item.createdAt.split("T")[0] : "N/A"}
      </Table.Td> */}
      <Table.Td className="border-primary rounded-lg">
        <span
          className={`border-primary py-2 rounded-lg w-full capitalize
            ${
              item.status.trim().toLowerCase() === "in_progress"
                ? "bg-[#fff0b8] text-[#FFAB07] px-2"
                : item.status.trim().toLowerCase() === "new"
                ? "bg-[#cafac2] text-[#70D162] px-7"
                : item.status.trim().toLowerCase() === "resolved"
                ? "bg-[#cea7f6] text-[#9160C1] px-3"
                : "bg-red-300 text-red-800 px-5"
            }
            `}
        >
          {item.status.includes("in_progress")
            ? item.status.replace("_", " ")
            : item.status}
        </span>
      </Table.Td>
      <Table.Td>
        {item?.involvedPersons?.length > 0 ? item.involvedPersons.length : 0}
      </Table.Td>
      <Table.Td className="capitalize">
        <span
          className={`border-primary py-2 rounded-2xl w-full capitalize px-5
        ${
          item.priority.trim().toLowerCase() === "high"
            ? "bg-[#FFEDCA] text-[#FFAB07]"
            : item.priority.trim().toLowerCase() === "medium"
            ? "bg-[#00C0A9] text-[#00C0A9] bg-opacity-20 px-7"
            : item.priority.trim().toLowerCase() === "low"
            ? "bg-[#01BD36] text-[#01BD36] bg-opacity-20 px-3"
            : "bg-gray-200 text-black"
        }
      `}
        >
          {item.priority.trim().charAt(0).toUpperCase() +
            item.priority.trim().slice(1).toLowerCase() || "unknown"}
        </span>
      </Table.Td>
      <Table.Td>{item.interactionLocation}</Table.Td>
      <Table.Td>{item.interactionType}</Table.Td>
      <Table.Td>
        {item.anonymous ? "Anonymous" : item.reporter?.firstName || "Anonymous"}
      </Table.Td>
      <Table.Td>
        {item.closureDate ? item.closureDate.split("T")[0] : "N/A"}
      </Table.Td>
    </Table.Tr>
  ));

  return (
    <>
      {loading ? (
        <div className="m-auto flex justify-center w-full ">
          <Loading />
        </div>
      ) : (
        <div className="mt-7">
          <div className="md:flex justify-between">
            {/* <div className="flex gap-4">
              <Button className="bg-[#F2F2F2] hover:bg-[#F2F2F2] hover:text-[#000] text-[#000]  rounded-lg font-bold">
                Recent Interaction
              </Button>
              <Button className="bg-[#fff] hover:bg-[#fff] hover:text-[#000] text-[#000]  rounded-lg font-bold">
                Top Stakeholders
              </Button>
            </div> */}
            {/* <Button
              className="bg-transparent text-primary hover:bg-transparent hover:text-primary mt-3 md:mt-0 underline"
              size="md"
              // onClick={() =>
              //   navigate(
              //     `/green-shield/financial/ESG-incident-management/StakeholderInteractions`
              //   )
              // }
            >
              View all
            </Button> */}
          </div>
          <div className="flex mb-5 flex-wrap justify-between items-center">
            <h5 className="text-2xl font-semibold">
              Risk Identification & Assessment
            </h5>

            <div className="flex flex-wrap gap-2 items-center">
              <Input
                type="text"
                placeholder={"SearchPlaceholder"} // Translated placeholder
                className="border border-gray-300 rounded-md sm:w-auto focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                // size="md"
                leftSection={<CiSearch />}
              />
              <Button
                className=" bg-primary text-white border-2 border-primary rounded-lg hover:bg-transparent hover:text-primary mt-3 md:mt-0"
                // size="md"
                onClick={() =>
                  navigate(
                    `/green-shield/financial/ESG-incident-management/addNewIncident/${id}`
                  )
                }
              >
                <FaPlus className="mr-2" size={10} /> Add new
                interaction
              </Button>
            </div>
          </div>
          <ScrollArea>
            <Table miw={999} verticalSpacing="sm">
              <Table.Thead className="bg-secondary-lite-gray pb-6 text-base font-medium text-center">
                <Table.Tr>
                <Table.Th>
                  <Checkbox
                    onChange={toggleAll}
                    checked={
                      recentData.length > 0 &&
                      selection.length === recentData.length
                    }
                    color="#07838F"
                  />
                </Table.Th>
                  <Table.Th className="text-center">Interaction ID</Table.Th>
                  <Table.Th className="text-center">Title</Table.Th>
                  {/* <Table.Th className="text-center">Date</Table.Th> */}
                  <Table.Th className="text-center">Status</Table.Th>
                  <Table.Th className="text-center">People involved</Table.Th>
                  <Table.Th className="text-center">Priority</Table.Th>
                  <Table.Th className="text-center">Location</Table.Th>
                  <Table.Th className="text-center">Category</Table.Th>
                  <Table.Th className="text-center">Source</Table.Th>
                  <Table.Th className="text-center">Closure Date</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>{rows}</Table.Tbody>
            </Table>
          </ScrollArea>
        </div>
      )}
    </>
  );
}
