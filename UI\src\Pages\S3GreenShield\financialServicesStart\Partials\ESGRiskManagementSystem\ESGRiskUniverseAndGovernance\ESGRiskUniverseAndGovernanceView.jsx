import { useState } from "react";
import ESGRiskTaxonomy from "./ESGRiskTaxonomy";
import AssessmentMatrix from "./AssessmentMatrix";
import RolesAndResponsibilities from "./RolesAndResponsibilities";
import TabsButton from "@/Components/ButtonsAndLinks/TabsButton";

export default function ESGRiskUniverseAndGovernanceView() {
  const [activeTab, setActiveTab] = useState("ESG Risk Taxonomy");

  const links = [
    "ESG Risk Taxonomy",
    "Assessment Matrix",
    "Roles & Responsibilities",
  ];

  return (
    <div className="">
      <div className="mt-5 grid lg:grid-cols-3 justify-center bg-[#FFFFFF] border-2 border-[#E8E7EA] p-6 gap-5 py-3 mb-6 rounded-lg text-primary">
        {links.map((tabName) => (
          <TabsButton
            key={tabName}
            active={activeTab}
            setActive={setActiveTab}
            btnName={tabName}
          />
        ))}
      </div>

      {activeTab === "ESG Risk Taxonomy" && <ESGRiskTaxonomy />}
      {activeTab === "Assessment Matrix" && <AssessmentMatrix />}
      {activeTab === "Roles & Responsibilities" && <RolesAndResponsibilities />}
    </div>
  );
}
