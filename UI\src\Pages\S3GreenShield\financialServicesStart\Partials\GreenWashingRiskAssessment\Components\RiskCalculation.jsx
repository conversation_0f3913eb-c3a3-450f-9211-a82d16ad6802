import React, { useState } from "react";
import RiskMatrixTable from "./RiskMatrixTable";
import RiskLevelMatrixTable from "./RiskLevelMatrixTable";
import { useDisclosure } from "@mantine/hooks";
import { Modal, Button, TextInput } from "@mantine/core";

const RiskCalculation = () => {
  const [opened, { open, close }] = useDisclosure(false);

  const [likelihood, setLikelihood] = useState({
    level1: [
      "Rare",
      "<10% chance of occurrence",
      "May occur only in exceptional circumstances",
    ],
    level2: [
      "Unlikely",
      "10-30% chance of occurrence",
      "Could occur at some time",
    ],
    level3: [
      "Possible",
      "30-60% chance of occurrence",
      "Might occur at some time",
    ],
    level4: [
      "Likely",
      "60-90% chance of occurrence",
      "Will probably occur in most circumstances",
    ],
    level5: [
      "Almost Certain",
      ">90% chance of occurrence",
      "Expected to occur in most circumstances",
    ],
  });

  const [impact, setImpact] = useState({
    level1: [
      "Insignificant",
      "Minimal financial loss (<$10,000)",
      "No reputational damage",
      "No regulatory implications",
      "No impact on stakeholder trust",
    ],
    level2: [
      "Minor",
      "Low financial loss ($10,000-$100,000)",
      "Limited reputational damage, local media coverage",
      "Minor regulatory breaches",
      "Slight impact on stakeholder trust",
    ],
    level3: [
      "Moderate",
      "Moderate financial loss ($100,000-$1M)",
      "Moderate reputational damage, national media coverage",
      "Moderate regulatory breaches, potential fines",
      "Moderate impact on stakeholder trust",
    ],
    level4: [
      "Major",
      "High financial loss ($1M-$10M)",
      "Significant reputational damage, international media coverage",
      "Significant regulatory breaches, substantial fines",
      "Significant impact on stakeholder trust",
    ],
    level5: [
      "Catastrophic",
      "Extreme financial loss (>$10M)",
      "Severe reputational damage, long-term international media coverage",
      "Severe regulatory breaches, potential for legal action or loss of license to operate",
      "Significant impact on stakeholder trust, potential loss of key stakeholders",
    ],
  });

  const handleLikelihoodChange = (e) => {
    setLikelihood({
      ...likelihood,
      [e.target.name]: e.target.value.split("\n"), // Split the input string into an array
    });
  };

  const handleImpactChange = (e) => {
    setImpact({
      ...impact,
      [e.target.name]: e.target.value.split("\n"), // Split the input string into an array
    });
  };

  const handleSave = () => {
    close();
  };

  return (
    <>
      <div className="flex flex-row justify-between">
        <div>
          <h3 className="font-bold text-2xl">Risk Matrix</h3>
          <p className="font-semibold text-lg">
            Criteria of Likelihood and Impact
          </p>
        </div>
        <Button onClick={open} className="bg-[#07838F] rounded-lg">
          Configure Data
        </Button>
      </div>

      <Modal
        opened={opened}
        onClose={close}
        title={
          <h2 className="text-[#07838F] font-bold">
            Update Criteria of Likelihood and Impact
          </h2>
        }
      >
        <div className="flex flex-col justify-center">
          <h2 className="font-bold mb-2">Likelihood</h2>
          {Object.keys(likelihood).map((level) => (
            <div key={level} className="flex items-center mb-4">
              <label className="w-1/6 font-medium text-right pr-2">
                {level}
              </label>

              <TextInput
                className="flex-1"
                name={level}
                value={likelihood[level].join("\n")} // Join array into a multi-line string
                onChange={handleLikelihoodChange}
                placeholder={`Enter ${level} likelihood`}
                multiline
              />
            </div>
          ))}

          <h2 className="font-bold mt-4 mb-2">Impact</h2>
          {Object.keys(impact).map((level) => (
            <div key={level} className="flex items-center mb-4">
              <label className="w-1/6 font-medium text-right pr-2">
                {level}
              </label>

              <TextInput
                className="flex-1"
                name={level}
                value={impact[level].join("\n")} // Join array into a multi-line string
                onChange={handleImpactChange}
                placeholder={`Enter ${level} impact`}
                multiline
              />
            </div>
          ))}

          <Button onClick={handleSave} className="mt-4 bg-[#07838F] rounded-lg">
            Save
          </Button>
        </div>
      </Modal>

      <RiskMatrixTable likelihood={likelihood} impact={impact} />

      <h3 className="font-semibold text-lg mt-3">Risk Level Matrix</h3>
      <p className="font-semibold text-sm mb-3">
        Risk Level = Impact value x Likelihood value
      </p>
      <RiskLevelMatrixTable />
    </>
  );
};

export default RiskCalculation;
