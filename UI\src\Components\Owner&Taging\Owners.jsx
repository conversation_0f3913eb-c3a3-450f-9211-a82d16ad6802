import axios from 'axios';
import Cookies from 'js-cookie';
import { useEffect, useState } from 'react';
import Loading from '../Loading';
import { MultiSelect } from '@mantine/core';

const OWNERS_STORAGE_KEY = 'assignees';
const LAST_UPDATED_KEY = 'assignees_last_updated';
const TWENTY_FOUR_HOURS_MS = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

const Owners = ({  handleOwners,currentOwners }) => {
  const [assignees, setAssignees] = useState([]);
  const [assigneesArray, setAssigneesArray] = useState([]);

  useEffect(() => {
    const lastUpdated = localStorage.getItem(LAST_UPDATED_KEY);
    const now = Date.now();

    if (lastUpdated && now - parseInt(lastUpdated, 10) > TWENTY_FOUR_HOURS_MS) {
      localStorage.removeItem(OWNERS_STORAGE_KEY);
      localStorage.removeItem(LAST_UPDATED_KEY);
    }
  }, []); // Run once on component mount to check for expiry

  const assigneesData = async () => {
    const storedAssignees = localStorage.getItem(OWNERS_STORAGE_KEY);

    if (storedAssignees) {
      try {
        const parsedAssignees = JSON.parse(storedAssignees);
        setAssigneesArray(parsedAssignees);
        const uniqueNames = [...new Set(parsedAssignees.map((item) => item.user_name))];
        setAssignees(uniqueNames);
      } catch (error) {
        console.error('Error parsing stored assignees:', error);
        fetchAssigneesFromApi();
      }
    } else {
      fetchAssigneesFromApi();
    }
  };

  const fetchAssigneesFromApi = async () => {
    try {
      const response = await axios.get(
        "https://portal-auth-main.azurewebsites.net/get-all-company-users",
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("level_user_token")}`,
          },
        }
      );
      setAssigneesArray(response.data);
      localStorage.setItem(OWNERS_STORAGE_KEY, JSON.stringify(response.data));
      localStorage.setItem(LAST_UPDATED_KEY, Date.now().toString());
      const uniqueNames = [...new Set(response.data.map((item) => item.user_name))];
      setAssignees(uniqueNames);
    } catch (error) {
      console.error('Error fetching assignees:', error);
      // Consider setting an error state
    }
  };

  useEffect(() => {
    assigneesData();
  }, []);

  const defaultAssignees = currentOwners.map(
    (name) =>
     assigneesArray.find((item) => item?.user_name === name)?.user_name
   )
  .filter(Boolean)

  return (
    <>
      {assignees.length === 0 ? (
        <>
          <Loading />
        </>
      ) : (
        <MultiSelect
          onChange={(selectedNames) => {
            const selectedIds = selectedNames.map(
              (name) =>
                assigneesArray.find((item) => item.user_name === name)
            );
            handleOwners(selectedIds);
          }}
          label=""
          placeholder="Pick value"
          data={assignees || []}
          defaultValue={defaultAssignees || []}
        />
      )}
    </>
  );
};

export default Owners;