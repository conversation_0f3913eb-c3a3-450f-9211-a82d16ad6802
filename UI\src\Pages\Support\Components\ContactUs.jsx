import { FaRegQuestionCircle } from "react-icons/fa";
import { MdOutlinePhone } from "react-icons/md";
import { MdOutlineMailOutline } from "react-icons/md";

const ContactUs = () => {
  const contactPhone = "+44 7862 322561";
  const contactEmail = "<EMAIL>";

  return (
    <section className="p-6 support-request bg-white rounded-2xl min-h-[376px]">
      <FaRegQuestionCircle className="w-12 h-12 mx-auto mt-10 text-primary" />

      <h2 className="mt-3 text-lg font-semibold text-center">
        Still have questions?
      </h2>

      <h3 className="mt-3 text-sm font-medium text-center">
        Reach out to us via instant support or email
      </h3>

      <div className="phone&chat-email grid lg:grid-cols-2 mt-8">
        <div className="flex flex-col items-center justify-center phone-chat lg:border-r-2">
          <h3 className="text-xs font-semibold text-center">Instant Support</h3>
          <h3 className="text-[.6rem] font-normal text-center">
            Get answers to your questions immediately.
          </h3>

          <div className="flex items-center gap-2 mt-4 btns">
            <a
              href={`tel:${contactPhone}`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center gap-1 bg-primary w-[82px] h-7 rounded-lg text-white"
            >
              <MdOutlinePhone className="w-4 h-4" />
              <span className="text-xs">Phone</span>
            </a>
          </div>
        </div>

        <div className="email">
          <h3 className="text-xs font-semibold text-center">Email</h3>
          <h3 className="text-[.6rem] font-normal text-center">
            Wait time 1 to 2 work days.
          </h3>
          <a
            href={`mailto:${contactEmail}?subject=Support Request`}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center justify-center mt-4 mx-auto gap-1 bg-primary w-[82px] h-7 rounded-lg text-white"
          >
            <MdOutlineMailOutline className="w-4 h-4" />
            <span className="text-xs">Email</span>
          </a>
        </div>
      </div>
    </section>
  );
};

export default ContactUs;