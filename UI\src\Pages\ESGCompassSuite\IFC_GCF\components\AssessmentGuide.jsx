import {
    Box,
    Title,
    Text,
    List,
    Group,
    Table,
    ThemeIcon,
    Stack,
    Paper,
} from "@mantine/core";
import { FaCheck } from "react-icons/fa";

export default function IfcGcfReadinessGuide() {
    return (
        <Box mx="auto" maxWidth="1200px" p="md">
            <Title order={1} mb="xl">
                IFC Performance Standards & GCF Readiness User Guide
            </Title>

            {/* Overview Section */}
            <Stack spacing="lg">
                <Text size="lg" mb="sm">
                    <b>Overview</b>
                </Text>
                <Text c="dimmed">
                    The IFC Performance Standards and GCF Readiness Assessment
                    Tool enables financial institutions to evaluate their
                    projects against the International Finance Corporation (IFC)
                    Performance Standards and Green Climate Fund (GCF)
                    requirements. This comprehensive system facilitates
                    compliance readiness assessment, documentation, and
                    improvement planning.
                </Text>

                {/* Getting Started Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Getting Started</b>
                </Text>
                <List listStyleType="decimal" spacing="sm">
                    <List.Item>
                        <Text>Click "New Assessment" from the dashboard</Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Select "Start Assessment" for each of the eight
                            sections
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Click "Generate Report" to create your reporting
                            dashboard
                        </Text>
                    </List.Item>
                </List>

                {/* Assessment Structure Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Assessment Structure</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>
                            IFC Performance Standards Framework:
                        </Text>
                        <List listStyleType="circle" spacing="sm" ml="1.5rem">
                            <List.Item>
                                Environmental and Social Management Systems
                                (ESMS)
                            </List.Item>
                            <List.Item>Labor and Working Conditions</List.Item>
                            <List.Item>
                                Resource Efficiency and Climate Impact
                            </List.Item>
                            <List.Item>
                                Community Health, Safety, and Security
                            </List.Item>
                            <List.Item>
                                Land Acquisition and Biodiversity
                            </List.Item>
                            <List.Item>
                                Indigenous Peoples and Cultural Heritage
                            </List.Item>
                            <List.Item>GCF-Specific Requirements</List.Item>
                            <List.Item>
                                Monitoring, Reporting and Disclosure
                            </List.Item>
                        </List>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>GCF Compliance Areas:</Text>
                        <List listStyleType="circle" spacing="sm" ml="1.5rem">
                            <List.Item>
                                Climate Resilience and Impact Potential
                            </List.Item>
                            <List.Item>Paradigm Shift Potential</List.Item>
                            <List.Item>
                                Sustainable Development Potential
                            </List.Item>
                            <List.Item>Country Ownership</List.Item>
                            <List.Item>Efficiency and Effectiveness</List.Item>
                            <List.Item>
                                GCF Environmental and Social Safeguards
                            </List.Item>
                        </List>
                    </List.Item>
                </List>

                {/* Rating System Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Bating System</b>
                </Text>
                <Table striped highlightOnHover>
                    <thead className="bg-gray-50 text-xl">
                        <tr>
                            <th className="py-2">Readiness Level</th>
                            <th className="py-2">Description</th>
                        </tr>
                    </thead>
                    <tbody className="text-lg">
                        <tr>
                            <td>
                                <Text fw={700}>1 - Not Started</Text>
                            </td>
                            <td>
                                No relevant policies, procedures, or systems in
                                place
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <Text fw={700}>2 - Initial Planning</Text>
                            </td>
                            <td>
                                Awareness exists with preliminary guidance
                                provided
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <Text fw={700}>3 - In Development</Text>
                            </td>
                            <td>
                                Active implementation underway but not yet
                                complete
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <Text fw={700}>4 - Partially Implemented</Text>
                            </td>
                            <td>
                                Key elements in place but with remaining gaps
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <Text fw={700}>5 - Fully Implemented</Text>
                            </td>
                            <td>
                                Comprehensive implementation with monitoring
                                systems
                            </td>
                        </tr>
                    </tbody>
                </Table>

                {/* Assessment Process Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Assessment Process</b>
                </Text>
                <List listStyleType="decimal" spacing="sm">
                    <List.Item>
                        <Text>
                            For each criterion, assign a Readiness Level (1-5)
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Examine project documentation against requirements
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>Select appropriate Readiness Level (1-5)</Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Add supporting Evidence to justify your rating
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Specify Actions Required to address any gaps
                        </Text>
                    </List.Item>
                </List>

                {/* Documentation Management Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Documentation Management</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>Evidence Library:</Text>
                        <Text size="sm">
                            The system maintains a centralized evidence
                            repository.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>Access existing documents or add new ones</Text>
                    </List.Item>
                    <List.Item>
                        <Text>Tag documents with relevant keywords</Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Set access permissions for sensitive materials
                        </Text>
                    </List.Item>
                </List>

                {/* Uploading Evidence Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Uploading Evidence</b>
                </Text>
                <List listStyleType="lower-alpha" spacing="sm" ml="1.5rem">
                    <List.Item>
                        <Text>
                            Select "Add Evidence" within any assessment
                            criterion
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Upload documents once and reference across multiple
                            criteria
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>Select files from your computer</Text>
                    </List.Item>
                    <List.Item>
                        <Text>Add metadata (name, URL, etc.)</Text>
                    </List.Item>
                    <List.Item>
                        <Text>Click "Upload" to add to the library</Text>
                    </List.Item>
                </List>
                <Text size="sm" c="dimmed">
                    The system accepts various formats (PDF, Word, Excel,
                    images) up to 25 MB per file.
                </Text>

                {/* Add Required Action Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Add the Required Action</b>
                </Text>
                <List listStyleType="decimal" spacing="sm">
                    <List.Item>
                        <Text>
                            Enter Action Item: In the "Action item" field, type
                            a clear and specific description of the action
                            required (e.g., "Conduct staff training on ESMS")
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Set Due Date: Use the date picker next to the
                            "mm/dd/yyyy" field to select a deadline for
                            completing the action (e.g., 06/15/2025)
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Tag Owner: In the "Tag Owner" dropdown, select the
                            responsible individual or team member to assign
                            ownership
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Click "Add Action" button to save the action item
                        </Text>
                    </List.Item>
                </List>

                {/* Tips and Troubleshooting Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Tips and Troubleshooting</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>Best Practices:</Text>
                        <List listStyleType="circle" spacing="sm" ml="1.5rem">
                            <List.Item>
                                Complete assessment sections in sequential order
                            </List.Item>
                            <List.Item>
                                Maintain consistent evidence documentation
                            </List.Item>
                            <List.Item>Update assessments regularly</List.Item>
                            <List.Item>
                                Conduct periodic reassessments
                            </List.Item>
                        </List>
                    </List.Item>
                </List>

                {/* Scoring and Evaluation Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Scoring and Evaluation</b>
                </Text>
                <Table striped highlightOnHover>
                    <thead className="bg-gray-50 text-xl">
                        <tr>
                            <th>Score Range</th>
                            <th>Grade</th>
                            <th>Compliance Level</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>0.0 - 0.2</td>
                            <td>D</td>
                            <td>Non-compliant</td>
                        </tr>
                        <tr>
                            <td>0.2 - 0.4</td>
                            <td>C</td>
                            <td>Potentially compliant</td>
                        </tr>
                        <tr>
                            <td>0.4 - 0.6</td>
                            <td>B</td>
                            <td>Largely compliant</td>
                        </tr>
                        <tr>
                            <td>0.6 - 0.8</td>
                            <td>A</td>
                            <td>Fully compliant</td>
                        </tr>
                        <tr>
                            <td>0.8 - 1.0</td>
                            <td>A+</td>
                            <td>Industry leading</td>
                        </tr>
                    </tbody>
                </Table>

                {/* Tip Section */}
                <Paper shadow="sm" p="md" radius="md" mt="xl" withBorder>
                    <Group position="apart">
                        <ThemeIcon variant="light" radius="xl" size="2rem">
                            <FaCheck size="1.2rem" />
                        </ThemeIcon>
                        <Text fw={500}>
                            You may return to incomplete sections later.
                        </Text>
                    </Group>
                </Paper>
            </Stack>
        </Box>
    );
}
