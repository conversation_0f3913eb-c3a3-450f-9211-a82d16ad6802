import Loading from "@/Components/Loading";
import {
  Button,
  Pagination,
  ScrollArea,
  Table,
  TextInput,
} from "@mantine/core";
import cx from "clsx";
import React, { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { AiOutlineExport } from "react-icons/ai";
import { CiSearch } from "react-icons/ci";
import CollectMultiselectFilter from "../CollectMultiselectFilter";

import useFormatTextKey from "@/hooks/useFormatTextKey";

export default function ManualTable({
  data,
  loading,
  Status,
  assetTypeAll,
  getTableData,
  error,
}) {
  useEffect(() => {
    !data && getTableData("manual");
  }, []);
  const { t } = useTranslation();
  const [value, setValue] = useState([]);
  const [search, setSearch] = useState("");
  const [sortedData, setSortedData] = useState();
  const [currentPage, setCurrentPage] = useState(1);
  const rowsPerPage = 5;
  const totalPages = Math.ceil(data?.length / rowsPerPage);
  // console.log(data);

  const currentData = useMemo(() => {
    return (
      (data &&
        data?.slice(
          (currentPage - 1) * rowsPerPage,
          currentPage * rowsPerPage
        )) ||
      []
    );
  }, [data, currentPage, rowsPerPage]);
  // console.log(sortedData);
  useEffect(() => {
    setSortedData(currentData);
    let filteredData;
    if (value[0] === "Pending") {
      filteredData = currentData.filter((item) => item.reviewState === null);
    } else if (value[0] === "Accepted") {
      filteredData = currentData.filter((item) => item.reviewState === true);
    } else if (value[0] === "Rejected") {
      filteredData = currentData.filter((item) => item.reviewState === false);
    } else {
      filteredData = currentData;
    }

    if (search) {
      filteredData = filteredData.filter((data) =>
        data.items.some((item) => {
          let activity = item.customFactor?.activity || {};
          let eFactors = item.customFactor?.eFactors || {};
          let uom = item.customFactor?.uom || {};

          return (
            item.companyAsset?.assetName
              ?.toLowerCase()
              .includes(search.toLowerCase()) ||
            Object.values(activity).some((value) =>
              value?.toString().toLowerCase().includes(search.toLowerCase())
            ) ||
            Object.values(eFactors).some((value) =>
              value?.toString().toLowerCase().includes(search.toLowerCase())
            ) ||
            Object.values(uom).some((value) =>
              value?.toString().toLowerCase().includes(search.toLowerCase())
            ) ||
            item.reportingYear?.toString().includes(search)
          );
        })
      );
    }
    setSortedData(filteredData);
  }, [value, search, currentData]);

  const handleSearchChange = (event) => {
    setSearch(event);
  };
  // console.log(sortedData);

  const rows = sortedData?.map((item, index) => {
    let assetType,
      fromDate,
      toDate,
      assetName,
      activity,
      eFactors,
      uom,
      quantity,
      reportingYear;
    const evidence = item?.uploadFileLinks?.evidence;
    const evidenceKey = Object.keys(evidence);
    const del = (e) => {
      e.target.closest("tr").remove();
    };
    item.items.forEach((items) => {
      const assets = assetTypeAll.find(
        (asset) => asset.id === items?.customFactor?.emissionSourceId
      );
      assetType = assets?.asset;
      fromDate = items.fromDate;
      toDate = items.toDate;
      assetName = items?.companyAsset?.assetName;
      activity = items?.customFactor?.activity;
      eFactors = items?.customFactor?.eFactors;
      uom = items?.customFactor?.uom;
      quantity = items?.quantity;
      reportingYear = items?.reportingYear;
    });
    return (
      <Table.Tr
        key={index}
        className={`${cx({
          // ["bg-[#07838F1A]"]: selected,
        })} text-sm font-bold text-[#626364] text-left`}
      >
        <Table.Td>
          <div className="w-20 block mx-auto">
            <p className="">
              {item.uploadedDT?.split("T")[1]?.substring(0, 5)}
            </p>
          </div>
        </Table.Td>
        <Table.Td>
          <div className="w-20 block mx-auto">
            <p className="">{fromDate}</p>
          </div>
        </Table.Td>
        <Table.Td>
          <div className="w-20 block mx-auto">
            <p className="">{toDate}</p>
          </div>
        </Table.Td>
        <Table.Td>
          <div className="w-32 block mx-auto">
            <p className="">{assetType}</p>
          </div>
        </Table.Td>
        <Table.Td>
          <div className="w-24 block mx-auto">
            <p className="">{assetName}</p>
          </div>
        </Table.Td>
        <Table.Td>
          <div className="block mx-auto min-w-36 text-nowrap">
            {activity &&
              Object.entries(activity).map(([key, value], idx) => (
                <p className="mt-2" key={idx}>
                  {useFormatTextKey(key)}: {value}
                </p>
              ))}
          </div>
        </Table.Td>
        <Table.Td>
          <div className="block mx-auto min-w-36 text-nowrap">
            {/* <p className="">{eFactors}</p> */}
            {eFactors &&
              Object.entries(eFactors).map(([key, value], idx) => (
                <p className="mt-2" key={idx}>
                  {useFormatTextKey(key)}: {value}
                </p>
              ))}
          </div>
        </Table.Td>
        <Table.Td>
          <div className="block mx-auto text-nowrap min-w-36">
            {/* <p className="">{uom}</p> */}
            {uom &&
              Object.entries(uom).map(([key, value], idx) => (
                <p className="mt-2" key={idx}>
                  {useFormatTextKey(key)}: {value}
                </p>
              ))}
          </div>
        </Table.Td>
        <Table.Td>
          <div className="block mx-auto min-w-36 text-nowrap">
            {/* <p className="">{quantity}</p> */}
            {quantity &&
              Object.entries(quantity).map(([key, value], idx) => (
                <p className="mt-2" key={idx}>
                  {useFormatTextKey(key)}: {value}
                </p>
              ))}
          </div>
        </Table.Td>
        <Table.Td>
          <div className="w-24 block mx-auto">
            <p className="">{reportingYear}</p>
          </div>
        </Table.Td>
        <Table.Td>
          <div className=" block mx-auto">
            <p
              className={`py-2 px-10 rounded-2xl`}
              style={{
                backgroundColor:
                  item.reviewState === null
                    ? Status.Pending.bg
                    : item.reviewState === true
                    ? Status.Accepted.bg
                    : item.reviewState === false
                    ? Status.Rejected.bg
                    : "",
                color:
                  item.reviewState === null
                    ? Status.Pending.text
                    : item.reviewState === true
                    ? Status.Accepted.text
                    : item.reviewState === false
                    ? Status.Rejected.text
                    : "",
              }}
            >
              {item.reviewState === null
                ? "Pending"
                : item.reviewState === true
                ? "Accepted"
                : item.reviewState === false
                ? "Rejected"
                : "Not Found"}
            </p>
          </div>
        </Table.Td>
        <Table.Td>
          <div className="">
            <p className="w-full">
              {evidenceKey.map((item, idx) => (
                <React.Fragment key={idx}>
                  {/* {console.log(item)} */}
                  <Button
                    key={idx}
                    variant="subtle"
                    href={evidence[item]}
                    target="_blank"
                    component="a"
                  >
                    {item}
                  </Button>
                </React.Fragment>
              ))}
            </p>
          </div>
        </Table.Td>
      </Table.Tr>
    );
  });

  return (
    <>
      {loading ? (
        <Loading />
      ) : (
        <div className="bg-white mt-7 p-1 rounded-lg">
          <div className="p-2 mt-1  grid items-center  xl:justify-between px-4 bg-white xl:grid-cols-3 rounded-t-lg w-full">
            <div className="xl:col-span-1 w-full flex justify-center xl:justify-start Manage-Uploaded-Records">
              <h1 className="font-bold text-lg text-black">
                {t("Recent upload manual input data")}
              </h1>
            </div>
            <div className="xl:col-span-2 grid items-center  justify-center grid-cols-1 lg:grid-cols-3 xl:grid-cols-4 sm:items-center w-full lg:gap-5">
              <TextInput
                className="w-full col-span-2 Search-manual-collect"
                placeholder="Search by Topic Area or Assessment Question"
                leftSection={<CiSearch className="w-5 h-5" />}
                leftSectionPointerEvents="none"
                value={search}
                onChange={(e) => handleSearchChange(e.target.value)}
                // disabled
              />{" "}
              <div className=" md:col-span-1 m-3 Filter-manual-collect bg-white hover:bg-white border-2 border-[#E8E7EA] rounded-lg shadow-sm md:ms-auto mx-auto w-full">
                <CollectMultiselectFilter
                  setValue={setValue}
                  value={value}
                  style={
                    "text-[#9E939A] font-semibold  w-full flex justify-center items-center cursor-pointer py-2 text-nowrap"
                  }
                />
              </div>
              <div className="md:col-span-1 Export-Records  justify-center items-center  p-2 rounded-lg shadow-sm w-full">
                <Button className="bg-[#e6f3f4]  text-primary hover:bg-[#e6f3f4] hover:text-[#00C0A9] rounded-lg w-full">
                  {t("export")}
                  <AiOutlineExport className="ms-2" />
                </Button>
              </div>
            </div>
          </div>
          <>
            {(search && sortedData?.length === 0) ||
            (value.length && sortedData?.length === 0) ? (
              <h1 className="mt-5 text-center capitalize">
                Your Search is not Found
              </h1>
            ) : (
              <div className="w-full">
                <Table.ScrollContainer
                  className="scrollable-container"
                  maw={"99%"}
                >
                  <Table
                    // miw={1000}
                    verticalSpacing="sm"
                    className="p-2 bg-white shadow-lg"
                    withTableBorder
                    highlightOnHover
                  >
                    <Table.Thead className="border-b-2 border pb-6 bg-[#f5f4f5] text-secondary-500 font-bold text-base">
                      <Table.Tr>
                        <Table.Th className="text-left">Timestamp</Table.Th>
                        <Table.Th className="text-left">From</Table.Th>
                        <Table.Th className="text-left">To</Table.Th>
                        <Table.Th className="text-left">Assets Type</Table.Th>
                        <Table.Th className="text-left">Assets</Table.Th>
                        <Table.Th className="text-left">Activity</Table.Th>
                        <Table.Th className="text-left">E-Factors</Table.Th>
                        <Table.Th className="text-left">UOM</Table.Th>
                        <Table.Th className="text-left">Quantity</Table.Th>
                        <Table.Th className="text-left text-nowrap">
                          Reporting Year
                        </Table.Th>
                        <Table.Th className="text-center text-nowrap Status-Overview">
                          Status
                        </Table.Th>
                        <Table.Th className="text-left">
                          Evidence/Notes
                        </Table.Th>
                        {/* <Table.Th className="text-center">

                <span className="flex gap-3 justify-center">
                  <span className="bg-white text-[#404B52] rounded-md w-8 h-8 flex justify-center items-center text-sm font-medium cursor-pointer">
                    <MdKeyboardArrowLeft />
                  </span>
                  <span className="bg-white text-[#404B52] rounded-md w-8 h-8 flex justify-center items-center text-sm font-medium cursor-pointer">
                    <MdKeyboardArrowRight />
                  </span>
                </span>
              </Table.Th> */}
                      </Table.Tr>
                    </Table.Thead>
                    <Table.Tbody>{rows}</Table.Tbody>
                  </Table>
                </Table.ScrollContainer>
                <div className="md:flex justify-between mt-5">
                  <p
                    className="text-sm text-black"
                    hidden={!sortedData?.length}
                  >
                    {t("showingData", {
                      start: (currentPage - 1) * rowsPerPage + 1,
                      end: Math.min(currentPage * rowsPerPage, data?.length),
                      total: data?.length,
                    })}
                  </p>
                  <Pagination
                    page={currentPage}
                    onChange={(e) => {
                      setCurrentPage(e);
                    }}
                    total={totalPages}
                    color="#dde7e9"
                    autoContrast
                    className={`flex justify-center mt-5 gap-0 md:mt-0 ${
                      !sortedData?.length && "hidden"
                    }`}
                    classNames={{ control: "gap:none rounded-none" }}
                  />
                </div>
              </div>
            )}
          </>
        </div>
      )}
    </>
  );
}
