import ApiS1Config from "@/Api/apiS1Config";
import Loading from "@/Components/Loading";
import MainLayout from "@/Layout/MainLayout";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import { Button } from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { FaCheckCircle } from "react-icons/fa";
import { GoShare } from "react-icons/go";
import { useNavigate } from "react-router";
import HrddTable from "./Partials/HrddTable";
import HrddReport from "./Partials/HrddReport";
import { IoMdHome } from "react-icons/io";

const Hrdd = () => {
  const { hrdd } = useSideBarRoute();
  const [active, setActive] = useState("Assessment");

  const [selectedScope, setSelectedScope] = useState();
  const [postLoading, setPostLoading] = useState(false);
  const [postReport, setPostReport] = useState(false);
  const { t } = useTranslation();
  const navigate = useNavigate();

  const PriorityLevels = {
    1: "Low",
    2: "Medium",
    3: "High",
    4: "Top",
  };

  const ReadinessLevel = {
    1: "Not Started",
    2: "Initial Planning",
    3: "In Development",
    4: "Partially Implemented",
    5: "Fully Implemented",
    6: "Not Applicable",
  };

  const readinessColorMap = {
    "Not Started": {
      bg: "#AB020233",
      border: "#AB0202",
      text: "#AB0202",
    },
    "Not Applicable": {
      bg: "#e0e2e7",
      border: "#667085",
      text: "#667085",
    },
    "Initial Planning": {
      bg: "#e9dff3",
      border: "#9160C1",
      text: "#9160C1",
    },
    "In Development": {
      bg: "#ffeecd",
      border: "#FFAB07",
      text: "#FFAB07",
    },
    "Partially Implemented": {
      bg: "#d4e8fb",
      border: "#298BED",
      text: "#298BED",
    },
    "Fully Implemented": {
      bg: "#e2f6e7",
      border: "#01BD36",
      text: "#01BD36",
    },
  };

  const prioritySelectColorMap = {
    Top: {
      bg: "#AB02024D",
      border: "",
      text: "#AB0202",
    },
    High: {
      bg: "#ffd0b5",
      border: "#ffedca",
      text: "#ff6e1d",
    },
    Medium: {
      bg: "#ffe6b5",
      border: "#00C0A9",
      text: "#ffb932",
    },
    Low: {
      bg: "#c7f1d3",
      border: "#01BD36",
      text: "#01BD36",
    },
  };

  const getHRDDData = async () => {
    try {
      const { data } = await ApiS1Config.get("/get_assessment_data", {
        headers: { assessmentName: "Human Rights Due Diligence (HRDD)" },
      });
      setSelectedScope(data["Human Rights Due Diligence (HRDD)"]);
    } catch (error) {
      error.response.data.Message === "There is no active assessment"
        ? navigate("/hrdd-report")
        : "";
      //console.log(error);
    }
  };

  useEffect(() => {
    getHRDDData();
  }, []);

  const getReport = async () => {
    setPostReport(true);

    try {
      const { data } = await ApiS1Config.post(
        `/get_report`,
        {},
        {
          headers: {
            assessmentType: "Human Rights Due Diligence (HRDD)",
            status: true,
          },
        }
      );
      notifications.show({
        title: data?.Message,
        color: "green",
        icon: <FaCheckCircle />,
      });
      navigate("/hrdd-report");

      setPostReport(false);
    } catch (error) {
      setPostReport(false);
      //console.log(error);
      navigate("/hrdd-report");
      // notifications.show({
      //   title: error.response?.data?.Message || "An error occurred",
      //   color: "red",
      // });
    }
  };
  const postAnswers = async (edata) => {
    setPostLoading(true);
    //console.log(selectedScope);
    try {
      setPostLoading(true);
      const { data } = await ApiS1Config.post(
        `/post_scope`,

        edata,

        {
          headers: {
            assessmentName: "Human Rights Due Diligence (HRDD)",
          },
        }
      );
      setPostLoading(false);
      // //console.log(data);
      notifications.show({
        title: "Updated successfully!",
        color: "green",
        icon: <FaCheckCircle />,
      });
      getHRDDData();
    } catch (error) {
      setPostLoading(false);
      //console.log(error);
      notifications.show({
        title: error.response?.data?.Message || "An error occurred",
        color: "red",
      });
    }
  };

  let isAllSolved = selectedScope?.solved;

  return (
    <MainLayout 
    breadcrumbItems={[
      { title: <IoMdHome size={20}/>, href: "/get-started" },
      { title: "Due Diligence", href: "/Grc/due-diligence" },
      { title: "HRDD Due Diligence", href: "#" },
    ]}
    menus={hrdd} navbarTitle={"Human Rights Due Diligence (HRDD)"}>
      <div className="grid lg:grid-cols-2 justify-center bg-[#FFFFFF] border-2 border-[#E8E7EA] p-6 gap-5 py-3 mb-6 rounded-lg text-primary">
        <button
          className={`relative text-lg font-semibold py-3 px-6 transition-all ${
            active === "Assessment"
              ? "active-tab rounded"
              : "text-[#5A5A5A] rounded-lg border-2"
          }`}
          onClick={() => setActive("Assessment")}
        >
          {t("Assessment")}
        </button>

        <button
          className={`relative text-lg font-semibold py-3 px-6 transition-all ${
            active === "Report"
              ? "active-tab rounded"
              : "text-[#5A5A5A] rounded-lg border-2"
          }`}
          onClick={() => setActive("Report")}
        >
          {t("Report")}
        </button>
      </div>

      {active == "Assessment" ? (
        <>
          <div className="items-center justify-between w-full px-4 py-5 bg-white rounded-lg shadow-md mb-7 sm:flex ">
            <div>
              <h1 className="text-lg font-bold text-black">
                {t("humanRightsDueDiligence")}
              </h1>
              <p className="font-normal text-sm text-[#667085]">
                {t("descriptiveBodyText")}
              </p>
            </div>
            <div className="mt-5 sm:mt-0 grid md:grid-cols-2  gap-5">
              <Button
                disabled={postReport || !isAllSolved}
                onClick={getReport}
                className={`text-white rounded-md bg-primary  ${
                  !isAllSolved
                    ? "cursor-not-allowed opacity-50"
                    : "hover:bg-primary hover:opacity-90 "
                }`}
                size="md"
              >
                {postReport ? <Loading /> : "Assess"}
              </Button>
              <Button
                className="duration-200 ease-out bg-transparent border-2 rounded-lg text-primary border-secondary-300 hover:-translate-y-2 hover:bg-secondary-400 hover:bg-transparent hover:text-primary outline-none focus:outline-none"
                size="md"
              >
                <GoShare className="me-2" />
                {t("export")}
              </Button>
            </div>
          </div>
          <HrddTable
            // priorityBadgeColorMap={priorityBadgeColorMap}
            readinessColorMap={readinessColorMap}
            prioritySelectColorMap={prioritySelectColorMap}
            selectedScope={selectedScope}
            // setSelectedScope={setSelectedScope}
            PriorityLevels={PriorityLevels}
            ReadinessLevel={ReadinessLevel}
            postAnswers={postAnswers}
            // setPostAnswersState={setPostAnswersState}
            // PostAnswersState={PostAnswersState}
            postLoading={postLoading}
          />
        </>
      ) : (
        <>
          <HrddReport />
        </>
      )}
    </MainLayout>
  );
};

export default Hrdd;
