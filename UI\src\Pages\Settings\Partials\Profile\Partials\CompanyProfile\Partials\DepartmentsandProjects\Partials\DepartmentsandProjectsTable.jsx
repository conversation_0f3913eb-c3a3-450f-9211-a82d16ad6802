import { But<PERSON>, Checkbox, ScrollArea, Table, Pagination } from "@mantine/core";
import { BiEdit, BiTrash } from "react-icons/bi";
import { useTranslation } from "react-i18next";
import DepartmentandProjectsRow from "./DepartmentandProjectsRow";
import { useState, useEffect } from "react";
import Loading from "@/Components/Loading";
import ApiS2 from "@/Api/apiS2Config";
import { showNotification } from "@mantine/notifications";
import _ from "lodash";

const DepartmentsandProjectsTable = ({ searchTerm = "", refreshTrigger }) => {
  const { t } = useTranslation();

  const msg = (txt, color = "green") => {
    showNotification({ message: txt, color: color });
  };

  const [selection, setSelection] = useState([]);
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [itemId, setItemId] = useState(0);
  
  // Data fetching states
  const [tableData, setTableData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [loading, setLoading] = useState(true);
  
  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const rowsPerPage = 7;

  // Fetch departments and projects
  const fetchDepartmentsAndProjects = async () => {
    setLoading(true);
    try {
      const response = await ApiS2.get(
        `https://portal-auth-main-staging.azurewebsites.net/departments-and-projects`
      );
      const data = response.data;
      
      console.log("Fetched data:", data.departmentsAndProjects?.length, "items"); // Debug log
      setTableData(data.departmentsAndProjects || []);
      
    } catch (error) {
      console.error("Fetch error:", error);
      msg(error.response?.data?.message || "Failed to fetch data", "red");
      setTableData([]);
    } finally {
      setLoading(false);
    }
  };

  // Filter data based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredData(tableData);
    } else {
      const filtered = tableData.filter(item => {
        const searchLower = searchTerm.toLowerCase();
        
        // Handle locations array search
        const locationsMatch = Array.isArray(item.locations) 
          ? item.locations.some(location => location.toLowerCase().includes(searchLower))
          : item.locations?.toLowerCase().includes(searchLower);

        return (
          item.name?.toLowerCase().includes(searchLower) ||
          item.type?.toLowerCase().includes(searchLower) ||
          item.region?.toLowerCase().includes(searchLower) ||
          item.country?.toLowerCase().includes(searchLower) ||
          item.sector?.toLowerCase().includes(searchLower) ||
          locationsMatch
        );
      });
      setFilteredData(filtered);
    }
    // Reset to first page when search changes
    setCurrentPage(1);
  }, [tableData, searchTerm]);

  // Fetch data on component mount and when refreshTrigger changes
  useEffect(() => {
    fetchDepartmentsAndProjects();
  }, [refreshTrigger]); 

  const fetchAgain = async () => {
    console.log("Fetching updated data..."); // Debug log
    await fetchDepartmentsAndProjects();
  };

  // Pagination logic
  const chunkedData = _.chunk(filteredData, rowsPerPage);
  const totalPages = chunkedData.length || 0;
  const currentData = chunkedData[Math.min(currentPage - 1, totalPages - 1)] || [];

  const toggleRow = (id) =>
    setSelection((current) =>
      current.includes(id)
        ? current.filter((item) => item !== id)
        : [...current, id]
    );

  const toggleAll = () =>
    setSelection((current) => {
      if (currentData.length === 0) return [];
      return current.length === currentData.length
        ? []
        : currentData.map((item) => item.id);
    });

  const handleEdit = (id) => {
    setItemId(id);
    // Optionally refresh data when starting edit to ensure we have latest data
    fetchAgain();
  };

  const handleCancelEdit = () => {
    setItemId(0);
    // Refresh data when canceling edit to revert any unsaved changes visually
    fetchAgain();
  };

  // Enhanced setItemId function that also triggers reload
  const handleSetItemId = (id) => {
    setItemId(id);
    if (id === 0) {
      // When exiting edit mode, refresh the data
      fetchAgain();
    }
  };

  const handleDelete = async () => {
    if (selection.length === 0) {
      msg("Please select items to delete", "red");
      return;
    }

    setIsDeleting(true);
    try {
      // Delete each item individually
      const deletePromises = selection.map(id => 
        ApiS2.delete(`https://portal-auth-main-staging.azurewebsites.net/company-admin/departments-and-projects/${id}`)
      );
      
      await Promise.all(deletePromises);
      
      msg(`Successfully deleted ${selection.length} item(s)`);
      setSelection([]);
      setIsDeleteOpen(false);
      fetchAgain();
    } catch (error) {
      console.error("Delete error:", error);
      msg(error.response?.data?.message || "Failed to delete items", "red");
    } finally {
      setIsDeleting(false);
    }
  };

  const rows = currentData.map((item) => {
    return (
      <DepartmentandProjectsRow
        toggleRow={toggleRow}
        key={`${item.id}-${item.name}-${item.region}-${item.country}-${item.sector}`} // Force re-render when data changes
        item={item}
        selection={selection}
        fetchAgain={fetchAgain}
        itemId={itemId}
        setItemId={handleSetItemId} // Using enhanced version
      />
    );
  });

  return (
    <div className="my-8">
      <ScrollArea>
        <Table
          miw={800}
          verticalSpacing="sm"
          border={2}
          borderColor=""
          withTableBorder
        >
          <Table.Thead className="bg-secondary-lite-gray pb-6 text-base font-medium text-center">
            <Table.Tr>
              <Table.Th>
                <Checkbox
                  onChange={toggleAll}
                  checked={
                    currentData.length > 0 &&
                    selection.length === currentData.length
                  }
                  indeterminate={
                    selection.length > 0 && selection.length < currentData.length
                  }
                  color="#07838F"
                />
              </Table.Th>
              <Table.Th className="text-left">{t("Name")}</Table.Th>
              <Table.Th className="text-left">{t("Region")}</Table.Th>
              <Table.Th className="text-left">{t("Country")}</Table.Th>
              <Table.Th className="text-left">{t("Sector")}</Table.Th>
              <Table.Th className="text-left">{t("No. of Employees")}</Table.Th>
              <Table.Th className="text-left">{t("Locations")}</Table.Th>
              <Table.Th className="text-left">{t("No. of Assets")}</Table.Th>
              <Table.Th className="text-left">{t("Action")}</Table.Th>
              {itemId > 0 && <Table.Th className="text-left">Actions</Table.Th>}
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {loading ? (
              <Table.Tr>
                <Table.Td colSpan={itemId > 0 ? 10 : 9} className="text-center py-8">
                  <Loading />
                  <div className="mt-2">Loading departments and projects...</div>
                </Table.Td>
              </Table.Tr>
            ) : currentData.length === 0 ? (
              <Table.Tr>
                <Table.Td colSpan={itemId > 0 ? 10 : 9} className="text-center py-8 text-gray-500">
                  {searchTerm ? `No results found for "${searchTerm}"` : "No departments or projects found"}
                </Table.Td>
              </Table.Tr>
            ) : (
              rows
            )}
          </Table.Tbody>
        </Table>
      </ScrollArea>

      {/* Pagination Controls */}
      {!loading && filteredData.length > 0 && totalPages > 1 && (
        <div className="flex justify-end mt-4">
          <Pagination
            color="#05808b"
            value={Math.min(currentPage, totalPages)}
            onChange={setCurrentPage}
            total={totalPages}
          />
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex gap-4 mt-6 items-center">
        {/* Edit Button */}
        {selection.length === 1 && itemId === 0 && (
          <Button
            className="flex items-center gap-2 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
            onClick={() => handleEdit(selection[0])}
            type="button"
          >
            <BiEdit size={16} />
            Edit Selected
          </Button>
        )}

        {/* Cancel Edit Button */}
        {itemId > 0 && (
          <Button
            className="flex items-center gap-2 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg"
            onClick={handleCancelEdit}
            type="button"
          >
            Cancel Edit
          </Button>
        )}

        {/* Delete Confirmation/Delete Button */}
        {isDeleteOpen ? (
          <Button
            disabled={isDeleting}
            className={`flex items-center gap-2 font-bold text-white bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg ${
              isDeleting ? "opacity-50 cursor-not-allowed" : ""
            }`}
            onClick={handleDelete}
            type="button"
          >
            {isDeleting ? (
              <>
                Deleting... <Loading />
              </>
            ) : (
              <>
                <BiTrash size={16} />
                Confirm Delete ({selection.length})
              </>
            )}
          </Button>
        ) : (
          <Button
            disabled={selection.length === 0}
            className={`flex items-center gap-2 font-bold  text-red-500 hover:bg-red-50 px-4 py-2 rounded-lg ${
              selection.length === 0 ? "opacity-50 cursor-not-allowed" : ""
            }`}
            onClick={() => setIsDeleteOpen(true)}
            type="button"
          >
            <BiTrash size={16} />
            {/* Delete Selected ({selection.length}) */}
          </Button>
        )}

        {/* Cancel Delete Button */}
        {isDeleteOpen && (
          <Button
            className="flex items-center gap-2 bg-gray-400 hover:bg-gray-500 text-white px-4 py-2 rounded-lg"
            onClick={() => setIsDeleteOpen(false)}
            type="button"
          >
            Cancel
          </Button>
        )}
      </div>

      {/* Selection Info */}
      {!loading && selection.length > 0 && (
        <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
          <p className="text-sm text-blue-700">
            {selection.length === 1 
              ? "1 item selected" 
              : `${selection.length} items selected`}
            {selection.length > 1 && " - Only delete operation available for multiple selections"}
          </p>
        </div>
      )}

      {/* Data Summary */}
      {!loading && filteredData.length > 0 && (
        <div className="mt-4 text-sm text-gray-600">
          {searchTerm ? (
            `Showing ${filteredData.length} result(s) for "${searchTerm}"`
          ) : (
            `Showing ${currentData.length} of ${filteredData.length} departments/projects${
              totalPages > 1 ? ` (Page ${currentPage} of ${totalPages})` : ""
            }`
          )}
        </div>
      )}
    </div>
  );
};

export default DepartmentsandProjectsTable;