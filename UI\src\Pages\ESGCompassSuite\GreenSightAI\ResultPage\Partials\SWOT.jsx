import React from "react";
import { FaDotCircle } from "react-icons/fa";
import SWOTStat from "./SWOTStat";

const SWOT = ({ scopeTitle, statsS, statsO, statsW, statsT }) => {
  return (
    <>
      {statsS.length > 0 && (
        <div className="flex flex-col w-full shadow-md mb-2 p-3 rounded-md">
          {scopeTitle && (
            <h2 className="font-bold text-xl mb-2">{scopeTitle}</h2>
          )}
          <div className="text-primary tracking-widest text-shadow-custom text-4xl font-times text-center">
            <span>SWOT</span>
          </div>
          <div className="mt-6 grid grid-cols-1 gap-x-6 gap-y-10 sm:grid-cols-1 lg:grid-cols-2 xl:grid-cols-2 xl:gap-x-2 mx-auto w-full">
            {statsS.length > 0 && <SWOTStat title="Strengths" stats={statsS} />}
            {statsO.length > 0 && (
              <SWOTStat title="Weaknesses" stats={statsW} />
            )}
            {statsW.length > 0 && <SWOTStat title="Opportunities" stats={statsO} />}
            {statsT.length > 0 && <SWOTStat title="Possible Threats" stats={statsT} />}
          </div>
          
        </div>
      )}
    </>
  );
};

export default SWOT;
