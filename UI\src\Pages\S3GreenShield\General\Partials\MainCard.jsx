import { Button, Image } from '@mantine/core';
import { MdHistory } from 'react-icons/md';
import { useNavigate } from 'react-router-dom';
export default function MainCard({ img, headTittle, mt ,link}) {
    const navigate =useNavigate();
    return (
        <>
            <div className='w-full h-full'>
                <div className='bg-[#00c0a9] h-[263px] flex justify-center items-center'>
                    <Image src={img} width={800} height={800} />
                </div>
                <div className='bg-[#07838f] lg:h-[295px] pb-5'>
                    <div className='text-center'>
                        <h1 className='font-extrabold text-3xl text-white pt-10'>{headTittle}</h1>
                    </div>
                    <div className={`grid sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-x-10 ${mt} lg:mt-24 px-5`}>
                        {/* <Link className='' to={link}> */}
                            <Button className='border-2 border-white rounded-lg text-white bg-inherit px-1 text-clip opacity-30' disabled size='md' onClick={()=>navigate(link)}>
                                <span className='me-1'><MdHistory /></span>
                                <span>Assessment History</span>
                            </Button>
                        {/* </Link> */}
                        {/* <Link to={link}> */}
                            <Button className='bg-[#daedee] rounded-lg text-primary hover:bg-[#00c0a9] px-[50px] overflow-auto mt-3 sm:mt-0 md:mt-0 lg:mt-3 xl:mt-0' size='md' onClick={()=>navigate(link)}>Start Now</Button>
                        {/* </Link> */}
                    </div>
                </div>

            </div>
        </>
    )
}
