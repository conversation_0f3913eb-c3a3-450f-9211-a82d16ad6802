import React from "react";
import { But<PERSON>, Image } from "@mantine/core";
// import first from "@/assets/images/83820d3ab2515faa61515cfbc9674702.png";
import { SlLock } from "react-icons/sl";
import { Link, Navigate, useNavigate } from "react-router-dom";
export default function MainCard({
  topColor,
  bodyColor,
  borderColor,
  img,
  headTittle,
  mt,
  link,
}) {
  const navigate = useNavigate();
  return (
    <>
      <div className="w-full h-full p-2">
        <div
          className={`${topColor} h-[263px] flex justify-center items-center border-2 ${borderColor} rounded-t-lg`}
        >
          <Image src={img} className="w-1/2" width={800} height={800} />
        </div>
        <div
          className={` ${bodyColor} 2xl:h-[295px] p-4 rounded-b-lg flex flex-col items-center justify-center`}
        >
          <div className="flex p-2 h-[80px]  items-center justify-center text-center ">
            <h1 className="font-extrabold text-lg sm:text-xl md:text-2xl lg:text-3xl text-white">
              {headTittle}
            </h1>
          </div>

          <div
            className={`flex items-center justify-start  p-2 gap-x-10  ${mt} lg:mt-24 px-3`}
          >
            <Button
              className="bg-[#daedee] border border-[#fff] rounded-lg text-white px-12 py-2 bg-opacity-20"
              size="md"
              onClick={() => navigate(link)}
            >
              <span className="me-1">
                <SlLock />
              </span>
              <span>Start Now</span>
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
