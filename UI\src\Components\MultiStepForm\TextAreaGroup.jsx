import { useState, useEffect, useCallback, useRef } from "react";
import { AIIcon } from "@/assets/icons";
import { Loader } from "@mantine/core";
import { debounce } from "lodash";
import Cookies from "js-cookie";

import io from "socket.io-client";

import { useDisclosure } from "@mantine/hooks";
import { CiCircleInfo } from "react-icons/ci";
import { HiSparkles } from "react-icons/hi2";
import { IoIosArrowRoundForward, IoIosArrowRoundBack } from "react-icons/io";

// Fetch and cache user ID
const fetchUserId = async (token) => {
  try {
    const response = await fetch(
      "https://portal-auth-main-staging.azurewebsites.net/account_information",
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );
    if (!response.ok) {
      throw new Error(`HTTP error: ${response.status}`);
    }
    const data = await response.json();
    return data.userId || null;
  } catch (error) {
    console.error("[fetchUserId] Failed to fetch user ID:", error.message);
    return null;
  }
};

let cachedUserId = null;

const AIModal = ({
  opened,
  onClose,
  localValue,
  setLocalValue,
  handleEnhanceAnswer,
}) => {
  const [responses, setResponses] = useState([localValue]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [modalLoading, setModalLoading] = useState(false);
  const [newRespons, setNewRespons] = useState(false);

  const addResponse = (newResponse) => {
    setResponses((prev) => {
      const newResponses = [...prev, newResponse];
      setCurrentIndex(newResponses.length - 1);
      return newResponses;
    });
  };

  const handleAction = async (outputType) => {
    setModalLoading(true);
    const newResponse = await handleEnhanceAnswer(outputType, localValue);
    if (newResponse) {
      addResponse(newResponse);
      setNewRespons(true);
    }
    setModalLoading(false);
  };

  const handleAccept = () => {
    setLocalValue(responses[currentIndex]);
    if (newRespons) {
      onClose();
    }
  };

  if (!opened) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div className="bg-white rounded-lg shadow-lg w-[600px] p-4 relative">
        <div>
          <div className="text-lg flex items-center gap-2 bg-gradient-to-r from-[#2C5085] via-[#236D8B] to-[#12B6AD] bg-clip-text text-transparent font-semibold mb-4 text-start">
            <span>
              <HiSparkles className="text-[#07838F]" />
            </span>
            <p>Levelup AI</p>
          </div>
          <button
            onClick={onClose}
            className="absolute top-4 text-lg right-4 text-gray-950 hover:text-gray-700"
          >
            ×
          </button>
        </div>
        <hr className="mb-4 h-[2px] w-full bg-gradient-to-r from-[#2C5A8C] via-[#1C889C] to-[#13B1A8]" />

        <div className="flex justify-end gap-2 mb-4">
          <button
            onClick={() => handleAction("generate")}
            className={`px-3 py-1 ${
              newRespons
                ? "bg-[#07838F1A] text-[#07838F]"
                : "bg-gradient-to-r from-[#2C5A8C] via-[#1C889C] to-[#13B1A8] text-white"
            }  font-semibold rounded-md disabled:opacity-50`}
            disabled={modalLoading}
          >
            {modalLoading ? "Loading..." : "Generate"}
          </button>
          <button
            onClick={handleAccept}
            className={`px-3 py-1  ${
              newRespons
                ? "bg-gradient-to-r from-[#2C5A8C] via-[#1C889C] to-[#13B1A8]"
                : "bg-gray-400 cursor-not-allowed"
            } text-white font-semibold rounded-md `}
            disabled={modalLoading}
          >
            Accept
          </button>
        </div>

        <div className="border p-3 rounded-md bg-gray-100 max-h-[100px] overflow-y-auto mb-4 text-gray-900">
          {responses[currentIndex]}
        </div>

        <hr className="my-4" />

        <div className="flex justify-between w-full">
          <div className="flex justify-center gap-2">
            <button
              onClick={() => handleAction("refine")}
              className="px-3 py-1 border-[1px] border-[#CCCCCC] text-[#808080] rounded-md disabled:opacity-50"
              disabled={modalLoading}
            >
              {modalLoading ? "Loading..." : "Refine"}
            </button>
            <button
              onClick={() => handleAction("shorten")}
              className="px-3 py-1 border-[1px] border-[#CCCCCC] text-[#808080] rounded-md disabled:opacity-50"
              disabled={modalLoading}
            >
              {modalLoading ? "Loading..." : "Shorten"}
            </button>
            <button
              onClick={() => handleAction("lengthen")}
              className="px-3 py-1 border-[1px] border-[#CCCCCC] text-[#808080] rounded-md disabled:opacity-50"
              disabled={modalLoading}
            >
              {modalLoading ? "Loading..." : "Lengthen"}
            </button>
          </div>
          <div className="flex justify-center gap-2">
            <button
              onClick={() => setCurrentIndex((prev) => Math.max(prev - 1, 0))}
            >
              <IoIosArrowRoundBack className="text-3xl" />
            </button>

            <button
              onClick={() =>
                setCurrentIndex((prev) =>
                  Math.min(prev + 1, responses.length - 1)
                )
              }
            >
              <IoIosArrowRoundForward className="text-3xl" />
            </button>
          </div>
        </div>
        <hr className="my-4" />
        <div>
          <span className="flex items-center w-fit gap-2 bg-[#07838F1A] px-3 py-1 underline text-[#07838F] rounded-full">
            <CiCircleInfo />
            Verify responses before use.
          </span>
        </div>
      </div>
    </div>
  );
};

export function TextAreaGroup({
  id,
  label,
  question,
  value,
  setSections,
  socketRef,
}) {
  const [localValue, setLocalValue] = useState(value || "");
  const [loadingAI, setLoadingAI] = useState(false);
  const [isButtonVisible, setIsButtonVisible] = useState(!!value?.trim());
  const [isSomeoneTyping, setIsSomeoneTyping] = useState(false);
  const [typingUser, setTypingUser] = useState(null);
  const [currentUserId, setCurrentUserId] = useState(cachedUserId);
  const [opened, { open, close }] = useDisclosure(false);

  const isTypingRef = useRef(false);
  const isLocalTypingRef = useRef(false);

  // Create a stable debounce function for stop_typing
  const stopTypingDebounce = useRef(
    debounce(() => {
      if (isTypingRef.current) {
        sendTypingStatus("stop_typing");
        isTypingRef.current = false;
      }
    }, 2000)
  ).current;

  // Send typing status event
  const sendTypingStatus = (type, text = null) => {
    if (socketRef.current?.connected && currentUserId) {
      socketRef.current.emit(type, {
        disclosure_id: id,
        user: { user_id: currentUserId },
        ...(text !== null && { disclosure_text: text }),
      });
    } else {
      console.warn(
        `[sendTypingStatus] Socket not connected or user_id missing, cannot send ${type}`
      );
    }
  };

  // Debounced typing event
  const debouncedTyping = useCallback(
    debounce((newValue) => {
      if (isLocalTypingRef.current) {
        sendTypingStatus("typing", newValue);
      }
    }, 250),
    [currentUserId, id]
  );

  // Updated handleEnhanceAnswer
  const handleEnhanceAnswer = async (
    outputType = "generate",
    inputValue = localValue
  ) => {
    if (!inputValue.trim()) {
      return null;
    }
    setLoadingAI(true);

    try {
      const token = Cookies.get("level_user_token");
      if (!token) {
        throw new Error("Token not found");
      }

      const response = await fetch(
        "https://gen-ai0-staging.azurewebsites.net/process_request",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            processor: "issb_report",
            resources: {
              output_type: outputType !== "generate" ? outputType : undefined,
              question: question.requirement_text,
              user_answer: inputValue,
            },
          }),
        }
      );

      const data = await response.json();
      if (data?.ai_response?.enhanced_answer) {
        const enhancedAnswer = data.ai_response.enhanced_answer;
        sendTypingStatus("typing", enhancedAnswer);
        setTimeout(() => {
          sendTypingStatus("stop_typing");
          isTypingRef.current = false;
        }, 500);
        return enhancedAnswer;
      }
      return null;
    } catch (error) {
      console.error("[handleEnhanceAnswer] Error fetching AI response:", error);
      return null;
    } finally {
      setLoadingAI(false);
    }
  };

  // Update the button click to open modal
  const handleAIIconClick = () => {
    open();
  };

  // Handle textarea focus
  const handleFocus = () => {
    if (!currentUserId || !socketRef.current?.connected) {
      return;
    }
    sendTypingStatus("typing", localValue);
    isTypingRef.current = true;
    isLocalTypingRef.current = true;
  };

  // Handle textarea blur
  const handleBlur = () => {
    if (!currentUserId || !socketRef.current?.connected) {
      return;
    }
    sendTypingStatus("stop_typing");
    isTypingRef.current = false;
    isLocalTypingRef.current = false;
    stopTypingDebounce.cancel(); // Cancel any pending debounce
  };

  // Handle input change
  const handleInputChange = (e) => {
    const newValue = e.target.value;
    setLocalValue(newValue);
    setIsButtonVisible(!!newValue.trim());

    if (!currentUserId || !socketRef.current?.connected) {
      return;
    }

    isTypingRef.current = true;
    isLocalTypingRef.current = true;
    debouncedTyping(newValue);
    stopTypingDebounce(); // Reset the debounce timer
  };

  // Set up Socket.IO
  useEffect(() => {
    const token = Cookies.get("level_user_token");
    if (!token) {
      return;
    }

    const initialize = async () => {
      if (!cachedUserId) {
        const userId = await fetchUserId(token);
        if (!userId) {
          return;
        }
        cachedUserId = userId;
        setCurrentUserId(userId);
      } else {
        setCurrentUserId(cachedUserId);
      }

      socketRef.current.on("typing", (data) => {
        if (data.disclosure_id === id) {
          if (data.user?.user_id === currentUserId) {
            return;
          }
          if (isLocalTypingRef.current) {
            return;
          }
          setTypingUser(data.user);
          setIsSomeoneTyping(true);
          if (data.disclosure_text !== localValue) {
            setLocalValue(data.disclosure_text);
            setSections((prevSections) => {
              const updatedSections = [...prevSections];
              const sectionIndex = updatedSections.findIndex((section) =>
                section.topics.some((topic) =>
                  topic.disclosures.some((d) => d.id === id)
                )
              );
              if (sectionIndex !== -1) {
                const topicIndex = updatedSections[
                  sectionIndex
                ].topics.findIndex((topic) =>
                  topic.disclosures.some((d) => d.id === id)
                );
                const disclosureIndex = updatedSections[sectionIndex].topics[
                  topicIndex
                ].disclosures.findIndex((d) => d.id === id);
                updatedSections[sectionIndex].topics[topicIndex].disclosures[
                  disclosureIndex
                ].disclosure_text = data.disclosure_text;
              }
              return updatedSections;
            });
          }
        }
      });

      socketRef.current.on("stop_typing", (data) => {
        if (data.disclosure_id === id && data.user?.user_id !== currentUserId) {
          setIsSomeoneTyping(false);
          setTypingUser(null);
        }
      });
    };

    initialize();

    return () => {
      stopTypingDebounce.cancel();
    };
  }, [id, setSections, value]);

  // Update local value when prop value changes
  useEffect(() => {
    setLocalValue(value || "");
    setIsButtonVisible(!!value?.trim());
  }, [value]);

  const isDisabled =
    loadingAI || (isSomeoneTyping && typingUser?.user_id !== currentUserId);

  return (
    <div className="flex flex-col gap-2 relative">
      <label htmlFor={id} className="font-medium">
        {label}
      </label>
      <div className="relative">
        <textarea
          id={id}
          rows={4}
          className="w-full border border-gray-300 rounded-md p-2 pr-10"
          placeholder="Enter text or N/A if not applicable to you ..."
          value={localValue}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          disabled={isDisabled}
        />
        {isButtonVisible && (
          <button
            onClick={handleAIIconClick}
            disabled={loadingAI}
            className="absolute right-2 top-2"
          >
            {loadingAI ? (
              <Loader color="#07838F" size="sm" type="dots" />
            ) : (
              <AIIcon className="cursor-pointer" />
            )}
          </button>
        )}
        {isSomeoneTyping && typingUser?.user_id !== currentUserId && (
          <span className="absolute top-[-20px] right-2 text-[#07838F] text-sm">
            {typingUser?.user_name} is typing...
          </span>
        )}
      </div>
      <AIModal
        opened={opened}
        onClose={close}
        localValue={localValue}
        setLocalValue={setLocalValue}
        handleEnhanceAnswer={handleEnhanceAnswer}
      />
    </div>
  );
}
