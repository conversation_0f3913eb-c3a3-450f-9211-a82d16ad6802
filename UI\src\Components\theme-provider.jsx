import { ThemeProvider as NextThemesProvider } from "next-themes";
import { useEffect, useState } from "react";

export default function ThemeProvider({ children }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    
    <NextThemesProvider attribute="class" defaultTheme="light" enableSystem={true}>
      {children}
    </NextThemesProvider>
  );
}
