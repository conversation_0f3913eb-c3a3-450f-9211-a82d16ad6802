import { useState, useEffect } from 'react';
import { MoonIconNavBar, QuestionMarkIconNavBar } from '@/assets/icons';
import { useNotificationStore } from '@/Store/useNotificationStore';
import { NotificationsDropdown } from './NotificationsDropdown';
import { MdOutlineEmail } from 'react-icons/md';
import { UserAvatar } from '@/Pages/Settings/Partials/Profile/Partials/UserImg/UserAvatar';
import { useUserStore } from '@/Store/useUser';
import { Loader2 } from 'lucide-react';
import { driver } from "driver.js";
import { useTranslation } from "react-i18next";
import "driver.js/dist/driver.css";
import "@/assets/css/index.css";
import { Moon, Sun } from "lucide-react";
import { useTheme } from "next-themes";


const UserIcons = ({ user, setOpenMenu }) => {
  const { theme, setTheme } = useTheme();

  const [showNotifications, setShowNotifications] = useState(false);
  const { notifications } = useNotificationStore();
  const {loadingAvatar} = useUserStore();
  const unreadNotifications = notifications?.filter((notification) => !notification.seen).length;
  const [elementsReady, setElementsReady] = useState(false);
  const { t } = useTranslation();

    const getGuideSteps = () => [
    {
      element: ".StartPoint",
      popover: {
        title: t("👋 Welcome to LevelUp ESG®"),
        description: t(
          "Your all-in-one Sustainability and GRC ERP. Let’s take a quick tour to help you get started on tracking, reporting, and improving your ESG performance."
        ),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".Carbon-Intelligence-dashboard",
      popover: {
        title: t("📉 Carbon Intelligence"),
        description: t(
          "Start here to calculate your emissions footprint. Use tools like Scope3Connect, Decarbonisation, and Financed Emissions to manage and reduce your impact."
        ),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".ESGInsights-Reporting-dashboard",
      popover: {
        title: t("📊 ESG Insights & Reporting "),
        description: t(
          "Gain powerful insights with Sustain360 and track your SDG contributions. Use Materiality Assessment and FinanceGuard to prioritize and report ESG issues with confidence."
        ),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".Risk-Compliance-dashboard",
      popover: {
        title: t("🛡️ Risk & Compliance Management "),
        description: t(
          "Mitigate risks and stay audit-ready. From Due Diligence to Anti-greenwashing Rule, stay aligned with global ESG standards."
        ),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".Data-Foundation-dashboard",
      popover: {
        title: t("🧱 Data Foundation"),
        description: t(
          "Lay the groundwork with trusted data. Collect and validate your data with precision using Data Collection and Data Validation."
        ),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".DocVault-dashboard",
      popover: {
        title: t("📁 DocVault "),
        description: t(
          "Securely store and manage all your documents in one place."
        ),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".GreenHub-dashboard",
      popover: {
        title: t("📚 GreenHub"),
        description: t(
          "Your sustainability learning space. Explore insights, articles, and resources to LevelUp."
        ),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".LevelUp-AI-dashboard",
      popover: {
        title: t("✅ That’s it! You're ready to explore."),
        description: t(
          "Need help anytime? Click the green widget at the bottom right for LevelUp AI."
        ),
        side: "left",
        align: "center",
      },
    },
  ];

  const checkElementsExist = () => {
    const steps = getGuideSteps();
    const allElementsExist = steps.every((step) =>
      document.querySelector(step.element)
    );
    return allElementsExist;
  };

  useEffect(() => {
    const observer = new MutationObserver(() => {
      if (checkElementsExist()) {
        setElementsReady(true);
        observer.disconnect();
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    return () => observer.disconnect();
  }, [elementsReady]);

  const startGuide = () => {
    const driverObj = driver({
      showProgress: true,
      popoverClass: "my-custom-popover-class",
      nextBtnText: "Next",
      prevBtnText: "Back",
      doneBtnText: "Done",
      progressText: "Step {{current}} of {{total}}",
      overlayColor: "rgba(0, 0, 0, 0)",
      steps: getGuideSteps(),
      onDestroyStarted: () => {
        localStorage.setItem("hasSeenDashboardGuide", "true");
        driverObj.destroy();
      },
    });
    driverObj.drive();
  };

  useEffect(() => {
    const hasSeenGuide = localStorage.getItem("hasSeenDashboardGuide");
    if (!hasSeenGuide) {
      startGuide();
    }
    return () => {
      const driverObj = driver();
      if (driverObj.isActive()) {
        driverObj.destroy();
      }
    };
  }, [elementsReady]);

  return (
    <div className={`'w-full' duration-500 flex items-center justify-end gap-2 relative sm:w-full `}>
      <span
  onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
  className="cursor-pointer border-[#DCDCDC] dark:border-[#292929] border-2 rounded-full p-2 bg-white dark:bg-[#1E1E1E] text-gray-700 dark:text-white sm:relative duration-500 right-0"
>
  {theme === "dark" ? <Sun size={18} /> : <Moon size={18} />}
</span>

      <span onClick={startGuide} className={`cursor-pointer  border-[#DCDCDC] dark:border-[#292929] border-2 rounded-full p-2 bg-white dark:bg-[#1E1E1E] text-gray-700 dark:text-white sm:relative duration-500 right-0 `}>
        <QuestionMarkIconNavBar />
      </span>
      <span 
      data-notification-trigger
        onClick={() => {
          setShowNotifications(prev => !prev)
        }}
        className="relative cursor-pointer border border-[#DCDCDC] dark:border-[#292929] rounded-full p-2 bg-white dark:bg-[#1E1E1E] text-gray-700 dark:text-white"
      >
        {/* <NotificationIconNavBar /> */}
        <MdOutlineEmail className='text-[#9B9B9B]' size={20}/>
        {unreadNotifications > 0 ? (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-4 h-4 flex  items-center justify-center rounded-full select-none">
            {unreadNotifications > 99 ? "99+" : unreadNotifications }
          </span>
        ) : null}
      </span>

      <NotificationsDropdown isOpen={showNotifications} onClose={() => setShowNotifications(false)} />
      
      <span
        className={` cursor-pointer  border-[#DCDCDC] border-2  dark:border-[#292929] text-gray-700 dark:text-white rounded-full p- size-10 flex items-center justify-center text-2xl text-primary font-bold font-inter z-20 bg-white dark:bg-[#1E1E1E]`}
        onClick={() => setOpenMenu((prev) => !prev)}
      >
        <div className='relative'>
          <UserAvatar userName={user?.userName} size={40}/>
          {loadingAvatar && (
            <div className="absolute inset-0 flex items-center justify-center">
              <Loader2 className="animate-spin" />
            </div>
          )}
        </div>
      </span>
    </div>
  );
};

export default UserIcons;
