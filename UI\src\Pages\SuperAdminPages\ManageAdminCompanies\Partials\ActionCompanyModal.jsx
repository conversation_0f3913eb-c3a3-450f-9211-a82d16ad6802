import Loading from "@/Components/Loading";
import { Button, Modal } from "@mantine/core";

export default function ActionCompanyModal({
  close,
  opened,
  Action_user,
  selectedCompanyId,
  ActionLoading,
  SetSelectedUserId,
  mainTitle,
  ActionValue,
  setEditValue,
  setAccessUserValue,
}) {
  // console.log(selectedCompanyId);

  return (
    <Modal opened={opened} centered withCloseButton={false}>
      <h1 className="text-center text-xl font-bold capitalize">{mainTitle}</h1>
      <div className="flex justify-around">
        <Button
          className="bg-primary hover:bg-primary mt-5"
          onClick={() =>
            !ActionLoading && Action_user(selectedCompanyId, ActionValue)
          }
        >
          {ActionLoading ? <Loading /> : "Submit"}
        </Button>
        <Button
          className="bg-red-700 hover:bg-red-700 mt-5"
          onClick={() => {
            close();
            SetSelectedUserId(null);
            setAccessUserValue({});

          }}
        >
          Close
        </Button>
      </div>
    </Modal>
  );
}
