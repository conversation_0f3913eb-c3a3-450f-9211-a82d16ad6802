import useSideBarRoute from '@/hooks/useSideBarRoute';
import MainLayout from '@/Layout/MainLayout';
import useRoutes from '@/Routes/useRoutes';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { IoMdHome } from "react-icons/io";

export default function GreenHubStart() {
  const { t } = useTranslation();
  const { GreenHubMenu } = useSideBarRoute();
  const { GreenHubAcademy, GreenHubResources, GreenHubPeersCommunity } = useRoutes().GreenHubMap;

  return (
    <MainLayout menus={GreenHubMenu} navbarTitle={'GreenHub'}
        breadcrumbItems={[
            { title: <IoMdHome size={20}/>, href: "/get-started" },
            { title: "GreenHub", href: "#" },
        ]}
    >
      <div className="h-full overflow-y">
        <h1 className="px-8 py-4 text-xl font-bold bg-white rounded-2xl text-blue-950 mb-7">{t('GreenHubTitle')}</h1>
        <div className="flex flex-wrap md:flex-nowrap h-full gap-6">
          <Link
            to={GreenHubPeersCommunity.path}
            className={`w-full block  relative  h-[65vh] rounded-lg bg-[url(@/assets/images/peer-community.png)] bg-no-repeat bg-center bg-cover`}
          >
            <div className="absolute top-0 bottom-0 left-0 right-0 bg-black rounded-lg opacity-40"></div>
            <h2 className="absolute inset-0 px-8 py-10 text-4xl font-bold text-white">{t('PeersCommunity')}</h2>
          </Link>
          <Link
            to={GreenHubAcademy.path}
            className={`w-full block relative h-[65vh]  rounded-lg bg-[url(@/assets/images/academy.jpeg)] bg-no-repeat bg-center bg-cover`}
          >
            <div className="absolute top-0 bottom-0 left-0 right-0 bg-black rounded-lg opacity-40"></div>
            <h2 className="absolute inset-0 px-8 py-10 text-4xl font-bold text-white">{t('Academy')}</h2>
          </Link>
          <Link
            to={GreenHubResources.path}
            className={`w-full block relative h-[65vh] rounded-lg bg-[url(@/assets/images/resources.jpeg)]  bg-no-repeat bg-center bg-cover`}
          >
            <div className="absolute top-0 bottom-0 left-0 right-0 z-0 bg-black rounded-lg opacity-40"></div>
            <h2 className="absolute inset-0 px-8 py-10 text-4xl font-bold text-white ">{t('Resources')}</h2>
          </Link>
        </div>
      </div>
    </MainLayout>
  );
}
