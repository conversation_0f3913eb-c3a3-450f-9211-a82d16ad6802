import React, { useState } from "react";

import { PiExportLight } from "react-icons/pi";
import { FaQuestionCircle, FaPlus } from "react-icons/fa";
import { CiFilter } from "react-icons/ci";

import { Button, Modal } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import SearchBox from "@/Components/SearchBox";
import FinAntiTable from "../../financialServicesStart/Partials/FinancialAntiGreenWashing/Components/FinAntiTable";
import CompilanceTable from "./CompilanceTable/CompilanceTable";
import { FaRegCircleQuestion } from "react-icons/fa6";
const ComilanceAssessTable = () => {
  const [opened, { open, close }] = useDisclosure(false);

  const [addCategory, setAddCategory] = useState(false);
  return (
    <>
      <div className="flex items-center justify-between gap-5">
        <SearchBox
          classNames={{
            input:
              "border-[1px] border-[#BFBFBF] rounded-lg shadow-sm w-[340px] ",
          }}
        />

        <div className="d flex items-center justify-between gap-10">
          <div className="flex justify-start items-center m-3 bg-white p-2 rounded-lg shadow-sm">
            <CiFilter className="mx-2" />
            <FaPlus className="mx-2" />
            <p className="font-semibold mx-2">Filter</p>
          </div>

          <Button
            className="border-[1px] w-[105px] h-[42px] text-sm font-bold rounded-lg text-white border-secondary-300 bg-secondary-300 px-1"
            size="md"
          >
            <span className="me-1">
              <PiExportLight className="text-lg" />
            </span>
            <span>Export</span>
          </Button>

          <div className="title">
            <Button
              onClick={open}
              variant="transparent"
              mx={0}
              className="text-primary underline"
              px={0}
              rightSection={<FaRegCircleQuestion className="text-lg" />}
            >
              Assessment Scoring System Guide?
            </Button>
          </div>
          <Modal
            size={"95%"}
            opened={opened}
            onClose={close}
            withCloseButton={false}
          >
            {/* <RiskCalculation />
          <div className="flex justify-between">
            <ScoringMetrics />
            <ScoreCard />
          </div> */}
          </Modal>
        </div>
      </div>
        <CompilanceTable />
    </>
  );
};

export default ComilanceAssessTable;
