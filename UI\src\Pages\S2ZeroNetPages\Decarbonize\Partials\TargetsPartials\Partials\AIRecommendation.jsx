import circular_white from "@/assets/svg/circular-white.svg";
import Loading from "@/Components/Loading";
import { Button, Image, Modal, MultiSelect, NumberInput } from "@mantine/core";
import { isNotEmpty, useForm } from "@mantine/form";
import axios from "axios";
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { IoIosArrowDown } from "react-icons/io";
// import { driver } from "driver.js";
// import "driver.js/dist/driver.css";
// import "@/assets/css/index.css";
// import { FaQuestionCircle } from "react-icons/fa";

const AIRecommendation = ({ AiOpened, AiClose, assetTypes }) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState();
  const [elementsReady, setElementsReady] = useState(false);
  const activeTab = "AIRecommendation";

  const form = useForm({
    mode: "uncontrolled",
    initialValues: {
      categories: [],
      target_reduction: "",
      total_budget: "",
    },
    validate: {
      categories: isNotEmpty("Categories is required"),
      target_reduction: isNotEmpty("Reduction Ambition is required"),
      total_budget: isNotEmpty("Budget is required"),
    },
  });
  // useEffect(() => {
  //   form.setValues({ReductionAmbition:ReductionAmbition});
  // }, []);
  const AIRecommendationData = async (value) => {
    //console.log(value);
    setLoading(true);
    try {
      const { data } = await axios.post(
        "https://decarbonization-optimization-strategy-staging.azurewebsites.net/decarbonization",
        value
      );
      setLoading(false);
      setData(data);
      //console.log(data);
    } catch (error) {
      setLoading(false);
      //console.log(error);
    }
  };

  // const getGuideSteps = () => [
  //   {
  //     element: ".The-Decarbonise-AI-Categories",
  //     popover: {
  //       title: t(
  //         "Category (e.g., Energy, Waste)"
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".The-Decarbonise-AI-Reduction-Ambition",
  //     popover: {
  //       title: t(
  //         "Reduction Ambition (% between 5 and 100)"
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".The-Decarbonise-AI-Budget",
  //     popover: {
  //       title: t(
  //         "Budget (minimum $5000)"
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".The-Decarbonise-AI-S",
  //     popover: {
  //       title: t(
  //         "Submit your inputs."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".The-Decarbonise-AI-Suggestion",
  //     popover: {
  //       title: t(
  //         "AI will suggest:"
  //       ),
  //       description: t("Total CO₂e Reduction Budget usage Strategies by category"),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  // ];

  // const checkElementsExist = () => {
  //   const steps = getGuideSteps();
  //   const allElementsExist = steps.every((step) =>
  //     document.querySelector(step.element)
  //   );
  //   return allElementsExist;
  // };

  // useEffect(() => {
  //   if (activeTab === "AIRecommendation") {
  //     const observer = new MutationObserver(() => {
  //       if (checkElementsExist()) {
  //         setElementsReady(true);
  //         observer.disconnect();
  //       }
  //     });

  //     observer.observe(document.body, {
  //       childList: true,
  //       subtree: true,
  //     });

  //     return () => observer.disconnect();
  //   }
  // }, [activeTab]);

  // const startGuide = () => {
  //   const driverObj = driver({
  //     showProgress: true,
  //     popoverClass: "my-custom-popover-class",
  //     nextBtnText: "Next",
  //     prevBtnText: "Back",
  //     doneBtnText: "Done",
  //     overlayColor: "rgba(0, 0, 0, 0)",
  //     progressText: "Step {{current}} of {{total}}",
  //     steps: getGuideSteps(),
  //     onDestroyStarted: () => {
  //       localStorage.setItem("hasSeenDecarbonizeTargetsAIRecommendationGuide", "true");
  //       driverObj.destroy();
  //     },
  //   });
  //   driverObj.drive();
  // };

  // useEffect(() => {
  //   const hasSeenGuide = localStorage.getItem("hasSeenDecarbonizeTargetsAIRecommendationGuide");
  //   if (!hasSeenGuide) {
  //     startGuide();
  //   }
  //   return () => {
  //     const driverObj = driver();
  //     if (driverObj.isActive()) {
  //       driverObj.destroy();
  //     }
  //   };
  // }, [activeTab, elementsReady]);

  return (
    <Modal
      opened={AiOpened}
      // onClose={AiClose}
      size={"80%"}
      h={"full"}
      withCloseButton={false}
      classNames={{ body: "p-0 h-[600px]" }}
    >
      {/* <div
        onClick={startGuide}
        style={{
          position: "fixed",
          bottom: 20,
          right: 20,
          cursor: "pointer",
          zIndex: 1000,
        }}
      >
        <div className="h-12 w-12 flex items-center justify-center bg-gradient-to-tr from-[#2C5A8C] via-[#1C889C] to-[#13B1A8] p-1  cursor-pointer rounded-full">
          <FaQuestionCircle
            size={34}
            color="#ffffff"
            className="mx-auto cursor-pointer"
          />
        </div>
      </div> */}
      <div className="grid lg:grid-cols-3 h-full w-full">
        <div className="lg:col-span-1 bg-secondary-300 flex flex-col h-full">
          <div>
            <div className="flex justify-center items-center p-5">
              <Image src={circular_white} className="w-10 me-1 " />
              <h1 className="text-lg font-bold font-inter text-white">
                LevelUp ESG
              </h1>
            </div>
            <p className="text-lg font-semibold font-inter text-white text-center mt-10">
              AI Recommendation
            </p>
            <div className="grid gap-3 my-10 px-5">
              <MultiSelect
                {...form.getInputProps("categories")}
                key={form.key("categories")}
                label="Categories"
                placeholder="Pick value"
                data={[
                  "Refrigerants",
                  "Heat and Steam",
                  "Other Stationary",
                  "Purchased Electricity",
                  "Company Vehicles distance based",
                  "Company Vehicles fuel based",
                  "Natural Gas",
                ]}
                classNames={{ label: "text-white text-lg mb-1" }}
                radius={10}
                rightSection={<IoIosArrowDown />}
                className="The-Decarbonise-AI-Categories"
              />
              <NumberInput
                {...form.getInputProps("target_reduction")}
                key={form.key("target_reduction")}
                label="Reduction Ambition"
                placeholder="Enter 5% <= value <= 100%"
                classNames={{ label: "text-white text-lg mb-1" }}
                radius={10}
                rightSection={"%"}
                min={5}
                max={100}
                className="The-Decarbonise-AI-Reduction-Ambition"
              />
              <NumberInput
                {...form.getInputProps("total_budget")}
                key={form.key("total_budget")}
                label="Budget"
                placeholder="Enter Value Start with 5000 USD"
                classNames={{ label: "text-white text-lg mb-1" }}
                radius={10}
                rightSection={"$"}
                min={5000}
                className="The-Decarbonise-AI-Budget"
              />
              <div>
                <Button
                  size="sm"
                  radius="md"
                  className=" bg-primary hover:bg-secondary The-Decarbonise-AI-S"
                  onClick={form.onSubmit(AIRecommendationData)}
                >
                  {loading ? <Loading /> : t("Submit")}
                </Button>
                <Button
                  size="sm"
                  radius="md"
                  className=" bg-red-600 hover:bg-red-700 ms-2"
                  onClick={() => {
                    AiClose();
                    form.reset();
                    setData(null);
                  }}
                >
                  {t("Close")}
                </Button>
              </div>
            </div>
          </div>

          <div className="mt-auto">
            <p className="text-white text-xs text-center mb-5">
              AI can make errors. Verify important details
            </p>
          </div>
        </div>

        <div className="lg:col-span-2 px-10 py-10">
          {!data ? (
            <div className="flex flex-col h-full">
              <div>
                <h1 className="text-base font-semibold font-inter">
                  AI Recommendation
                  <small className="text-xs font-medium">
                    (Please submit your Values first)
                  </small>
                </h1>
                <hr className="h-[1.5px] bg-[#15192C45] rounded-lg my-3" />

                <div>
                  <h1 className="text-base font-semibold font-inter">
                    Decarbonisation Implementation report
                  </h1>
                  <p className="flex items-center">
                    <span className="p-[3.5px] bg-black rounded-full me-1"></span>
                    Total CO2e Reduction :
                  </p>
                  <p className="flex items-center">
                    <span className="p-[3.5px] bg-black rounded-full me-1"></span>
                    Total Budget Utilized :
                  </p>
                </div>

                <hr className="h-[1.5px] bg-[#15192C45] rounded-lg my-3" />

                <div>
                  <h1 className="text-base font-semibold font-inter">
                    Categories and Strategies:
                  </h1>
                </div>

                {/* <hr className="h-[1.5px] bg-[#15192C45] rounded-lg my-3" /> */}
                {/* <h5 className="font-semibold text-base">Reduction Ambition</h5> */}
              </div>

              {/* <div className="mt-auto">
        <p className="p-2 text-center bg-slate-300 rounded-lg">
         AI can make errors. Verify important details
        </p>
       </div> */}
            </div>
          ) : loading ? (
            <Loading />
          ) : (
            <div className="flex flex-col h-full">
              <div>
                <h1 className="text-base font-semibold font-inter The-Decarbonise-AI-Suggestion">
                  AI Recommendation
                </h1>
                <hr className="h-[1.5px] bg-[#15192C45] rounded-lg my-3" />

                <div>
                  <h1 className="text-base font-semibold font-inter">
                    Decarbonisation Implementation report
                  </h1>
                  <p className="flex items-center">
                    <span className="p-[3.5px] bg-black rounded-full me-1"></span>
                    Total CO2e Reduction : {data?.total_reduction}
                  </p>
                  <p className="flex items-center">
                    <span className="p-[3.5px] bg-black rounded-full me-1"></span>
                    Total Budget Utilized : {data?.total_budget}
                  </p>
                </div>

                <hr className="h-[1.5px] bg-[#15192C45] rounded-lg my-3" />

                <div>
                  <h1 className="text-base font-semibold font-inter">
                    Categories and Strategies:
                  </h1>
                  {data.categories.map((item) => (
                    <div key={item.category_name}>
                      <h5 className="flex items-center">
                        Category: {item.category_name}
                      </h5>
                      {item.strategies.map((items, idx) => (
                        <p key={idx} className="flex items-center ms-2">
                          {idx + 1}. Strategy: {items.strategy_name}
                        </p>
                      ))}
                    </div>
                  ))}
                </div>

                <hr className="h-[1.5px] bg-[#15192C45] rounded-lg my-3" />
                <h5 className="font-semibold text-base">Reduction Ambition</h5>
              </div>

              {/* هذا الجزء سيظل دائمًا في الأسفل */}
              <div className="mt-auto">
                <p className="p-2 text-center bg-slate-300 rounded-lg">
                  AI can make errors. Verify important details
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default AIRecommendation;
