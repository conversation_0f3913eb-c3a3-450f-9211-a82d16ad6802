import { create } from "zustand";

export const useUserStore = create((set, get) => ({
  user: {
    id: "",
    name: "",
    email: "",
    role: "",
    avatar: "",
    created_at: "",
    updated_at: "",
  },
  tempAvatar: "",
  loading: false,
  loadingAvatar:false,
  setLoading: (loading) => set({ loading }),
  setUser: (user) => set({ user }),
  getUser: () => get().user,
  updateAvatar: (avatar) => set({ user: { ...get().user, avatar } }),
  getAvatar: () => {
    return localStorage.getItem("tempAvatar") ||  localStorage.getItem("avatar");
  },
  setTempAvatar: (avatar) => {
    set({ tempAvatar: avatar });
    localStorage.setItem("tempAvatar", avatar);
  },
  getTempAvatar: () => localStorage.getItem("tempAvatar"),
  clearTempAvatar: () => {
    set({ tempAvatar: "" });
    localStorage.removeItem("tempAvatar");
  },
  setLoadingAvatar: (loadingAvatar) => set({ loadingAvatar }),
}));