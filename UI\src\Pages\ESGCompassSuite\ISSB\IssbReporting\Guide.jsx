import {
  Box,
  Title,
  Text,
  List,
  Stack,
  Paper,
  Group,
  ThemeIcon,
} from "@mantine/core";
import { FaCheck } from "react-icons/fa";

export default function EsgReportingPlatformGuide() {
  return (
    <Box mx="auto" maxWidth="1200px" p="md">
      <Title order={1} mb="xl">
        ESG Reporting Platform (Supports TNFD, ISSB, GRI) User Guide
      </Title>

      <Stack spacing="lg">
        {/* Overview Section */}
        <Text size="lg" mb="sm">
          <b>Overview</b>
        </Text>
        <Text c="dimmed">
          This platform enables users to generate structured ESG reports
          according to major reporting standards, including TNFD, ISSB, and GRI.
          It supports data entry, scoring, and qualitative/quantitative
          analysis.
        </Text>

        <Text size="lg" mt="lg" mb="sm">
          <b>Sections Overview</b>
        </Text>
        <List listStyleType="disc" spacing="sm">
          <List.Item>
            <Text fw={700}>Dashboard - ESG Reporting Suite</Text>
            <List listStyleType="circle" spacing="sm" ml="1.5rem">
              <List.Item>
                <b>Purpose:</b> Overview of reports in progress and status.
              </List.Item>
              <List.Item>
                <b>Features:</b>
              </List.Item>
              <List listStyleType="disc" spacing="sm" ml="1.5rem">
                <List.Item>View and manage saved ESG reports.</List.Item>
                <List.Item>Create a new report.</List.Item>
                <List.Item>
                  View status (e.g., &quot;Draft,&quot; &quot;Completed&quot;).
                </List.Item>
              </List>
            </List>
          </List.Item>
        </List>

        {/* Step-by-Step Workflow Section */}
        <Text size="lg" mt="lg" mb="sm">
          <b>Step-by-Step Workflow</b>
        </Text>

        <List listStyleType="decimal" spacing="sm">
          {/* Step 1 */}
          <List.Item>
            <Text fw={700}>Step 1: Start a New Report</Text>
            <List listStyleType="disc" spacing="sm" ml="1.5rem">
              <List.Item>Click &quot;Start New Report.&quot;</List.Item>
              <List.Item>
                Choose a declaration standard (GRI, ISSB, TNFD).
              </List.Item>
              <List.Item>
                The system initializes a report template with sections relevant
                to the selected standard.
              </List.Item>
            </List>
          </List.Item>

          {/* Step 2 */}
          <List.Item>
            <Text fw={700}>Step 2: Fill in Company Specific Information</Text>
            <List listStyleType="disc" spacing="sm" ml="1.5rem">
              <List.Item>Provide details like:</List.Item>
              <List listStyleType="circle" spacing="sm" ml="1.5rem">
                <List.Item>Company Name</List.Item>
                <List.Item>Reporting Year (with gender segmentation)</List.Item>
                <List.Item>
                  Number of employees (with gender segmentation)
                </List.Item>
                <List.Item>Reporting frameworks used</List.Item>
                <List.Item>Business sector/industry</List.Item>
              </List>
              <List.Item>Click Save.</List.Item>
            </List>
          </List.Item>

          {/* Step 3 */}
          <List.Item>
            <Text fw={700}>Step 3: Answer Reporting Questions</Text>
            <List listStyleType="disc" spacing="sm" ml="1.5rem">
              <List.Item>
                Each standard (GRI, ISSB, TNFD) has pre-loaded reporting themes
                and topics.
              </List.Item>
              <List.Item>Expand each theme to reveal sub-topics.</List.Item>
              <List.Item>For each sub-topic:</List.Item>
              <List listStyleType="circle" spacing="sm" ml="1.5rem">
                <List.Item>
                  Rate applicability or implementation level (drop-downs)
                </List.Item>
                <List.Item>Upload evidence, if required</List.Item>
                <List.Item>
                  Use the &quot;Mark Complete&quot; button to finalize a
                  section.
                </List.Item>
              </List>
            </List>
          </List.Item>

          {/* Step 4 */}
          <List.Item>
            <Text fw={700}>Step 4: Universal Standards Implementation</Text>
            <List listStyleType="disc" spacing="sm" ml="1.5rem">
              <List.Item>For platforms using ISSB and TNFD:</List.Item>
              <List listStyleType="circle" spacing="sm" ml="1.5rem">
                <List.Item>
                  Fill out mandatory governance, materiality, and risk
                  management disclosures.
                </List.Item>
                <List.Item>Select indicators like:</List.Item>
                <List listStyleType="square" spacing="sm" ml="1.5rem">
                  <List.Item>Climate-related targets</List.Item>
                  <List.Item>Risk identification and oversight</List.Item>
                  <List.Item>Double materiality assessment</List.Item>
                  <List.Item>Scenario planning disclosures</List.Item>
                  <List.Item>
                    Attach internal documentation or reference strategy
                    documents.
                  </List.Item>
                </List>
              </List>
            </List>
          </List.Item>

          {/* Step 5 */}
          <List.Item>
            <Text fw={700}>Step 5: Scoring Metrics</Text>
            <List listStyleType="disc" spacing="sm" ml="1.5rem">
              <List.Item>
                Assign qualitative and quantitative metrics to specific
                sub-sections.
              </List.Item>
              <List.Item>
                E.g., % renewable energy, water usage reduction, supply chain
                emissions.
              </List.Item>
              <List.Item>Use drop-downs to set:</List.Item>
              <List listStyleType="circle" spacing="sm" ml="1.5rem">
                <List.Item>Baseline value</List.Item>
                <List.Item>Target value</List.Item>
                <List.Item>Achieved value</List.Item>
              </List>
              <List.Item>
                Values update performance graphs automatically.
              </List.Item>
            </List>
          </List.Item>

          {/* Step 6 */}
          <List.Item>
            <Text fw={700}>Step 6: Qualitative Summary</Text>
            <List listStyleType="disc" spacing="sm" ml="1.5rem">
              <List.Item>Enter narrative explanations or policies.</List.Item>
              <List.Item>
                Option to upload policy documents or screenshots.
              </List.Item>
            </List>
          </List.Item>

          {/* Step 7 */}
          <List.Item>
            <Text fw={700}>Step 7: Final Submission</Text>
            <List listStyleType="disc" spacing="sm" ml="1.5rem">
              <List.Item>
                After completing all topics and evidence uploads:
              </List.Item>
              <List.Item>Review your report.</List.Item>
              <List.Item>Export report as PDF or Docx.</List.Item>
              <List.Item>
                Submit for third-party verification (if enabled).
              </List.Item>
            </List>
          </List.Item>
        </List>

        {/* Optional Tip Banner */}
        <Paper shadow="sm" p="md" radius="md" mt="xl" withBorder>
          <Group position="apart">
            <ThemeIcon variant="light" radius="xl" size="2rem">
              <FaCheck size="1.2rem" />
            </ThemeIcon>
            <Text fw={500}>
              Use the &quot;Mark Complete&quot; button after finalizing each
              section to track progress!
            </Text>
          </Group>
        </Paper>
      </Stack>
    </Box>
  );
}
