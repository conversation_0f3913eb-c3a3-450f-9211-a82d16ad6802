import React from "react";
import { BarChart } from "@mantine/charts";
import { Select } from "@mantine/core";
import axios from "axios";

const TopEmissionSources = () => {
  const data = [
    { emission: "Purchased Electricity", precentage: 59000 },
    { emission: "Employee Commuting", precentage: 49000 },
    { emission: "Business Travel", precentage: 39000 },
    { emission: "Heating and Cooling (HVAC)", precentage: 30000 },
    { emission: "IT Equipment and Data Centers", precentage: 22000 },
    { emission: "Purchased Goods and Services", precentage: 18000 },
    { emission: "Waste Generated in Operations", precentage: 12000 },
    { emission: "Water Usage and Treatment", precentage: 8000 },
    { emission: "Paper Consumption", precentage: 6000 },
  ];
  return (
    <>
      <div className="flex items-center justify-between p-2 mb-4">
        <h3 className="text-2xl font-semibold">Top Emission Sources</h3>
        <Select
          classNames={{
            label: "py-2",
            root: "rounded-lg focus:border-primary",
          }}
          // label="Categories"
          placeholder="Pick value"
          defaultValue="Yearly"
          data={["Yearly", "Year", "Years"]}
        />
      </div>

      <BarChart
        className="bg-white p-8 rounded-lg shadow-lg"
        classNames={{bar:"rounded-none"}}
        h={600}
        data={data}
        dataKey="emission"
        orientation="vertical"
        // withXAxis={false}
        yAxisProps={{ width: 80 }}
        barProps={{ radius: 10 }}
        series={[{ name: "precentage", color: "blue" }]}
      />
    </>
  );
};

export default TopEmissionSources;
