import { useState, useMemo, useEffect } from "react";
import {
  Popover,
  Table,
  Group,
  Text,
  Select,
  MultiSelect,
} from "@mantine/core";
import { format, parseISO } from "date-fns";
import { toast } from "react-toastify";
import Cookies from "js-cookie";
import { DatePicker } from "@mantine/dates";
import { IoCalendar } from "react-icons/io5";
import { useDisclosure } from "@mantine/hooks";

const Details = ({
  topicId,
  topicReviewers,
  detailsData,
  double_materiality_uid,
  topicDueDate,
  xbrl_tags,
}) => {
  const [opened, { open, close }] = useDisclosure(false);
  const [companyUsers, setCompanyUsers] = useState([]);
  const [date, setDate] = useState(
    topicDueDate ? parseISO(topicDueDate) : null
  );
  const [selectedUID, setSelectedUID] = useState(
    detailsData?.["Double Materiality Assessment"]?.scope?.double?.topics[0]
      ?.uid || ""
  );
  const [selectedOwners, setSelectedOwners] = useState([]);
  const [selectedReviewers, setSelectedReviewers] = useState([]);

  const baseUrl = "https://issb-report-api-staging.azurewebsites.net";

  // Prepare dropdown data for UIDs
  const uidOptions = useMemo(() => {
    return (
      detailsData?.["Double Materiality Assessment"]?.scope?.double?.topics.map(
        (topic) => ({
          value: topic.uid,
          label: topic.uid,
        })
      ) || []
    );
  }, [detailsData]);

  // Memoize selectedTopic to stabilize useEffect dependencies
  const selectedTopic = useMemo(() => {
    console.log(detailsData);
    return (
      detailsData?.[
        "Double Materiality Assessment"
      ]?.scope?.double?.topics.find((topic) => topic.uid === selectedUID) || {}
    );
  }, [detailsData, selectedUID]);

  // Map Priority to Materiality Rating
  const materialityRating = useMemo(() => {
    return detailsData?.["Priority levels"]?.[selectedTopic.Priority] || "N/A";
  }, [detailsData, selectedTopic.Priority]);

  // Fetch company users
  const fetchCompanyUsers = async () => {
    const token = Cookies.get("level_user_token");
    if (!token) {
      toast.error("Authorization token is missing");
      return;
    }

    try {
      const response = await fetch(
        "https://portal-auth-main.azurewebsites.net/get-all-company-users",
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error(
          `Failed to fetch company users: ${response.statusText}`
        );
      }

      const users = await response.json();
      setCompanyUsers(users);
    } catch (error) {
      console.error("Error fetching company users:", error);
      toast.error("Failed to fetch company users");
    }
  };

  // Add reviewer to topic
  const addReviewer = async (userId, userName) => {
    const token = Cookies.get("level_user_token");
    if (!token) {
      toast.error("Authorization token is missing");
      return false;
    }

    try {
      const response = await fetch(
        `${baseUrl}/api/v1/topics/${topicId}/reviewers`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({ user_id: parseInt(userId) }),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to add reviewer: ${response.statusText}`);
      }

      setSelectedReviewers((prev) => [
        ...prev,
        { value: userId.toString(), label: userName },
      ]);
      return true;
    } catch (error) {
      console.error("Error adding reviewer:", error);
      toast.error(`Failed to add reviewer: ${error.message}`);
      return false;
    }
  };

  // Remove reviewer from topic
  const removeReviewer = async (userId, userName) => {
    const token = Cookies.get("level_user_token");
    if (!token) {
      toast.error("Authorization token is missing");
      return false;
    }

    try {
      const response = await fetch(
        `${baseUrl}/api/v1/topics/${topicId}/reviewers/${userId}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to remove reviewer: ${response.statusText}`);
      }

      setSelectedReviewers((prev) =>
        prev.filter((reviewer) => reviewer.value !== userId.toString())
      );
      return true;
    } catch (error) {
      console.error("Error removing reviewer:", error);
      toast.error(`Failed to remove reviewer: ${error.message}`);
      return false;
    }
  };

  // Update due date via API
  const updateDueDate = async (newDate) => {
    const token = Cookies.get("level_user_token");
    if (!token) {
      toast.error("Authorization token is missing");
      return false;
    }

    try {
      const formattedDate = format(newDate, "yyyy-MM-dd");
      const response = await fetch(`${baseUrl}/api/v1/topics/${topicId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ due_date: formattedDate }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update due date: ${response.statusText}`);
      }

      return true;
    } catch (error) {
      console.error("Error updating due date:", error);
      toast.error(`Failed to update due date: ${error.message}`);
      return false;
    }
  };

  // Update double_materiality_uid via API
  const updateDoubleMaterialityUID = async (newUID) => {
    const token = Cookies.get("level_user_token");
    if (!token) {
      toast.error("Authorization token is missing");
      return false;
    }

    try {
      const response = await fetch(`${baseUrl}/api/v1/topics/${topicId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ double_materiality_uid: newUID }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update UID: ${response.statusText}`);
      }

      return true;
    } catch (error) {
      console.error("Error updating UID:", error);
      toast.error(`Failed to update UID: ${error.message}`);
      return false;
    }
  };

  // Update owners via API
  const updateOwners = async (updatedTopics) => {
    const token = Cookies.get("level_user_token");
    if (!token) {
      toast.error("Authorization token is missing");
      return false;
    }

    try {
      const response = await fetch(
        "https://levelupportals1api-staging.azurewebsites.net/post_additional_scope",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
            assessmentName: "Double Materiality",
            toolName: "Double Materiality Assessment",
            scopeName: "double",
          },
          body: JSON.stringify({ topics: updatedTopics }),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to update owners: ${response.statusText}`);
      }

      return true;
    } catch (error) {
      console.error("Error updating owners:", error);
      toast.error(`Failed to update owners: ${error.message}`);
      return false;
    }
  };

  // Handle Owner MultiSelect changes
const handleOwnerChange = async (newValues) => {
  const allTopics = detailsData?.["Double Materiality Assessment"]?.scope?.double?.topics || [];
  const updatedTopics = allTopics.map((topic) => {
    if (topic.uid === selectedUID) {
      const currentOwners = topic.owner || [];
      const newOwners = newValues.map((value) => {
        // البحث عن المستخدم في الـ owners الحاليين للحفاظ على الـ notified
        const existingOwner = currentOwners.find((owner) => owner.user_id.toString() === value);
        if (existingOwner) {
          return existingOwner; // الحفاظ على الأوبجيكت الحالي بما في ذلك notified
        } else {
          // إضافة الأوبجيكت الكامل من companyUsers للمستخدم الجديد
          const user = companyUsers.find((u) => u.user_id.toString() === value);
          return user ? { ...user } : null; // نسخ الأوبجيكت الكامل بدون تعديل
        }
      }).filter(owner => owner !== null); // إزالة أي قيم null في حالة عدم العثور على المستخدم
      return { ...topic, owner: newOwners };
    }
    return topic;
  });

  // إرسال التحديث مرة واحدة
  const success = await updateOwners(updatedTopics);
  if (success) {
    // تحديث الـ selectedOwners لعرضها في الـ MultiSelect
    const newSelectedOwners = newValues.map((value) => {
      const user = companyUsers.find((u) => u.user_id.toString() === value);
      return { value, label: user ? user.user_name : value };
    });
    setSelectedOwners(newSelectedOwners);
  } else {
    toast.error("Failed to update owners");
  }
};

  // Handle UID change
  const handleUIDChange = async (newUID) => {
    if (!newUID) return; // Ignore null selections
    const previousUID = selectedUID; // Store current UID for rollback
    setSelectedUID(newUID); // Update UI immediately
    try {
      const success = await updateDoubleMaterialityUID(newUID);
      if (!success) {
        setSelectedUID(previousUID); // Revert on failure
        toast.error("Failed to update UID. Reverted to previous UID.");
      }
    } catch (error) {
      setSelectedUID(previousUID); // Revert on error
      toast.error("Error updating UID. Reverted to previous UID.");
    }
  };

  // Initialize selected owners, reviewers, and due date
  useEffect(() => {
    // Initialize owners from selectedTopic.owner
    if (selectedTopic.owner && selectedTopic.owner.length > 0) {
      const initialOwners = selectedTopic.owner.map((owner) => ({
        value: owner.user_id.toString(),
        label: owner.user_name,
      }));
      setSelectedOwners(initialOwners);
    } else {
      setSelectedOwners([]);
    }

    // Initialize reviewers from selectedTopic.reviewers or topicReviewers
    const reviewers = selectedTopic.reviewers || topicReviewers || [];
    if (reviewers.length > 0) {
      const initialReviewers = reviewers.map((reviewer) => ({
        value: reviewer.user_id.toString(),
        label: reviewer.user_name,
      }));
      setSelectedReviewers(initialReviewers);
    } else {
      setSelectedReviewers([]);
    }

    // Initialize due date from topicDueDate
    setDate(topicDueDate ? parseISO(topicDueDate) : null);
  }, [detailsData, selectedUID, topicReviewers, topicDueDate, selectedTopic]);

  // Fetch company users on component mount
  useEffect(() => {
    fetchCompanyUsers();
  }, []);

  // Prepare options for MultiSelect
const userOptions = useMemo(() => {
  return companyUsers.map((user) => ({
    value: user.user_id.toString(),
    label: user.user_name,
  }));
}, [companyUsers]);

  // Handle Reviewer MultiSelect changes
  const handleReviewerChange = async (newValues) => {
    const currentReviewerIds = selectedReviewers.map(
      (reviewer) => reviewer.value
    );
    const addedReviewers = newValues.filter(
      (value) => !currentReviewerIds.includes(value)
    );
    const removedReviewers = currentReviewerIds.filter(
      (value) => !newValues.includes(value)
    );

    let allSuccess = true;

    for (const userId of addedReviewers) {
      const user = companyUsers.find((u) => u.user_id.toString() === userId);
      if (user) {
        const success = await addReviewer(userId, user.name);
        if (!success) allSuccess = false;
      }
    }

    for (const userId of removedReviewers) {
      const user = companyUsers.find((u) => u.user_id.toString() === userId);
      if (user) {
        const success = await removeReviewer(userId, user.name);
        if (!success) allSuccess = false;
      }
    }

    if (allSuccess) {
      const newReviewers = newValues.map((value) => {
        const user = companyUsers.find((u) => u.user_id.toString() === value);
        return {
          value,
          label: user ? user.name : value,
        };
      });
      setSelectedReviewers(newReviewers);
    }
  };

  // Handle DatePicker change
  const handleDateChange = async (selectedDate) => {
    if (!selectedDate) return; // Ignore null selections
    const previousDate = date; // Store current date for rollback
    setDate(selectedDate); // Update UI immediately
    try {
      const success = await updateDueDate(selectedDate);
      if (!success) {
        setDate(previousDate); // Revert on failure
        toast.error("Failed to update due date. Reverted to previous date.");
      }
    } catch (error) {
      setDate(previousDate); // Revert on error
      toast.error("Error updating due date. Reverted to previous date.");
    } finally {
      close(); // Close Popover after handling
    }
  };

  return (
    <div>
      <h2 className="font-medium mb-2">Details</h2>
      <div className="bg-[#F8F8F8] w-full border rounded p-4 overflow-x-auto">
        <Table
          verticalSpacing="sm"
          horizontalSpacing="md"
          withTableBorder
          withColumnBorders
          className="min-w-[1300px] w-full">
          <Table.Tbody>
            <Table.Tr>
              <Table.Td className="font-semibold text-left">UID</Table.Td>
              <Table.Td className="text-left">
                <Select
                  placeholder="Choose a UID"
                  data={uidOptions}
                  value={double_materiality_uid || selectedUID}
                  onChange={handleUIDChange}
                  searchable
                  styles={{
                    input: {
                      border: "none",
                      backgroundColor: "transparent",
                      padding: "0",
                    },
                    dropdown: {
                      zIndex: 1000,
                    },
                  }}
                />
              </Table.Td>
              <Table.Td className="font-semibold text-left">
                XBRL Tagging
              </Table.Td>
              <Table.Td style={{ maxWidth: "300px" }}>
                <div className="flex flex-wrap gap-2">
                  {xbrl_tags.map((tag) => (
                    <span
                      key={tag}
                      className="bg-gray-200 text-black text-[10px] px-4 py-1 rounded-full mr-2"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </Table.Td>
            </Table.Tr>
            <Table.Tr>
              <Table.Td className="font-semibold text-left">ESG Topic</Table.Td>
              <Table.Td className="text-left">
                {selectedTopic.name || "N/A"}
              </Table.Td>
              <Table.Td className="font-semibold text-left">Owner</Table.Td>
              <Table.Td className="text-left">
                <MultiSelect
                  data={userOptions}
                  value={selectedOwners.map((owner) => owner.value)}
                  onChange={handleOwnerChange}
                  placeholder="Select owners"
                  searchable
                  styles={{
                    input: {
                      border: "none",
                      backgroundColor: "transparent",
                      padding: "0",
                      display: "flex",
                      flexWrap: "wrap",
                    },
                    dropdown: {
                      zIndex: 1000,
                    },
                  }}
                />
              </Table.Td>
            </Table.Tr>
            <Table.Tr>
              <Table.Td className="font-semibold text-left">Category</Table.Td>
              <Table.Td className="text-left">
                {selectedTopic.Category || "N/A"}
              </Table.Td>
              <Table.Td className="font-semibold text-left">Reviewer</Table.Td>
              <Table.Td className="text-left">
                <MultiSelect
                  data={userOptions}
                  value={selectedReviewers.map((reviewer) => reviewer.value)}
                  onChange={handleReviewerChange}
                  placeholder="Select reviewers"
                  searchable
                  styles={{
                    input: {
                      border: "none",
                      backgroundColor: "transparent",
                      padding: "0",
                      display: "flex",
                      flexWrap: "wrap",
                    },
                    dropdown: {
                      zIndex: 1000,
                    },
                  }}
                />
              </Table.Td>
            </Table.Tr>
            <Table.Tr>
              <Table.Td className="font-semibold text-left">
                Materiality Rating
              </Table.Td>
              <Table.Td className="text-left">
                <span className="bg-[#AB020238] text-[#AB0202] px-4 py-1 rounded-full">
                  {materialityRating}
                </span>
              </Table.Td>
              <Table.Td className="font-semibold text-left">Due Date</Table.Td>
              <Table.Td className="text-left">
                <Group gap="xs" align="center">
                  <Popover
                    opened={opened}
                    onClose={close}
                    position="bottom-start"
                    shadow="md"
                    withArrow
                  >
                    <Popover.Target>
                      <button onClick={open}>
                        <IoCalendar className="text-xl cursor-pointer text-[#07838F]" />
                      </button>
                    </Popover.Target>
                    <Popover.Dropdown>
                      <DatePicker value={date} onChange={handleDateChange} minDate={new Date()} />
                    </Popover.Dropdown>
                  </Popover>
                  <Text size="sm">
                    {date ? format(date, "dd/MM/yyyy") : "Select a date"}
                  </Text>
                </Group>
              </Table.Td>
            </Table.Tr>
          </Table.Tbody>
        </Table>
      </div>
    </div>
  );
};

export default Details;