import { useEffect, useState } from "react";
import IssbCard from "@/Issb/IssbCard";
import ReportCard from "@/Issb/ReportCard";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import MainLayout from "@/Layout/MainLayout";
import { FaClipboardList } from "react-icons/fa6";
import axios from "axios";
import Cookies from "js-cookie";
import { IoMdHome } from "react-icons/io";
import { Select, Input } from "@mantine/core";
import { Filter } from "@/assets/icons";
import GuideModalButton from "@/Components/Guide/GuideModalButton";
import Guide from "./Guide";

const IssbReportingSuite = () => {
    const { issbMenu } = useSideBarRoute();
    const ISSBReports = [
        "Comprehensive data quality assessment",
        "AI-powered data collection, validation & narrative engine",
        "Exportable outputs for regulatory and stakeholder submissions",
    ];
    const [reports, setReports] = useState([]);
    const [displayedCount, setDisplayedCount] = useState(10);
    const [loading, setLoading] = useState(true);
    const [searchQuery, setSearchQuery] = useState("");
    const [statusFilter, setStatusFilter] = useState(null);
    const [frameworkFilter, setFrameworkFilter] = useState(null);
    const [yearFilter, setYearFilter] = useState(null);

    useEffect(() => {
        const fetchReports = async () => {
            const token = Cookies.get("level_user_token");
            try {
                const response = await axios.get(
                    "https://issb-report-api-staging.azurewebsites.net/api/v1/reports",
                    {
                        headers: { Authorization: `Bearer ${token}` },
                        params: { limit: 1000 },
                    }
                );
                setReports(response.data.reports);
            } catch (error) {
                console.error("Error fetching reports:", error);
            } finally {
                setLoading(false);
            }
        };
        fetchReports();
    }, []);

    const filterReports = () => {
        return reports.filter((report) => {
            const matchesSearch = searchQuery
                ? report.framework
                      ?.toLowerCase()
                      .includes(searchQuery.toLowerCase()) ||
                  report.id?.toString().includes(searchQuery)
                : true;

            const matchesStatus = statusFilter
                ? report.status === statusFilter
                : true;

            const matchesFramework = frameworkFilter
                ? report.framework === frameworkFilter
                : true;

            const reportYear = report.created_at
                ? new Date(report.created_at).getFullYear().toString()
                : null;
            const matchesYear = yearFilter ? reportYear === yearFilter : true;

            return (
                matchesSearch &&
                matchesStatus &&
                matchesFramework &&
                matchesYear
            );
        });
    };

    const loadMore = () => {
        setDisplayedCount((prev) => prev + 10);
    };

    const availableYears = Array.from(
        new Set(
            reports
                .filter((report) => report.created_at)
                .map((report) =>
                    new Date(report.created_at).getFullYear().toString()
                )
        )
    ).sort((a, b) => b - a);

    const filteredReports = filterReports();
    const displayedReports = filteredReports.slice(0, displayedCount);
    const hasMoreReports = displayedCount < filteredReports.length;

    return (
        <MainLayout
            menus={issbMenu}
            navbarTitle={"ESG Reporting Suite"}
            breadcrumbItems={[
                { title: <IoMdHome size={20} />, href: "/get-started" },
                { title: "ESG Reporting Suite", href: "#" },
            ]}
        >
            <div className="flex flex-col gap-5 px-2 py-10 w-full">
                <div className="w-full justify-between flex items-center py-5 px-3">
                    <GuideModalButton buttonText="ESG Reporting Guide">
                        <Guide />
                    </GuideModalButton>
                </div>
                <IssbCard
                    icon={
                        <div className="p-4">
                            <FaClipboardList className="text-[#29919B] text-3xl" />
                        </div>
                    }
                    title="ESG Reporting"
                    description="Create standardised ESG reports with AI assistance and guided workflows"
                    items={ISSBReports}
                    btnString="Start New Report"
                    link="/Insights-reporing/issb-reporting-suite/esg-reporting"
                />
                <div className="rounded-3xl w-full border-2 bg-white p-6 shadow-sm transition-all">
                    <div>
                        <h2 className="text-[32px] font-bold text-[#494949]">
                            Your Reports
                        </h2>
                        <p className="text-[#494949] font-medium text-lg">
                            View, manage, and create ESG reports
                        </p>
                    </div>
                    <div className="flex max-md:flex-col justify-between w-full gap-6 items-center my-8">
                        <Input
                            type="text"
                            placeholder="Search"
                            className="rounded w-full md:w-1/3"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                        />
                        <div className="flex max-md:flex-col items-center justify-between gap-2 w-full md:w-[60%]">
                            <div className="text-[#494949] inline-flex justify-between items-center gap-2">
                                <span>
                                    <Filter />
                                </span>
                                Filters:
                            </div>
                            <div className="flex items-center justify-between max-md:flex-col gap-2 w-full">
                                <Select
                                    placeholder="Status"
                                    data={[
                                        "Completed",
                                        "In Progress",
                                        "Archived",
                                    ]}
                                    clearable
                                    searchable
                                    checkIconPosition="right"
                                    value={statusFilter}
                                    onChange={setStatusFilter}
                                    className="w-full"
                                />
                                <Select
                                    placeholder="Framework"
                                    data={["ISSB", "GRI", "TNFD"]}
                                    clearable
                                    searchable
                                    checkIconPosition="right"
                                    value={frameworkFilter}
                                    onChange={setFrameworkFilter}
                                    className="w-full"
                                />
                                <Select
                                    placeholder="Year"
                                    data={availableYears}
                                    clearable
                                    searchable
                                    checkIconPosition="right"
                                    value={yearFilter}
                                    onChange={(value) => {
                                        setYearFilter(value);
                                    }}
                                    className="w-full"
                                />
                            </div>
                        </div>
                    </div>
                    {loading ? (
                        <div className="flex items-center justify-center">
                            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
                        </div>
                    ) : (
                        <div>
                            {displayedReports.length > 0 ? (
                                displayedReports.map((report) => (
                                    <ReportCard
                                        key={report.id}
                                        previousReportid={report.id}
                                        framework={report.framework}
                                        status={report.status}
                                        createdAt={report.created_at}
                                        updatedAt={report.updated_at}
                                    />
                                ))
                            ) : (
                                <p className="text-center text-[#494949]">
                                    No reports match your filters.
                                </p>
                            )}
                            {hasMoreReports && (
                                <div className="flex justify-center mt-4">
                                    <button
                                        onClick={loadMore}
                                        className="bg-[#07838F] text-white px-6 py-2 rounded-full hover:bg-[#07838fce]"
                                    >
                                        Load More
                                    </button>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </div>
        </MainLayout>
    );
};

export default IssbReportingSuite;
