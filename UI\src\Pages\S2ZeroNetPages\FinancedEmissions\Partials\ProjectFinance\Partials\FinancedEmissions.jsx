import Api_PCAF from '@/Api/apiS2_PCAF';
import { useProjectFinanceStore } from '@/Store/useProjectFinanceStore';
import { Spinner } from '@react-pdf-viewer/core';
import axios from 'axios';
import Cookies from 'js-cookie';
import React from 'react';
import { BsStars } from "react-icons/bs";

const FinancedEmissions = () => {
    const {
        financing,
        project_cost,
        project_annual_emissions,
        project_name,
        project_type,
        project_phase,
        project_size,
        data_quality_score,
        lifetime,
        loading,

        setFinancing,
        setProjectCost,
        setProjectAnnualEmissions,
        setProjectName,
        setProjectType,
        setProjectPhase,
        setProjectSize,
        setDataQualityScore,
        setLifetime,
        setLoading,
        setResults,
        estimate_project_annual_emissions_loading,
        setEstimateProjectAnnualEmissionsLoading,
    } = useProjectFinanceStore();
    const handleCalculate = async () => {
        setLoading(true);
        Api_PCAF.post("/project-finance", {
            "financing": financing,
            "project_cost": project_cost,
            "project_annual_emissions": project_annual_emissions,
            "project_name": project_name,
            "project_type": project_type,
            "project_phase": project_phase,
            "project_size": project_size,
            "data_quality_score": data_quality_score,
            "lifetime": lifetime
        }).then((res) => {
            setLoading(false);
            const financedEmissions = res.data;
            setResults(financedEmissions);
        }).catch((error) => {
            console.log(error);
            setLoading(false);
        });
    }
    const Estimate_ProjectAnnualEmission = async ()=>{
        const questions = getAnsweredQuestions();
        console.log(questions);
        setEstimateProjectAnnualEmissionsLoading(true);
        axios.post("https://gen-ai0-staging.azurewebsites.net/process_request",
            {
                "processor": "pcaf",
                "resources":{
                    "qa_context": questions,
                    "estimation_needed": "Project's Annual Emissions"
                }
            },
            {
                headers:{ "Authorization": `Bearer ${Cookies.get("level_user_token")}`}
            }
        ).then((res)=>{
            setEstimateProjectAnnualEmissionsLoading(false);
            setProjectAnnualEmissions(res.data.ai_response.value);
            console.log(res.data)
        }).catch((err)=>{
            setEstimateProjectAnnualEmissionsLoading(false);
            console.log(err);
        })
    }
    const getAnsweredQuestions = ()=>{
        let questions = []
        if (financing !== "") {
            questions.push({
                question: "Financing Provided",
                user_answer: financing
            });
        }
        if (project_cost !== "") {
            questions.push({
                question: "Total Project Cost",
                user_answer: project_cost
            });
        }
        if (project_name !== "") {
            questions.push({
                question: "Project Name",
                user_answer: project_name
            });
        }
        if (project_type !== "") {
            questions.push({
                question: "Project Type",
                user_answer: project_type
            });
        }
        if (project_phase !== "") {
            questions.push({
                question: "Project Phase",
                user_answer: project_phase
            });
        }
        if (project_size !== "") {
            questions.push({
                question: "Project Size",
                user_answer: project_size
            });
        }
        if (data_quality_score !== "") {
            questions.push({
                question: "Data Quality Score",
                user_answer: data_quality_score
            });
        }
        if (lifetime !== "") {
            questions.push({
                question: "Project Lifetime",
                user_answer: lifetime
        })}
        return questions;
    }
  return (
    <div>
        <div className='flex flex-col gap-4 rounded-xl bg-white p-4 border-[#E8E7EA] border-2'>
            <h1 className='flex justify-center text-center bg-[#E6F3F4] text-[#8F8F8F] p-2 rounded-xl'>
            Financed Emissions = (Financing Provided / Total Project Cost) * Project&apos;s Total Emissions
            </h1>
            <table className="w-full">
            <thead className="bg-[#F5F4F5]">
                <tr className='font-bold'>
                <th className="text-start p-3 ">Parameter</th>
                <th className="text-start p-3 ">Value</th>
                <th className="text-start p-3 ">AI Assistant</th>
                </tr>
            </thead>
            <tbody>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Financing Provided</td>
                    <td className="p-3 border border-gray-300">
                        <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="e.g., 500,000" onChange={(e) => setFinancing(e.target.value)} value={financing}/>
                    </td>
                    <td className="p-3 border border-gray-300">Required Input</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Total Project Cost</td>
                    <td className="p-3 border border-gray-300">
                        <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="e.g., 500,000" onChange={(e) => setProjectCost(e.target.value)} value={project_cost}/>
                    </td>
                    <td className="p-3 border border-gray-300">Required Input</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Project Name</td>
                    <td className="p-3 border border-gray-300">
                        <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="eg, Coastal Wind Farm" onChange={(e) => setProjectName(e.target.value)} value={project_name}/>
                    </td>
                    <td className="p-3 border border-gray-300">Required</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Project Type</td>
                    <td className="p-3 border border-gray-300">
                        <select type="text" className="border border-gray-400 rounded-lg p-2 w-full" onChange={(e) => setProjectType(e.target.value)} value={project_type}>
                            <option value="Renewable Energy" className='text-gray-400'>Renewable Energy</option>
                            <option value="Technology">Technology</option>
                            <option value="Finance">Finance</option>
                        </select>
                    </td>
                    <td className="p-3 border border-gray-300">Required</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Project Phase</td>
                    <td className="p-3 border border-gray-300">
                        <select type="text" className="border border-gray-400 rounded-lg p-2 w-full" onChange={(e) => setProjectPhase(e.target.value)} value={project_phase}>
                            <option value="Greenfield (New Project)" className='text-gray-400'>Greenfield (New Project)</option>
                            <option value="Technology">Technology</option>
                            <option value="Finance">Finance</option>
                        </select>
                    </td>
                    <td className="p-3 border border-gray-300">Required</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Project Capacity/Size</td>
                    <td className="p-3 border border-gray-300">
                        <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="g. 100 MW" onChange={(e) => setProjectSize(e.target.value)} value={project_size}/>
                    </td>
                    <td className="p-3 border border-gray-300">If available</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Lifetime of Project</td>
                    <td className="p-3 border border-gray-300">
                        <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="g. 100 MW" onChange={(e) => setLifetime(e.target.value)} value={lifetime}/>
                    </td>
                    <td className="p-3 border border-gray-300">If available</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Project&apos;s Annual Emissions (CO)</td>
                    <td className="p-3 border border-gray-300">
                        <input disabled={estimate_project_annual_emissions_loading} type="text" className={"border border-gray-400 rounded-lg p-2 w-full" + (estimate_project_annual_emissions_loading ? " cursor-not-allowed text-gray-500" : "")} placeholder="9.5,000" onChange={(e) => setProjectAnnualEmissions(e.target.value)} value={project_annual_emissions}/>
                    </td>
                    <td className="p-3 border border-gray-300">
                        <button disabled={estimate_project_annual_emissions_loading} className='flex flex-row items-center justify-center gap-2 rounded-md bg-[#1C889C] text-[#fff] p-2 w-full' onClick={Estimate_ProjectAnnualEmission}>
                            {estimate_project_annual_emissions_loading ? <Spinner size='24px' /> : <BsStars /> } Estimate
                        </button>
                    </td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Data Quality Score</td>
                    <td className="p-3 border border-gray-300">
                        <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="3-Based on project type average Number" onChange={(e) => setDataQualityScore(e.target.value)} value={data_quality_score}/>
                    </td>
                    <td className="p-3 border border-gray-300">Auto-updates</td>
                </tr>
            </tbody>
            </table>

            <button disabled={loading} className='flex w-full justify-center text-center text-base font-semibold bg-[#07838F] text-[#ffffff] p-2 rounded-lg' onClick={handleCalculate}>
                {
                    loading ? <Spinner size='24px' /> : "Calculate Financed Emissions"
                }
            </button>
        </div>
    </div>
  )
}

export default FinancedEmissions