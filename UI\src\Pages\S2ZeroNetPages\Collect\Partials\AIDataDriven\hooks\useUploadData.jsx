import { useState } from "react";
import axios from "axios";
import Cookies from "js-cookie";
import { useGetData } from "./useGetData";

export const useUploadData = () => {
  const [loading, setLoading] = useState(false);
  const [files, setFiles] = useState([]);
  const { handleGetData } = useGetData();

  const handleRemove = (indexToRemove) => {
    setFiles((prevFiles) =>
      prevFiles.filter((_, idx) => idx !== indexToRemove)
    );
  };

  const handleUploadData = async (files) => {
    if (!files) {
      alert("Please select a PDF file.");
      return;
    }
    setLoading(true);
    const formData = new FormData();
    for (const file of files) {
      formData.append("files", file);
    }
    try {
      const { data } = await axios.post(
        "https://pdf-extraction-staging.azurewebsites.net/submit_pdfs",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            withCredentials: true,
            Authorization: `Bearer ${Cookies.get("level_user_token")}`,
          },
        }
      );
      console.log(data);
      setLoading(false);
      handleGetData();
    } catch (error) {
      console.log(error);
      setLoading(false);
    }
  };

  return { files, setFiles, loading, handleRemove, handleUploadData };
};
