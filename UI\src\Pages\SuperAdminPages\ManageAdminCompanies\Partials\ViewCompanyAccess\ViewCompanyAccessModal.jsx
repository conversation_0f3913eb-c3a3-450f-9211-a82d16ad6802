import { Modal } from "@mantine/core";
import ViewAccessTable from "./ViewCompanyAccessTable";

export default function ViewAccessModal({
  opened,
  close,
  data,
  SetSelectedCompanyId,
  SetSelectedViewAccess,
  selectedCompanyId,
  getAllCompanyAccounts
}) {
  console.log(data);

  return (
    <Modal
      opened={opened}
      onClose={() => {
        close();
        SetSelectedCompanyId(null);
        SetSelectedViewAccess(null);
      }}
      size={"100%"}
      centered
    >
      <ViewAccessTable
        data={data}
        selectedCompanyId={selectedCompanyId}
        close={close}
        getAllCompanyAccounts={getAllCompanyAccounts}
      />
    </Modal>
  );
}
