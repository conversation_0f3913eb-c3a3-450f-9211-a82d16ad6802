// import ApiS2 from "@/Api/apiS2Config";
// import Loading from "@/Components/Loading";
import MainLayout from "@/Layout/MainLayout";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import { Button, Input, Modal, 
  // Pagination 
} from "@mantine/core";
import { 
  // useEffect, 
  useState } from "react";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router";
import DepartmentsandProjectsTable from "./Partials/DepartmentsandProjectsTable";
import { useAuth } from "@/Contexts/AuthContext";
import { CiSearch } from "react-icons/ci";
import { FaPlus } from "react-icons/fa";
import { useDisclosure } from "@mantine/hooks";
import DepartmentsandProjectsForm from "./Partials/DepartmentsandProjectsForm";

export default function DepartmentsandProjects() {
  const { user } = useAuth();
  const { getStartMenu } = useSideBarRoute();
  const { pathname } = useLocation();
  const [opened, { open, close }] = useDisclosure(false);
  const navigate = useNavigate();
  const { t } = useTranslation();
  // const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [refreshTrigger, setRefreshTrigger] = useState(0); // Add refresh trigger state

  // Function to handle successful form submission
  const handleFormSuccess = () => {
    close(); // Close the modal
    setRefreshTrigger(prev => prev + 1); // Trigger table refresh by updating state
  };

  return (
    <>
      <MainLayout menus={getStartMenu} navbarTitle={t("Configurations")}>
        <>
          <div className="w-full p-5 text-center bg-white rounded-lg">
            <h1 className="text-3xl font-bold text-primary">{t("Departments and Projects")}</h1>
          </div>
          <div className="justify-around p-5 mt-5 text-center rounded-lg xl:flex">
            {user?.userRole === "Admin" && (
              <Button
                className={`px-16 hover:bg-primary hover:text-white ${
                  pathname.includes("/Configurations/CompanyUserManage")
                    ? "bg-primary text-white"
                    : "bg-[#07838F33] border border-primary text-primary"
                }`}
                size="lg"
                onClick={() =>
                  navigate(
                    "/Settings/CompanyProfile/Configurations/CompanyUserManage"
                  )
                }
              >
                {t("Manage Users")}
              </Button>
            )}
            {user?.userRole === "Admin" && (
              <Button
                className={`px-16 hover:bg-primary hover:text-white block mx-auto xl:mx-0 mt-5 xl:mt-0 ${
                  pathname.includes("/Configurations/CompanyLogs")
                    ? "bg-primary text-white"
                    : "bg-[#07838F33] border border-primary text-primary"
                }`}
                size="lg"
                onClick={() =>
                  navigate(
                    "/Settings/CompanyProfile/Configurations/CompanyLogs"
                  )
                }
              >
                {t("Company Logs")}
              </Button>
            )}
            {user?.userRole === "Admin" && (
            <Button
              className={`hover:bg-primary hover:text-white block mx-auto xl:mx-0 mt-5 xl:mt-0 ${
                pathname.includes("/Configurations/DepartmentsandProjects")
                  ? "bg-primary text-white"
                  : "bg-[#07838F33] border border-primary text-primary"
              }`}
              size="lg"
              onClick={() =>
                navigate("/Settings/CompanyProfile/Configurations/DepartmentsandProjects")
              }
            >
              {t("Departments and Projects")}
            </Button>
            )}
            <Button
              className={`px-24 hover:bg-primary hover:text-white block mx-auto xl:mx-0 mt-5 xl:mt-0 ${
                pathname.includes("/Configurations/Assets")
                  ? "bg-primary text-white"
                  : "bg-[#07838F33] border border-primary text-primary"
              }`}
              size="lg"
              onClick={() =>
                navigate("/Settings/CompanyProfile/Configurations/Assets")
              }
            >
              {t("Assets")}
            </Button>
            <Button
              className={`hover:bg-primary hover:text-white block mx-auto xl:mx-0 mt-5 xl:mt-0 ${
                pathname.includes("/Configurations/CustomFactor")
                  ? "bg-primary text-white"
                  : "bg-[#07838F33] border border-primary text-primary"
              }`}
              size="lg"
              onClick={() =>
                navigate("/Settings/CompanyProfile/Configurations/CustomFactor")
              }
            >
              {t("Emission Factor Selection")}
            </Button>
          </div>
        </>

        {/* Modal */}
        <Modal size={"xl"} opened={opened} onClose={close} title="">
          <div className="p-6">
            <div className="flex items-center gap-2 mb-6">
              <div className="w-6 h-6 rounded-xl bg-primary/50 flex items-center justify-center">
                <FaPlus size={10} className="text-primary" />
              </div>
              <h1 className="text-2xl font-bold text-navy-900">Add Departments/Projects</h1>
            </div>
            <hr className="mt-1 mb-3 bg-[#D1D1D1] h-[2px]" />

            <h2 className="bg-gray-100 py-1 px-1 text-xl font-bold rounded-xl text-navy-900">
              Departments/Projects Details
            </h2>

            <DepartmentsandProjectsForm onSuccess={handleFormSuccess} />
          </div>
        </Modal>

        <div className="shadow-lg rounded-2xl bg-white p-4">
          <div className="flex mb-5 justify-between items-center">
            <h5 className="text-2xl font-semibold">Departments/Projects List</h5>

            <div className="flex gap-2 items-center">
              <Input
                type="text"
                placeholder={t("SearchPlaceholder")}
                className="border border-gray-300 rounded-md sm:w-auto focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                size="md"
                leftSection={<CiSearch />}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <Button
                onClick={open}
                size="md"
                className="bg-primary flex items-center gap-4 add-asset"
              >
                <FaPlus size={10} className="mr-2" />
                Add Department/Project
              </Button>
            </div>
          </div>
          
          <DepartmentsandProjectsTable 
            searchTerm={searchTerm} 
            refreshTrigger={refreshTrigger}
          />
        </div>
      </MainLayout>
    </>
  );
}