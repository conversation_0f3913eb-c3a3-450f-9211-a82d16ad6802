// Criterion.jsx
import { useEffect, useState } from "react";
import { useAssessmentStore } from "../AssessmentContext";
import { Select, TextInput } from "@mantine/core";
import EvidenceEditor from "./EvidenceEditor";
import ActionsEditor from "./ActionsEditor";

const Criterion = ({ criterion, sectionIndex, topicIndex }) => {
    const {
        updateCriterion,
        addCriterionEvidence,
        deleteCriterionEvidence,
        addCriterionAction,
        deleteCriterionAction,
    } = useAssessmentStore();
    const [currentEvidences, setCurrentEvidences] = useState([
        ...(criterion.evidences || []),
    ]);
    const [currentActions, setCurrentActions] = useState([
        ...(criterion.actions || []),
    ]);

    // Status colors configuration
    const statusColors = {
        "Not Started": {
            bg: "#AB020233",
            border: "#AB0202",
            text: "#AB0202",
        },
        "Initial Planning": {
            bg: "#e9dff3",
            border: "#9160C1",
            text: "#9160C1",
        },
        "In Development": {
            bg: "#ffeecd",
            border: "#FFAB07",
            text: "#FFAB07",
        },
        "Partially Implemented": {
            bg: "#d4e8fb",
            border: "#298BED",
            text: "#298BED",
        },
        "Fully Implemented": {
            bg: "#e2f6e7",
            border: "#01BD36",
            text: "#01BD36",
        },
    };

    const statusOptions = Object.keys(statusColors);

    const handleLevelChange = async (value) => {
        try {
            // Get index of selected status (1-based)
            const level = statusOptions.indexOf(value) + 1;

            // Update backend
            await updateCriterion(criterion.id, level);
        } catch (error) {
            console.error("Failed to update criterion level:", error);
        }
    };

    const handleAddEvidence = async (data) => {
        try {
            const response = await addCriterionEvidence(criterion.id, data);

            setCurrentEvidences(response.criterion.evidences);
        } catch (error) {
            console.error("Failed to add evidence:", error);
        }
    };

    const handleDeleteEvidence = async (evidenceId) => {
        try {
            const response = await deleteCriterionEvidence(
                criterion.id,
                evidenceId
            );

            setCurrentEvidences(response.criterion.evidences);
        } catch (error) {
            console.error("Failed to delete evidence:", error);
        }
    };

    const handleAddAction = async (data) => {
        try {
            const response = await addCriterionAction(criterion.id, data);
            setCurrentActions(response.criterion.actions);
        } catch (error) {
            console.error("Failed to add evidence:", error);
        }
    };

    const handleDeleteAction = async (actionId) => {
        try {
            const response = await deleteCriterionAction(
                criterion.id,
                actionId
            );
            setCurrentActions(response.criterion.actions);
        } catch (error) {
            console.error("Failed to delete evidence:", error);
        }
    };

    return (
        <tr className="border-y-2 border-[#E8E7EA] box-border font-semibold w-full">
            <td className="p-4 border-x-2 border-[#E8E7EA] last:border-r-0 box-border w-[500px]">
                {sectionIndex}.{topicIndex}.{criterion.index}{" "}
                {criterion.question}
            </td>

            <td className="p-4 border-r-2 border-[#E8E7EA] last:border-r-0 box-border w-52">
                <Select
                    data={statusOptions}
                    value={
                        criterion.level
                            ? statusOptions[criterion.level - 1]
                            : null
                    }
                    onChange={handleLevelChange}
                    radius="xl"
                    size="xs"
                    withCheckIcon={false}
                    allowDeselect={false}
                    rightSectionWidth="0"
                    placeholder="Select your level"
                    styles={(theme) => {
                        const readinessLevelValue = criterion.level
                            ? statusOptions[criterion.level - 1]
                            : null;
                        const readinessStyles = readinessLevelValue
                            ? statusColors[readinessLevelValue]
                            : {
                                  bg: theme.colors.gray[0],
                                  text: "black",
                                  border: theme.colors.gray[3],
                              };

                        return {
                            input: {
                                backgroundColor: readinessStyles.bg,
                                color: readinessStyles.text,
                                border: `1px solid ${readinessStyles.border}`,
                                padding: "16px 12px",
                                borderRadius: "15px",
                                fontSize: "14px",
                                fontWeight: "500",
                                textAlign: "center",
                                boxShadow: `0 2px 4px rgba(0, 0, 0, 0.1)`,
                            },
                        };
                    }}
                />
            </td>

            <td className="p-4 border-r-2 border-[#E8E7EA] last:border-r-0 box-border w-[500px]">
                <EvidenceEditor
                    evidences={currentEvidences}
                    onAddEvidence={handleAddEvidence}
                    onDeleteEvidence={handleDeleteEvidence}
                />
            </td>

            <td className="p-6 border-r-2 border-[#E8E7EA] box-border w-[500px] h-full">
                <ActionsEditor
                    actions={currentActions}
                    onAddAction={handleAddAction}
                    onDeleteAction={handleDeleteAction}
                />
            </td>
        </tr>
    );
};

export default Criterion;
