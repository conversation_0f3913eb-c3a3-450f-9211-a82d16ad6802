import GreenHubAPI from "@/Api/GreenHubAPI";
import axios from "axios";
import Cookies from "js-cookie";

export async function getTranslation(body) {
  const data = await GreenHubAPI.post(
    "/translate",
    body
  ).catch(function (error) {
    if (error.response) {
      console.log(error.response.data);
      console.log(error.response.status);
      console.log(error.response.headers);
    } else if (error.request) {
      console.log(error.request);
    } else {
      console.log("Error", error.message);
    }
    console.log(error.config);
  });
  // if (data.error) throw new Error(data.error);
  console.log(data);
  return data?.data?.data;
}
