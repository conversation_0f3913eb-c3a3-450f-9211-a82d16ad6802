import React, { useState } from "react";
// import ReactApex<PERSON><PERSON> from "react-apexcharts";
import { ReactApexChart } from "apexcharts";

const generateData = (count, yrange) => {
  let i = 0;
  let series = [];
  while (i < count) {
    let x = (i + 1).toString();
    let y =
      Math.floor(Math.random() * (yrange.max - yrange.min + 1)) + yrange.min;

    series.push({ x: x, y: y });
    i++;
  }
  return series;
};

const ApexChart = () => {
  const [series] = useState([
    { name: "Metric1", data: generateData(18, { min: 0, max: 90 }) },
    { name: "Metric2", data: generateData(18, { min: 0, max: 90 }) },
    { name: "Metric3", data: generateData(18, { min: 0, max: 90 }) },
    { name: "Metric4", data: generateData(18, { min: 0, max: 90 }) },
    { name: "Metric5", data: generateData(18, { min: 0, max: 90 }) },
    { name: "Metric6", data: generateData(18, { min: 0, max: 90 }) },
    { name: "Metric7", data: generateData(18, { min: 0, max: 90 }) },
    { name: "Metric8", data: generateData(18, { min: 0, max: 90 }) },
    { name: "Metric9", data: generateData(18, { min: 0, max: 90 }) },
  ]);

  const [options] = useState({
    chart: {
      height: 350,
      type: "heatmap",
    },
    dataLabels: {
      enabled: false,
    },
    colors: ["#008FFB"],
    title: {
      text: "HeatMap Chart (Single color)",
    },
  });

  return (
    <div>
      <div id="chart">
        <ReactApexChart
          options={options}
          series={series}
          type="heatmap"
          height={350}
        />
      </div>
      <div id="html-dist"></div>
    </div>
  );
};

export default ApexChart;
