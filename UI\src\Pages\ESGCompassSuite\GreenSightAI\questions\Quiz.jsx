import { Link } from "react-router-dom"; // استيراد useParams و useNavigate
import Question from "./Question";
import Loading from "../../../../Components/Loading";
import { useSustain360Store } from "@/Store/Assessment/useSustain360Store";
import { useEffect } from "react";
const Quiz = () => {

  const { activeQuestion,activeQuestionIndex,QuizLoading,isLastSection, activeAssesmentSection,setActiveQuestion ,getActiveAssesmentSection, goToPreviousQuestion,goToNextQuestion} = useSustain360Store();

  
  const showPreviousQuestion = () => {
    goToPreviousQuestion();
    console.log(activeQuestion)
  };

  const showNextQuestion = () => {
    goToNextQuestion();
  };

  return (
    <div>
      {QuizLoading ? (
        <Loading />
      ) : getActiveAssesmentSection() ? (
        <>
          {getActiveAssesmentSection()?.questions.length > 0 && (
            <>
              <div className="flex justify-end text-secondary font-semibold pr-3">
                <span>{activeQuestionIndex + 1}</span>
                <span>/{getActiveAssesmentSection()?.questions.length}</span>
              </div>
              <Question
                key={activeQuestionIndex}
                question={
                  getActiveAssesmentSection().questions[activeQuestionIndex]
                }
                currentQuestionIndex={activeQuestionIndex}
                section={getActiveAssesmentSection()}
              />
            </>
          )}
          <div className="flex flex-col items-start md:flex-row sm:justify-between my-5">
            <div className="flex flex-col-reverse  sm:flex-row items-start sm:items-center sm:justify-end">
              <div className="flex gap-5 sm:gap-0 mb-4 sm:mb-0">
                
                  <button
                    disabled={activeQuestion <= 1}
                    title={`${
                      activeQuestion <= 1
                        ? "You are in the first question"
                        : ""
                    }`}
                    className={`"min-w-[100px] text-secondary text-lg font-semibold py-1 px-1 sm:pl-2 sm:pr-4 sm:mx-2 sm:my-0 rounded-lg border-2 border-secondary ${
                      activeQuestion <= 1
                        ? "cursor-not-allowed"
                        : " hover:bg-secondary hover:text-white"
                    }`}
                    onClick={showPreviousQuestion}
                  >
                    {`<`} Prev Question
                  </button>
                  <button
                    className={`"min-w-[100px] text-secondary text-lg font-semibold py-1 px-1 sm:pl-2 sm:pr-4 sm:mx-2 sm:my-0 rounded-lg border-2 border-secondary hover:bg-secondary hover:text-white
                      ${activeQuestion >= 29 ? "cursor-not-allowed" : ""}`}
                    onClick={()=>showNextQuestion()}
                    disabled={activeQuestion >= 29}
                  >
                    Next Question {`>`}
                  </button>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row justify-end">
            <Link
              to={"/Insights-reporing/greensight/summary"}
              className="bg-[#2b939c] min-w-[100px] text-lg text-white font-semibold px-3 py-1 my-2 rounded-lg"
            >
              Review
            </Link>
          </div>
        </>
      ) : (
        <>Not Found The scope</>
      )}
    </div>
  );
};

export default Quiz;
