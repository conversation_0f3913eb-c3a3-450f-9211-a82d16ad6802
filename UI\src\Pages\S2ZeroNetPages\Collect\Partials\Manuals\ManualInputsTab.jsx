import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import ManualForm from "./ManualForm";
import ManualTable from "./ManualTable";
// import { driver } from "driver.js";
// import "driver.js/dist/driver.css";
// import "@/assets/css/index.css";
// import { FaQuestionCircle } from "react-icons/fa";

export default function ManualInputsTab({
  activeTab,
  assetTypeDrop,
  assetTypeAll,
  // dataCFByAT,
  AssetsError,
  handelAssetType,
  loading,
  getTableData,
  loadingTable,
  data,
  Status,
  error,
  allItemSpecificities,
}) {
  const { t } = useTranslation();
    // const [elementsReady, setElementsReady] = useState(false);


  useEffect(() => {
    handelAssetType();
    // getTableData("manual")
  }, []);

  // const getGuideSteps = () => [
  //   {
  //     element: ".Enter-records",
  //     popover: {
  //       title: t("Enter your records using Manual input"),
  //       description: t("You will just pick from drop down menu"),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".From-Dates",
  //     popover: {
  //       title: t("From Dates"),
  //       description: t(
  //         "Select the date range during which the activity occurred (format: DD/MM/YYYY)."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".To-Dates",
  //     popover: {
  //       title: t("To Dates"),
  //       description: t(
  //         "Select the date range during which the activity occurred (format: DD/MM/YYYY)."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Assets-collect",
  //     popover: {
  //       title: t("Assets"),
  //       description: t(
  //         "Choose the asset you want to record emissions for (from the assets you previously created)."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Emission-Source",
  //     popover: {
  //       title: t("Emission Source"),
  //       description: t(
  //         "Select the emission source related to this activity (e.g., Purchased Electricity, Refrigerants)."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Activity",
  //     popover: {
  //       title: t("Activity"),
  //       description: t(
  //         "Pick the activity associated with the emission (e.g., Energy Consumption, Business Travel)."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".E-Factors",
  //     popover: {
  //       title: t("E-Factors"),
  //       description: t(
  //         "Choose the relevant custom emission factor from the list you entered earlier."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".UOM",
  //     popover: {
  //       title: t("UOM"),
  //       description: t(
  //         "Select the appropriate unit for measuring the activity (e.g., kWh, liters)."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Reporting-Year",
  //     popover: {
  //       title: t("Reporting Year"),
  //       description: t(
  //         "Enter the reporting year for which the data is being collected (e.g., 2024)."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Location-Specificity",
  //     popover: {
  //       title: t("Location Specificity"),
  //       description: t(
  //         "Choose whether the emission factor is globally applicable or specific to a certain location."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Source-Physical",
  //     popover: {
  //       title: t("Source Physical"),
  //       description: t(
  //         "Specify whether the source of the activity is a physical asset."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Source-Service",
  //     popover: {
  //       title: t("Source service"),
  //       description: t(
  //         "Specify whether the source of the activity is a service."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Additional-Payload",
  //     popover: {
  //       title: t("Additional Payload (Optional)"),
  //       description: t("Add any extra metadata if needed."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Evidence",
  //     popover: {
  //       title: t("Evidence"),
  //       description: t(
  //         "Upload supporting documents such as invoices, reports, or other proof (e.g., File.pdf)."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Actual-Estimate-Data",
  //     popover: {
  //       title: t("Actual / Estimate Data"),
  //       description: t(
  //         "Specify whether the entered data is Actual (measured) or Estimated (calculated or assumed)."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Click-Confirm-Button",
  //     popover: {
  //       title: t("Click “Confirm” Button"),
  //       description: t(
  //         "After filling in all fields, click Confirm to save the record into the system. The record will appear under the Recent Uploaded Manual Input Data table."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Manage-Uploaded-Records",
  //     popover: {
  //       title: t("Manage Uploaded Records:"),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Search-manual-collect",
  //     popover: {
  //       title: t("Search"),
  //       description: t(
  //         "Use the search bar to quickly find specific records by topic area or assessment question."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Filter-manual-collect",
  //     popover: {
  //       title: t("Filter"),
  //       description: t(
  //         "Filter records by their status: ◦ Pending ◦ Accepted ◦ Rejected"
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Status-Overview",
  //     popover: {
  //       title: t("Status Overview"),
  //       description: t(
  //         "Showing number of entries (e.g., 2 entries currently shown)."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Export-Records",
  //     popover: {
  //       title: t("Export Records"),
  //       description: t(
  //         "Use the Export button to download all or filtered records into a CSV or Excel file for reporting, analysis, or backup."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".notes1",
  //     popover: {
  //       title: t("NOTE 1"),
  //       description: t(
  //         "Manual input is best suited for small data entry (one record at a time)."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".notes2",
  //     popover: {
  //       title: t("NOTES 2"),
  //       description: t(
  //         "For bulk uploads, you can later switch to Batch Input or use AI Data Driven options."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".notes3",
  //     popover: {
  //       title: t("NOTES 3"),
  //       description: t(
  //         "Ensure that all required fields are filled correctly to avoid validation errors."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  // ];

  //   const checkElementsExist = () => {
  //   const steps = getGuideSteps();
  //   const allElementsExist = steps.every(step => 
  //     document.querySelector(step.element)
  //   );
  //   return allElementsExist;
  // };

  // useEffect(() => {
  //   if (activeTab === "manual-input") { // تحقق من أن التبويبة نشطة
  //     const observer = new MutationObserver(() => {
  //       if (checkElementsExist()) {
  //         setElementsReady(true);
  //         observer.disconnect();
  //       }
  //     });

  //     observer.observe(document.body, {
  //       childList: true,
  //       subtree: true,
  //     });

  //     return () => observer.disconnect();
  //   }
  // }, [activeTab]);


  
  // const startGuide = () => {
  //   const driverObj = driver({
  //     showProgress: true,
  //     popoverClass: "my-custom-popover-class",
  //     nextBtnText: "Next",
  //     prevBtnText: "Back",
  //     doneBtnText: "Done",
  //     progressText: "Step {{current}} of {{total}}",
  //     overlayColor: "rgba(0, 0, 0, 0)",
  //     steps: getGuideSteps(),
  //     onDestroyStarted: () => {
  //       localStorage.setItem("hasSeenEmissionCollectGuide", "true");
  //       driverObj.destroy();
  //     },
  //   });
  //   driverObj.drive();
  // };

  // useEffect(() => {
  //   const hasSeenGuide = localStorage.getItem("hasSeenEmissionCollectGuide");
  //   if (!hasSeenGuide) {
  //     startGuide();
  //   }
  //   return () => {
  //     const driverObj = driver();
  //     if (driverObj.isActive()) {
  //       driverObj.destroy();
  //     }
  //   };
  // }, [activeTab, elementsReady]);

  return (
    <>
      {/* <div
        onClick={startGuide}
        style={{
          position: "fixed",
          bottom: 20,
          right: 20,
          cursor: "pointer",
          zIndex: 1000,
        }}
      >
        <div className="h-12 w-12 flex items-center justify-center bg-gradient-to-tr from-[#2C5A8C] via-[#1C889C] to-[#13B1A8] p-1  cursor-pointer rounded-full">
          <FaQuestionCircle
            size={34}
            color="#ffffff"
            className="mx-auto cursor-pointer"
          />
        </div>
      </div> */}
      <ManualForm
        getTableData={getTableData}
        // dataCFByAT={dataCFByAT}
        AssetsError={AssetsError}
        assetTypeAll={assetTypeAll}
        assetTypeDrop={assetTypeDrop}
        loading={loading}
        allItemSpecificities={allItemSpecificities}
      />

      <ManualTable
        getTableData={getTableData}
        data={data}
        loading={loadingTable}
        Status={Status}
        assetTypeAll={assetTypeAll}
        // error={AssetsError}
        error={error}
      />
    </>
  );
}
