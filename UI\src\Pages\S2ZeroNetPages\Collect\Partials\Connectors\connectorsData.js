import XeroImg from "@/assets/svg/Xero.svg";
import HubSpotImg from "@/assets/svg/HubSpot.svg";
import OracleNetsuiteImg from "@/assets/svg/OracleNetsuite.svg";
import SageHRImg from "@/assets/svg/Sage HR.svg";
import QuickBooksImg from "@/assets/svg/QuickBooks.svg";
import ZohoBooksImg from "@/assets/svg/Zoho Books.svg";
import salesforce from "@/assets/svg/airbyte.png";
import mysql from "@/assets/images/mysql.png";
import microsoftsqlserver from "@/assets/images/microsoft-sql-server.png";
import countries from "world-countries";

const prioritized = ['United Kingdom', 'United Arab Emirates', 'United States', 'Saudi Arabia', 'Qatar'];

// Sort countries: prioritized first, then the rest
const sortedCountries = [
  ...countries.filter(c => prioritized.includes(c.name.common)),
  ...countries
    .filter(c => !prioritized.includes(c.name.common))
    .sort((a, b) => a.name.common.localeCompare(b.name.common))
];

export const connectorsData = {
MySQL: {
  title: "MySQL",
  formConnection: {
    client: "mysql",
    name: "",
    connection: {
      name: "",
      keyName: "MySQL",
      config: {
        database: "",
        host: "",
        port: '',
        user: "",
        password: ""
      }
    }
  },
  inputs: [
    {
      label: "Name",
      placeholder: "Enter connection name",
      key: "text",
      formConnection: "name"
    },
    {
      label: "Database",
      placeholder: "Enter database name",
      key: "text",
      formConnection: "connection.config.database"
    },
    {
      label: "Host",
      placeholder: "Enter host address",
      key: "text",
      formConnection: "connection.config.host"
    },
    {
      label: "Port",
      placeholder: "Enter port number",
      key: "text",
      formConnection: "connection.config.port"
    },
    {
      label: "User",
      placeholder: "Enter username",
      key: "text",
      formConnection: "connection.config.user"
    },
    {
      label: "Password",
      placeholder: "Enter password",
      key: "text",
      formConnection: "connection.config.password"
    }
  ],
  img: mysql,
  validate: {
    name: (value) => (value ? null : "Name is required"),
    "connection.config.database": (value) => (value ? null : "Database name is required"),
    "connection.config.host": (value) => (value ? null : "Host is required"),
    "connection.config.port": (value) => (value ? null : "Port is required"),
    "connection.config.user": (value) => (value ? null : "Username is required"),
    "connection.config.password": (value) => (value ? null : "Password is required")
  }
},
MSSQL: {
  title: "Microsoft SQL Server",
  formConnection: {
    client: "microsoft",
    name: "",
    connection: {
      name: "source-mssql",
      keyName: "MSSQL",
      config: {
        database: "",
        host: "",
        port: '',
        user: "",
        password: ""
      }
    }
  },
  inputs: [
    {
      label: "Name",
      placeholder: "Enter connection name",
      key: "text",
      formConnection: "name"
    },
    {
      label: "Database",
      placeholder: "Enter database name",
      key: "text",
      formConnection: "connection.config.database"
    },
    {
      label: "Host",
      placeholder: "Enter host address",
      key: "text",
      formConnection: "connection.config.host"
    },
    {
      label: "Port",
      placeholder: "Enter port number",
      key: "text",
      formConnection: "connection.config.port"
    },
    {
      label: "User",
      placeholder: "Enter username",
      key: "text",
      formConnection: "connection.config.user"
    },
    {
      label: "Password",
      placeholder: "Enter password",
      key: "text",
      formConnection: "connection.config.password"
    }
  ],
  img: microsoftsqlserver,
  validate: {
    name: (value) => (value ? null : "Name is required"),
    "connection.config.database": (value) => (value ? null : "Database name is required"),
    "connection.config.host": (value) => (value ? null : "Host is required"),
    "connection.config.port": (value) => (value ? null : "Port is required"),
    "connection.config.user": (value) => (value ? null : "Username is required"),
    "connection.config.password": (value) => (value ? null : "Password is required")
  }
},
 Xero: {
  title: "Xero",
  formConnection: {
    client: "airbyte",
    name: "", // User will input this
    connection: {
     name: "source-xero", // Constant
     keyName: "Xero",
     config: {
      credentials: {
       access_token: "",
       auth_type: "oauth2_access_token", // Constant
      },
      tenant_id: "",
      start_date: "",
     },
    },
  },
  inputs: [
   {
    label: "Name",
    placeholder: "Enter a name",
    key: "text",
    formConnection: "name",
   },
   {
    label: "Access Token",
    placeholder: "Enter your Access Token",
    key: "text",
    formConnection: "connection.config.credentials.access_token",
   },
   {
    label: "Tenant ID",
    placeholder: "Enter your Tenant ID",
    key: "text",
    formConnection: "connection.config.tenant_id",
   },
   {
    label: "Start Date",
    placeholder: "Enter your Start Date",
    key: "date",
    formConnection: "connection.config.start_date",
   },
  ],
  img: XeroImg,
  validate: {
   name: (value) => (value ? null : "Name is required"),
   connection: {
    config: {
     credentials: {
      access_token: (value) => (value ? null : "Access Token is required"),
     },
     tenant_id: (value) => (value ? null : "Tenant ID is required"),
     start_date: (value) => (value ? null : "Start Date is required"),
    },
   },
  },
 },
 OracleNetsuite: {
  title: "Oracle Netsuite",
  formConnection: {
    client: "airbyte",
   name: "", // User input
   connection: {
    name: "source-netsuite",
    keyName: "Oracle Netsuite",
    config: {
     realm: "",
     consumer_key: "",
     consumer_secret: "",
     token_key: "",
     token_secret: "",
    },
    start_date: "",
   },
  },
  inputs: [
   {
    label: "Name",
    placeholder: "Enter a name",
    key: "text",
    formConnection: "name",
   },
   {
    label: "Realm",
    placeholder: "Enter your Realm",
    key: "text",
    formConnection: "connection.config.realm",
   },
   {
    label: "Consumer Key",
    placeholder: "Enter your Consumer Key",
    key: "text",
    formConnection: "connection.config.consumer_key",
   },
   {
    label: "Consumer Secret",
    placeholder: "Enter your Consumer Secret",
    key: "text",
    formConnection: "connection.config.consumer_secret",
   },
   {
    label: "Token Key",
    placeholder: "Enter your Token Key",
    key: "text",
    formConnection: "connection.config.token_key",
   },
   {
    label: "Token Secret",
    placeholder: "Enter your Token Secret",
    key: "text",
    formConnection: "connection.config.token_secret",
   },
   {
    label: "Start Date",
    placeholder: "Enter your Start Date",
    key: "date",
    formConnection: "connection.start_date",
   },
  ],
  img: OracleNetsuiteImg,
  validate: {
   name: (value) => (value ? null : "Name is required"),
   connection: {
    config: {
     realm: (value) => (value ? null : "Realm is required"),
     consumer_key: (value) => (value ? null : "Consumer Key is required"),
     consumer_secret: (value) => (value ? null : "Consumer Secret is required"),
     token_key: (value) => (value ? null : "Token Key is required"),
     token_secret: (value) => (value ? null : "Token Secret is required"),
    },
    start_date: (value) => (value ? null : "Start Date is required"),
   },
  },
 },
 HubSpot: {
  title: "HubSpot",
  formConnection: {
  client: "airbyte",
 name: "", // User will input this
 connection: {
  name: "source-hubspot", // Constant
  config: {
   credentials: {
    access_token: "",
    credentials_title: "", // Constant
   },
  },
 },
  },
  inputs: [
   {
    label: "Name",
    placeholder: "Enter name",
    key: "text",
    formConnection: "name",
   },
   {
    label: "Access Token",
    placeholder: "Enter your Access Token",
    key: "text",
    formConnection: "connection.config.credentials.access_token",
   },
   {
    label: "Credentials Title",
    placeholder: "Select Option",
    key: "DropDown",
    data: ["Private App Credentials"],
    formConnection: "connection.config.credentials.credentials_title",
   },
  ],
  img: HubSpotImg,
  validate: {
   name: (value) => (value ? null : "Name is required"),
   connection: {
    config: {
     credentials: {
      access_token: (value) => (value ? null : "Access Token is required"),
     },
    },
   },
  },
 },
 SageHR: {
  title: "Sage HR",
  formConnection: {
    client: "airbyte",
   name: "", // User input
   connection: {
    name: "source-sage-hr", // Constant
    keyName: "Sage HR", // Constant
    config: {
     api_key: "",
     subdomain: "",
    },
   },
  },
  inputs: [
   {
    label: "Name",
    placeholder: "Enter connection name",
    key: "text",
    formConnection: "name",
   },
   {
    label: "API Key",
    placeholder: "Enter your API Key",
    key: "text",
    formConnection: "connection.config.api_key",
   },
   {
    label: "Subdomain",
    placeholder: "Enter your Subdomain",
    key: "text",
    formConnection: "connection.config.subdomain",
   },
  ],
  img: SageHRImg,
  validate: {
   name: (value) => (value ? null : "Name is required"),
   "connection.config.api_key": (value) =>
    value ? null : "API Key is required",
   "connection.config.subdomain": (value) =>
    value ? null : "Subdomain is required",
  },
 },
 QuickBooks: {
  title: "QuickBooks",
  formConnection: {
    client: "airbyte",
   name: "", // User will input this
   connection: {
    name: "source-quickbooks", // Constant
    keyName: "QuickBooks", // Constant
    config: {
     realm_id: "",
     client_id: "",
     access_token: "",
     client_secret: "",
     refresh_token: "",
     token_expiry_date: "",
     start_date: "", // Should be like this format
     sandbox: "",
     auth_type: "", // Constant + Not required
    },
   },
  },
  inputs: [
   {
    label: "Name",
    placeholder: "Enter connection name",
    key: "text",
    formConnection: "name",
   },
   {
    label: "Realm ID",
    placeholder: "Enter Realm ID",
    key: "text",
    formConnection: "connection.config.realm_id",
   },
   {
    label: "Client ID",
    placeholder: "Enter Client ID",
    key: "text",
    formConnection: "connection.config.client_id",
   },
   {
    label: "Access Token",
    placeholder: "Enter Access Token",
    key: "text",
    formConnection: "connection.config.access_token",
   },
   {
    label: "Client Secret",
    placeholder: "Enter Client Secret",
    key: "text",
    formConnection: "connection.config.client_secret",
   },
   {
    label: "Refresh Token",
    placeholder: "Enter Refresh Token",
    key: "text",
    formConnection: "connection.config.refresh_token",
   },
   {
    label: "Token Expiry Date",
    placeholder: "Enter Token Expiry Date",
    key: "date",
    formConnection: "connection.config.token_expiry_date",
   },
   {
    label: "Start Date",
    placeholder: "Enter Start Date",
    key: "date",
    formConnection: "connection.config.start_date",
   },
   {
    label: "Sandbox Mode",
    placeholder: "Select Sandbox Mode",
    key: "DropDown",
    data: ["true", "false"],
    formConnection: "connection.config.sandbox",
   },
   {
    label: "Auth Type",
    placeholder: "Select Auth Type",
    key: "DropDown",
    data: ["oauth2.0"],
    formConnection: "connection.config.auth_type",
   },
  ],
  img: QuickBooksImg,
  validate: {
   name: (value) => (value ? null : "Name is required"),
   "connection.config.realm_id": (value) =>
    value ? null : "Realm ID is required",
   "connection.config.client_id": (value) =>
    value ? null : "Client ID is required",
   "connection.config.access_token": (value) =>
    value ? null : "Access Token is required",
   "connection.config.client_secret": (value) =>
    value ? null : "Client Secret is required",
   "connection.config.refresh_token": (value) =>
    value ? null : "Refresh Token is required",
   "connection.config.token_expiry_date": (value) =>
    value ? null : "Token Expiry Date is required",
   "connection.config.start_date": (value) =>
    value ? null : "Start Date is required",
   "connection.config.sandbox": (value) =>
    value ? null : "Sandbox is required",
   "connection.config.auth_type": (value) =>
    value ? null : "Sandbox is required",
  },
 },
 ZohoBooks: {
  title: "Zoho Books",
  formConnection: {
   client: "airbyte",
   name: "", // User will input this
   connection: {
    name: "source-zoho-books", // Constant
    keyName: "Zoho Books", // Constant
    config: {
     region: "eu",
     client_id: "",
     client_secret: "",
     refresh_token: "",
     start_date: "", // Should be like this format
    },
   },
  },
  inputs: [
   {
    label: "Name",
    placeholder: "Enter name",
    key: "text",
    formConnection: "name",
   },
   {
    label: "Region",
    placeholder: "Select Region",
    key: "DropDown",
    formConnection: "connection.config.region",
    data: sortedCountries.map((country) => country.name.common),
   },
   {
    label: "Client ID",
    placeholder: "Enter your Client ID",
    key: "text",
    formConnection: "connection.config.client_id",
   },
   {
    label: "Client Secret",
    placeholder: "Enter your Client Secret",
    key: "text",
    formConnection: "connection.config.client_secret",
   },
   {
    label: "Refresh Token",
    placeholder: "Enter your Refresh Token",
    key: "text",
    formConnection: "connection.config.refresh_token",
   },
   {
    label: "Start Date",
    placeholder: "Enter your Start Date",
    key: "date",
    formConnection: "connection.config.start_date",
   },
  ],
  img: ZohoBooksImg,
  validate: {
   name: (value) => (value ? null : "Name is required"),
   "connection.config.client_id": (value) => (value ? null : "Client ID is required"),
   "connection.config.client_secret": (value) =>
    value ? null : "Client Secret is required",
   "connection.config.refresh_token": (value) =>
    value ? null : "Refresh Token is required",
  },
 },
 Salesforce: {
  title: "Salesforce",
  formConnection: {
    client: "airbyte",
    name: "", // User will input this
    connection: {
      name: "source-salesforce", // Constant, from provided data
      keyName: "Salesforce", // Display name
      config: {
        client_id: "",
        client_secret: "",
        refresh_token: "",
        auth_type: "Client" // Constant, from provided data
      }
    }
  },
  inputs: [
    {
      label: "Name",
      placeholder: "Enter connection name",
      key: "text",
      formConnection: "name"
    },
    {
      label: "Client ID",
      placeholder: "Enter your Client ID",
      key: "text",
      formConnection: "connection.config.client_id"
    },
    {
      label: "Client Secret",
      placeholder: "Enter your Client Secret",
      key: "text",
      formConnection: "connection.config.client_secret"
    },
    {
      label: "Refresh Token",
      placeholder: "Enter your Refresh Token",
      key: "text",
      formConnection: "connection.config.refresh_token"
    },
    // {
    //   label: "Auth Type",
    //   placeholder: "Select Auth Type",
    //   key: "DropDown",
    //   data: ["Client"], // Based on provided data
    //   formConnection: "connection.config.auth_type"
    // }
  ],
  img: salesforce, // Reference to imported SVG
  validate: {
    name: (value) => (value ? null : "Name is required"),
    "connection.config.client_id": (value) => (value ? null : "Client ID is required"),
    "connection.config.client_secret": (value) => (value ? null : "Client Secret is required"),
    "connection.config.refresh_token": (value) => (value ? null : "Refresh Token is required"),
    // "connection.config.auth_type": (value) => (value ? null : "Auth Type is required")
  }
}
};
