import { useState } from "react";
import CompanyInfoForm from "./CompanyInfoForm";
import AccordionDemo from "./Accordion";
import { useAnswersStore } from "@/Store/useAnswersStore";

export default function MultiStepForm() {
  const [currentStep, setCurrentStep] = useState(0);
  const companyInfo = useAnswersStore((state) => state.companyInfo);
  const setCompanyInfo = useAnswersStore((state) => state.setCompanyInfo);

  const handleCompanyInfoChange = (e) => {
    setCompanyInfo(e.target.name, e.target.value);
  };
  const nextStep = () => setCurrentStep((prev) => prev + 1);
  const previousStep = () => setCurrentStep((prev) => prev - 1);

  return (
    <div className="w-full mx-auto">
      {currentStep === 0 && (
        <CompanyInfoForm
          companyInfo={companyInfo}
          onChange={handleCompanyInfoChange}
          onNext={nextStep}
        />
      )}
      {currentStep === 1 && (
        <AccordionDemo onPreviousStep={previousStep} />
      )}
    </div>
  );
}
