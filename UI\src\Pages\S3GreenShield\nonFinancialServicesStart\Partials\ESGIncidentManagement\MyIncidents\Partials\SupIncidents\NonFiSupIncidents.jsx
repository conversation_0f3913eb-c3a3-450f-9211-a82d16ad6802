import S3Layout from "@/Layout/S3Layout";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import {
  Button,
  Checkbox,
  Group,
  Text,
  TextInput,
  Textarea,
} from "@mantine/core";
import React, { useEffect, useState } from "react";
import { IoMdCheckmarkCircle } from "react-icons/io";
import { IoCloseCircleSharp } from "react-icons/io5";
import { LuBadgeCheck } from "react-icons/lu";
import SupIncidentsTable from "./Partials/SupIncidentsTable";
import { useParams } from "react-router";
import ApiS3 from "@/Api/apiS3";
import Loading from "@/Components/Loading";

export default function NonFiSupIncidents() {
  const { greenShieldNonFiESGIncidentManagement } = useSideBarRoute();
  const [data, setData] = useState();
  const [loading, setLoading] = useState(false);
  const { id } = useParams();
  // const [checked, setChecked] = useState([]);

  // const handleChange = (index) => {
  //   const newChecked = [...checked];
  //   newChecked[index] = !newChecked[index];
  //   setChecked(newChecked);
  // };

  const getSupIncidents = async () => {
    setLoading(true);
    try {
      setLoading(true);

      const { data } = await ApiS3.get(`/incident-management/incidents/${id}`);
      setLoading(true);

      if (data.message === "Incident details fetched successfully") {
        setLoading(false);

        setData(data.incident);
      }
      // console.log(data);
    } catch (error) {
      setLoading(false);

      console.log(error);
    }
  };
  useEffect(() => {
    getSupIncidents();
  }, []);
  console.log(data);
  const files = [
    { name: "document1.pdf" },
    { name: "image2.jpg" },
    { name: "spreadsheet3.xlsx" },
  ];

  return (
    <S3Layout menus={greenShieldNonFiESGIncidentManagement}>
      {loading ? (
        <Loading />
      ) : (
        <div>
          <div className="w-full bg-white rounded-lg p-5 md:flex justify-between shadow-md">
            <div className="">
              <div className="flex items-center">
                <h1 className="text-xl font-semibold text-black me-1">
                  Incident :
                </h1>
                <TextInput
                  variant="unstyled"
                  placeholder="Add Title..."
                  size="md"
                  value={data?.title}
                />
              </div>
              <p className="text-base font-medium text-primary text-start">
                Incident ID: {data?.trackingId}
              </p>
            </div>
            <div className="flex items-center justify-center gap-10 mt-5 md:mt-0">
              <div className="text-center">
                <h1 className="text-base font-bold">Created on</h1>
                <p className="text-base font-bold">
                  {data?.createdAt.split("T")[0]}
                </p>
              </div>
              <div>
                <p
                  className={`text-base font-medium px-10 py-2 rounded-xl
              capitalize  ${
                data?.status.includes("in_progress")
                  ? "bg-[#fff0b8] text-[#FFAB07] px-8"
                  : data?.status.includes("new")
                  ? "bg-[#cafac2] text-[#70D162] px-8"
                  : data?.status.includes("resolved")
                  ? "bg-[#cea7f6] text-[#9160C1] px-8"
                  : "bg-red-300 text-red-800 px-8"
              }`}
                >
                  {data?.status}
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 mt-10">
            <div className="">
              <h1 className="w-full font-bold text-xl">Incident Details</h1>

              <div className="grid grid-cols-1 lg:grid-cols-2 items-center w-full gap-x-10 gap-y-3 text-start p-3 bg-white rounded-lg mt-3 shadow-md">
                <TextInput
                  radius="md"
                  label="Country"
                  placeholder="Enter Country"
                  value={data?.details.country}
                />
                <TextInput
                  radius="md"
                  label="State / City"
                  placeholder="Enter State / City"
                  value={data?.details.city}
                />
                <TextInput
                  radius="md"
                  label="Location"
                  placeholder="Enter Location"
                  value={data?.details.location}
                />
                <TextInput
                  radius="md"
                  label="Impact Assessment"
                  placeholder="Enter Impact Assessment"
                  value={data?.details.impactAssessment}
                />
                <div className="lg:col-span-2">
                  <Textarea
                    label="Details"
                    placeholder="Enter Details..."
                    minRows={1}
                    value={data?.details.message ? data?.details.message : ""}
                  />
                </div>
              </div>
            </div>
            <div className="flex flex-col justify-center w-full rounded-lg  items-center p-4 gap-3  ">
              <h1 className="w-full font-bold text-xl">Evidence Attachments</h1>
              {files.length > 0 && (
                <div className="mt-4 p-5 border border-primary text-primary rounded-lg truncate w-full shadow-md">
                  <ul className="h-auto">
                    {data?.evidences?.length > 0
                      ? data?.evidences?.map((file, index) => (
                          <li
                            key={index}
                            className="flex items-center justify-between mb-2"
                          >
                            <h1
                              style={{
                                maxWidth: "100%",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "nowrap",
                              }}
                            >
                              {file}
                            </h1>

                            <LuBadgeCheck className="ms-2 text-2xl text-primary" />
                            {/* </button> */}
                          </li>
                        ))
                      : "Evidences is Empty"}
                  </ul>
                </div>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 mt-10">
            <div className="bg-white rounded-lg p-5 shadow-md">
              <h1 className="w-full font-bold text-xl">Indvidual Involved</h1>
              <div className="md:flex justify-between ">
                <SupIncidentsTable involvedIndividualsData={data?.involvedIndividuals}/>
              </div>
            </div>

            <div className=" w-full rounded-lg  items-center p-4 gap-3 ">
              <div className=" md:flex justify-between">
                <p className="font-bold text-lg">Recent Activities</p>
                <Button
                  className="bg-transparent  text-primary hover:bg-transparent hover:text-primary mt-3 md:mt-0 underline"
                  size="md"
                >
                  View all
                </Button>
              </div>
              <div className="bg-white p-3 rounded-lg">
                <div className="flex justify-between items-center">
                  <h1 className="font-medium text-md text-primary">Today</h1>
                  <select className="mt-4 sm:mt-0 px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="Today">Today</option>
                    <option value="Tomorrow">Tomorrow</option>
                    <option value="Yesterday">Yesterday</option>
                  </select>
                </div>
                <div className="grid gap-4 justify-start ">
                  {/* {checked.map((isChecked, index) => ( */}

                  <Checkbox
                    // checked={isChecked}
                    // onChange={() => handleChange(index)}
                    color="teal"
                    size="sm"
                    label="Aleesha added a comment to E- Q.17"
                  />
                  <Checkbox
                    // checked={isChecked}
                    // onChange={() => handleChange(index)}
                    color="teal"
                    size="sm"
                    label="Lisa added a query to E- Q.10"
                  />
                  <Checkbox
                    // checked={isChecked}
                    // onChange={() => handleChange(index)}
                    color="teal"
                    size="sm"
                    label="Your environment report was accepted"
                  />
                  <Checkbox
                    // checked={isChecked}
                    // onChange={() => handleChange(index)}
                    color="teal"
                    size="sm"
                    label="Lisa added a query to E- Q.10"
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="w-full  p-5 mt-5">
            <div className=" md:flex justify-between">
              <p className="font-bold text-lg">Comments / Notes</p>
              <Button
                className="bg-transparent  text-primary hover:bg-transparent hover:text-primary mt-3 md:mt-0 underline"
                size="md"
              >
                View all
              </Button>
            </div>
            <div className="bg-white p-5 rounded-lg text-start">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-5">
                <div className="">
                  <Group className="lg:flex justify-between mb-3">
                    <h1 className="font-normal text-sm text-blue-950">
                      Lisa added a query to E- Q.10
                    </h1>
                    <p className="font-normal text-xs text-gray-700">
                      Just Now . By user Admin
                    </p>
                  </Group>
                  <Group className="lg:flex justify-between mb-3">
                    <h1 className="font-normal text-sm text-blue-950">
                      Your environment report was accepted{" "}
                    </h1>
                    <p className="font-normal text-xs text-gray-700">
                      Just Now . By user Admin
                    </p>
                  </Group>
                  <Group className="lg:flex justify-between mb-3">
                    <h1 className="font-normal text-sm text-blue-950">
                      Lisa added a query to E- Q.10{" "}
                    </h1>
                    <p className="font-normal text-xs text-gray-700">
                      Just Now . By user Admin
                    </p>
                  </Group>
                </div>
                <div className="grid lg:grid-cols-2 gap-3">
                  <TextInput
                    radius="md"
                    label="Add Comment"
                    placeholder="Add Comment"
                  />
                  <div className="flex  items-center">
                    <Button
                      className="bg-primary w-full text-white border-2 border-primary rounded-lg hover:bg-primary hover:text-white"
                      size="md"
                    >
                      Comment
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </S3Layout>
  );
}
