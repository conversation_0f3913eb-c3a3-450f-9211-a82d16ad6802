import Chatbot from "@/Components/Chatbot";
import { Breadcrumbs } from "@mantine/core";
import Navbar from "@/Components/NavBar/Navbar";
import { Link } from "react-router-dom";
import Sidebar from "@/Components/SideBar/Sidebar";
import { useState } from "react";
import { IoIosArrowForward } from "react-icons/io";

const S2Layout = ({
  menus = [],
  children,
  breadcrumbItems = [],
  navbarTitle,
}) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isChatBotOpen, setIsChatBotOpen] = useState(false)

  const items = breadcrumbItems.map((item, index) => (
    <Link
      className={`${
        index !== breadcrumbItems.length - 1
          ? "text-gray-400"
          : "text-primary font-[500]"
      }`}
      to={item.href}
      key={index}
    >
      {item.title}
    </Link>
  ));

  return (
    <div className="flex flex-col ">
      <Navbar
        navbarTitle={navbarTitle}
        isSidebarOpen={isSidebarOpen}
        setIsSidebarOpen={setIsSidebarOpen}
        items={items && items}
      />

      <div className="flex-grow flex h-screen overflow-hidden max-md:text-center z-10">
        <Sidebar
          menus={menus}
          isOpen={isSidebarOpen}
          setIsOpen={setIsSidebarOpen}
          openChatBot={()=> setIsChatBotOpen(!isChatBotOpen)}

        />
        <div className={"px-5 w-full mt-16 block h-screen mx-auto pb-20 overflow-y-auto"}>
          <div className=" text-2xl font-semibold font-inter leading-none text-primary mx-auto my-8 px-2 flex flex-col sm:flex-row items-center justify-between">
            <div>{navbarTitle}</div>
            <p className="text-base font-normal mt-5 sm:mt-0">
              {items?.length > 0 && (
                <Breadcrumbs className="" separator={<IoIosArrowForward color="gray" size={20} />}>
                  {items}
                </Breadcrumbs>
              )}
            </p>
          </div>
          {children}
          <br />
          <br />
          <br />
          <br />
          <br />
        </div>
      </div>
      {
        isChatBotOpen &&
      <Chatbot isChatBotOpen={isChatBotOpen} closeChatFunc={()=> setIsChatBotOpen(false)} />
      }
    </div>
  );
};

export default S2Layout;
