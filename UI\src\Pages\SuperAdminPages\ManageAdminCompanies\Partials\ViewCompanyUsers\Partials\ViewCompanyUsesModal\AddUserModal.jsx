import ApiProfile from "@/Api/apiProfileConfig";
import Loading from "@/Components/Loading";
import { Button, Modal, Select, TextInput } from "@mantine/core";
import { isNotEmpty, useForm } from "@mantine/form";
import { showNotification } from "@mantine/notifications";
import React, { useState } from "react";
import { IoIosArrowDown } from "react-icons/io";

export default function AddUserModel({
  close,
  opened,
  getAllCompanyAccounts,
  selectedCompanyId,
  closeModel,
}) {
  const [loading, setLoading] = useState(false);

  const form = useForm({
    mode: "uncontrolled",
    initialValues: {
      user_name: "",
      email: "",
      new_role: "",
    },
    validate: {
      user_name: isNotEmpty("User Name is required"),
      email: isNotEmpty("Email is required"),
      new_role: isNotEmpty("Role is required"),
    },
  });
  const add_new_user = async (value) => {
    const editValue = {
      company_id: selectedCompanyId,
      user_name: value.user_name,
      email: value.email,
      user_role:
        value.new_role === "User" ? 1 : value.new_role === "Admin" && 4,
    };
    console.log(editValue);

    try {
      setLoading(true);
      const { data } = await ApiProfile.post("/admin/add_user", editValue);
      showNotification({
        message: data.message,
        color: "green",
      });
      form.reset();
      setLoading(false);
      getAllCompanyAccounts();
      close();
      closeModel();
      console.log(data);
    } catch (error) {
      setLoading(false);
      error.response?.data.message &&
        showNotification({
          message: error.response?.data.message,
          color: "red",
        });
      error.response?.data.error &&
        showNotification({
          message: error.response?.data.error,
          color: "red",
        });
      console.log(error);
    }
  };
  return (
    <Modal opened={opened} onClose={close} title="Add New User" centered>
      <TextInput
        {...form.getInputProps("user_name")}
        key={form.key("user_name")}
        placeholder="Enter User Name ..."
        label={"User Name"}
      />
      <Select
        {...form.getInputProps("new_role")}
        key={form.key("new_role")}
        placeholder="Enter Position ..."
        label={"Role"}
        className="my-3"
        data={["User", "Admin"]}
        rightSection={<IoIosArrowDown />}
      />
      <TextInput
        {...form.getInputProps("email")}
        key={form.key("email")}
        placeholder="Enter Email ..."
        label={"Email"}
      />
      <Button
        className="bg-primary hover:bg-primary mt-5"
        onClick={!loading && form.onSubmit(add_new_user)}
      >
        {loading ? <Loading /> : "Submit"}
      </Button>
    </Modal>
  );
}
