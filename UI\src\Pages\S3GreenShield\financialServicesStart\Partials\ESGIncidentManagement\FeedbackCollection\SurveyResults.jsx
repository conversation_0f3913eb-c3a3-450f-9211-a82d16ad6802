import { useState, useEffect } from "react";

import start from "@/assets/images/start.svg";
import { Modal } from "@mantine/core";

import {
  Text,
  Title,
  Group,
  Badge,
  Progress,
  Paper,
  Box,
  Accordion,
  List,
  Skeleton,
  Card,
  Tabs,
  Container,
  SimpleGrid,
  Stack,
  Divider,
  Avatar,
  ThemeIcon,
  Center,
  Space,
  Alert,
} from "@mantine/core";
import { notifications } from "@mantine/notifications";
import {
  IconX,
  IconChartBar,
  IconList,
  IconUser,
  IconCalendar,
  IconAlertCircle,
  // IconCheckCircle,
  IconMoodSmile,
  IconReport,
  IconFileAnalytics,
  IconArrowUp,
  IconArrowDown,
  IconDots,
  IconSearch,
  IconFilter,
} from "@tabler/icons-react";
import ApiS3 from "@/Api/apiS3";
import { useParams } from "react-router";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
  Cell,
  PieChart,
  Pie,
  Legend,
  Sector,
  LineChart,
  Line,
  CartesianGrid,
  // TooltipProps,
} from "recharts";
import axios from "axios";
import Cookies from "js-cookie";
export default function SurveyResults() {
  const { id } = useParams();
  const [activeTab, setActiveTab] = useState("analytics");
  const [loading, setLoading] = useState(true);
  const [responses, setResponses] = useState([]);
  const [analytics, setAnalytics] = useState(null);
  const [surveyTitle, setSurveyTitle] = useState("");
  const [surveyInfo, setSurveyInfo] = useState(null);
  const [activeIndex, setActiveIndex] = useState(0);
  const [showPopup, setShowPopup] = useState(false);
  const measureTextWidth = (text, font = "12px Arial") => {
    const canvas = document.createElement("canvas");
    const context = canvas.getContext("2d");
    context.font = font;
    return context.measureText(text).width;
  };
  const [aiSummary, setAiSummary] = useState("");
  const wrapTextByWidth = (text, maxWidth, font = "12px Arial") => {
    const words = text.split(" ");
    const lines = [];
    let currentLine = "";

    for (let i = 0; i < words.length; i++) {
      const testLine = currentLine ? `${currentLine} ${words[i]}` : words[i];
      const testWidth = measureTextWidth(testLine, font);

      if (testWidth <= maxWidth) {
        currentLine = testLine;
      } else {
        if (currentLine) lines.push(currentLine);
        currentLine = words[i];
      }
    }

    if (currentLine) lines.push(currentLine);
    return lines;
  };

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const responseData = await ApiS3.get(`surveys/${id}/responses`);
        setResponses(responseData.data);

        if (responseData.data && responseData.data.length > 0) {
          setSurveyTitle(responseData.data[0].survey.title);
          setSurveyInfo({
            target: responseData.data[0].survey.target,
            createdAt: responseData.data[0].survey.createdAt,
            totalResponses: responseData.data.length,
          });
        }

        const analyticsData = await ApiS3.get(`analytics/${id}/completion`);
        setAnalytics(analyticsData.data);
      } catch (error) {
        console.error("Error fetching data:", error);
        notifications.show({
          title: "Error",
          message: "Failed to fetch survey data",
          color: "red",
          icon: <IconX />,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id]);

  const getAISummary = async (question, answers) => {
    try {
      // Validate inputs
      if (!answers || !Array.isArray(answers)) {
        throw new Error("Answers must be provided as an array");
      }

      // Make the API request
      const response = await axios.post(
        "https://gen-ai0-staging.azurewebsites.net/process_request",
        {
          processor: "survey",
          resources: {
            question: question,
            answers: answers,
            output_type: "shorten",
          },
        },
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("level_user_token")}`,
            "Content-Type": "application/json",
          },
          timeout: 10000, // 10 second timeout
        }
      );

      // Validate and process response
      if (!response.data?.ai_response?.overview) {
        throw new Error("Invalid response structure from AI service");
      }

      const summary = response.data.ai_response.overview;
      setAiSummary(summary);
      console.log("AI Summary:", summary);
      return summary;
    } catch (error) {
      console.error("Error getting AI summary:", error);

      // Handle specific error cases
      if (error.response) {
        // The request was made and the server responded with a status code
        console.error("Server responded with:", error.response.status);
        if (error.response.status === 401) {
          // Handle unauthorized error (token expired, etc.)
          // Maybe redirect to login or refresh token
        }
      } else if (error.request) {
        // The request was made but no response was received
        console.error("No response received from server");
      } else {
        // Something happened in setting up the request
        console.error("Request setup error:", error.message);
      }

      // Set a default or error summary
      setAiSummary(
        "Unable to generate summary at this time. Please try again later."
      );
      return null;
    }
  };

  const COLORS = ["#07838F"];

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <Paper
          shadow="md"
          p="xs"
          bg="white"
          style={{ border: "1px solid #f0f0f0" }}
        >
          <Text size="sm" weight={600}>
            {label}
          </Text>
          <Text size="sm" color={payload[0].color}>
            {`${payload[0].name}: ${payload[0].value} responses`}
          </Text>
        </Paper>
      );
    }
    return null;
  };

  // Pie chart active shape
  const renderActiveShape = (props) => {
    const {
      cx,
      cy,
      innerRadius,
      outerRadius,
      startAngle,
      endAngle,
      fill,
      payload,
      percent,
      value,
    } = props;

    return (
      <g>
        <text x={cx} y={cy} dy={-20} textAnchor="middle" fill="#333">
          {payload.option}
        </text>
        <text
          x={cx}
          y={cy}
          dy={8}
          textAnchor="middle"
          fill="#333"
          fontSize={16}
          fontWeight={600}
        >
          {value}
        </text>
        <text x={cx} y={cy} dy={30} textAnchor="middle" fill="#999">
          {`(${(percent * 100).toFixed(1)}%)`}
        </text>
        <Sector
          cx={cx}
          cy={cy}
          innerRadius={innerRadius}
          outerRadius={outerRadius + 5}
          startAngle={startAngle}
          endAngle={endAngle}
          fill={fill}
        />
        <Sector
          cx={cx}
          cy={cy}
          startAngle={startAngle}
          endAngle={endAngle}
          innerRadius={innerRadius - 3}
          outerRadius={innerRadius - 1}
          fill={fill}
        />
      </g>
    );
  };

  const renderCompletionStats = () => {
    if (!analytics) return null;

    const completionData = analytics.completionStats.completionDistribution.map(
      (item, index) => ({
        name: `Response #${index + 1}`,
        value: item.percentage,
        color:
          item.percentage >= 80
            ? "green"
            : item.percentage >= 50
            ? "blue"
            : "orange",
      })
    );

    return (
      <Card shadow="sm" padding="lg" radius="md" withBorder>
        <Group position="apart" mb="lg">
          <div>
            <Text size="xl" weight={600}>
              Completion Rate
            </Text>
            <Text size="sm" color="dimmed">
              Average completion across all responses
            </Text>
          </div>
          <ThemeIcon size={56} radius="xl" color="green" variant="light">
            <Text weight={700} size="xl">
              {analytics.completionStats.averageCompletion}%
            </Text>
          </ThemeIcon>
        </Group>

        <Progress
          value={analytics.completionStats.averageCompletion}
          color="green"
          size="xl"
          radius="md"
          mb="xl"
          animate
        />

        <Text weight={500} mb="md">
          Completion by Respondent
        </Text>
        <Box h={200}>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={completionData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis dataKey="name" />
              <YAxis domain={[0, 100]} tickFormatter={(value) => `${value}%`} />
              <Tooltip
                content={({ active, payload, label }) => {
                  if (active && payload && payload.length) {
                    return (
                      <Paper shadow="sm" p="xs" withBorder>
                        <Text size="sm">{label}</Text>
                        <Text weight={600} color={payload[0].payload.color}>
                          {payload[0].value}% completed
                        </Text>
                      </Paper>
                    );
                  }
                  return null;
                }}
              />
              <Line
                type="monotone"
                dataKey="value"
                stroke="#4DABF7"
                strokeWidth={2}
                dot={{ fill: "#4DABF7", r: 4 }}
                activeDot={{ r: 6, fill: "#1c7ed6" }}
              />
            </LineChart>
          </ResponsiveContainer>
        </Box>
      </Card>
    );
  };

  const renderQuestionAnalysis = (question) => {
    if (question.type === "text") {
      return (
        <Stack spacing="sm">
          <div className="flex justify-end mb-4">
            <button
              onClick={() => {
                getAISummary(question.questionText, question.textResponses);
                setShowPopup(true);
              }}
              className="flex items-center  bg-[#cfe8e6] text-[#1C889C] text-base font-medium px-4 py-2 rounded-lg shadow-sm hover:opacity-90 transition"
            >
              <img src={start} alt="icon" className="w-5 h-5" />

              <span className="ms-2">Generate AI Summary</span>
            </button>
          </div>

          <Text size="sm" weight={600} mt="md">
            Text Responses ({question.textResponses.length})
          </Text>

          {question.textResponses.length > 0 ? (
            <Paper
              p="md"
              radius="md"
              withBorder
              style={{ maxHeight: 250, overflow: "auto" }}
            >
              <List spacing="sm">
                {question.textResponses.map((response, idx) => (
                  <List.Item
                    key={idx}
                    icon={
                      <ThemeIcon
                        size={24}
                        radius="xl"
                        color="blue"
                        variant="light"
                      >
                        {idx + 1}
                      </ThemeIcon>
                    }
                  >
                    {response || (
                      <Text italic color="dimmed">
                        No response provided
                      </Text>
                    )}
                  </List.Item>
                ))}
              </List>
            </Paper>
          ) : (
            <Center p="md">
              <Text color="dimmed">No text responses available</Text>
            </Center>
          )}
        </Stack>
      );
    } else {
      // For multiple choice questions, show both bar chart and pie chart for better visualization
      return (
        <SimpleGrid
          // cols={question.options.length <= 2 ? 1 : 2}
          breakpoints={[{ maxWidth: "md", cols: 1 }]}
          spacing="lg"
        >
          <Box mt="md" h={400}>
            <Text size="sm" weight={600} mb="md">
              Bar Chart View
            </Text>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={question.options}
                margin={{ top: 5, right: 30, left: 20, bottom: 50 }}
                barSize={50}
              >
                <CartesianGrid strokeDasharray="33" stroke="#f0f0f0" />
                <XAxis
                  dataKey="option"
                  interval={0}
                  height={90}
                  tick={({ x, y, payload }) => {
                    const maxLabelWidth = 100;
                    const lines = wrapTextByWidth(payload.value, maxLabelWidth);

                    return (
                      <g transform={`translate(${x},${y + 10})`}>
                        <text textAnchor="middle" fill="#333" fontSize={12}>
                          {lines.map((line, index) => (
                            <tspan key={index} x="0" dy={index === 0 ? 0 : 16}>
                              {line}
                            </tspan>
                          ))}
                        </text>
                      </g>
                    );
                  }}
                />

                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Bar dataKey="count" name="Responses" animationDuration={1000}>
                  {question.options.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={COLORS[index % COLORS.length]}
                    />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </Box>

          {/* {question.options.length > 1 && (
            <Box mt="md" h={300}>
              <Text size="sm" weight={600} mb="md">
                Pie Chart View
              </Text>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    activeIndex={activeIndex}
                    activeShape={renderActiveShape}
                    data={question.options}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={90}
                    fill="#8884d8"
                    dataKey="count"
                    onMouseEnter={(_, index) => setActiveIndex(index)}
                    animationDuration={800}
                  >
                    {question.options.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[index % COLORS.length]}
                      />
                    ))}
                  </Pie>
                  <Legend
                    layout="horizontal"
                    verticalAlign="bottom"
                    align="center"
                  />
                  <Tooltip
                    formatter={(value) => [`${value} responses`, "Count"]}
                  />
                </PieChart>
              </ResponsiveContainer>
            </Box>
          )} */}
        </SimpleGrid>
      );
    }
  };

  const renderAnalytics = () => {
    if (loading) {
      return (
        <Stack spacing="xl">
          <Skeleton height={40} width="70%" mb="md" />
          <Skeleton height={250} radius="md" mb="lg" />
          <Skeleton height={30} mb="md" />
          <Skeleton height={250} radius="md" mb="lg" />
          <Skeleton height={30} mb="md" />
          <Skeleton height={250} radius="md" />
        </Stack>
      );
    }

    if (!analytics) {
      return (
        <Alert
          icon={<IconAlertCircle size={24} />}
          title="No data available"
          color="blue"
          variant="filled"
        >
          No analytics are available for this survey yet. Check back when there
          are more responses.
        </Alert>
      );
    }

    return (
      <Stack spacing="xl">
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Group position="apart" mb="md">
            <div>
              <Title order={3}>Survey Overview</Title>
              <Text size="sm" color="dimmed">
                Key metrics for {surveyTitle}
              </Text>
            </div>
            <Badge size="xl" color="blue" variant="filled">
              {responses.length} Responses
            </Badge>
          </Group>

          <SimpleGrid cols={3} breakpoints={[{ maxWidth: "sm", cols: 1 }]}>
            <Paper withBorder p="md" radius="md">
              <Group position="apart" mb="xs">
                <Text size="xs" color="dimmed" weight={500}>
                  COMPLETION RATE
                </Text>
                <Badge
                  size="sm"
                  color={
                    analytics.completionStats.averageCompletion >= 80
                      ? "green"
                      : "blue"
                  }
                >
                  {analytics.completionStats.averageCompletion}%
                </Badge>
              </Group>
              <Progress
                value={analytics.completionStats.averageCompletion}
                color={
                  analytics.completionStats.averageCompletion >= 80
                    ? "green"
                    : "blue"
                }
                size="sm"
              />
            </Paper>

            <Paper withBorder p="md" radius="md">
              <Group position="apart">
                <div>
                  <Text size="xs" color="dimmed" weight={500}>
                    TARGET AUDIENCE
                  </Text>
                  <Text size="lg" weight={500}>
                    {surveyInfo?.target || "General"}
                  </Text>
                </div>
                <ThemeIcon size={38} radius="md" variant="light" color="grape">
                  <IconUser size={20} />
                </ThemeIcon>
              </Group>
            </Paper>

            <Paper withBorder p="md" radius="md">
              <Group position="apart">
                <div>
                  <Text size="xs" color="dimmed" weight={500}>
                    QUESTIONS ANALYZED
                  </Text>
                  <Text size="lg" weight={500}>
                    {analytics.questionWiseAnalysis.length}
                  </Text>
                </div>
                <ThemeIcon size={38} radius="md" variant="light" color="orange">
                  <IconFileAnalytics size={20} />
                </ThemeIcon>
              </Group>
            </Paper>
          </SimpleGrid>
        </Card>

        {renderCompletionStats()}
        <div className="mt-6 flex items-center justify-between">
          <Title order={3} mt="lg">
            Question Analysis
          </Title>
        </div>
        <Modal
          opened={showPopup}
          onClose={() => setShowPopup(false)}
          title=" Generate AI Summary"
          size="lg"
          centered
        >
          <div className=" bg-[#cfe8e6]  rounded-md">
            <Text>{aiSummary}</Text>
          </div>
        </Modal>

        <Text color="dimmed" mb="md">
          Detailed breakdown of responses by question
        </Text>

        {analytics.questionWiseAnalysis.map((question, index) => (
          <Card
            key={question.questionId}
            shadow="sm"
            padding="lg"
            radius="md"
            withBorder
            mb="lg"
          >
            <Group position="apart" noWrap align="flex-start">
              <div>
                <Group spacing="xs" mb="xs">
                  <ThemeIcon size={28} radius="xl" color="blue" variant="light">
                    {index + 1}
                  </ThemeIcon>
                  <Text size="lg" weight={600}>
                    {question.questionText}
                  </Text>
                </Group>

                {/* <Group mb="md" mt="xs">
                  <Badge
                    color={question.required ? "red" : "blue"}
                    variant="filled"
                  >
                    {question.required ? "Required" : "Optional"}
                  </Badge>
                  <Badge color="gray">
                    {question.type === "text"
                      ? "Text Input"
                      : "Multiple Choice"}
                  </Badge>
                  <Badge color="cyan">
                    {question.responseCount}{" "}
                    {question.responseCount === 1 ? "Response" : "Responses"}
                  </Badge>
                </Group> */}
              </div>
            </Group>

            <Divider my="md" />

            {renderQuestionAnalysis(question)}
          </Card>
        ))}
      </Stack>
    );
  };

  const renderResponseCards = () => {
    if (loading) {
      return (
        <Stack spacing="md">
          {Array(3)
            .fill(0)
            .map((_, index) => (
              <Card key={index} shadow="sm" padding="lg" radius="md" withBorder>
                <Skeleton height={30} width="70%" mb="md" />
                <Group mb="md">
                  <Skeleton circle height={40} />
                  <div style={{ flex: 1 }}>
                    <Skeleton height={10} width="40%" mb={6} />
                    <Skeleton height={10} width="60%" />
                  </div>
                </Group>
                <Skeleton height={15} width="90%" mb="sm" />
                <Skeleton height={15} width="80%" mb="sm" />
                <Skeleton height={15} width="85%" mb="sm" />
              </Card>
            ))}
        </Stack>
      );
    }

    if (!responses || responses.length === 0) {
      return (
        <Alert
          icon={<IconAlertCircle size={24} />}
          title="No responses yet"
          color="blue"
          variant="filled"
        >
          This survey hasn't received any responses yet. Check back later or
          share the survey with more participants.
        </Alert>
      );
    }

    return (
      <Stack spacing="md">
        <Group position="apart" mb="md">
          <Text size="xl" weight={600}>
            {responses.length}{" "}
            {responses.length === 1 ? "Response" : "Responses"}
          </Text>
          <Group>
            <Badge
              // leftSection={<IconCheckCircle size={12} />}
              color="green"
              variant="filled"
            >
              {analytics?.completionStats.averageCompletion || 0}% Avg.
              Completion
            </Badge>
          </Group>
        </Group>

        {responses.map((response, index) => (
          <Card
            key={response._id}
            shadow="sm"
            padding="lg"
            radius="md"
            withBorder
          >
            <Group position="apart" mb="lg">
              <Group>
                <Avatar
                  size="md"
                  radius="xl"
                  color={COLORS[index % COLORS.length].toLowerCase()}
                >
                  {response.whoResponse
                    ? response.whoResponse.charAt(0).toUpperCase()
                    : "R"}
                </Avatar>
                <div>
                  <Text weight={600}>
                    {response.whoResponse || `Respondent #${index + 1}`}
                  </Text>
                  <Group spacing="xs">
                    <IconCalendar size={14} />
                    <Text size="xs" color="dimmed">
                      {new Date(response.createdAt).toLocaleDateString()} at{" "}
                      {new Date(response.createdAt).toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </Text>
                  </Group>
                </div>
              </Group>
              <Badge color="blue" variant="filled">
                {response.survey.target || "General"}
              </Badge>
            </Group>

            <Accordion variant="contained" radius="md">
              {response.answers.map((answer) => (
                <Accordion.Item key={answer._id} value={answer._id}>
                  <Accordion.Control>
                    <Group position="apart">
                      <Text weight={500}>{answer.question.text}</Text>
                      <Badge
                        size="sm"
                        color={answer.response ? "green" : "gray"}
                        variant={answer.response ? "filled" : "outline"}
                      >
                        {answer.response ? "Answered" : "No answer"}
                      </Badge>
                    </Group>
                  </Accordion.Control>
                  <Accordion.Panel>
                    <Group mb="xs">
                      <Badge
                        color={answer.question.required ? "red" : "blue"}
                        size="sm"
                      >
                        {answer.question.required ? "Required" : "Optional"}
                      </Badge>
                      <Badge color="gray" size="sm">
                        {answer.question.type}
                      </Badge>
                    </Group>
                    <Paper p="sm" radius="md" bg="gray.0">
                      {answer.response ? (
                        <Text>{answer.response}</Text>
                      ) : (
                        <Text italic color="dimmed">
                          No response provided
                        </Text>
                      )}
                    </Paper>
                  </Accordion.Panel>
                </Accordion.Item>
              ))}
            </Accordion>
          </Card>
        ))}
      </Stack>
    );
  };

  return (
    <Container size="xl">
      <Paper shadow="xs" p="md" withBorder mb="xl">
        <Group position="apart">
          <div>
            <Title order={2}>
              {loading ? (
                <Skeleton height={36} width={200} />
              ) : (
                surveyTitle || "Survey Results"
              )}
            </Title>
            {!loading && surveyInfo && (
              <Text color="dimmed" size="sm">
                Created on {new Date(surveyInfo.createdAt).toLocaleDateString()}
              </Text>
            )}
          </div>

          <Tabs value={activeTab} onChange={setActiveTab}>
            <Tabs.List>
              <Tabs.Tab value="analytics" icon={<IconChartBar size={16} />}>
                Analytics
              </Tabs.Tab>
              <Tabs.Tab value="responses" icon={<IconList size={16} />}>
                Responses
              </Tabs.Tab>
            </Tabs.List>
          </Tabs>
        </Group>
      </Paper>

      {activeTab === "responses" ? renderResponseCards() : renderAnalytics()}

      <Space h="xl" />
    </Container>
  );
}
