import { useState, useEffect } from "react";
import {
 Chart as ChartJS,
 LineElement,
 PointElement,
 Tooltip,
 Legend,
 RadialLinearScale,
 Filler,
} from "chart.js";
import { Radar } from "react-chartjs-2";
import PropTypes from "prop-types";

// Register the components needed by ChartJS
ChartJS.register(
 LineElement,
 PointElement,
 Tooltip,
 Legend,
 RadialLinearScale,
 Filler
);

const RadarChart = ({ labels, label, data, backgroundColor, borderColor }) => {
 const [chartData, setChartData] = useState({
  labels: labels,
  datasets: [
   {
    label: label,
    data: data,
    backgroundColor: backgroundColor || "rgba(75, 192, 192, 0.2)",
    borderColor: borderColor || "rgba(75, 192, 192, 1)",
    borderWidth: 2,
   },
  ],
 });

 const options = {
  scales: {
   r: {
    angleLines: {
     display: false,
    },
    ticks: {
     stepSize: 20,
     max: 100,
     font: {
      size: 10, // Adjust this value to reduce the font size
     },
     backdropColor: "rgba(0, 0, 0, 0)",
    },
    suggestedMin: 0,
    suggestedMax: 100,
    pointLabels: {
     font: {
      size: 10,
      weight: "bold",
     },
     color: "black",
     padding: 0,
     callback: function (label) {
      const words = label.split(" ");
      const lines = [];
      let currentLine = "";

      words.forEach((word) => {
       if ((currentLine + word).length < 20) {
        currentLine += word + " ";
       } else {
        lines.push(currentLine.trim());
        currentLine = word + " ";
       }
      });

      lines.push(currentLine.trim());

      return lines;
     },
    },
   },
  },
 };

 useEffect(() => {
  // Additional useEffect to update the chart data if props change
  setChartData({
   labels: labels,
   datasets: [
    {
     label: label,
     data: data,
     backgroundColor: backgroundColor || "rgba(75, 192, 192, 0.2)",
     borderColor: borderColor || "rgba(75, 192, 192, 1)",
     borderWidth: 2,
    },
   ],
  });
 }, [labels, label, data, backgroundColor, borderColor]);

 return (
  <div className="w-[250px] lg:w-[400px] h-[250px] lg:h-[400px] mx-auto">
   <Radar data={chartData} options={options} />
  </div>
 );
};

RadarChart.propTypes = {
 labels: PropTypes.array.isRequired,
 label: PropTypes.string.isRequired,
 data: PropTypes.array.isRequired,
 backgroundColor: PropTypes.string,
 borderColor: PropTypes.string,
};

export default RadarChart;
