//does not blank the screen

import { useState, useEffect } from "react";
import { Button, TextInput, Select, MultiSelect } from "@mantine/core";
import ApiS2 from "@/Api/apiS2Config";
import { showNotification } from "@mantine/notifications";
import { useTranslation } from "react-i18next";
import { validateField } from "./AssetsStaticData";
import { IoIosArrowDown } from "react-icons/io";
import Loading from "@/Components/Loading";
import countries from "world-countries";
import { driver } from "driver.js";
import "driver.js/dist/driver.css";
import "@/assets/css/index.css";
import { FaQuestionCircle } from "react-icons/fa";

const prioritized = [
  "United Kingdom",
  "United Arab Emirates",
  "United States",
  "Saudi Arabia",
  "Qatar",
];

// Sort countries: prioritized first, then the rest
const sortedCountries = [
  ...countries.filter((c) => prioritized.includes(c.name.common)),
  ...countries
    .filter((c) => !prioritized.includes(c.name.common))
    .sort((a, b) => a.name.common.localeCompare(b.name.common)),
];

const AssetsForm = ({ fetchAgain, assetTypeDrop, assetTypeAll }) => {
  const [loading, setLoading] = useState({});
  const [departmentProjects, setDepartmentProjects] = useState([]);
  const [departmentProjectsData, setDepartmentProjectsData] = useState([]); // Store full data
  const [loadingDepartments, setLoadingDepartments] = useState(true);
  const [data, setData] = useState([
    {
      id: Date.now().toString(), // Assign a unique ID using Date.now()
      assetType: "",
      assetName: "",
      siteName: "",
      location: "",
      projectIds: [], // Changed to array to store multiple IDs
      reference: "",
      notes: "",
      isNew: true,
      isEditing: true, // Allow editing for new rows
    },
  ]);
  const [errors, setErrors] = useState({});

  const { t } = useTranslation();

  // Fetch departments and projects
  const fetchDepartmentProjects = async () => {
    setLoadingDepartments(true);
    try {
      const response = await ApiS2.get(
        'https://portal-auth-main-staging.azurewebsites.net/departments-and-projects'
      );

      let data;
      if (response.data) {
        data = response.data; 
      } else {
        data = response; 
      }
      
      if (data && data.departmentsAndProjects && Array.isArray(data.departmentsAndProjects)) {
        // Store full data for ID mapping
        setDepartmentProjectsData(data.departmentsAndProjects);
        
        // Create dropdown data with value as ID and label as name
        const dropdownData = data.departmentsAndProjects
          .filter(item => item.name && item.name.trim() !== '')
          .map(item => ({
            value: item.id.toString(), // Use ID as value
            label: item.name // Use name as label
          }));
        
        setDepartmentProjects(dropdownData);
      } else {
        console.warn("Unexpected data structure:", data);
        setDepartmentProjects([]);
        setDepartmentProjectsData([]);
      }
      
    } catch (error) {
      console.error("Fetch departments error:", error);
      msg(error.message || "Failed to load departments and projects", "red");
      setDepartmentProjects([]);
      setDepartmentProjectsData([]);
    } finally {
      setLoadingDepartments(false);
    }
  };

  // Load departments on component mount
  useEffect(() => {
    fetchDepartmentProjects();
  }, []);

  const getGuideSteps = () => [
    {
      element: ".multi-select-department-project",
      popover: {
        title: t("Select one or more departments or projects for this asset."),
        description: t("Choose from the available departments and projects in your organization. You can select multiple options."),
        side: "bottom",
        align: "center",
      },
    },
    {
      element: ".multi-select-emission",
      popover: {
        title: t(
          "Each asset can have one or multiple emission sources associated with it."
        ),
        description: t(
          "Dropdown to pick the emission source (e.g., Refrigerants, Purchased Electricity, etc.)"
        ),
        side: "bottom",
        align: "center",
      },
    },
    {
      element: ".input-site-name",
      popover: {
        title: t(
          "Enter the name of the site where the asset is located (e.g., Headquarters Building)."
        ),
        side: "bottom",
        align: "center",
      },
    },
    {
      element: ".input-asset-name",
      popover: {
        title: t(
          "Enter the unique name or identifier for the asset (e.g., Diesel Generator)."
        ),
        side: "bottom",
        align: "center",
      },
    },
    {
      element: ".select-country",
      popover: {
        title: t("Dropdown to select the country of the asset."),
        side: "bottom",
        align: "center",
      },
    },
    {
      element: ".input-reference",
      popover: {
        title: t(
          "Optional text to add a reference code or identifier for the asset, such as a serial number or internal code (e.g., DG-001)."
        ),
        side: "top",
        align: "center",
      },
    },
    {
      element: ".input-notes",
      popover: {
        title: t(
          "Optional text box for any additional information or comments about the asset (e.g., Backup generator used during"
        ),
        side: "top",
        align: "center",
      },
    },
    {
      element: ".button-add-more",
      popover: {
        title: t("Allow you to add more the one asset at the same time."),
        side: "bottom",
        align: "center",
      },
    },
    {
      element: ".button-save",
      popover: {
        title: t("to save the asset and add it to your assets list."),
        side: "top",
        align: "center",
      },
    },
  ];

  const startFormGuide = () => {
    const driverFormObj = driver({
      showProgress: true,
      popoverClass: "my-custom-popover-class",
      nextBtnText: "Next",
      prevBtnText: "Back",
      doneBtnText: "Done",
      overlayColor: "rgba(0, 0, 0, 0)",
      progressText: "Step {{current}} of {{total}}",
      steps: getGuideSteps(),
      onDestroyStarted: () => {
        localStorage.setItem("hasSeenFormGuide", "true");
        driverFormObj.destroy();
      },
    });
    driverFormObj.drive();
  };

  useEffect(() => {
    const hasSeenGuide = localStorage.getItem("hasSeenFormGuide");
    if (!hasSeenGuide) {
      startFormGuide();
    }
    return () => {
      const driverFormObj = driver();
      if (driverFormObj.isActive()) {
        driverFormObj.destroy();
      }
    };
  }, []);

  const msg = (txt, color = "green") => {
    showNotification({ message: txt, color: color });
  };

  const handleCustomAssetsTable = async (item) => {
    let { assetName, location, assetType, siteName, projectIds } = item;

    if (
      assetName == "" ||
      location == "" ||
      assetType.length == 0 ||
      siteName === "" ||
      projectIds.length === 0 // Changed to check array length
    ) {
      msg(
        `You have to fill all row inputs ${
          assetType.length == 0 ? `(Emission Source)` : ""
        } ${!assetName ? "(Asset Name)" : ""} ${!location ? "(Country)" : ""} ${
          !assetType ? "(Emission Source)" : ""
        } ${!siteName ? "(Site Name)" : ""} ${projectIds.length === 0 ? "(Department/Project)" : ""}`,
        "red"
      );
      return;
    }
    const sendedData = {
      assetName,
      location,
      reference: item.reference,
      notes: item.notes,
      siteName,
      projectIds, // This now contains array of IDs
      assetTypeIds: assetType,
    };

    toggleLoading(item.id, true);
    try {
      const { data } = await ApiS2.post("/admin/create-company-assets", [
        sendedData,
      ]);
      if (data) {
        fetchAgain();
        toggleLoading(item.id, false);
      }
      msg("Asset Added Successfully");
    } catch (error) {
      console.log(error);
      toggleLoading(item.id, false);
      msg(error.response.data.message, "red");
      error?.response?.data?.duplicate_assets?.map((item, idx) => {
        msg(`${item.message}`, "red");
      });
      error?.response?.data?.validation_errors?.map((item, idx) => {
        msg(` ${item.message}`, "red");
      });
      error?.response?.data?.created_assets?.map((item, idx) => {
        msg(` ${item.message}`, "red");
      });
    }
  };

  const addNewRow = () => {
    setData([
      ...data,
      {
        id: Date.now().toString(), // Assign a unique ID using Date.now()
        assetType: "",
        assetName: "",
        siteName: "",
        location: "",
        projectIds: [], // Initialize as empty array
        reference: "",
        notes: "",
        isNew: true,
        isEditing: true, // Allow editing for new rows
      },
    ]);
  };

  const deleteSelectedRows = (itemId) => {
    setData(data.filter((item) => item.id !== itemId));
  };

  const handleInputChange = (field, value, item) => {
    const error = validateField(field, value);

    // Update errors state
    setErrors((prev) => ({
      ...prev,
      [item.id]: {
        ...prev[item.id],
        [field]: error,
      },
    }));
    const updatedData = data.map((dataItem) =>
      dataItem.id === item.id ? { ...dataItem, [field]: value } : dataItem
    );
    setData(updatedData); // Assuming you have a setData function from useState
  };

  // New handler for department/project selection
  const handleDepartmentProjectChange = (selectedValues, item) => {
    // Convert string values back to numbers for projectIds
    const projectIds = selectedValues.map(val => parseInt(val));
    
    const updatedData = data.map((dataItem) =>
      dataItem.id === item.id ? { ...dataItem, projectIds: projectIds } : dataItem
    );
    setData(updatedData);
  };

  const handleEmissionSource = (field, value, item) => {
    const assetsItems = assetTypeAll.filter((el) => value.includes(el.asset));
    const assetsIds = assetsItems.map((el) => el.id);
    const updatedData = data.map((dataItem) =>
      dataItem.id === item.id ? { ...dataItem, [field]: assetsIds } : dataItem
    );
    setData(updatedData); // Assuming you have a setData function from useState
  };

  const toggleLoading = (id, value) => {
    setLoading((prevState) => ({
      ...prevState,
      [id]: value,
    }));
  };

  const formCards = data?.map((item) => {
    const isEditable = item.isEditing;
    const itemErrors = errors[item.id] || {};

    return (
      <div className="border-b-2 py-3 mb-5 border-gray-400" key={item.id}>
        <div className="grid lg:grid-cols-2 gap-4 ">
          <div>
            <MultiSelect
              label="Select Department/Project"
              placeholder={loadingDepartments ? "Loading departments..." : "Select Department/Project ..."}
              data={departmentProjects}
              value={item.projectIds.map(id => id.toString())} // Convert back to strings for display
              onChange={(selectedValues) => handleDepartmentProjectChange(selectedValues, item)}
              disabled={!isEditable || loadingDepartments}
              searchable
              clearable
              className="multi-select-department-project"
            />
            {itemErrors.projectIds && (
              <span className="text-red-500 text-xs mt-1">
                {itemErrors.projectIds}
              </span>
            )}
          </div>
          <div>
            <MultiSelect
              label="Select Emission"
              placeholder="Pick Emission Source ..."
              data={assetTypeDrop}
              onChange={(e) => handleEmissionSource("assetType", e, item)}
              className="multi-select-emission"
            />
            {itemErrors.assetType && (
              <span className="text-red-500 text-xs mt-1">
                {itemErrors.assetType}
              </span>
            )}
          </div>

          <div>
            <TextInput
              label="Site Name"
              onChange={(e) =>
                handleInputChange("siteName", e.target.value, item)
              }
              placeholder="Enter Site Name ..."
              disabled={!isEditable}
              className="input-site-name"
            />
            {itemErrors.siteName && (
              <span className="text-red-500 text-xs mt-1">
                {itemErrors.siteName}
              </span>
            )}
          </div>

          <div>
            <TextInput
              label="Asset Name"
              onChange={(e) =>
                handleInputChange("assetName", e.target.value, item)
              }
              placeholder="Enter Asset Name ..."
              disabled={!isEditable}
              className="input-asset-name"
            />
            {itemErrors.assetName && (
              <span className="text-red-500 text-xs mt-1">
                {itemErrors.assetName}
              </span>
            )}
          </div>

          <div>
            <Select
              label="Select Country"
              id="Country"
              name="Country"
              comboboxProps={{ withinPortal: true }}
              data={sortedCountries.map((country) => country.name.common)}
              placeholder="Select Country ..."
              searchable
              rightSection={!item?.location && <IoIosArrowDown />}
              value={item?.location}
              onChange={(e) => handleInputChange("location", e, item)}
              disabled={!isEditable}
              clearable
              className="select-country"
            />
            {itemErrors.location && (
              <span className="text-red-500 text-xs mt-1">
                {itemErrors.location}
              </span>
            )}
          </div>

          <div>
            <TextInput
              label="Reference"
              onChange={(e) =>
                handleInputChange("reference", e.target.value, item)
              }
              placeholder="Enter Asset Reference ..."
              disabled={!isEditable}
              className="input-reference"
            />
            {itemErrors.reference && (
              <span className="text-red-500 text-xs mt-1">
                {itemErrors.reference}
              </span>
            )}
          </div>

          <div>
            <TextInput
              label="Notes"
              onChange={(e) => handleInputChange("notes", e.target.value, item)}
              placeholder="Enter Additional Notes ..."
              disabled={!isEditable}
              className="input-notes"
            />
          </div>
        </div>

        <div className="grid lg:grid-cols-2 gap-3 mt-3">
          <Button
            variant="outline"
            // disabled={loading[item?.id]}
            className={`font-bold text-xs border-gray-200
              px-5 `}
            onClick={() => deleteSelectedRows(item.id)}
            type="button"
          >
            Cancel
          </Button>
          <Button
            // disabled={loading[item?.id]}
            className={`button-save font-bold text-xs bg-primary text-white hover:bg-primary
             hover:text-white px-5 `}
            onClick={() => handleCustomAssetsTable(item)}
            type="submit"
          >
            {loading[item?.id] ? <Loading /> : "Save"}
          </Button>
        </div>
      </div>
    );
  });

  return (
    <div>
      <div
        onClick={startFormGuide}
        style={{
          position: "fixed",
          top: 80,
          right: 35,
          cursor: "pointer",
          zIndex: 1000,
        }}
      >
        <div className="h-12 w-12 flex items-center justify-center bg-gradient-to-tr from-[#2C5A8C] via-[#1C889C] to-[#13B1A8] p-1  cursor-pointer rounded-full">
          <FaQuestionCircle
            size={34}
            color="#ffffff"
            className="mx-auto cursor-pointer"
          />
        </div>
      </div>

      <div className="flex justify-end mt-4">
        <Button
          onClick={addNewRow}
          className="button-add-more bg-teal-500 hover:bg-teal-600 text-white font-medium py-2 px-6 rounded-lg transition-colors"
        >
          Add More
        </Button>
      </div>
      <div>{formCards}</div>
    </div>
  );
};

export default AssetsForm;