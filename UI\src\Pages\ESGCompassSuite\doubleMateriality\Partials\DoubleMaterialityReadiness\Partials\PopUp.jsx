import { Image, Modal } from '@mantine/core'
import React from 'react'
import report from '@/assets/svg/Guide on Assessment Scale and Score System 1.svg'
export default function DoubleMaterialityPopUp({opened ,close}) {
    return (
        <>
            <Modal
                opened={opened}
                onClose={close}
                centered
                size="100rem"
                // fullScreen
                className=""
                transitionProps={{ transition: "fade", duration: 800 }}
                // withCloseButton={false}
            >
                <Image src={report} className='w-full'/>
            </Modal>
        </>
    )
}
