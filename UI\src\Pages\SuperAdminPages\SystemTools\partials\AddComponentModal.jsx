import ApiProfile from "@/Api/apiProfileConfig";
import Loading from "@/Components/Loading";
import { Button, Modal, TextInput } from "@mantine/core";
import { isNotEmpty, useForm } from "@mantine/form";
import { showNotification } from "@mantine/notifications";
import { useState, useEffect, useCallback } from "react";

const AddComponentModal = ({ close, opened, groupId, groupName, parentComponentId, parentComponentName, refreshGroups }) => {
  const [loading, setLoading] = useState(false);
  const [componentData, setComponentData] = useState(null);
  const [parentLevel, setParentLevel] = useState(null);
  
  // Create the form with correct initial values
  const form = useForm({
    initialValues: {
      systemComponentName: "",
      componentGroupId: "",
      parentComponentId: null,
    },
    validate: {
      systemComponentName: isNotEmpty("Component Name is required"),
    },
  });

  // Function to recursively find a component by ID and get its level
  const findComponentLevel = useCallback((components, targetId) => {
    if (!components || !components.length) return null;
    
    for (const component of components) {
      if (component.systemComponentId === parseInt(targetId)) {
        return component.level;
      }
      
      // Check children recursively
      if (component.children && component.children.length > 0) {
        const foundLevel = findComponentLevel(component.children, targetId);
        if (foundLevel !== null) return foundLevel;
      }
    }
    return null;
  }, []);

  // Fetch component data and find parent level if needed
  const fetchComponentData = useCallback(async () => {
    if (!opened) return;
    
    try {
      setLoading(true);
      const { data } = await ApiProfile.get("https://portal-auth-main.azurewebsites.net/admin/system-components");
      setComponentData(data);
      
      // Find parent component level if this is a subcomponent
      if (parentComponentId && data && data.grouped_components) {
        let foundLevel = null;
        
        // Search for parent component in all groups
        for (const groupName in data.grouped_components) {
          const group = data.grouped_components[groupName];
          const level = findComponentLevel(group.components, parentComponentId);
          
          if (level !== null) {
            foundLevel = level;
            break;
          }
        }
        
        // If found, set the parent level
        if (foundLevel !== null) {
          setParentLevel(foundLevel);
        } else {
          // Default to level 1 if parent not found
          setParentLevel(1);
        }
      } else {
        // Not a subcomponent, no parent level needed
        setParentLevel(null);
      }
      
      setLoading(false);
    } catch (error) {
      console.error("Error fetching component data:", error);
      setLoading(false);
    }
  }, [opened, parentComponentId, findComponentLevel]);
  
  // Initialize form values when modal opens or props change
  useEffect(() => {
    if (opened) {
      // Set initial form values
      form.setValues({
        systemComponentName: "",
        componentGroupId: groupId ? groupId.toString() : "",
        parentComponentId: parentComponentId ? parentComponentId.toString() : null,
      });
      
      // Fetch component data and get parent level
      fetchComponentData();
    } else {
      // Reset form when modal closes
      form.reset();
      setParentLevel(null);
    }
  }, [opened, groupId, parentComponentId, fetchComponentData]);

  const handleSubmit = async (values) => {
    try {
      setLoading(true);
      
      // Create the submit data object
      const submitData = {
        componentGroupId: parseInt(values.componentGroupId),
        systemComponentName: values.systemComponentName
      };
      
      // Only include componentLevel and componentParentId for subcomponents
      if (parentComponentId) {
        submitData.componentParentId = parseInt(values.parentComponentId);
        
        // Calculate child level based on parent level
        if (parentLevel !== null) {
          submitData.componentLevel = parentLevel + 1;
        }
      }
      
      console.log("Submitting with data:", submitData);
      
      const { data } = await ApiProfile.post(
        "https://portal-auth-main.azurewebsites.net/admin/system-components",
        submitData
      );
      
      showNotification({
        message: data.message || (parentComponentId ? "Subcomponent added successfully" : "Component added successfully"),
        color: "green",
      });
      
      refreshGroups();
      close();
    } catch (error) {
      console.error("Error adding component:", error);
      error.response?.data.message &&
        showNotification({
          message: error.response?.data.message,
          color: "red",
        });
      error.response?.data.error &&
        showNotification({
          message: error.response?.data.error,
          color: "red",
        });
    } finally {
      setLoading(false);
    }
  };

  const isSubcomponent = parentComponentId !== null;
  const modalTitle = isSubcomponent 
    ? `Add New Subcomponent to ${parentComponentName}` 
    : `Add New Component to ${groupName}`;

  // Calculate the new level for display (parent level + 1)
  // const newComponentLevel = parentLevel !== null ? parentLevel + 1 : 1;

  return (
    <Modal 
      opened={opened} 
      onClose={close} 
      title={modalTitle} 
      centered
    >
      <form onSubmit={form.onSubmit(handleSubmit)}>
        {isSubcomponent && (
          <p className="mb-3 text-sm text-gray-600">
            Adding subcomponent to {parentComponentName} 
            {/* (Level {newComponentLevel}) */}
          </p>
        )}
        <TextInput
          placeholder={isSubcomponent ? "Enter Subcomponent Name..." : "Enter Component Name..."}
          label={isSubcomponent ? "Subcomponent Name" : "Component Name"}
          className="mb-4"
          required
          {...form.getInputProps('systemComponentName')}
        />
        <Button
          type="submit"
          className="bg-primary hover:bg-primary mt-3 w-full"
          disabled={loading}
        >
          {loading ? <Loading /> : "Submit"}
        </Button>
      </form>
    </Modal>
  );
};

export default AddComponentModal;