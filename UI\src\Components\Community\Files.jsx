import Modal from '../Modal';
import PdfViewer from '@/Components/PdfViewer';
import axios from 'axios';
import { useEffect, useRef, useState } from 'react';
import { BsFileEarmarkText } from 'react-icons/bs';

const Files = ({ attachments, className, hideSize, flex }) => {
  const [filesInfo, setFilesInfo] = useState([]);
  const modalRef = useRef();

  let classes =
    'bg-white p-2 text-lg flex items-center gap-4 rounded hover:bg-transparent duration-300 border border-transparent hover:border-primary cursor-pointer my-2';
  if (className) classes = className;

  async function getFileInfo(fileUrl) {
    if (!fileUrl || fileUrl.includes('[object File]')) return null;

    let fileName = decodeURIComponent(fileUrl.split('/').pop().split('?')[0]);

    fileName = fileName.replace(/^[0-9a-fA-F-]+-/, '');

    try {
      const response = await axios.get(fileUrl, { method: 'HEAD', headers: { withCredentials: true } });

      if (!response.status === 200) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const sizeInBytes = response.headers.get('Content-Length');

      const fileSize = sizeInBytes
        ? sizeInBytes < 1024 * 1024
          ? (sizeInBytes / 1024).toFixed(2) + ' KB'
          : (sizeInBytes / (1024 * 1024)).toFixed(2) + ' MB'
        : 'Size not available';

      return { name: fileName, size: fileSize, url: fileUrl };
    } catch (error) {
      console.error('Error fetching file size:', error);
      return { name: fileName, size: 'Size not available', url: fileUrl };
    }
  }

  useEffect(() => {
    async function fetchAllFilesInfo() {
      let results = await Promise.all(attachments.map((fileUrl) => getFileInfo(fileUrl)));
      results = results.filter((result) => result !== null);
      setFilesInfo(results);
    }

    fetchAllFilesInfo();
  }, [attachments]);

  return (
    <div className={flex && 'flex items-center gap-2'}>
      {filesInfo.length > 0 &&
        filesInfo.map((file, i) => {
          return (
            <div key={i} className={classes} onClick={() => modalRef.current.open()}>
              <Modal ref={modalRef} title={file.name}>
                <PdfViewer pdfUrl={file.url} text={file.name} />
              </Modal>
              <BsFileEarmarkText className="text-primary " />
              <div>
                <p className=" text-gray-800">{file.name}</p>
                {!hideSize && <p className="text-sm text-gray-500">{file.size}</p>}
              </div>
            </div>
          );
        })}
    </div>
  );
};

export default Files;
