import ApiProfile from "@/Api/apiProfileConfig";
import Loading from "@/Components/Loading";
import {
 Button,
 Input,
 Pagination,
 ScrollArea,
 Select,
 Table,
 TextInput,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { showNotification } from "@mantine/notifications";
import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { CiSearch } from "react-icons/ci";
import { IoIosArrowDown } from "react-icons/io";
import { RiDeleteBin6Line } from "react-icons/ri";
import { TbEdit } from "react-icons/tb";
import AddUserModel from "./AddUserModal";
import ActionUserModal from "./ActionUserModal";
import { LuUserRoundCog } from "react-icons/lu";
import ViewAdditionalRoleModal from "@/Pages/Settings/Partials/Profile/Partials/CompanyProfile/Partials/CompanyUserManage/Partials/ViewAdditionalRole/ViewAdditionalRoleModal";

export default function CampanyUserManageTable({
 Data,
 selectedCompanyId,
 getAllCompanyAccounts,
 closeModel,
}) {
	
 const [loading, setLoading] = useState(false);
 const [data, setData] = useState();
 const [delLoading, setDelLoading] = useState(false);
 const [EditLoading, setEditLoading] = useState(false);
 const [accessUserLoading, setAccessUserLoading] = useState(false);
 const [AdditionalRoleValue, setAdditionalRoleValue] = useState({});
 const [EditValue, setEditValue] = useState({});
 const [accessUserValue, setAccessUserValue] = useState({});
 const [EditErrorValue, setEditErrorValue] = useState({});
 // const [data, setData] = useState([]);
 const [selectedUserId, SetSelectedUserId] = useState(null);
 const { t } = useTranslation();
 const [opened, { open, close }] = useDisclosure(false);
 const [
  ViewAdditionalRoleOpened,
  { open: ViewAdditionalRoleOpen, close: ViewAdditionalRoleClose },
 ] = useDisclosure(false);
 const [DelOpened, { open: DelOpen, close: DelClose }] = useDisclosure(false);
 const [EditOpened, { open: EditOpen, close: EditClose }] =
  useDisclosure(false);
 const [accessOpened, { open: accessOpen, close: accessClose }] =
  useDisclosure(false);
 const [currentPage, setCurrentPage] = useState(1);
 const rowsPerPage = 8;
 const totalPages = Math.ceil(data?.length / rowsPerPage);
 useEffect(() => {
  setData(Data);
 }, [Data]);
 const currentData = useMemo(() => {
  return (
   (data &&
    data?.slice((currentPage - 1) * rowsPerPage, currentPage * rowsPerPage)) ||
   []
  );
 }, [data, currentPage, rowsPerPage]);

 const delete_user = async (user_id) => {
  // console.log(user_id);
  const value = {
   user_id,
  };
  try {
   setDelLoading(true);
   const { data } = await ApiProfile.put("/admin/delete_user", value);
   showNotification({
    message: data.message,
    color: "green",
   });
   setDelLoading(false);
   DelClose();
   closeModel();
   getAllCompanyAccounts();
   // setData(data);
   console.log(data);
  } catch (error) {
   setDelLoading(false);
   error.response?.data.message &&
    showNotification({
     message: error.response?.data.message,
     color: "red",
    });
   error.response?.data.error &&
    showNotification({
     message: error.response?.data.error,
     color: "red",
    });
   console.log(error);
  }
 };
 const update_user_access = async (user_id, accessValue) => {
  console.log(accessValue);
  const value = {
   user_id,
  };
  try {
   setAccessUserLoading(true);
   const { data } = await ApiProfile.put(
    `/admin/${
     accessValue === "enable"
      ? "enable_user"
      : accessValue === "disable" && "disable_user"
    }`,
    value
   );
   showNotification({
    message: data.message,
    color: "green",
   });
   setAccessUserLoading(false);
   accessClose();
   setAccessUserValue({});
   closeModel();
   getAllCompanyAccounts();
   // setData(data);
   console.log(data);
  } catch (error) {
   setAccessUserLoading(false);
   error.response?.data.message &&
    showNotification({
     message: error.response?.data.message,
     color: "red",
    });
   error.response?.data.error &&
    showNotification({
     message: error.response?.data.error,
     color: "red",
    });
   console.log(error);
  }
 };
 const change_user_role = async (user_id, new_role) => {
  // console.log(user_id);
  const value = {
   user_id,
   new_role: new_role === "User" ? 1 : new_role === "Admin" && 4,
  };
  try {
   setEditLoading(true);
   const { data } = await ApiProfile.put("/admin/change_user_role", value);
   showNotification({
    message: data.message,
    color: "green",
   });
   setEditLoading(false);
   EditClose();
   SetSelectedUserId(null);
   closeModel();
   getAllCompanyAccounts();
   // setData(data);
   console.log(data);
  } catch (error) {
   setEditLoading(false);
   error.response?.data.message &&
    showNotification({
     message: error.response?.data.message,
     color: "red",
    });
   error.response?.data.error &&
    showNotification({
     message: error.response?.data.error,
     color: "red",
    });
   console.log(error);
  }
 };

 const handleEditInputChange = (UserId, value) => {
  // Update evidenceFile with the file
  setEditValue((prev) => ({ [UserId]: value }));
 };
 const handleEditErrorRow = (UserId, value) => {
  // Update evidenceFile with the file
  setEditErrorValue((prev) => ({ [UserId]: value }));
 };
 const handleAccessUserRow = (UserId, value) => {
  // Update evidenceFile with the file
  setAccessUserValue((prev) => ({ [UserId]: value }));
 };
 const handleAdditionalRoleForUser = (UserId, value) => {
  setAdditionalRoleValue((prev) => ({ [UserId]: value }));
 };
 const toggleEditRow = (id) => {
  setData((currentData) =>
   currentData.map((item) =>
    item.id === id ? { ...item, isEditing: !item.isEditing } : item
   )
  );
 };
 console.log(currentData);

 const rows = currentData?.map((item, idx) => {
  const isEditable = item.isEditing;

  console.log(`${item.userId}-${idx}`);

  return (
   <>
    <Table.Tr
     key={`${item.userId}-${idx}`} // Use the unique ID as the key
     className={` text-sm font-bold text-[#626364] text-center`}
    >
     <Table.Td>
      <TextInput
       variant={"unstyled"}
       readOnly
       classNames={{
        input: "cursor-default",
       }}
       defaultValue={item?.userName}
      />
     </Table.Td>
     <Table.Td>
      <Select
       variant={isEditable ? "filled" : "unstyled"}
       readOnly={!isEditable}
       classNames={{
        input: !isEditable && "cursor-default",
       }}
       data={["User", "Admin"]}
       rightSection={isEditable ? <IoIosArrowDown /> : " "}
       defaultValue={item?.userRole}
       onChange={(e) => {
        handleEditInputChange(item.id, e);
        handleEditErrorRow(item.id, false);
        // console.log(e);
       }}
       // value={
       // 	EditValue?.[item?.id] || item?.role === 1
       // 		? "User"
       // 		: item?.role === 4 && "Admin"
       // }
      />
     </Table.Td>
     <Table.Td className="">
      <div className="text-center">
       <Button
        className=" bg-gray-300 hover:bg-gray-300 text-gray-700 hover:text-gray-700 
										rounded-full"
        onClick={() => {
         ViewAdditionalRoleOpen();
         SetSelectedUserId(item.userId);
         handleAdditionalRoleForUser(item.userId, item?.additional_roles);
        }}
       >
        <LuUserRoundCog className="me-2" /> Additional Role
       </Button>
      </div>
     </Table.Td>
     <Table.Td>
      <TextInput
       variant={"unstyled"}
       readOnly
       classNames={{
        input: "cursor-default",
       }}
       defaultValue={item?.email}
      />
     </Table.Td>

     <Table.Td>
      <div className="flex items-center justify-center gap-5">
       <span
        className="text-xl cursor-pointer "
        onClick={() => {
         toggleEditRow(item.id);
         handleEditErrorRow(item.id, false);
        }}
       >
        <TbEdit />
       </span>
       <div>
        <Button
         className="bg-primary hover:bg-primary"
         hidden={!isEditable}
         onClick={() => {
          if (
           EditValue?.[item.id] !== undefined &&
           EditValue?.[item.id] !== item?.role
          ) {
           EditOpen();
           SetSelectedUserId(item.id);
           handleEditErrorRow(item.id, false);
          } else {
           handleEditErrorRow(item.id, true);
          }
         }}
        >
         Submit
        </Button>
        {EditErrorValue[item.id] && (
         <h1 className="capitalize text-red-700 text-xs">
          please change user role first
         </h1>
        )}
        {/* {console.log(Error)} */}
       </div>
      </div>
     </Table.Td>
     {/* <Table.Td>
						<div>
							<span
								className="flex justify-center items-center text-xl"
								onClick={() => {
									DelOpen();
									SetSelectedUserId(item.id);
								}}
							>
								<RiDeleteBin6Line className="cursor-pointer" />
						
							</span>
						</div>
					</Table.Td> */}
     <Table.Td>
      <div className="flex justify-center gap-3">
       <Button
        className={
         !item.enabled
          ? "bg-primary hover:bg-primary"
          : "bg-gray-500 hover:bg-gray-500 cursor-not-allowed opacity-40"
        }
        size="xs"
        disabled={item.enabled}
        onClick={() => {
         accessOpen();
         SetSelectedUserId(item.id);
         handleAccessUserRow(item.id, "enable");
        }}
       >
        Enable
       </Button>
       <Button
        className={
         item.enabled
          ? "bg-red-700 hover:bg-red-700"
          : "bg-gray-500 hover:bg-gray-500 cursor-not-allowed opacity-40"
        }
        size="xs"
        disabled={!item.enabled}
        onClick={() => {
         accessOpen();
         SetSelectedUserId(item.id);
         handleAccessUserRow(item.id, "disable");
        }}
       >
        Disable
       </Button>
      </div>
     </Table.Td>
    </Table.Tr>
   </>
  );
 });

 return (
  <>
   <>
    <div className="items-center justify-between flex">
     <Input
      type="text"
      placeholder={t("SearchPlaceholder")} // Translated placeholder
      className="border border-gray-300 rounded-md sm:w-auto focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-10/12 lg:w-1/4"
      size="md"
      leftSection={<CiSearch />}
     />
    </div>
    <div className="p-5 bg-white rounded-lg mt-7">
     <div className="w-full">
      <Button
       className="bg-[#05808b57] border-2 border-primary font-bold text-primary hover:bg-[#05808b57] hover:text-primary w-full"
       size="lg"
       onClick={() => open()}
      >
       {t("+ Add User")} {/* Translated button text */}
      </Button>
     </div>
     {loading ? (
      <Loading />
     ) : (
      <>
       <ScrollArea>
        <Table miw={800} verticalSpacing="sm">
         <Table.Thead className="pb-6 text-base font-bold text-center border-b-2 border-primary text-primary">
          <Table.Tr>
           {/* <Table.Th className="text-start"></Table.Th>
<Table.Th style={{ width: rem(40) }}>
</Table.Th> */}
           <Table.Th className="text-start">
            {t("Name")} {/* Translated table header */}
           </Table.Th>
           <Table.Th className="text-start">
            {t("Role")} {/* Translated table header */}
           </Table.Th>
           <Table.Th className="text-center">
            {t("Additional Role")} {/* Translated table header */}
           </Table.Th>
           <Table.Th className="text-start">
            {t("Email")} {/* Translated table header */}
           </Table.Th>
           <Table.Th className="text-center">
            {t("Edit")} {/* Translated table header */}
           </Table.Th>
           {/* <Table.Th className="text-center">
												{t("Delete")}
												
											</Table.Th> */}
           <Table.Th className="text-center">
            {t("Enable/Disable")} {/* Translated table header */}
           </Table.Th>
          </Table.Tr>
         </Table.Thead>
         <Table.Tbody>{rows}</Table.Tbody>
        </Table>
       </ScrollArea>
       <div className="md:flex justify-between mt-5">
        <p className="text-sm text-secondary-300" hidden={!data?.length}>
         {t("showingData", {
          start: (currentPage - 1) * rowsPerPage + 1,
          end: Math.min(currentPage * rowsPerPage, data?.length),
          total: data?.length,
         })}
        </p>
        <Pagination
         page={currentPage}
         onChange={(e) => {
          setCurrentPage(e);
         }}
         total={totalPages}
         color="#00c0a9"
         className={`flex justify-center mt-5 md:mt-0 ${
          !data?.length && "hidden"
         }`}
        />
       </div>
      </>
     )}

     {/* <!-- Pagination --> */}
    </div>
    <AddUserModel
     close={close}
     opened={opened}
     getAllCompanyAccounts={getAllCompanyAccounts}
     selectedCompanyId={selectedCompanyId}
     closeModel={closeModel}
    />
    {DelOpened && selectedUserId && (
     <ActionUserModal
      close={DelClose}
      opened={DelOpened}
      Action_user={delete_user}
      selectedUserId={selectedUserId}
      ActionLoading={delLoading}
      SetSelectedUserId={SetSelectedUserId}
      mainTitle={"Are you sure to Delete this account"}
     />
    )}
    {ViewAdditionalRoleOpened && selectedUserId && (
     <ViewAdditionalRoleModal
      close={ViewAdditionalRoleClose}
      opened={ViewAdditionalRoleOpened}
      selectedUserId={selectedUserId}
      SetSelectedUserId={SetSelectedUserId}
      show_company_users={getAllCompanyAccounts}
      data={AdditionalRoleValue[selectedUserId]}
      setAdditionalRoleValue={setAdditionalRoleValue}
      addApi={"/admin/add_user_additional_role"}
      removeApi={"/admin/remove_user_additional_role"}
      closeModel={closeModel}
     />
    )}
    {EditOpened && selectedUserId && EditValue[selectedUserId] && (
     <ActionUserModal
      close={EditClose}
      opened={EditOpened}
      Action_user={change_user_role}
      ActionLoading={EditLoading}
      selectedUserId={selectedUserId}
      SetSelectedUserId={SetSelectedUserId}
      mainTitle={`Are you sure to Update this account to '${EditValue[selectedUserId]}' Role`}
      ActionValue={EditValue[selectedUserId]}
      setEditValue={setEditValue}
     />
    )}

    {accessOpened && selectedUserId && accessUserValue[selectedUserId] && (
     <ActionUserModal
      close={accessClose}
      opened={accessOpened}
      Action_user={update_user_access}
      ActionLoading={accessUserLoading}
      selectedUserId={selectedUserId}
      SetSelectedUserId={SetSelectedUserId}
      mainTitle={`Are you sure to '${accessUserValue[selectedUserId]}' this account`}
      ActionValue={accessUserValue[selectedUserId]}
      setAccessUserValue={setAccessUserValue}
     />
    )}
   </>
  </>
 );
}
