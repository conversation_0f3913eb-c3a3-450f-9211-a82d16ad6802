import useSideBarRoute from "@/hooks/useSideBarRoute";
import S2Layout from "@/Layout/S2Layout";
import { Tabs } from "@mantine/core";
import axios from "axios";
import Cookies from "js-cookie";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import ActionsView from "./Partials/ActionsPartials/ActionsView";
import PowerBIAnalyticsView from "./Partials/PowerBIAnalyticsView/PowerBIAnalyticsView";
import AnalyticsView from "./Partials/AnalyticsPartials/AnalyticsView";
// import ClimateProgramView from "./Partials/ClimateProgramPartials/ClimateProgramView";
// import OffsettingView from "./Partials/Offsetting/OffsettingView";
import TargetsView from "./Partials/TargetsPartials/TargetsView";
import ApiS2 from "@/Api/apiS2Config";
import { IoMdHome } from "react-icons/io";

const DecarbonizeView = () => {
    const { netZeroMenu } = useSideBarRoute();
    const { t } = useTranslation();
    const [assignees, setAssignees] = useState([]);
    const [assetTypes, setAssetTypes] = useState([]);
    const [loading, setLoading] = useState(true);

    const [activeTab, setActiveTab] = useState("targets");
    const assigneesData = async () => {
        try {
            const assignees = await axios.get(
                "https://portal-auth-main.azurewebsites.net/get-company-users",
                {
                    headers: {
                        Authorization: `Bearer ${Cookies.get(
                            "level_user_token"
                        )}`,
                    },
                }
            );
            const assigneesData = Object.entries(assignees.data).map(
                ([key, value]) => ({
                    value: key, // convert key to number
                    label: value,
                })
            );
            setAssignees(assigneesData);
        } catch (error) {
            //console.log(error);
        }
    };
    const allAssetType = async () => {
        try {
            const { data } = await ApiS2.get(
                "/carbon-factors/get-all-asset-types"
            );
            setLoading(false);
            setAssetTypes(data);
        } catch (error) {
            //console.log(error);
        }
    };
    useEffect(() => {
        assigneesData();
        allAssetType();
    }, []);
    return (
        <S2Layout menus={netZeroMenu} navbarTitle={"Decarbonisation"} 
        breadcrumbItems={[
            { title: <IoMdHome size={20}/>, href: "/get-started" },
            { title: "Decarboinsation", href: "#" },
          ]}>
            <div className="pb-8">
                <Tabs
                    className=""
                    defaultValue={activeTab}
                    onChange={setActiveTab}
                >
                    <Tabs.List
                        // justify="center"
                        className="flex flex-col justify-center gap-3 py-5 mb-6 rounded-xl shadow-lg md:justify-around md:flex-row before:hidden bg-white "
                    >
                        <Tabs.Tab
                            value="targets"
                            className={`text-xl border-none outline-none  font-semibold hover:bg-transparent  ${
                                activeTab === "targets"
                                    ? "text-primary"
                                    : "text-[#9C9C9C]"
                            } `}
                        >
                            {t("tabs.targets")}
                        </Tabs.Tab>

                        <Tabs.Tab
                            value="actions"
                            className={`text-xl border-none outline-none  font-semibold hover:bg-transparent  ${
                                activeTab === "actions"
                                    ? "text-primary"
                                    : "text-[#9C9C9C]"
                            } `}
                        >
                            {t("tabs.actions")}
                        </Tabs.Tab>

                        <Tabs.Tab
                            value="power-BI-analytics"
                            className={`text-xl border-none outline-none  font-semibold hover:bg-transparent  ${
                                activeTab === "power-BI-analytics"
                                    ? "text-primary"
                                    : "text-[#9C9C9C]"
                            } `}
                        >
                            {t("Climate Scenario Planning")}
                        </Tabs.Tab>

                        <Tabs.Tab
                            value="analytics"
                            className={`text-xl border-none outline-none  font-semibold hover:bg-transparent  ${
                                activeTab === "analytics"
                                    ? "text-primary"
                                    : "text-[#9C9C9C]"
                            } `}
                        >
                            {t("Power BI-analytics")}
                        </Tabs.Tab>

                        {/* <Tabs.Tab
              value="climate-program"
              className={`text-xl border-none outline-none  font-semibold hover:bg-transparent  ${
                activeTab === "climate-program"
                  ? "text-primary"
                  : "text-[#9C9C9C]"
              } `}
            >
              {t("tabs.climateProgram")}
            </Tabs.Tab>
            <Tabs.Tab
              value="Offsetting"
              className={`text-xl border-none outline-none  font-semibold hover:bg-transparent  ${
                activeTab === "Offsetting" ? "text-primary" : "text-[#9C9C9C]"
              } `}
            >
              {t("Offsetting")}
            </Tabs.Tab> */}
                    </Tabs.List>

                    <Tabs.Panel value="targets">
                        <TargetsView
                            assignees={assignees}
                            assetTypes={assetTypes}
                            activeTab={activeTab}
                        />
                    </Tabs.Panel>

                    <Tabs.Panel value="actions">
                        <ActionsView
                            assignees={assignees}
                            assigneesData={assigneesData}
                            assetTypes={assetTypes}
                            loading={loading}
                            allAssetType={allAssetType}
                            setLoading={setLoading}
                        />
                    </Tabs.Panel>

                    <Tabs.Panel value="analytics">
                        <AnalyticsView />
                    </Tabs.Panel>

                    <Tabs.Panel value="power-BI-analytics">
                        <PowerBIAnalyticsView />
                    </Tabs.Panel>

                    {/* <Tabs.Panel value="climate-program">
            <ClimateProgramView />
          </Tabs.Panel>
          <Tabs.Panel value="Offsetting">
            <OffsettingView />
          </Tabs.Panel> */}
                </Tabs>
            </div>
        </S2Layout>
    );
};

export default DecarbonizeView;
