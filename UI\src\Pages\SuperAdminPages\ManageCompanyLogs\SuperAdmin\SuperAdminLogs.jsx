import ApiProfile from '@/Api/apiProfileConfig';
import Loading from '@/Components/Loading';
import { Button, Select, Tooltip } from '@mantine/core';
import { DateInput } from '@mantine/dates';
import { useForm } from '@mantine/form';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { IoIosArrowDown } from 'react-icons/io';
import SuperAdminLogsTable from './Partials/SuperAdminLogsTable';
export default function SuperAdminLogs() {
  const [loading, setLoading] = useState()
  const [filteredData, setFilteredData] = useState()
  const [originalLogsData, setOriginalLogsData] = useState([]); // البيانات الأصلية
  const [logsData, setLogsData] = useState([])

  const [data, setData] = useState({
    Action_Type: "",
    UserName: "",
    date: "",
  })
  const form = useForm({
    mode: "uncontrolled",
    initialValues: {
      Action_Type: "",
      UserName: "",
      date: "",
    },

  });
  const add_new_filter = (value) => {
    console.log(value);
    const { Action_Type, UserName, date } = value;

    // استخدام البيانات الأصلية دائمًا كأساس للفلترة
    const filteredData = originalLogsData.filter((item) => {
      const matchesActionType = !Action_Type || item.actionType === Action_Type;

      const matchesUserName =
        !UserName || item.actionMessage.includes(`Super admin ${UserName}`);

      const matchesDate = !date || item.createdAt.includes(date);

      return matchesActionType && matchesUserName && matchesDate;
    });

    console.log(filteredData);

    setLogsData(filteredData);
  };
  const isAnyFieldFilled =
    data.Action_Type?.trim() !== "" ||
    data.UserName?.trim() !== "" ||
    data?.date && dayjs(data?.date).format("YYYY-MM-DD")?.trim() !== "";
  // console.log(form.values);
  const getLogsCompanyData = async () => {
    try {
      const { data } = await ApiProfile.get(
        "/admin/get_super_admin_logs"
      );
      setLogsData(data);
      setOriginalLogsData(data);
      console.log(data);
    } catch (error) {
      console.log(error);
    }
  };
  useEffect(() => {
    getLogsCompanyData();
  }, []);
  useEffect(() => {
    form.setValues({
      Action_Type: data?.Action_Type,
      UserName: data?.UserName,
      date: data?.date && dayjs(data?.date).format("YYYY-MM-DD"),
    })
  }, [data])
  return (
    <>
      <div className='grid grid-cols-1 md:grid-cols-4 items-center gap-5'>
        <Select
          // {...form.getInputProps("Action_Type")}
          // key={form.key("Action_Type")}
          value={data.Action_Type}
          placeholder="Select Company Name ..."
          label={"Action Type"}
          rightSection={<IoIosArrowDown />}
          data={["Enable User", "Disable User", 'Update User Information', 'Delete User']}
          onChange={(value) => setData((prev) => ({ ...prev, Action_Type: value }))}
        />
        <Select
          // {...form.getInputProps("UserName")}
          // key={form.key("UserName")}
          value={data.UserName}
          placeholder="Select User Name ..."
          label={"Admin Name"}
          data={["Dr Ahmed Shawky", "Hady", 'Mahmoud Youssef']}
          rightSection={<IoIosArrowDown />}
          onChange={(value) => setData((prev) => ({ ...prev, UserName: value }))}
        />
        <DateInput
          clearable
          valueFormat="YYYY-MM-DD"
          placeholder="Select Date ..."
          label={"Date"}
          onChange={(value) => {
            console.log(value)

            setData((prev) => ({ ...prev, date: value }));

          }}
        />
        <Tooltip
          multiline
          w={220}
          radius={"md"}
          withArrow
          transitionProps={{ duration: 200 }}
          className={`${isAnyFieldFilled ? "hidden" : ""}`}
          label={
            !isAnyFieldFilled && (
              <span className="capitalize flex justify-center">
                please Select at least on input
              </span>
            )
          }
        >

          <Button
            className="bg-primary hover:bg-primary mt-5"
            onClick={!loading && form.onSubmit(add_new_filter)}
            disabled={!isAnyFieldFilled || loading}
          >
            {loading ? <Loading /> : "Submit"}
          </Button>
        </Tooltip>
      </div>
      <SuperAdminLogsTable data={logsData} />
    </>
  )
}
