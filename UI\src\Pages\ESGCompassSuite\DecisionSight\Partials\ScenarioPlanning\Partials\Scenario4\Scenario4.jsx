import React from "react";
import { BarChart } from "@mantine/charts";

const Scenario4 = () => {
  const data = [
    { month: "Current", Risk_Reduction: 1200, Cost: 900, Co_Benefits: 200 },
    { month: "Proactive", Risk_Reduction: 1900, Cost: 1200, Co_Benefits: 400 },
    { month: "Reactive", Risk_Reduction: 400, Cost: 1000, Co_Benefits: 200 },
    { month: "Transformative", Risk_Reduction: 1000, Cost: 200, Co_Benefits: 800 },
  ];
  return (
    <div>
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-4 rounded-xl bg-[#dfedf0] p-4 border-[#E8E7EA] border-2 dark:bg-[#282828] dark:text-white dark:border-black">
          ANALYSIS RESULTS Scenario 4
        </div>
        <div className="flex flex-row w-full justify-between p-4 items-center gap-4">
          <div className="w-full bg-[#dfedf0] p-4 rounded-xl dark:bg-[#282828] dark:text-white dark:border-black ">
            <BarChart
              h={500}
              data={data}
              dataKey="month"
              withLegend
              legendProps={{ verticalAlign: 'bottom', height: 50 }}
              series={[
                { name: "Risk_Reduction", color: "violet.6" },
                { name: "Cost", color: "blue.6" },
                { name: "Co_Benefits", color: "teal.6" },
              ]}
              tickLine="y"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Scenario4;
