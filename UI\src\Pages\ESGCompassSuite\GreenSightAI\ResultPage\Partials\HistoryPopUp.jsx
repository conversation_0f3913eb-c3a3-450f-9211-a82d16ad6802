// CustomPopup.js
import ApiSustain360 from "@/Api/apiSustain360";
import Loading from "@/Components/Loading";
import Share from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/Share";
import ViewPDF from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/ViewPDF";
import { Button, Modal, ScrollArea, Table } from "@mantine/core";
import { useEffect, useState } from "react";
import { FiDownload } from "react-icons/fi";

function HistoryPopUp({ opened, close }) {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState();
  const getAssessmentHistory = async () => {
    try {
      setLoading(true);
      const { data } = await ApiSustain360.get("/api/assessments/reports");
      setData(data);
      setLoading(false);
      //console.log(data);
    } catch (error) {
      setLoading(false);
      //console.log(error);
    }
  };
  useEffect(() => {
    getAssessmentHistory();
  }, []);
  
  const rows = data?.map((item, idx) => (
    <Table.Tr
      key={`${idx + 1}-${idx}`}
      //  className={`${cx({
      //   // ["bg-[#07838F1A]"]: selected,
      //  })} text-sm font-bold text-[#626364] text-center`}
    >
      <Table.Td className="text-center">
        <p>{new Date(item.createdAt).toLocaleDateString("en-CA")}</p>
      </Table.Td>
      <Table.Td className="text-center">
        <p>
        {item.analysis?.overallScore?.score_percentage}
        </p>
      </Table.Td>
      <Table.Td className="text-center">
          <p>{item.analysis?.overallScore?.level}</p>
        </Table.Td>
      <Table.Td className="mx-auto ">
        <div className="flex justify-center items-center  h-12 mx-auto">
          <ViewPDF
            btnStyle={
              item?.report_url
                ? "border-[1px] flex items-center px-2 justify-between  h-[40px] text-[17px] font-bold rounded-lg text-secondary-300 border-secondary-300 bg-white hover:text-white hover:bg-secondary-300"
                : "border-[1px] flex items-center px-2 justify-between h-[40px] text-[17px] font-bold rounded-lg text-white border-gray-300 bg-gray-300 hover:text-white hover:bg-gray-300 cursor-not-allowed"
            }
            pdfUrl={item?.report_url}
            text={"View Report"}
            disabled={!item?.report_url && true}
          />
        </div>
      </Table.Td>
      <Table.Td className="mx-auto">
        <div className="flex justify-center items-center">
          <Share
            link={item?.report_url}
            btnStyle={
              item?.report_url
                ? "border-[1px] flex items-center px-2 justify-between  h-[40px] text-[17px] font-bold rounded-lg text-secondary-300 border-secondary-300 bg-white hover:text-white hover:bg-secondary-300"
                : "border-[1px] flex items-center px-2 justify-between h-[40px] text-[17px] font-bold rounded-lg text-white border-gray-300 bg-gray-300 hover:text-white hover:bg-gray-300 cursor-not-allowed"
            }
            disabled={!item?.report_url && true}
          />
        </div>
      </Table.Td>
      <Table.Td className="mx-auto">
        <div className="flex justify-center items-center">
          <Button
            component="a"
            className={
              item?.report_url
                ? "border-[1px] flex items-center px-4 justify-between h-[40px] text-sm font-bold rounded-lg text-white bg-secondary-300 ease-out duration-200 hover:translate-y-2 hover:bg-secondary-400"
                : "border-[1px] flex items-center px-2 justify-between h-[40px] text-[17px] font-bold rounded-lg text-white border-gray-300 bg-gray-300 hover:text-white hover:bg-gray-300 cursor-not-allowed"
            }
            href={item?.report_url}
            download
            disabled={!item?.report_url ? true : false}
          >
            Download Report
            <span>
              <FiDownload className="text-lg ms-1" />
            </span>
          </Button>
        </div>
      </Table.Td>
    </Table.Tr>
  ));
  return (
    <Modal
      opened={opened}
      onClose={close}
      centered
      size={"100%"}
      withCloseButton={true}
      title={"GreenSight AI"}
    >
      {loading ? (
        <Loading />
      ) : (
        <>
          {/* <h1>{assessmentType || "assessmentName"}</h1> */}
          <ScrollArea>
            <Table
              miw={800}
              //  mih={600}
              verticalSpacing="sm"
              className="p-2 my-1 bg-white  rounded-xl"
            >
              <Table.Thead className="border-b-2 border pb-6 text-secondary-500 font-bold text-base text-center">
                <Table.Tr>
                  <Table.Th className="text-center">Date</Table.Th>
                  <Table.Th className="text-center">Score Percentage</Table.Th>
                  <Table.Th className="text-center">Level</Table.Th>
                  <Table.Th className="text-center">View   report</Table.Th>
                  <Table.Th className="text-center">Share</Table.Th>
                  <Table.Th className="text-center">Download report</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>{rows}</Table.Tbody>
            </Table>
          </ScrollArea>
        </>
      )}
    </Modal>
  );
}

export default HistoryPopUp;
