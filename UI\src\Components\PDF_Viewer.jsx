import { Mo<PERSON> } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { useTranslation } from "react-i18next";
import { MdOutlineRemoveRedEye } from "react-icons/md";
import Loading from "@/Components/Loading";
import { Worker, Viewer } from "@react-pdf-viewer/core";
import { defaultLayoutPlugin } from "@react-pdf-viewer/default-layout";
import "@react-pdf-viewer/core/lib/styles/index.css";
import "@react-pdf-viewer/default-layout/lib/styles/index.css";

const ViewPDF = ({ pdfUrl, btnStyle, text, disabled }) => {
  const { t } = useTranslation();
  const defaultLayoutPluginInstance = defaultLayoutPlugin();
  const [opened, { open, close }] = useDisclosure(false);

  return (
    <>
      <button onClick={open} className={btnStyle} disabled={disabled}>
        <span>
          <MdOutlineRemoveRedEye className="text-2xl ms-1 mr-2" />
        </span>
        <span>{t(text)}</span>
      </button>
      <Modal opened={opened} onClose={close} size={"95%"} centered>
        {!pdfUrl ? (
          <Loading />
        ) : (
          <Worker workerUrl="https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js">
            <Viewer
              fileUrl={pdfUrl}
              plugins={[defaultLayoutPluginInstance]}
            />
          </Worker>
        )}
      </Modal>
    </>
  );
};

export default ViewPDF;