import { useState } from "react";
import { Checkbox, ScrollArea, Table } from "@mantine/core";

import UpdateAction from "./UpdateAction";
import DeleteTarget from "../../TargetsPartials/Partials/DeleteTarget";
import { FaTrashAlt } from "react-icons/fa";

const ActionsTable = ({
  tableData,
  assetTypes,
  refetchAgain,
  allAssignees,
}) => {
  let status = [
    { id: 1, value: "Not Planned", label: "Not Planned" },
    { id: 2, value: "Planned", label: "Planned" },
    { id: 3, value: "In Progress", label: "In Progress" },
    { id: 4, value: "on hold", label: "on hold" },
    { id: 5, value: "Completed", label: "Completed" },
  ];

  const [selectedRows, setSelectedRows] = useState([]);
//console.log(allAssignees);

  const rows = tableData?.map((action) => {
    const u = allAssignees
    .filter((user) => action?.userIds?.includes(parseInt(user?.value)))
    .map((us) => <h3 key={us.value}>{us?.label?.name}</h3>);
    return (
      <Table.Tr key={action.id} >
        <Table.Td className="w-[70px]">
          <div className="d flex justify-center items-center">
            <Checkbox
              aria-label="Select row"
              checked={selectedRows.includes(action.id)}
              onChange={(event) =>
                setSelectedRows(
                  event.currentTarget.checked
                    ? [...selectedRows, action.id]
                    : selectedRows.filter((position) => position !== action.id)
                )
              }
            />
          </div>
        </Table.Td>
        <Table.Td>{u}</Table.Td>
        <Table.Td >
          {action?.asset_type?.asset}
        </Table.Td>
        <Table.Td>{action.name}</Table.Td>
        <Table.Td>{status.find((s) => s.id == action.status).value}</Table.Td>
        <Table.Td>{action.yearFrom}</Table.Td>
        {/* <Table.Td>{action.yearTo}</Table.Td> */}
        <Table.Td>{action.reductionValue}</Table.Td>
        <Table.Td className="flex justify-around items-center gap-2">
          <UpdateAction
            action={action}
            assetTypes={assetTypes}
            refetchAgain={refetchAgain}
            allAssingees={allAssignees}
          />
        </Table.Td>
      </Table.Tr>
    );
  });
  return (
    <>
      {selectedRows.length > 0 && (
        <DeleteTarget
        keyName={'action_ids'}
          ids={selectedRows}
          title={"Delete All"}
          refetchAgain={()=> {
            refetchAgain()
            setSelectedRows([])
          }}
          url={"decarbonize/delete_climate_action"}
        />
      )}
      <ScrollArea>
        <Table
          miw={1000}
          highlightOnHover
          withTableBorder
          withColumnBorders
          classNames={{
            table: "rounded-xl border border-gray-300 bg-white font-semibold",
            tbody: "rounded-xl",
            th: "p-4 bg-primary text-white text-left",
            td: "p-4 text-left",
            tr: "hover:bg-gray-50",
          }}
        >
          <Table.Thead>
            <Table.Tr >
            <Table.Th className="Data-Table-Delete22">
                <FaTrashAlt className="w-4 h-4 mx-auto text-white" />
              </Table.Th>
              <Table.Th className="w-[250px]">Assignees</Table.Th>
              <Table.Th>Action Category</Table.Th>
              <Table.Th>Action Title</Table.Th>
              <Table.Th>Status</Table.Th>
              <Table.Th className="w-[100px]">Due Date</Table.Th>
              {/* <Table.Th>start date</Table.Th> */}
              <Table.Th >
                Emissions reduction
                <br /> potential (kg CO₂e/month)
              </Table.Th>
              <Table.Th className="Data-Table-Edit22">Edit</Table.Th>

            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>{rows}</Table.Tbody>
        </Table>
      </ScrollArea>
    </>
  );
};

export default ActionsTable;
