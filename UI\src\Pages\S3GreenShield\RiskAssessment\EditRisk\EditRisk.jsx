import { useEffect, useState } from "react";

import notes from "../../../../../public/assets/Images/risk-identification-icons/note-2.png";
import impact from "../../../../../public/assets/Images/risk-identification-icons/impact-icon.png";
import money from "../../../../../public/assets/Images/risk-identification-icons/moneys.png";
import riskicon from "../../../../../public/assets/Images/risk-identification-icons/danger.png";

import useSideBarRoute from "@/hooks/useSideBarRoute";
import S3Layout from "@/Layout/S3Layout";
import ApiS3 from "@/Api/apiS3";
import Cookies from "js-cookie";
import axios from "axios";
import {
  ESGDangerIcon,
  ESGFinancialSolidIcon,
  ESGImpactSolidIcon,
  ESGNotesSolidIcon,
  ESGRiskSolidIcon,
  ESGTaskIcon,
ESGShieldIcon,
ESGStatusUpIcon,
ESGClipboardIcon,
ESGShieldCheckIcon
} from "@/assets/icons";
import { showNotification } from "@mantine/notifications";
import FinancialImpactForm from "../../financialServicesStart/Partials/ESGRiskManagementSystem/RiskIdentificationAndAssessment/components/FinancialImpactForm";
import ImpactAssessmentForm from "../../financialServicesStart/Partials/ESGRiskManagementSystem/RiskIdentificationAndAssessment/components/ImpactAssessmentForm";
import RiskAssessmentForm from "../../financialServicesStart/Partials/ESGRiskManagementSystem/RiskIdentificationAndAssessment/components/RiskAssessmentForm";
import NotesForm from "../../financialServicesStart/Partials/ESGRiskManagementSystem/RiskIdentificationAndAssessment/components/NotesForm";
import { useParams } from "react-router";
import { Link, useSearchParams } from "react-router-dom";
import SelectFormBtn from "../../financialServicesStart/Partials/ESGRiskManagementSystem/RiskIdentificationAndAssessment/components/SelectFormBtn";

const EditRisk = () => {
  const [activeTab, setActiveTab] = useState(2);
  const data = [
    {
      id: 1,
      name: "Universe and Governance",
      icon: <ESGDangerIcon active={activeTab === 1} />,
      path: "/green-shield/financial/ESG-risk-management/main/systems/1",
    },
    {
      id: 2,
      name: "IRO Assessment",
      icon: <ESGTaskIcon active={activeTab === 2} />,
      path: "/green-shield/financial/ESG-risk-management/main/systems/2",
    },
    {
      id: 3,
      name: "Mitigation Strategies",
      icon: <ESGShieldIcon active={activeTab === 3} />,
      path: "/green-shield/financial/ESG-risk-management/main/systems/3",
    },
    {
      id: 4,
      name: "Analytics",
      icon: <ESGStatusUpIcon active={activeTab === 4} />,
      path: "/green-shield/financial/ESG-risk-management/main/systems/4",
    },
    {
      id: 5,
      name: "Audit",
      icon: <ESGClipboardIcon active={activeTab === 5} />,
      path: "/green-shield/financial/ESG-risk-management/main/systems/5",
    },
    {
      id: 6,
      name: "Anti-Greenwashing",
      icon: <ESGShieldCheckIcon active={activeTab === 6} />,
      path: "/green-shield/financial/ESG-risk-management/main/systems/6",
    },
  ];
  const { id } = useParams();

  const [refetch, setRefetch] = useState(false);

  const [searchParams] = useSearchParams();
  const type = searchParams.get("type");

  const { ESGRiskManagementMenu } = useSideBarRoute();
  const [Buttons, setButtons] = useState([
    {
      title: "Risk Identification",
      imgSrc: riskicon,
      sIcon: <ESGRiskSolidIcon />,
      progress: 0,
      id: 1,
    },
    {
      title: "Financial Impact",
      imgSrc: money,
      sIcon: <ESGFinancialSolidIcon />,
      progress: 0,
      id: 2,
    },
    {
      title: "ESG Impact Assessment",
      imgSrc: impact,
      sIcon: <ESGImpactSolidIcon />,
      progress: 0,
      id: 3,
    },
    {
      title: "Additional Information",
      imgSrc: notes,
      sIcon: <ESGNotesSolidIcon />,
      progress: 0,
      id: 4,
    },
  ]);
  const msg = (txt, color = "green") => {
    showNotification({ message: txt, color: color });
  };

  const [loading, setloading] = useState();

  const [selected, setSelected] = useState(1);

  // impact ai
  const [isImpactAiDone, setIsImpactAiDone] = useState(false);

  // financil ai
  const [isExposureAiDone, setIsExposureAiDone] = useState(false);
  const [isOpportunityAiDone, setIsOpportunityAiDone] = useState(false);

  // risk ai
  const [isRiskAiDone, setIsRiskAiDone] = useState(false);

  const [allLikelyHoodData, setAllLikelyHoodData] = useState([]);
  const [allImpactSeverityData, setAllImpactSeverityData] = useState([]);

  const [riskData, setRiskData] = useState({
    event: "",
    riskDescription: "",
    KRI: "",
    threshold: "",
    thresholdLevel: "",
    frequency: "",
    riskOwner: "",
    division: "",
    likelihoodId: "",
    impactSeverity: "",
    consequences: "",
    inherentRisk: "",
    controlEffectiveness: "",
    residualRisk: "",
    riskResponse: "",
    trend: "",
  });

  const [financialData, setFinancialData] = useState({
    financialImpactType: "",
    financialExposure: "",
    financialImpactTimeframe: "",
    affectedStakeholders: "",
    reputationalImpact: "",
    financialExposureDescription: "",
    opportunityDescription: "",
    category: "",
    value: "",
  });
  // console.log("🚀 ~ EditRisk ~ financialData:", financialData)

  const [impactData, setImpactData] = useState({
    esgImpactDomain: "",
    valueChainScope: "",
    impactDescription: "",
    rootCaseCategory: "",
    impactType: "",
    impactOnSDG: "",
    exposureLevel: "",
    actionStatus: "",
    progressTracking: 0,
  });
  // console.log("🚀 ~ EditRisk ~ impactData:", impactData)

  const [notesData, setNotesData] = useState({
    attachment: "",
    alignsWithRiskAppetite: "",
    preparedBy: "",
    Reviewer: "",
    dateRecorded: "",
    comments: "",
  });
  const [dateValue, setDateValue] = useState("");

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await ApiS3.get(`risk/${id}`);
        if (response.status == 200) {
          setRiskData({
            riskDescription: response.data.riskDescription || '',
            event: response.data.event || '',
            KRI: response.data.KRI || '',
            threshold: response.data.threshold || '',
            frequency: response.data.frequency || '',
            riskOwner: response.data.riskOwner|| '',
            division: response.data.division|| '',
            likelihoodId: response.data.likelihoodId?._id,
            impactSeverity: response.data.impactSeverity?._id,
            consequences: response.data.consequences || '',
            inherentRisk: response.data.inherentRisk,
            controlEffectiveness: response.data.controlEffectiveness,
            residualRisk: response.data.residualRisk,
            riskResponse: response.data.riskResponse || '',
            trend: response.data.trend || '',
          });

          setFinancialData({
            financialImpactType: response.data.financialImpactType || '',
            financialExposure: response.data.financialExposure || '',
            financialImpactTimeframe: response.data.financialImpactTimeframe || '',
            affectedStakeholders: response.data.affectedStakeholders || '',
            reputationalImpact: response.data.reputationalImpact || '',
            financialExposureDescription:
              response.data.financialExposureDescription || '',
            opportunityDescription: response.data.opportunityDescription || '',
            category: response.data.category || '',
            value: response.data.value || '',
          });

          setImpactData({
            esgImpactDomain: response.data.esgImpactDomain || '',
            valueChainScope: response.data.valueChainScope || "",
            impactDescription: response.data.impactDescription || '',
            rootCaseCategory: response.data.rootCaseCategory || '',
            impactType: response.data.impactType || '',
            impactOnSDG: response.data.impactOnSDG || "",
            expousreLevel: response.data.expousreLevel || '',
            actionStatus: response.data.actionStatus || '',
            progressTracking: response.data.progressTracking || '',
          });

          setNotesData({
            attachment: [],
            alignsWithRiskAppetite: response.data.alignsWithRiskAppetite || '',
            preparedBy: response.data.preparedBy || '',
            Reviewer: response.data.Reviewer || '',
            dateRecorded: response.data.dateRecorded || '',
            comments: response.data.comments || "",
          });
        }
      } catch (error) {
        console.log("Error fetching data:", error);
      }
    };

    fetchData();
  }, [id, refetch]); // Refetch data whenever refreshTable changes

  const [likilyHoodData, setLikilyHoodData] = useState([]);
  const [impactSeverityData, setImpactSeverityData] = useState([]);

  // async function
  const getData = async () => {
    const token = Cookies.get("level_user_token");
    const res = await axios.get(
      "https://portals3-h3bjdkhtbghye2aa.uksouth-01.azurewebsites.net/master-data/risk-identification-and-assessment",
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    setAllLikelyHoodData(res.data.likelihoods);
    setAllImpactSeverityData(res.data.impacts);
    const likilyData = res.data.likelihoods.map((ob) => {
      // value = ob._id - label = ob.name
      return { value: ob._id, label: ob.name };
    });
    const impactData = res.data.impacts.map((ob) => {
      // value = ob._id - label = ob.name
      return { value: ob._id, label: ob.name };
    });
    setLikilyHoodData(likilyData);
    setImpactSeverityData(impactData);
  };

  useEffect(() => {
    getData();
  }, []);

  const updateRisk = async () => {

    const newdata  = {
      ...riskData,
      ...financialData,
      ...impactData,
      ...notesData
    }
  
    setloading(true);
    
    try {
      await ApiS3.patch(`risk/${id}`, newdata);
      msg("Risk updated successfully", "green");
      setRefetch(!refetch);
    } catch (er) {
      msg(`Something went wrong ${er.response.data.message}`, "red");
    }
    setloading(false);
  };

  const updateProgress = (id, newProgress) => {
    setButtons((prev) =>
      prev.map((btn) =>
        btn.id === id ? { ...btn, progress: newProgress } : btn
      )
    );
  };

  useEffect(() => {
    // Count non-empty fields
    const filledFields = Object.values(impactData).filter(Boolean).length;
    const totalFields = Object.keys(impactData).length;
    const progress = (filledFields / totalFields) * 100;

    // Update progress in parent
    updateProgress(3, progress);
  }, [impactData]);

  useEffect(() => {
    // Count non-empty fields
    const filledFields = Object.values(financialData).filter(Boolean).length;
    const totalFields = Object.keys(financialData).length;
    const progress = (filledFields / totalFields) * 100;

    // Update progress in parent
    updateProgress(2, progress);
  }, [financialData]);

  useEffect(() => {
    // Count non-empty fields
    const filledFields = Object.values(riskData).filter(Boolean).length;
    const totalFields = Object.keys(riskData).length;
    const progress = (filledFields / totalFields) * 100;

    // Update progress in parent
    updateProgress(1, progress);
  }, [riskData]);

  useEffect(() => {
    // Count non-empty fields
    const filledFields = Object.values(notesData).filter(Boolean).length;
    const totalFields = Object.keys(notesData).length;
    const progress = (filledFields / totalFields) * 100;

    // Update progress in parent
    updateProgress(4, progress);
  }, [notesData]);

  const changeImpact = (key, value) => {
    setImpactData((prev) => ({ ...prev, [key]: value }));
  };
  const changeFinancial = (key, value) => {
    setFinancialData((prev) => ({ ...prev, [key]: value }));
  };
  const changeRisk = (key, value) => {
    setRiskData((prev) => ({ ...prev, [key]: value }));
  };
  const changeNotes = (key, value) => {
    setNotesData((prev) => ({ ...prev, [key]: value }));
  };

  // const [allReputationalImpactData, setAllReputationalImpactData] = useState([]);


  const calculateResidualRisk = (inherentRisk, controlEffectiveness) => {
    // Map control effectiveness (1-5) to its corresponding percentage reduction
    const effectivenessMap = {
      10: 0.1, // Poor/None (10% reduction)
      30: 0.3, // Weak (30% reduction)
      50: 0.5, // Moderate (50% reduction)
      70: 0.7, // Effective (70% reduction)
      90: 0.9, // Very Effective (90% reduction)
    };

    // Get the percentage reduction based on the control effectiveness value
    const effectivenessPercentage = effectivenessMap[controlEffectiveness] || 0;

    // Calculate residual risk
    const residualRisk = inherentRisk * (1 - effectivenessPercentage);

    if (residualRisk < 1) {
      return Math.round(1);
    }else {
      // Round the result to the nearest integer
      return Math.round(residualRisk);
    }

  };
  // useEffect to calculate residual risk when inherentRisk or controlEffectiveness changes
  useEffect(() => {
    if (riskData.inherentRisk !== "" && riskData.controlEffectiveness !== "") {
      const residualRisk = calculateResidualRisk(
        riskData.inherentRisk,
        riskData.controlEffectiveness
      );
      setRiskData((prev) => ({ ...prev, residualRisk }));
    }
  }, [riskData.inherentRisk, riskData.controlEffectiveness]);

  // Calculate inherent risk value when likelihood or impact severity changes
  useEffect(() => {
    if (riskData.likelihoodId && riskData.impactSeverity) {
      const likelihoodValue = allLikelyHoodData.find(
        (item) => item._id === riskData.likelihoodId
      );
      const severityValue = allImpactSeverityData.find(
        (item) => item._id === riskData.impactSeverity
      );
      const inherentRiskValue = likelihoodValue?.value * severityValue?.value;
      setRiskData((prev) => ({
        ...prev,
        inherentRisk: inherentRiskValue,
      }));
    }
  }, [riskData.likelihoodId, riskData.impactSeverity]);

  return (
    <S3Layout
      menus={ESGRiskManagementMenu}
      navbarTitle="Risk Identification & Assessment"
    >
            <div
        className="flex gap-10 mb-10 pb-3 min-w-full overflow-x-auto [&::-webkit-scrollbar]:h-[8px] [&::-webkit-scrollbar-thumb]:bg-[#05808b] [&::-webkit-scrollbar-track]:bg-white [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:rounded-full"
        role="tablist"
      >
        {data.map((tab, idx) => (
          <div key={tab.id} className="flex w-full gap-7 items-center">
            <Link
              to={tab.path}
              className={`whitespace-nowrap p-2 text-xs rounded-xl flex w-full items-center gap-2 font-semibold transition-colors duration-200
                    ${tab.id === 2 ? "bg-white" : "hover:bg-gray-100"}
                    `}
              onClick={() => setActiveTab(tab.id)}
              role="tab"
              aria-selected={tab.id === 2}
              aria-controls={`tabpanel-${tab.id}`}
            >
              {tab.icon}
              <span className={`${tab.id === 2 ? "gradient-text" : ""}`}>
                {tab.name}
              </span>
            </Link>
            {idx !== data.length - 1 && (
              <span className="w-[1px] h-full bg-[#1C889C]"></span>
            )}
          </div>
        ))}
      </div>
      <div className="grid lg:grid-cols-4 gap-5">
        {Buttons.map((item, index) => (
          <SelectFormBtn
            key={index}
            item={item}
            selected={selected}
            setSelected={setSelected}
            index={index}
          />
        ))}
      </div>

      {selected == 1 && (
        <RiskAssessmentForm
          type={type}
          handleChange={changeRisk}
          formData={riskData}
          isRiskDone={isRiskAiDone}
          setIsRiskDone={setIsRiskAiDone}
          likelihoodLevels={likilyHoodData}
          impactSeverityData={impactSeverityData}
          myData={riskData}
          addRiskFn={updateRisk}
          loading={loading}
        />
      )}

      {selected == 2 && (
        <FinancialImpactForm
          type={type}
          handleChange={changeFinancial}
          formData={financialData}
          isAiDoneExposure={isExposureAiDone}
          setIsAiDoneExposure={setIsExposureAiDone}
          isAiDoneOpportunity={isOpportunityAiDone}
          setIsAiDoneOpportunity={setIsOpportunityAiDone}
          loading={loading}
          myData={financialData}
          reputationalImpactData={impactSeverityData}
          updateRisk={updateRisk}
        />
      )}

      {selected == 3 && (
        <ImpactAssessmentForm
          type={type}
          handleChange={changeImpact}
          formData={impactData}
          isAiDone={isImpactAiDone}
          setIsAiDone={setIsImpactAiDone}
          myData={impactData}
          handleSubmit={updateRisk}
          loading={loading}
        />
      )}

      {selected == 4 && (
        <NotesForm
          type={type}
          handleChange={changeNotes}
          formData={notesData}
          addRiskFn={updateRisk}
          loading={loading}
          dateValue={dateValue}
          setDateValue={setDateValue}
        />
      )}
            <br />
            <br />
    </S3Layout>
  );
};

export default EditRisk;
