import ApiS1Config from "@/Api/apiS1Config";
import { notifications } from "@mantine/notifications";
import { createContext, useContext, useEffect, useState } from "react";
import { FaCheckCircle } from "react-icons/fa";
import { useNavigate, useParams } from "react-router";

const GreenSighContext = createContext();

// const MAX_SCOPES_NUMBER = 5;
const scopeId = [
  { id: 1, name: "General" },
  { id: 2, name: "Environment" },
  { id: 4, name: "Social" },
  { id: 3, name: "Governance" },
  { id: 5, name: "Finalize" },
];
const GreenSighProvider = ({ children }) => {
  const { categoryText, questionId } = useParams();

  // the new start
  const [loading, setLoading] = useState(false);
  const [postAnswersState, setPostAnswersState] = useState(false);
  const [questionSet, setQuestionSet] = useState();
  const [scope, setScope] = useState();
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [currentScopeID, setCurrentScopeID] = useState();
  const navigate = useNavigate();

  //  get_assessment_data and set the setQuestionSet Scope
  const get_assessment_data = async () => {
    setLoading(true);
    setCurrentQuestionIndex(0);
    try {
      setLoading(false);
      const { data } = await ApiS1Config.get("/get_assessment_data", {
        headers: { assessmentName: "GreenSight AI" },
      });
      //console.log(data);
      // //console.log(data); // Log to see the fetched data
      setQuestionSet(data.questionSet); // Update the question set state
      setCurrentScopeID(categoryText);
    } catch (error) {
      setLoading(false);
      //console.log(error); // Log any error
      error.response.data.Message === "There is no active assessment" &&
        navigate("/result");
      setQuestionSet(null);
    } finally {
      setLoading(false); // Always stop loading regardless of success or error
    }
  };

  const set_scope_question = () => {
    if (!questionSet) return; // Ensure questionSet is not undefined before filtering
    const filterScope = questionSet?.filter(
      (item) => item?.categoryText === categoryText
    );
    if (filterScope && filterScope.length > 0) {
      setScope(filterScope[0]);
    }
    // //console.log(filterScope); // Log to check the filtered result
    const questionObjects = filterScope[0]?.questionObjects.find(
      (item) => item?.questionId === questionId
    );
    // //console.log(filterScope);
    // //console.log(questionObjects);
  };

  const get_scope_question = (categoryText) => {
    if (!questionSet) return; // Ensure questionSet is not undefined before filtering
    const filterScope = questionSet?.filter(
      (item) => item?.categoryText === categoryText
    );
    if (filterScope && filterScope.length > 0) {
      setScope(filterScope[0]);
    }
    // //console.log(filterScope); // Log to check the filtered result
  };

  useEffect(() => {
    const fetchData = async () => {
      await get_assessment_data(); // Fetch data first
    };

    fetchData();
  }, [categoryText]); // Fetch data only once when the component mounts

  useEffect(() => {
    // Call set_scope_question whenever questionSet or categoryText changes
    if (questionSet && categoryText) {
      set_scope_question();
      get_scope_question(categoryText);
      setCurrentScopeID(categoryText);
    }
  }, [questionSet, categoryText]); // Depend on both questionSet and categoryText

  // post Answers Func
  const postAnswers = async () => {
    //console.log(scope);
    const extractedData = {
      categoryText: scope.categoryText,
      questionObjects: scope.questionObjects.map((question) => ({
        responseOption: question.responseOption,
      })),
      scopeText: scope.scopeText,
    };
    //console.log(extractedData);
    if (postAnswersState && extractedData) {
      try {
        const { data } = await ApiS1Config.post(
          `/post_scope`,

          extractedData,

          {
            headers: {
              assessmentName: "GreenSight AI",
              categoryName: categoryText,
            },
          }
        );
        //console.log(data);
        notifications.show({
          title: "Updated successfully!",
          color: "green",
          icon: <FaCheckCircle />,
          loading,
        });
        setPostAnswersState(false);
      } catch (error) {
        //console.log(error);
        error.response.data.Message === "There is no active assessment" &&
          navigate("/result");
        error.response.data.message === "There is no active assessment" &&
          navigate("/result");
        notifications.show({
          title: "There an error occurred! try again.",
          color: "red",
        });
      }
    }
  };

  // handle Previous Scope func
  const handlePreviousScope = () => {
    setCurrentQuestionIndex(0);

    const currentCategoryText = scopeId.findIndex(
      (scope) => scope.name.toLowerCase() === categoryText.toLowerCase()
    );

    if (currentCategoryText > 0) {
      const previousScope = scopeId[currentCategoryText - 1];
      setCurrentScopeID(previousScope.name);
      navigate(`/diagnosis/${previousScope.name}/question/1`);
    } else {
      navigate("/summary");
    }

    postAnswers();
  };
  // handle Next Scope func
  const handleNextScope = () => {
    setCurrentQuestionIndex(0);

    const currentCategoryText = scopeId.findIndex(
      (scope) => scope.name.toLowerCase() === categoryText.toLowerCase()
    );

    if (
      currentCategoryText !== -1 &&
      currentCategoryText < scopeId.length - 1
    ) {
      const nextScope = scopeId[currentCategoryText + 1];
      setCurrentScopeID(nextScope.name);
      navigate(`/diagnosis/${nextScope.name}/question/1`);
    } else {
      navigate("/summary");
    }

    postAnswers();
  };

  const handleReview = async () => {
    await postAnswers();
    navigate("/summary");
  };

  return (
    <GreenSighContext.Provider
      value={{
        loading,
        setScope,
        scope,
        setPostAnswersState,
        postAnswersState,
        get_assessment_data,
        // summary,
        // resultReady,
        currentScopeID,
        postAnswers,
        handlePreviousScope,
        handleNextScope,
        currentQuestionIndex,
        setCurrentQuestionIndex,
        handleReview,
        categoryText,
        questionId,
        get_scope_question,
      }}
    >
      {children}
    </GreenSighContext.Provider>
  );
};

export const useGreenSighAssessment = () => useContext(GreenSighContext);

export default GreenSighProvider;
