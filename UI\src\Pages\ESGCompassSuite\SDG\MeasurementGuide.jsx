import {
    Box,
    Title,
    Text,
    List,
    Group,
    ThemeIcon,
    Stack,
    Paper,
} from "@mantine/core";
import { FaCheck } from "react-icons/fa";

export default function SdgImpactMeasurementGuide() {
    return (
        <Box mx="auto" maxWidth="1200px" p="md">
            <Title order={1} mb="xl">
                SDG Alignment and Impact Measurement: Impact Measurement User
                Guide
            </Title>

            {/* Overview Section */}
            <Stack spacing="lg">
                <Text size="lg" mb="sm">
                    <b>Overview</b>
                </Text>
                <Text c="dimmed">
                    SDG Alignment and Impact Measurement helps your organization
                    measure the impact of your sustainability initiatives. The
                    Impact Measurement page quantifies your progress and
                    effectiveness.
                </Text>

                {/* Getting Started Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Getting Started</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>
                            Accessing the Impact Measurement Page
                        </Text>
                        <List listStyleType="circle" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={400}>
                                    Login: Log into the system using your
                                    credentials
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={400}>
                                    Navigate: From the main dashboard, select
                                    SDG & Impact.
                                </Text>
                            </List.Item>
                        </List>
                    </List.Item>
                </List>

                {/* Page Structure Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Page Structure</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>
                            The Impact Measurement page includes:
                        </Text>
                        <List listStyleType="circle" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={400}>
                                    Assessment Questions: Questions to quantify
                                    the impact of your sustainability efforts.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={400}>
                                    Editable Responses: Tools to review and
                                    update previous answers.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={400}>
                                    Search and Filter: Tools to locate specific
                                    topics or filter questions by columns for
                                    targeted review.
                                </Text>
                            </List.Item>
                        </List>
                    </List.Item>
                </List>

                {/* Interacting with the Impact Measurement Page Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Interacting with the Impact Measurement Page</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>Answer Questions:</Text>
                        <Text size="sm">
                            Respond to questions to quantify the impact of your
                            sustainability initiatives by selecting:
                        </Text>
                        <List listStyleType="circle" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={700}>Assessment Level</Text> for each
                                question in each topic area:
                                <List
                                    listStyleType="disc"
                                    spacing="sm"
                                    ml="1.5rem"
                                >
                                    <List.Item>
                                        <Text fw={700}>Not Started:</Text> No
                                        action has been taken.
                                    </List.Item>
                                    <List.Item>
                                        <Text fw={700}>Not Applicable:</Text>{" "}
                                        The question does not apply to your
                                        organization.
                                    </List.Item>
                                    <List.Item>
                                        <Text fw={700}>Initial Planning:</Text>{" "}
                                        Planning is underway but not yet
                                        implemented.
                                    </List.Item>
                                    <List.Item>
                                        <Text fw={700}>In Development:</Text>{" "}
                                        Active development or preparation is in
                                        progress.
                                    </List.Item>
                                    <List.Item>
                                        <Text fw={700}>
                                            Partially Implemented:
                                        </Text>{" "}
                                        Some measures are in place but not fully
                                        effective.
                                    </List.Item>
                                    <List.Item>
                                        <Text fw={700}>Fully Implemented:</Text>{" "}
                                        Complete and effective measures are in
                                        place.
                                    </List.Item>
                                </List>
                            </List.Item>
                        </List>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Priority:</Text>
                        <Text size="sm">
                            Assign a priority level to each question:
                        </Text>
                        <List listStyleType="circle" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={700}>Top:</Text> Critical and requires
                                immediate attention.
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>High:</Text> Important and needs
                                prompt action.
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Medium:</Text> Requires action
                                within a reasonable timeframe.
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Low:</Text> Can be addressed with
                                lower urgency.
                            </List.Item>
                        </List>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Evidence:</Text>
                        <Text size="sm">
                            Upload or link supporting documents, data, or
                            records (e.g., audit reports, training logs) to
                            substantiate your assessment level and priority.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Key Initiative:</Text>
                        <Text size="sm">
                            Specify a key initiative tied to the question to
                            align with broader Sustainable Development Goals
                            (SDGs) like "Implement Fair Wage Policies" (SDG 8:
                            Decent Work) or "Gender Equality."
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Tags:</Text>
                        <Text size="sm">
                            Add labels to categorize questions or actions (e.g.,
                            "Labor Rights," "Supply Chain," "Urgent") for easier
                            tracking and filtering.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Edit Responses:</Text>
                        <Text size="sm">
                            Update answers to reflect recent changes in
                            practices.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Use Search/Filter:</Text>
                        <Text size="sm">
                            Use the search bar to find specific topics or filter
                            questions by columns for targeted review.
                        </Text>
                    </List.Item>
                </List>

                {/* Understanding Progress Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Understanding Your Progress</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>Progress Overview:</Text>
                        <List listStyleType="disc" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={700}>Impact Quantification:</Text>
                                <Text size="sm">
                                    Questions assess the effectiveness of your
                                    sustainability initiatives.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Editable Insights:</Text>
                                <Text size="sm">
                                    Updated responses ensure accuracy in
                                    measuring impact.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Focussed Analysis:</Text>
                                <Text size="sm">
                                    Search and filter options help you
                                    prioritize key impact areas.
                                </Text>
                            </List.Item>
                        </List>
                    </List.Item>
                </List>

                {/* Next Steps Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Next Steps</b>
                </Text>
                <List listStyleType="decimal" spacing="sm">
                    <List.Item>
                        <Text>
                            Answer all questions to quantify your sustainability
                            impact accurately.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Use search/filter tools to revisit and update
                            specific topics as needed.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Save your progress by clicking Save and assess with
                            the Assess button.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Proceed to the Reporting page to generate a detailed
                            report.
                        </Text>
                    </List.Item>
                </List>

                {/* Tip Section */}
                <Paper shadow="sm" p="md" radius="md" mt="xl" withBorder>
                    <Group position="apart">
                        <ThemeIcon variant="light" radius="xl" size="2rem">
                            <FaCheck size="1.2rem" />
                        </ThemeIcon>
                        <Text fw={500}>
                            Include recent data in your responses for a precise
                            impact assessment!
                        </Text>
                    </Group>
                </Paper>
            </Stack>
        </Box>
    );
}
