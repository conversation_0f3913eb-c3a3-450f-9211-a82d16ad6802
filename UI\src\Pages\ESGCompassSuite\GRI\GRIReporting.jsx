import useSideBarRoute from "@/hooks/useSideBarRoute";
import MainLayout from "@/Layout/MainLayout";
import AccordionDemo from "./MultiStepForm/Accordion";

const GRIReporting = () => {
  const { issbMenu } = useSideBarRoute();

  return (
    <MainLayout menus={issbMenu} navbarTitle={"GRI Reporting"}>
      <div className="min-h-screen p-4 md:p-10">
        <AccordionDemo />
      </div>
    </MainLayout>
  )
}

export default GRIReporting;