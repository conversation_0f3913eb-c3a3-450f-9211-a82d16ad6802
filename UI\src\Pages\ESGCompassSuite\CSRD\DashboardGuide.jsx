import {
    Box,
    Title,
    Text,
    List,
    Group,
    ThemeIcon,
    Stack,
    Paper,
} from "@mantine/core";
import { FaCheck } from "react-icons/fa";

export default function CsrdEsrsDashboardGuide() {
    return (
        <Box mx="auto" maxWidth="1200px" p="md">
            <Title order={1} mb="xl">
                CSRD Readiness: CSRD & ESRS Readiness Dashboard User Guide
            </Title>

            {/* Overview Section */}
            <Stack spacing="lg">
                <Text size="lg" mb="sm">
                    <b>Overview</b>
                </Text>
                <Text c="dimmed">
                    CSRD Readiness assesses your organization's alignment with
                    the Corporate Sustainability Reporting Directive (CSRD)
                    standards using European Sustainability Reporting Standards
                    (ESRS). The Dashboard page is your hub for managing this
                    assessment, and the Reporting page generates and shares
                    detailed reports based on your assessment.
                </Text>

                {/* Getting Started Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Getting Started</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>
                            Accessing the CSRD & ESRS Readiness Dashboard
                        </Text>
                        <List listStyleType="circle" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={400}>
                                    Login: Into the system using your
                                    credentials
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={400}>
                                    Navigate: From the main dashboard, select
                                    Regulatory Readiness and then CSRD
                                    Dashboard.
                                </Text>
                            </List.Item>
                        </List>
                    </List.Item>
                </List>

                {/* Page Structure Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Page Structure</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>The Dashboard page includes:</Text>
                        <List listStyleType="circle" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={400}>
                                    Assessment Categories: Cross-cutting
                                    Standards, Environment, Social, and
                                    Governance with ESRS-aligned questions.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={400}>
                                    Editable Responses: To review and update
                                    previous answers.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={400}>
                                    Use Search/Filter: Use the search bar to
                                    find specific topics or filter questions by
                                    columns for targeted review.
                                </Text>
                            </List.Item>
                        </List>
                    </List.Item>
                </List>

                {/* Interacting with the Dashboard Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Interacting with the Dashboard</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>Select Category:</Text>
                        <Text size="sm">
                            Choose a category (e.g., Environment, Social) to
                            answer relevant ESRS questions.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Answer Questions:</Text>
                        <Text size="sm">
                            Respond to questionnaires like ESRS E1 (Climate
                            Change) to evaluate readiness by selecting:
                        </Text>
                        <List listStyleType="circle" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={700}>Assessment Level</Text> for each
                                question in each topic area:
                                <List
                                    listStyleType="disc"
                                    spacing="sm"
                                    ml="1.5rem"
                                >
                                    <List.Item>
                                        <Text fw={700}>Not Started:</Text> No
                                        action has been taken.
                                    </List.Item>
                                    <List.Item>
                                        <Text fw={700}>Not Applicable:</Text>{" "}
                                        The question does not apply to your
                                        organization.
                                    </List.Item>
                                    <List.Item>
                                        <Text fw={700}>Initial Planning:</Text>{" "}
                                        Planning is underway but not yet
                                        implemented.
                                    </List.Item>
                                    <List.Item>
                                        <Text fw={700}>In Development:</Text>{" "}
                                        Active development or preparation is in
                                        progress.
                                    </List.Item>
                                    <List.Item>
                                        <Text fw={700}>
                                            Partially Implemented:
                                        </Text>{" "}
                                        Some measures are in place but not fully
                                        effective.
                                    </List.Item>
                                    <List.Item>
                                        <Text fw={700}>Fully Implemented:</Text>{" "}
                                        Complete and effective measures are in
                                        place.
                                    </List.Item>
                                </List>
                            </List.Item>
                        </List>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Priority:</Text>
                        <Text size="sm">
                            Assign a priority level to each question:
                        </Text>
                        <List listStyleType="circle" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={700}>Top:</Text> Critical and requires
                                immediate attention.
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>High:</Text> Important and needs
                                prompt action.
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Medium:</Text> Requires action
                                within a reasonable timeframe.
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Low:</Text> Can be addressed with
                                lower urgency.
                            </List.Item>
                        </List>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Evidence Upload:</Text>
                        <Text size="sm">
                            Link supporting documents, data, or records (e.g.,
                            audit reports, training logs) to substantiate your
                            assessment level and priority.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Action Items:</Text>
                        <Text size="sm">
                            Define specific tasks or steps needed to address
                            gaps (e.g., "Conduct staff training on human rights
                            policies," "Review supplier contracts for
                            compliance").
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Owner:</Text>
                        <Text size="sm">
                            Assign a responsible individual or team (e.g., "HR
                            Manager," "Sustainability Team") to oversee each
                            action item.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Tags:</Text>
                        <Text size="sm">
                            Add labels to categorize questions or actions (e.g.,
                            "Labor Rights," "Supply Chain," "Urgent") for easier
                            tracking and filtering.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Edit Responses:</Text>
                        <Text size="sm">
                            Update answers to reflect recent changes in
                            practices.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Use Search/Filter:</Text>
                        <Text size="sm">
                            Use the search bar to find specific topics or filter
                            questions by columns for targeted review.
                        </Text>
                    </List.Item>
                </List>

                {/* Understanding Progress Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Understanding Your Progress</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>Progress Overview:</Text>
                        <List listStyleType="disc" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={700}>Category Coverage:</Text>
                                <Text size="sm">
                                    Questions align with ESRS standards across
                                    Cross-cutting, Environment, Social, and
                                    Governance areas.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Editable Insights:</Text>
                                <Text size="sm">
                                    Updated responses ensure your assessment
                                    reflects current practices.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Targeted Review:</Text>
                                <Text size="sm">
                                    Search and filter options help you focus on
                                    key CSRD compliance areas.
                                </Text>
                            </List.Item>
                        </List>
                    </List.Item>
                </List>

                {/* Next Steps Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Next Steps</b>
                </Text>
                <List listStyleType="decimal" spacing="sm">
                    <List.Item>
                        <Text>
                            Select a category and answer its ESRS questions
                            based on your organization's needs.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Use search/filter tools to revisit and update
                            specific topics as required.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Save your progress by clicking Save and assess with
                            the Assess button.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Proceed to the Reporting page to view your results.
                        </Text>
                    </List.Item>
                </List>

                {/* Tip Section */}
                <Paper shadow="sm" p="md" radius="md" mt="xl" withBorder>
                    <Group position="apart">
                        <ThemeIcon variant="light" radius="xl" size="2rem">
                            <FaCheck size="1.2rem" />
                        </ThemeIcon>
                        <Text fw={500}>
                            Focus on one category at a time to ensure thorough
                            ESRS compliance!
                        </Text>
                    </Group>
                </Paper>
            </Stack>
        </Box>
    );
}
