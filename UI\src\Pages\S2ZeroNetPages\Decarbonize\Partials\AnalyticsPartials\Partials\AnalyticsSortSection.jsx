import React, { useState } from "react";
import { Checkbox, Select } from "@mantine/core";
import { YearPickerInput } from "@mantine/dates";

const AnalyticsSortSection = () => {
  const [value, setValue] = useState(null);
  return (
    <div>
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-4 flex items-start">
          Analytics
          <span className="text-xs text ml-1 bg-[#e09432] py-1 px-2 rounded-full text-white">Setup Required</span>
        </h2>
        <p>Get insights into your emissions data and reduction progress.</p>
      </div>
      <div className="flex flex-col items-start gap-4 md:flex-row md:gap-10">
        {/* dates */}
        {/* <div className="flex flex-col items-start my-4">
          <h3 className="font-semibold text-sm mb-4">Date</h3>
          {/* <div className="flex flex-col gap-4 md:flex-row ">
            <div>
              <YearPickerInput
                className="flex flex-col items-start gap-2 md:flex-row md:items-center"
                label="From"
                placeholder="Start date"
                value={value}
                onChange={setValue}
              />
            </div>
            <div>
              <YearPickerInput
                className="flex flex-col items-start gap-2 md:flex-row md:items-center"
                label="To"
                placeholder="Due date"
                value={value}
                onChange={setValue}
              />
            </div>
          </div> 
        </div> */}

        {/* scopes */}
        {/* <div className="flex flex-col items-start my-4">
          <h3 className="font-semibold text-sm">Scopes</h3>
          <div className="flex flex-col justify-start gap-5 md:flex-row">
            <Checkbox
              className="my-4 font-semibold"
              label="S1"
              color="teal"
              variant="outline"
            />
            <Checkbox
              className="my-4 font-semibold"
              label="S2"
              color="teal"
              variant="outline"
            />
            <Checkbox
              className="my-4 font-semibold"
              label="S3"
              color="teal"
              variant="outline"
            />
          </div>
        </div> */}

        {/* category */}
        {/* <Select
          className="flex flex-col items-start my-2 me-6"
          classNames={{ label: "py-2" }}
          label="Categories"
          placeholder="Pick value"
          defaultValue="Electricity"
          data={["Electricity", "Commute", "Maintenance"]}
        /> */}
      </div>
    </div>
  );
};

export default AnalyticsSortSection;
