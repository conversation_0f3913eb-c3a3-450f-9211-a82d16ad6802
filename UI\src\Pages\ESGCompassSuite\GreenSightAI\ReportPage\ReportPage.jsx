import useSideBarRoute from "@/hooks/useSideBarRoute";
import S3Layout from "@/Layout/S3Layout";
import { useTranslation } from "react-i18next";

import { Alert } from "@mantine/core";
import { YearPickerInput } from "@mantine/dates";

import { Ci<PERSON><PERSON><PERSON><PERSON>heck, CiShare2 } from "react-icons/ci";
import { FiDownload } from "react-icons/fi";
import { IoIosArrowDown } from "react-icons/io";
import { MdOutlineRemoveRedEye } from "react-icons/md";

import reportImg from "@/assets/images/report-page-img.png";
import axios from "axios";
import Cookies from "js-cookie";

const ReportPage = () => {
  const { t } = useTranslation();
  const { netZeroMenu } = useSideBarRoute();

  const downloadReport = async () => {
    const headers = {
      headers: {
        Authorization: `Bearer ${Cookies.get("level_user_token")}`,
        "Content-Type": "application/json",
      },
    };

    let link = "";

    try {
      await axios({
        url: "s3-b7b5bpamezamfsas.eastus-01.azurewebsites.net/green/risks/download-report",
        method: "GET",
        ...headers,
        responseType: "blob",
      })
        .then((response) => {
          link = document.createElement("a");
          link.href = URL.createObjectURL(new Blob([response.data]));
          link.download = "updated-presentation.pptx";
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .catch((error) => {
          console.error("There was a problem with the request:", error);
        });
    } catch (er) {
      console.error(er, "error");
      // Handle the error or show notification here
    }
  };

  return (
    <S3Layout
    // menus={netZeroMenu}
    // breadcrumbItems={[
    //   { title: "Launchpad", href: "/get-started" },
    //   { title: "Review", href: "#" },
    // ]}
    >
      <div className="w-full left-section">
        <div className="report-page flex justify-between gap-y-3 flex-wrap mb-[38px]">
          <div className="year-pick bg-secondary-300 px-3 flex items-center overflow-hidden rounded-xl lg:w-[214px] lg:h-[52px] border-[1px] text-white">
            <YearPickerInput
              w={"100%"}
              bg={"#00C0A9"}
              radius={"sm"}
              variant="unstyled"
              size="lg"
              placeholder={t("reportPageMain.reportingYear")}
            />
            <IoIosArrowDown size={14} />
          </div>

          <div className="flex flex-wrap items-center gap-6">
            <button className="border-[1px] flex items-center px-4 justify-between w-[195px] h-[56px] text-[17px] font-bold rounded-lg text-secondary-300 border-secondary-300 bg-white">
              <span>{t("reportPageMain.viewReport")}</span>
              <span>
                <MdOutlineRemoveRedEye className="text-lg" />
              </span>
            </button>
            <button className="border-[1px] flex items-center px-4 justify-between w-[155px] h-[56px] text-[17px] font-bold rounded-lg text-secondary-300 border-secondary-300 bg-white">
              <span>{t("reportPageMain.share")}</span>
              <span>
                <CiShare2 className="text-lg" />
              </span>
            </button>
            <button
              onClick={downloadReport}
              className="border-[1px] flex items-center px-4 justify-between w-[195px] h-[56px] text-sm font-bold rounded-lg text-white bg-primary"
            >
              <span>{t("reportPageMain.downloadReport")}</span>
              <span>
                <FiDownload className="text-lg" />
              </span>
            </button>
          </div>
        </div>

        <Alert
          variant="light"
          color="rgba(0, 191, 185, 0.11)"
          icon={
            <CiCircleCheck className="text-[#00BFB9] text-[19px] mt-2 w-[30px] h-[30px]" />
          }
          className="text-[#00BFB9]"
        >
          <span className="text-[#00BFB9] text-[19px]">
            {t("reportPageMain.reviewReady")}
          </span>
        </Alert>

        <div className="w-full mt-8 img-wrapper">
          <img
            src={reportImg}
            alt="report image"
            width={817}
            className="w-full"
            height={453}
          />
        </div>
      </div>
    </S3Layout>
  );
};

export default ReportPage;
