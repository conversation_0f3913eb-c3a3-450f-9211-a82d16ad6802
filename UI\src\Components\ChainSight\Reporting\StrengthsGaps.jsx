const StrengthsGaps = ({ latestReport }) => {
  // تحديد عدد الأعمدة بناءً على وجود عناصر في strengths و gaps
  const hasStrengths = latestReport?.priority_actions?.strengths?.length > 0;
  const hasGaps = latestReport?.priority_actions?.gaps?.length > 0;
  const gridColumns = hasStrengths && hasGaps ? 'md:grid-cols-2' : 'md:grid-cols-1';

  return (
    <div className="p-6 my-6 bg-white border-2 border-[#E8E7EA] rounded-lg">
      <h1 className="text-2xl font-bold text-[#272727] mb-6">
        Strengths & Gaps
      </h1>

      <div className={`grid ${gridColumns} gap-8`}>
        {/* Strengths Section */}
        {hasStrengths && (
          <div className="p-5 rounded-lg border-2 border-[#E8E7EA]">
            <h2 className="text-xl font-semibold text-[#272727] mb-4">
              Top Strengths
            </h2>
            <div className="space-y-4">
              {latestReport?.priority_actions.strengths.map((item, index) => (
                <div
                  key={index}
                  className="flex justify-between items-center bg-[#00C0A91A] border-l-4 border-l-[#00C0A9] p-4"
                >
                  <div>
                    <h3 className="font-medium text-[#272727]">{item.title}</h3>
                    {/* <p className="text-sm text-[#525252] mt-1">{item.category}</p> */}
                  </div>
                  <span className="bg-[#00C0A91A] text-[#00C0A9] text-sm font-medium px-2.5 py-0.5 rounded-full">
                    {item.total_score}/{item.max_score}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Gaps Section */}
        {hasGaps && (
          <div className="p-5 border-2 rounded-lg">
            <h2 className="text-xl font-semibold text-[#272727] mb-4">
              Priority Gaps
            </h2>
            <div className="space-y-4">
              {latestReport?.priority_actions.gaps.map((item, index) => (
                <div
                  key={index}
                  className="flex justify-between items-center bg-[#E81E1E1A] border-l-4 border-l-[#E81E1E] p-4"
                >
                  <div>
                    <h3 className="font-medium text-[#272727]">{item.title}</h3>
                    {/* <p className="text-sm text-[#525252] mt-1">{item.category}</p> */}
                  </div>
                  <span className="bg-[#E81E1E1A] text-[#E81E1E] text-sm font-medium px-2.5 py-0.5 rounded-full">
                    {item.total_score}/{item.max_score}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StrengthsGaps;