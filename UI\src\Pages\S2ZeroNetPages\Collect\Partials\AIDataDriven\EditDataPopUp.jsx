import { useDisclosure } from "@mantine/hooks";
import { Modal, Button, TextInput } from "@mantine/core";
import { RiEdit2Line } from "react-icons/ri";
import { useForm } from "@mantine/form";
import { useUpdateData } from "./hooks/useUpdateData";
import { useEffect } from "react";
import Loading from "@/Components/Loading";

const inputs = [
  {
    lable: "Billing Period",
    placeholder: "Enter your billing period",
    key: "Billing Period",
  },
  {
    lable: "Cost per kWh",
    placeholder: "Enter your cost per kWh",
    key: "Cost per kWh",
  },
  {
    lable: "Customer Number",
    placeholder: "Enter your customer number",
    key: "Customer Number",
  },
  {
    lable: "Statement Date",
    placeholder: "Enter your statement date",
    key: "Statement Date",
  },
  {
    lable: "Total Energy Cost (excl. VAT)",
    placeholder: "Enter your total energy cost (excl. VAT)",
    key: "excl",
  },
  {
    lable: "Total Energy Cost (incl. VAT)",
    placeholder: "Enter your total energy cost (incl. VAT)",
    key: "incl",
  },
  {
    lable: "Total kWh Consumed",
    placeholder: "Enter your total kWh consumed",
    key: "Total kWh Consumed",
  },
];

export default function EditDataPopUp({ data, id }) {
  const [opened, { open, close }] = useDisclosure(false);
  const { handleUpdateData, loading } = useUpdateData();

  const form = useForm({
    mode: "uncontrolled",
    initialValues: {
      "Billing Period": "kkk",
      "Cost per kWh": "kk",
      "Customer Number": "kk",
      "Statement Date": "kk",
      excl: data?.["excl"],
      incl: data?.["incl"],
      "Total kWh Consumed": "kk",
    },
  });

  useEffect(() => {
    form.setValues({
      "Billing Period": data?.["Billing Period"],
      "Cost per kWh": data?.["Cost per kWh"],
      "Customer Number": data?.["Customer Number"],
      "Statement Date": data?.["Statement Date"],
      excl: data?.["Total Energy Cost (excl. VAT)"],
      incl: data?.["Total Energy Cost (incl. VAT)"],
      "Total kWh Consumed": data?.["Total kWh Consumed"],
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  const handleSubmit = (values) => {
    console.log(values);
    const updateData = {
      "Billing Period": values["Billing Period"],
      "Customer Number": values["Customer Number"],
      "Statement Date": values["Statement Date"],
      "Total Energy Cost (excl. VAT)": +values["excl"],
      "Total Energy Cost (incl. VAT)": values["incl"],
      "Cost per kWh": +values["Cost per kWh"],
      "Total kWh Consumed": +values["Total kWh Consumed"],
    };
    console.log(updateData);
    handleUpdateData(updateData, id);
    // close();
  };

  return (
    <>
      <Button onClick={open} loading={loading} disabled={loading} color="green">
        <RiEdit2Line />
      </Button>

      <Modal opened={opened} onClose={close} size="auto" title="Edit Data">
        {data ? (
          <form
            onSubmit={form.onSubmit(handleSubmit, (errors) => {
              const firstErrorPath = Object.keys(errors)[0];
              form.getInputNode(firstErrorPath)?.focus();
            })}
            className="flex flex-col gap-4 items-end w-full"
          >
            <div className="grid md:grid-cols-2 gap-4 w-[80vw] sm:w-[70vw] md:w-[60vw] lg:w-[50vw]">
              {inputs.map(({ key, lable, placeholder }) => {
                return (
                  <TextInput
                    withAsterisk
                    key={form.key(key)}
                    label={lable}
                    placeholder={placeholder}
                    {...form.getInputProps(key)}
                  />
                );
              })}
            </div>
            <Button
              type="submit"
              onClick={handleSubmit}
              className="bg-primary hover:bg-[#37868F] text-white px-4 py-2 rounded-md disabled:bg-[#9aa5b1] disabled:hover:bg-[#9aa5b1] disabled:cursor-not-allowed"
            >
              Submit
            </Button>
          </form>
        ) : (
          <Loading />
        )}
      </Modal>
    </>
  );
}
