import ApiS2 from "@/Api/apiS2Config";
import { Modal } from "@mantine/core";
import { useEffect, useState } from "react";
import UpdateManualForm from "./UpdateManualForm";
import Loading from "@/Components/Loading";

export default function UpdateReviewPopUp({
 opened,
 onClose,
 data,
 assetTypeAll,
 getTableData,
 setSelectedItems,
 selectedItemsId,
 closePopup,
}) {
 const [loading, setLoading] = useState(false);
 const [allItemSpecificities, setAllItemSpecificities] = useState();
 const [AssetsError, setAssetsError] = useState();
 const [emissionsAll, setEmissionsAll] = useState([]); //? list of strings
 const [emissions, setEmissions] = useState([]);
 const [associatedFactors, setAssociatedFactors] = useState([]);
 //  const [assetType, setAssetType] = useState();
 const companyAssetId = data?.items[selectedItemsId]?.companyAssetId;
  //console.log(emissionsAll);

 const fetchItemSpecificities = () =>
  ApiS2.get("/carbon-factors/get_item_specificities");

 const fetchCompanyAssetsByType = (companyAssetId) =>
  ApiS2.get("/carbon-factors/get-items-associated-with-company-asset", {
   headers: { "company-asset-id": companyAssetId },
  });

 const handleError = (error) => {
  const errorMessage =
   error.response?.data.error ||
   error.response?.data.message
  setAssetsError(errorMessage);
  console.error(error);
 };

 const getItemSpecificities = async () => {
  setLoading(true);

  try {
   const [specificitiesRes, {data:customFactorsRes}] = await Promise.all([
    fetchItemSpecificities(),
    fetchCompanyAssetsByType(companyAssetId),
   ]);

   setAllItemSpecificities(specificitiesRes.data);
   setEmissionsAll(customFactorsRes?.associated_emissions);
   const emissionsList = customFactorsRes?.associated_emissions?.map(
    (el) => el.asset
   );
   setEmissions(emissionsList);
   setAssociatedFactors(customFactorsRes.associated_factors);
  } catch (error) {
   //console.log(error);

   handleError(error);
  } finally {
   setLoading(false);
  }
 };

 useEffect(() => {
  if (companyAssetId) {
   getItemSpecificities();
  }
 }, [companyAssetId]);

 return (
  <Modal
   opened={opened}
   onClose={onClose}
   centered
   size={"100%"}
   withCloseButton={false}
  >
   {companyAssetId && loading ? (
    <Loading />
   ) : (
    <UpdateManualForm
     allItemSpecificities={allItemSpecificities}
     loading={loading}
     AssetsError={AssetsError}
     onClose={onClose}
     data={data}
     getTableData={getTableData}
     setSelectedItems={setSelectedItems}
     selectedItemsId={selectedItemsId}
     closePopup={closePopup}
     emissionsAll={emissionsAll}
     associatedFactors={associatedFactors}
     emissions={emissions}
    />
   )}
  </Modal>
 );
}
