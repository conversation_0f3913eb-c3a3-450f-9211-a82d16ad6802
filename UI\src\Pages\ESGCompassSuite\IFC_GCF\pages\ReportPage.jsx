import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Select, Text, Button } from "@mantine/core";
import Cookies from "js-cookie";
import { useEffect, useState } from "react";
import { Bar, Radar, Line } from "react-chartjs-2";
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    Tooltip,
    Legend,
    RadialLinearScale,
    PointElement,
    LineElement,
    Filler,
} from "chart.js";
import { LuClock } from "react-icons/lu";
import { GoArrowRight } from "react-icons/go";
import { TbFilterSearch } from "react-icons/tb";
import axios from "axios";
import { TbDownload, TbHistory } from "react-icons/tb";
import { FaRegEye } from "react-icons/fa";
import { FaRegEye as FaRegEye6 } from "react-icons/fa6";
import {
    IoArrowDownOutline,
    IoShareSocial,
    IoShareSocialOutline,
} from "react-icons/io5";
import { useDisclosure } from "@mantine/hooks";
import { Viewer, Worker } from "@react-pdf-viewer/core";
import { defaultLayoutPlugin } from "@react-pdf-viewer/default-layout";

ChartJS.register(
    CategoryScale,
    LinearScale,
    BarElement,
    Tooltip,
    Legend,
    RadialLinearScale,
    PointElement,
    LineElement,
    Filler
);

const formatDate = (isoDate) => {
    const options = {
        year: "numeric",
        month: "numeric",
        day: "numeric",
    };
    return new Date(isoDate).toLocaleDateString("en-US", options);
};

const API_BASE_URL =
    "https://finance-guard-api-staging.azurewebsites.net/ifc-and-gcf-assessment";
const DOCVAULT_BASE_URL = "https://docvault-staging.azurewebsites.net";

const fetchReportData = async () => {
    const token = Cookies.get("level_user_token");
    if (!token) throw new Error("Authorization token missing");

    try {
        const response = await fetch(
            `${API_BASE_URL}/assessments/last-completed/report`,
            {
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                },
            }
        );

        if (!response.ok) {
            throw new Error(response.statusText);
        }

        return response.json();
    } catch (error) {
        console.error("Error fetching report data:", error);
        throw error;
    }
};

const exportAndDownloadReportPDF = async (reportId) => {
    const token = Cookies.get("level_user_token");

    if (!token) {
        alert("You must be logged in to download the report.");
        return;
    }

    try {
        // Step 1: Export the report and get the file ID
        const exportResponse = await fetch(
            `${API_BASE_URL}/reports/${reportId}/export-pdf`,
            {
                method: "POST",
                headers: {
                    Authorization: `Bearer ${token}`,
                    "Content-Type": "application/json",
                },
            }
        );

        if (!exportResponse.ok) {
            throw new Error("Failed to export report PDF");
        }

        const fileData = await exportResponse.json();
        const fileId = fileData.id;
        const fileName = fileData.name;

        // Step 2: Download the file using the file ID
        const downloadResponse = await fetch(
            `${DOCVAULT_BASE_URL}/api/v1/files/${fileId}/download`,
            {
                method: "GET",
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            }
        );

        if (!downloadResponse.ok) {
            throw new Error("Failed to download report PDF");
        }

        const blob = await downloadResponse.blob();
        const url = window.URL.createObjectURL(blob);

        const a = document.createElement("a");
        a.href = url;
        a.download = fileName;
        a.click();
        window.URL.revokeObjectURL(url);
    } catch (error) {
        console.error("Download error:", error);
        alert("Error downloading the report.");
    }
};

const generatePDFPreview = async (reportId, setPdfUrl, openModal) => {
    const token = Cookies.get("level_user_token");
    if (!token) {
        alert("You must be logged in to view the report.");
        return;
    }

    try {
        openModal();

        // Step 1: Export the report and get the file ID
        const exportResponse = await fetch(
            `${API_BASE_URL}/reports/${reportId}/export-pdf`,
            {
                method: "POST",
                headers: {
                    Authorization: `Bearer ${token}`,
                    "Content-Type": "application/json",
                },
            }
        );

        if (!exportResponse.ok) {
            throw new Error("Failed to export report PDF");
        }

        const fileData = await exportResponse.json();
        const fileId = fileData.id;

        // Step 2: Get the PDF file
        const downloadResponse = await fetch(
            `${DOCVAULT_BASE_URL}/api/v1/files/${fileId}/download`,
            {
                method: "GET",
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            }
        );

        if (!downloadResponse.ok) {
            throw new Error("Failed to download report PDF");
        }

        const blob = await downloadResponse.blob();
        const url = window.URL.createObjectURL(blob);

        // Set PDF URL and open modal
        setPdfUrl(url);
    } catch (error) {
        console.error("PDF preview error:", error);
        alert("Error loading the report preview.");
    }
};

const OverallAlignmentScore = ({ score, grade, status }) => {
    return (
        <div className="w-full rounded-xl border border-gray-300 p-6 gap-6 flex flex-col text-2xl font-semibold bg-white">
            <h1>Overall Alignment Score</h1>
            <div className="flex w-full">
                <div className="w-1/2 border border-gray-300 rounded-lg rounded-r-none flex items-center p-4">
                    <span className="w-fit text-5xl bg-clip-text text-transparent font-bold bg-gradient-to-r from-[#2C5A8C] via-[#1C889C] to-[#13B1A8]">
                        {score}
                    </span>
                </div>
                <div className="w-1/2 border border-gray-300 rounded-lg rounded-l-none border-l-0 flex flex-col justify-center p-6">
                    <span className="font-semibold text-4xl gap-1">
                        {grade}
                    </span>
                    <span className="text-sm text-[#07838F]">{status}</span>
                </div>
            </div>
        </div>
    );
};

const SectionScores = ({ sections }) => {
    const options = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: "bottom",
            },
        },
        scales: {
            x: {
                ticks: {
                    maxRotation: 45,
                    minRotation: 45,
                    font: {
                        size: 12,
                    },
                },
                callback: function (value) {
                    const label = data.labels[value];
                    return label?.length > 10
                        ? label.slice(0, 15) + "..."
                        : label;
                },
            },
            y: {
                ticks: {
                    font: {
                        size: 12,
                    },
                },
            },
        },
    };

    const data = {
        labels: sections.map((section) => section.title),
        datasets: [
            {
                label: "Current Score",
                data: sections.map((section) => section.score),
                backgroundColor: "#07838F",
            },
            {
                label: "Target Score",
                data: sections.map((section) => section.target),
                backgroundColor: "#F4B351",
            },
        ],
    };

    return (
        <div className="w-full rounded-xl border border-gray-300 p-6 gap-6 flex flex-col text-2xl font-semibold bg-white">
            <h1>Overall Alignment Score</h1>
            <div className="flex w-full">
                <Bar className="w-full" data={data} options={options} />
            </div>
        </div>
    );
};

const AreasForImprovement = ({ sections }) => {
    const areasList = sections.map((section) => ({
        title: section.title,
        score: section.score,
    }));

    return (
        <div className="w-1/2 rounded-xl border border-gray-300 p-6 gap-6 flex flex-col bg-white">
            <h1 className="text-2xl font-semibold">Areas for Improvement</h1>
            <div className="w-full flex flex-col h-36 overflow-hidden overflow-y-auto">
                {areasList.map((area, index) => (
                    <div
                        key={index}
                        className="w-full border-b border-gray-300 flex justify-between items-center p-3"
                    >
                        <span>{area.title}</span>
                        <span className="text-sm font-semibold">
                            {area.score}
                        </span>
                    </div>
                ))}
            </div>
        </div>
    );
};

const ComplianceStatus = () => {
    const statusList = [
        {
            name: "Fully Implemented",
            number: 6,
            color: "#00C0A9",
        },
        {
            name: "Partially Implemented",
            number: 0,
            color: "#FBB90D",
        },
        {
            name: "In Development",
            number: 8,
            color: "#FF9500",
        },
        {
            name: "Initial / Not Started",
            number: 1,
            color: "#E81E1E",
        },
    ];

    return (
        <div className="w-1/2 rounded-xl border border-gray-300 p-6 gap-6 flex flex-col bg-white">
            <h1 className="text-2xl font-semibold">Areas for Improvement</h1>
            <div className="w-full flex flex-col h-72">
                {statusList.map((status, index) => (
                    <div
                        className="w-full flex justify-between items-center p-2"
                        key={index}
                    >
                        <div className="flex gap-2 items-center">
                            <span
                                className="flex w-3 aspect-square rounded-full"
                                style={{ backgroundColor: status.color }}
                            />
                            <span>{status.name}</span>
                        </div>
                        <span>{status.number}</span>
                    </div>
                ))}
            </div>
        </div>
    );
};

const AreasForImprovementSpiderChar = ({ sections }) => {
    const data = {
        labels: sections.map((section) => section.title),
        datasets: [
            {
                label: "Current Score",
                data: sections.map((section) => section.score),
                backgroundColor: "rgba(7, 131, 143, 0.2)",
                borderColor: "rgba(7, 131, 143, 1)",
                borderWidth: 1,
            },
            {
                label: "Target Score",
                data: sections.map((section) => section.target),
                backgroundColor: "rgba(244, 179, 81, 0.2)",
                borderColor: "rgba(244, 179, 81, 1)",
                borderWidth: 1,
            },
        ],
    };

    return (
        <div className="w-1/2 rounded-xl border border-gray-300 p-6 gap-6 flex flex-col bg-white">
            <h1 className="text-2xl font-semibold">Areas for Improvement</h1>
            <Radar data={data} />
        </div>
    );
};

const Progress = () => {
    const options = {
        responsive: true,
        plugins: {
            legend: {
                position: "bottom",
            },
            title: {
                display: true,
                text: "Chart.js Line Chart",
            },
        },
    };

    const labels = [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
    ];

    const data = {
        labels,
        datasets: [
            {
                label: "Dataset 1",
                data: [30, 130, 81, 237, 726, 12, 920],
                borderColor: "rgba(7, 131, 143, 1)",
                backgroundColor: "rgba(7, 131, 143, 0.5)",
            },
        ],
    };

    return (
        <div className="w-1/2 rounded-xl border border-gray-300 p-6 gap-6 flex flex-col bg-white">
            <h1 className="text-2xl font-semibold">Areas for Improvement</h1>
            <div className="w-full flex-1 flex justify-center items-center">
                <Line options={options} data={data} />
            </div>
        </div>
    );
};

const GapAnalysis = ({ sections }) => {
    const options = {
        responsive: true,
        indexAxis: "y",
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: "bottom",
            },
        },
        scales: {
            x: {
                ticks: {
                    font: {
                        size: 12,
                    },
                },
                stacked: true,
            },
            y: {
                ticks: {
                    maxRotation: 45,
                    minRotation: 45,
                    font: {
                        size: 12,
                    },
                },
                stacked: true,
            },
        },
    };

    const data = {
        labels: sections.map((section) => section.title),
        datasets: [
            {
                label: "Current Score",
                data: sections.map((section) => section.score),
                backgroundColor: "#07838F",
            },
            {
                label: "Target Score",
                data: sections.map((section) => section.target),
                backgroundColor: "#F4B351",
            },
        ],
    };

    return (
        <div className="w-full rounded-xl border border-gray-300 p-6 gap-6 flex flex-col text-2xl font-semibold bg-white">
            <h1>Gap Analysis</h1>
            <div className="flex w-full">
                <Bar className="w-full" data={data} options={options} />
            </div>
        </div>
    );
};

const PriorityActions = ({ priority_actions }) => {
    return (
        <div className="w-full rounded-xl border border-gray-300 p-6 bg-white">
            <h1 className="text-2xl font-semibold mb-6">
                Priority Action Plan
            </h1>
            <div className="rounded-2xl border border-gray-200 overflow-hidden">
                <table className="w-full">
                    <thead>
                        <tr className="bg-gray-50 text-left">
                            <th className="py-4 px-4 font-medium">Section</th>
                            <th className="py-4 px-4 font-medium">
                                Current Score
                            </th>
                            <th className="py-4 px-4 font-medium">
                                Key Actions
                            </th>
                            <th className="py-4 px-4 font-medium">Timeline</th>
                        </tr>
                    </thead>
                    <tbody>
                        {priority_actions.map((item, index) => (
                            <tr
                                key={index}
                                className="border-b border-gray-200 last:border-b-0"
                            >
                                <td className="py-4 px-4 font-medium">
                                    {item.section}
                                </td>
                                <td className="py-4 px-4 text-purple-600 font-medium">
                                    {item.score}
                                </td>
                                <td className="py-4 px-4">
                                    <ul className="list-disc pl-5 space-y-1">
                                        {item.actions.map((action, idx) => (
                                            <li key={idx} className="text-sm">
                                                {action}
                                            </li>
                                        ))}
                                    </ul>
                                </td>
                                <td className="py-4 px-4 text-purple-600 font-medium">
                                    Q2 2025
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

export default function ReportPage() {
    const [reportId, setReportId] = useState(null);
    const [report, setReport] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [pdfUrl, setPdfUrl] = useState(null);
    const [opened, { open, close }] = useDisclosure(false);
    const defaultLayoutPluginInstance = defaultLayoutPlugin();

    useEffect(() => {
        const fetchData = async () => {
            try {
                const reportData = await fetchReportData();
                setReportId(reportData.report.id);
                setReport(reportData.report.data);
            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };
        fetchData();
    }, []);

    // Clean up blob URL when component unmounts
    useEffect(() => {
        return () => {
            if (pdfUrl) {
                window.URL.revokeObjectURL(pdfUrl);
            }
        };
    }, [pdfUrl]);

    return (
        <div className="w-full flex-col items-center gap-6">
            {loading ? (
                <div className="w-full flex justify-center items-center h-[80vh]">
                    <Loader size="xl" />
                </div>
            ) : error ? (
                <div className="flex justify-center items-center w-full h-[80vh] text-red-600/40 font-semibold text-4xl">
                    {error}
                </div>
            ) : (
                <div className="flex flex-col gap-6">
                    {/* Progress bar and action buttons */}
                    <div className="w-full flex flex-col py-6 px-8 gap-6 bg-white rounded-2xl">
                        {/* Action buttons */}
                        <div className="flex items-center justify-between w-full">
                            <div className="flex items-center gap-6">
                                <button
                                    onClick={() =>
                                        generatePDFPreview(
                                            reportId,
                                            setPdfUrl,
                                            open
                                        )
                                    }
                                    className="px-4 py-2 rounded-xl bg-transparent border-2 border-primary text-primary hover:bg-primary hover:text-white transition-all duration-200 cursor-pointer font-semibold text-xl flex items-center gap-2 group"
                                >
                                    <div className="w-6 relative">
                                        <span className="transition-all duration-150 hover:duration-300 group-hover:scale-105 opacity-100 group-hover:opacity-0 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                                            <FaRegEye size={24} />
                                        </span>
                                        <span className="transition-all duration-150 hover:duration-300 group-hover:scale-105 opacity-0 group-hover:opacity-100 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                                            <FaRegEye6 size={24} />
                                        </span>
                                    </div>{" "}
                                    View Report
                                </button>
                                <button
                                    onClick={() =>
                                        exportAndDownloadReportPDF(reportId)
                                    }
                                    className="px-4 py-2 rounded-xl bg-primary border-2 border-primary hover:border-secondary-300 hover:bg-secondary-300 text-white transition-all duration-200 cursor-pointer font-semibold text-xl flex items-center gap-2 group"
                                >
                                    <div className="w-6 relative">
                                        <span className="transition-all duration-150 hover:duration-300 group-hover:translate-y-[calc(-50%+2px)] opacity-100 group-hover:opacity-0 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                                            <TbDownload size={24} />
                                        </span>
                                        <span className="transition-all duration-150 hover:duration-300 group-hover:translate-y-[calc(-50%+2px)] opacity-0 group-hover:opacity-100 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                                            <IoArrowDownOutline size={24} />
                                        </span>
                                    </div>{" "}
                                    Export & Download Report
                                </button>
                            </div>
                        </div>
                    </div>

                    <OverallAlignmentScore
                        score={report.assessment_score}
                        grade={report.grade}
                        status={report.status}
                    />

                    <div className="flex w-full gap-6">
                        <AreasForImprovement sections={report.sections} />
                        <ComplianceStatus />
                    </div>

                    <SectionScores sections={report.sections} />

                    <div className="flex w-full gap-6">
                        <AreasForImprovementSpiderChar
                            sections={report.sections}
                        />
                        <Progress />
                    </div>

                    <GapAnalysis sections={report.sections} />

                    <PriorityActions
                        priority_actions={report.priority_actions}
                    />

                    <Modal
                        size="95%"
                        opened={opened}
                        onClose={() => {
                            close();
                            if (pdfUrl) {
                                window.URL.revokeObjectURL(pdfUrl);
                                setPdfUrl(null);
                            }
                        }}
                        withCloseButton
                        scrollAreaComponent={ScrollArea.Autosize}
                        className="rounded-2xl"
                    >
                        <ScrollArea.Autosize mah={800} type="scroll">
                            <div className="w-full h-[70vh]">
                                {pdfUrl ? (
                                    <Worker workerUrl="https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js">
                                        <Viewer
                                            fileUrl={pdfUrl} // Pass Blob URL
                                            plugins={[
                                                defaultLayoutPluginInstance,
                                            ]}
                                        />
                                    </Worker>
                                ) : (
                                    <div className="w-full h-full flex justify-center items-center">
                                        <Loader size="xl" />
                                    </div>
                                )}
                            </div>
                        </ScrollArea.Autosize>
                    </Modal>
                </div>
            )}
        </div>
    );
}
