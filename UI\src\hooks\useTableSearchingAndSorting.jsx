import { useState, useEffect } from "react";

const useTableSearchingAndSorting = (selectedScope) => {
  const [selection, setSelection] = useState([]);
  const [sortedData, setSortedData] = useState([]);
  const [search, setSearch] = useState("");
  const [sortDirection, setSortDirection] = useState("asc");
  const [reverseSortDirection, setReverseSortDirection] = useState(false);
  const [edit, setEdit] = useState(false);
  const [value, setValue] = useState([]);

  useEffect(() => {
    setSortedData(selectedScope?.topics);
    setEdit(false);
  }, [selectedScope]);

  const toggleRow = (rowId) => {
    setSelection((prevSelection) => {
      const isSelected = prevSelection.includes(rowId);
      return isSelected
        ? prevSelection.filter((id) => id !== rowId)
        : [...prevSelection, rowId];
    });
  };

  const toggleAll = () => {
    const allIds = selectedScope?.topics?.map((item) => item.id);
    setSelection((current) => (current.length === allIds.length ? [] : allIds));
  };

  const handleSearchChange = (event) => {
    const value = event.target.value;
    setSearch(value);
  
    if (value === "") {
      setSortedData(selectedScope?.topics || []);
      return;
    }
  
    const filteredData = (selectedScope?.topics || []).filter(
      (item) =>
        item.name.toLowerCase().includes(value.toLowerCase()) ||
        item.questions.some((question) =>
          question.questionText.toLowerCase().includes(value.toLowerCase())
        )
    );
  
    setSortedData(filteredData);
  };
  
  const handleSort = (columnKey) => {
    const sorted = [...sortedData].sort((a, b) => {
      if (sortDirection === "asc") {
        return a[columnKey].localeCompare(b[columnKey]);
      } else {
        return b[columnKey].localeCompare(a[columnKey]);
      }
    });

    setSortedData(sorted);
    setSortDirection(sortDirection === "asc" ? "desc" : "asc");
  };

  return {
    selection,
    sortedData,
    search,
    sortDirection,
    reverseSortDirection,
    edit,
    value,
    toggleRow,
    toggleAll,
    handleSearchChange,
    handleSort,
    setEdit,
    setValue,
    setReverseSortDirection,
  };
};

export default useTableSearchingAndSorting;
