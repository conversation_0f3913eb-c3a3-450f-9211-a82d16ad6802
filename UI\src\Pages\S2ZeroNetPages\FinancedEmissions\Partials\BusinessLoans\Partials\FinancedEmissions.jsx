import Api_PCAF from '@/Api/apiS2_PCAF';
import { useBusinessLoansStore } from '@/Store/useBusinessLoansStore';
import { Spinner } from '@react-pdf-viewer/core';
import React from 'react';
import { BsStars } from "react-icons/bs";
import axios from "axios";
import Cookies from 'js-cookie';
import { set } from 'react-hook-form';

const FinancedEmissions = () => {
    const {
        data_quality_score,
        loan_amount,
        borrower_name,
        borrower_industry,
        borrower_assets,
        borrower_emissions,
        emission_scope,
        loading,
        estimate_borrower_assets_loading,
        estimate_borrower_emissions_loading,

        setLoanAmount,
        setBorrowerName,
        setBorrowerIndustry,
        setBorrowerAssets,
        setBorrowerEmissions,
        setDataQualityScore,
        setEmissionScope,
        setResults,
        setLoading,
        setBorrowerEmissionsLoading,
        setBorrowerAssetsLoading

    } = useBusinessLoansStore();

    const handleCalculate = async () => {
        setLoading(true);
        Api_PCAF.post("/business-loans", {
            "loan_amount": loan_amount,
            "borrower_name": borrower_name,
            "borrower_industry": borrower_industry,
            "borrower_assets": borrower_assets,
            "borrower_emissions": borrower_emissions,
            "emission_scope": emission_scope,
            "data_quality_score": data_quality_score
        }).then((res) => {
            setLoading(false);
            const financedEmissions = res.data;
            setResults(financedEmissions);
        }).catch((error) => {
            setLoading(false);
        });
    }
    const EstimateBorrowAssets = async ()=>{
        const questions = getAnsweredQuestions();
        setBorrowerAssetsLoading(true);
        axios.post("https://gen-ai0-staging.azurewebsites.net/process_request",
            {
                "processor": "pcaf",
                "resources":{
                    "qa_context": questions,
                    "estimation_needed": "Total Borrower's Assets"
                }
            },
            {
                headers:{ "Authorization": `Bearer ${Cookies.get("level_user_token")}`}
            }
        ).then((res)=>{
            setBorrowerAssetsLoading(false);
            setBorrowerAssets(res.data.ai_response.value)
        }).catch((err)=>{
            setBorrowerAssetsLoading(false);
            console.log(err);
        })
    }
    const EstimateBorrowEmmision = async ()=>{
        const questions = getAnsweredQuestions();
        setBorrowerEmissionsLoading(true);
        axios.post("https://gen-ai0-staging.azurewebsites.net/process_request",
            {
                "processor": "pcaf",
                "resources":{
                    "qa_context": questions,
                    "estimation_needed": "Borrower's Total Emissions (tCO2e)"
                }
            },
            {
                headers:{ "Authorization": `Bearer ${Cookies.get("level_user_token")}`}
            }
        ).then((res)=>{
            setBorrowerEmissionsLoading(false);
            setBorrowerEmissions(res.data.ai_response.value)
        }).catch((err)=>{
            setBorrowerEmissionsLoading(false);
            console.log(err);
        })
    }

    const getAnsweredQuestions = ()=>{
        let questions = []

        if (loan_amount !== "") {
            questions.push({
                question: "Outstanding Loan Amount",
                user_answer: loan_amount
            });
        }
        if (borrower_name !== "") {
            questions.push({
                question: "Borrower Name",
                user_answer: borrower_name
            });
        }
        if (borrower_industry !== "") {
            questions.push({
                question: "Borrower Industry",
                user_answer: borrower_industry
            });
        }
        if (borrower_assets !== "") {
            questions.push({
                question: "Total Borrower's Assets",
                user_answer: borrower_assets
            });
        }
        if (borrower_emissions !== "") {
            questions.push({
                question: "Borrower's Emissions",
                user_answer: borrower_emissions
            });
        }
        if (emission_scope !== "") {
            questions.push({
                question: "Emission Scope",
                user_answer: emission_scope
            });
        }
        if(data_quality_score !== "") {
            questions.push({
                question: "Data Quality Score",
                user_answer: data_quality_score
        });
        }
        return questions;
    }
  return (
    <div>
        <div className='flex flex-col gap-4 rounded-xl bg-white p-4 border-[#E8E7EA] border-2'>
            <h1 className='flex justify-center text-center bg-[#E6F3F4] text-[#8F8F8F] p-2 rounded-xl'>
            Financed Emissions = (Outstanding Loan Amount / Total Borrower&apos;s Assets) * Borrower&apos;s Total Emissions
            </h1>
            <table className="w-full">
            <thead className="bg-[#F5F4F5]">
                <tr className='font-bold'>
                <th className="text-start p-3 ">Parameter</th>
                <th className="text-start p-3 ">Value</th>
                <th className="text-start p-3 ">AI Assistant</th>
                </tr>
            </thead>
            <tbody>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Outstanding Loan Amount</td>
                    <td className="p-3 border border-gray-300">
                        <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="e.g., 500,000" onChange={(e) => setLoanAmount(e.target.value)} value={loan_amount}/>
                    </td>
                    <td className="p-3 border border-gray-300">Required Input</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Borrower Name</td>
                    <td className="p-3 border border-gray-300">
                        <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="e.g., XYZ Corporation" onChange={(e) => setBorrowerName(e.target.value)} value={borrower_name}/>
                    </td>
                    <td className="p-3 border border-gray-300">For AI Assist</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Borrower Industry</td>
                    <td className="p-3 border border-gray-300">
                    <select type="text" className="border border-gray-400 rounded-lg p-2 w-full" onChange={(e) => setBorrowerIndustry(e.target.value)} value={borrower_industry}>
                            <option value="Manufacturing" className='text-gray-400'>Manufacturing</option>
                            <option value="Technology">Technology</option>
                            <option value="Finance">Finance</option>
                        </select>
                    </td>
                    <td className="p-3 border border-gray-300">Required</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Total Borrower&apos;s Assets</td>
                    <td className="p-3 border border-gray-300">
                        <input disabled={estimate_borrower_assets_loading} type="text" className={"border border-gray-400 rounded-lg p-2 w-full" + (estimate_borrower_assets_loading ? " cursor-not-allowed text-gray-500" : "")} placeholder="e.g., 500,000" onChange={(e) => setBorrowerAssets(e.target.value)} value={borrower_assets}/>
                    </td>
                    <td className="p-3 border border-gray-300">
                        <button disabled={estimate_borrower_assets_loading} className='flex flex-row items-center justify-center gap-2 rounded-md bg-[#1C889C] text-[#fff] p-2 w-full' onClick={()=>{EstimateBorrowAssets()}}>
                            {estimate_borrower_assets_loading ? <Spinner size='24px' /> : <BsStars /> } Estimate
                        </button>
                    </td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Borrower&apos;s Total Emissions (tCO2e)</td>
                    <td className="p-3 border border-gray-300">
                        <input disabled={estimate_borrower_emissions_loading} type="text" className={"border border-gray-400 rounded-lg p-2 w-full" + (estimate_borrower_emissions_loading ? " cursor-not-allowed text-gray-500" : "")} placeholder="e.g., 20,000" onChange={(e) => setBorrowerEmissions(e.target.value)} value={borrower_emissions}/>
                    </td>
                    <td className="p-3 border border-gray-300">
                        <button disabled={estimate_borrower_emissions_loading} className='flex flex-row items-center justify-center gap-2 rounded-md bg-[#1C889C] text-[#fff] p-2 w-full' onClick={()=>{EstimateBorrowEmmision()}} >
                            {estimate_borrower_emissions_loading ? <Spinner size='24px' /> : <BsStars /> } Estimate
                        </button>
                    </td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Emission Scope</td>
                    <td className="p-3 border border-gray-300">
                    <select type="text" className="border border-gray-400 rounded-lg p-2 w-full" onChange={(e) => setEmissionScope(e.target.value)} value={emission_scope}>
                            <option value="Scope 1 & 2" className='text-gray-400'>Scope 1 & 2</option>
                            <option value="Scope 1">Scope 1</option>
                            <option value="Scope 2">Scope 2</option>
                        </select>
                    </td>
                    <td className="p-3 border border-gray-300"></td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Data Quality Score</td>
                    <td className="p-3 border border-gray-300">
                        <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="3 - Sector average emissions Number" onChange={(e) => setDataQualityScore(e.target.value)} value={data_quality_score}/>
                    </td>
                    <td className="p-3 border border-gray-300">Auto-updates</td>
                </tr>
            </tbody>
            </table>

            <button disabled={loading} className='flex w-full justify-center text-center text-base font-semibold bg-[#07838F] text-[#ffffff] p-2 rounded-lg' onClick={handleCalculate}>
                {
                    loading ? <Spinner size='24px' /> : "Calculate Financed Emissions"
                }
            </button>
        </div>
    </div>
  )
}

export default FinancedEmissions