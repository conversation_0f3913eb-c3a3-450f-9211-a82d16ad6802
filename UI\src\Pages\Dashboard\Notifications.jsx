// Dashboard.jsx
import { useEffect, useState } from "react";
import useDashboardStore from "./useDashboardStore";
import MainLayout from "@/Layout/MainLayout";
import TasksTab from "./components/TasksTab";
import CommentsTab from "./components/CommentsTab";
import { LoadingOverlay } from "@mantine/core";

export default function Dashboard() {
    const {
        user,
        tasks,
        comments,
        taskStats,
        loading,
        error,
        initializeDashboard,
        fetchDashboardData,
    } = useDashboardStore();
    const [activeTabIndex, setActiveTabIndex] = useState(0);

    useEffect(() => {
        const fetchData = async () => {
            const result = await initializeDashboard();

            if (result) {
                // Dashboard initialized successfully
                console.log("Dashboard initialized");
            }
        };

        fetchData();
    }, []);

    // Format task priority list for TasksTab
    const taskPriorityList = tasks.map((task) => ({
        name: task.taskName,
        category: task.groupName,
        progress:
            taskStats.totalTasks > 0
                ? Math.round((taskStats.finished / taskStats.totalTasks) * 100)
                : 0,
        date: new Date(task.dueDate || Date.now()),
        status: task.dueDate
            ? new Date(task.dueDate) < new Date()
                ? "Past Due"
                : "Upcoming"
            : "Incomplete",
    }));

    // Create tabs with proper data
    const tabs = [
        {
            name: "My Tasks",
            notification_number: taskStats.totalTasks,
            component: <TasksTab tasks={taskPriorityList} />,
        },
        {
            name: "My Comments",
            notification_number: comments.length,
            component: <CommentsTab comments={comments} />,
        },
    ];

    return (
        <MainLayout navbarTitle="Dashboard">
            <div className="w-full h-[80%] overflow-hidden pr-2 overflow-y-auto flex flex-col items-center gap-6 ">
                {/* Welcome Section */}
                <div className="rounded-lg border-[#E8E7EA] dark:border-black border bg-white p-4 w-full dark:bg-[#282828] dark:text-white">
                    {loading ? (
                        <LoadingOverlay />
                    ) : error ? (
                        <div>Error: {error}</div>
                    ) : (
                        <>
                            <h1 className="text-2xl font-bold mb-2">
                                Welcome,{" "}
                                <span className="text-[#1C889C]">
                                    {user?.userName || "Guest"}
                                </span>
                            </h1>
                            <p className="text-[#8D8D8D]">
                                You have {taskStats.pastDue} tasks past due and{" "}
                                {taskStats.incomplete} tasks to complete.
                            </p>

                            {/* Task Summary Cards */}
                            <div className="mt-4 grid grid-cols-4 w-full ">
                                {[
                                    {
                                        name: "Total Tasks",
                                        value: taskStats.totalTasks,
                                        change: `${
                                            taskStats.totalTasks > 0
                                                ? Math.round(
                                                      (taskStats.finished /
                                                          taskStats.totalTasks) *
                                                          100
                                                  )
                                                : 0
                                        }% complete`,
                                        type: "normal",
                                    },
                                    {
                                        name: "Finished",
                                        value: taskStats.finished,
                                        change: `${
                                            taskStats.totalTasks > 0
                                                ? Math.round(
                                                      (taskStats.finished /
                                                          taskStats.totalTasks) *
                                                          100
                                                  )
                                                : 0
                                        }% complete`,
                                        type: "good",
                                    },
                                    {
                                        name: "Upcoming",
                                        value: taskStats.upcoming,
                                        change: `${
                                            taskStats.totalTasks > 0
                                                ? Math.round(
                                                      (taskStats.upcoming /
                                                          taskStats.totalTasks) *
                                                          100
                                                  )
                                                : 0
                                        }% of total`,
                                        type: "normal",
                                    },
                                    {
                                        name: "Past Due",
                                        value: taskStats.pastDue,
                                        change: `${
                                            taskStats.totalTasks > 0
                                                ? Math.round(
                                                      (taskStats.pastDue /
                                                          taskStats.totalTasks) *
                                                          100
                                                  )
                                                : 0
                                        }% of total`,
                                        type: "bad",
                                    },
                                ].map((task, index) => (
                                    <div
                                        key={index}
                                        className={`border-[#E8E7EA] border p-4  dark:border-black ${
                                            index === 0
                                                ? "rounded-l-lg border-r-0"
                                                : index === 3
                                                ? "rounded-r-lg"
                                                : "border-r-0"
                                        }`}
                                    >
                                        <h2 className="text-[#7A7A7A]">
                                            {task.name}
                                        </h2>
                                        <div className="flex items-end">
                                            <span className="text-3xl font-bold mr-2">
                                                {task.value}
                                            </span>
                                            <span
                                                className={`text-sm -translate-y-1 ${
                                                    task.type === "good"
                                                        ? "text-[#00C0A9]"
                                                        : task.type === "bad"
                                                        ? "text-[#EA0B0B]"
                                                        : "text-[#6B7280]"
                                                }`}
                                            >
                                                {task.change}
                                            </span>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </>
                    )}
                </div>

                {/* Tabs Section */}
                <div className="rounded-lg flex justify-start items-center gap-4 border-[#E8E7EA] border bg-white  dark:bg-[#282828] dark:border-black  dark:text-white p-2 w-full">
                    {loading ? (
                        <LoadingOverlay />
                    ) : error ? (
                        <div>Error: {error}</div>
                    ) : (
                        tabs.map((tab, index) => (
                            <div
                                key={index}
                                onClick={() => setActiveTabIndex(index)}
                                className={`rounded-lg cursor-pointer transition-all duration-300 p-1 px-2 flex gap-2 text-[#07838F] ${
                                    index === activeTabIndex
                                        ? "bg-[#07838F1A] font-semibold"
                                        : "bg-transparent font-normal"
                                }`}
                            >
                                <span className="capitalize transition-all duration-300">
                                    {tab.name}
                                </span>
                                <span className="bg-[#07838F33] rounded-full px-3 text-xs flex justify-center items-center font-normal">
                                    {tab.notification_number}
                                </span>
                            </div>
                        ))
                    )}
                </div>

                {/* Active Tab View */}
                {loading ? (
                    <LoadingOverlay />
                ) : error ? (
                    <div>Error: {error}</div>
                ) : (
                    tabs[activeTabIndex].component
                )}
            </div>
        </MainLayout>
    );
}
