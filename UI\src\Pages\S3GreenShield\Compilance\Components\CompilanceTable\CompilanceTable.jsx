import {
  Badge,
  Checkbox,
  rem,
  <PERSON><PERSON><PERSON><PERSON>,
  Select,
  Table
} from "@mantine/core";
import cx from "clsx";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { BsFillExclamationCircleFill } from "react-icons/bs";
import { FaArrowDown } from "react-icons/fa6";
import { IssbData } from "./tableData";

const DueDiligenceTable = () => {
  const [selection, setSelection] = useState(["2"]);
  const [selectedReadinessLevel, setSelectedReadinessLevel] = useState({});
  const [selectedPriorityState, setSelectedPriorityState] = useState({});
  const [selecteYesOrNo, setSelecteYesOrNo] = useState({});

  // const readinessColorMap = {
  //   "Not started": {
  //     bg: "rgba(171, 2, 2, 0.13)",
  //     border: "rgba(171, 2, 2, 1)",
  //     text: "rgba(171, 2, 2, 1)",
  //   },
  //   "Initial Planning": {
  //     bg: "#FF00B81A",
  //     border: "#FF00B8",
  //     text: "#FF00B8",
  //   },
  //   "In Development": {
  //     bg: "rgba(145, 96, 193, 0.2)",
  //     border: "rgba(145, 96, 193, 1)",
  //     text: "rgba(145, 96, 193, 1)",
  //   },
  //   "Partially Implemented": {
  //     bg: "rgba(41, 139, 237, 0.2)",
  //     border: "rgba(41, 139, 237, 1)",
  //     text: "rgba(41, 139, 237, 1)",
  //   },
  //   "Fully Implementedeted": {
  //     bg: "rgba(1, 189, 54, 0.2)",
  //     border: "rgba(1, 189, 54, 1)",
  //     text: "rgba(1, 189, 54, 1)",
  //   },
  // };

  const prioritySelectColorMap = {
    Top: {
      bg: "rgba(171, 2, 2, 0.2)",
      border: "rgba(171, 2, 2, 1)",
      text: "rgba(171, 2, 2, 1)",
    },
    High: {
      bg: "rgba(255, 171, 7, 0.2)",
      border: "rgba(255, 171, 7, 1)",
      text: "rgba(255, 171, 7, 1)",
    },
    Medium: {
      bg: "rgba(0, 192, 169, 0.2)",
      border: "rgba(0, 192, 169, 1)",
      text: "rgba(0, 192, 169, 1)",
    },
    low: {
      bg: "#01BD3638",
      border: "rgba(1, 189, 54, 1)",
      text: "rgba(1, 189, 54, 1)",
    },
  };
  const priorityBadgeColorMap = {
    Top: 'rgba(171, 2, 2, 1)',
    Extreme: "rgba(171, 2, 2)",
    High: "rgba(255, 171, 7,1)",
    Medium: "rgba(0, 192, 169, 1)",
    low: "rgba(1, 189, 54, 1)"
  };
  const yestNoMap = {
    Yes: 'rgba(0, 192, 169, 1)',
    No: "rgba(171, 2, 2)",
  };

  const toggleRow = (id) =>
    setSelection((current) =>
      current.includes(id)
        ? current.filter((item) => item !== id)
        : [...current, id]
    );

  const toggleAll = () =>
    setSelection((current) =>
      current.length === IssbData.length ? [] : IssbData.map((item) => item.id)
    );

  const handleSelectReadinessChange = (id, value) => {
    setSelectedReadinessLevel((prev) => ({ ...prev, [id]: value }));
  };

  const handleSelectPriorityChange = (rowId, value) => {
    setSelectedPriorityState((prev) => ({ ...prev, [rowId]: value }));
  };

  const handleSelectYesOrNo = (rowId, value) => {
    setSelecteYesOrNo((prev) => ({ ...prev, [rowId]: value }));
  };

  const readinessColorMap = {
    "5.	Progress over 75% ": {
      bg: "rgba(171, 2, 2, 0.13)",
      border: "rgba(171, 2, 2, 1)",
      text: "rgba(171, 2, 2, 1)",
    },
    // "Initial Planning": {
    //   bg: "rgba(255, 171, 7, 0.21)",
    //   border: "rgba(255, 171, 7, 1)",
    //   text: "rgba(255, 171, 7, 1)",
    // },
    "3.	Progress of 25-50% ": {
      bg: "rgba(145, 96, 193, 0.2)",
      border: "rgba(145, 96, 193, 1)",
      text: "rgba(145, 96, 193, 1)",
    },
    // "Partially Implemented": {
    //   bg: "rgba(41, 139, 237, 0.2)",
    //   border: "rgba(41, 139, 237, 1)",
    //   text: "rgba(41, 139, 237, 1)",
    // },
    "4.	Progress of 50-75% ": {
      bg: "rgba(1, 189, 54, 0.2)",
      border: "rgba(1, 189, 54, 1)",
      text: "rgba(1, 189, 54, 1)",
    },
  };

  const yesno = {
    Yes: {
      bg: "rgba(0, 192, 169, 0.2)",
      border: "rgba(0, 192, 169, 1)",
      text: "rgba(0, 192, 169, 0.2)",
    },
    No: {
      bg: "rgba(255, 171, 7, 0.21)",
      border: "rgba(255, 171, 7, 1)",
      text: "rgba(255, 171, 7, 1)",
    },
  };

  const rows = IssbData.flatMap((item) => {
    return item.questions.map((question, idx) => {
      const selected = selection.includes(item.id);
      const rowId = `${item.id}-${idx}`;

      return (
        <Table.Tr
          key={rowId}
          className={cx({
            ["bg-[#07838F1A]"]: selected,
          })}
        >
          {/* select/deselect column */}
          <Table.Td>
            <Checkbox
              checked={selection.includes(item.id)}
              onChange={() => toggleRow(item.id)}
              color="#07838F"
            />
          </Table.Td>

          {/* Topic Area column */}
          <Table.Td className="text-left">{item.topicTitle}</Table.Td>
          {/* Topic question column */}
          <Table.Td className="md:w-60 w-60 text-left">{question}</Table.Td>
          
          <Table.Td>
            <div className="flex justify-center items-center">
              <Badge
                variant="dot"
                className="h-[35px] w-[100px] "
                color={
                  yestNoMap[selecteYesOrNo[rowId]] || "gray"
                }
                size="md"
                style={{
                  backgroundColor:
                  yesno[selecteYesOrNo[rowId]]?.bg ||
                    "rgba(0, 0, 0, 0.1)",
                  color:
                  yesno[selecteYesOrNo[rowId]]?.text ||
                    "black",
                  fontWeight: "500",
                }}
              >
                <Select
                  value={selecteYesOrNo[rowId] || ""}
                  onChange={(value) => handleSelectYesOrNo(rowId, value)}
                  data={Object.keys(yestNoMap)}
                  variant="unstyled"
                  radius="xl"
                  size="xs"
                  placeholder="Yes"
                  rightSectionWidth="0"
                  withCheckIcon={false}
                  allowDeselect={false}
                  classNames={{
                    root: "",
                    input: "text-center w-20",
                  }}
                  styles={(theme) => ({
                    input: {
                      color:
                        yestNoMap[selecteYesOrNo[rowId]]
                          ?.text || "black",
                      fontSize: "14px",
                      fontWeight: "500",
                    },
                    dropdown: {
                      backgroundColor: "white",
                    },
                  })}
                />
              </Badge>

            </div>
          </Table.Td>
          <Table.Td className="flex text-center h-[100px] items-center justify-center">
            {item.hasRedFlag == true ? (
              <BsFillExclamationCircleFill className="w-[20px] h-[20px] text-secondary-danger-100" />
            ) : (
              "-"
            )}
          </Table.Td>

          <Table.Td>
            a) Implement verified carbon offset program
            <br />
            b) Obtain third-party certification
            <br />
            c) xxx
          </Table.Td>

          <Table.Td>
            <Select
              value={selectedReadinessLevel[rowId] || ""}
              onChange={(value) => handleSelectReadinessChange(rowId, value)}
              radius="xl"
              classNames={{
                root: "w-fit",
                input: "text-center",
              }}
              size="xs"
              withCheckIcon={false}
              allowDeselect={false}
              rightSectionWidth="0"
              placeholder="Select your Score"
              data={Object.keys(readinessColorMap)}
              styles={(theme) => ({
                input: {
                  backgroundColor:
                    readinessColorMap[selectedReadinessLevel[rowId]]?.bg ||
                    theme.colors.gray[0],
                  color:
                    readinessColorMap[selectedReadinessLevel[rowId]]?.text ||
                    "black",
                  border: `1px solid ${
                    readinessColorMap[selectedReadinessLevel[rowId]]?.border ||
                    theme.colors.gray[3]
                  }`,
                  padding: "16px 12px",
                  borderRadius: "15px",
                  fontSize: "14px",
                  fontWeight: "500",
                  boxShadow: `0 2px 4px rgba(0, 0, 0, 0.1)`,
                },
                dropdown: {
                  backgroundColor: "white",
                },
                item: {
                  "&[data-selected]": {
                    backgroundColor:
                      readinessColorMap[selectedReadinessLevel[rowId]]?.bg ||
                      theme.colors.gray[0],
                    color:
                      readinessColorMap[selectedReadinessLevel[rowId]]?.text ||
                      "black",
                    opacity: 1,
                  },
                },
              })}
            />
          </Table.Td>

          <Table.Td>
            <Badge
              variant="dot"
              className="px-4 h-[35px]"
              color={
                priorityBadgeColorMap[selectedPriorityState[rowId]] || "gray"
              }
              size="md"
              style={{
                backgroundColor:
                  prioritySelectColorMap[selectedPriorityState[rowId]]?.bg ||
                  "rgba(0, 0, 0, 0.1)",
                color:
                  prioritySelectColorMap[selectedPriorityState[rowId]]?.text ||
                  "black",
                fontWeight: "500",
              }}
            >
              <Select
                value={selectedPriorityState[rowId] || ""}
                onChange={(value) => handleSelectPriorityChange(rowId, value)}
                data={Object.keys(prioritySelectColorMap)}
                variant="unstyled"
                radius="xl"
                size="xs"
                placeholder="Priority"
                rightSectionWidth="0"
                withCheckIcon={false}
                allowDeselect={false}
                classNames={{
                  root: "",
                  input: "text-center w-20",
                }}
                styles={(theme) => ({
                  input: {
                    color:
                      prioritySelectColorMap[selectedPriorityState[rowId]]
                        ?.text || "black",
                    fontSize: "14px",
                    fontWeight: "500",
                  },
                  dropdown: {
                    backgroundColor: "white",
                  },
                })}
              />
            </Badge>
          </Table.Td>

          {/* Evidence/Notes column */}
          <Table.Td>Jane Doe</Table.Td>
          <Table.Td>
            {item.due_date.map((d, i) => (
              <h4 key={i}>{d}</h4>
            ))}
          </Table.Td>
          <Table.Td className="text-center">ISO 14064-3</Table.Td>
        </Table.Tr>
      );
    });
  });

  const { t } = useTranslation();

  return (
    <>
      <div className="p-2 my-1 bg-white shadow-xl border-[0.90px]">
        <ScrollArea h={400}>
          <Table
            miw={1800}
            stickyHeader
            stickyHeaderOffset={1}
            verticalSpacing="sm"
            className="scrollable-container"
          >
            <Table.Thead className="pb-6 text-base font-thin">
              <Table.Tr className="text-secondary-500">
                <Table.Th style={{ width: rem(40) }}>
                  <Checkbox
                    onChange={toggleAll}
                    checked={selection.length === IssbData.length}
                    indeterminate={
                      selection.length > 0 &&
                      selection.length !== IssbData.length
                    }
                    color="#07838F"
                  />
                </Table.Th>

                <Table.Th className="text-center">
                  <h1 className="flex items-center justify-start gap-3 ">
                    {t("Category")}{" "}
                    <FaArrowDown className="ms-1 text-[#00C0A9]" />
                  </h1>
                </Table.Th>
                <Table.Th className="text-center w-1/5">
                  <h1 className="flex items-center justify-start gap-3 ">
                    {t("Question")}{" "}
                    <FaArrowDown className="ms-1 text-[#00C0A9]" />
                  </h1>
                </Table.Th>
                <Table.Th className="text-center w-1/6">
                  <h1 className="flex items-center justify-center gap-3 ">
                    {t("YES / NO")}{" "}
                    <FaArrowDown className="ms-1 text-[#00C0A9]" />
                  </h1>
                </Table.Th>
                <Table.Th className="text-center w-[10%]">
                  <h1 className="flex items-center justify-start gap-3 ">
                    {t("Red Flag")}{" "}
                    <FaArrowDown className="ms-1 text-[#00C0A9]" />
                  </h1>
                </Table.Th>
                <Table.Th className="text-center w-1/5">
                  <h1 className="flex items-center justify-start gap-3 ">
                    {t("actionItems")}{" "}
                    <FaArrowDown className="ms-1 text-[#00C0A9]" />
                  </h1>
                </Table.Th>
                <Table.Th className="text-center w-1/5">
                  <h1 className="flex items-center justify-center gap-3 ">
                    {t("Impact Score")}{" "}
                    <FaArrowDown className="ms-1 text-[#00C0A9]" />
                  </h1>
                </Table.Th>
                <Table.Th className="text-center w-1/5">
                  <h1 className="flex items-center justify-center gap-3 ">
                    {t("priority")}{" "}
                    <FaArrowDown className="ms-1 text-[#00C0A9]" />
                  </h1>
                </Table.Th>
                <Table.Th className="text-center w-1/5">
                  <h1 className="flex items-center justify-start gap-3 ">
                    {t("owner")} <FaArrowDown className="ms-1 text-[#00C0A9]" />
                  </h1>
                </Table.Th>
                <Table.Th className="text-center w-[30%]">
                  <h1 className="flex items-center w-[150px] justify-start gap-3 ">
                    {t("dueDate")}{" "}
                    <FaArrowDown className="ms-1 text-[#00C0A9]" />
                  </h1>
                </Table.Th>
                <Table.Th className="text-center ">
                  <h1 className="flex items-center w-[200px] justify-start gap-3 ">
                    {t("Regulatory Reference ")}{" "}
                    <FaArrowDown className="ms-1 text-[#00C0A9]" />
                  </h1>
                </Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody className="text-base font-semibold text-gray-600">
              {rows}
            </Table.Tbody>
          </Table>
        </ScrollArea>
      </div>
    </>
  );
};

export default DueDiligenceTable;
