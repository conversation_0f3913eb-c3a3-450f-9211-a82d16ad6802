{"name": "levelup", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "concurrently \"npm run dev\" \"node server.js\"", "dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@mantine/carousel": "^7.17.5", "@mantine/charts": "^7.11.0", "@mantine/core": "^7.17.5", "@mantine/dates": "^7.11.0", "@mantine/dropzone": "^7.17.5", "@mantine/form": "^7.11.1", "@mantine/hooks": "^7.11.0", "@mantine/notifications": "^7.11.1", "@mdi/js": "^7.4.47", "@radix-ui/react-accordion": "^1.2.3", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@table-library/react-table-library": "^4.1.7", "@tabler/icons-react": "^3.11.0", "aos": "^2.3.4", "apexcharts": "^4.1.0", "axios": "^1.6.7", "chart.js": "^4.4.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "docx": "^9.3.0", "driver": "^1.0.0", "driver.js": "^1.3.6", "emailjs": "^4.0.3", "embla-carousel-react": "^8.6.0", "file-saver": "^2.0.5", "formik": "^2.4.6", "framer-motion": "^12.10.5", "html-to-image": "^1.10.8", "i18next": "^23.15.0", "i18next-browser-languagedetector": "^8.0.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lucide-react": "^0.484.0", "mammoth": "^1.9.0", "moment": "^2.30.1", "motion": "^12.12.1", "nanoid": "^5.0.7", "next-themes": "^0.4.6", "pdfjs-dist": "^3.11.174", "powerbi-client": "^2.23.1", "powerbi-client-react": "^1.4.0", "prom-client": "^15.1.3", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-draggable": "^4.4.6", "react-dropzone": "^14.2.3", "react-hook-form": "^7.54.2", "react-i18next": "^15.0.1", "react-icons": "^5.0.1", "react-joyride": "^2.9.3", "react-lazy-load-image-component": "^1.6.3", "react-pdf": "^9.2.1", "react-player": "^2.16.0", "react-router": "^6.22.1", "react-router-dom": "^6.22.3", "react-select": "^5.10.1", "react-select-country-list": "^2.2.3", "react-slick": "^0.30.2", "react-toastify": "^10.0.4", "recharts": "^2.12.7", "rsuite": "^5.74.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sweetalert2": "^11.6.13", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vite": "^6.2.2", "world-countries": "^5.1.0", "xlsx": "^0.18.5", "zustand": "^5.0.3"}, "devDependencies": {"@types/react": "^18.3.20", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "concurrently": "^9.1.0", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "http-proxy-middleware": "^3.0.0", "postcss": "^8.4.38", "postcss-preset-mantine": "^1.15.0", "postcss-simple-vars": "^7.0.1", "tailwindcss": "^3.4.1"}}