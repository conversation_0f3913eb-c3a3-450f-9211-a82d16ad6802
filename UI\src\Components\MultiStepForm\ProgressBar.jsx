export function ProgressBar({ completed, total }) {
  const progressPercentage = (completed / total) * 100;

  return (
    <div className="w-full flex items-center mx-auto">
      <div className="h-3 w-full bg-gray-200 rounded">
        <div
          className="h-full bg-[#07838F] rounded transition-all duration-300"
          style={{ width: `${progressPercentage}%` }}
        ></div>
      </div>
      <div className="px-2 flex gap-2 items-center text-sm font-medium text-[#939393]">
        <span>
          {completed}/{total}
        </span>
        <span>Steps</span>
      </div>
    </div>
  );
}
