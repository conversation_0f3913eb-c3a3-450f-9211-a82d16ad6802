import S3Layout from "@/Layout/S3Layout";
import {
  ESGDangerIcon,
  ESGTaskIcon,
  ESGShieldIcon,
  ESGStatusUpIcon,
  ESGClipboardIcon,
  ESGShieldCheckIcon,
} from "@/assets/icons";
import { useEffect, useState } from "react";
import ESGRiskUniverseAndGovernanceView from "./ESGRiskUniverseAndGovernance/ESGRiskUniverseAndGovernanceView";
import RiskIdentificationAndAssessment from "./RiskIdentificationAndAssessment/RiskIdentificationAndAssessment";
import MitigationStrategiesView from "./MitigationStrategies/MitigationStrategiesView";
import AnalyticsReportingView from "./AnalyticsAndReporting/AnalyticsReportingView";
import Audit from "./Audit/Audit";
import GreenWashingRiskAssessmentView from "../GreenWashingRiskAssessment/GreenWashingRiskAssessmentView";
import { IoMdHome } from "react-icons/io";
import { useLocation } from "react-router-dom";

export default function ESGRiskManagementSystem() {
  const location = useLocation();

  const [activeTab, setActiveTab] = useState(1);
  const data = [
    {
      id: 1,
      name: "Universe and Governance",
      icon: <ESGDangerIcon active={activeTab === 1} />,
      path: "/green-shield/financial/ESG-risk-management/ESGRiskUniverseAndGovernanceView",
    },
    {
      id: 2,
      name: "IRO Assessment",
      icon: <ESGTaskIcon active={activeTab === 2} />,
      path: "/green-shield/financial/ESG-risk-management/RiskIdentificationAndAssessment",
    },
    {
      id: 3,
      name: "Mitigation Strategies",
      icon: <ESGShieldIcon active={activeTab === 3} />,
      path: "/green-shield/financial/ESG-risk-management/MitigationStrategiesView",
    },
    {
      id: 4,
      name: "Analytics",
      icon: <ESGStatusUpIcon active={activeTab === 4} />,
      path: "/green-shield/financial/ESG-risk-management/AnalyticsAndReporting/AnalyticsReportingView",
    },
    {
      id: 5,
      name: "Audit",
      icon: <ESGClipboardIcon active={activeTab === 5} />,
      path: "/green-shield/financial/ESG-risk-management/Audit",
    },
    {
      id: 6,
      name: "Anti-Greenwashing",
      icon: <ESGShieldCheckIcon active={activeTab === 6} />,
      path: "/green-shield/financial/green-washing-risk-assessment",
    },
  ];

  useEffect(() => {
    const path = location.pathname;
    const id = path.split('/').pop();
    if (id && !isNaN(id)) {
      setActiveTab(parseInt(id));
    }
  }, [location.pathname]);

  const getBreadcrumbName = (id) => {
    const tab = data.find(item => item.id === id);
    return tab ? tab.name : "Unknown";
  }

  return (
    <S3Layout
      navbarTitle={getBreadcrumbName(activeTab)}
      // navbarTitle={'Risk & Impact Management'}
      breadcrumbItems={[
        { title: <IoMdHome size={20}/>, href: "/get-started" },
        { title: `Integrated Risk & Audit`, href: "#" },
      ]}>
      <div className="overflow-x-hidden min-h-screen">
        <div 
        className="flex gap-10 mb-10 pb-3 min-w-full overflow-x-auto [&::-webkit-scrollbar]:h-[8px] [&::-webkit-scrollbar-thumb]:bg-[#05808b] [&::-webkit-scrollbar-track]:bg-white [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:rounded-full"
        role="tablist">
          {data.map((tab, idx) => (
            <div key={tab.id} className="flex w-full gap-7 items-center">
              <button
                className={`whitespace-nowrap p-2 text-xs rounded-xl flex w-full items-center gap-2 font-semibold transition-colors duration-200
                    ${tab.id === activeTab ? "bg-white" : "hover:bg-gray-100"}
                    `}
                onClick={() => setActiveTab(tab.id)}
                role="tab"
                aria-selected={tab.id === activeTab}
                aria-controls={`tabpanel-${tab.id}`}
              >
                {tab.icon}
                <span
                  className={`${tab.id === activeTab ? "gradient-text" : ""}`}
                >
                  {tab.name}
                </span>
              </button>
              {idx !== data.length - 1 && (
                <span className="w-[1px] h-full bg-[#1C889C]"></span>
              )}
            </div>
          ))}
        </div>
      {activeTab === 1 && <ESGRiskUniverseAndGovernanceView />}
      {activeTab === 2 && <RiskIdentificationAndAssessment />}
      {activeTab === 3 && <MitigationStrategiesView />}
      {activeTab === 4 && <AnalyticsReportingView />}
      {activeTab === 5 && <Audit />}
      {activeTab === 6 && <GreenWashingRiskAssessmentView />}
      </div>

      <br />
      <br />
      <br />
      <br />

    </S3Layout>
  );
}
