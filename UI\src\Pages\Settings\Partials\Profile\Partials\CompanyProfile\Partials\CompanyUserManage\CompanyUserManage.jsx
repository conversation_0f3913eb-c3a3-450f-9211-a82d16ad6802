import MainLayout from "@/Layout/MainLayout";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import { Button } from "@mantine/core";
import { useTranslation } from "react-i18next"; // Import i18next hook
import { useLocation, useNavigate } from "react-router";

import ConfigurationsUserManageTable from "./Partials/ConfigurationsUserManageTable";
import { useAuth } from "@/Contexts/AuthContext";

export default function CompanyUserManage() {
  const { user } = useAuth();
  // console.log(user.userRole);

  const { getStartMenu } = useSideBarRoute();
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation(); // Use translation function

  return (
    <>
      <MainLayout menus={getStartMenu} navbarTitle={t("Configurations")}>
        <div>
          <div className="w-full p-5 text-center bg-white rounded-lg">
            <h1 className="text-3xl font-bold text-primary">
              {t("Company User Management")}
            </h1>
          </div>
          <div className="justify-around  p-5 mt-5 text-center rounded-lg xl:flex">
            {user?.userRole === "Admin" && (
              <Button
                className={`px-16 hover:bg-primary hover:text-white  ${
                  pathname.includes("/Configurations/CompanyUserManage")
                    ? "bg-primary text-white"
                    : "bg-[#07838F33] border border-primary text-primary"
                }`}
                size="lg"
                onClick={() =>
                  navigate(
                    "/Settings/CompanyProfile/Configurations/CompanyUserManage"
                  )
                }
              >
                {t("Manage Users")}
              </Button>
            )}
            {user?.userRole === "Admin" && (
              <Button
                className={`px-16 hover:bg-primary hover:text-white  block mx-auto xl:mx-0 mt-5 xl:mt-0  ${
                  pathname.includes("/Configurations/CompanyLogs")
                    ? "bg-primary text-white"
                    : "bg-[#07838F33] border border-primary text-primary"
                }`}
                size="lg"
                onClick={() =>
                  navigate(
                    "/Settings/CompanyProfile/Configurations/CompanyLogs"
                  )
                }
              >
                {t("Company Logs")}
              </Button>
            )}
            {user?.userRole === "Admin" && (
              <Button
                className={`hover:bg-primary hover:text-white block mx-auto xl:mx-0 mt-5 xl:mt-0 ${
                  pathname.includes("/Configurations/DepartmentsandProjects")
                    ? "bg-primary text-white"
                    : "bg-[#07838F33] border border-primary text-primary"
                }`}
                size="lg"
                onClick={() =>
                  navigate(
                    "/Settings/CompanyProfile/Configurations/DepartmentsandProjects"
                  )
                }
              >
                {t("Departments and Projects")}
              </Button>
            )}
            <Button
              className={`px-24 hover:bg-primary hover:text-white block mx-auto xl:mx-0 mt-5 xl:mt-0 ${
                pathname.includes("/Configurations/Assets")
                  ? "bg-primary text-white"
                  : "bg-[#07838F33] border border-primary text-primary"
              }`}
              size="lg"
              onClick={() =>
                navigate("/Settings/CompanyProfile/Configurations/Assets")
              }
            >
              {t("Assets")}
            </Button>
            <Button
              className={` hover:bg-primary hover:text-white block mx-auto xl:mx-0 mt-5 xl:mt-0 ${
                pathname.includes("/Configurations/CustomFactor")
                  ? "bg-primary text-white"
                  : "bg-[#07838F33] border border-primary text-primary"
              }`}
              size="lg"
              onClick={() =>
                navigate("/Settings/CompanyProfile/Configurations/CustomFactor")
              }
            >
              {t("Emission Factor Selection")}
            </Button>
          </div>
        </div>
        <div className="">
          <ConfigurationsUserManageTable />
          <hr className="mt-3 bg-[#D1D1D1] h-[2px]" />
        </div>
        <div className="mt-3"></div>
      </MainLayout>
    </>
  );
}
