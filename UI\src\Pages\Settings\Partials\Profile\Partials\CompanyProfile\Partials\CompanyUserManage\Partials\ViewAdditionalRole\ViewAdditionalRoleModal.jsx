import ApiProfile from "@/Api/apiProfileConfig";
import { Modal } from "@mantine/core";
import { useEffect, useState } from "react";
import ViewAdditionalRoleTable from "./ViewAdditionalRoleTable";

export default function ViewAdditionalRoleModal({
 opened,
 close,
 selectedUserId,
 SetSelectedUserId,
 show_company_users,
 data,
 setAdditionalRoleValue,
 addApi,
 removeApi,
 closeModel
}) {
  
 const [AdditionalRoles, setAdditionalRoles] = useState([]);
 const getAllAdditionalRoles = async () => {
  try {
   const { data } = await ApiProfile.get("/get_all_additional_roles");
   setAdditionalRoles(data);
   console.log(data);
  } catch (error) {
   console.log(error);
  }
 };
 useEffect(() => {
  getAllAdditionalRoles();
 }, []);
  // const additionalRole = [
  //  {
  //   createdDate: "Fri, 03 Jan 2025 00:00:00 GMT",
  //   enabled: true,
  //   roleDescription: "This role for Legal and Compliance in S3",
  //   roleId: 1,
  //   roleName: "Legal & Compliance",
  //  },
  //  {
  //   createdDate: "Fri, 03 Jan 2025 00:00:00 GMT",
  //   enabled: true,
  //   roleDescription: "This role for Legal and Compliance in S3",
  //   roleId: 2,
  //   roleName: "Compliance & Legal",
  //  },
  // ];
 return (
  <Modal
   opened={opened}
   onClose={() => {
    close();
    SetSelectedUserId(null);
    setAdditionalRoleValue({})
   }}
   title={"Add or Remove Additional Roles"}
   size={"50%"}
   centered
  >
   <ViewAdditionalRoleTable
    AdditionalRoles={AdditionalRoles}
    data={data}
    selectedUserId={selectedUserId}
    show_company_users={show_company_users}
    close={close}
    SetSelectedUserId={SetSelectedUserId}
    setAdditionalRoleValue={setAdditionalRoleValue}
    addApi={addApi}
    removeApi={removeApi}
    closeModel={closeModel}
   />
  </Modal>
 );
}
