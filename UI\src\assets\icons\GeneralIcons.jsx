export const ExportIcon = ()=> (
    <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11.6777 9.66732L18.5111 2.83398" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
<path d="M19.1777 6.16602V2.16602H15.1777" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
<path d="M10.0117 2.16602H8.34505C4.17839 2.16602 2.51172 3.83268 2.51172 7.99935V12.9993C2.51172 17.166 4.17839 18.8327 8.34505 18.8327H13.3451C17.5117 18.8327 19.1784 17.166 19.1784 12.9993V11.3327" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
</svg>

)


export const ChatBotLaunchPadIcon = ()=> (
    <svg width="100" height="100" viewBox="0 0 181 181" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1171_34637)">
<path d="M87.9845 18.8495C121.094 17.4396 149.08 43.056 150.49 76.062C151.899 109.068 126.199 136.963 93.0886 138.364C59.9853 139.764 32.0096 114.15 30.6002 81.1505C29.1909 48.1507 54.8812 20.259 87.9845 18.8495Z" fill="url(#paint0_linear_1171_34637)"/>
<path d="M89.987 36.631C90.5931 36.5886 91.1602 36.6161 91.7462 36.7816C92.8496 37.0872 93.7795 37.8291 94.3203 38.8357C94.8559 39.8165 94.9758 40.9699 94.6539 42.0394C94.1936 43.5476 93.1068 44.3565 91.7705 45.0718C91.9047 46.601 92.0811 48.1996 92.4081 49.6988C94.1384 50.0262 95.8729 50.0663 97.4911 50.8651C98.3542 51.2909 99.0275 52.0991 99.9701 52.3608C101.116 52.6794 102.458 52.7108 103.642 52.8817C106.417 53.2826 109.234 53.9629 111.802 55.1027C116.395 57.1416 119.648 60.3459 121.636 64.9552C122.009 65.8197 122.23 67.1612 122.848 67.8558C123.159 68.2045 123.652 68.3758 124.069 68.5636C125.883 69.3802 126.927 70.3862 127.638 72.2659C129.035 75.9536 129.006 83.9274 126.43 87.0598C125.403 88.3094 123.328 88.5865 122.679 89.6091C122.112 90.5028 121.893 91.717 121.453 92.6936C120.241 95.3792 118.496 97.7659 116.149 99.5749C112.722 102.216 108.225 103.573 104.235 105.131L89.4127 110.766L85.7355 112.067C85.1512 112.279 84.5776 112.569 83.9702 112.707C83.6717 112.774 83.3478 112.804 83.087 112.614C82.1892 111.96 82.8739 107.17 82.6173 105.882C82.542 105.503 82.364 105.137 81.9888 104.983C80.9903 104.573 79.5907 104.651 78.5143 104.494C75.245 104.016 71.9969 103.37 68.9807 101.979C64.6435 99.979 61.2917 96.7598 59.4763 92.3034C59.1129 91.4104 58.8592 90.066 58.2278 89.3546C57.6606 88.7154 56.4117 88.424 55.6728 87.937C54.0052 86.838 53.301 84.9519 52.9281 83.069C52.2343 79.5662 52.2285 72.3345 55.0134 69.7743C55.9339 68.9285 57.6687 68.5581 58.2979 67.7625C58.8585 67.0531 59.0557 65.8952 59.4094 65.0598C59.9165 63.8614 60.5222 62.6824 61.2495 61.6023C64.724 56.4393 70.3686 54.1552 76.2792 53.0751C77.8442 52.7891 79.5192 52.7577 81.0533 52.3721C82.0505 52.1211 82.755 51.2527 83.6785 50.8337C85.2925 50.1013 86.9786 50.0181 88.6881 49.6826C88.9846 48.1818 89.1415 46.627 89.3253 45.1077C89.0376 44.9157 88.727 44.7512 88.4334 44.567C87.3632 43.8954 86.619 42.9739 86.3458 41.7243C86.1107 40.6289 86.3299 39.4853 86.9532 38.5531C87.6798 37.4463 88.7205 36.9056 89.987 36.631Z" fill="#FEFEFE"/>
<path d="M115.76 81.8952C115.558 85.3669 114.876 89.348 112.543 92.0592C109.216 95.9273 102.7 96.5749 97.921 96.9647C91.2903 97.5055 80.2037 97.4336 73.9352 95.3713C72.3387 94.8461 70.7517 94.0836 69.469 92.9856C66.3475 90.3133 65.5836 85.6849 65.3156 81.7964C64.9687 76.7607 65.0977 69.0566 68.6401 65.0636C72.0889 61.1754 79.0835 60.509 83.9956 60.193C91.046 59.7394 106.225 59.4651 111.688 64.3055C114.78 67.0455 115.523 71.5369 115.764 75.4565C115.903 77.6003 115.902 79.7513 115.76 81.8952Z" fill="url(#paint1_linear_1171_34637)"/>
<path d="M101.057 73.7109C101.791 73.6371 102.461 73.6306 103.13 74.002C103.905 74.432 104.371 75.2056 104.616 76.0316C105.118 77.7283 105.154 80.4724 104.256 82.0529C103.81 82.8365 103.227 83.2658 102.368 83.508C101.592 83.5799 100.841 83.576 100.147 83.1661C99.3517 82.6969 98.9054 81.8373 98.6728 80.9747C98.2168 79.2838 98.2259 76.6287 99.1499 75.0806C99.6096 74.3103 100.206 73.9282 101.057 73.7109Z" fill="#FEFEFE"/>
<path d="M78.6858 73.7145C79.4154 73.6553 80.1095 73.6261 80.7867 73.9632C81.5595 74.3475 82.0493 75.0819 82.3156 75.8781C82.8831 77.5742 82.8899 80.2636 82.0691 81.8855C81.6559 82.7022 81.0703 83.1862 80.2099 83.4757C79.4553 83.5686 78.7417 83.5988 78.0365 83.2487C77.2355 82.8514 76.7529 82.097 76.4943 81.2694C75.9616 79.5646 75.9522 76.8045 76.8175 75.2004C77.2414 74.4146 77.8345 73.9645 78.6858 73.7145Z" fill="#FEFEFE"/>
</g>
<defs>
<filter id="filter0_d_1171_34637" x="0.544922" y="0.793945" width="180" height="179.625" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
<feFlood floodOpacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="12"/>
<feGaussianBlur stdDeviation="15"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1171_34637"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1171_34637" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1171_34637" x1="28.2893" y1="156.056" x2="174.962" y2="143.227" gradientUnits="userSpaceOnUse">
<stop stopColor="#2C5A8C"/>
<stop offset="0.46" stopColor="#1C889C"/>
<stop offset="1" stopColor="#13B1A8"/>
</linearGradient>
<linearGradient id="paint1_linear_1171_34637" x1="64.2344" y1="102.748" x2="125.789" y2="95.4538" gradientUnits="userSpaceOnUse">
<stop stopColor="#2C5A8C"/>
<stop offset="0.46" stopColor="#1C889C"/>
<stop offset="1" stopColor="#13B1A8"/>
</linearGradient>
</defs>
</svg>

)