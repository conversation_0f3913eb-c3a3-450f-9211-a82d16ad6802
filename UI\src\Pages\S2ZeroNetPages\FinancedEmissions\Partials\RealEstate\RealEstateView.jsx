import CommercialProperties from "./Partials/CommercialRealEstate";
import PortfolioSummary from "./Partials/PortfolioSummary";
import RegionalSettings from "./Partials/RegionalSettings";
import ResidentialProperties from "./Partials/ResidentialProperties";
import { Button } from "@mantine/core";

const RealEstateView = () => {
  return (
    <div>
      <div className="flex flex-col gap-4">
        <PortfolioSummary />
        <ResidentialProperties />
        <CommercialProperties />
        <RegionalSettings />
        <Button
          w="100%"
          className="bg-[#07838F] text-white hover:bg-[#066B7A] px-6 py-2 rounded"
        >
          Calculate Portfolio Emissions
        </Button>
      </div>
    </div>
  );
};

export default RealEstateView;
