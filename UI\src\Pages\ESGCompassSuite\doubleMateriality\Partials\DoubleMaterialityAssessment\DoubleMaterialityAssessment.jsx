import Loading from "@/Components/Loading";
import { useDoubleMateriality } from "@/Contexts/DoubleMaterialityContext";
import { Button, TextInput } from "@mantine/core";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import HeatMap from "./Partials/HeatMap";
import MaterialityTable from "./Partials/MaterialityTable";
import { PiExportLight } from "react-icons/pi";
import * as XLSX from "xlsx";
import { Tabs } from "@mantine/core";
import { CiSearch } from "react-icons/ci";
import MaterialityGuide from "./Partials/MaterialityGuide";
import SingleMaterialityTable from "./Partials/SingleMaterialityTable";

const DoubleMaterialityAssessment = () => {
  const [active, setActive] = useState("Double Materiality Assessment");
  const [selectedItems, setSelectedItems] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");

  const { DMAData, PriorityLevels, prioritySelectColorMap, SingleMTData } =
    useDoubleMateriality();
  const { t } = useTranslation();

  const  doubleMateriality = DMAData;
  const singleMaterialityData = SingleMTData;

  const handleSelectItem = (id) => {
    setSelectedItems((prev) => {
      const isAlreadySelected = prev.includes(id);
      if (isAlreadySelected) {
        return prev.filter((item) => item !== id);
      } else {
        return [...prev, id];
      }
    });
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value.toLowerCase());
  };

  const filteredDoubleMTData = doubleMateriality?.filter((item) => {
    if (!searchTerm) return true;
    return (
      item.name.toLowerCase().includes(searchTerm) ||
      item.Category.toLowerCase().includes(searchTerm)
    );
  });

  const filteredSingleMTData = singleMaterialityData?.filter((item) => {
    if (!searchTerm) return true;
    return (
      item.name.toLowerCase().includes(searchTerm) ||
      item.Category.toLowerCase().includes(searchTerm)
    );
  });

  const exportToExcel = (data,materialityType) => {
    // Filter data to include only items with IDs in selectedItems
    const filteredData = data.filter((item) =>
      selectedItems.includes(item.id.toString())
    );
    // If no items are selected, export all data
    const exportData = selectedItems.length > 0 ? filteredData : data;

    // Create a new workbook
    const workbook = XLSX.utils.book_new();

    // Convert the filtered data to a worksheet
    const worksheet = XLSX.utils.json_to_sheet(exportData);

    // Add the worksheet to the workbook
    XLSX.utils.book_append_sheet(
      workbook,
      worksheet,
      materialityType
    );

    // Generate the Excel file
    XLSX.writeFile(workbook, `${materialityType}.xlsx`);
  };

  return (
    <div>
      <MaterialityGuide />

      <HeatMap
        data={doubleMateriality}
        selectedItems={selectedItems}
        PriorityLevels={PriorityLevels}
        prioritySelectColorMap={prioritySelectColorMap}
      />

      <Tabs
        defaultValue="Double Materiality Assessment"
        variant="none"
        className="pt-10"
      >
        <Tabs.List className="grid md:grid-cols-2 justify-center bg-[#FFFFFF] border-2 border-[#E8E7EA] p-6 gap-5 py-3 mb-6 rounded-lg text-primary">
          <Tabs.Tab
            value="Double Materiality Assessment"
            className={`relative text-lg font-semibold py-3 px-6 transition-all ${
              active === "Double Materiality Assessment"
                ? "active-tab rounded"
                : "text-[#5A5A5A] bg-[#FFFFFF] rounded-lg border-[#E8E7EA] border-2"
            }`}
            onClick={() => {
              setActive("Double Materiality Assessment");
            }}
          >
            {t("Double Materiality Assessment")}
          </Tabs.Tab>

          <Tabs.Tab
            value="Single Materiality Assessment"
            className={`relative text-lg font-semibold py-3 px-6 transition-all ${
              active === "Single Materiality Assessment"
                ? "active-tab rounded"
                : "text-[#5A5A5A] bg-[#FFFFFF] rounded-lg base-border"
            }`}
            onClick={() => {
              setActive("Single Materiality Assessment");
            }}
          >
            {t("Single Materiality Assessment")}
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="Double Materiality Assessment">
          <div className="flex lg:flex-row flex-col items-center justify-between gap-5 p-3 mt-5 bg-white rounded-lg base-border">
            <TextInput
              className="w-full"
              placeholder="Search by Topic Area or category"
              leftSection={<CiSearch className="w-5 h-5" />}
              value={searchTerm}
              onChange={handleSearchChange}
            />

            <Button
              className="border-[1px] relative z-0 w-[105px] h-[42px] text-sm font-bold rounded-lg text-secondary-300 border-secondary-300 bg-white px-1"
              size="md"
              onClick={() => exportToExcel(filteredDoubleMTData,'Double Materiality Assessment')}
            >
              <span className="me-1">
                <PiExportLight className="text-lg" />
              </span>
              <span> {t("export")}</span>
            </Button>
          </div>
          {DMAData ? (
            <MaterialityTable
              data={filteredDoubleMTData}
              onSelectItem={handleSelectItem}
              setSelectedItems={setSelectedItems}
            />
          ) : (
            <Loading />
          )}
        </Tabs.Panel>

        <Tabs.Panel value="Single Materiality Assessment">
        <div className="flex lg:flex-row flex-col items-center justify-between gap-5 p-3 mt-5 bg-white rounded-lg base-border">
            <TextInput
              className="w-full"
              placeholder="Search by Topic Area or category"
              leftSection={<CiSearch className="w-5 h-5" />}
              value={searchTerm}
              onChange={handleSearchChange}
            />

            <Button
              className="border-[1px] relative z-0 w-[105px] h-[42px] text-sm font-bold rounded-lg text-secondary-300 border-secondary-300 bg-white px-1"
              size="md"
              onClick={() => exportToExcel(filteredSingleMTData,'Single Materiality Assessment')}
            >
              <span className="me-1">
                <PiExportLight className="text-lg" />
              </span>
              <span> {t("export")}</span>
            </Button>
          </div>
          {SingleMTData ? (
            <SingleMaterialityTable
              data={filteredSingleMTData}
              onSelectItem={handleSelectItem}
              setSelectedItems={setSelectedItems}
            />
          ) : (
            <Loading />
          )}
        </Tabs.Panel>
      </Tabs>
    </div>
  );
};

export default DoubleMaterialityAssessment;
