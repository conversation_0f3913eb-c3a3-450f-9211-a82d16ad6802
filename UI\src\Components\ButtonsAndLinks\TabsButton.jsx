import { useTranslation } from 'react-i18next';

const TabsButton = ({active,setActive,btnName = ''}) => {
  const { t } = useTranslation();

  return (
    <button
    title={t(btnName)}
    className={`relative text-lg font-semibold py-3 px-6 transition-all ${
      active === btnName
        ? "active-tab rounded"
        : "text-[#5A5A5A] rounded-lg border-2"
    }`}
    onClick={() => setActive(btnName)}
  >
    {t(btnName)}
  </button>
  )
}

export default TabsButton
