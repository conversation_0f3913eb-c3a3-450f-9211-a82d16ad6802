import { Table, Button } from "@mantine/core";
import { GoogleSheetIcon, DocumentDownload } from "@/assets/svg/ImageSVG";
import { useState } from "react";

const PCAFPortfolioEmissionsResults = () => {
  const [googleHover, setGoogleHover] = useState(false)
  const [pdfHover, setPdfHover] = useState(false)

  return (
    <div className="flex flex-col gap-4 rounded-[10px] bg-[#07838F1A] p-4 border-[#E8E7EA] border">
      <h2 className="text-2xl text-[#272727] font-bold">
        PCAF Portfolio Emissions Results
      </h2>
      <div>
        <div className="flex justify-between divide-x divide-solid gap-4 rounded-lg bg-white border-[#E8E7EA] border">
          <div className="flex-1 p-4 flex flex-col ">
            <h3 className="text-[#272727] text-lg font-bold">
              Total Financed Emissions
            </h3>
            <div className="flex gap-2">
              <p className="text-[#07838F] text-3xl font-bold">456</p>
              <span className="self-end text-[#6B7280]">tCO₂e/year</span>
            </div>
          </div>
          <div className="flex-1 p-4 flex flex-col">
            <h3 className="text-[#272727] text-lg font-bold">
              Emission Intensity
            </h3>
            <div className="flex gap-2">
              <p className="text-[#07838F] text-3xl font-bold">0.0</p>
              <span className="self-end text-[#6B7280]">tCO₂e/$M loaned</span>
            </div>
          </div>
          <div className="flex-1 p-4 flex flex-col">
            <h3 className="text-[#272727] text-lg font-bold">Coverage Rate</h3>
            <div className="flex gap-2">
              <p className="text-[#07838F] text-3xl font-bold">100</p>
              <span className="self-end text-[#6B7280]">% of portfolio</span>
            </div>
          </div>
          <div className="flex-1 p-4 flex flex-col">
            <h3 className="text-[#272727] text-lg font-bold">
              Weighted Avg Data Quality
            </h3>
            <div className="flex gap-2">
              <p className="text-[#07838F] text-3xl font-bold">0.0</p>
              <span className="self-end text-[#6B7280]">PCAF Score (1-5)</span>
            </div>
          </div>
        </div>
      </div>
      <div className="flex flex-col gap-4 rounded-[10px] bg-[#F8F8F8] p-4 border-[#E8E7EA] border">
        <h3 className="text-xl font-bold text-[#272727]">
          Emissions Breakdown by Vehicle Category
        </h3>
        <Table highlightOnHover withTableBorder withColumnBorders>
          <Table.Tr className="bg-[#F5F4F5] text-[#272727] font-bold">
            <Table.Th>Vehicle Category</Table.Th>
            <Table.Th>Number of Loans</Table.Th>
            <Table.Th>Outstanding ($M)</Table.Th>
            <Table.Th>Financed Emissions (tCO₂e)</Table.Th>
            <Table.Th>% of Total</Table.Th>
            <Table.Th>Data Quality</Table.Th>
          </Table.Tr>
          <Table.Tr className="h-20 text-[#272727] font-bold">
            <Table.Td></Table.Td>
            <Table.Td>0</Table.Td>
            <Table.Td>0.0M</Table.Td>
            <Table.Td>0.0</Table.Td>
            <Table.Td>38%</Table.Td>
            <Table.Td>
              <span className="text-[#991B1B] px-6 py-1 bg-[#FEE2E2] rounded-2xl">
                Score 5
              </span>
            </Table.Td>
          </Table.Tr>
        </Table>
      </div>
      <div className="flex flex-col gap-4 rounded-[10px] bg-[#F8F8F8] p-4 border-[#E8E7EA] border">
        <h3>Data Quality Scores:</h3>
        <div className="flex gap-8">
          <div className="flex gap-2 items-center">
            <span className="inline-block h-4 w-4 bg-[#08B12D]" />
            <span>1 - Actual data</span>
          </div>
          <div className="flex gap-2 items-center">
            <span className="inline-block h-4 w-4 bg-[#07838F]" />
            <span>2 - Specific factors</span>
          </div>
          <div className="flex gap-2 items-center">
            <span className="inline-block h-4 w-4 bg-[#FBB90D]" />
            <span>3 - Average factors</span>
          </div>
          <div className="flex gap-2 items-center">
            <span className="inline-block h-4 w-4 bg-[#FF9500]" />
            <span>4 - Proxy data</span>
          </div>
          <div className="flex gap-2 items-center">
            <span className="inline-block h-4 w-4 bg-[#E81E1E]" />
            <span>5 - Estimates</span>
          </div>
        </div>
      </div>
      <div className="flex gap-2">
        <Button
          leftSection={<GoogleSheetIcon color={googleHover ? "#FFFFFF" : ""} />}
          onMouseEnter={() => setGoogleHover(true)}
          onMouseLeave={() => setGoogleHover(false)}
          className="w-full rounded-md bg-white hover:bg-[#07838F] text-[#07838F]"
        >
          Export to Excel
        </Button>
        <Button
          leftSection={<DocumentDownload color={pdfHover ? "#FFFFFF" : ""} />}
          onMouseEnter={() => setPdfHover(true)}
          onMouseLeave={() => setPdfHover(false)}
          className="w-full rounded-md bg-white hover:bg-[#07838F] text-[#07838F]"
        >
          Export PDF Report
        </Button>
      </div>
    </div>
  );
};

export default PCAFPortfolioEmissionsResults;
