import { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import Cookies from "js-cookie";
import Progress from "@/Components/ChainSight/Assessment/Progress";
import { Button } from "@mantine/core";
import Question from "@/Components/ChainSight/Assessment/Question";
import { toast } from "react-toastify";
import { GenerateDocument } from "@/assets/icons";

const Assessment = ({ isActive }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const baseUrl = "https://chain-sight-staging.azurewebsites.net";
  const [sections, setSections] = useState([]);
  const [activeSection, setActiveSection] = useState(null);
  const [loading, setLoading] = useState(true);
  const [assessmentID, setAssessmentID] = useState(null);
  const [reportURL, setReportURL] = useState(null);
  const [companyUsers, setCompanyUsers] = useState([]);

  // Fetch company users
  const fetchCompanyUsers = async () => {
    const token = Cookies.get("level_user_token");
    if (!token) {
      toast.error("Authorization token is missing");
      return;
    }

    try {
      const response = await fetch(
        "https://portal-auth-main.azurewebsites.net/get-all-company-users",
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch company users: ${response.statusText}`);
      }

      const users = await response.json();
      setCompanyUsers(users);
    } catch (error) {
      console.error("Error fetching company users:", error);
      toast.error("Failed to fetch company users");
    }
  };

  // Fetch create assessment
  const fetchCreateAssessment = async () => {
    const response = await fetch(`${baseUrl}/api/assessments`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${Cookies.get("level_user_token")}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        JSON.stringify({ status: response.status, message: errorData.message })
      );
    }

    const data = await response.json();
    return data;
  };

  // Fetch get assessment
  const fetchGetAssessment = async (assessmentId) => {
    const response = await fetch(`${baseUrl}/api/assessments/${assessmentId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${Cookies.get("level_user_token")}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch assessments");
    }

    const data = await response.json();
    return data;
  };

  // Fetch last in-progress assessment
  const fetchLastInProgressAssessment = async () => {
    const response = await fetch(
      `${baseUrl}/api/assessments/in-progress/last`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${Cookies.get("level_user_token")}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error(
        JSON.stringify({
          status: response.status,
          message: "Failed to fetch last in-progress assessment",
        })
      );
    }

    const data = await response.json();
    return data;
  };

  // Get an Assessment Report as PDF and Download it
  const fetchGetAssessmentReport = async (assessmentId) => {
    const response = await fetch(
      `${baseUrl}/api/assessments/${assessmentId}/report`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${Cookies.get("level_user_token")}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error(
        JSON.stringify({
          status: response.status,
          message: "Failed to fetch assessment report",
        })
      );
    }

    if (response.status === 400) {
      toast.error("Please complete the assessment before generating the report.");
      return;
    }

    const data = await response.json();
    setReportURL(data.report_url);
    console.log("Report URL:", data.report_url);
    return data;
  };

  // Download the report PDF
  const downloadReport = async (reportURL) => {
    if (!reportURL) {
      console.error("No report URL available");
      return;
    }
    const response = await fetch(`${reportURL}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${Cookies.get("level_user_token")}`,
      },
    });
    if (!response.ok) {
      console.error("Failed to download report");
      return;
    }
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.style.display = "none";
    a.href = url;
    a.download = `Sustainable_Procurement_Evaluation_${assessmentID}.pdf`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    a.remove();
  };

  // Fetch assessment data and company users only once on mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch company users
        await fetchCompanyUsers();

        let assessmentData;
        try {
          assessmentData = await fetchLastInProgressAssessment();
          setAssessmentID(assessmentData.id);
        } catch (error) {
          const errorObj = JSON.parse(error.message);
          if (errorObj.status === 404) {
            const createdAssessment = await fetchCreateAssessment();
            assessmentData = await fetchGetAssessment(createdAssessment.id);
            setAssessmentID(createdAssessment.id);
          } else {
            throw error;
          }
        }

        const sortedSections = assessmentData.sections.sort(
          (a, b) => a.order - b.order
        );
        setSections(sortedSections);
        setActiveSection(sortedSections[0]?.title || "Policy and Governance");
        setLoading(false);
      } catch (error) {
        console.error("Error fetching assessments:", error);
        setLoading(false);
      }
    };

    fetchData();
  }, [isActive]);

  // Handle navigation state changes
  useEffect(() => {
    if (location.state?.targetSectionTitle && sections.length > 0) {
      const targetSection = sections.find(
        (s) => s.title === location.state.targetSectionTitle
      );
      if (targetSection) {
        setActiveSection(targetSection.title);
      }
    }
  }, [location.state?.targetSectionTitle, sections]);

  const HandleAssessmentExport = () => {
    fetchGetAssessmentReport(assessmentID);
    downloadReport(reportURL);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  const activeSectionData = sections.find(
    (section) => section.title === activeSection
  );
  const activeSectionOrder = activeSectionData ? activeSectionData.order : 1;

  const handleNextSection = () => {
    const nextSection = sections.find(
      (section) => section.order === activeSectionOrder + 1
    );
    if (nextSection) {
      setActiveSection(nextSection.title);
    }
  };

  const handlePreviousSection = () => {
    const prevSection = sections.find(
      (section) => section.order === activeSectionOrder - 1
    );
    if (prevSection) {
      setActiveSection(prevSection.title);
    }
  };

  return (
    <div className="w-full flex flex-col gap-5 items-center justify-center">
      <div className="w-full border border-[#ECECEC] rounded-lg bg-white flex flex-col items-center justify-between p-4 gap-4">
        <div className="w-full bg-[#07838F1A] p-4 rounded-lg flex max-md:flex-col max-md:items-center gap-4 justify-between">
          <div className="w-full">
            <Progress completed={activeSectionOrder} total={sections.length} />
          </div>
          <Button color="#07838F" w={300} onClick={HandleAssessmentExport} >
            <span className="mr-2"><GenerateDocument /></span>
            Assess & Generate Report
          </Button>
        </div>
        <div className="w-full flex gap-4 flex-wrap">
          {sections.map((section) => (
            <div
              key={section.id}
              className={`${
                activeSection === section.title
                  ? "bg-[#07838F1A] text-[#07838F]"
                  : "bg-white border border-[#E8E7EA] text-[#8F8F8F]"
              } px-4 py-2 cursor-pointer rounded-lg mb-2`}
              onClick={() => setActiveSection(section.title)}
            >
              {section.title}
            </div>
          ))}
        </div>
      </div>
      <div className="flex flex-col gap-6 p-4 w-full border border-[#ECECEC] rounded-lg bg-white">
        {activeSectionData?.questions.map((question) => (
          <Question
            key={question.id}
            id={question.id}
            question={question.text}
            options={question.options}
            baseUrl={baseUrl}
            messages={question.messages}
            is_flagged={question.is_flagged}
            targetQuestionId={location.state?.targetQuestionId}
            collaborators={question.collaborators}
            companyUsers={companyUsers}
          />
        ))}
        <div className="flex justify-between mt-4 w-full">
          <Button
            onClick={handlePreviousSection}
            color="#07838F"
            className={`${
              activeSectionOrder === 1
                ? "bg-gray-300"
                : "bg-[#07838F] hover:bg-[#07838fce]"
            } text-white px-6 py-1 md:w-40 w-full rounded cursor-pointer font-semibold`}
            disabled={activeSectionOrder === 1}
          >
            Previous
          </Button>

          <Button
            color="#F5F4F5"
            className={`
              " text-[#555F62] border border-[#E1DEE0] px-6 md:w-40 w-full py-1 rounded cursor-pointer font-semibold`}
            onClick={() => {
              navigate("/Grc/chain-sight/review", { state: { sections } });
            }}
          >
            Review
          </Button>

          <Button
            onClick={handleNextSection}
            color="#07838F"
            className={`${
              activeSectionOrder === sections.length
                ? "bg-gray-300 cursor-not-allowed"
                : "bg-[#07838f] hover:bg-[#07848fab]"
            } text-white px-6 md:w-40 w-full py-1 rounded cursor-pointer font-semibold`}
            disabled={activeSectionOrder === sections.length}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Assessment;