import { DonutChart } from "@mantine/charts";
import { Table } from "@mantine/core";

const formatDate = (isoDate) => {
    const options = {
        year: "numeric",
        month: "long",
        day: "2-digit",
    };
    return new Date(isoDate).toLocaleDateString("en-US", options);
};

const PortfolioCompanies = ({ allPortfolioData }) => {
    const handleView = (id) => {
        console.log(id);
    };
    return (
            <Table withBorder stickyHeader>
                <Table.Thead>
                    <Table.Tr>
                        <Table.Th>Company</Table.Th>
                        <Table.Th>Industry</Table.Th>
                        <Table.Th className="flex items-center justify-center">Overall Score</Table.Th>
                        <Table.Th>E</Table.Th>
                        <Table.Th>S</Table.Th>
                        <Table.Th>G</Table.Th>
                        <Table.Th>YoY Change</Table.Th>
                        <Table.Th>Last Assessment</Table.Th>
                        {/* <Table.Th>Actions</Table.Th> */}
                    </Table.Tr>
                </Table.Thead>
                <Table.Tbody className="h-full overflow-y-auto">
                    {allPortfolioData?.map((item) => (
                        <Table.Tr key={item.id}>
                            <Table.Td>{item.company_name || "N/A"}</Table.Td>
                            <Table.Td>{item.industry || "N/A"}</Table.Td>
                            <Table.Td className="flex items-center justify-center">
                                {/* {item.PortfolioHistories[0].average_total_percentage.toFixed(2) || "N/A"} */}
                                <DonutChart
                                    data={[
                                        {
                                            name: "Score",
                                            value: parseInt(item.PortfolioHistories[0].average_total_percentage.toFixed(0)),
                                            color: "blue.6",
                                        },
                                        {
                                            name: "Other",
                                            value: 100 - parseInt(item.PortfolioHistories[0].average_total_percentage.toFixed(0)),
                                            color: "gray.6",
                                        },
                                        
                                    ]}
                                    size={40}
                                    thickness={7}
                                />
                            </Table.Td>
                            <Table.Td>
                                {item.PortfolioHistories[0].average_environmental_percentage.toFixed(2) || "N/A"}
                            </Table.Td>
                            <Table.Td>
                                {item.PortfolioHistories[0].average_social_percentage.toFixed(2) || "N/A"}
                            </Table.Td>
                            <Table.Td>
                                {item.PortfolioHistories[0].average_governance_percentage.toFixed(2) || "N/A"}
                            </Table.Td>
                            <Table.Td>{item.PortfolioHistories[0]?.change || "N/A"}</Table.Td>
                            <Table.Td>
                                {formatDate(item.PortfolioHistories[0]?.updatedAt) || "N/A"}
                            </Table.Td>
                            {/* <Table.Td>
                            <Button
                                variant="outline"
                                color="blue"
                                onClick={() => handleView(item.id)}
                            >
                                View
                            </Button>
                        </Table.Td> */}
                        </Table.Tr>
                    ))}
                </Table.Tbody>
            </Table>
    );
};
export default PortfolioCompanies;
