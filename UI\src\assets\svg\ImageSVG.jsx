export const LaunchpadIcon = ({ color }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_40003739_50753)">
        <path
          d="M18.449 30.8203H13.7266"
          stroke={color || "#6F6F6F"}
          strokeWidth="1.47577"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M15.3455 0.681087C12.5427 2.92634 3.16802 11.8983 11.3653 26.0993H20.8102C28.837 11.9083 19.5995 2.93904 16.8261 0.685863C16.617 0.51592 16.356 0.42275 16.0866 0.421881C15.8172 0.421012 15.5556 0.512496 15.3455 0.681087Z"
          fill={color || "#6F6F6F"}
        />
        <path
          d="M8.10332 14.2004L3.47677 19.7522C3.3612 19.8909 3.27898 20.0542 3.23641 20.2297C3.19385 20.4051 3.19208 20.5879 3.23124 20.7641L5.05572 28.9743C5.09921 29.17 5.19183 29.3514 5.32483 29.5014C5.45784 29.6515 5.62686 29.7651 5.81595 29.8317C6.00505 29.8984 6.208 29.9157 6.40566 29.8822C6.60332 29.8487 6.78919 29.7653 6.94574 29.6401L11.3697 26.1009M23.9922 14.0938L28.7076 19.7522C28.8231 19.8909 28.9053 20.0542 28.9479 20.2297C28.9905 20.4051 28.9922 20.5879 28.9531 20.7641L27.1286 28.9743C27.0851 29.17 26.9925 29.3514 26.8595 29.5014C26.7265 29.6515 26.5575 29.7651 26.3684 29.8317C26.1793 29.8984 25.9763 29.9157 25.7787 29.8822C25.581 29.8487 25.3951 29.7653 25.2386 29.6401L20.8146 26.1009"
          fill={color || "#6F6F6F"}
        />
        <path
          d="M8.10332 14.2004L3.47677 19.7522C3.3612 19.8909 3.27898 20.0542 3.23641 20.2297C3.19385 20.4051 3.19208 20.5879 3.23124 20.7641L5.05572 28.9743C5.09921 29.17 5.19183 29.3514 5.32483 29.5014C5.45784 29.6515 5.62686 29.7651 5.81595 29.8317C6.00505 29.8984 6.208 29.9157 6.40566 29.8822C6.60332 29.8487 6.78919 29.7653 6.94574 29.6401L11.3697 26.1009M23.9922 14.0938L28.7076 19.7522C28.8231 19.8909 28.9053 20.0542 28.9479 20.2297C28.9905 20.4051 28.9922 20.5879 28.9531 20.7641L27.1286 28.9743C27.0851 29.17 26.9925 29.3514 26.8595 29.5014C26.7265 29.6515 26.5575 29.7651 26.3684 29.8317C26.1793 29.8984 25.9763 29.9157 25.7787 29.8822C25.581 29.8487 25.3951 29.7653 25.2386 29.6401L20.8146 26.1009"
          stroke={color || "#6F6F6F"}
          strokeWidth="1.47577"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M17.0833 12.4219C17.0833 13.0202 16.5983 13.5052 16 13.5052C15.4017 13.5052 14.9167 13.0202 14.9167 12.4219C14.9167 11.8236 15.4017 11.3385 16 11.3385C16.5983 11.3385 17.0833 11.8236 17.0833 12.4219Z"
          fill={color || "#6F6F6F"}
          stroke={color || "#6F6F6F"}
          strokeWidth="0.833333"
        />
      </g>
      <defs>
        <clipPath id="clip0_40003739_50753">
          <rect
            width="32"
            height="32"
            fill="white"
            transform="translate(0 0.421875)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
export const EmissionsCalculationIcon = ({ color }) => {
  return (
    <svg
      width="25"
      height="20"
      viewBox="0 0 26 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.6729 14.2424C7.11345 13.9557 6.49068 13.807 5.85734 13.807C0.917344 14.1575 0.917344 21.3899 5.85734 21.7404H17.5635C18.9885 21.751 20.3607 21.22 21.4057 20.2535C24.8785 17.2055 23.0207 11.0776 18.4501 10.4934C16.8035 0.542172 2.52179 4.32302 5.91012 13.807"
        stroke={color || "#6F6F6F"}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.7305 10.9589C17.2794 10.6827 17.881 10.5341 18.4932 10.5234"
        stroke={color || "#6F6F6F"}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.6481 15.875H11.0185C9.0763 15.875 7.5 17.5601 7.5 19.6364V27.1591C7.5 29.2354 9.0763 30.9205 11.0185 30.9205H16.6481C18.5904 30.9205 20.1667 29.2354 20.1667 27.1591V19.6364C20.1667 17.5601 18.5904 15.875 16.6481 15.875ZM11.6237 28.3853C11.49 28.5283 11.3141 28.6035 11.1311 28.6035C10.9411 28.6035 10.7652 28.5283 10.6315 28.3853C10.4978 28.2424 10.4204 28.0543 10.4204 27.8512C10.4204 27.6556 10.4978 27.46 10.6315 27.3171C10.6948 27.2494 10.7722 27.1967 10.8567 27.1591C11.0326 27.0839 11.2226 27.0839 11.3985 27.1591C11.4407 27.1742 11.483 27.1967 11.5181 27.2268C11.5604 27.2494 11.5956 27.287 11.6237 27.3171C11.7574 27.46 11.8348 27.6556 11.8348 27.8512C11.8348 28.0543 11.7574 28.2424 11.6237 28.3853ZM10.4204 24.8421C10.4204 24.7443 10.4415 24.6465 10.4767 24.5562C10.5119 24.4585 10.5611 24.3832 10.6315 24.308C10.7933 24.135 11.0396 24.0522 11.2648 24.1049C11.307 24.1124 11.3563 24.1275 11.3985 24.15C11.4407 24.1651 11.483 24.1876 11.5181 24.2177C11.5604 24.2403 11.5956 24.2779 11.6237 24.308C11.687 24.3832 11.7433 24.4585 11.7785 24.5562C11.8137 24.6465 11.8278 24.7443 11.8278 24.8421C11.8278 25.0452 11.7574 25.2333 11.6237 25.3762C11.49 25.5192 11.3141 25.5944 11.1311 25.5944C11.0326 25.5944 10.9411 25.5718 10.8567 25.5342C10.7722 25.4966 10.6948 25.4439 10.6315 25.3762C10.4978 25.2333 10.4204 25.0452 10.4204 24.8421ZM14.4385 28.3853C14.3752 28.453 14.2978 28.5057 14.2133 28.5433C14.1289 28.5809 14.0374 28.6035 13.9389 28.6035C13.7559 28.6035 13.58 28.5283 13.4463 28.3853C13.3126 28.2424 13.2352 28.0543 13.2352 27.8512C13.2352 27.7986 13.2422 27.7534 13.2493 27.7008C13.2633 27.6556 13.2774 27.6105 13.2915 27.5653C13.3126 27.5202 13.3337 27.4751 13.3548 27.4299C13.383 27.3923 13.4111 27.3547 13.4463 27.3171C13.5096 27.2494 13.587 27.1967 13.6715 27.1591C13.9319 27.0463 14.2415 27.1065 14.4385 27.3171C14.5722 27.46 14.6426 27.6556 14.6426 27.8512C14.6426 28.0543 14.5722 28.2424 14.4385 28.3853ZM14.4385 25.3762C14.3048 25.5192 14.1289 25.5944 13.9389 25.5944C13.7559 25.5944 13.58 25.5192 13.4463 25.3762C13.3126 25.2333 13.2352 25.0452 13.2352 24.8421C13.2352 24.6465 13.3126 24.4509 13.4463 24.308C13.7067 24.0297 14.1781 24.0297 14.4385 24.308C14.5019 24.3832 14.5581 24.4585 14.5933 24.5562C14.6285 24.6465 14.6426 24.7443 14.6426 24.8421C14.6426 25.0452 14.5722 25.2333 14.4385 25.3762ZM11.7222 22.2392C10.9974 22.2392 10.3993 21.6073 10.3993 20.825V20.0727C10.3993 19.2978 10.9904 18.6584 11.7222 18.6584H15.9444C16.6693 18.6584 17.2674 19.2903 17.2674 20.0727V20.825C17.2674 21.5998 16.6763 22.2392 15.9444 22.2392H11.7222ZM17.2533 28.3853C17.1196 28.5283 16.9437 28.6035 16.7537 28.6035C16.6622 28.6035 16.5707 28.5809 16.4863 28.5433C16.4019 28.5057 16.3244 28.453 16.2611 28.3853C16.1274 28.2424 16.057 28.0543 16.057 27.8512C16.057 27.6556 16.1274 27.46 16.2611 27.3171C16.4511 27.1065 16.7678 27.0463 17.0281 27.1591C17.1126 27.1967 17.19 27.2494 17.2533 27.3171C17.387 27.46 17.4574 27.6556 17.4574 27.8512C17.4574 28.0543 17.387 28.2424 17.2533 28.3853ZM17.4081 25.128C17.373 25.2183 17.3237 25.301 17.2533 25.3762C17.1196 25.5192 16.9437 25.5944 16.7537 25.5944C16.5707 25.5944 16.3948 25.5192 16.2611 25.3762C16.1274 25.2333 16.05 25.0452 16.05 24.8421C16.05 24.6465 16.1274 24.4509 16.2611 24.308C16.5215 24.0297 16.993 24.0297 17.2533 24.308C17.387 24.4509 17.4644 24.6465 17.4644 24.8421C17.4644 24.9399 17.4433 25.0377 17.4081 25.128Z"
        fill={color || "#6F6F6F"}
      />
    </svg>
  );
};
export const SupplyChainIcon = ({ color }) => {
  return (
    <svg
      width="25"
      height="20"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20 30.3333C19.64 30.3333 19.3067 30.1333 19.1333 29.8267C18.96 29.5067 18.96 29.1333 19.1467 28.8133L20.5467 26.48C20.8267 26.0133 21.44 25.8533 21.92 26.1333C22.4 26.4133 22.5467 27.0267 22.2667 27.5067L21.9067 28.1067C25.5867 27.24 28.3467 23.9333 28.3467 19.9867C28.3467 19.44 28.8 18.9867 29.3467 18.9867C29.8933 18.9867 30.3467 19.44 30.3467 19.9867C30.3333 25.6933 25.6933 30.3333 20 30.3333Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M2.66666 13C2.12 13 1.66666 12.5467 1.66666 12C1.66666 6.30667 6.30666 1.66667 12 1.66667C12.36 1.66667 12.6933 1.86667 12.8667 2.17333C13.04 2.49333 13.04 2.86667 12.8533 3.18667L11.4533 5.52C11.1733 5.98667 10.56 6.14667 10.08 5.85333C9.61333 5.57333 9.45333 4.96 9.74666 4.48L10.1067 3.88C6.41333 4.74667 3.66666 8.05333 3.66666 12C3.66666 12.5467 3.21333 13 2.66666 13Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M21.8667 10.9467L16.8533 8.24C16.32 7.96 15.6933 7.96 15.16 8.24L10.1333 10.9467C9.77334 11.1467 9.54667 11.5333 9.54667 11.96C9.54667 12.4 9.77334 12.7867 10.1333 12.9867L15.1467 15.6933C15.4133 15.84 15.7067 15.9067 16 15.9067C16.2933 15.9067 16.5867 15.84 16.8533 15.6933L21.8667 12.9867C22.2267 12.7867 22.4533 12.4 22.4533 11.96C22.4533 11.5333 22.2267 11.1467 21.8667 10.9467Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M14.32 16.6267L9.65333 14.2933C9.29333 14.12 8.88 14.1333 8.53333 14.3467C8.2 14.56 8 14.92 8 15.32V19.7333C8 20.4933 8.42667 21.1867 9.10667 21.52L13.7733 23.8533C13.9333 23.9333 14.1067 23.9733 14.2933 23.9733C14.5067 23.9733 14.7067 23.92 14.8933 23.8C15.24 23.5867 15.44 23.2267 15.44 22.8267V18.4133C15.4267 17.6533 15.0133 16.9733 14.32 16.6267Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M23.4533 14.3467C23.1067 14.1333 22.6933 14.12 22.3333 14.2933L17.6667 16.6267C16.9867 16.9733 16.56 17.6533 16.56 18.4133V22.8267C16.56 23.2267 16.76 23.5867 17.1067 23.8C17.2933 23.92 17.4933 23.9733 17.7067 23.9733C17.88 23.9733 18.0533 23.9333 18.2267 23.8533L22.8933 21.52C23.5733 21.1733 24 20.4933 24 19.7333V15.32C24 14.92 23.8 14.56 23.4533 14.3467Z"
        fill={color || "#6F6F6F"}
      />
    </svg>
  );
};
export const DecarbonisationIcon = ({ color }) => {
  return (
    <svg
      width="21"
      height="20"
      viewBox="0 0 21 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_0_3)">
        <path
          d="M12.3261 6.15735C11.4964 6.15735 10.8261 6.82766 10.8261 7.65735C10.8261 8.48262 11.4964 9.15735 12.3261 9.15735C13.1559 9.15735 13.8261 8.48262 13.8261 7.65735C13.8257 6.82766 13.1559 6.15735 12.3261 6.15735Z"
          fill={color || "#6F6F6F"}
        />
        <path
          d="M17.372 4.07309C16.6289 1.8019 14.4738 0.166126 12.0288 0.157426C9.50015 0.14457 7.3585 1.76748 6.6418 4.00009C6.52751 4.34764 6.20637 4.58401 5.83713 4.58401C3.8846 4.58401 2.13853 5.93653 1.86618 7.82118C1.52759 10.1566 3.37903 12.1574 5.70545 12.1574H18.1115C19.6376 12.1574 20.8687 10.9252 20.825 9.42666C20.8029 8.70125 20.469 8.05715 19.954 7.59763C19.4353 7.13395 18.7403 6.85484 17.9973 6.85484C17.7377 6.85484 17.5397 6.63169 17.575 6.37828C17.6757 5.65287 17.6362 4.88016 17.372 4.07309ZM9.47377 9.74024C9.22739 9.8219 8.9725 9.86464 8.71294 9.86464C7.42866 9.86464 6.3865 8.84724 6.3865 7.59347C6.3865 6.3397 7.42866 5.3223 8.71294 5.3223C8.9725 5.3223 9.22739 5.36088 9.46916 5.4426C9.69773 5.51975 9.82094 5.76445 9.74191 5.98799C9.66288 6.21564 9.41219 6.33138 9.18321 6.25422C9.03367 6.20693 8.87526 6.18122 8.71253 6.18122C7.91213 6.18122 7.26553 6.8125 7.26553 7.59387C7.26553 8.37108 7.91213 9.00651 8.71253 9.00651C8.87526 9.00651 9.03367 8.98075 9.18321 8.92935C9.41178 8.85219 9.66247 8.97209 9.74191 9.19558C9.82129 9.418 9.70274 9.66269 9.47377 9.74024ZM12.2314 9.86464C10.9471 9.86464 9.90499 8.84724 9.90499 7.59347C9.90499 6.3397 10.9471 5.3223 12.2314 5.3223C13.5157 5.3223 14.5578 6.3397 14.5578 7.59347C14.5578 8.84724 13.5153 9.86464 12.2314 9.86464ZM16.6994 11.157H15.1207C14.9406 11.157 14.7778 11.0496 14.7116 10.8866C14.6458 10.7236 14.6853 10.539 14.8127 10.4188L16.0354 9.27234C16.1144 9.19518 16.1144 9.08783 16.0838 9.01899C16.0617 8.96298 16.0179 8.8987 15.9168 8.89454C15.7189 8.89454 15.5608 9.04925 15.5608 9.23793C15.5608 9.47396 15.3629 9.66725 15.1211 9.66725C14.8793 9.66725 14.6814 9.47396 14.6814 9.23793C14.6814 8.57685 15.2311 8.03601 15.9126 8.03601C16.3701 8.04467 16.7393 8.29774 16.902 8.70125C17.069 9.11769 16.9679 9.58137 16.6425 9.88618L16.2117 10.2985H16.6998C16.9462 10.2985 17.1395 10.4918 17.1395 10.7277C17.1391 10.9638 16.9454 11.157 16.6994 11.157Z"
          fill={color || "#6F6F6F"}
        />
        <path
          d="M7.25271 13.1574V15.9683H5.82609L8.32585 19.1574L10.8261 15.9683H9.3994V13.1574H7.25271Z"
          fill={color || "#6F6F6F"}
        />
        <path
          d="M13.4266 13V15.811H12L14.4998 19L17 15.811H15.5733V13H13.4266Z"
          fill={color || "#6F6F6F"}
        />
      </g>
      <defs>
        <clipPath id="clip0_0_3">
          <rect width="21" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
export const FinancedEmissionsIcon = ({ color }) => {
  return (
    <svg
      width="21"
      height="21"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17 12.5511V8.30407C17.4804 8.45068 17.9797 8.52582 18.482 8.52707C19.3548 8.51842 20.2148 8.31623 21 7.93507C21.9291 7.55216 22.73 6.91266 23.3091 6.09134C23.8882 5.27003 24.2215 4.30083 24.27 3.29707C24.268 3.15549 24.2359 3.01596 24.1759 2.8877C24.1159 2.75944 24.0294 2.64538 23.922 2.55307C23.1216 1.94522 22.1643 1.57831 21.1627 1.49541C20.161 1.41252 19.1565 1.61707 18.267 2.08507C17.3629 2.48937 16.578 3.11959 15.988 3.91508C15.368 3.06911 14.5385 2.39914 13.581 1.97107C12.651 1.48232 11.601 1.26847 10.5539 1.35454C9.50674 1.4406 8.50577 1.82302 7.66801 2.45707C7.56076 2.54927 7.47429 2.66317 7.4143 2.79124C7.35432 2.91932 7.32218 3.05866 7.32001 3.20007C7.37025 4.24953 7.71841 5.26296 8.32374 6.12171C8.92906 6.98047 9.7665 7.64901 10.738 8.04908C11.5618 8.44816 12.4637 8.6599 13.379 8.66908C13.9292 8.66776 14.476 8.58176 15 8.41407V12.5511C12.5806 12.8072 10.3517 13.9817 8.77274 15.8325C7.19373 17.6834 6.38499 20.0695 6.51321 22.499C6.64144 24.9285 7.69686 27.2162 9.46192 28.8906C11.227 30.5649 13.5671 31.4983 16 31.4983C18.4329 31.4983 20.7731 30.5649 22.5381 28.8906C24.3032 27.2162 25.3586 24.9285 25.4868 22.499C25.615 20.0695 24.8063 17.6834 23.2273 15.8325C21.6483 13.9817 19.4194 12.8072 17 12.5511ZM16 20.9971C16.6737 20.9959 17.3264 21.2318 17.8436 21.6635C18.3608 22.0952 18.7096 22.6951 18.8289 23.3582C18.9481 24.0213 18.8303 24.7052 18.4959 25.2901C18.1615 25.8749 17.6319 26.3235 17 26.5571V26.6841C17 26.9493 16.8947 27.2036 16.7071 27.3912C16.5196 27.5787 16.2652 27.6841 16 27.6841C15.7348 27.6841 15.4804 27.5787 15.2929 27.3912C15.1054 27.2036 15 26.9493 15 26.6841V26.5571C14.451 26.354 13.9773 25.988 13.6421 25.5081C13.307 25.0282 13.1266 24.4574 13.125 23.8721C13.125 23.6069 13.2304 23.3525 13.4179 23.165C13.6054 22.9774 13.8598 22.8721 14.125 22.8721C14.3902 22.8721 14.6446 22.9774 14.8321 23.165C15.0197 23.3525 15.125 23.6069 15.125 23.8721C15.125 24.0451 15.1763 24.2143 15.2725 24.3582C15.3686 24.5021 15.5053 24.6142 15.6652 24.6805C15.825 24.7467 16.001 24.764 16.1707 24.7303C16.3404 24.6965 16.4964 24.6132 16.6187 24.4908C16.7411 24.3684 16.8244 24.2125 16.8582 24.0428C16.892 23.873 16.8746 23.6971 16.8084 23.5372C16.7422 23.3773 16.63 23.2407 16.4861 23.1445C16.3422 23.0484 16.1731 22.9971 16 22.9971C15.3258 22.9989 14.6725 22.7633 14.1547 22.3315C13.6369 21.8997 13.2877 21.2994 13.1684 20.6359C13.0491 19.9723 13.1672 19.2879 13.5022 18.7028C13.8371 18.1177 14.3674 17.6692 15 17.4361V17.3091C15 17.0439 15.1054 16.7895 15.2929 16.602C15.4804 16.4144 15.7348 16.3091 16 16.3091C16.2652 16.3091 16.5196 16.4144 16.7071 16.602C16.8947 16.7895 17 17.0439 17 17.3091V17.4361C17.5497 17.6395 18.024 18.0063 18.3591 18.4872C18.6943 18.968 18.8743 19.5399 18.875 20.1261C18.875 20.3913 18.7697 20.6456 18.5821 20.8332C18.3946 21.0207 18.1402 21.1261 17.875 21.1261C17.6098 21.1261 17.3554 21.0207 17.1679 20.8332C16.9804 20.6456 16.875 20.3913 16.875 20.1261C16.875 19.953 16.8237 19.7838 16.7275 19.64C16.6314 19.4961 16.4947 19.3839 16.3349 19.3177C16.175 19.2515 15.999 19.2341 15.8293 19.2679C15.6596 19.3016 15.5037 19.385 15.3813 19.5074C15.2589 19.6297 15.1756 19.7856 15.1418 19.9554C15.1081 20.1251 15.1254 20.301 15.1916 20.4609C15.2578 20.6208 15.37 20.7575 15.5139 20.8536C15.6578 20.9498 15.827 21.0011 16 21.0011V20.9971Z"
        fill={color || "#6F6F6F"}
      />
    </svg>
  );
};
export const ProfileAnalytics = ({ color }) => {
  return (
    <svg
      width="30"
      height="30"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M2 22H22" stroke={color || "#8F8F8F"} />
      <path
        d="M9.75 4V22H14.25V4C14.25 2.9 13.8 2 12.45 2H11.55C10.2 2 9.75 2.9 9.75 4Z"
        stroke={color || "#8F8F8F"}
      />
      <path
        d="M3 10V22H7V10C7 8.9 6.6 8 5.4 8H4.6C3.4 8 3 8.9 3 10Z"
        stroke={color || "#8F8F8F"}
      />
      <path
        d="M17 15V22H21V15C21 13.9 20.6 13 19.4 13H18.6C17.4 13 17 13.9 17 15Z"
        stroke={color || "#8F8F8F"}
      />
    </svg>
  );
};
export const ListedEquity = ({ color }) => {
  return (
    <svg
      width="30"
      height="30"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.6317 2.21048C16.2217 1.80048 15.5117 2.08048 15.5117 2.65048V6.14048C15.5117 7.60048 16.7517 8.81048 18.2617 8.81048C19.2117 8.82048 20.5317 8.82048 21.6617 8.82048C22.2317 8.82048 22.5317 8.15048 22.1317 7.75048C20.6917 6.30048 18.1117 3.69048 16.6317 2.21048Z"
        fill={color || "#8F8F8F"}
      />
      <path
        d="M21.332 10.19H18.442C16.072 10.19 14.142 8.26 14.142 5.89V3C14.142 2.45 13.692 2 13.142 2H8.90203C5.82203 2 3.33203 4 3.33203 7.57V16.43C3.33203 20 5.82203 22 8.90203 22H16.762C19.842 22 22.332 20 22.332 16.43V11.19C22.332 10.64 21.882 10.19 21.332 10.19ZM12.332 17.75H8.33203C7.92203 17.75 7.58203 17.41 7.58203 17C7.58203 16.59 7.92203 16.25 8.33203 16.25H12.332C12.742 16.25 13.082 16.59 13.082 17C13.082 17.41 12.742 17.75 12.332 17.75ZM14.332 13.75H8.33203C7.92203 13.75 7.58203 13.41 7.58203 13C7.58203 12.59 7.92203 12.25 8.33203 12.25H14.332C14.742 12.25 15.082 12.59 15.082 13C15.082 13.41 14.742 13.75 14.332 13.75Z"
        fill={color || "#8F8F8F"}
      />
    </svg>
  );
};
export const ProjectFinance = ({ color }) => {
  return (
    <svg
      width="30"
      height="30"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.633 7.91949V13.0695C19.633 16.1495 17.873 17.4695 15.233 17.4695H6.44296C5.99296 17.4695 5.56296 17.4295 5.16296 17.3395C4.91296 17.2995 4.67297 17.2295 4.45297 17.1495C2.95297 16.5895 2.04297 15.2895 2.04297 13.0695V7.91949C2.04297 4.83949 3.80296 3.51953 6.44296 3.51953H15.233C17.473 3.51953 19.083 4.46953 19.513 6.63953C19.583 7.03953 19.633 7.44949 19.633 7.91949Z"
        stroke={color || "#8F8F8F"}
      />
      <path
        d="M22.6341 10.9206V16.0706C22.6341 19.1506 20.8741 20.4706 18.2341 20.4706H9.44406C8.70406 20.4706 8.03407 20.3706 7.45407 20.1506C6.26407 19.7106 5.45406 18.8006 5.16406 17.3406C5.56406 17.4306 5.99406 17.4706 6.44406 17.4706H15.2341C17.8741 17.4706 19.6341 16.1506 19.6341 13.0706V7.92059C19.6341 7.45059 19.5941 7.03062 19.5141 6.64062C21.4141 7.04063 22.6341 8.38059 22.6341 10.9206Z"
        stroke={color || "#8F8F8F"}
      />
      <path
        d="M10.8314 13.1394C12.2895 13.1394 13.4714 11.9574 13.4714 10.4994C13.4714 9.04136 12.2895 7.85938 10.8314 7.85938C9.37339 7.85938 8.19141 9.04136 8.19141 10.4994C8.19141 11.9574 9.37339 13.1394 10.8314 13.1394Z"
        stroke={color || "#8F8F8F"}
      />
      <path d="M5.11328 8.30078V12.7008" stroke={color || "#8F8F8F"} />
      <path d="M16.5547 8.30078V12.7008" stroke={color || "#8F8F8F"} />
    </svg>
  );
};
export const AutoLoans = ({ color }) => {
  return (
    <svg
      width="30"
      height="30"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20.9427 16.8354C20.2864 12.8866 16.7762 8.72713 15 7H9C7.22378 8.72713 3.71361 12.8866 3.05727 16.8354C2.56893 19.7734 5.27927 22 8.30832 22H15.6917C18.7207 22 21.4311 19.7734 20.9427 16.8354Z"
        stroke={color || "#8F8F8F"}
      />
      <path
        d="M17 3.5L17.6512 3.8721C17.7949 3.62066 17.7809 3.30897 17.6154 3.07133C17.4499 2.8337 17.1624 2.71259 16.8767 2.7602L17 3.5ZM14 4L13.4697 4.53033L13.7426 4.80325L14.1233 4.7398L14 4ZM12 2L12.5303 1.46967C12.2374 1.17678 11.7626 1.17678 11.4697 1.46967L12 2ZM7 3.5L7.1233 2.7602C6.83764 2.71259 6.5501 2.8337 6.38458 3.07133C6.21906 3.30897 6.20514 3.62066 6.34882 3.8721L7 3.5ZM10 4L9.8767 4.7398L10.2574 4.80325L10.5303 4.53033L10 4ZM16.3488 3.1279L14.3488 6.6279L15.6512 7.3721L17.6512 3.8721L16.3488 3.1279ZM16.8767 2.7602L13.8767 3.2602L14.1233 4.7398L17.1233 4.2398L16.8767 2.7602ZM14.5303 3.46967L12.5303 1.46967L11.4697 2.53033L13.4697 4.53033L14.5303 3.46967ZM6.34882 3.8721L8.34882 7.3721L9.65118 6.6279L7.65118 3.1279L6.34882 3.8721ZM6.8767 4.2398L9.8767 4.7398L10.1233 3.2602L7.1233 2.7602L6.8767 4.2398ZM10.5303 4.53033L12.5303 2.53033L11.4697 1.46967L9.46967 3.46967L10.5303 4.53033Z"
        fill={color || "#8F8F8F"}
      />
      <path
        d="M12 11.5C10.8954 11.5 10 12.1716 10 13C10 13.8284 10.8954 14.5 12 14.5C13.1046 14.5 14 15.1716 14 16C14 16.8284 13.1046 17.5 12 17.5M12 11.5C12.8708 11.5 13.6116 11.9174 13.8862 12.5M12 11.5V10M12 17.5C11.1292 17.5 10.3884 17.0826 10.1138 16.5M12 17.5V19"
        stroke={color || "#8F8F8F"}
      />
    </svg>
  );
};
export const BusinessLoans = ({ color }) => {
  return (
    <svg
      width="30"
      height="30"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.66797 14C2.66797 10.4934 2.66797 8.74003 3.57593 7.55992C3.74214 7.34388 3.92786 7.14579 4.1304 6.96849C5.23675 6 6.88049 6 10.168 6H15.168C18.4555 6 20.0992 6 21.2055 6.96849C21.4081 7.14579 21.5938 7.34388 21.76 7.55992C22.668 8.74003 22.668 10.4934 22.668 14C22.668 17.5066 22.668 19.26 21.76 20.4401C21.5938 20.6561 21.4081 20.8542 21.2055 21.0315C20.0992 22 18.4555 22 15.168 22H10.168C6.88049 22 5.23675 22 4.1304 21.0315C3.92786 20.8542 3.74214 20.6561 3.57593 20.4401C2.66797 19.26 2.66797 17.5066 2.66797 14Z"
        stroke={color || "#8F8F8F"}
      />
      <path
        d="M16.668 6C16.668 4.11438 16.668 3.17157 16.0822 2.58579C15.4964 2 14.5536 2 12.668 2C10.7824 2 9.83954 2 9.25376 2.58579C8.66797 3.17157 8.66797 4.11438 8.66797 6"
        stroke={color || "#8F8F8F"}
      />
      <path
        d="M12.668 11C11.5634 11 10.668 11.6716 10.668 12.5C10.668 13.3284 11.5634 14 12.668 14C13.7725 14 14.668 14.6716 14.668 15.5C14.668 16.3284 13.7725 17 12.668 17M12.668 11C13.5388 11 14.2796 11.4174 14.5542 12M12.668 11V10M12.668 17C11.7972 17 11.0563 16.5826 10.7818 16M12.668 17V18"
        stroke={color || "#8F8F8F"}
      />
      <path d="M6.66797 12H2.66797" stroke={color || "#8F8F8F"} />
      <path d="M22.668 12L18.668 12" stroke={color || "#8F8F8F"} />
    </svg>
  );
};
export const RealEstate = ({ color }) => {
  return (
    <svg
      width="30"
      height="30"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.5 8H10.5C8.018 8 7.5 8.518 7.5 11V22H17.5V11C17.5 8.518 16.982 8 14.5 8Z"
        stroke={color || "#8F8F8F"}
      />
      <path
        d="M11.5 12L13.5 12M11.5 15H13.5M11.5 18H13.5"
        stroke={color || "#8F8F8F"}
      />
      <path
        d="M21.5 22V8.18564C21.5 6.95735 21.5 6.3432 21.2013 5.84966C20.9026 5.35612 20.3647 5.08147 19.2889 4.53216L14.9472 2.31536C13.7868 1.72284 13.5 1.93166 13.5 3.22873V7.7035"
        stroke={color || "#8F8F8F"}
      />
      <path
        d="M3.5 22V13C3.5 12.1727 3.67267 12 4.5 12H7.5"
        stroke={color || "#8F8F8F"}
      />
      <path d="M22.5 22H2.5" stroke={color || "#8F8F8F"} />
    </svg>
  );
};
export const SovereignBonds = ({ color }) => {
  return (
    <svg
      width="30"
      height="30"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.168 15C15.8959 15 18.918 12.0899 18.918 8.5C18.918 4.91015 15.8959 2 12.168 2C8.44005 2 5.41797 4.91015 5.41797 8.5C5.41797 12.0899 8.44005 15 12.168 15Z"
        stroke={color || "#8F8F8F"}
      />
      <path
        d="M7.68967 13.5198L7.67969 20.8998C7.67969 21.7998 8.30969 22.2398 9.08969 21.8698L11.7697 20.5999C11.9897 20.4899 12.3597 20.4899 12.5797 20.5999L15.2697 21.8698C16.0397 22.2298 16.6797 21.7998 16.6797 20.8998V13.3398"
        stroke={color || "#8F8F8F"}
      />
    </svg>
  );
};
export const GreenSightIcon = ({ color }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 30 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.2684 8.92037C6.2684 4.30265 10.0086 0.5625 14.6263 0.5625C6.55048 0.5625 0 8.92037 0 8.92037C0 8.92037 6.55048 17.2782 14.6263 17.2782C10.0086 17.2782 6.2684 13.5381 6.2684 8.92037Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M20.9748 8.1668C20.0502 7.60265 18.7025 7.67055 17.5481 8.33396C16.4877 8.94513 15.7877 9.94285 15.6937 10.9406C15.6571 10.9771 15.6153 11.0137 15.5788 11.0503V9.24288C16.3676 8.6526 16.869 7.56608 16.869 6.36464C16.869 5.0326 16.2579 3.83116 15.3071 3.30879C15.1504 3.21999 14.9624 3.21999 14.8057 3.30879C13.855 3.83116 13.2438 5.0326 13.2438 6.36464C13.2438 7.56608 13.7453 8.6526 14.534 9.24288V11.0346C14.4923 10.998 14.4452 10.9615 14.3982 10.9249C14.2676 9.9324 13.5311 8.95558 12.4446 8.3862C11.264 7.76458 9.91632 7.75413 9.01263 8.34963C8.86114 8.44888 8.77234 8.61604 8.77756 8.79364C8.79846 9.87494 9.57678 10.9771 10.7573 11.5988C11.3633 11.9174 12.0058 12.0741 12.617 12.0741C13.061 12.0741 13.4789 11.9905 13.8497 11.8234C14.1736 12.0898 14.4087 12.3562 14.534 12.6069V14.08C14.534 14.3673 14.7691 14.6024 15.0564 14.6024C15.3437 14.6024 15.5788 14.3673 15.5788 14.08V12.633C15.7721 12.3771 16.0489 12.048 16.2788 11.8181C16.6183 11.954 16.9892 12.0219 17.3809 12.0219C18.0287 12.0219 18.7182 11.839 19.3503 11.4734C20.5047 10.81 21.2412 9.68167 21.2256 8.59514C21.2256 8.41754 21.1263 8.25038 20.9748 8.15636V8.1668Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M15.375 0.5625C19.9927 0.5625 23.7329 4.30265 23.7329 8.92037C23.7329 13.5381 19.9927 17.2782 15.375 17.2782C23.4508 17.2782 30.0013 8.92037 30.0013 8.92037C30.0013 8.92037 23.4508 0.5625 15.375 0.5625Z"
        fill={color || "#6F6F6F"}
      />
    </svg>
  );
};
export const StakeholderEngagementIcon = ({ color }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.0013 3.08594C8.50797 3.08594 5.66797 5.92594 5.66797 9.41927C5.66797 12.8459 8.34797 15.6193 11.8413 15.7393C11.948 15.7259 12.0546 15.7259 12.1346 15.7393C12.1613 15.7393 12.1746 15.7393 12.2013 15.7393C12.2146 15.7393 12.2146 15.7393 12.228 15.7393C15.6413 15.6193 18.3213 12.8459 18.3346 9.41927C18.3346 5.92594 15.4946 3.08594 12.0013 3.08594Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M18.7733 19.2897C15.0533 16.8097 8.98661 16.8097 5.23995 19.2897C3.54661 20.423 2.61328 21.9564 2.61328 23.5964C2.61328 25.2364 3.54661 26.7564 5.22661 27.8764C7.09328 29.1297 9.54661 29.7564 11.9999 29.7564C14.4533 29.7564 16.9066 29.1297 18.7733 27.8764C20.4533 26.743 21.3866 25.223 21.3866 23.5697C21.3733 21.9297 20.4533 20.4097 18.7733 19.2897Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M26.6525 10.2061C26.8658 12.7927 25.0258 15.0594 22.4792 15.3661C22.4658 15.3661 22.4658 15.3661 22.4525 15.3661H22.4125C22.3325 15.3661 22.2525 15.3661 22.1858 15.3927C20.8925 15.4594 19.7058 15.0461 18.8125 14.2861C20.1858 13.0594 20.9725 11.2194 20.8125 9.2194C20.7192 8.1394 20.3458 7.15274 19.7858 6.31274C20.2925 6.0594 20.8792 5.8994 21.4792 5.84607C24.0925 5.6194 26.4258 7.56607 26.6525 10.2061Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M29.319 22.5398C29.2123 23.8331 28.3857 24.9531 26.999 25.7131C25.6657 26.4465 23.9857 26.7931 22.319 26.7531C23.279 25.8865 23.839 24.8065 23.9457 23.6598C24.079 22.0065 23.2923 20.4198 21.719 19.1531C20.8257 18.4465 19.7857 17.8865 18.6523 17.4731C21.599 16.6198 25.3057 17.1931 27.5857 19.0331C28.8123 20.0198 29.439 21.2598 29.319 22.5398Z"
        fill={color || "#6F6F6F"}
      />
    </svg>
  );
};
export const DoubleMaterialityIcon = ({ color }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.999 30.7552C19.639 30.7552 19.3056 30.5552 19.1323 30.2485C18.959 29.9419 18.959 29.5552 19.1456 29.2485L20.5456 26.9152C20.8256 26.4352 21.439 26.2885 21.919 26.5685C22.399 26.8485 22.5456 27.4619 22.2656 27.9419L21.9056 28.5419C25.5856 27.6752 28.3456 24.3685 28.3456 20.4219C28.3456 19.8752 28.799 19.4219 29.3456 19.4219C29.8923 19.4219 30.3456 19.8752 30.3456 20.4219C30.3323 26.1152 25.6923 30.7552 19.999 30.7552Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M2.66797 13.4193C2.1213 13.4193 1.66797 12.9659 1.66797 12.4193C1.66797 6.72594 6.30797 2.08594 12.0013 2.08594C12.3613 2.08594 12.6946 2.28594 12.868 2.5926C13.0413 2.89927 13.0413 3.28594 12.8546 3.5926L11.4546 5.93927C11.1746 6.40594 10.5613 6.56594 10.0813 6.2726C9.61463 5.9926 9.45463 5.37927 9.74797 4.89927L10.108 4.29927C6.41464 5.16594 3.66797 8.4726 3.66797 12.4193C3.66797 12.9659 3.21464 13.4193 2.66797 13.4193Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M14.2253 18.8865L10.0386 16.6331C9.59865 16.3931 9.06531 16.3931 8.62531 16.6331L4.43865 18.8865C4.13198 19.0465 3.94531 19.3798 3.94531 19.7398C3.94531 20.0998 4.13198 20.4331 4.43865 20.5931L8.62531 22.8465C8.85198 22.9665 9.09198 23.0198 9.33198 23.0198C9.57198 23.0198 9.81198 22.9665 10.0386 22.8465L14.2253 20.5931C14.532 20.4331 14.7186 20.0998 14.7186 19.7398C14.7186 19.3798 14.5186 19.0598 14.2253 18.8865Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M7.93463 23.6334L4.0413 21.6868C3.74797 21.5401 3.4013 21.5534 3.10797 21.7268C2.8413 21.9001 2.66797 22.2068 2.66797 22.5401V26.2201C2.66797 26.8601 3.01464 27.4334 3.58797 27.7134L7.4813 29.6601C7.61464 29.7134 7.7613 29.7534 7.90797 29.7534C8.0813 29.7534 8.25463 29.7001 8.41464 29.6068C8.69463 29.4334 8.86797 29.1268 8.86797 28.7934V25.1134C8.86797 24.4868 8.50797 23.9134 7.93463 23.6334Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M15.5474 21.7268C15.2674 21.5534 14.9208 21.5401 14.6141 21.6868L10.7208 23.6334C10.1474 23.9134 9.80078 24.4868 9.80078 25.1268V28.8068C9.80078 29.1401 9.97411 29.4468 10.2541 29.6201C10.4141 29.7001 10.5874 29.7534 10.7608 29.7534C10.9074 29.7534 11.0541 29.7134 11.1874 29.6468L15.0808 27.7001C15.6541 27.4201 16.0008 26.8468 16.0008 26.2068V22.5268C16.0008 22.2068 15.8274 21.9001 15.5474 21.7268Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M27.5613 5.52708L23.3746 3.27375C22.9346 3.03375 22.4012 3.03375 21.9612 3.27375L17.7746 5.52708C17.4679 5.68708 17.2812 6.02042 17.2812 6.38042C17.2812 6.74042 17.4679 7.07375 17.7746 7.23375L21.9612 9.48708C22.1879 9.60708 22.4279 9.66042 22.6679 9.66042C22.9079 9.66042 23.1479 9.60708 23.3746 9.48708L27.5613 7.23375C27.8679 7.07375 28.0546 6.74042 28.0546 6.38042C28.0546 6.00708 27.8546 5.68708 27.5613 5.52708Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M21.2667 10.2584L17.3733 8.31177C17.08 8.1651 16.7333 8.17844 16.44 8.35177C16.1733 8.5251 16 8.83177 16 9.1651V12.8451C16 13.4851 16.3467 14.0584 16.92 14.3384L20.8133 16.2851C20.9467 16.3518 21.0933 16.3918 21.24 16.3918C21.4133 16.3918 21.5867 16.3384 21.7467 16.2451C22.0267 16.0718 22.2 15.7651 22.2 15.4318V11.7518C22.2 11.1118 21.84 10.5384 21.2667 10.2584Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M28.8795 8.35177C28.5995 8.17844 28.2528 8.1651 27.9461 8.31177L24.0528 10.2584C23.4795 10.5384 23.1328 11.1118 23.1328 11.7518V15.4318C23.1328 15.7651 23.3061 16.0718 23.5861 16.2451C23.7461 16.3384 23.9195 16.3918 24.0928 16.3918C24.2395 16.3918 24.3861 16.3518 24.5195 16.2851L28.4128 14.3384C28.9861 14.0451 29.3328 13.4718 29.3328 12.8451V9.1651C29.3328 8.83177 29.1595 8.5251 28.8795 8.35177Z"
        fill={color || "#6F6F6F"}
      />
    </svg>
  );
};
export const ISSBReadinessIcon = ({ color }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17 12.5511V8.30407C17.4804 8.45068 17.9797 8.52582 18.482 8.52707C19.3548 8.51842 20.2148 8.31623 21 7.93507C21.9291 7.55216 22.73 6.91266 23.3091 6.09134C23.8882 5.27003 24.2215 4.30083 24.27 3.29707C24.268 3.15549 24.2359 3.01596 24.1759 2.8877C24.1159 2.75944 24.0294 2.64538 23.922 2.55307C23.1216 1.94522 22.1643 1.57831 21.1627 1.49541C20.161 1.41252 19.1565 1.61707 18.267 2.08507C17.3629 2.48937 16.578 3.11959 15.988 3.91508C15.368 3.06911 14.5385 2.39914 13.581 1.97107C12.651 1.48232 11.601 1.26847 10.5539 1.35454C9.50674 1.4406 8.50577 1.82302 7.66801 2.45707C7.56076 2.54927 7.47429 2.66317 7.4143 2.79124C7.35432 2.91932 7.32218 3.05866 7.32001 3.20007C7.37025 4.24953 7.71841 5.26296 8.32374 6.12171C8.92906 6.98047 9.7665 7.64901 10.738 8.04908C11.5618 8.44816 12.4637 8.6599 13.379 8.66908C13.9292 8.66776 14.476 8.58176 15 8.41407V12.5511C12.5806 12.8072 10.3517 13.9817 8.77274 15.8325C7.19373 17.6834 6.38499 20.0695 6.51321 22.499C6.64144 24.9285 7.69686 27.2162 9.46192 28.8906C11.227 30.5649 13.5671 31.4983 16 31.4983C18.4329 31.4983 20.7731 30.5649 22.5381 28.8906C24.3032 27.2162 25.3586 24.9285 25.4868 22.499C25.615 20.0695 24.8063 17.6834 23.2273 15.8325C21.6483 13.9817 19.4194 12.8072 17 12.5511ZM16 20.9971C16.6737 20.9959 17.3264 21.2318 17.8436 21.6635C18.3608 22.0952 18.7096 22.6951 18.8289 23.3582C18.9481 24.0213 18.8303 24.7052 18.4959 25.2901C18.1615 25.8749 17.6319 26.3235 17 26.5571V26.6841C17 26.9493 16.8947 27.2036 16.7071 27.3912C16.5196 27.5787 16.2652 27.6841 16 27.6841C15.7348 27.6841 15.4804 27.5787 15.2929 27.3912C15.1054 27.2036 15 26.9493 15 26.6841V26.5571C14.451 26.354 13.9773 25.988 13.6421 25.5081C13.307 25.0282 13.1266 24.4574 13.125 23.8721C13.125 23.6069 13.2304 23.3525 13.4179 23.165C13.6054 22.9774 13.8598 22.8721 14.125 22.8721C14.3902 22.8721 14.6446 22.9774 14.8321 23.165C15.0197 23.3525 15.125 23.6069 15.125 23.8721C15.125 24.0451 15.1763 24.2143 15.2725 24.3582C15.3686 24.5021 15.5053 24.6142 15.6652 24.6805C15.825 24.7467 16.001 24.764 16.1707 24.7303C16.3404 24.6965 16.4964 24.6132 16.6187 24.4908C16.7411 24.3684 16.8244 24.2125 16.8582 24.0428C16.892 23.873 16.8746 23.6971 16.8084 23.5372C16.7422 23.3773 16.63 23.2407 16.4861 23.1445C16.3422 23.0484 16.1731 22.9971 16 22.9971C15.3258 22.9989 14.6725 22.7633 14.1547 22.3315C13.6369 21.8997 13.2877 21.2994 13.1684 20.6359C13.0491 19.9723 13.1672 19.2879 13.5022 18.7028C13.8371 18.1177 14.3674 17.6692 15 17.4361V17.3091C15 17.0439 15.1054 16.7895 15.2929 16.602C15.4804 16.4144 15.7348 16.3091 16 16.3091C16.2652 16.3091 16.5196 16.4144 16.7071 16.602C16.8947 16.7895 17 17.0439 17 17.3091V17.4361C17.5497 17.6395 18.024 18.0063 18.3591 18.4872C18.6943 18.968 18.8743 19.5399 18.875 20.1261C18.875 20.3913 18.7697 20.6456 18.5821 20.8332C18.3946 21.0207 18.1402 21.1261 17.875 21.1261C17.6098 21.1261 17.3554 21.0207 17.1679 20.8332C16.9804 20.6456 16.875 20.3913 16.875 20.1261C16.875 19.953 16.8237 19.7838 16.7275 19.64C16.6314 19.4961 16.4947 19.3839 16.3349 19.3177C16.175 19.2515 15.999 19.2341 15.8293 19.2679C15.6596 19.3016 15.5037 19.385 15.3813 19.5074C15.2589 19.6297 15.1756 19.7856 15.1418 19.9554C15.1081 20.1251 15.1254 20.301 15.1916 20.4609C15.2578 20.6208 15.37 20.7575 15.5139 20.8536C15.6578 20.9498 15.827 21.0011 16 21.0011V20.9971Z"
        fill={color || "#6F6F6F"}
      />
    </svg>
  );
};
export const IssbReportingSuiteIcon = ({ color }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.1382 3.08984H12.8716C11.4849 3.08984 10.3516 4.20984 10.3516 5.59651V6.84984C10.3516 8.23651 11.4716 9.35651 12.8582 9.35651H19.1382C20.5249 9.35651 21.6449 8.23651 21.6449 6.84984V5.59651C21.6582 4.20984 20.5249 3.08984 19.1382 3.08984Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M22.9867 6.84656C22.9867 8.96656 21.2534 10.6999 19.1334 10.6999H12.8667C10.7467 10.6999 9.01339 8.96656 9.01339 6.84656C9.01339 6.0999 8.21339 5.63323 7.54672 5.97989C5.66672 6.97989 4.38672 8.96656 4.38672 11.2466V23.7932C4.38672 27.0732 7.06672 29.7532 10.3467 29.7532H21.6534C24.9334 29.7532 27.6134 27.0732 27.6134 23.7932V11.2466C27.6134 8.96656 26.3334 6.97989 24.4534 5.97989C23.7867 5.63323 22.9867 6.0999 22.9867 6.84656ZM16.5067 23.0199H10.6667C10.1201 23.0199 9.66672 22.5666 9.66672 22.0199C9.66672 21.4732 10.1201 21.0199 10.6667 21.0199H16.5067C17.0534 21.0199 17.5067 21.4732 17.5067 22.0199C17.5067 22.5666 17.0534 23.0199 16.5067 23.0199ZM20.0001 17.6866H10.6667C10.1201 17.6866 9.66672 17.2332 9.66672 16.6866C9.66672 16.1399 10.1201 15.6866 10.6667 15.6866H20.0001C20.5467 15.6866 21.0001 16.1399 21.0001 16.6866C21.0001 17.2332 20.5467 17.6866 20.0001 17.6866Z"
        fill={color || "#6F6F6F"}
      />
    </svg>
  );
};
export const NewIssbReportingSuiteIcon = ({ color }) => {
  return (
    <svg
      width="22"
      height="22"
      viewBox="0 0 32 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.6336 9.46275H6.90842C6.24542 9.46275 5.70795 8.92528 5.70795 8.26228V7.48563C5.70795 6.82262 5.17048 6.28516 4.50748 6.28516H1.41797"
        stroke="#6F6F6F"
        strokeWidth="0.960375"
      />
      <path
        d="M10.6336 16.1193H6.90842C6.24542 16.1193 5.70795 16.6568 5.70795 17.3198V18.0964C5.70795 18.7594 5.17048 19.2969 4.50748 19.2969H1.41797"
        stroke="#6F6F6F"
        strokeWidth="0.960375"
      />
      <circle cx="1.44713" cy="6.34166" r="1.44713" fill={color || "#6F6F6F"} />
      <circle
        cx="1.44713"
        cy="1.44713"
        r="1.44713"
        transform="matrix(1 0 0 -1 0 20.6836)"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M10.6506 12.832H0.761719"
        stroke="#6F6F6F"
        strokeWidth="0.960375"
      />
      <circle cx="1.44713" cy="12.8924" r="1.44713" fill={color || "#6F6F6F"} />
      <path
        d="M28.4992 11.1112H25.6081C23.2372 11.1112 21.3064 9.18041 21.3064 6.80949V3.91836C21.3064 3.36814 20.8562 2.91797 20.306 2.91797H16.0644C12.9832 2.91797 10.4922 4.91875 10.4922 8.49015V17.3536C10.4922 20.925 12.9832 22.9258 16.0644 22.9258H23.9274C27.0086 22.9258 29.4996 20.925 29.4996 17.3536V12.1116C29.4996 11.5613 29.0494 11.1112 28.4992 11.1112Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M23.8001 3.12853C23.39 2.71837 22.6797 2.99848 22.6797 3.5687V7.06006C22.6797 8.52063 23.9202 9.73111 25.4308 9.73111C26.3811 9.74111 27.7016 9.74111 28.8321 9.74111C29.4023 9.74111 29.7024 9.07085 29.3023 8.67069C27.8617 7.22012 25.2807 4.6091 23.8001 3.12853Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M14.3075 19.5352H12.6598L15.1705 12.2624H17.152L19.6591 19.5352H18.0114L16.1896 13.9244H16.1328L14.3075 19.5352ZM14.2045 16.6765H18.0966V17.8768H14.2045V16.6765ZM22.0721 12.2624V19.5352H20.5344V12.2624H22.0721Z"
        fill="white"
      />
    </svg>
  );
};
export const CSRDReadinessIcon = ({ color }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        y="0.421875"
        width="32"
        height="31.4667"
        rx="15.7333"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M11.5705 15.2472H10.2886C10.2795 15.1411 10.2553 15.0449 10.2159 14.9585C10.178 14.8722 10.125 14.7979 10.0568 14.7358C9.99015 14.6722 9.90909 14.6237 9.81364 14.5903C9.71818 14.5555 9.60985 14.5381 9.48864 14.5381C9.27651 14.5381 9.09697 14.5896 8.95 14.6926C8.80454 14.7956 8.69394 14.9434 8.61818 15.1358C8.54394 15.3282 8.50682 15.5593 8.50682 15.829C8.50682 16.1138 8.5447 16.3525 8.62045 16.5449C8.69773 16.7358 8.80909 16.8797 8.95454 16.9767C9.1 17.0722 9.275 17.1199 9.47954 17.1199C9.59621 17.1199 9.70076 17.1055 9.79318 17.0767C9.88561 17.0464 9.96591 17.0032 10.0341 16.9472C10.1023 16.8911 10.1576 16.8237 10.2 16.7449C10.2439 16.6646 10.2735 16.5744 10.2886 16.4744L11.5705 16.4835C11.5553 16.6805 11.5 16.8812 11.4045 17.0858C11.3091 17.2888 11.1735 17.4767 10.9977 17.6494C10.8235 17.8206 10.6076 17.9585 10.35 18.0631C10.0924 18.1676 9.79318 18.2199 9.45227 18.2199C9.025 18.2199 8.64167 18.1282 8.30227 17.9449C7.96439 17.7616 7.69697 17.4919 7.5 17.1358C7.30454 16.7797 7.20682 16.3441 7.20682 15.829C7.20682 15.3108 7.30682 14.8744 7.50682 14.5199C7.70682 14.1638 7.97651 13.8949 8.31591 13.7131C8.6553 13.5297 9.03409 13.4381 9.45227 13.4381C9.74621 13.4381 10.0167 13.4782 10.2636 13.5585C10.5106 13.6388 10.7273 13.7562 10.9136 13.9108C11.1 14.0638 11.25 14.2525 11.3636 14.4767C11.4773 14.7009 11.5462 14.9578 11.5705 15.2472ZM14.6528 14.9562C14.6407 14.8047 14.5839 14.6866 14.4824 14.6017C14.3824 14.5169 14.2301 14.4744 14.0256 14.4744C13.8953 14.4744 13.7884 14.4903 13.7051 14.5222C13.6233 14.5525 13.5627 14.5941 13.5233 14.6472C13.4839 14.7002 13.4634 14.7608 13.4619 14.829C13.4589 14.885 13.4687 14.9358 13.4915 14.9812C13.5157 15.0252 13.5536 15.0653 13.6051 15.1017C13.6566 15.1366 13.7225 15.1684 13.8028 15.1972C13.8831 15.2259 13.9786 15.2517 14.0892 15.2744L14.471 15.3562C14.7286 15.4108 14.9491 15.4828 15.1324 15.5722C15.3157 15.6616 15.4657 15.7669 15.5824 15.8881C15.6991 16.0078 15.7847 16.1426 15.8392 16.2926C15.8953 16.4426 15.9241 16.6062 15.9256 16.7835C15.9241 17.0896 15.8475 17.3487 15.696 17.5608C15.5445 17.7729 15.3278 17.9343 15.046 18.0449C14.7657 18.1555 14.4286 18.2108 14.0347 18.2108C13.6301 18.2108 13.2771 18.1509 12.9756 18.0312C12.6756 17.9116 12.4422 17.7275 12.2756 17.479C12.1104 17.229 12.0271 16.9093 12.0256 16.5199H13.2256C13.2331 16.6623 13.2687 16.782 13.3324 16.879C13.396 16.9759 13.4854 17.0494 13.6006 17.0994C13.7172 17.1494 13.8559 17.1744 14.0165 17.1744C14.1513 17.1744 14.2642 17.1578 14.3551 17.1244C14.446 17.0911 14.515 17.0449 14.5619 16.9858C14.6089 16.9267 14.6331 16.8593 14.6347 16.7835C14.6331 16.7123 14.6097 16.6502 14.5642 16.5972C14.5203 16.5426 14.4475 16.4941 14.346 16.4517C14.2445 16.4078 14.1074 16.3669 13.9347 16.329L13.471 16.229C13.0589 16.1396 12.7339 15.9903 12.496 15.7812C12.2597 15.5706 12.1422 15.2835 12.1437 14.9199C12.1422 14.6244 12.221 14.3661 12.3801 14.1449C12.5407 13.9222 12.7627 13.7487 13.046 13.6244C13.3309 13.5002 13.6574 13.4381 14.0256 13.4381C14.4013 13.4381 14.7263 13.5009 15.0006 13.6267C15.2748 13.7525 15.4862 13.9297 15.6347 14.1585C15.7847 14.3858 15.8604 14.6517 15.8619 14.9562H14.6528ZM16.4062 18.1562V13.5017H18.4153C18.7608 13.5017 19.0631 13.5646 19.3222 13.6903C19.5812 13.8161 19.7828 13.9972 19.9267 14.2335C20.0706 14.4699 20.1426 14.7532 20.1426 15.0835C20.1426 15.4169 20.0684 15.6979 19.9199 15.9267C19.7729 16.1555 19.5661 16.3282 19.2994 16.4449C19.0343 16.5616 18.7244 16.6199 18.3699 16.6199H17.1699V15.6381H18.1153C18.2638 15.6381 18.3903 15.6199 18.4949 15.5835C18.6009 15.5456 18.682 15.4858 18.7381 15.404C18.7956 15.3222 18.8244 15.2153 18.8244 15.0835C18.8244 14.9502 18.7956 14.8419 18.7381 14.7585C18.682 14.6737 18.6009 14.6116 18.4949 14.5722C18.3903 14.5312 18.2638 14.5108 18.1153 14.5108H17.6699V18.1562H16.4062ZM19.1335 16.0199L20.2972 18.1562H18.9244L17.7881 16.0199H19.1335ZM22.4722 18.1562H20.6813V13.5017H22.454C22.9328 13.5017 23.3464 13.5949 23.6949 13.7812C24.0449 13.9661 24.3146 14.2328 24.504 14.5812C24.6949 14.9282 24.7903 15.3441 24.7903 15.829C24.7903 16.3138 24.6956 16.7305 24.5063 17.079C24.3169 17.4259 24.0487 17.6926 23.7017 17.879C23.3547 18.0638 22.9449 18.1562 22.4722 18.1562ZM21.9449 17.0835H22.4267C22.657 17.0835 22.8532 17.0464 23.0153 16.9722C23.179 16.8979 23.3032 16.7699 23.3881 16.5881C23.4744 16.4062 23.5176 16.1532 23.5176 15.829C23.5176 15.5047 23.4737 15.2517 23.3858 15.0699C23.2994 14.8881 23.1722 14.76 23.004 14.6858C22.8373 14.6116 22.6328 14.5744 22.3903 14.5744H21.9449V17.0835Z"
        fill={"white"}
      />
      <path
        d="M27.3964 23.7109L26.4321 24.2179L26.6164 23.1444L25.8359 22.3836L26.9142 22.227L27.3964 21.25L27.8785 22.227L28.9571 22.3836L28.1769 23.1444L28.361 24.2182L27.3964 23.7109Z"
        fill={"white"}
      />
      <path
        d="M8.8349 5.16758L8.05469 4.40702L9.13295 4.25044L9.61541 3.27344L10.0976 4.25044L11.1758 4.40702L10.3953 5.16758L10.5797 6.24162L9.61541 5.73468L8.6508 6.24162L8.8349 5.16758Z"
        fill={"white"}
      />
      <path
        d="M4.26038 10.0351L3.48047 9.27451L4.55874 9.11793L5.04089 8.14062L5.52304 9.11793L6.60131 9.27421L5.8211 10.0351L6.00519 11.1088L5.04089 10.6019L4.07658 11.1088L4.26038 10.0351Z"
        fill={"white"}
      />
      <path
        d="M29.2714 17.1566L28.3067 17.6635L28.4911 16.5895L27.7109 15.8289L28.7892 15.6723L29.2714 14.6953L29.7535 15.6723L30.8318 15.8289L30.0516 16.5895L30.2357 17.6635L29.2714 17.1566Z"
        fill={"white"}
      />
      <path
        d="M22.8185 28.3756L21.8539 28.8825L22.0383 27.8085L21.2578 27.048L22.3361 26.8911L22.8185 25.9141L23.3007 26.8911L24.379 27.048L23.5987 27.8085L23.7828 28.8825L22.8185 28.3756Z"
        fill={"white"}
      />
      <path
        d="M16.217 3.95343L15.2524 4.46067L15.4365 3.38664L14.6562 2.62577L15.7345 2.46949L16.217 1.49219L16.6991 2.46949L17.7774 2.62577L16.9972 3.38664L17.1813 4.46067L16.217 3.95343Z"
        fill={"white"}
      />
      <path
        d="M22.8185 5.73468L21.8539 6.24162L22.0383 5.16758L21.2578 4.40702L22.3361 4.25044L22.8185 3.27344L23.3007 4.25044L24.379 4.40702L23.5987 5.16758L23.7828 6.24162L22.8185 5.73468Z"
        fill={"white"}
      />
      <path
        d="M27.3964 10.6019L26.4321 11.1088L26.6164 10.0351L25.8359 9.27421L26.9142 9.11793L27.3964 8.14062L27.8785 9.11793L28.9571 9.27451L28.1769 10.0351L28.361 11.1088L27.3964 10.6019Z"
        fill={"white"}
      />
      <path
        d="M16.217 30.2109L15.2524 30.7185L15.4365 29.6445L14.6562 28.8839L15.7345 28.7273L16.217 27.75L16.6991 28.7273L17.7774 28.8839L16.9972 29.6445L17.1813 30.7185L16.217 30.2109Z"
        fill={"white"}
      />
      <path
        d="M8.8349 27.8085L8.05469 27.048L9.13295 26.8911L9.61541 25.9141L10.0976 26.8911L11.1758 27.048L10.3953 27.8085L10.5797 28.8825L9.61541 28.3756L8.6508 28.8825L8.8349 27.8085Z"
        fill={"white"}
      />
      <path
        d="M4.26038 23.1444L3.48047 22.3836L4.55874 22.227L5.04089 21.25L5.52304 22.227L6.60131 22.3836L5.8211 23.1444L6.00519 24.2179L5.04089 23.7109L4.07658 24.2182L4.26038 23.1444Z"
        fill={"white"}
      />
      <path
        d="M2.38177 16.5895L1.60156 15.8289L2.67983 15.6723L3.16228 14.6953L3.64444 15.6723L4.7227 15.8289L3.94219 16.5895L4.12659 17.6635L3.16228 17.1566L2.19768 17.6635L2.38177 16.5895Z"
        fill={"white"}
      />
    </svg>
  );
};
export const SDGAlignmentIcon = ({ color }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle
        cx="16"
        cy="16.8906"
        r="11.5"
        stroke={color || "#6F6F6F"}
        strokeWidth="9"
        strokeDasharray="8 3"
      />
    </svg>
  );
};
export const IntegratedRiskIcon = ({ color }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M28.7452 15.2081L26.9452 13.1015C26.6119 12.7015 26.3319 11.9548 26.3319 11.4215V9.15479C26.3319 7.74146 25.1719 6.58146 23.7585 6.58146H21.4919C20.9585 6.58146 20.1985 6.30146 19.7985 5.96812L17.6919 4.16813C16.7719 3.38146 15.2652 3.38146 14.3452 4.16813L12.2119 5.96812C11.8119 6.30146 11.0652 6.58146 10.5319 6.58146H8.22521C6.81187 6.58146 5.65187 7.74146 5.65187 9.15479V11.4215C5.65187 11.9415 5.38521 12.6881 5.05187 13.0881L3.25187 15.2081C2.47854 16.1415 2.47854 17.6348 3.25187 18.5415L5.05187 20.6615C5.38521 21.0481 5.65187 21.8081 5.65187 22.3281V24.6081C5.65187 26.0215 6.81187 27.1815 8.22521 27.1815H10.5452C11.0652 27.1815 11.8252 27.4615 12.2252 27.7948L14.3319 29.5948C15.2519 30.3815 16.7585 30.3815 17.6785 29.5948L19.7852 27.7948C20.1852 27.4615 20.9319 27.1815 21.4652 27.1815H23.7319C25.1452 27.1815 26.3052 26.0215 26.3052 24.6081V22.3415C26.3052 21.8081 26.5852 21.0615 26.9185 20.6615L28.7185 18.5548C29.5319 17.6481 29.5319 16.1415 28.7452 15.2081ZM14.9985 11.7281C14.9985 11.1815 15.4519 10.7281 15.9985 10.7281C16.5452 10.7281 16.9985 11.1815 16.9985 11.7281V18.1681C16.9985 18.7148 16.5452 19.1681 15.9985 19.1681C15.4519 19.1681 14.9985 18.7148 14.9985 18.1681V11.7281ZM15.9985 23.3815C15.2652 23.3815 14.6652 22.7815 14.6652 22.0481C14.6652 21.3148 15.2519 20.7148 15.9985 20.7148C16.7319 20.7148 17.3319 21.3148 17.3319 22.0481C17.3319 22.7815 16.7452 23.3815 15.9985 23.3815Z"
        fill={color || "#6F6F6F"}
      />
    </svg>
  );
};
export const AntigreenwashingIcon = ({ color }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.19594 15.8907H10.0219C10.5401 15.8907 10.9724 16.2864 11.0181 16.8025C11.2498 19.4211 13.4062 21.4468 15.9994 21.4468C18.5927 21.4468 20.749 19.4211 20.9808 16.8025C21.0264 16.2864 21.4587 15.8907 21.9769 15.8907L26.8473 15.8906C27.0933 15.8905 27.37 15.8904 27.6052 15.923C27.8863 15.9619 28.2517 16.0636 28.5561 16.3785C28.8558 16.6886 28.9479 17.0504 28.98 17.3318C29.0067 17.5662 28.9989 17.8841 28.993 18.1269C28.9787 18.7221 28.9442 19.3348 28.8591 19.9563C28.8463 20.0504 28.8231 20.2214 28.7885 20.3513C28.746 20.5107 28.6441 20.7992 28.3586 21.0226C28.0432 21.2693 27.698 21.2767 27.5614 21.2756C27.4249 21.2745 27.2425 21.253 27.1276 21.2394C26.1566 21.1259 25.1838 21.9408 25.1838 23.0025C25.1838 23.541 25.415 24.0191 25.7781 24.3443C25.8633 24.4204 25.9986 24.5414 26.089 24.6419C26.181 24.7441 26.3977 25.0009 26.4273 25.3906C26.4544 25.7477 26.3138 26.0152 26.2258 26.1544C26.1544 26.2675 26.0453 26.3999 25.9854 26.4727C25.3969 27.1893 24.7351 27.8418 24.0114 28.4182C23.9487 28.4683 23.8289 28.564 23.7252 28.6282C23.5941 28.7093 23.3548 28.8317 23.0347 28.8225C22.6861 28.8125 22.4396 28.6474 22.3341 28.571C22.2351 28.4993 22.115 28.3918 22.0429 28.3272C21.7372 28.0538 21.3421 27.8918 20.9101 27.8918C20.0072 27.8918 19.2255 28.6669 19.1844 29.6082C19.1802 29.705 19.1734 29.8645 19.156 29.9842C19.1376 30.1112 19.0856 30.3974 18.8577 30.6525C18.6479 30.8874 18.3996 30.978 18.2509 31.0186C18.134 31.0506 17.9838 31.0728 17.9057 31.0843C16.6598 31.2705 15.339 31.2705 14.0931 31.0843C14.015 31.0728 13.8648 31.0506 13.7479 31.0186C13.5992 30.978 13.3509 30.8874 13.1411 30.6525C12.9132 30.3974 12.8613 30.1112 12.8428 29.9842C12.8254 29.8646 12.8186 29.705 12.8145 29.6082C12.7733 28.6669 11.9916 27.8918 11.0887 27.8918C10.6567 27.8918 10.2616 28.0538 9.9559 28.3272C9.88379 28.3918 9.76375 28.4993 9.66477 28.571C9.55923 28.6474 9.31277 28.8125 8.96414 28.8225C8.644 28.8317 8.40469 28.7093 8.27358 28.6282C8.16987 28.564 8.05009 28.4683 7.98742 28.4182C7.26369 27.8418 6.60186 27.1893 6.01343 26.4727C5.95348 26.3999 5.84444 26.2675 5.77301 26.1544C5.68502 26.0151 5.54444 25.7477 5.57153 25.3906C5.60109 25.0009 5.81785 24.7441 5.90981 24.6419C6.00025 24.5414 6.13556 24.4204 6.2207 24.3443C6.58377 24.0191 6.81496 23.541 6.81496 23.0025C6.81496 21.9408 5.84221 21.1259 4.87119 21.2394C4.75628 21.253 4.57389 21.2745 4.43745 21.2756C4.30084 21.2767 3.95565 21.2693 3.64023 21.0226C3.3547 20.7992 3.25285 20.5107 3.21031 20.3513C3.17566 20.2214 3.15249 20.0504 3.13975 19.9563C3.05463 19.3348 3.02019 18.7221 3.00586 18.1269C2.9999 17.8841 2.99209 17.5662 3.0188 17.3318C3.05087 17.0504 3.14305 16.6886 3.44276 16.3785C3.74712 16.0636 4.1125 15.9619 4.39359 15.923C4.62882 15.8904 4.94994 15.8905 5.19594 15.8907Z"
        fill={color || "#6F6F6F"}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.0087 2.9576C10.3984 2.10495 13.1585 2.59975 15.0564 4.49767C16.0501 5.49142 16.6592 6.72155 16.8916 8.01111C18.51 6.55692 20.7592 6.20206 22.7155 6.90009C22.9981 7.00092 23.2204 7.2233 23.3213 7.50588C24.052 9.55378 23.6289 11.9227 21.9988 13.5528C20.6409 14.9107 18.7705 15.431 17 15.1447V16.888C17 17.4403 16.5523 17.888 16 17.888C15.4477 17.888 15 17.4403 15 16.888V12.4238C12.8675 12.8436 10.5832 12.2513 8.94298 10.6111C7.04506 8.71317 6.55026 5.95305 7.40291 3.56339C7.50374 3.28081 7.72612 3.05843 8.0087 2.9576Z"
        fill={color || "#6F6F6F"}
      />
    </svg>
  );
};
export const ESGDueDiligenceIcon = ({ color }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.0331 22.4565C14.3467 22.7701 14.3467 23.2786 14.0331 23.5922C13.7195 23.9058 13.211 23.9058 12.8973 23.5922L12.708 23.4029L6.17741 29.9335C5.28878 30.8222 3.84808 30.8222 2.95945 29.9335C2.07081 29.0449 2.07081 27.6042 2.95945 26.7156L9.49005 20.1849L9.3008 19.9957C8.98719 19.6821 8.98719 19.1736 9.3008 18.86C9.61441 18.5464 10.1229 18.5464 10.4365 18.86L14.0331 22.4565ZM29.7055 12.0216C29.7055 16.8927 25.7426 20.8556 20.8714 20.8556C18.7243 20.8556 16.7541 20.0855 15.2214 18.8072L13.9384 20.0903L12.8027 18.9546L14.0857 17.6715C12.8075 16.139 12.0373 14.1686 12.0373 12.0216C12.0373 7.15042 16.0002 3.1875 20.8714 3.1875C25.7426 3.1875 29.7055 7.15042 29.7055 12.0216ZM25.9577 9.34459C25.9577 8.90109 25.5981 8.54146 25.1546 8.54146H24.0837C22.7716 8.54146 21.6045 9.17422 20.8713 10.1504C20.1381 9.17422 18.971 8.54146 17.6589 8.54146H16.5881C16.1446 8.54146 15.7849 8.901 15.7849 9.34459V10.4154C15.7849 12.6295 17.5863 14.4309 19.8004 14.4309H20.0681V16.3048C20.0681 16.7483 20.4276 17.1079 20.8712 17.1079C21.3147 17.1079 21.6743 16.7484 21.6743 16.3048V14.4309H21.942C24.1562 14.4309 25.9575 12.6295 25.9575 10.4154V9.34459H25.9577ZM21.6744 12.557V12.8247H21.9421C23.2706 12.8247 24.3514 11.7439 24.3514 10.4154V10.1477H24.0837C22.7552 10.1477 21.6744 11.2285 21.6744 12.557ZM17.659 10.1477H17.3913V10.4154C17.3913 11.7439 18.4721 12.8247 19.8006 12.8247H20.0683V12.557C20.0683 11.2285 18.9875 10.1477 17.659 10.1477Z"
        fill={color || "#6F6F6F"}
      />
    </svg>
  );
};
export const HumanRightsIcon = ({ color }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_40003344_38124)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M16.0535 10.8591C15.714 10.8591 15.3862 10.9096 15.077 11.003L15.3673 6.39781C15.5656 6.46512 15.778 6.50162 15.999 6.50162C16.2199 6.50162 16.4323 6.46512 16.6306 6.39781L16.9189 10.9715C16.6427 10.8983 16.3527 10.8591 16.0535 10.8591ZM16.4998 2.63862V2.24306C16.4998 1.96756 16.2745 1.74219 15.999 1.74219C15.7235 1.74219 15.498 1.96763 15.498 2.24306V2.63862C15.658 2.59656 15.8258 2.57406 15.9989 2.57406C16.172 2.57406 16.3399 2.59656 16.4998 2.63862H16.4998ZM28.6893 11.7414C28.6893 13.7044 27.098 15.2957 25.135 15.2957C23.1721 15.2957 21.5808 13.7044 21.5808 11.7414H21.7275L24.7571 5.4445C24.5714 5.36669 24.415 5.23306 24.3075 5.06519C21.6398 5.56587 20.081 4.28619 17.9428 4.81712C17.9558 4.72587 17.9627 4.63262 17.9627 4.53781C17.9627 4.07725 17.804 3.65381 17.5386 3.31887C19.956 2.73806 22.0017 4.46631 24.2162 4.19306C24.3564 3.82125 24.7152 3.55512 25.135 3.55512C25.6763 3.55512 26.1177 3.99656 26.1177 4.53781C26.1177 4.94525 25.8675 5.29594 25.513 5.4445L28.5426 11.7414H28.6893ZM27.7137 11.7414L25.135 6.38187L22.5564 11.7414H27.7137H27.7137ZM6.86284 15.2957C4.89991 15.2957 3.30859 13.7044 3.30859 11.7415H3.45534L6.48497 5.44456C6.13041 5.296 5.88022 4.94531 5.88022 4.53787C5.88022 3.99662 6.32166 3.55519 6.86291 3.55519C7.28278 3.55519 7.64153 3.82131 7.78178 4.19312C9.99622 4.46637 12.0418 2.73812 14.4593 3.31894C14.1938 3.65381 14.0353 4.07731 14.0353 4.53787C14.0353 4.63269 14.0422 4.72594 14.0552 4.81719C11.917 4.28625 10.3582 5.56594 7.69047 5.06525C7.58297 5.23312 7.42659 5.36675 7.24091 5.44456L10.2705 11.7415H10.4173C10.4173 13.7044 8.82597 15.2957 6.86303 15.2957H6.86284ZM9.44147 11.7415L6.86284 6.38194L4.28422 11.7415H9.44147ZM15.9989 5.62662C16.5986 5.62662 17.0877 5.1375 17.0877 4.53787C17.0877 3.93825 16.5986 3.44912 15.9989 3.44912C15.3992 3.44912 14.9102 3.93825 14.9102 4.53787C14.9102 5.1375 15.3993 5.62662 15.9989 5.62662ZM15.9989 28.0319C13.12 28.0319 10.7862 25.6981 10.7862 22.8192H6.78022V24.3541L8.63403 24.7197L9.44809 26.6822L8.39503 28.2526L10.5657 30.4232L12.136 29.3702L14.0985 30.1843L14.4641 32.0381H17.5338L17.8994 30.1843L19.8619 29.3702L21.4322 30.4232L23.6029 28.2526L22.5498 26.6822L23.3639 24.7197L25.2177 24.3541V22.8192H21.2117C21.2117 25.6981 18.8778 28.0319 15.9989 28.0319ZM10.8347 21.5069H21.2722C21.0316 18.8359 18.7872 16.7426 16.0535 16.7426C13.3198 16.7426 11.0753 18.8358 10.8347 21.5069ZM16.0535 11.7341C14.6788 11.7341 13.5577 12.8552 13.5577 14.2299C13.5577 15.6046 14.6788 16.7257 16.0535 16.7257C17.4281 16.7257 18.5493 15.6046 18.5493 14.2299C18.5493 12.8553 17.4282 11.7341 16.0535 11.7341Z"
          fill={color || "#6F6F6F"}
        />
      </g>
      <defs>
        <clipPath id="clip0_40003344_38124">
          <rect
            width="32"
            height="20"
            fill="white"
            transform="translate(0 0.890625)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
export const DecisionSightIcon = ({ color }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M24.0108 11.2109C24.3091 11.368 24.4566 11.4265 24.5833 11.5142C26.913 13.1236 29.2415 14.7352 31.5656 16.3521C32.1524 16.7605 32.1448 16.8558 31.5503 17.2695C29.2393 18.8735 26.9272 20.4753 24.6139 22.0771C24.473 22.1746 24.3211 22.2569 24.0796 22.4031C24.0501 22.1085 24.0162 21.9222 24.0152 21.7349C24.0086 21.1035 23.9944 20.471 24.0184 19.8396C24.0348 19.4237 23.9015 19.2785 23.4612 19.285C20.7043 19.3284 18.5102 21.6038 18.4916 24.5021C18.4752 27.0829 18.4763 29.6638 18.4971 32.2458C18.5014 32.7321 18.3747 32.9054 17.8611 32.8924C16.5871 32.8588 15.3119 32.8729 14.0378 32.8859C13.686 32.8891 13.5057 32.8155 13.5089 32.4126C13.5308 29.4884 13.4565 26.562 13.5636 23.6421C13.7318 19.0782 17.4951 15.0742 22.0581 14.4687C22.5444 14.4038 23.0361 14.3539 23.5245 14.3583C23.9234 14.3626 24.0228 14.2012 24.0162 13.8438C23.9999 13.0175 24.0108 12.1933 24.0108 11.2109Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M15.172 16.9392C14.0072 18.2843 13.1921 19.7778 12.6993 21.5302C12.5594 21.3753 12.4567 21.267 12.3594 21.1533C11.352 19.9749 10.0746 19.3511 8.51427 19.2742C8.10233 19.2536 7.96684 19.4009 7.98213 19.7984C8.00617 20.4287 7.99087 21.0612 7.9865 21.6926C7.9854 21.8833 7.96356 22.0728 7.94389 22.3793C7.68711 22.2352 7.51665 22.1551 7.36476 22.05C5.05154 20.4515 2.7405 18.8507 0.430553 17.2467C-0.138739 16.8514 -0.143111 16.7475 0.415254 16.3597C2.73831 14.7438 5.06465 13.1312 7.39208 11.5218C7.53194 11.4254 7.69584 11.3615 7.98868 11.2109C7.98868 11.6886 7.98868 12.0395 7.98868 12.3893C7.98868 12.8582 8.01382 13.3294 7.98213 13.7962C7.95263 14.2337 8.13402 14.3409 8.55142 14.3442C10.8723 14.3637 12.9342 15.1207 14.7437 16.5568C14.8803 16.6673 15.007 16.7919 15.172 16.9392Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M10.4141 8.71711C10.7353 8.25682 10.9888 7.89183 11.2445 7.52901C12.7131 5.44415 14.1827 3.35929 15.6535 1.2766C16.0163 0.763233 16.1605 0.761066 16.5244 1.27551C18.1645 3.59323 19.8003 5.91419 21.4349 8.23732C21.5234 8.36295 21.5803 8.51133 21.695 8.73227C21.4601 8.76368 21.3104 8.79942 21.1607 8.80159C20.469 8.80809 19.7751 8.83516 19.0857 8.79509C18.5863 8.76585 18.4705 8.95646 18.4781 9.41676C18.5021 10.9135 18.477 12.4114 18.4945 13.9092C18.4989 14.2558 18.3929 14.4551 18.0858 14.6414C17.4674 15.0172 16.8751 15.4385 16.2982 15.8771C16.0611 16.0569 15.9256 16.0472 15.695 15.8728C15.1017 15.4266 14.4942 14.9966 13.8637 14.6056C13.5894 14.4356 13.5064 14.2525 13.5086 13.9525C13.5195 12.419 13.502 10.8854 13.5206 9.35177C13.526 8.93155 13.3895 8.77668 12.9578 8.79725C12.3219 8.82866 11.6838 8.81025 11.0467 8.80267C10.8774 8.79942 10.708 8.75827 10.4141 8.71711Z"
        fill={color || "#6F6F6F"}
      />
    </svg>
  );
};
export const DocVaultIcon = ({ color }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M28.079 16.6475L27.8656 16.3542C27.4923 15.9008 27.0523 15.5408 26.5456 15.2742C25.8656 14.8875 25.0923 14.6875 24.2923 14.6875H7.69229C6.89229 14.6875 6.13229 14.8875 5.43896 15.2742C4.91896 15.5542 4.45229 15.9408 4.06563 16.4208C3.30563 17.3942 2.94563 18.5942 3.06563 19.7942L3.55896 26.0208C3.73229 27.9008 3.95896 30.2208 8.18563 30.2208H23.8123C28.039 30.2208 28.2523 27.9008 28.439 26.0075L28.9323 19.8075C29.0523 18.6875 28.759 17.5675 28.079 16.6475ZM19.1856 24.0075H12.799C12.279 24.0075 11.8656 23.5808 11.8656 23.0742C11.8656 22.5675 12.279 22.1408 12.799 22.1408H19.1856C19.7056 22.1408 20.119 22.5675 20.119 23.0742C20.119 23.5942 19.7056 24.0075 19.1856 24.0075Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M27.4407 12.6359C27.4696 13.0195 27.0561 13.2754 26.6972 13.137C25.9442 12.8466 25.1442 12.7014 24.3078 12.7014H7.69448C6.85492 12.7014 6.02999 12.855 5.2739 13.1461C4.9191 13.2826 4.50781 13.0364 4.50781 12.6562V9.76802C4.50781 5.00802 5.96115 3.55469 10.7211 3.55469H12.2945C14.2011 3.55469 14.8011 4.16802 15.5745 5.16802L17.1745 7.30135C17.5078 7.75469 17.5211 7.78135 18.1078 7.78135H21.2811C25.5617 7.78135 27.1636 8.95903 27.4407 12.6359Z"
        fill={color || "#6F6F6F"}
      />
    </svg>
  );
};
export const ConnectorsIcon = ({ color = "#6F6F6F" }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16 27.444C14.0682 27.444 12.2248 27.1616 10.8092 26.6486C10.3228 26.4724 9.91169 26.2769 9.5625 26.0703V28.1448C9.5625 29.4331 12.4447 30.4774 16 30.4774C19.5553 30.4774 22.4375 29.4331 22.4375 28.1448V26.0703C22.0884 26.277 21.6772 26.4724 21.1908 26.6487C19.7753 27.1616 17.9318 27.444 16 27.444Z"
        fill={color}
      />
      <path
        d="M16 22.197C14.0729 22.197 12.2307 21.9141 10.8129 21.4003C10.3249 21.2235 9.9125 21.0275 9.5625 20.8203V23.2417C9.5625 24.53 12.4447 25.5744 16 25.5744C19.5553 25.5744 22.4375 24.53 22.4375 23.2417V20.8204C22.0875 21.0276 21.6751 21.2236 21.1872 21.4004C19.7693 21.9141 17.9272 22.197 16 22.197Z"
        fill={color}
      />
      <path
        d="M16 20.3215C19.5553 20.3215 22.4375 19.2771 22.4375 17.9889C22.4375 16.7006 19.5553 15.6562 16 15.6562C12.4447 15.6562 9.5625 16.7006 9.5625 17.9889C9.5625 19.2771 12.4447 20.3215 16 20.3215Z"
        fill={color}
      />
      <path
        d="M24.375 7.55463C24.1107 7.55463 23.8459 7.56838 23.5828 7.59569C22.8707 6.41138 21.8893 5.40663 20.7138 4.66469C19.3041 3.77494 17.6741 3.30469 16 3.30469C11.3941 3.30469 7.59925 6.84413 7.19238 11.3459C6.74206 11.2361 6.27825 11.1797 5.8125 11.1797C2.6075 11.1797 0 13.7871 0 16.9922C0 20.1973 2.6075 22.8047 5.8125 22.8047H7.6875V17.9855C7.6875 17.1346 8.09356 15.5599 10.8129 14.5746C12.2307 14.0608 14.0729 13.7779 16 13.7779C17.9271 13.7779 19.7693 14.0608 21.1871 14.5746C23.9065 15.5599 24.3125 17.1346 24.3125 17.9855V22.8047H24.375C28.5794 22.8047 32 19.3841 32 15.1797C32 10.9752 28.5794 7.55463 24.375 7.55463Z"
        fill={color}
      />
    </svg>
  );
};

export const GreenHubIcon = ({ color = "#6F6F6F" }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M30.0768 14.1403C30.4926 14.178 30.9606 14.2995 31.3651 14.6496C31.7747 15.004 31.9245 15.4251 31.9746 15.8029C32.0146 16.1043 31.9955 16.5093 31.9808 16.8182C31.8377 19.884 30.9997 23.6833 28.7483 26.2473C27.5968 27.5587 26.0544 28.5679 24.0548 28.9435C22.4311 29.2485 20.5995 29.1182 18.5525 28.501C20.9729 25.794 23.4514 24.0733 25.1171 23.0769C25.871 22.6259 26.0747 21.712 25.5721 21.0356C25.0695 20.3592 24.0509 20.1764 23.2971 20.6273C21.45 21.7322 18.7437 23.6128 16.0976 26.5426C15.2815 24.6134 15.0797 22.8854 15.4271 21.3541C15.8314 19.5723 16.9494 18.1896 18.4039 17.1507C21.2493 15.1183 25.4913 14.3149 28.9395 14.1487C29.2898 14.1316 29.7406 14.1097 30.0768 14.1403ZM12.9352 30.6871C13.9357 29.1161 15.0095 27.74 16.0933 26.5399C16.2207 26.8411 16.3631 27.1472 16.5207 27.4581C16.6467 27.7068 16.8709 27.9047 17.1501 28.0137C17.6269 28.1997 18.0929 28.3611 18.5483 28.4984C17.6005 29.5584 16.6617 30.7696 15.784 32.1478C15.3344 32.8536 14.3323 33.0989 13.5456 32.6955C12.759 32.2922 12.4857 31.393 12.9352 30.6871Z"
        fill={color} // استخدام اللون القادم من الـ props
      />
      <path
        d="M18.8967 2.46535C16.32 0.365718 12.3896 0.365718 9.81283 2.46534C7.83398 4.07778 5.4054 6.30338 3.46332 8.8594C1.53208 11.4011 0 14.3767 0 17.4658C0 22.4088 3.72073 27.7701 10.09 29.403C10.415 29.4863 10.5775 29.5279 10.7326 29.4732C10.8877 29.4185 10.9811 29.2796 11.168 29.0018C11.7736 28.1013 12.4089 27.2646 13.0462 26.485C13.1882 26.3114 13.2591 26.2246 13.2802 26.1276C13.3013 26.0305 13.2709 25.923 13.2102 25.7079C12.7449 24.06 12.6596 22.458 13.0066 20.9285C13.5476 18.5441 15.0469 16.7345 16.8622 15.438C19.4252 13.6074 22.7764 12.6707 25.8216 12.2395C26.353 12.1642 26.6187 12.1266 26.7386 11.9818C26.7716 11.9419 26.7952 11.9006 26.8121 11.8533C26.8735 11.6816 26.7559 11.4714 26.5207 11.0512C24.5018 7.58597 21.4332 4.5321 18.8967 2.46535Z"
        fill={color} // استخدام اللون القادم من الـ props
      />
    </svg>
  );
};

export const MeasureWindIcon = ({ color }) => {
  return (
    <svg
      width="24"
      height="25"
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15 22.9844L15 6.48438C15 4.55438 16.57 2.98437 18.5 2.98437C20.43 2.98437 22 4.55437 22 6.48437C22 8.41437 20.43 9.98437 18.5 9.98437L18 9.98437"
        stroke={color || "#07838F"}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12 22.9844L12 6.48437C12 4.56437 10.43 2.98437 8.5 2.98437C6.58 2.98437 5 4.56438 5 6.48438C5 8.40437 6.57 9.98437 8.5 9.98437L9 9.98437"
        stroke={color || "#07838F"}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.99719 22.9844L8.99719 15.6744C8.99719 14.1844 7.78719 12.9844 6.30719 12.9844C4.81719 12.9844 3.61719 14.1944 3.61719 15.6744C3.61719 17.1644 4.82719 18.3644 6.30719 18.3644L6.68719 18.3644"
        stroke={color || "#07838F"}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
export const MeasurDocumentcloudIcon = ({ color }) => {
  return (
    <svg
      width="25"
      height="25"
      viewBox="0 0 25 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.9766 2.98438H9.97656C4.97656 2.98438 2.97656 4.98438 2.97656 9.98438V15.9844C2.97656 20.9844 4.97656 22.9844 9.97656 22.9844"
        stroke={color || "#07838F"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M22.9766 10.9844V13.9844"
        stroke={color || "#07838F"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M22.9766 10.9844H18.9766C15.9766 10.9844 14.9766 9.98438 14.9766 6.98438V2.98438L22.9766 10.9844Z"
        stroke={color || "#07838F"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14.7391 19.2462C12.3891 19.4162 12.3891 22.8162 14.7391 22.9862H20.2991C20.9691 22.9862 21.6291 22.7361 22.1191 22.2861C23.7691 20.8461 22.8891 17.9661 20.7191 17.6961C19.9391 13.0061 13.1591 14.7861 14.7591 19.2561"
        stroke={color || "#07838F"}
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
export const BreakdownAssetIcon = ({ color }) => {
  return (
    <svg
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1.83594 20.1641H20.1693"
        stroke={color || "#07838F"}
        strokeWidth="1.37631"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.5833 1.83594H6.41667C3.66667 1.83594 2.75 3.47677 2.75 5.5026V20.1693H19.25V5.5026C19.25 3.47677 18.3333 1.83594 15.5833 1.83594Z"
        stroke={color || "#07838F"}
        strokeWidth="1.37631"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.41406 15.125H9.16406"
        stroke={color || "#07838F"}
        strokeWidth="1.37631"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.8281 15.125H15.5781"
        stroke={color || "#07838F"}
        strokeWidth="1.37631"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.41406 11H9.16406"
        stroke={color || "#07838F"}
        strokeWidth="1.37631"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.8281 11H15.5781"
        stroke={color || "#07838F"}
        strokeWidth="1.37631"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.41406 6.875H9.16406"
        stroke={color || "#07838F"}
        strokeWidth="1.37631"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.8281 6.875H15.5781"
        stroke={color || "#07838F"}
        strokeWidth="1.37631"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
export const BreakdownScopesIcon = ({ color }) => {
  return (
    <svg
      width="23"
      height="22"
      viewBox="0 0 23 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20.1979 7.81261V3.65094C20.1979 2.35844 19.6113 1.83594 18.1538 1.83594H14.4504C12.9929 1.83594 12.4062 2.35844 12.4062 3.65094V7.80344C12.4062 9.1051 12.9929 9.61844 14.4504 9.61844H18.1538C19.6113 9.6276 20.1979 9.10511 20.1979 7.81261Z"
        stroke={color || "#8F8F8F"}
        strokeWidth="1.37631"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M20.1979 18.1225V14.4192C20.1979 12.9617 19.6113 12.375 18.1538 12.375H14.4504C12.9929 12.375 12.4062 12.9617 12.4062 14.4192V18.1225C12.4062 19.58 12.9929 20.1667 14.4504 20.1667H18.1538C19.6113 20.1667 20.1979 19.58 20.1979 18.1225Z"
        stroke={color || "#8F8F8F"}
        strokeWidth="1.37631"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.65885 7.81261V3.65094C9.65885 2.35844 9.07219 1.83594 7.61469 1.83594H3.91135C2.45385 1.83594 1.86719 2.35844 1.86719 3.65094V7.80344C1.86719 9.1051 2.45385 9.61844 3.91135 9.61844H7.61469C9.07219 9.6276 9.65885 9.10511 9.65885 7.81261Z"
        stroke={color || "#8F8F8F"}
        strokeWidth="1.37631"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.65885 18.1225V14.4192C9.65885 12.9617 9.07219 12.375 7.61469 12.375H3.91135C2.45385 12.375 1.86719 12.9617 1.86719 14.4192V18.1225C1.86719 19.58 2.45385 20.1667 3.91135 20.1667H7.61469C9.07219 20.1667 9.65885 19.58 9.65885 18.1225Z"
        stroke={color || "#8F8F8F"}
        strokeWidth="1.37631"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
export const BreakdownCountriesIcon = ({ color }) => {
  return (
    <svg
      width="23"
      height="22"
      viewBox="0 0 23 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.0651 20.1693C16.1277 20.1693 20.2318 16.0652 20.2318 11.0026C20.2318 5.93999 16.1277 1.83594 11.0651 1.83594C6.00249 1.83594 1.89844 5.93999 1.89844 11.0026C1.89844 16.0652 6.00249 20.1693 11.0651 20.1693Z"
        stroke={color || "#8F8F8F"}
        strokeWidth="1.37631"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.40052 2.75H8.31719C6.52969 8.10333 6.52969 13.8967 8.31719 19.25H7.40052"
        stroke={color || "#8F8F8F"}
        strokeWidth="1.37631"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.8125 2.75C15.6 8.10333 15.6 13.8967 13.8125 19.25"
        stroke={color || "#8F8F8F"}
        strokeWidth="1.37631"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.8125 14.6667V13.75C8.16583 15.5375 13.9592 15.5375 19.3125 13.75V14.6667"
        stroke={color || "#8F8F8F"}
        strokeWidth="1.37631"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.8125 8.25469C8.16583 6.46719 13.9592 6.46719 19.3125 8.25469"
        stroke={color || "#8F8F8F"}
        strokeWidth="1.37631"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const BreakdownSitesIcon = ({ color }) => {
  return (
    <svg
      width="23"
      height="22"
      viewBox="0 0 23 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.0944 12.3059C12.6739 12.3059 13.9544 11.0255 13.9544 9.44594C13.9544 7.8664 12.6739 6.58594 11.0944 6.58594C9.51484 6.58594 8.23438 7.8664 8.23438 9.44594C8.23438 11.0255 9.51484 12.3059 11.0944 12.3059Z"
        stroke={color || "#8F8F8F"}
        strokeWidth="1.37631"
      />
      <path
        d="M3.41003 7.78511C5.21586 -0.153227 16.9767 -0.14406 18.7734 7.79427C19.8275 12.4509 16.9309 16.3926 14.3917 18.8309C12.5492 20.6093 9.63419 20.6093 7.78253 18.8309C5.25253 16.3926 2.35586 12.4418 3.41003 7.78511Z"
        stroke={color || "#8F8F8F"}
        strokeWidth="1.37631"
      />
    </svg>
  );
};
export const BreakdownTier2Icon = ({ color = "#8F8F8F" }) => {
  return (
    <svg
      width="23"
      height="22"
      viewBox="0 0 23 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.9828 9.57387L19.0844 13.4055C18.3144 16.7147 16.7928 18.053 13.9328 17.778C13.4744 17.7414 12.9794 17.6589 12.4478 17.5305L10.9078 17.1639C7.08527 16.2564 5.90277 14.368 6.8011 10.5364L7.69944 6.69554C7.88277 5.91637 8.10277 5.23804 8.37777 4.67887C9.45027 2.46054 11.2744 1.86471 14.3361 2.58887L15.8669 2.94637C19.7078 3.84471 20.8811 5.74221 19.9828 9.57387Z"
        stroke={color}
        strokeWidth="1.37631"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.9298 17.771C13.3615 18.156 12.6465 18.4769 11.7757 18.761L10.3273 19.2377C6.68816 20.411 4.77232 19.4302 3.58982 15.791L2.41649 12.1702C1.24316 8.53104 2.21482 6.60604 5.85399 5.43271L7.30232 4.95604C7.67816 4.83687 8.03566 4.73604 8.37482 4.67188C8.09982 5.23104 7.87982 5.90938 7.69649 6.68854L6.79816 10.5294C5.89982 14.361 7.08232 16.2494 10.9048 17.1569L12.4448 17.5235C12.9765 17.6519 13.4715 17.7344 13.9298 17.771Z"
        stroke={color}
        strokeWidth="1.37631"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
export const DashboardsDecisionSightIcon = ({ color }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M18.2269 5.79688H15.0879C14.6165 5.79688 14.3808 5.79688 14.2344 5.94332C14.0879 6.08977 14.0879 6.32547 14.0879 6.79688L14.0879 26.0447C14.0879 26.5161 14.0879 26.7518 14.2344 26.8983C14.3808 27.0447 14.6165 27.0447 15.0879 27.0447H18.227H18.227H18.227H18.227H18.227C19.9688 27.0447 21.3487 27.0447 22.4484 26.9143C23.5798 26.7802 24.5093 26.4993 25.3059 25.8718C25.8519 25.4418 26.3221 24.9112 26.6982 24.307C27.2376 23.4404 27.4778 22.4373 27.5939 21.1974C27.7083 19.9762 27.7083 18.4373 27.7083 16.4638V16.3778C27.7083 14.4043 27.7083 12.8654 27.5939 11.6442C27.4778 10.4042 27.2376 9.40116 26.6982 8.53453C26.3221 7.93034 25.8519 7.39982 25.3059 6.96974C24.5093 6.34224 23.5798 6.06142 22.4484 5.92726C21.3487 5.79685 19.9687 5.79686 18.2269 5.79688H18.2269H18.2269ZM11.4476 27.0295C10.7325 27.0135 10.1006 26.9809 9.54119 26.9145C8.40983 26.7804 7.48027 26.4996 6.68368 25.8721C6.1377 25.442 5.66748 24.9115 5.29142 24.3073C4.75203 23.4406 4.51184 22.4376 4.39566 21.1976C4.28124 19.9764 4.28124 18.4375 4.28125 16.464V16.3781C4.28124 14.4045 4.28124 12.8656 4.39566 11.6444C4.51184 10.4045 4.75203 9.40139 5.29142 8.53476C5.66748 7.93057 6.1377 7.40005 6.68368 6.96997C7.48027 6.34247 8.40983 6.06165 9.54119 5.92749C10.1006 5.86116 10.7325 5.82857 11.4476 5.81256C11.9176 5.80204 12.1526 5.79677 12.3031 5.94387C12.4535 6.09097 12.4535 6.32911 12.4535 6.80541V26.0366C12.4535 26.5129 12.4535 26.7511 12.3031 26.8982C12.1526 27.0453 11.9176 27.04 11.4476 27.0295ZM9.72941 10.9301C9.72941 10.4787 9.36353 10.1128 8.91219 10.1128H7.82256C7.37122 10.1128 7.00533 10.4787 7.00533 10.9301C7.00533 11.3814 7.37122 11.7473 7.82256 11.7473H8.91219C9.36353 11.7473 9.72941 11.3814 9.72941 10.9301ZM9.72941 14.199C9.72941 13.7476 9.36353 13.3817 8.91219 13.3817H7.82256C7.37122 13.3817 7.00533 13.7476 7.00533 14.199C7.00533 14.6503 7.37122 15.0162 7.82256 15.0162H8.91219C9.36353 15.0162 9.72941 14.6503 9.72941 14.199Z"
        fill={color || "#6F6F6F"}
      />
    </svg>
  );
};
export const DataQualityDecisionSightIcon = ({ color }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16 20.4193C20.9706 20.4193 25 16.5391 25 11.7526C25 6.96614 20.9706 3.08594 16 3.08594C11.0294 3.08594 7 6.96614 7 11.7526C7 16.5391 11.0294 20.4193 16 20.4193Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M21.0533 21.2366C21.4933 21.0099 22 21.3433 22 21.8366V28.3033C22 29.5033 21.16 30.0899 20.12 29.5966L16.5467 27.9033C16.24 27.7699 15.76 27.7699 15.4533 27.9033L11.88 29.5966C10.84 30.0766 10 29.4899 10 28.2899L10.0267 21.8366C10.0267 21.3433 10.5467 21.0233 10.9733 21.2366C12.48 21.9966 14.1867 22.4233 16 22.4233C17.8133 22.4233 19.5333 21.9966 21.0533 21.2366Z"
        fill={color || "#6F6F6F"}
      />
    </svg>
  );
};
export const RecommendationsDecisionSightIcon = ({ color }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20.5193 7.36761L22.3993 11.1276C22.6526 11.6476 23.3326 12.1409 23.9059 12.2476L27.3059 12.8076C29.4793 13.1676 29.9859 14.7409 28.4259 16.3143L25.7726 18.9676C25.3326 19.4076 25.0793 20.2743 25.2259 20.9009L25.9859 24.1809C26.5859 26.7676 25.1993 27.7809 22.9193 26.4209L19.7326 24.5276C19.1593 24.1809 18.1993 24.1809 17.6259 24.5276L14.4393 26.4209C12.1593 27.7676 10.7726 26.7676 11.3726 24.1809L12.1326 20.9009C12.2526 20.2609 11.9993 19.3943 11.5593 18.9543L8.90594 16.3009C7.34594 14.7409 7.85261 13.1676 10.0259 12.7943L13.4259 12.2343C13.9993 12.1409 14.6793 11.6343 14.9326 11.1143L16.8126 7.35427C17.8393 5.32761 19.4926 5.32761 20.5193 7.36761Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M10.6641 8.08594H2.66406C2.1174 8.08594 1.66406 7.6326 1.66406 7.08594C1.66406 6.53927 2.1174 6.08594 2.66406 6.08594H10.6641C11.2107 6.08594 11.6641 6.53927 11.6641 7.08594C11.6641 7.6326 11.2107 8.08594 10.6641 8.08594Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M6.66406 26.7578H2.66406C2.1174 26.7578 1.66406 26.3045 1.66406 25.7578C1.66406 25.2111 2.1174 24.7578 2.66406 24.7578H6.66406C7.21073 24.7578 7.66406 25.2111 7.66406 25.7578C7.66406 26.3045 7.21073 26.7578 6.66406 26.7578Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M3.9974 17.4219H2.66406C2.1174 17.4219 1.66406 16.9685 1.66406 16.4219C1.66406 15.8752 2.1174 15.4219 2.66406 15.4219H3.9974C4.54406 15.4219 4.9974 15.8752 4.9974 16.4219C4.9974 16.9685 4.54406 17.4219 3.9974 17.4219Z"
        fill={color || "#6F6F6F"}
      />
    </svg>
  );
};
export const RecommendationsDecisionSighStartIcon = ({ color }) => {
  return (
    <svg
      width="17"
      height="17"
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.08411 7.2875L8.28411 7.62927L8.63664 7.80962L10.2907 8.6558L8.63707 9.50104L8.28424 9.68138L8.08411 10.0234L7.05821 11.7764L6.03117 10.0229L5.83105 9.68126L5.47847 9.50104L3.82485 8.6558L5.47889 7.80962L5.83118 7.62939L6.03117 7.28794L7.05821 5.53443L8.08411 7.2875Z"
        fill="white"
        stroke="white"
        strokeWidth="2.71097"
      />
      <path
        d="M13.5465 3.59024L13.6921 3.34116L13.838 3.59049L13.8714 3.60752L13.8383 3.62442L13.6921 3.87397L13.5463 3.62467L13.5127 3.60752L13.5465 3.59024Z"
        fill="white"
        stroke="white"
        strokeWidth="2.71097"
      />
      <path
        d="M14.8078 12.6637L16.8489 13.7073L14.8078 14.7509L13.6911 16.6586L12.5763 14.7509L10.5352 13.7073L12.5763 12.6637L13.6911 10.7578L14.8078 12.6637Z"
        fill="white"
      />
    </svg>
  );
};
export const AIRecommendations = ({ color }) => {
  return (
    <svg
      width="24"
      height="26"
      viewBox="0 0 24 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.4088 9.76105L18.3337 13.0044L12.4088 16.245L9.16828 22.1701L5.92498 16.245L0 13.0044L5.92498 9.76105L9.16828 3.83594L12.4088 9.76105Z"
        fill="url(#paint0_linear_40003946_47963)"
      />
      <path
        d="M20.6077 3.38029L23.6207 5.02865L20.6077 6.67701L18.9593 9.6873L17.3138 6.67701L14.3008 5.02865L17.3138 3.38029L18.9593 0.367188L20.6077 3.38029Z"
        fill="url(#paint1_linear_40003946_47963)"
      />
      <path
        d="M20.6077 19.3267L23.6207 20.9751L20.6077 22.6234L18.9593 25.6365L17.3138 22.6234L14.3008 20.9751L17.3138 19.3267L18.9593 16.3164L20.6077 19.3267Z"
        fill="url(#paint2_linear_40003946_47963)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40003946_47963"
          x1="-0.344619"
          y1="24.8732"
          x2="22.0653"
          y2="22.9193"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_40003946_47963"
          x1="14.1256"
          y1="11.0614"
          x2="25.5176"
          y2="10.0682"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_40003946_47963"
          x1="14.1256"
          y1="27.0106"
          x2="25.5176"
          y2="26.0174"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};
export const AIRecommendationsIcon = ({ color }) => {
  return (
    <svg
      width="45"
      height="49"
      viewBox="0 0 45 49"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M23.6372 18.2119L34.89 24.3728L23.6372 30.5283L17.4828 41.7832L11.3231 30.5283L0.0703125 24.3728L11.3231 18.2119L17.4828 6.95703L23.6372 18.2119Z"
        fill="#A8B7B8"
      />
      <path
        d="M39.2086 6.09065L44.931 9.22175L39.2086 12.3529L36.0781 18.071L32.9529 12.3529L27.2305 9.22175L32.9529 6.09065L36.0781 0.367188L39.2086 6.09065Z"
        fill="#A8B7B8"
      />
      <path
        d="M39.2086 36.3822L44.931 39.5133L39.2086 42.6444L36.0781 48.3679L32.9529 42.6444L27.2305 39.5133L32.9529 36.3822L36.0781 30.6641L39.2086 36.3822Z"
        fill="#A8B7B8"
      />
    </svg>
  );
};
export const LoadingSpinnerIcon = ({ color }) => {
  return (
    <svg
      width="91"
      height="91"
      viewBox="0 0 91 91"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle
        cx="82.7978"
        cy="45.1494"
        r="7.50299"
        transform="rotate(180 82.7978 45.1494)"
        fill="#A8B7B8"
      />
      <circle
        cx="7.77045"
        cy="45.1494"
        r="7.50299"
        transform="rotate(180 7.77045 45.1494)"
        fill="#A8B7B8"
      />
      <circle
        cx="45.2822"
        cy="82.665"
        r="7.50299"
        transform="rotate(180 45.2822 82.665)"
        fill="#A8B7B8"
      />
      <circle
        cx="45.2822"
        cy="7.63763"
        r="7.50299"
        transform="rotate(180 45.2822 7.63763)"
        fill="url(#paint0_linear_40003952_49565)"
      />
      <circle
        cx="18.7578"
        cy="71.6821"
        r="7.50299"
        transform="rotate(-135 18.7578 71.6821)"
        fill="#A8B7B8"
      />
      <circle
        cx="71.8086"
        cy="18.6235"
        r="7.50299"
        transform="rotate(-135 71.8086 18.6235)"
        fill="#A8B7B8"
      />
      <circle
        cx="18.7515"
        cy="18.625"
        r="7.50299"
        transform="rotate(-45 18.7515 18.625)"
        fill="#A8B7B8"
      />
      <circle
        cx="71.8101"
        cy="71.6758"
        r="7.50299"
        transform="rotate(-45 71.8101 71.6758)"
        fill="#A8B7B8"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40003952_49565"
          x1="37.4971"
          y1="17.353"
          x2="55.8394"
          y2="15.7538"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#2C5A8C" />
          <stop offset="0.46" stop-color="#1C889C" />
          <stop offset="1" stop-color="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};
export const RightIcon = ({ color }) => {
  return (
    <svg
      width="65"
      height="65"
      viewBox="0 0 65 65"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.5"
        y="0.367188"
        width="64"
        height="64"
        rx="32"
        fill="#D1FAE5"
      />
      <path
        d="M25.5 33.3672L29.5 37.3672L39.5 27.3672"
        stroke="#059669"
        strokeWidth="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
export const DataValidationIcon = ({ color }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 44 45"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M31.3486 4.05078H23.6486C17.6801 4.05078 15.0582 6.28732 14.72 11.7463C14.6855 12.3022 15.1386 12.7591 15.6955 12.7591H20.3486C28.0486 12.7591 31.6236 16.3341 31.6236 24.0341V28.6872C31.6236 29.2441 32.0805 29.6972 32.6364 29.6627C38.0954 29.3245 40.3319 26.7027 40.3319 20.7341V13.0341C40.3319 6.61745 37.7653 4.05078 31.3486 4.05078Z"
        fill={color || "url(#paint0_linear_40005864_62872)"}
      />
      <path
        d="M20.3513 15.0508H12.6513C6.23464 15.0508 3.66797 17.6174 3.66797 24.0341V31.7341C3.66797 38.1508 6.23464 40.7174 12.6513 40.7174H20.3513C26.768 40.7174 29.3346 38.1508 29.3346 31.7341V24.0341C29.3346 17.6174 26.768 15.0508 20.3513 15.0508ZM22.533 25.4091L15.7313 32.2108C15.4746 32.4674 15.1446 32.5958 14.7963 32.5958C14.448 32.5958 14.118 32.4674 13.8613 32.2108L10.4513 28.8008C9.93797 28.2874 9.93797 27.4624 10.4513 26.9491C10.9646 26.4358 11.7896 26.4358 12.303 26.9491L14.778 29.4241L20.663 23.5391C21.1763 23.0258 22.0013 23.0258 22.5146 23.5391C23.028 24.0524 23.0463 24.8958 22.533 25.4091Z"
        fill={color || "url(#paint1_linear_40005864_62872)"}
      />
      <defs>
        <linearGradient
          id="paint0_linear_40005864_62872"
          x1="14.2015"
          y1="33.4806"
          x2="45.5523"
          y2="30.7471"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={color || "#2C5A8C"} />
          <stop offset="0.46" stopColor={color || "#1C889C"} />
          <stop offset="1" stopColor={color || "#13B1A8"} />
        </linearGradient>
        <linearGradient
          id="paint1_linear_40005864_62872"
          x1="3.18551"
          y1="44.5016"
          x2="34.5587"
          y2="41.7662"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={color || "#2C5A8C"} />
          <stop offset="0.46" stopColor={color || "#1C889C"} />
          <stop offset="1" stopColor={color || "#13B1A8"} />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const TNFDIcon = ({ color }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 32 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.6413 9.15285H6.63542C6.12516 9.15285 5.7115 8.7392 5.7115 8.22893V6.89657C5.7115 6.38631 5.29785 5.97266 4.78758 5.97266H1.41797"
        stroke={color || "#6F6F6F"}
        strokeWidth="0.739135"
      />
      <path
        d="M10.6413 15.8159H6.63542C6.12516 15.8159 5.7115 16.2295 5.7115 16.7398V18.0722C5.7115 18.5824 5.29785 18.9961 4.78758 18.9961H1.41797"
        stroke={color || "#6F6F6F"}
        strokeWidth="0.739135"
      />
      <ellipse
        cx="1.44833"
        cy="6.03035"
        rx="1.44833"
        ry="1.44832"
        fill={color || "#6F6F6F"}
      />
      <ellipse
        cx="1.44833"
        cy="1.44832"
        rx="1.44833"
        ry="1.44832"
        transform="matrix(1 0 0 -1 0 20.3828)"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M10.6588 12.5195H0.761719"
        stroke={color || "#6F6F6F"}
        strokeWidth="0.739135"
      />
      <ellipse
        cx="1.44833"
        cy="12.585"
        rx="1.44833"
        ry="1.44832"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M28.4984 10.575H25.6049C23.2321 10.575 21.2997 8.64261 21.2997 6.26973V3.37622C21.2997 2.82555 20.8492 2.375 20.2985 2.375H16.0533C12.9696 2.375 10.4766 4.37743 10.4766 7.95177V16.8225C10.4766 20.3969 12.9696 22.3993 16.0533 22.3993H23.9229C27.0066 22.3993 29.4997 20.3969 29.4997 16.8225V11.5762C29.4997 11.0255 29.0491 10.575 28.4984 10.575Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M23.7932 2.58181C23.3827 2.17131 22.6719 2.45165 22.6719 3.02235V6.51659C22.6719 7.97836 23.9134 9.18984 25.4252 9.18984C26.3764 9.19985 27.698 9.19985 28.8294 9.19985C29.4 9.19985 29.7004 8.52903 29.2999 8.12855C27.8582 6.67678 25.275 4.06361 23.7932 2.58181Z"
        fill={color || "#6F6F6F"}
      />
      <path
        d="M13.1658 10.3789H11.9423L13.8065 4.97869H15.2779L17.1395 10.3789H15.916L14.5633 6.21272H14.5211L13.1658 10.3789ZM13.0893 8.25626H15.9793V9.14751H13.0893V8.25626ZM18.9312 4.97869V10.3789H17.7895V4.97869H18.9312Z"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M26.8211 15.6604C26.9215 15.6705 27.0344 15.7032 27.1321 15.7974C27.2309 15.8928 27.267 16.0062 27.2791 16.1079C27.2888 16.189 27.2842 16.298 27.2806 16.3811C27.2461 17.2063 27.0439 18.2288 26.5005 18.9189C26.2226 19.2719 25.8504 19.5435 25.3678 19.6446C24.976 19.7267 24.534 19.6916 24.0399 19.5255C24.6241 18.7969 25.2222 18.3338 25.6242 18.0656C25.8061 17.9443 25.8553 17.6983 25.734 17.5162C25.6127 17.3342 25.3669 17.285 25.185 17.4063C24.7393 17.7036 24.0863 18.2097 23.4478 18.998C23.4784 19.0785 23.5126 19.1603 23.5503 19.2434C23.5807 19.3104 23.6349 19.3636 23.7022 19.3929C23.8173 19.443 23.9298 19.4865 24.0397 19.5234C23.811 19.8087 23.5844 20.1347 23.3726 20.5056C23.2641 20.6956 23.0222 20.7616 22.8324 20.6531C22.6425 20.5445 22.5766 20.3025 22.685 20.1125C22.9264 19.6899 23.1854 19.3196 23.4468 18.9968C23.2504 18.4782 23.2019 18.0136 23.2857 17.6019C23.3832 17.1224 23.653 16.7502 24.0041 16.4706C24.6908 15.9236 25.7145 15.7074 26.5467 15.6626C26.6312 15.658 26.74 15.6521 26.8211 15.6604Z"
        fill="white"
      />
      <path
        d="M24.1229 12.5137C23.5011 11.9486 22.5525 11.9486 21.9307 12.5137C21.4531 12.9477 20.867 13.5467 20.3983 14.2346C19.9322 14.9187 19.5625 15.7196 19.5625 16.551C19.5625 17.8814 20.4604 19.3244 21.9976 19.7639C22.076 19.7863 22.1152 19.7975 22.1526 19.7828C22.1901 19.768 22.2126 19.7306 22.2577 19.6559C22.4039 19.4135 22.5572 19.1883 22.711 18.9785C22.7452 18.9318 22.7624 18.9084 22.7675 18.8823C22.7725 18.8562 22.7652 18.8272 22.7506 18.7693C22.6383 18.3258 22.6177 17.8946 22.7014 17.483C22.832 16.8412 23.1938 16.3542 23.6319 16.0052C24.2505 15.5125 25.0592 15.2604 25.7941 15.1444C25.9224 15.1241 25.9865 15.114 26.0154 15.075C26.0234 15.0643 26.0291 15.0531 26.0332 15.0404C26.048 14.9942 26.0196 14.9376 25.9628 14.8245C25.4756 13.8919 24.735 13.0699 24.1229 12.5137Z"
        fill="white"
      />
    </svg>
  );
};

export const DataSyncIcon = ({ color }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 29 29"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_40006030_66574)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M14.4587 0.976563H14.3252C11.7687 0.976548 9.76067 0.976536 8.19292 1.18726C6.58638 1.40319 5.31174 1.85473 4.31023 2.85596C3.30872 3.85719 2.85706 5.13149 2.64107 6.73759C2.48794 7.87625 2.44606 9.24722 2.43462 10.892C2.4308 11.4409 2.42889 11.7153 2.60003 11.8876C2.77118 12.0599 3.04703 12.0599 3.59873 12.0599H25.1851C25.7368 12.0599 26.0127 12.0599 26.1838 11.8876C26.355 11.7153 26.3531 11.4409 26.3492 10.892C26.3378 9.24722 26.2959 7.87625 26.1428 6.73759C25.9268 5.13149 25.4751 3.85719 24.4736 2.85596C23.4721 1.85473 22.1975 1.40319 20.5909 1.18726C19.0232 0.976536 17.0152 0.976548 14.4587 0.976563ZM7.97299 6.51833C7.97299 5.55183 8.75649 4.76833 9.72299 4.76833C10.6895 4.76833 11.473 5.55183 11.473 6.51833C11.473 7.48483 10.6895 8.26833 9.72299 8.26833C8.75649 8.26833 7.97299 7.48483 7.97299 6.51833ZM14.3897 6.51833C14.3897 6.03508 14.7814 5.64333 15.2647 5.64333L19.9313 5.64333C20.4146 5.64333 20.8063 6.03508 20.8063 6.51833C20.8063 7.00158 20.4146 7.39333 19.9313 7.39333L15.2647 7.39333C14.7814 7.39333 14.3897 7.00158 14.3897 6.51833Z"
          fill={color || "#5A5A5A"}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M25.1851 13.8086H3.59873C3.04703 13.8086 2.77118 13.8086 2.60003 13.9809C2.42889 14.1532 2.4308 14.4276 2.43462 14.9765C2.44606 16.6213 2.48794 17.9922 2.64107 19.1309C2.85706 20.737 3.30872 22.0113 4.31023 23.0125C5.31174 24.0138 6.58638 24.4653 8.19292 24.6812C9.88208 24.9083 11.6014 24.9012 13.3083 24.8942C13.6756 24.8927 14.0422 24.8912 14.4079 24.892C14.6368 24.8925 14.7512 24.8928 14.819 24.8287C14.8867 24.7646 14.8935 24.6342 14.9073 24.3734C14.9221 24.0925 14.9773 23.8141 15.0719 23.5475C15.1367 23.3651 15.1691 23.2739 15.1536 23.2074C15.1382 23.1409 15.0713 23.0757 14.9377 22.9453L14.9376 22.9453C14.5867 22.603 14.2846 22.1948 13.9886 21.7948C13.849 21.6061 13.7107 21.4192 13.5692 21.2418C12.5645 19.9825 12.7712 18.1474 14.0309 17.143C14.6695 16.6337 15.4562 16.4358 16.2076 16.5285C16.3966 16.5518 16.4912 16.5635 16.5561 16.5465C16.621 16.5295 16.6897 16.4789 16.827 16.3778C18.521 15.1302 20.7524 14.6445 22.9241 15.2099C23.9389 15.4741 24.8515 15.9446 25.6217 16.5673C25.9151 16.8045 26.3223 16.6599 26.331 16.2826C26.3406 15.8677 26.3461 15.4326 26.3492 14.9765C26.3531 14.4276 26.355 14.1532 26.1838 13.9809C26.0127 13.8086 25.7368 13.8086 25.1851 13.8086ZM9.72298 17.6003C8.75648 17.6003 7.97298 18.3838 7.97298 19.3503C7.97298 20.3168 8.75648 21.1003 9.72298 21.1003C10.6895 21.1003 11.473 20.3168 11.473 19.3503C11.473 18.3838 10.6895 17.6003 9.72298 17.6003Z"
          fill={color || "#5A5A5A"}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M16.8334 18.787C18.1192 17.1424 20.3345 16.3452 22.48 16.904C23.6211 17.2012 24.5844 17.8396 25.2779 18.6825C25.6873 19.18 25.6159 19.9153 25.1183 20.3246C24.6207 20.734 23.8855 20.6626 23.4761 20.165C23.0899 19.6956 22.5486 19.333 21.8919 19.162C20.2428 18.7325 18.5905 19.6922 18.1667 21.2297C18.0501 21.6527 17.706 21.9745 17.2762 22.0625C16.8464 22.1506 16.4035 21.9901 16.1299 21.6471L14.9368 20.1512C14.5351 19.6475 14.6177 18.9134 15.1215 18.5117C15.6252 18.1099 16.3592 18.1925 16.761 18.6963L16.8334 18.787ZM24.9212 21.89C24.4915 21.9782 24.1476 22.2999 24.0311 22.7227C23.6072 24.2602 21.955 25.2199 20.3059 24.7904C19.6484 24.6192 19.1067 24.256 18.7204 23.7859C18.3114 23.2881 17.5762 23.2162 17.0784 23.6253C16.5806 24.0343 16.5086 24.7695 16.9177 25.2673C17.6114 26.1115 18.5755 26.7509 19.7178 27.0484C21.8637 27.6073 24.0795 26.8097 25.3652 25.1644L25.4373 25.2547C25.8394 25.7582 26.5735 25.8403 27.077 25.4382C27.5805 25.0361 27.6626 24.302 27.2605 23.7986L26.0674 22.3047C25.7936 21.962 25.3508 21.8018 24.9212 21.89Z"
          fill={color || "#5A5A5A"}
        />
      </g>
      <defs>
        <clipPath id="clip0_40006030_66574">
          <rect
            width="28"
            height="28"
            fill="white"
            transform="translate(0.972656 0.101562)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const AnomomlisIcon = ({ color }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 29 29"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.3477 6.82031H23.3477"
        stroke={color || "#5A5A5A"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.3477 10.3203H19.8477"
        stroke={color || "#5A5A5A"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M24.5143 14.4036C24.5143 20.5286 19.556 25.487 13.431 25.487C7.30599 25.487 2.34766 20.5286 2.34766 14.4036C2.34766 8.27865 7.30599 3.32031 13.431 3.32031"
        stroke={color || "#5A5A5A"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M25.681 26.6536L23.3477 24.3203"
        stroke={color || "#5A5A5A"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const DataQualityIcon = ({ color }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 29 29"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M25.291 22.538L23.366 22.993C22.9343 23.098 22.596 23.4247 22.5026 23.8564L22.0943 25.5714C21.8726 26.5047 20.6826 26.7964 20.0643 26.0614L16.576 22.048C16.296 21.7214 16.4476 21.208 16.8676 21.103C18.9326 20.6014 20.7876 19.4464 22.1526 17.8014C22.3743 17.533 22.771 17.498 23.016 17.743L25.606 20.333C26.4926 21.2197 26.1776 22.328 25.291 22.538Z"
        fill={color || "#07838F"}
      />
      <path
        d="M3.6497 22.538L5.5747 22.993C6.00637 23.098 6.3447 23.4247 6.43804 23.8564L6.84637 25.5714C7.06804 26.5047 8.25803 26.7964 8.87637 26.0614L12.3647 22.048C12.6447 21.7214 12.493 21.208 12.073 21.103C10.008 20.6014 8.15303 19.4464 6.78803 17.8014C6.56637 17.533 6.1697 17.498 5.9247 17.743L3.3347 20.333C2.44804 21.2197 2.76303 22.328 3.6497 22.538Z"
        fill={color || "#07838F"}
      />
      <path
        d="M14.5007 3.32031C9.98565 3.32031 6.33398 6.97198 6.33398 11.487C6.33398 13.1786 6.83565 14.7303 7.69898 16.0253C8.95898 17.892 10.954 19.2103 13.2757 19.5486C13.6723 19.6186 14.0807 19.6536 14.5007 19.6536C14.9207 19.6536 15.329 19.6186 15.7257 19.5486C18.0473 19.2103 20.0423 17.892 21.3023 16.0253C22.1657 14.7303 22.6673 13.1786 22.6673 11.487C22.6673 6.97198 19.0157 3.32031 14.5007 3.32031ZM18.0707 11.2303L17.1023 12.1986C16.939 12.362 16.8457 12.677 16.904 12.9103L17.184 14.112C17.4057 15.057 16.904 15.4303 16.064 14.9286L14.8973 14.2403C14.6873 14.112 14.3373 14.112 14.1273 14.2403L12.9607 14.9286C12.1207 15.4186 11.619 15.057 11.8407 14.112L12.1207 12.9103C12.1673 12.6886 12.0857 12.362 11.9223 12.1986L10.9307 11.2303C10.359 10.6586 10.5457 10.087 11.339 9.95865L12.5873 9.74865C12.7973 9.71365 13.0423 9.52698 13.1357 9.34031L13.824 7.96365C14.1973 7.21698 14.804 7.21698 15.1773 7.96365L15.8657 9.34031C15.959 9.52698 16.204 9.71365 16.4257 9.74865L17.674 9.95865C18.4557 10.087 18.6423 10.6586 18.0707 11.2303Z"
        fill={color || "#07838F"}
      />
    </svg>
  );
};

export const AssessmentGuidIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17 19.0938H13L8.54999 22.0538C7.88999 22.4938 7 22.0238 7 21.2238V19.0938C4 19.0938 2 17.0938 2 14.0938V8.09375C2 5.09375 4 3.09375 7 3.09375H17C20 3.09375 22 5.09375 22 8.09375V14.0938C22 17.0938 20 19.0938 17 19.0938Z"
        stroke="#07838F"
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.9998 12.0234V11.8135C11.9998 11.1335 12.4198 10.7735 12.8398 10.4835C13.2498 10.2035 13.6598 9.84347 13.6598 9.18347C13.6598 8.26347 12.9198 7.52344 11.9998 7.52344C11.0798 7.52344 10.3398 8.26347 10.3398 9.18347"
        stroke="#07838F"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.9955 14.4141H12.0045"
        stroke="#07838F"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const GoogleSheetIcon = ({ color }) => (
  <svg
    width="25"
    height="25"
    viewBox="0 0 25 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M15.5 2.52344V4.02344C15.5 5.43765 15.5 6.14476 15.9393 6.5841C16.3787 7.02344 17.0858 7.02344 18.5 7.02344H20"
      stroke={color || "#07838F"}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.5 16.0234V8.02344C4.5 5.19501 4.5 3.7808 5.37868 2.90212C6.25736 2.02344 7.67157 2.02344 10.5 2.02344H14.6716C15.0803 2.02344 15.2847 2.02344 15.4685 2.09956C15.6522 2.17568 15.7968 2.32019 16.0858 2.60922L19.9142 6.43765C20.2032 6.72668 20.3478 6.8712 20.4239 7.05497C20.5 7.23874 20.5 7.44311 20.5 7.85186V16.0234C20.5 18.8519 20.5 20.2661 19.6213 21.1448C18.7426 22.0234 17.3284 22.0234 14.5 22.0234H10.5C7.67157 22.0234 6.25736 22.0234 5.37868 21.1448C4.5 20.2661 4.5 18.8519 4.5 16.0234Z"
      stroke={color || "#07838F"}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.5 11.0234V14.0234M12.5 14.0234V17.0234M12.5 14.0234H8M12.5 14.0234H17M10 17.0234H15C15.9428 17.0234 16.4142 17.0234 16.7071 16.7305C17 16.4377 17 15.9662 17 15.0234V13.0234C17 12.0806 17 11.6092 16.7071 11.3163C16.4142 11.0234 15.9428 11.0234 15 11.0234H10C9.05719 11.0234 8.58579 11.0234 8.29289 11.3163C8 11.6092 8 12.0806 8 13.0234V15.0234C8 15.9662 8 16.4377 8.29289 16.7305C8.58579 17.0234 9.05719 17.0234 10 17.0234Z"
      stroke={color || "#07838F"}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const DocumentDownload = ({ color }) => (
  <svg
    width="25"
    height="25"
    viewBox="0 0 25 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.5 11.0234V17.0234L11.5 15.0234"
      stroke={color || "#07838F"}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.5 17.0234L7.5 15.0234"
      stroke={color || "#07838F"}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M22.5 10.0234V15.0234C22.5 20.0234 20.5 22.0234 15.5 22.0234H9.5C4.5 22.0234 2.5 20.0234 2.5 15.0234V9.02344C2.5 4.02344 4.5 2.02344 9.5 2.02344H14.5"
      stroke={color || "#07838F"}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M22.5 10.0234H18.5C15.5 10.0234 14.5 9.02344 14.5 6.02344V2.02344L22.5 10.0234Z"
      stroke={color || "#07838F"}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
