import ApiS2 from "@/Api/apiS2Config";
import Loading from "@/Components/Loading";
import { Button, Select, TextInput, Tooltip } from "@mantine/core";
import { DateInput } from "@mantine/dates";
import { isNotEmpty, useForm } from "@mantine/form";
import { useDisclosure } from "@mantine/hooks";
import { showNotification } from "@mantine/notifications";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { FaCalendarDay } from "react-icons/fa";
import { FaRegCircleCheck } from "react-icons/fa6";
import { IoIosArrowDown, IoIosCheckmarkCircle } from "react-icons/io";
import ManualInputPopUp from "./ManualInputPopUp";
export default function UpdateManualForm({
 allItemSpecificities,
 loading,
 AssetsError,
 onClose,
 data,
 getTableData,
 setSelectedItems,
 selectedItemsId,
 closePopup,
 emissionsAll,
 emissions,
 associatedFactors,
}) {
 const [QuantityOpened, { open: QuantityOpen, close: QuantityClose }] =
  useDisclosure(false);
 const [submitLoading, setSubmitLoading] = useState(false);
 const [selectedActivity, setSelectedActivity] = useState(null);
 const [selectedEFactors, setSelectedEFactors] = useState(null);
 const [selectedUom, setSelectedUom] = useState(null);
 const [selectedReporting, setSelectedReporting] = useState(null);

 const [inputValues, setInputValues] = useState({});

 const [Quantity, SetQuantity] = useState(null);
 const [startDateValue, setStartDateValue] = useState(null);
 const [endDateValue, setEndDateValue] = useState(null);

 const [companyAssetSelected, setCompanyAssetSelected] = useState(); //? string

 const [selectedEmissions, setSelectedEmissions] = useState("");
 //  //console.log("emissions", emissionsAll);
 const [dataByActivity, setDataByActivity] = useState([]);
 const [dataEFactorByActivity, setDataEFactorByActivity] = useState([]);
 const [dataUOMByActivity, setDataUOMByActivity] = useState([]);
 const [dataByEmissions, setDataByEmissions] = useState([]);
 const [dataActivityByEmissions, setDataActivityByEmissions] = useState([]);
 const [CustomFactors, setCustomFactors] = useState([]); //? list of objects

 //  //console.log(dataByActivity);
 //  //console.log(selectedUom);

 //  //console.log(dataUOMByActivity);
 const assignDataBasedOnEmissions = (emission, activity, eFactor, uom) => {
  let filteredData;
  //console.log(filteredData);

  if (emission) {
   const filtredEmissionId = emissionsAll?.find(
    (em) => em.asset === emission
   )?.id;
   const filtredCustomFactor = associatedFactors.filter(
    (em) => em?.emissionSourceId === filtredEmissionId
   );

   // استخراج الأنشطة الفريدة
   const activityValues = filtredCustomFactor.map((item) => item.activity);
   const uniqueActivities = Array.from(
    new Set(activityValues.map(JSON.stringify))
   ).map(JSON.parse);
   const formattedActivities = uniqueActivities.map((activity) => {
    const entries = Object.entries(activity).map(
     ([key, value]) => `${key}:${value}`
    );
    return entries.length > 1 ? `${entries.join(" || ")}` : `${entries[0]}`;
   });

   setDataActivityByEmissions(formattedActivities);
   setDataByEmissions(filtredCustomFactor);
  }

  if (activity) {
   const dataByActivity = associatedFactors.filter((item) => {
    return Object.entries(activity).every(
     ([key, value]) => item.activity[key] === value
    );
   });
   setDataByActivity(dataByActivity);
   filteredData = dataByActivity;
   // فلترة eFactors غير null
   const eFactorsValues = dataByActivity
    .map((item) => item.eFactors)
    .filter((eFactors) => eFactors !== null);

   // تنسيق eFactors
   const uniqueEFactors = Array.from(
    new Set(eFactorsValues.map(JSON.stringify))
   ).map(JSON.parse);
   const formattedEFactors = uniqueEFactors.map((eFactors) => {
    const entries = Object.entries(eFactors).map(
     ([key, value]) => `${key}:${value}`
    );
    return entries.length > 1 ? `${entries.join(" || ")}` : `${entries[0]}`;
   });

   setDataEFactorByActivity(formattedEFactors);
  }

  if (eFactor) {
   const dataByEfactor =
    filteredData ||
    dataByActivity.filter((item) => {
     return (
      item.eFactors &&
      Object.entries(eFactor).every(
       ([key, value]) => item.eFactors[key] === value
      )
     );
    });
   setCustomFactors(dataByEfactor);
   const UOMValues = dataByEfactor
    .map((item) => item.uom)
    .filter((uom) => uom !== null); // تجاهل null

   // تنسيق UOM
   const uniqueUOM = Array.from(new Set(UOMValues.map(JSON.stringify))).map(
    JSON.parse
   );
   const formattedUOM = uniqueUOM.map((UOM) => {
    const entries = Object.entries(UOM).map(
     ([key, value]) => `${key}:${value}`
    );
    return entries.length > 1 ? `${entries.join(" || ")}` : `${entries[0]}`;
   });
   //console.log(formattedUOM);

   setDataUOMByActivity(formattedUOM);
  }

  if (uom) {
   const dataByUOM = dataByActivity.filter((item) => {
    return (
     item.uom &&
     Object.entries(uom).every(([key, value]) => item.uom[key] === value)
    );
   });
   setCustomFactors(dataByUOM);
   const UOMValues = dataByUOM
    .map((item) => item.uom)
    .filter((uom) => uom !== null); // تجاهل null

   const uniqueUOM = Array.from(new Set(UOMValues.map(JSON.stringify))).map(
    JSON.parse
   );
   const formattedUOM = uniqueUOM.map((UOM) => {
    const entries = Object.entries(UOM).map(
     ([key, value]) => `${key}:${value}`
    );
    return entries.length > 1 ? `${entries.join(" || ")}` : `${entries[0]}`;
   });

   //console.log(dataByUOM);
  }
 };

 // From Core
 const form = useForm({
  mode: "uncontrolled",
  initialValues: {
   fromDate: "",
   toDate: "",
   assetsType: "",
   assets: "",
   activity: "",
   eFactors: "",
   uom: "",
   quantity: "",
   reportingYear: "",
   source_physical: "",
   source_service: "",
   location_specificity: "",
  },
  validate: {
   fromDate: isNotEmpty("Start Date is required"),
   toDate: isNotEmpty("End Date is required"),
   assetsType: isNotEmpty("Assets Type is required"),
   assets: isNotEmpty("Assets is required"),
   activity: isNotEmpty("Activity is required"),
   quantity: isNotEmpty("quantity is required"),
   reportingYear: isNotEmpty("Reporting Years required"),
   source_physical: isNotEmpty("Source Physical required"),
   source_service: isNotEmpty("Source Service required"),
   location_specificity: isNotEmpty("Location Specificity required"),
  },
 });

 const handledFormSubmit = async (value) => {
  //console.log(value);

  setSubmitLoading(true);

  const newJsonValues = {
   [data?.items[selectedItemsId]?.id]: {
    fromDate: value.fromDate,
    toDate: value.toDate,
    activity: value.activity || null,
    eFactor: value.eFactors || null,
    uom: value.uom || null,
    asset_type_id: dataByActivity?.[0]?.emissionSourceId,
    companyAssetId: data?.items?.[selectedItemsId]?.companyAsset?.id,
    // customFactorId: dataByActivity[0]?.id,
    quantity: value.quantity || {},
    reportingYear:
     value.reportingYear === "No Options"
      ? null
      : Number(value.reportingYear) || null,
    SourcePhysicalId:
     (value.source_physical &&
      allItemSpecificities.source_physical.find(
       (item) => item.source === value.source_physical
      )?.id) ||
     "",
    SourceServiceId:
     (value.source_service &&
      allItemSpecificities.source_service.find(
       (item) => item.source === value.source_service
      )?.id) ||
     "",
    LocationSpecificityId:
     (value.location_specificity &&
      allItemSpecificities.location_specificity.find(
       (item) => item.location === value.location_specificity
      )?.id) ||
     "",
   },
  };
  //console.log(newJsonValues);
  ApiS2.put("/batch_inputs/update_inputs", newJsonValues)
   .then(({ data }) => {
    setSubmitLoading(false);
    getTableData("manual");
    getTableData("batch");
    data?.inputType === "batch" && closePopup();
    showNotification({
     message: "Form Data Added Successfully",
     color: "green",
    });
    onClose();
    form.reset();
    restAllInputs();
    //  //console.log(data);
   })
   .catch((error) => {
    setSubmitLoading(false);
    //console.log(error);
    error.response.data.message &&
     showNotification({
      message: error.response.data.message,
      color: "red",
     });
    error.response.data.error &&
     showNotification({
      message: error.response.data.error,
      color: "red",
     });
    error.response.data.validation_errors?.[0].message &&
     showNotification({
      message: error.response.data.validation_errors?.[0].message,
      color: "red",
     });
   });
 };

 // rest All Inputs Fun
 const restAllInputs = () => {
  setSelectedEmissions(null);
  setSelectedActivity(null);
  setSelectedEFactors(null);
  setSelectedUom(null);
  setInputValues({});
  setDataByActivity([]);
  setStartDateValue(null);
  setEndDateValue(null);
 };
 const handleInputChange = (key, value, section) => {
  // //console.log("🚀 ~ handleInputChange ~ section:", section);
  setInputValues((prev) => ({
   ...prev,
   [section]: {
    ...prev[section],
    [key]: value,
   },
  }));
 };
 const handleSaveAdditionalPayload = (item) => {
  // setInputValues({})
  switch (item) {
   case "quantityKeys":
    SetQuantity(inputValues?.quantityKeys);
    QuantityClose();
    break;

   default:
    break;
  }
 };
 // form.setValues useEffect
 const getSpecificity = (items, id, key) =>
  (id && items?.find((item) => item?.id === id)?.[key]) || "";

 const formatDate = (date) => (date ? dayjs(date).format("DD/MM/YYYY") : "");

 useEffect(() => {
  const selectedItem = data?.items[selectedItemsId] || {};
  // //console.log(String(selectedItem?.reportingYear));
  assignDataBasedOnEmissions(null, null, selectedEFactors);
  form.setValues({
   assetsType: selectedEmissions,
   assets: companyAssetSelected,
   activity: selectedActivity === "No Options" ? null : selectedActivity,
   eFactors: selectedEFactors === "No Options" ? null : selectedEFactors,
   uom: selectedUom === "No Options" ? null : selectedUom || null,
   reportingYear: selectedReporting || "",
   fromDate: (startDateValue && formatDate(startDateValue)) || "",
   toDate: (endDateValue && formatDate(endDateValue)) || "",
   quantity: Quantity || null,
   source_physical: getSpecificity(
    allItemSpecificities?.source_physical,
    selectedItem.SourcePhysicalId,
    "source"
   ),
   source_service: getSpecificity(
    allItemSpecificities?.source_service,
    selectedItem?.SourceServiceId,
    "source"
   ),
   location_specificity: getSpecificity(
    allItemSpecificities?.location_specificity,
    selectedItem?.LocationSpecificityId,
    "location"
   ),
  });
 }, [
  companyAssetSelected,
  selectedActivity,
  selectedEFactors,
  selectedUom,
  Quantity,
  selectedReporting,
  startDateValue,
  endDateValue,
 ]);
 useEffect(() => {
  const selectedItem = data?.items[selectedItemsId] || {};
  const emissionsId = selectedItem.customFactor.emissionSourceId;
  const SelectedEmissions = emissionsAll?.find((em) => em.id === emissionsId);
  // //console.log(selectedItem.customFactor?.activity);
  setSelectedReporting(String(selectedItem?.reportingYear));
  setStartDateValue(new Date(selectedItem.fromDate) || null);
  setEndDateValue(new Date(selectedItem.toDate) || null);
  setCompanyAssetSelected(selectedItem.companyAsset?.assetName || null);
  setSelectedEmissions(SelectedEmissions?.asset);
  assignDataBasedOnEmissions(SelectedEmissions?.asset);
  setSelectedActivity(selectedItem.customFactor?.activity || null);
  assignDataBasedOnEmissions(null, selectedItem.customFactor?.activity);
  setSelectedEFactors(selectedItem.customFactor?.eFactors || null);
  setSelectedUom(selectedItem.customFactor?.uom || null);

  SetQuantity(selectedItem?.quantity || null);
  // assignDataBasedOnEmissions(null, );
 }, [data]);

 const { t } = useTranslation();

 return (
  <>
   {loading || !CustomFactors ? (
    <Loading />
   ) : AssetsError ? (
    <h1 className="capitalize text-center">{AssetsError}</h1>
   ) : (
    <form
     className="w-full px-10 py-4 bg-white shadow-md rounded-xl"
     onSubmit={form.onSubmit(handledFormSubmit)}
    >
     <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 sm:gap-x-5 md:gap-x-10 lg:gap-x-28 gap-y-10 text-start">
      <DateInput
       {...form.getInputProps("fromDate")}
       key={form.key("fromDate")}
       label="From"
       placeholder="DD/MM/YYYY"
       valueFormat="DD/MM/YYYY"
       rightSection={<FaCalendarDay />}
       value={startDateValue}
       onChange={setStartDateValue}
       radius={10}
       size="md"
      />
      <DateInput
       {...form.getInputProps("toDate")}
       key={form.key("toDate")}
       label="To"
       placeholder="DD/MM/YYYY"
       valueFormat="DD/MM/YYYY"
       value={endDateValue}
       onChange={setEndDateValue}
       rightSection={<FaCalendarDay />}
       radius={10}
       size="md"
      />

      <TextInput
       label="Assets"
       placeholder="Choose"
       value={companyAssetSelected}
       radius={10}
       size="md"
       readOnly
      />

      <Tooltip
       multiline
       w={220}
       radius={"md"}
       withArrow
       transitionProps={{ duration: 200 }}
       label={
        <span className="capitalize flex justify-center text-center">
         please select asset first
        </span>
       }
       hidden={
        companyAssetSelected || (companyAssetSelected && emissions.length !== 0)
       }
      >
       <Select
        {...form.getInputProps("assetsType")}
        key={form.key("assetsType")}
        disabled={emissions.length == 0 || !companyAssetSelected}
        label={`Emission Source`}
        placeholder="Choose"
        onChange={(value) => {
         setSelectedEmissions(value ? value : "");
         assignDataBasedOnEmissions(value);
         setInputValues({});
         setSelectedActivity(null);
         setSelectedEFactors(null);
         setSelectedUom(null);
         SetQuantity(null);
         setDataByActivity([]);
        }}
        value={selectedEmissions}
        data={emissions}
        radius={10}
        size="md"
        rightSection={" "}
        readOnly
       />
      </Tooltip>
      {/* open activity */}
      <Tooltip
       multiline
       w={220}
       radius={"md"}
       withArrow
       transitionProps={{ duration: 200 }}
       hidden={
        !selectedEmissions ||
        (selectedEmissions && dataByEmissions.length !== 0)
       }
       label={
        <span className="capitalize flex justify-center text-center">
         there is no Custom Factor in this Emission Source
        </span>
       }
      >
       <Select
        {...form.getInputProps("activity")}
        key={form.key("activity")}
        label="Activity"
        radius={10}
        placeholder="Choose"
        rightSection={<IoIosArrowDown />}
        onChange={(e) => {
         if (e) {
          if (e === "No Options") {
           setSelectedActivity("No Options"); // حفظ "No Options" عند اختياره
          } else {
           const activity = Object.fromEntries(
            e.split(" || ").map((pair) => pair.split(":"))
           );
           setSelectedActivity(activity);
           assignDataBasedOnEmissions(null, activity);
          }
          // //console.log(selectedActivity);
         } else {
          setSelectedActivity(null);
          SetQuantity(null);
          setDataByActivity([]);
          setInputValues({});
          setSelectedReporting(null);
         }
         setSelectedEFactors(null);
         setSelectedUom(null);
        }}
        data={
         dataActivityByEmissions?.length > 0
          ? dataActivityByEmissions
          : [{ label: "No Options", value: "No Options" }]
        }
        value={
         selectedActivity === "No Options"
          ? "No Options" // إظهار "No Options" عند اختياره
          : selectedActivity
          ? typeof selectedActivity === "object"
            ? Object.entries(selectedActivity)
               .map(([key, value]) => `${key}:${value}`)
               .join(" || ")
            : selectedActivity
          : ""
        }
        disabled={!selectedEmissions}
       />
      </Tooltip>

      <Select
       {...form.getInputProps("eFactors")}
       key={form.key("eFactors")}
       label="E-Factors"
       placeholder="Choose"
       rightSection={<IoIosArrowDown />}
       radius={10}
       onChange={(e) => {
        if (e) {
         if (e === "No Options") {
          setSelectedEFactors("No Options");
          assignDataBasedOnEmissions(null, null, "No Options");
         } else {
          const eFactors = Object.fromEntries(
           e.split(" || ").map((pair) => pair.split(":"))
          );
          setSelectedEFactors(eFactors);
          assignDataBasedOnEmissions(null, null, eFactors);
         }
         // //console.log(selectedActivity);
        } else {
         setSelectedEFactors(null);
        }
       }}
       value={
        selectedEFactors === "No Options"
         ? "No Options" // إظهار "No Options" عند اختياره
         : selectedEFactors
         ? typeof selectedEFactors === "object"
           ? Object.entries(selectedEFactors)
              .map(([key, value]) => `${key}:${value}`)
              .join(" || ")
           : selectedEFactors
         : ""
       }
       data={
        dataEFactorByActivity?.length > 0
         ? dataEFactorByActivity
         : [{ label: "No Options", value: "No Options" }]
       }
       disabled={!selectedActivity}
      />

      <Select
       {...form.getInputProps("uom")}
       key={form.key("uom")}
       rightSection={<IoIosArrowDown />}
       label="UOM"
       radius={10}
       placeholder="Choose"
       onChange={(e) => {
        if (e) {
         if (e === "No Options") {
          setSelectedUom("No Options"); // حفظ "No Options" عند اختياره
          assignDataBasedOnEmissions(null, null, null, "No Options");
         } else {
          const UOM = Object.fromEntries(
           e.split(" || ").map((pair) => pair.split(":"))
          );
          setSelectedUom(UOM);
          assignDataBasedOnEmissions(null, null, null, UOM);
         }
         // //console.log(selectedActivity);
        } else {
         setSelectedUom(null);
        }
       }}
       value={
        selectedUom === "No Options"
         ? "No Options" // إظهار "No Options" عند اختياره
         : selectedUom
         ? typeof selectedUom === "object"
           ? Object.entries(selectedUom)
              .map(([key, value]) => `${key}:${value}`)
              .join(" || ")
           : selectedUom
         : ""
       }
       data={
        dataUOMByActivity?.length > 0
         ? dataUOMByActivity
         : [{ label: "No Options", value: "No Options" }]
       }
       disabled={!selectedActivity || !selectedEFactors}
      />

      {/* open quantity */}
      <div className="flex items-start justify-end flex-col">
       <Tooltip
        multiline
        w={220}
        radius={"md"}
        withArrow
        transitionProps={{ duration: 200 }}
        className={`${
         !selectedActivity
          ? "hidden"
          : selectedActivity &&
            dataByActivity?.[0]?.quantityKeys !== null &&
            "hidden"
        }`}
        label={
         <span className="capitalize flex justify-center">
          there is no Quantity
         </span>
        }
       >
        <Button
         disabled={
          selectedActivity === null ||
          dataByActivity?.[0]?.quantityKeys === null
         }
         radius={10}
         size="md"
         className={`relative z-10 w-full bg-primary hover:bg-primary  ${
          (form.errors?.quantity ? "  bg-red-500 hover:bg-red-500" : "",
          selectedActivity === null ? "cursor-not-allowed" : "cursor-pointer")
         }`}
         onClick={QuantityOpen}
        >
         + {t("Add Quantity")}
         {Quantity != undefined && (
          <IoIosCheckmarkCircle
           className="absolute text-white top-1 right-1 z-20"
           color="white"
          />
         )}
        </Button>
       </Tooltip>
       <p className="capitalize text-red-500 text-sm">{form.errors.quantity}</p>
      </div>
      <Select
       {...form.getInputProps("reportingYear")}
       key={form.key("reportingYear")}
       rightSection={<IoIosArrowDown />}
       label="Reporting Year"
       placeholder="Enter Year..."
       radius={10}
       size="md"
       data={
        dataByActivity?.[0]?.acceptableYears
         ? dataByActivity[0]?.acceptableYears.map((num) => `${num}`)
         : ["No options"]
       }
       clearable
       disabled={
        selectedActivity === null || dataByActivity[0]?.acceptableYears === null
       }
       value={selectedReporting}
       onChange={setSelectedReporting}
      />

      <Select
       {...form.getInputProps("location_specificity")}
       key={form.key("location_specificity")}
       rightSection={<IoIosArrowDown />}
       label="Location Specificity"
       radius={10}
       size="md"
       placeholder="Choose"
       data={allItemSpecificities?.location_specificity?.map(
        (item) => item.location
       )}
       clearable
      />
      <Select
       {...form.getInputProps("source_physical")}
       key={form.key("source_physical")}
       rightSection={<IoIosArrowDown />}
       label="Source Physical"
       radius={10}
       size="md"
       placeholder="Choose"
       data={allItemSpecificities?.source_physical?.map((item) => item.source)}
      />
      <Select
       {...form.getInputProps("source_service")}
       key={form.key("source_service")}
       rightSection={<IoIosArrowDown />}
       label="Source Service"
       radius={10}
       size="md"
       placeholder="Choose"
       // onChange={setSelectedUom}
       // value={selectedUom}
       data={allItemSpecificities?.source_service?.map((item) => item.source)}
      />

      <div></div>
      <Button
       onClick={() => {
        onClose();
        // setSelectedRow(null);
        setSelectedItems(null);
       }}
       color=""
       radius={10}
       size="md"
       className="w-full bg-red-700 hover:bg-red-700"
      >
       Cancel
      </Button>
      <Button
       type={"submit"}
       disabled={submitLoading}
       color=""
       radius={10}
       size="md"
       className="w-full bg-[#00C0A9] hover:bg-[#00C0A9] text-white"
      >
       {submitLoading ? (
        <Loading />
       ) : (
        <span className="flex items-center ">
         <FaRegCircleCheck className="me-1" /> {t("Confirm")}
        </span>
       )}
      </Button>

      {QuantityOpened && (
       <ManualInputPopUp
        opened={QuantityOpened}
        close={QuantityClose}
        t={t}
        handleSaveAdditionalPayload={handleSaveAdditionalPayload}
        handleInputChange={handleInputChange}
        title={"Quantity"}
        item={"quantityKeys"}
        customFactors={dataByActivity}
        inputValues={inputValues}
        PopUpInputs={data?.items?.[selectedItemsId]?.quantity}
       />
      )}
     </div>
    </form>
   )}
  </>
 );
}
