import ApiS1Config from "@/Api/apiS1Config";
import MainLayout from "@/Layout/MainLayout";
import { Button } from "@mantine/core";
import { notifications, showNotification } from "@mantine/notifications";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { FaCheckCircle } from "react-icons/fa";
import { GoShare } from "react-icons/go";
import { useNavigate } from "react-router";
import IssbTable from "./Partials/IssbTable";
import IssbReport from "./Partials/IssbReport";
import { IoMdHome } from "react-icons/io";
import GuideModalButton from "@/Components/Guide/GuideModalButton";
import Guide from "./AssessmentGuide";

const Issb = () => {
    const [selectedScope, setSelectedScope] = useState();
    const [PostAnswersState, setPostAnswersState] = useState();
    const [postLoading, setPostLoading] = useState(false);
    const [reportLoading, setReportLoading] = useState(false);

    const { t } = useTranslation();
    const navigate = useNavigate();

    const [active, setActive] = useState("Readiness Assessment");

    const PriorityLevels = { 1: "Low", 2: "Medium", 3: "High", 4: "Top" };
    const ReadinessLevel = {
        1: "Not Started",
        2: "Initial Planning",
        3: "In Development",
        4: "Partially Implemented",
        5: "Fully Implemented",
        6: "Not Applicable",
    };

    const readinessColorMap = {
        "Not Started": {
            bg: "#AB020233",
            border: "#AB0202",
            text: "#AB0202",
        },
        "Not Applicable": {
            bg: "#e0e2e7",
            border: "#667085",
            text: "#667085",
        },
        "Initial Planning": {
            bg: "#e9dff3",
            border: "#9160C1",
            text: "#9160C1",
        },
        "In Development": {
            bg: "#ffeecd",
            border: "#FFAB07",
            text: "#FFAB07",
        },
        "Partially Implemented": {
            bg: "#d4e8fb",
            border: "#298BED",
            text: "#298BED",
        },
        "Fully Implemented": {
            bg: "#e2f6e7",
            border: "#01BD36",
            text: "#01BD36",
        },
    };

    const prioritySelectColorMap = {
        Top: {
            bg: "#AB02024D",
            border: "",
            text: "#AB0202",
        },
        High: {
            bg: "#ffd0b5",
            border: "#ffedca",
            text: "#ff6e1d",
        },
        Medium: {
            bg: "#ffe6b5",
            border: "#00C0A9",
            text: "#ffb932",
        },
        Low: {
            bg: "#c7f1d3",
            border: "#01BD36",
            text: "#01BD36",
        },
    };

    const getISSBData = async () => {
        try {
            const { data } = await ApiS1Config.get("/get_assessment_data", {
                headers: { assessmentName: "ISSB Readiness" },
            });
            setSelectedScope(data["ISSB Readiness"]);
        } catch (error) {
            error.response.data.Message === "Company assessment not found"
                ? navigate("/select-assessment")
                : "";
            if (
                error.response.data.Message === "There is no active assessment"
            ) {
                navigate("/issb-report");
                showNotification({
                    message: (
                        <span className="capitalize">
                            {error.response.data.Message}
                        </span>
                    ),
                    color: "red",
                });
            }
            //console.log(error);
        }
    };

    const postAnswers = async (extraData) => {
        setPostLoading(true);
        //console.log(selectedScope);
        try {
            setPostLoading(true);
            const { data } = await ApiS1Config.post(`/post_scope`, extraData, {
                headers: {
                    assessmentName: "ISSB Readiness",
                },
            });
            setPostLoading(false);
            //console.log(data);
            notifications.show({
                title: "Updated successfully!",
                color: "green",
                icon: <FaCheckCircle />,
            });
            setPostAnswersState(false);
            getISSBData();
        } catch (error) {
            setPostLoading(false);
            //console.log(error);
            notifications.show({
                title: error.response.data.Message,
                color: "red",
            });
        }
    };
    const getReport = async () => {
        setReportLoading(true);
        try {
            setReportLoading(true);
            const { data } = await ApiS1Config.post(
                "/get_report",
                {},
                {
                    headers: {
                        assessmentType: "ISSB Readiness",
                    },
                }
            );
            setReportLoading(false);
            navigate("/issb-report");
            //console.log(data);
        } catch (error) {
            setReportLoading(false);
            //console.log(error);
        }
    };

    return (
        <MainLayout
            navbarTitle={"ISSB Readiness"}
            breadcrumbItems={[
                { title: <IoMdHome size={20} />, href: "/get-started" },
                {
                    title: "Regulatory Readiness",
                    href: "/Grc/regulatory-readiness",
                },
                { title: "ISSB Readiness", href: "#" },
            ]}
        >
            <div className="grid lg:grid-cols-2 justify-center bg-[#FFFFFF] border-2 border-[#E8E7EA] p-6 gap-5 py-3 mb-6 rounded-lg text-primary">
                <button
                    className={`relative text-lg font-semibold py-3 px-6 transition-all ${
                        active === "Readiness Assessment"
                            ? "active-tab rounded"
                            : "text-[#5A5A5A] rounded-lg border-2"
                    }`}
                    onClick={() => setActive("Readiness Assessment")}
                >
                    {t("Readiness Assessment")}
                </button>

                <button
                    className={`relative text-lg font-semibold py-3 px-6 transition-all ${
                        active === "Readiness Assessment Report"
                            ? "active-tab rounded"
                            : "text-[#5A5A5A] rounded-lg border-2"
                    }`}
                    onClick={() => setActive("Readiness Assessment Report")}
                >
                    {t("Readiness Assessment Report")}
                </button>
            </div>
            {active == "Readiness Assessment" ? (
                <>
                    <div className="w-full justify-between flex items-center py-5 px-3">
                        <GuideModalButton buttonText="Assessment Guide">
                            <Guide />
                        </GuideModalButton>
                    </div>
                    <div className="items-center justify-between w-full px-4 py-5 bg-white rounded-lg shadow-md mb-7 sm:flex ">
                        <div>
                            <h1 className="text-lg font-bold text-black">
                                {t("issb")}
                            </h1>
                            <p className="font-normal text-sm text-[#667085]">
                                {t("descriptiveBodyText")}
                            </p>
                        </div>
                        <div className="mt-5 sm:mt-0   gap-5">
                            <Button
                                className="duration-200 ease-out bg-transparent border-2 rounded-lg text-primary border-secondary-300 hover:-translate-y-2 hover:bg-secondary-400 hover:bg-transparent hover:text-primary outline-none focus:outline-none"
                                size="md"
                            >
                                <GoShare className="me-2" />
                                {t("export")}
                            </Button>
                        </div>
                    </div>
                    <IssbTable
                        readinessColorMap={readinessColorMap}
                        prioritySelectColorMap={prioritySelectColorMap}
                        selectedScope={selectedScope}
                        setSelectedScope={setSelectedScope}
                        PriorityLevels={PriorityLevels}
                        ReadinessLevel={ReadinessLevel}
                        postAnswers={postAnswers}
                        setPostAnswersState={setPostAnswersState}
                        PostAnswersState={PostAnswersState}
                        postLoading={postLoading}
                        getISSBData={getISSBData}
                        reportLoading={reportLoading}
                        getReport={getReport}
                    />
                </>
            ) : (
                <>
                    <IssbReport />
                </>
            )}
        </MainLayout>
    );
};

export default Issb;
