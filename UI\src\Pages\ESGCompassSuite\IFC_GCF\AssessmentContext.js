// AssessmentContext.js
import { create } from "zustand";
import axios from "axios";
import Cookies from "js-cookie";

const API_BASE_URL =
    "https://finance-guard-api-staging.azurewebsites.net/ifc-and-gcf-assessment";

export const useAssessmentStore = create((set, get) => ({
    assessment: null,
    loading: true,
    error: null,
    completedSections: 0,
    companyUsers: [],

    fetchAssessment: async () => {
        const token = Cookies.get("level_user_token");
        if (!token) throw new Error("Authorization token missing");

        try {
            set({ loading: true });
            const response = await axios.get(
                `${API_BASE_URL}/assessments/last`,
                {
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${token}`,
                    },
                }
            );

            // Calculate initial completed sections
            const [completedCount, updatedSections] =
                get().checkSectionsCompleted(response.data.assessment.sections);

            set({
                assessment: {
                    ...response.data.assessment,
                    sections: updatedSections,
                },
                completedSections: completedCount,
                loading: false,
            });

            return response.data;
        } catch (error) {
            const errorMessage = error.response?.data?.message || error.message;
            set({ error: errorMessage, loading: false });
            throw error;
        }
    },

    checkTopicsCompleted: (topics) => {
        return topics.map((topic) => {
            const is_completed = !topic.criteria.some(
                (criteria) => criteria.level === null
            );
            return {
                ...topic,
                is_completed,
            };
        });
    },

    checkSectionsCompleted: (sections) => {
        let numberOfSectionCompleted = 0;
        const updatedSections = sections.map((section) => {
            const updatedTopics = get().checkTopicsCompleted(section.topics);
            const is_completed = !updatedTopics.some(
                (topic) => !topic.is_completed
            );
            numberOfSectionCompleted += is_completed ? 1 : 0;
            return {
                ...section,
                topics: updatedTopics,
                is_completed,
            };
        });
        return [numberOfSectionCompleted, updatedSections];
    },

    updateCriterion: async (criteriaID, level) => {
        const token = Cookies.get("level_user_token");
        if (!token) throw new Error("Authorization token missing");

        try {
            const response = await axios.patch(
                `${API_BASE_URL}/criteria/${criteriaID}`,
                { level },
                {
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${token}`,
                    },
                }
            );

            // Update local state
            set((state) => {
                const updatedSections = state.assessment.sections.map(
                    (section) => ({
                        ...section,
                        topics: section.topics.map((topic) => ({
                            ...topic,
                            criteria: topic.criteria.map((criterion) =>
                                criterion.id === criteriaID
                                    ? {
                                          ...criterion,
                                          ...response.data.criterion,
                                      }
                                    : criterion
                            ),
                        })),
                    })
                );

                // Recalculate completed sections
                const [completedCount] =
                    get().checkSectionsCompleted(updatedSections);

                return {
                    assessment: {
                        ...state.assessment,
                        sections: updatedSections,
                    },
                    completedSections: completedCount,
                };
            });

            return response.data;
        } catch (error) {
            console.error("Error updating criterion:", error);
            throw error;
        }
    },

    addCriterionEvidence: async (criteriaID, evidenceData) => {
        const token = Cookies.get("level_user_token");
        if (!token) throw new Error("Authorization token missing");

        try {
            const response = await axios.post(
                `${API_BASE_URL}/criteria/${criteriaID}/evidences`,
                evidenceData,
                {
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${token}`,
                    },
                }
            );

            // Update local state
            set((state) => {
                const updatedSections = state.assessment.sections.map(
                    (section) => ({
                        ...section,
                        topics: section.topics.map((topic) => ({
                            ...topic,
                            criteria: topic.criteria.map((criterion) =>
                                criterion.id === criteriaID
                                    ? {
                                          ...criterion,
                                          evidences: [
                                              ...(criterion.evidences || []),
                                              response.data.evidence,
                                          ],
                                      }
                                    : criterion
                            ),
                        })),
                    })
                );

                return {
                    assessment: {
                        ...state.assessment,
                        sections: updatedSections,
                    },
                };
            });

            return response.data;
        } catch (error) {
            console.error("Error adding evidence:", error);
            throw error;
        }
    },

    deleteCriterionEvidence: async (criteriaID, evidenceID) => {
        const token = Cookies.get("level_user_token");
        if (!token) throw new Error("Authorization token missing");

        try {
            const response = await axios.delete(
                `${API_BASE_URL}/criteria/${criteriaID}/evidences/${evidenceID}`,
                {
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${token}`,
                    },
                }
            );

            // Update local state
            set((state) => {
                const updatedSections = state.assessment.sections.map(
                    (section) => ({
                        ...section,
                        topics: section.topics.map((topic) => ({
                            ...topic,
                            criteria: topic.criteria.map((criterion) =>
                                criterion.id === criteriaID
                                    ? {
                                          ...criterion,
                                          evidences: criterion.evidences.filter(
                                              (e) => e.id !== evidenceID
                                          ),
                                      }
                                    : criterion
                            ),
                        })),
                    })
                );

                return {
                    assessment: {
                        ...state.assessment,
                        sections: updatedSections,
                    },
                };
            });

            return response.data;
        } catch (error) {
            console.error("Error deleting evidence:", error);
            throw error;
        }
    },

    addCriterionAction: async (criteriaID, actionData) => {
        const token = Cookies.get("level_user_token");
        if (!token) throw new Error("Authorization token missing");

        try {
            const response = await axios.post(
                `${API_BASE_URL}/criteria/${criteriaID}/actions`,
                actionData,
                {
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${token}`,
                    },
                }
            );

            return response.data;
        } catch (error) {
            console.error("Error adding action:", error);
            throw error;
        }
    },

    deleteCriterionAction: async (criteriaID, actionID) => {
        const token = Cookies.get("level_user_token");
        if (!token) throw new Error("Authorization token missing");

        try {
            const response = await axios.delete(
                `${API_BASE_URL}/criteria/${criteriaID}/actions/${actionID}`,
                {
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${token}`,
                    },
                }
            );

            return response.data;
        } catch (error) {
            console.error("Error deleting action:", error);
            throw error;
        }
    },

    fetchCompanyUsers: async () => {
        const token = Cookies.get("level_user_token");
        if (!token) {
            toast.error("Authorization token is missing");
            return;
        }

        try {
            const response = await fetch(
                "https://portal-auth-main.azurewebsites.net/get-company-users",
                {
                    method: "GET",
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${token}`,
                    },
                }
            );

            if (!response.ok) {
                throw new Error(
                    `Failed to fetch company users: ${response.statusText}`
                );
            }

            let users = await response.json();
            set({
                companyUsers: users,
            });
        } catch (error) {
            console.error("Error fetching company users:", error);
        }
    },
}));
