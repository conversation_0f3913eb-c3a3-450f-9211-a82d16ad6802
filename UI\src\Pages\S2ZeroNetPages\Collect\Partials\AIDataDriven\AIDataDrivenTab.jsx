import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { FiUpload } from "react-icons/fi";
import AIDataDrivenTable from "./AIDataDrivenTable";
import { useId } from "react";
import { Button, FileInput } from "@mantine/core";
import { useUploadData } from "./hooks/useUploadData";
import ShowUploadedFiles from "./ShowUploadedFiles";
// import { driver } from "driver.js";
// import "driver.js/dist/driver.css";
// import "@/assets/css/index.css";
// import { FaQuestionCircle } from "react-icons/fa";

export default function AIDataDrivenTab({ activeTab }) {
  const uploadfileId = useId();
  const { t } = useTranslation();
  const { files, setFiles, handleRemove, handleUploadData, loading } =
    useUploadData();
  // const [elementsReady, setElementsReady] = useState(false);

  // const checkElementsExist = () => {
  //   const steps = getGuideSteps();
  //   const allElementsExist = steps.every((step) =>
  //     document.querySelector(step.element)
  //   );
  //   return allElementsExist;
  // };

  // useEffect(() => {
  //   if (activeTab === "AIDataDriven") {
  //     const observer = new MutationObserver(() => {
  //       if (checkElementsExist()) {
  //         setElementsReady(true);
  //         observer.disconnect();
  //       }
  //     });

  //     observer.observe(document.body, {
  //       childList: true,
  //       subtree: true,
  //     });

  //     return () => observer.disconnect();
  //   }
  // }, [activeTab]);

  // const getGuideSteps = () => [
  //   {
  //     element: ".Fill-the-Ai-Template",
  //     popover: {
  //       title: t("Fill in the Template"),
  //       description: t(
  //         "Manually fill the template with your billing or energy usage data"
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Upload-the-AIData-Template",
  //     popover: {
  //       title: t("Upload the Template"),
  //       description: t(
  //         "Upload Data: After filling in the data, click the Upload Data button to upload the file into the system."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Upload-the-AIData-Template",
  //     popover: {
  //       title: t("Upon upload: The system reads and extracts your data."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Manage-and-Review-Uploaded-Data",
  //     popover: {
  //       title: t("Manage and Review Uploaded Data"),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Data-Table-Ai-View",
  //     popover: {
  //       title: t("Data Table View"),
  //       description: t(
  //         "Uploaded data appears in a structured table with the following actions"
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Open-a-detailed-view-of-the-record",
  //     popover: {
  //       title: t("View: Open a detailed view of the record."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Edit-the-ai-record",
  //     popover: {
  //       title: t("Edit: Correct or update any field."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Delete-the-ai-record",
  //     popover: {
  //       title: t("Delete: Remove any wrong entry."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  // ];

  // const startGuide = () => {
  //   const driverObj = driver({
  //     showProgress: true,
  //     popoverClass: "my-custom-popover-class",
  //     nextBtnText: "Next",
  //     prevBtnText: "Back",
  //     doneBtnText: "Done",
  //     progressText: "Step {{current}} of {{total}}",
  //     steps: getGuideSteps(),
  //     overlayColor: "rgba(0, 0, 0, 0)",
  //     onDestroyStarted: () => {
  //       localStorage.setItem("hasSeenAIDataCollectGuide", "true");
  //       driverObj.destroy();
  //     },
  //   });
  //   driverObj.drive();
  // };

  // useEffect(() => {
  //   const hasSeenGuide = localStorage.getItem("hasSeenAIDataCollectGuide");
  //   if (!hasSeenGuide) {
  //     startGuide();
  //   }
  //   return () => {
  //     const driverObj = driver();
  //     if (driverObj.isActive()) {
  //       driverObj.destroy();
  //     }
  //   };
  // }, [activeTab, elementsReady]);

  return (
    <div>
      {/* <div
        onClick={startGuide}
        style={{
          position: "fixed",
          bottom: 20,
          right: 20,
          cursor: "pointer",
          zIndex: 1000,
        }}
      >
        <div className="h-12 w-12 flex items-center justify-center bg-gradient-to-tr from-[#2C5A8C] via-[#1C889C] to-[#13B1A8] p-1  cursor-pointer rounded-full">
          <FaQuestionCircle
            size={34}
            color="#ffffff"
            className="mx-auto cursor-pointer"
          />
        </div>
      </div> */}
      <div className="bg-white rounded-lg p-5">
        <div className="md:flex justify-between items-center mb-4 ">
          <p className="font-semibold text-[16px] leading-[24px] Fill-the-Ai-Template">
            <span className="text-secondary-300 capitalize me-1">
              {t("step1 :")}
            </span>
            {t(
              "Add data in the template, save the file, and upload the template."
            )}
          </p>
          <div className="flex gap-4 items-center">
            <FileInput
              className="hidden"
              id={uploadfileId}
              type="file"
              value={files}
              onChange={(newFiles) => setFiles(newFiles)}
              multiple
              accept=".pdf"
            />
            <ShowUploadedFiles
              files={files}
              handleRemove={handleRemove}
              handleUploadData={handleUploadData}
              loading={loading}
            />
            <label
              htmlFor={uploadfileId}
              className="flex Upload-the-AIData-Template items-center justify-center gap-4 p-2 px-5 text-white shadow-md cursor-pointer bg-secondary-300 rounded-lg mt-5 md:mt-0"
            >
              <span>
                <FiUpload />
              </span>
              <span className="text-nowrap">{t("Upload Data")}</span>
            </label>
          </div>
        </div>
        <hr className="h-[2px] bg-gray-300  mx-auto" />
        <div className="mt-4 flex justify-end w-full">
          <h1></h1>
          <Button
            onClick={() => handleUploadData(files)}
            loading={loading}
            disabled={loading || files.length === 0}
            className="px-8 text-lg mt-5 text-white border-2 shadow-md bg-primary rounded-md md:mx-0 md:mt-0 disabled:cursor-not-allowed"
          >
            <span>{t("Extract")}</span>
          </Button>
        </div>
      </div>
      <div className="w-full">
        <AIDataDrivenTable />
      </div>
    </div>
  );
}
