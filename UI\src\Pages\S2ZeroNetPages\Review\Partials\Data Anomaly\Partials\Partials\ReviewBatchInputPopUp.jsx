import { Modal } from "@mantine/core";
import React from "react";
import ReviewBatchPopUpTable from "./ReviewBatchPopUpTable";
// import PopUpTable from "./PopUpTable";

export default function ReviewBatchInputPopUp({
 opened,
 onClose,
 currentBatchId,
 data,
 assetTypeAll,
 getTableData
}) {
 const Status = {
  Pending: {
   bg: "#ffeecd",
   text: "#FFAB07",
   // border: "#00C0A9",
  },
  Accepted: {
   bg: "#ccf2d7",
   text: "#01BD36",
   // border: "#FF6007",
  },
  Rejected: {
   bg: "#eecccc",
   text: "#AB0202",
   // border: "#FF6007",
  },
 };
 const CurrentRow = data?.filter((item) => currentBatchId === item.id);
 return (
  <Modal
   opened={opened}
   onClose={onClose}
   centered
   size={"100%"}
   withCloseButton={false}
  >
   <ReviewBatchPopUpTable
    data={CurrentRow}
    Status={Status}
    assetTypeAll={assetTypeAll}
    closePopup={onClose}
    getTableData={getTableData}
   />
  </Modal>
 );
}
