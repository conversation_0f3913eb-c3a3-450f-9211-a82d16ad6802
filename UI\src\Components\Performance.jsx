import React from "react";
import Gauge<PERSON>hart from "../Components/Charts/GaugeChart";
import PropType from "prop-types";

const Performance = ({ assessments, activeTabIndex }) => {
  // Select the assessment based on the active tab index
  const activeAssessment = assessments?.[activeTabIndex];
  console.log(activeAssessment);
  return (
    <div className="mb-6 grid grid-cols-1 lg:grid-cols-3 ">
      {activeAssessment?.labels?.map((category, catIndex) => (
        <div key={catIndex} className="w-[100%]">
          <GaugeChart
            assessmentScore={category?.labels?.map((data) => data?.score)}
            assessmentName={category?.labels?.map((data) => data?.name)}
            category={category?.category_name}
          />
        </div>
      ))}
    </div>
  );
};

Performance.propTypes = {
  assessments: PropType.array,
  activeTabIndex: PropType.number,
};

export default Performance;
