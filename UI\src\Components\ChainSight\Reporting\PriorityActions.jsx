import { Progress, Text } from "@mantine/core";

const PriorityActions = ({ latestReport }) => {
  const top = latestReport?.priority_actions?.top_performing_areas || [];
  const bottom = latestReport?.priority_actions?.bottom_performing_areas || [];
  const medium = latestReport?.priority_actions?.strategic_opportunities || [];

  const allActions = [
    ...top.map((action) => ({ key: action.id, ...action, source: "top" })),
    ...medium.map((action) => ({ key: action.id, ...action, source: "medium" })),
    ...bottom.map((action) => ({ key: action.id, ...action, source: "bottom" })),
  ];

  const getPriorityStyles = (source) => {
    switch (source) {
      case "top":
        return {
          label: "High",
          bgColor: "bg-[#00C0A91A]",
          textColor: "text-[#00C0A9]",
          progressColor: "#00C0A9",
        };
      case "bottom":
        return {
          label: "Low",
          bgColor: "bg-[#FF4D4F1A]",
          textColor: "text-[#FF4D4F]",
        };
      case "medium":
        return {
          label: "Medium",
          bgColor: "bg-[#8C8C8C1A]",
          textColor: "text-[#8C8C8C]",
        };
      default:
        return {
          label: "Unknown",
          bgColor: "bg-[#E8E7EA]",
          textColor: "text-[#525252]",
        };
    }
  };

  return (
    <div className="border-2 border-[#E8E7EA] bg-[#FFFFFF] rounded-lg p-6">
      <div className="flex justify-between items-center mb-4">
        <Text size="xl" className="font-bold text-[#272727]" mb="md">
          Priority Actions
        </Text>
      </div>
      {allActions?.map((action) => {
        const { label, bgColor, textColor } = getPriorityStyles(action.source);
        return (
          <div
            key={action.id}
            className="border-2 border-[#E8E7EA] bg-[#FFFFFF] rounded-lg p-4 mb-4"
          >
            <div className="flex justify-between items-center">
              <Text weight={500} className="text-[#272727]">
                {action.title}
              </Text>
              <span
                className={`${bgColor} ${textColor} text-sm px-4 rounded-full`}
              >
                {label}
              </span>
            </div>
            {action.collaborators != 0 && (
              <Text size="sm" className="text-[#525252]">
                Collaboration | Owner:{" "}
                {action.collaborators.map((c) => <span key={c.user_id}>{c.user_name}</span>)}
              </Text>
            )}
            <div className="flex justify-between items-center mt-2 w-full">
              <div className="w-full flex gap-2 items-center">
                <Progress
                  value={action.percentage * 100}
                  color="#00C0A9"
                  className="w-[30%]"
                />
                <Text size="sm" weight={500}>
                  {(action.percentage * 100).toFixed(0)}%
                </Text>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default PriorityActions;
