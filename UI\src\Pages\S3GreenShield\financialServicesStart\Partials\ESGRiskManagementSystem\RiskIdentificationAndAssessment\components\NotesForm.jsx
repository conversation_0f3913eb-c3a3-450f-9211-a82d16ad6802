
import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Loader, Select, Textarea, TextInput } from "@mantine/core";
import { IconCalendar } from "@tabler/icons-react";

import { TbFileUpload } from "react-icons/tb";
import { DateInput } from '@mantine/dates';
import { MdKeyboardArrowDown } from "react-icons/md";
import dayjs from "dayjs";
import authConfig from "@/Api/authConfig";


const NotesForm = ({ handleChange, formData, addRiskFn, loading, type, setDateValue, dateValue }) => {
  const {
    attachment,
    alignsWithRiskAppetite,
    preparedBy,
    Reviewer,
    dateRecorded,
    comments,
  } = formData;
  console.log("🚀 ~ NotesForm ~ comments:", comments)
  const [company, setCompany] = useState([]);

  useEffect(() => {
    const Company = async () => {
      try {
        const response = await authConfig.get("/get_all_user_by_token");

        const users = response.data.companyUsers;

        if (Array.isArray(users)) {
          const userAuthId = users
            .filter((user) => !!user.userAuthId)
            .map((user) => ({
              value: user.userAuthId,
              label: user.userName,
            }));
          setCompany(userAuthId);
        } else {
          console.error("Error:", response.data);
        }
      } catch (error) {
        console.error("Error company ", error);
      }
    };

    Company();
  }, []);

  return (
    <div>
      <h5 className="text-primary font-bold text-center lg:text-2xl mb-10 mt-6">
        Additional Information
      </h5>

      <div>
        <div className="grid lg:grid-cols-2 gap-5">
          <div className="flex flex-col">
            <label htmlFor="fileUpload" className="font-semibold">
              Attachments
            </label>
            <div className={`flex items-center justify-between
           border ${type == 'view' ? 'bg-gray-300' : 'bg-white'} rounded-lg h-[37px] w-full`}>
              <input
                disabled={type == "view"}
                multiple
                type="file"
                id="fileUpload"
                className="hidden"
                onChange={(e) => handleChange("attachment", Array.from(e.target.files))}
              />
              {
                attachment && attachment.length > 0 ?
                  <span className="pl-2">
                    {attachment.length} file(s) selected
                  </span> :

                  <label
                    htmlFor="fileUpload"
                    className={`flex items-center gap-2 ${type == 'view' ? 'cursor-not-allowed' : 'cursor-pointer'} flex-1`}
                  >
                    <TbFileUpload className="ml-2" /> Upload files
                  </label>
              }
              <button
                disabled={type == "view"}
                onClick={() => document.getElementById("fileUpload").click()}
                className="bg-gray-200 px-4 h-full rounded-r-md"
              >
                Upload
              </button>
            </div>
          </div>

          <Select
            rightSection={<MdKeyboardArrowDown />}
            disabled={type == "view"}
            label="Aligns with Risk Appetite?"
            placeholder="Yes"
            value={alignsWithRiskAppetite}
            onChange={(value) => handleChange("alignsWithRiskAppetite", value)}
            data={[
              { value: "Yes", label: "Yes" },
              { value: "No", label: "No" },
            ]}
            rightSectionPointerEvents="none"
          />
        </div>

        <div className="relative mb-8 mt-8">
          <div className="flex justify-between items-center">
            <label
              htmlFor="fin-decs"
              className="text-base font-medium text-black"
            >Comments
            </label>
          </div>
          <Textarea
            id="fin-decs"
            disabled={type == "view"}
            // label="Financial Exposure Description"
            placeholder="Optional"
            value={comments}
            onChange={(e) => handleChange("comments", e.target.value)}
            classNames={{
              root: "w-full",
              input: "border border-gray-300 rounded min-h-[145px] mt-5 px-3 py-2",
            }}
          />
        </div>
        <h4 className="font-bold mt-8">Review & Authorisation</h4>

        <div className="grid lg:grid-cols-3 gap-5 mt-3">
          <TextInput
            disabled={type == "view"}
            label="Prepared By"
            placeholder="Jane Smith"
            value={preparedBy}
            onChange={(e) => handleChange("preparedBy", e.target.value)}
            className="flex-1"
          />
          <Select
            label="Reviewer/Validator"
            placeholder="Select reviewer"
            className="w-full"
            data={company}
            value={Reviewer}
            onChange={(value) => handleChange("Reviewer", value)}
            disabled={type === "view"}
            searchable
            maxDropdownHeight={200}
            size="md"
          />

          <DateInput
            disabled={type == "view"}
            label={`Date ${dateRecorded}`}
            placeholder="DD/MM/YYYY"
            valueFormat="DD/MM/YYYY"
            value={dateValue}
            onChange={(value) => {
              handleChange("dateRecorded", dayjs(value).format("DD/MM/YYYY"))
              setDateValue(value)
            }}
            leftSection={<IconCalendar size={16} />}
          />
        </div>

        {
          type !== "view" &&
          <>
            <div className="flex gap-5 mt-5">
              <Button className="hover:bg-slate-300 flex-1 bg-bg-lite1 text-gray-700 py-2 px-4 text-center border-r border-gray-300">
                Clear
              </Button>
              <Button onClick={addRiskFn} disabled={loading}
                className={`flex-1 flex gap-2 items-center justify-center  text-white py-2 px-4 text-center ${loading ? "cursor-not-allowed bg-gray-400" : "bg-teal-500 hover:bg-green-600"}`}>
                Save
                {loading && <Loader size="xs" color="white" className="ml-2" />}
              </Button>
            </div>

            {/* <Button className=" bg-primary hover:bg-green-600 w-full mx-auto mt-5 text-white py-2 px-4 text-center">
          Confirm
        </Button> */}
          </>
        }

      </div>
    </div>
  );
};

export default NotesForm;
