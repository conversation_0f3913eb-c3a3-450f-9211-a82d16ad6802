import { InternalFilesIcon, URLIcon, WebIcon } from "@/assets/icons/DataFoundationIcons";
import { useState } from "react";
import UrlScrapping from "./Components/UrlScrapping";
import WebScrapping from "./Components/WebScrapping";
import InternalFilesScrapping from "./Components/InternalFilesScrapping";

const DataScrapping = () => {
  const [active, setActive] = useState("URL")

  const tabs = ['URL','Web','Internal Files']
  
  return (
    <>
    <div className="flex justify-between bg-white p-2 border-2 rounded-xl">
        {tabs.map((tab) => (
          <button
            key={tab}
            className={`relative text-lg font-semibold py-3 px-6 transition-all ${
              active === tab
                ? "active-tab rounded before:hidden"
                : "text-[#5A5A5A] rounded-lg"
            }`}
            onClick={() => setActive(tab)}
          >
            <div className="flex items-center gap-2">
              {tab === 'URL' && <URLIcon color={active === tab} />}
              {tab === 'Web' && <WebIcon color={active === tab} />}
              {tab === 'Internal Files' && <InternalFilesIcon color={active === tab} />}
              <span>{tab}</span>
            </div>
          </button>
        ))}
    </div>
    {active === 'URL' && <UrlScrapping />}
    {active === 'Web' && <WebScrapping />}
    {active === 'Internal Files' && <InternalFilesScrapping />}
    </>
  );
};

export default DataScrapping;
