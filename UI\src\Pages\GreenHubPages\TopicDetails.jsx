import MainLayout from '@/Layout/MainLayout';
import { useParams } from 'react-router';
import { FaDownload } from 'react-icons/fa6';
import ViewPDF from '../S2ZeroNetPages/NetZeroReport/Components/ViewPDF';
import { useState, useEffect } from 'react';
import ApiS3 from '@/Api/apiS3';
import TopicCart from './Partials/TopicCart';
import { IoMdHome } from "react-icons/io";
function TopicDetails() {
  const { id } = useParams(); // Extract the `id` parameter from the URL
  const [topic, setTopic] = useState(null); // State for the current topic
  const [randomTopics, setRandomTopics] = useState([]); // State for three random topics

  // Fetch the current topic details
  const getTopic = async () => {
    try {
      const response = await ApiS3.get(`/greenhub-resourses/${id}`);
      setTopic(response?.data);
    } catch (error) {
      console.error('Error fetching topic:', error);
    }
  };

  // Fetch all topics
  const getTopics = async () => {
    try {
      const response = await ApiS3.get(`/greenhub-resourses/`);
      setRandomTopics(getRandomTopics(response?.data, 3));
    } catch (error) {
      console.error('Error fetching topics:', error);
    }
  };

  // Helper function to get random topics
  const getRandomTopics = (topicsArray, count) => {
    const shuffled = [...topicsArray].sort(() => 0.5 - Math.random()); // Shuffle the array
    return shuffled.slice(0, count); // Return the first `count` items
  };

  // Download attachment
  const downloadAttachment = (url) => {
    fetch(url)
      .then((response) => response.blob())
      .then((blob) => {
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = `${topic.topicName}.pdf`;
        link.click();
      })
      .catch((error) => console.error('Error downloading the file:', error));
  };

  // Fetch data on component mount
  useEffect(() => {
    getTopic();
    getTopics();
  }, [id]); // Re-fetch when the `id` changes

  if (!topic) return <div>Loading...</div>; // Show loading state while fetching data

  return (
    <MainLayout
        navbarTitle="Resources"
        breadcrumbItems={[
            { title: <IoMdHome size={20}/>, href: "/get-started" },
            { title: "Resources", href: "#" },
        ]}
    >
      {/* Topic Header Section */}
      <section className="px-10">
        <div className="flex flex-col items-start pt-6 pl-8 my-5 bg-white rounded-2xl">
          <h3 className="text-3xl max-md:text-3xl md:w-[70%] font-bold text-[#0f0f0f]">{topic.topicName}</h3>
          <p className="my-4 md:w-[60%] leading-[1.5] text-xl text-gray-600 text-start">JAN 31, 2024</p>
        </div>
      </section>

      {/* Topic Image Section */}
      <section className="px-10">
        <div className="w-full h-64 sm:h-80 md:h-96 bg-gray-100 flex items-center justify-center overflow-hidden">
          <img src={topic.image} alt={topic.topicName} className="max-h-full object-contain" />
        </div>
      </section>

      {/* Topic Content Section */}
      <section className="px-10 py-10">
        <div className="w-full flex flex-col items-start lg:gap-5 text-lg">
          <p className="text-gray-500 text-xl">{topic.content}</p>
        </div>
      </section>

      {/* Topic Files Section */}
      <section className="px-10 py-10">
        <div className="">
          {topic?.files?.map((file, index) => (
            <ul key={index}>
              <li className="flex gap-3 items-center">
                <ViewPDF
                  pdfUrl={file}
                  text={`View ${topic.topicName} Report`}
                  btnStyle={
                    'text-[#07838F] bg-transparent hover:bg-transparent hover:text-[#07738F] duration-300 text-2xl  flex gap-2 items-center flex-row-reverse'
                  }
                />
                <span>{'//'}</span>
                <button className="text-[#07838F] text-2xl flex gap-2 items-center" onClick={() => downloadAttachment(file)}>
                  <FaDownload />
                  <span>Download</span>
                </button>
              </li>
            </ul>
          ))}
        </div>
      </section>

      {/* Related Insights Section */}
      <section className="px-10">
        <h1 className="text-[#07838F] text-4xl">Related Insights</h1>
        <div className="relatedInsights px-16">
          <div className="grid grid-cols-1 mt-6 gap-x-6 gap-y-10 sm:grid-cols-2 xl:grid-cols-3 lg:gap-10 md:gap-x-4">
            {randomTopics.map((relatedTopic) => (
              <TopicCart key={relatedTopic._id} image={relatedTopic.image} topicName={relatedTopic.topicName} id={relatedTopic._id} />
            ))}
          </div>
        </div>
      </section>
    </MainLayout>
  );
}

export default TopicDetails;
