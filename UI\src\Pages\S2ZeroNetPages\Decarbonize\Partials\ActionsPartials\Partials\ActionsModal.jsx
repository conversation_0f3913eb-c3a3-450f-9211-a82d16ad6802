import { useRef, useState, useEffect } from "react";
import {
  Modal,
  Button,
  Select,
  Input,
  InputWrapper,
  Radio,
  Group,
  MultiSelect,
  Textarea,
} from "@mantine/core";

import { YearPickerInput } from "@mantine/dates";
import { isNotEmpty, useForm } from "@mantine/form";
import { showNotification } from "@mantine/notifications";
import Loading from "@/Components/Loading";
import ApiS2 from "@/Api/apiS2Config";
import { useTranslation } from "react-i18next";
// import { driver } from "driver.js";
// import "driver.js/dist/driver.css";
// import "@/assets/css/index.css";
// import { FaQuestionCircle } from "react-icons/fa";

const ActionsModal = ({
  isOpen,
  onRequestClose,
  refetchAgain,
  assetTypes,
  allAssignees = [],
}) => {
  const { t } = useTranslation();
  const categories = assetTypes?.map((categ) => {
    const c = { value: `${categ.id}`, label: categ.asset };
    return c;
  });
  const [elementsReady, setElementsReady] = useState(false);
  const activeTab = "modal";
  const [yearFrom, setYearFrom] = useState("");
  const [yearTo, setYearTo] = useState("");
  const [status, setStatus] = useState("");
  const [selectedAssingess, setSelectedAssingess] = useState([]);

  // const [actionCatege, setActionCatege] = useState("");
  const actionCategeId = useRef();

  const [submit, setSubmit] = useState(false);

  const form = useForm({
    mode: "uncontrolled",
    initialValues: {
      name: "",
      reductionValue: "",
      description: "",
    },
    validate: {
      name: isNotEmpty("title is required"),
      reductionValue: isNotEmpty("reductionValue is required"),
      description: "",
    },
  });

  const addTargetFunc = async (values) => {
    let assetName = categories.find((el) => el.value == actionCategeId.current);

    let d = {
      name: values.name,
      reductionValue: values.reductionValue,
      description: values.description,
      yearFrom: yearFrom,
      yearTo: yearTo,
      status: status,
      assetTypeId: actionCategeId.current,
      assetTypeName: assetName.label,
      assignees: selectedAssingess,
    };

    if (yearFrom == "" || yearTo == "") {
      showNotification({
        message: "date is required",
        color: "red",
        position: "top-center",
      });
      return;
    }

    if (values.description == "") {
      showNotification({
        message: "description is required",
        color: "red",
        position: "top-center",
      });
      return;
    }
    setSubmit(true);
    refetchAgain(true);

    try {
      const { data } = await ApiS2.post(
        "/decarbonize/create_climate_action",
        d
      );
      showNotification({
        message: "Form Data Added Successfully",
        color: "green",
      });
      refetchAgain(false);
    } catch (er) {
      showNotification({
        message: "Error happend",
        color: "red",
      });
      //console.log(er);
    }
    setSubmit(false);
  };

  const handleYearFrom = (d) => {
    let da = d.getFullYear();
    setYearFrom(da);
  };

  const handleYearTo = (d) => {
    let da = d.getFullYear();
    setYearTo(da);
  };
  const uniqueAssignees = [
    ...new Map(allAssignees.map((item) => [item.label.name, item])).values(),
  ];

  // const getGuideSteps = () => [
  //   {
  //     element: ".Fill-in-the-new-action-details",
  //     popover: {
  //       title: t("Fill in the new action details"),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Fill-in-the-Action-Title",
  //     popover: {
  //       title: t("Action Title"),
  //       description: t("Give a short and clear name to describe the action (e.g., 'Upgrade Air Conditioning Units')."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Fill-in-the-Action-Category",
  //     popover: {
  //       title: t("Action Category"),
  //       description: t("Select the type of initiative related to the action (e.g.,Refrigerants (e.g., replacing old cooling systems))"),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Fill-in-the-Action-Status",
  //     popover: {
  //       title: t("Status"),
  //       description: t("Choose the current status of the action: Not Planned: Idea stage, not yet scheduled. Planned: Scheduled but not started. In Progress: Work has started. Completed: Action finished. On Hold: Temporarily paused."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Fill-in-the-Action-Start-Date",
  //     popover: {
  //       title: t("Start Date"),
  //       description: t("When the action is expected to begin."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Fill-in-the-Action-Due-Date",
  //     popover: {
  //       title: t("Due Date"),
  //       description: t("The deadline to complete the action."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Fill-in-the-Action-Emission-Reduction-Percentage",
  //     popover: {
  //       title: t("Emission Reduction Percentage (% from 1 to 100)"),
  //       description: t("Estimate how much this action will contribute to reducing emissions compared to baseline (e.g., 5% reduction)."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Fill-in-the-Action-Description",
  //     popover: {
  //       title: t("Description (optional)"),
  //       description: t("Add any extra explanation or notes about the action, like how it will be implemented or any special requirements."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Fill-in-the-Action-Assignees",
  //     popover: {
  //       title: t("Assignees (pick who is responsible)"),
  //       description: t("Choose the team members who will lead or be accountable for carrying out this action."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Fill-in-the-Action-Save",
  //     popover: {
  //       title: t("Click 'Add Action' to save your action."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  // ];

  // const checkElementsExist = () => {
  //   const steps = getGuideSteps();
  //   const allElementsExist = steps.every((step) =>
  //     document.querySelector(step.element)
  //   );
  //   return allElementsExist;
  // };

  // useEffect(() => {
  //   if (activeTab === "modal") {
  //     const observer = new MutationObserver(() => {
  //       if (checkElementsExist()) {
  //         setElementsReady(true);
  //         observer.disconnect();
  //       }
  //     });

  //     observer.observe(document.body, {
  //       childList: true,
  //       subtree: true,
  //     });

  //     return () => observer.disconnect();
  //   }
  // }, [activeTab]);

  // const startGuide = () => {
  //   const driverObj = driver({
  //     showProgress: true,
  //     popoverClass: "my-custom-popover-class",
  //     nextBtnText: "Next",
  //     prevBtnText: "Back",
  //     doneBtnText: "Done",
  //     overlayColor: "rgba(0, 0, 0, 0)",
  //     progressText: "Step {{current}} of {{total}}",
  //     steps: getGuideSteps(),
  //     onDestroyStarted: () => {
  //       localStorage.setItem("hasSeenDecarbonizeActionsModalGuide", "true");
  //       driverObj.destroy();
  //     },
  //   });
  //   driverObj.drive();
  // };

  // useEffect(() => {
  //   const hasSeenGuide = localStorage.getItem("hasSeenDecarbonizeActionsModalGuide");
  //   if (!hasSeenGuide) {
  //     startGuide();
  //   }
  //   return () => {
  //     const driverObj = driver();
  //     if (driverObj.isActive()) {
  //       driverObj.destroy();
  //     }
  //   };
  // }, [activeTab, elementsReady]);

  return (
    <Modal opened={isOpen} onClose={onRequestClose} title="Add New Action">
      {/* <div
        onClick={startGuide}
        style={{
          position: "absolute",
          bottom: -85,
          left: 20,
          cursor: "pointer",
          zIndex: 1000,
        }}
      >
        <div className="h-12 w-12 flex items-center justify-center bg-gradient-to-tr from-[#2C5A8C] via-[#1C889C] to-[#13B1A8] p-1  cursor-pointer rounded-full">
          <FaQuestionCircle
            size={34}
            color="#ffffff"
            className="mx-auto cursor-pointer"
          />
        </div>
      </div> */}
      <form onSubmit={form.onSubmit(addTargetFunc)} className="Fill-in-the-new-action-details">
        <div>
          <InputWrapper label="Action Title" className="py-3 Fill-in-the-Action-Title">
            <Input
              placeholder=""
              {...form.getInputProps("name")}
              key={form.key("name")}
            />
          </InputWrapper>

          <Select
            onChange={(e) => (actionCategeId.current = e)}
            classNames={{ label: "py-2" }}
            label="Action category"
            placeholder="Pick value"
            data={categories}
            className="Fill-in-the-Action-Category"
          />
        </div>

        <Radio.Group className="py-4 Fill-in-the-Action-Status" name="favoriteFramework" label="Status">
          <Group mt="xs">
            <Radio
              onClick={() => setStatus(1)}
              color="teal.9"
              value="Not planned"
              label="Not planned"
            />
            <Radio
              onClick={() => setStatus(2)}
              color="teal.9"
              value="Planned"
              label="Planned"
            />
            <Radio
              onClick={() => setStatus(5)}
              color="teal.9"
              value="Completed"
              label="Completed"
            />
            <Radio
              onClick={() => setStatus(3)}
              color="teal.9"
              value="In progress"
              label="In progress"
            />
            <Radio
              onClick={() => setStatus(4)}
              color="teal.9"
              value="On hold"
              label="On hold"
            />
          </Group>
        </Radio.Group>

        <div className="grid grid-cols-1 gap-4 mt-4 sm:grid-cols-2 pb-3">
          <div>
            <YearPickerInput
              label="Start date"
              placeholder="Start date"
              onChange={handleYearFrom}
              className="Fill-in-the-Action-Start-Date"
            />
          </div>
          <div>
            <YearPickerInput
              label="Due date"
              placeholder="Due date"
              onChange={handleYearTo}
              className="Fill-in-the-Action-Due-Date"
            />
          </div>
        </div>
        <Input.Wrapper label={t("targetsModal.emissionReductionLabel")} className="Fill-in-the-Action-Emission-Reduction-Percentage">
          <Input
            // placeholder={t("targetsModal.emissionReductionPlaceholder")}
            rightSection="%"
            // value={reductionPct}
            {...form.getInputProps("reductionValue")}
            key={form.key("reductionValue")}
            type="number"
            // onChange={(e) => setReductionPct(e.target.value)}
          />
          <Input.Description className="py-2">
            {t("targetsModal.emissionReductionDescription")}
          </Input.Description>
        </Input.Wrapper>

        <Textarea
          {...form.getInputProps("description")}
          key={form.key("description")}
          label="Discreption"
          placeholder=""
          className="Fill-in-the-Action-Description"
        />

        {allAssignees.length == 0 ? (
          <>
            <Loading /> Loading Assignees{" "}
          </>
        ) : (
          <MultiSelect
            onChange={(selectedNames) => {
              //console.log(selectedNames);

              const selectedIds = selectedNames.map(
                (name) =>
                  uniqueAssignees.find((item) => item.label.name === name)
                    ?.value
              );

              //console.log(selectedIds);
              setSelectedAssingess(selectedIds); // Set selected IDs
            }}
            label="Assignees"
            placeholder="Pick value"
            data={uniqueAssignees.map((item) => item.label.name)}
            className="Fill-in-the-Action-Assignees"
          />
        )}

        <div className="flex justify-end py-5">
          {submit ? (
            <Loading />
          ) : (
            <Button
              type="submit"
              variant="filled"
              size="sm"
              radius="md"
              className="mt-auto ms-2 mb-2 bg-primary hover:bg-secondary Fill-in-the-Action-Save"
              // onClick={open}
            >
              Add Target
            </Button>
          )}
        </div>
      </form>
    </Modal>
  );
};

export default ActionsModal;
