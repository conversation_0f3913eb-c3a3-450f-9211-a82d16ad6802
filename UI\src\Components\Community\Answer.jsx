import Files from './Files';

const Answer = ({ Answer, timeAgo }) => {
  let { user, content, createdAt, attachments } = Answer;
  return (
    <div className="p-2 flex items-center gap-4 w-full my-2 hover:bg-[#07838F1A] duration-300">
      {user?.image ? (
        <img src={user?.image} alt={user?.userName || 'User'} className="w-10 h-10 rounded-full" />
      ) : (
        <div className="w-10 h-10 rounded-full cursor-default text-center flex items-center justify-center font-bold text-sm text-primary bg-[#07838F1A]">
          {(user?.userName && user?.userName[0]) || 'U'}
        </div>
      )}
      <div className="flex justify-between flex-col w-full">
        <div className="flex items-center gap-4">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <p className="text-base font-semibold cursor-default .text-primary">{user?.userName || 'User'}</p>
              <p className="text-xs text-gray-600">{timeAgo(createdAt)}</p>
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <p className="text-base font-normal text-gray-700">{content}</p>
          {attachments.filter((e) => e !== null).filter((e) => !e.includes('[object File]')).length > 0 && (
            <div className="my-2">
              <Files
                modal={true}
                attachments={attachments}
                className="px-2 py-1 text-xs cursor-pointer text-black flex items-center gap-1 bg-gray-600/10 rounded-full hover:bg-gray-600/20 duration-300"
                hideSize
                flex
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Answer;
