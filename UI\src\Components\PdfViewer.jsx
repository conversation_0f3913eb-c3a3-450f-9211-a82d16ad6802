import { Worker, Viewer } from '@react-pdf-viewer/core';
import { defaultLayoutPlugin } from '@react-pdf-viewer/default-layout';
import '@react-pdf-viewer/core/lib/styles/index.css';
import '@react-pdf-viewer/default-layout/lib/styles/index.css';

const ViewPDF = ({ pdfUrl }) => {
  const defaultLayoutPluginInstance = defaultLayoutPlugin();

  return (
    <div className="w-[90vw] h-[90vh]">
      {!pdfUrl ? (
        <div className="error-message">
          <p>
            There seems to be an issue displaying the PDF. Please check if you have any browser extensions like Internet Download Manager (IDM) that
            might be blocking the PDF display.
          </p>
        </div>
      ) : (
        <Worker workerUrl="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js">
          <Viewer fileUrl={pdfUrl} defaultScale={1.0} plugins={[defaultLayoutPluginInstance]} />
        </Worker>
      )}
    </div>
  );
};

export default ViewPDF;
