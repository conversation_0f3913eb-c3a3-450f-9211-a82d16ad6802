// src/axiosConfig.js

import axios from "axios";
import Cookies from "js-cookie";

const baseURL = "https://portal-auth-main-staging.azurewebsites.net";

const AuthConfig = axios.create({
 baseURL,
 headers: {
  "Content-Type": "application/json",
  withCredentials: true,
  // Authorization: `Bearer ${Cookies.get("level_user_token")}`,
 },
});

AuthConfig.interceptors.request.use(
 (config) => {
  // Modify config before sending the request
  config.headers["Authorization"] = `Bearer ${Cookies.get("level_user_token")}`;
  return config;
 },
 (error) => {
  // Handle request error
  return Promise.reject(error);
 }
);
AuthConfig.interceptors.response.use(
 (response) => response,
 (error) => {
  if (error.response && error.response.status === 401) {
   const errorMessage = error.response.data.message;
   const currentPath = window.location.pathname;

   if (
    errorMessage === "Token is expired." ||
    (errorMessage === "Token is invalid." && currentPath !== "/login")
   ) {
    console.log("Token is invalid. Redirecting to login...");
    Cookies.remove("level_user_token");
    localStorage.clear();
    window.location.href = "/login"; // Redirect to login page
   }
  }
  return Promise.reject(error);
 }
);

export default AuthConfig;
