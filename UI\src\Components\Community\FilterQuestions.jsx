import { useTranslation } from 'react-i18next';

const FilterQuestions = ({ setFilters, filters }) => {
  const { t } = useTranslation();

  return (
    <span className="flex items-center justify-center overflow-hidden font-medium border rounded-lg text-secondary-400 border-secondary-400 bg-transparent bg-opacity-10 h-max">
      <button
        onClick={() => {
          if (filters.sort.includes('oldest')) setFilters((prev) => ({ ...prev, sort: prev.sort.filter((item) => item !== 'oldest') }));
          if (!filters.sort.includes('newest')) return setFilters((prev) => ({ ...prev, sort: [...prev.sort, 'newest'] }));

          setFilters((prev) => ({ ...prev, sort: prev.sort.filter((item) => item !== 'newest') }));
        }}
        className={`${filters.sort.includes('newest') && 'bg-[#07838F1A]'} px-2 py-2 hover:bg-[#07838F1A] duration-500`}
      >
        {t('Newest')}
      </button>
      <span className="text-gray-300">|</span>
      <button
        onClick={() => {
          if (filters.sort.includes('newest')) setFilters((prev) => ({ ...prev, sort: prev.sort.filter((item) => item !== 'newest') }));
          if (!filters.sort.includes('oldest')) return setFilters((prev) => ({ ...prev, sort: [...prev.sort, 'oldest'] }));

          setFilters((prev) => ({ ...prev, sort: prev.sort.filter((item) => item !== 'oldest') }));
        }}
        className={`px-2 py-2  hover:bg-[#07838F1A] duration-500 ${filters.sort.includes('oldest') && 'bg-[#07838F1A]'} `}
      >
        {t('Oldest')}
      </button>
      <span className="text-gray-300">|</span>
      <button
        onClick={() => {
          if (filters.type.includes('text')) setFilters((prev) => ({ ...prev, type: prev.type.filter((item) => item !== 'text') }));
          if (!filters.type.includes('polls')) return setFilters((prev) => ({ ...prev, type: [...prev.type, 'polls'] }));
          setFilters((prev) => ({ ...prev, type: prev.type.filter((item) => item !== 'polls') }));
        }}
        className={`${filters.type.includes('polls') && 'bg-[#07838F1A]'} px-2 py-2  hover:bg-[#07838F1A] duration-500`}
      >
        {t('Polls')}
      </button>
      <span className="text-gray-300">|</span>
      <button
        onClick={() => {
          if (filters.type.includes('polls')) setFilters((prev) => ({ ...prev, type: prev.type.filter((item) => item !== 'polls') }));
          if (!filters.type.includes('text')) return setFilters((prev) => ({ ...prev, type: [...prev.type, 'text'] }));
          setFilters((prev) => ({ ...prev, type: prev.type.filter((item) => item !== 'text') }));
        }}
        className={`${filters.type.includes('text') && 'bg-[#07838F1A]'} px-2 py-2  hover:bg-[#07838F1A] duration-500`}
      >
        {t('Questions')}
      </button>
    </span>
  );
};

export default FilterQuestions;
