import { useState, useEffect } from "react";
import { Select, TextInput, Group } from "@mantine/core";
import Cookies from "js-cookie";
import { toast } from "react-toastify";

const DATA_SOURCE_OPTIONS = [
  "Industry average",
  "Third-party provided",
  "Estimated/calculated",
  "Direct measurement",
];

const VERIFICATION_OPTIONS = [
  "Not verified",
  "Internally verified",
  "Third-party verified",
];

const METHODOLOGY_OPTIONS = [
  "No methodology",
  "Company-specific methodology",
  "Mixed methodology",
  "Industry-specific methodology",
  "Standard methodology",
];

const QualityAssurance = ({ topicId, quality_assurance }) => {
  const [dataSource, setDataSource] = useState("");
  const [verificationStatus, setVerificationStatus] = useState("");
  const [calculationMethodology, setCalculationMethodology] = useState("");
  const [dataQualityScore, setDataQualityScore] = useState("");

  const baseUrl = "https://issb-report-api-staging.azurewebsites.net";

  useEffect(() => {
    if (quality_assurance) {
      setDataSource(quality_assurance.data_source || "");
      setVerificationStatus(quality_assurance.verification || "");
      setCalculationMethodology(quality_assurance.methodology_type || "");
      setDataQualityScore(
        quality_assurance.quality_label && quality_assurance.quality_score != null
          ? `${quality_assurance.quality_label} (${quality_assurance.quality_score})`
          : "N/A"
      );
    }
  }, [quality_assurance]);

  const updateDataQuality = async (field, value, currentValue) => {
    if (value === currentValue) {
      return;
    }

    const token = Cookies.get("level_user_token");
    if (!token) {
      toast.error("Authorization token is missing");
      return;
    }

    const payload = { [field]: value };
    try {
      const response = await fetch(`${baseUrl}/api/v1/topics/${topicId}/quality-assurance`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(payload),
      });
      if (!response.ok) {
        throw new Error(`Failed to update data quality: ${response.statusText}`);
      }
      const data = await response.json();

      setDataSource(field === "data_source" ? value : (data.topic.quality_assurance.data_source || dataSource));
      setVerificationStatus(field === "verification" ? value : (data.topic.quality_assurance.verification || verificationStatus));
      setCalculationMethodology(field === "methodology_type" ? value : (data.topic.quality_assurance.methodology_type || calculationMethodology));
      
      const newLabel = data.topic.quality_assurance.quality_label || "Unknown";
      const newScore = data.topic.quality_assurance.quality_score != null ? data.topic.quality_assurance.quality_score : "N/A";
      setDataQualityScore(`${newLabel} (${newScore})`);


    } catch (error) {
      console.error("Error updating data quality:", error);
      toast.error(`Failed to update data quality: ${error.message}`);
      if (field === "data_source") setDataSource(currentValue);
      if (field === "verification") setVerificationStatus(currentValue);
      if (field === "methodology_type") setCalculationMethodology(currentValue);
    }
  };

  return (
    <div className="mt-4">
      <h2 className="font-bold mb-2">Quality Assurance</h2>
      <div className="bg-[#F8F8F8] w-full border rounded p-4 md:flex max-md:flex-col justify-between gap-4">
        <Group className="max-md:flex-col w-full max-md:mb-4">
          <Select
            label="Data Source"
            placeholder="Select data source"
            data={DATA_SOURCE_OPTIONS}
            value={dataSource}
            onChange={(value) => {
              setDataSource(value);
              updateDataQuality("data_source", value, quality_assurance?.data_source);
            }}
            className="w-full"
          />
          <Select
            label="Verification Status"
            placeholder="Select verification status"
            data={VERIFICATION_OPTIONS}
            value={verificationStatus}
            onChange={(value) => {
              setVerificationStatus(value);
              updateDataQuality("verification", value, quality_assurance?.verification);
            }}
          className="w-full"
          />
        </Group>
        <Group className="max-md:flex-col w-full">
          <Select
            label="Calculation Methodology"
            placeholder="Select calculation methodology"
            data={METHODOLOGY_OPTIONS}
            value={calculationMethodology}
            onChange={(value) => {
              setCalculationMethodology(value);
              updateDataQuality("methodology_type", value, quality_assurance?.methodology_type);
            }}
          className="w-full"
          />
          <TextInput
            label="Data Quality Score"
            value={dataQualityScore}
            readOnly
            className="w-full"
          />
        </Group>
      </div>
    </div>
  );
};

export default QualityAssurance;