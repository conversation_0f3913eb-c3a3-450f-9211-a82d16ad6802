// src/axiosConfig.js

import axios from "axios";
import Cookies from "js-cookie";

// const baseURL = "https://leveluppportals2api.azurewebsites.net";
const baseURL = "https://leveluppportals2api-staging.azurewebsites.net";

const PublicSupplyChainConfig = axios.create({
  baseURL,
  headers: {
    "Content-Type": "application/json",
    withCredentials: true,
    // Authorization: `Bearer ${Cookies.get("level_user_token")}`,
  },
});
// console.log(sessionStorage.getItem("PublicSupplyChainToken"));
PublicSupplyChainConfig.interceptors.request.use(
  (config) => {
    // Modify config before sending the request
    config.headers["Authorization"] = `${sessionStorage.getItem(
      "PublicSupplyChainToken"
    )}`;
    return config;
  },
  (error) => {
    // Handle request error
    return Promise.reject(error);
  }
);
PublicSupplyChainConfig.interceptors.response.use(
  (response) => response,
  (error) => {
    console.log(error);
    if (error.response && error.response.status === 401) {
      const errorMessage = error.response.data.message;
      const currentPath = window.location.pathname;

      if (errorMessage === "Token is expired." || errorMessage === "Token is invalid."  && currentPath !== "/login") {
        console.log("Token is invalid. Redirecting to login...");
        localStorage.clear();
        // Cookies.remove("level_user_token");
        // window.location.href = "/login"; // Redirect to login page
      }
    }
    return Promise.reject(error);
  }
);

export default PublicSupplyChainConfig;
