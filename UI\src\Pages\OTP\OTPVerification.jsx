import { useEffect, useState, useRef } from "react";
import { useTranslation } from "react-i18next";
import sectionImage9 from "@/assets/images/loginAIbk.png";
import Logo from "@/assets/images/circular-primary-logo.png";
import { useAuth } from "@/Contexts/AuthContext";
import { useNavigate } from "react-router";
import { Button } from "@mantine/core";
import { notifications } from "@mantine/notifications";

const OTPVerification = () => {
  const { t } = useTranslation();
  const { verifyOTP, resendOTP, token, otpId } = useAuth();
  const [loading, setLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const navigate = useNavigate();
  const [otpValues, setOtpValues] = useState(["", "", "", "", ""]);
  const inputRefs = [useRef(), useRef(), useRef(), useRef(), useRef()];

  useEffect(() => {
    // If there's no otpId, redirect to login - user shouldn't be on this page
    if (!otpId) {
      notifications.show({
        message: "Authentication session expired. Please login again.",
        color: "yellow",
      });
      navigate("/login");
      return;
    }
    
    // If user already has a token, redirect to dashboard
    if (token) {
      navigate("/get-started");
      return;
    }
  }, [token, navigate, otpId]);

  // Handle input change for OTP fields
  const handleOtpChange = (index, value) => {
    // Only allow numbers
    if (value && !/^\d+$/.test(value)) return;

    const newOtpValues = [...otpValues];
    newOtpValues[index] = value;
    setOtpValues(newOtpValues);

    // Move to next input if current one is filled
    if (value !== "" && index < 4) {
      inputRefs[index + 1].current.focus();
    }
  };

  // Handle key press for OTP fields
  const handleKeyDown = (e, index) => {
    // Move to previous input when backspace is pressed on an empty input
    if (e.key === "Backspace" && otpValues[index] === "" && index > 0) {
      inputRefs[index - 1].current.focus();
    }
  };

  // Handle OTP submission
  const handleVerifyOTP = async () => {
    const otp = otpValues.join("");
    if (otp.length !== 5) {
      notifications.show({
        message: "Please enter a valid 5-digit OTP code",
        color: "red",
      });
      return;
    }

    setLoading(true);
    try {
      const result = await verifyOTP({ otp: parseInt(otp, 10) });
      
      // If verification failed, redirect back to login
      if (result?.error) {
        notifications.show({
          message: "Invalid verification code. Please try again.",
          color: "red",
        });
        navigate("/login");
      }
      // Successful verification will automatically redirect to /get-started via the verifyOTP function
      
    } catch (error) {
      console.error("OTP verification failed:", error);
      notifications.show({
        message: "Verification failed. Please try again.",
        color: "red",
      });
      navigate("/login");
    } finally {
      setLoading(false);
    }
  };

  // Handle resend OTP
  const handleResendOTP = async () => {
    setResendLoading(true);
    try {
      await resendOTP();
      // Clear OTP fields after resend
      setOtpValues(["", "", "", "", ""]);
      inputRefs[0].current.focus();
    } catch (error) {
      console.error("Failed to resend OTP:", error);
    } finally {
      setResendLoading(false);
    }
  };

  // Handle paste functionality for OTP
  const handlePaste = (e) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text/plain").trim();
    
    if (/^\d{5}$/.test(pastedData)) {
      // If pasted content is exactly 5 digits, distribute it to the inputs
      const digits = pastedData.split("");
      const newOtpValues = [...otpValues];
      
      digits.forEach((digit, index) => {
        if (index < 5) {
          newOtpValues[index] = digit;
        }
      });
      
      setOtpValues(newOtpValues);
      inputRefs[4].current.focus();
    }
  };

  // Submit OTP when user presses Enter
  useEffect(() => {
    function handleKeyPress(e) {
      if (e.key === "Enter" && otpValues.every(val => val !== "")) {
        handleVerifyOTP();
      }
    }

    window.addEventListener("keypress", handleKeyPress);

    return () => {
      window.removeEventListener("keypress", handleKeyPress);
    };
  }, [otpValues]);

  return (
 <section className="flex items-center justify-center min-h-screen px-4 py-8 bg-white relative">
      <div className="absolute inset-0 bg-[linear-gradient(#1C889C_1px,transparent_1px),linear-gradient(90deg,#1C889C_1px,transparent_1px)] bg-[size:100px_100px] opacity-10"></div>
      <div className="w-full max-w-8xl flex flex-col justify-around md:flex-row gap-4 md:gap-6 p-14 overflow-hidden relative z-10">
      {/* Left Section (Image + Text) */}
      <div className="w-full md:w-1/3 basis-full md:basis-2/5 rounded-xl shadow-lg m-4 bg-white p-6 md:p-10 flex flex-col justify-center">
                <div className="flex flex-col h-full">
                  <h1 className="flex items-center gap-2">
                    <img className="w-12 md:w-16" src={Logo} alt="LevelUp ESG Logo" />
                    <span className="text-xl md:text-2xl font-bold text-primary">
                      LevelUp ESG<sup>&reg;</sup>
                    </span>
                  </h1>

            <h2 className="my-4 md:my-6 pb-2 md:pb-4 text-xl md:text-2xl font-medium text-center text-[#525252]">
              {t("Two-Factor Authentication")}
            </h2>
            
            <p className="mb-6 text-gray-600">
              {t("Please enter the verification code sent to your email")}
            </p>
            
            <div className="flex flex-col mb-4">
              <label className="font-bold text-[#272727] mb-3">
                {t("Verification Code")}
              </label>
              
              <div className="flex justify-between gap-2" onPaste={handlePaste}>
                {otpValues.map((digit, index) => (
                  <input
                    key={index}
                    ref={inputRefs[index]}
                    className="p-3 text-center w-12 h-12 md:w-16 md:h-16 text-xl border rounded-md focus:border-primary outline-0"
                    type="text"
                    maxLength={1}
                    value={digit}
                    onChange={(e) => handleOtpChange(index, e.target.value)}
                    onKeyDown={(e) => handleKeyDown(e, index)}
                  />
                ))}
              </div>
            </div>
            
            <div className="flex items-center justify-center gap-3 mt-6">
              <Button
                disabled={loading || otpValues.some(val => val === "")}
                loading={loading}
                onClick={handleVerifyOTP}
                className="w-full text-lg text-white rounded-md bg-primary hover:bg-secondary"
              >
                {t("Verify")}
              </Button>
            </div>
            
            <div className="mt-6 text-center">
              <p className="text-gray-600">
                {t("Didn't receive a code?")}
              </p>
              <button 
                className="mt-2 font-semibold text-primary hover:underline"
                onClick={handleResendOTP}
                disabled={resendLoading}
              >
                {resendLoading ? t("Sending...") : t("Resend Code")}
              </button>
            </div>
            
            <p className="mt-5 text-sm text-center">
              {t("By continuing, you agree to our")}
              <a
                className="pl-1 font-semibold underline underline-offset-2"
                href="https://levelupesg.co/legal/privacy-policy"
                target="_blank"
                rel="noopener noreferrer"
              >
                {t("Privacy Policy")}
              </a>
            </p>
        </div>
      </div>

      {/* Right Section (OTP Form) */}
      <div className="w-full md:w-1/3 basis-full md:basis-2/5 overflow-hidden rounded-xl flex items-center justify-center">
        <img 
            src={sectionImage9} 
            className="w-full h-full object-contain" 
            alt="AI photo" 
            />
      </div>
      </div>
    </section>
  );
};

export default OTPVerification;