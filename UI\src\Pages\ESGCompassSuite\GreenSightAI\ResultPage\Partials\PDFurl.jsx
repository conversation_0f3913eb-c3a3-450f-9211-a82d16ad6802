import { useDisclosure } from "@mantine/hooks";
import { Button, Input, Modal } from "@mantine/core";
import { useState } from "react";
import PDFIframe from "./PDFIframe";
import { FaEye } from "react-icons/fa";
import { IoDownloadOutline, IoShareSocialOutline } from "react-icons/io5";
import { Link } from "react-router-dom";
import HistoryPopUp from "./HistoryPopUp";
import { MdHistory } from "react-icons/md";
import Cookies from "js-cookie";

const PDFurl = ({ pdf_url, report_date }) => {
  const [historyOpened, { open: historyOpen, close: historyClose }] =
    useDisclosure(false);
  const [shareOpened, { open: openShareModal, close: closeShareModal }] =
    useDisclosure(false);
  const [pdfOpened, { open: openPDFModal, close: closePDFModal }] =
    useDisclosure(false);
  const [shareTo, setShareTo] = useState("");

  const [downloadReportLoader, setDownloadReportLoader] = useState(false);

  const handleDownloadPDF = async (PDFLink) => {
    if (!PDFLink) {
      console.error("No report URL available");
      return;
    }
    try {
    // Fetch the PDF as a binary blob
    setDownloadReportLoader(true);
    const response = await fetch(PDFLink, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${Cookies.get("level_user_token")}`,
      },
    });
    if (!response.ok) {
      console.error("Failed to download report");
      setDownloadReportLoader(false);
      return;
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.style.display = "none";
    a.href = url;
    a.download = `Sustain360 .pdf`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    a.remove();
    setDownloadReportLoader(false);
    } catch (error) {
      console.error("Failed to download report", error);
      setDownloadReportLoader(false);
    }
  };
 
  const ShareModal = () => (
    <Modal
      opened={shareOpened}
      onClose={closeShareModal}
      title="Share Report"
      radius={15}
      withCloseButton={false}
      centered
    >
      <div className="text-center">
        <Input
          placeholder="Write email"
          size="lg"
          radius={12}
          color="green"
          value={shareTo}
          onChange={(e) => setShareTo(e.currentTarget.value)}
        />
        <a
          href={`mailto:${shareTo}?subject=Sustainability%20Maturity%20Assessment%20Results&body=Here%20is%20the%20link%20to%20the%20report:%20${pdf_url}`}
          target="_blank"
          className={`${
            shareTo
              ? "bg-[#2b939c] border-primary text-primary"
              : "pointer-events-none bg-gray-400"
          } inline-block mt-4 border-2  mx-3  bg-opacity-10 px-10 py-2 rounded-lg active:scale-95 transition-all`}
        >
          Share
        </a>
      </div>
    </Modal>
  );
  // console.log(report_date);
  return (
    <div className="relative">
      <div className="flex-grow px-2">
        <div className="grid grid-cols-1 gap-4 p-4 pb-0">
          <div className="bg-gray-200/30 backdrop-blur-md hover:scale-[1.01] transition-all shadow-md p-4 rounded-xl">
            <h2 className="mb-4 font-semibold text-2xl">
              Your Sustainability Maturity Assessment Results For Last
              Assessment
            </h2>
            <p>
              Following the analysis of your provided inputs, we are pleased to
              present your assessment outcomes.
            </p>
            <div className="h-fit block xl:flex justify-between mt-4">
              <p className="my-auto text-sm">
                <span className="me-1">Report Date:</span>
                {new Date(report_date).toLocaleDateString("en-CA")}
              </p>
              <div className="grid md:grid-cols-2 xl:grid-cols-4 justify-center items-center gap-2 mt-5 xl:mt-0">
                <Button
                  className={
                    "bg-transparent text-primary border border-primary hover:bg-primary hover:text-white rounded-lg"
                  }
                  size="md"
                  onClick={historyOpen}
                >
                  <MdHistory className="me-1" />
                  Assessment History
                </Button>
                <Button
                  className="bg-transparent text-primary border border-primary hover:bg-primary hover:text-white rounded-lg"
                  size="md"
                  onClick={openPDFModal}
                >
                  <FaEye className="me-1" />
                  Show Report
                </Button>
                <Button
                  className="bg-transparent text-primary border border-primary hover:bg-primary hover:text-white rounded-lg"
                  size="md"
                  onClick={openShareModal}
                >
                  <IoShareSocialOutline className="me-1" />
                  Share
                </Button>{" "}
                <Button
                  disabled={downloadReportLoader}
                  size="md"
                  onClick={() => handleDownloadPDF(pdf_url)}
                  className={`bg-[#2b939c] text-white px-4 py-2 hover:bg-primary justify-center rounded-lg active:scale-95 transition-all flex items-center ${downloadReportLoader ? "cursor-not-allowed" : "cursor-pointer"}`}
                >
                  <div className="flex items-center gap-1">
                  {downloadReportLoader ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  ) : (
                    <IoDownloadOutline className="me-1" />
                  )}
                  <div>Download Report</div>
                  </div>
                </Button>
                <ShareModal />
                <PDFIframe
                  opened={pdfOpened}
                  close={closePDFModal}
                  PDFURL={pdf_url}
                />
                {historyOpened && (
                  <HistoryPopUp
                    opened={historyOpened}
                    close={historyClose}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PDFurl;
