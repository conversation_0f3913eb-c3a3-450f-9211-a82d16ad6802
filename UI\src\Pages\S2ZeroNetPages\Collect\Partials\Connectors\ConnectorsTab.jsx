import { useTranslation } from "react-i18next";
import { useState } from "react";
import { Tabs } from "@mantine/core";
import CreatConnectionTab from "./CreatConnectionTab/CreatConnectionTab";
import ShowConnectors from "./ShowConnectors/ShowConnectors";
import S2Layout from "@/Layout/S2Layout";
import { IoMdHome } from "react-icons/io";
import axios from "axios";
import Cookies from "js-cookie";
import { BsDatabaseFillGear } from "react-icons/bs";
import { RiSparkling2Fill } from "react-icons/ri";
import AIDataDrivenTab from "../../Partials/AIDataDriven/AIDataDrivenTab";
import { AIDataDrivenProvider } from "../../Partials/AIDataDriven/AiDataDrivenContext";
import { DataSyncIcon } from "@/assets/svg/ImageSVG";
import DataScrapping from "@/Pages/DataFoundationSystem/DataCollection/DataScrapping";

export default function ConnectorsTab() {
  const { t } = useTranslation();
  const [active, setActive] = useState("Connectors");
  const [activeTab, setActiveTab] = useState("Create Connection");
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);

  const getTableData = async () => {
    try {
      setLoading(true);
      const { data } = await axios.get(
        "https://connectors0.azurewebsites.net/connector/list",
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("level_user_token")}`,
          },
        }
      );
      setLoading(false);
      setData(data);
    } catch (error) {
      setLoading(false);
      console.error("Error fetching data:", error);
    }
  };
  return (
    <S2Layout
      breadcrumbItems={[
        { title: <IoMdHome size={20} />, href: "/get-started" },
        { title: "Data Collection", href: "#" },
      ]}
      navbarTitle={t("Data Collection")}
    >
      <Tabs defaultValue="Connectors" variant="pills" className="p-10 ">
        <Tabs.List className="grid md:grid-cols-3 justify-center bg-[#FFFFFF] border-2 border-[#E8E7EA] p-6 gap-5 py-3 mb-6 rounded-lg text-primary">
          <Tabs.Tab
            value="Connectors"
            leftSection={<BsDatabaseFillGear size={20} />}
            className={`relative text-lg font-semibold py-3 px-6 transition-all  ${
              active === "Connectors"
                ? "active-tab"
                : " text-[#5A5A5A]  bg-[#FFFFFF] rounded-lg border-[#E8E7EA] border-2"
            }`}
            onClick={() => {
              setActive("Connectors");
            }}
          >
            {t("Connectors")}
          </Tabs.Tab>
          <Tabs.Tab
            value="Data Scrapping"
            leftSection={<DataSyncIcon size={1} color={active === "Data Scrapping" ? "#07838F" : ""} />}
            className={` relative text-lg font-semibold py-3 px-6 transition-all ${
              active === "Data Scrapping"
                ? "active-tab"
                : " text-[#5A5A5A]  bg-[#FFFFFF] rounded-lg border-[#E8E7EA] border-2"
            }`}
            onClick={() => {
              setActive("Data Scrapping");
            }}
          >
            Data Scrapping
          </Tabs.Tab>
          <Tabs.Tab
            value="AI Driven"
            leftSection={<RiSparkling2Fill size={20} />}
            className={`relative text-lg font-semibold py-3 px-6 transition-all ${
              active === "AI Driven"
                ? "active-tab"
                : " text-[#5A5A5A]  bg-[#FFFFFF] rounded-lg border-[#E8E7EA] border-2"
            }`}
            onClick={() => {
              setActive("AI Driven");
            }}
          >
            AI Driven
          </Tabs.Tab>
        </Tabs.List>
        <Tabs.Panel value="Connectors">
          <div>
            <Tabs defaultValue={activeTab} onChange={setActiveTab}>
              <Tabs.List className="grid md:grid-cols-2 justify-center gap-5 py-3 mb-6 rounded-md text-primary">
                <Tabs.Tab
                  value="Create Connection"
                  className={`text-lg ${
                    activeTab === "Create Connection"
                      ? "text-white bg-opacity-100"
                      : "bg-opacity-10"
                  } hover:opacity-70 rounded-lg py-2 font-semibold bg-primary focus:border-b-primary`}
                >
                  {t("Create Connection")}
                </Tabs.Tab>

                <Tabs.Tab
                  value="Show Connections"
                  className={`text-lg ${
                    activeTab === "Show Connections"
                      ? "text-white bg-opacity-100"
                      : "bg-opacity-10"
                  } hover:opacity-70 rounded-lg py-2 font-semibold bg-primary focus:border-b-primary`}
                >
                  {t("Show Connections")}
                </Tabs.Tab>
              </Tabs.List>

              <Tabs.Panel value="Create Connection">
                <CreatConnectionTab getTableData={getTableData} />
              </Tabs.Panel>

              <Tabs.Panel value="Show Connections">
                <ShowConnectors
                  data={data}
                  loading={loading}
                  getTableData={getTableData}
                />
              </Tabs.Panel>
            </Tabs>
          </div>
        </Tabs.Panel>
        <Tabs.Panel value="Data Scrapping">
          <DataScrapping />
        </Tabs.Panel>
        <Tabs.Panel value="AI Driven">
          <AIDataDrivenProvider>
            <AIDataDrivenTab />
          </AIDataDrivenProvider>
        </Tabs.Panel>
      </Tabs>
    </S2Layout>
  );
}
