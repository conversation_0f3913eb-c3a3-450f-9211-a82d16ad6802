import React from 'react'
import { FaArrowDown } from "react-icons/fa6";
import  PropType from 'prop-types';


function CO2({number,numMonth}) {
  return (
    <div className='bg-white py-[1rem] px-[1.5rem] rounded-2xl shadow-2xl w-full  mx-auto h-[80px] mb-5'>
        <div className='flex flex-wrap justify-between items-center'>
            <div className='flex gap-[.5rem] items-center'>
                <span className='font-bold text-[30px]'>{number}</span>
                <p>CO2e</p>
            </div>
            <div>
                <div className='flex items-center gap-[.5rem] text-[#70D162] '>
                    <span className='font-bold text-[30px]  '>{numMonth}</span>
                    <FaArrowDown  className='text-lg'/>
                </div>
                
            </div>
        </div>
    </div>
  )
}

CO2.propTypes = {
  number: PropType.number,
  numMonth: PropType.number
};

export default CO2
