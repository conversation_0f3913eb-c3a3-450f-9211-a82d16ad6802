import { GreenSightFinanceIcon } from "@/assets/icons/InsightsAndReporingIcons";
import { CarbonIcon } from "@/assets/icons/MainSideBarIcons";
import IssbCard from "@/Issb/IssbCard";
import S3Layout from "@/Layout/S3Layout";

const GreenSightView = () => {
  const greenSightMenu = [
    "Intuitive ESG analytics, dashboard and sharable report",
    "AI-powered ESG SWOT analysis",
    "Strategic prioritised action plans and progress tracking",
  ];

  const greenSightFinancialMenu = [
    "Finance sector-specific ESG assessment",
    "AI-powered ESG SWOT analysis",
    "Strategic prioritised action plans",
    "Progress tracking and benchmarking",
  ];
  return (
    <S3Layout
      menus={[]}
      navbarTitle={"GreenSight AI"}
      breadcrumbItems={[
        { title: "Launchpad", href: "/get-started" },
        { title: "GreenSight AI", href: "#" },
      ]}
    >
      <div className="flex flex-col gap-5 w-full min-h-screen px-3">
        <IssbCard
          icon={<CarbonIcon />}
          title="GreenSight"
          description="Cutting-edge proprietary system to assess and enhance your ESG maturity journey:"
          items={greenSightMenu}
          btnString="Start Assessment"
          link="/Insights-reporing/green-sight-ai/green-sight"
        />

        <IssbCard
          icon={<GreenSightFinanceIcon />}
          title="GreenSight for Financial Services"
          description="Specialised system designed for financial institutions to assess and enhance ESG maturity:"
          items={greenSightFinancialMenu}
          btnString="Start Assessment"
          link="/Insights-reporing/green-sight-ai/green-sight-finance"
        />
      </div>

      <br />
      <br />
      <br />
      <br />
    </S3Layout>
  );
};

export default GreenSightView;
