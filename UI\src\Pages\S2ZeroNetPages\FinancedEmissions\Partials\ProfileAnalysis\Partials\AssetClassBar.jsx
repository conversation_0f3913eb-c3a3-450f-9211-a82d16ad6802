import { useEffect, useState } from "react";
import { Bar<PERSON><PERSON> } from "@mantine/charts";
import Cookies from "js-cookie";

const API_URL = 'https://pcaf-api-staging.azurewebsites.net/profile-analysis/financed-emissions';

// Define colors and abbreviations for each asset class
const assetClassConfig = {
  'Listed Equity': { color: '#00C0A9', abbr: 'Listed Equity' },
  'Business Loans': { color: '#9160C1', abbr: 'Business Loans' },
  'Real Estate': { color: '#F4B351', abbr: 'Real Estate' },
  'Project Finance': { color: '#298BED', abbr: 'Project Finance' },
  'Sovereign Bonds': { color: '#0C3360', abbr: 'Sovereign Bonds' },
  'Auto Loans': { color: '#597DA6', abbr: 'Auto Loans' },
};

const AssetClassBar = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [chartData, setChartData] = useState([]);
  const [chartSeries, setChartSeries] = useState([]);
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        // Get the token from cookies
        const token = Cookies.get("level_user_token");
        
        if (!token) {
          throw new Error("Authentication token not found");
        }
        
        const response = await fetch(API_URL, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}`);
        }

        const data = await response.json();
        transformDataForChart(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching portfolio data:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Transform the data for the bar chart
  const transformDataForChart = (data) => {
    if (!data || !data.asset_class_breakdown) return;
    
    // Format data for the chart
    const barData = [{ name: "Emissions" }];
    const seriesData = [];
    
    // Process each asset class
    data.asset_class_breakdown.forEach(item => {
      if (item.emissions_intensity > 0) {
        // Get abbreviation for the asset class
        const abbr = assetClassConfig[item.asset_class]?.abbr || item.asset_class;
        
        // Add data point to the chart data
        barData[0][abbr] = formatEmissionsValue(item.emissions_intensity);
        
        // Add to series configuration
        seriesData.push({
          name: abbr,
          color: assetClassConfig[item.asset_class]?.color || '#CCCCCC'
        });
      }
    });
    
    setChartData(barData);
    setChartSeries(seriesData);
  };

  // Format large emissions values for better display
  const formatEmissionsValue = (value) => {
    // For very large values, scale them down for better visualization
    if (value > 1000000) {
      return value / 1000000; // Convert to millions
    } else if (value > 1000) {
      return value / 1000; // Convert to thousands
    }
    return value;
  };
  
  // Format y-axis labels
  const formatYAxisLabel = (value) => {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toFixed(1);
  };

  return (
    <div className="w-full flex flex-col gap-4 bg-white border rounded-lg p-4">
      <h2 className="text-2xl font-bold">Emissions Intensity by Asset Class</h2>
      {/* <p className="text-gray-600">tCO₂e/$M invested</p> */}
      
      {loading ? (
        <div className="w-full h-96 flex justify-center items-center">
          <p>Loading chart data...</p>
        </div>
      ) : error ? (
        <div className="w-full h-96 flex justify-center items-center text-red-500">
          <p>Error: {error}</p>
        </div>
      ) : chartData.length === 0 || chartSeries.length === 0 ? (
        <div className="w-full h-96 flex justify-center items-center">
          <p>No emissions intensity data available</p>
        </div>
      ) : (
        <div className="w-full h-96 flex justify-center items-center bg-white rounded-lg">
          <BarChart
            h={300}
            data={chartData}
            dataKey="name"
            withLegend
            yAxisProps={{ tickFormatter: formatYAxisLabel }}
            tooltipProps={{
              formatter: (value) => [`${value.toLocaleString()} tCO₂e/$M`, 'Emissions Intensity'],
            }}
            series={chartSeries}
          />
        </div>
      )}
      
      {/* Optional explanation for very large values */}
      {/* {chartData.length > 0 && (
        <div className="text-sm text-gray-500 mt-2">
          Note: Values are shown in their original units (tCO₂e/$M invested). 
          Larger values may be displayed in thousands (K) or millions (M) for readability.
        </div>
      )} */}
    </div>
  );
};

export default AssetClassBar;