import { useEffect, useState } from 'react';
import { PieChart } from '@mantine/charts';
import Cookies from "js-cookie";

const API_URL = 'https://pcaf-api-staging.azurewebsites.net/profile-analysis/financed-emissions';

// Define colors for each asset class
const assetClassColors = {
  'Listed Equity': '#00C0A9',
  'Business Loans': '#9160C1',
  'Real Estate': '#F4B351',
  'Project Finance': '#298BED',
  'Sovereign Bonds': '#0C3360',
  'Auto Loans': '#597DA6',
};

const AssetClassPie = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [chartData, setChartData] = useState([]);
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        // Get the token from cookies
        const token = Cookies.get("level_user_token");
        
        if (!token) {
          throw new Error("Authentication token not found");
        }
        
        const response = await fetch(API_URL, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}`);
        }

        const data = await response.json();
        transformDataForChart(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching portfolio data:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Transform the data for the pie chart
  const transformDataForChart = (data) => {
    if (!data || !data.asset_class_breakdown) return;
    
    // Get total financed emissions
    const totalEmissions = data.total_financed_emissions || 
      data.asset_class_breakdown.reduce((sum, item) => sum + (item.financed_emissions || 0), 0);
    
    // Calculate percentage for each asset class and prepare data for chart
    const formattedData = data.asset_class_breakdown
      .filter(item => item.financed_emissions > 0) // Only include items with emissions
      .map(item => {
        // Calculate percentage of total emissions
        const percentage = (item.financed_emissions / totalEmissions) * 100;
        
        return {
          name: item.asset_class,
          value: parseFloat(percentage.toFixed(2)), // Rounded percentage
          color: assetClassColors[item.asset_class] || '#CCCCCC', // Use default color if asset class not found
          rawValue: item.financed_emissions // Store the raw value for reference
        };
      });
    
    setChartData(formattedData);
  };

  // Format percentage for display
  const formatPercentage = (value) => {
    return `${value}%`;
  };

  return (
    <div className="flex flex-col w-full gap-4 bg-white border rounded-lg p-4">
      <h2 className="text-2xl font-bold">Emissions by Asset Class</h2>
      
      {loading ? (
        <div className="flex w-full h-96 items-center justify-center">
          <p>Loading chart data...</p>
        </div>
      ) : error ? (
        <div className="flex w-full h-96 items-center justify-center text-red-500">
          <p>Error: {error}</p>
        </div>
      ) : chartData.length === 0 ? (
        <div className="flex w-full h-96 items-center justify-center">
          <p>No emissions data available</p>
        </div>
      ) : (
        <div className="flex flex-col md:flex-row w-full h-auto md:h-96 bg-white rounded-lg gap-8 p-6">
          <div className="w-full md:w-1/2 flex justify-center items-center">
            <PieChart
              withLabels={false}
              size={280}
              data={chartData}
              valueFormatter={formatPercentage}
            />
          </div>

          <div className="w-full md:w-1/2 flex flex-col justify-center items-center">
            <h3 className="font-semibold mb-4 text-lg">Asset Class Breakdown</h3>
            <ul className="grid grid-cols-1 gap-4">
              {chartData.map((item) => (
                <li key={item.name} className="flex items-center gap-3">
                  <span
                    className="w-4 h-4 inline-block rounded-sm"
                    style={{ backgroundColor: item.color }}
                  ></span>
                  <span className="font-medium flex-grow">{item.name}</span>
                  <span className="font-bold">{item.value}%</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default AssetClassPie;