import ApiProfile from "@/Api/apiProfileConfig";
import Loading from "@/Components/Loading";
import { Button, Select, Tooltip } from "@mantine/core";
import { DateInput } from "@mantine/dates";
import { useForm } from "@mantine/form";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import { IoIosArrowDown } from "react-icons/io";
import CompanyAdminLogsTable from "./Partials/CompanyAdminLogsTable";
import { useLocation, useNavigate } from "react-router";
import { useAuth } from "@/Contexts/AuthContext";
import { useTranslation } from "react-i18next";
import MainLayout from "@/Layout/MainLayout";
import useSideBarRoute from "@/hooks/useSideBarRoute";

export default function CompanyLogs() {
  const { getStartMenu } = useSideBarRoute();
  const [loading, setLoading] = useState();
  const [filteredData, setFilteredData] = useState();
  const [originalLogsData, setOriginalLogsData] = useState([]);
  const [logsData, setLogsData] = useState([]);
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { user } = useAuth();
  const [data, setData] = useState({
    Action_Type: "",
    UserName: "",
    date: "",
  });
  const form = useForm({
    mode: "uncontrolled",
    initialValues: {
      Action_Type: "",
      UserName: "",
      date: "",
    },
  });
  const add_new_filter = (value) => {
    console.log(value);
    const { Action_Type, UserName, date } = value;

    const filteredData = originalLogsData.filter((item) => {
      const matchesActionType = !Action_Type || item.actionType === Action_Type;

      const matchesUserName =
        !UserName || item.actionMessage.includes(`Company admin ${UserName}`);

      const matchesDate = !date || item.createdAt.includes(date);

      return matchesActionType && matchesUserName && matchesDate;
    });

    console.log(filteredData);

    setLogsData(filteredData);
  };
  const isAnyFieldFilled =
    data?.Action_Type?.trim() !== "" ||
    data?.UserName?.trim() !== "" ||
    (data?.date && dayjs(data?.date).format("YYYY-MM-DD")?.trim() !== "");
  // console.log(form.values);
  const getLogsCompanyData = async () => {
    try {
      const { data } = await ApiProfile.get(
        "/company_admin/get_company_admin_logs"
      );
      setLogsData(data);
      setOriginalLogsData(data);
      console.log(data);
    } catch (error) {
      console.log(error);
    }
  };
  useEffect(() => {
    getLogsCompanyData();
  }, []);
  useEffect(() => {
    form.setValues({
      Action_Type: data?.Action_Type,
      UserName: data?.UserName,
      date: data?.date && dayjs(data?.date).format("YYYY-MM-DD"),
    });
  }, [data]);
  return (
    <>
      <MainLayout menus={getStartMenu} navbarTitle={t("Configurations")}>
        <div className="w-full p-5 text-center bg-white rounded-lg">
          <h1 className="text-3xl font-bold text-primary">
            {t("Admin Company Logs")}
          </h1>
        </div>
        <div className="justify-around  p-5 mt-5 text-center rounded-lg xl:flex">
          {user?.userRole === "Admin" && (
            <Button
              className={`px-16 hover:bg-primary hover:text-white  ${
                pathname.includes("/Configurations/CompanyUserManage")
                  ? "bg-primary text-white"
                  : "bg-[#07838F33] border border-primary text-primary"
              }`}
              size="lg"
              onClick={() =>
                navigate(
                  "/Settings/CompanyProfile/Configurations/CompanyUserManage"
                )
              }
            >
              {t("Manage Users")}
            </Button>
          )}
          {user?.userRole === "Admin" && (
            <Button
              className={`px-16 hover:bg-primary hover:text-white  block mx-auto xl:mx-0 mt-5 xl:mt-0  ${
                pathname.includes("/Configurations/CompanyLogs")
                  ? "bg-primary text-white"
                  : "bg-[#07838F33] border border-primary text-primary"
              }`}
              size="lg"
              onClick={() =>
                navigate("/Settings/CompanyProfile/Configurations/CompanyLogs")
              }
            >
              {t("Company Logs")}
            </Button>
          )}
          {user?.userRole === "Admin" && (
            <Button
              className={`hover:bg-primary hover:text-white block mx-auto xl:mx-0 mt-5 xl:mt-0 ${
                pathname.includes("/Configurations/DepartmentsandProjects")
                  ? "bg-primary text-white"
                  : "bg-[#07838F33] border border-primary text-primary"
              }`}
              size="lg"
              onClick={() =>
                navigate("/Settings/CompanyProfile/Configurations/DepartmentsandProjects")
              }
            >
              {t("Departments and Projects")}
            </Button>
            )}
          <Button
            className={`px-24 hover:bg-primary hover:text-white block mx-auto xl:mx-0 mt-5 xl:mt-0 ${
              pathname.includes("/Configurations/Assets")
                ? "bg-primary text-white"
                : "bg-[#07838F33] border border-primary text-primary"
            }`}
            size="lg"
            onClick={() =>
              navigate("/Settings/CompanyProfile/Configurations/Assets")
            }
          >
            {t("Assets")}
          </Button>
          <Button
            className={` hover:bg-primary hover:text-white block mx-auto xl:mx-0 mt-5 xl:mt-0 ${
              pathname.includes("/Configurations/CustomFactor")
                ? "bg-primary text-white"
                : "bg-[#07838F33] border border-primary text-primary"
            }`}
            size="lg"
            onClick={() =>
              navigate("/Settings/CompanyProfile/Configurations/CustomFactor")
            }
          >
            {t("Emission Factor Selection")}
          </Button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-5">
          <Select
            // {...form.getInputProps("Action_Type")}
            // key={form.key("Action_Type")}
            value={data.Action_Type}
            placeholder="Select Company Name ..."
            label={"Action Type"}
            rightSection={<IoIosArrowDown />}
            data={[
              "Enable User",
              "Disable User",
              "Update User Information",
              "Delete User",
            ]}
            onChange={(value) =>
              setData((prev) => ({ ...prev, Action_Type: value }))
            }
          />
          <Select
            // {...form.getInputProps("UserName")}
            // key={form.key("UserName")}
            type="text"
            value={data.UserName}
            placeholder="Select User Name ..."
            label={"Admin Name"}
            data={["Hady", "Abdelrahman Mamdouh", "Mahmoud Youssef"]}
            rightSection={<IoIosArrowDown />}
            onChange={(value) =>
              setData((prev) => ({ ...prev, UserName: value }))
            }
          />
          <DateInput
            clearable
            valueFormat="YYYY-MM-DD"
            placeholder="Select Date ..."
            label={"Date"}
            onChange={(value) => {
              console.log(value);

              setData((prev) => ({ ...prev, date: value }));
            }}
          />

          <Tooltip
            multiline
            w={220}
            radius={"md"}
            withArrow
            transitionProps={{ duration: 200 }}
            className={`${isAnyFieldFilled ? "hidden" : ""}`}
            label={
              !isAnyFieldFilled && (
                <span className="capitalize flex justify-center">
                  please Select at least on input
                </span>
              )
            }
          >
            <Button
              className="bg-primary hover:bg-primary mt-5"
              onClick={!loading && form.onSubmit(add_new_filter)}
              disabled={!isAnyFieldFilled || loading}
            >
              {loading ? <Loading /> : "Submit"}
            </Button>
          </Tooltip>
        </div>
        <CompanyAdminLogsTable data={logsData} />
      </MainLayout>
    </>
  );
}
