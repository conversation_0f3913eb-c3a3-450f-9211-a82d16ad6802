import ApiS2 from "@/Api/apiS2Config";
import Loading from "@/Components/Loading";
import { Button, FileInput, Input, NumberInput } from "@mantine/core";
import { showNotification } from "@mantine/notifications";
import { useState } from "react";
import { MdOutlineFileDownload } from "react-icons/md";
import { TbHandClick } from "react-icons/tb";

import HandleSpecificInput from "./HandleSpecificInput";
import { BiTrash } from "react-icons/bi";
import axios from "axios";
import Cookies from "js-cookie";

const CustomFactorInputs = ({
  selctedEmissionId,
  assetType,
  fetchAgain,
  assetNamesIds,
  uniqueActivites,
  uniqueUom,
  uniqueEFactor,
  uniqueActivitesList,
  uniqueEfactorList,
  uniqueUomList,
}) => {
  const [loading, setLoading] = useState(false);
  const msg = (txt, color = "green") => {
    showNotification({ message: txt, color: color });
  };

  const [activitiesList, setActivitiesList] = useState([{ "": "" }]);
  const [eFactorList, setEFactorList] = useState([{ "": "" }]);
  const [uomList, setUomList] = useState([{ "": "" }]);
  const [emissionValueByYear, setEmissionValueByYear] = useState([
    {
      year: 0,
      emission_value: 0,
    },
  ]);

  const [emissionIntensity, setEmissionIntensity] = useState(null);

  // source type
  const [sourceType, setsourceType] = useState("");

  // source file
  const [sourceFile, setsourceFile] = useState("");
  // console.log("🚀 ~ sourceFile:", sourceFile)

  // text source
  const [sourceText, setsourceText] = useState("");
  // console.log("🚀 ~ sourceText:", sourceText)

  const handleEmission = (value) => {
    setEmissionIntensity(value);
  };

  const handleActivityList = (key, idx, mainList) => {
    let mainCateg = [];
    let setFunction;

    // Determine which list to update
    switch (mainList) {
      case "activity":
        mainCateg = activitiesList;
        setFunction = setActivitiesList;
        break;
      case "efactor":
        mainCateg = eFactorList;
        setFunction = setEFactorList;
        break;
      case "uom":
        mainCateg = uomList;
        setFunction = setUomList;
        break;
      default:
        return; // Exit if invalid list type
    }

    const currentObj = mainCateg.map((item, i) => {
      if (idx === i) {
        const oldKey = Object.keys(item)[0];
        const oldValue = item[oldKey];
        return { [key]: oldValue };
      } else {
        return item;
      }
    });

    setFunction(currentObj);
  };

  // Function to handle value changes
  const handleValueChange = (value, idx, mainList) => {
    let mainCateg = [];
    let setFunction;

    // Determine which list to update
    switch (mainList) {
      case "activity":
        mainCateg = activitiesList;
        setFunction = setActivitiesList;
        break;
      case "efactor":
        mainCateg = eFactorList;
        setFunction = setEFactorList;
        break;
      case "uom":
        mainCateg = uomList;
        setFunction = setUomList;
        break;
      default:
        return; // Exit if invalid list type
    }

    const currentObj = mainCateg.map((item, i) => {
      if (idx === i) {
        const currentKey = Object.keys(item)[0];
        return { [currentKey]: value };
      } else {
        return item;
      }
    });

    setFunction(currentObj);
  };

  // Function to add new item to a list
  const handleAddItem = (mainList) => {
    switch (mainList) {
      case "activity":
        setActivitiesList([...activitiesList, { "": "" }]);
        break;
      case "efactor":
        setEFactorList([...eFactorList, { "": "" }]);
        break;
      case "uom":
        setUomList([...uomList, { "": "" }]);
        break;
      case "emission":
        setEmissionValueByYear([
          ...emissionValueByYear,
          { year: 0, emission_value: 0 },
        ]);
        break;
    }
  };

  // Function to remove item from a list
  const handleRemoveItem = (idx, mainList) => {
    switch (mainList) {
      case "activity":
        setActivitiesList(activitiesList.filter((_, i) => i !== idx));
        break;
      case "efactor":
        setEFactorList(eFactorList.filter((_, i) => i !== idx));
        break;
      case "uom":
        setUomList(uomList.filter((_, i) => i !== idx));
        break;
      case "emission":
        setEmissionValueByYear(emissionValueByYear.filter((_, i) => i !== idx));
        break;
    }
  };

  const handleFileInputChange = (file) => {
    setsourceFile(file);
  };

  const sendData = async () => {
    // if (emissionIntensity == null) {
    //   msg("Emission Factor is required", "red");
    //   return;
    // }

    if (emissionValueByYear.length === 0 || emissionValueByYear.every(item => item.year === 0 && item.emission_value === 0)) {
      msg("Emission Value by Year is required", "red");
      return;
    }

    // QXoetDezcvROfBFTCAAtbWlDvUMF5jqk == >key
    // cmp1-folder8
    // B2B collection post man


    // ++++++++++++++++++ file
    let sourceObj = [];
    if (sourceFile !== "") {
      setLoading(true);
      const h = {
        headers: {
          Authorization: `Bearer ${Cookies.get("level_user_token")}`,
          "Content-Type": "multipart/octet-stream",
          'x-api-key': 'QXoetDezcvROfBFTCAAtbWlDvUMF5jqk',
          'x-tags':'',
          'x-filename': sourceFile.name,
          'x-folder': 'cmp1-folder8', // This is the folder where the file will be uploaded,
          withCredentials: false,
        },
      };
      const formData = new FormData();
      formData.append("source_file", sourceFile);
      try {
        const data  = await axios.post(
          "https://docvault-staging.azurewebsites.net/api/v1/files/upload",
          formData,
          h
        );
        sourceObj = [{name: data.data.data.id}]
        msg(`File Uploaded Successfully`);
      } catch (er) {
        console.log("🚀 ~ sendData ~ er:", er)
        // msg(`File Error: ${er.response.data.message}`, "red");
        setLoading(false);
      }
      setLoading(false);

    }
    // ++++++++++++++++++ file 



    const activityObject = activitiesList.reduce((acc, curr) => {
      const [key, value] = Object.entries(curr)[0];
      if (key) {
        // Only add if key is not empty
        acc[key] = value;
      }
      return acc;
    }, {});

    const eFactorObject = eFactorList.reduce((acc, curr) => {
      const [key, value] = Object.entries(curr)[0];
      if (key) {
        // Only add if key is not empty
        acc[key] = value;
      }
      return acc;
    }, {});

    const uomObject = uomList.reduce((acc, curr) => {
      const [key, value] = Object.entries(curr)[0];
      if (key) {
        // Only add if key is not empty
        acc[key] = value;
      }
      return acc;
    }, {});

    const sourceType = sourceFile == "" ? "text" : "url";
    const source = sourceFile == "" ? sourceText : sourceObj;

    const objectData = {
      activity: activityObject,
      eFactors: eFactorObject,
      uom: uomObject,
      specificFactor: true,
      companyAssetIds: assetNamesIds,
      emissionSourceId: selctedEmissionId,
      source: source,// [{name: 'fileid'}] == data.id
      source_type: sourceType,
      emissionValueByYear: emissionValueByYear,
    };

    setLoading(true);
    try {
      const { data } = await ApiS2.post("/admin/create-custom-factors", [
        objectData,
      ]);
      setEmissionIntensity(null);
      setsourceFile("");
      setsourceText("");
      setLoading(false);
      fetchAgain();
      msg(data.message);
    } catch ({ response }) {
      setLoading(false);

      msg(response.data.message, "red");

      response?.data?.created_factors?.map((item) => {
        msg(` ${item.message}`, "red");
      });
      response?.data?.validation_errors?.map((item, idx) => {
        msg(` ${item.message}`, "red");
      });
      response?.data?.existing_factors?.map((item, idx) => {
        msg(` ${item.message}`, "red");
      });
    }
    setLoading(false);
  };

  return (
    <>
      <div className="mt-5 gird lg:grid-cols-1 gap-9">
        <div className="grid lg:grid-cols-1 gap-6">
          {/* Activities Section */}
          <div className="shadow-sm rounded-2xl border-2 relative ">
            <h3 className="text-primary font-medium p-3 border-b-2 mb-2">
              Activities
            </h3>

            <Button
              className="bg-green-600 p-0 mx-3 px-1"
              onClick={() => handleAddItem("activity")}
            >
              + Add Activity
            </Button>

            {activitiesList.map((ac, idx) => (
              <HandleSpecificInput
                target="activity"
                key={idx}
                idx={idx}
                object={ac}
                handleActivityList={handleActivityList}
                handleRemoveItem={handleRemoveItem}
                handleValueChange={handleValueChange}
                uniqueKeysList={uniqueActivites}
                uniqueValueList={uniqueActivitesList}
              />
            ))}
          </div>

          {/* E-Factor Section */}
          <div className="shadow-sm rounded-2xl border-2 relative ">
            <h3 className="text-primary font-medium p-3 border-b-2 mb-2">
              E-Factors
            </h3>
            <Button
              className="bg-green-600 p-0 mx-3 px-1"
              onClick={() => handleAddItem("efactor")}
            >
              + Add Efactor
            </Button>
            {eFactorList.map((efactor, idx) => (
              <HandleSpecificInput
                target="efactor"
                key={idx}
                idx={idx}
                object={efactor}
                handleActivityList={handleActivityList}
                handleRemoveItem={handleRemoveItem}
                handleValueChange={handleValueChange}
                uniqueKeysList={uniqueEFactor}
                uniqueValueList={uniqueEfactorList}
              />
            ))}
          </div>

          {/* UOM Section */}
          <div className="shadow-sm rounded-2xl border-2 relative ">
            <h3 className="text-primary font-medium p-3 border-b-2 mb-2">
              UOM
            </h3>
            <Button
              className="bg-green-600 p-0 mx-3 px-1"
              onClick={() => handleAddItem("uom")}
            >
              + Add UOM
            </Button>
            {uomList.map((uom, idx) => (
              <HandleSpecificInput
                target="uom"
                key={idx}
                idx={idx}
                object={uom}
                handleActivityList={handleActivityList}
                handleRemoveItem={handleRemoveItem}
                handleValueChange={handleValueChange}
                uniqueKeysList={uniqueUom}
                uniqueValueList={uniqueUomList}
              />
            ))}
          </div>

          <div className="shadow-sm rounded-2xl border-2 relative ">
            <h3 className="text-primary font-medium p-3 border-b-2 mb-2">
              Emission Value by Year
            </h3>
            <Button
              className="bg-green-600 p-0 mx-3 px-1"
              onClick={() => handleAddItem("emission")}
            >
              + Add Emission Value
            </Button>

            {emissionValueByYear.map((item, i) => (
              <div
                className="flex items-end w-fit gap-4 border-b-2 pb-2 px-3"
                key={i}
              >
                <Input.Wrapper label="Year">
                  <NumberInput
                    type="number"
                    value={item.year}
                    onChange={(value) => {
                      const newEmissionValues = [...emissionValueByYear];
                      newEmissionValues[i] = {
                        ...newEmissionValues[i],
                        year: value,
                      };
                      setEmissionValueByYear(newEmissionValues);
                    }}
                  />
                </Input.Wrapper>
                <Input.Wrapper label="Emission Value">
                  <NumberInput
                    value={item.emission_value}
                    onChange={(value) => {
                      const newEmissionValues = [...emissionValueByYear];
                      newEmissionValues[i] = {
                        ...newEmissionValues[i],
                        emission_value: value,
                      };
                      setEmissionValueByYear(newEmissionValues);
                    }}
                  />
                </Input.Wrapper>
                <Button 
                  className="bg-red-500/60 lg:text-sm text-xs hover:bg-red-400 py-0 px-2"
                  onClick={() => handleRemoveItem(i, "emission")}
                >
                  <BiTrash />
                </Button>
              </div>
            ))}
          </div>

          <div className="flex flex-col justify-between shadow-sm rounded-2xl border-2 relative p-3">
            <div>
              <label
                className="text-primary font-bold"
                htmlFor="emissionIntensity"
              >
                Emission Factor
              </label>
              <NumberInput
                id="emissionIntensity"
                disabled={assetType == "" ? true : false}
                value={emissionIntensity}
                onChange={(e) => handleEmission(e)}
                placeholder="Enter Number Of Emission Factor ..."
                rightSection={" "}
              />
            </div>

            <div className="">
              <Button
                className="bg-primary p-1 mb-2 mt-4"
                onClick={() => {
                  if (sourceType == "file") {
                    setsourceType("");
                  } else {
                    setsourceType("file");
                  }
                }}
              >
                <TbHandClick />
                {sourceType == "file"
                  ? "Enter Source Text"
                  : "Upload Source File"}
              </Button>

              {sourceType == "file" ? (
                <>
                  <label className="block" htmlFor="file">
                    Optional
                  </label>
                  <FileInput
                    id="file"
                    classNames={{ root: "w-fit", input: "px-12" }}
                    leftSection={<MdOutlineFileDownload className="w-5 h-5" />}
                    placeholder="Upload file"
                    className="w-full bg-[#F2F2F2] mb-3"
                    radius="md"
                    leftSectionPointerEvents="none"
                    onChange={(file) => handleFileInputChange(file)}
                  />
                </>
              ) : (
                <Input.Wrapper label="Source (optional)">
                  <Input
                    onChange={(e) => setsourceText(e.target.value)}
                    placeholder="Enter Source text ..."
                  />
                </Input.Wrapper>
              )}
            </div>
          </div>
        </div>
      </div>

      <Button
        disabled={loading || assetType == ""}
        className={`mt-20 ${
          loading || assetType == ""
            ? `bg-secondary-primary-lite cursor-not-allowed`
            : `bg-primary`
        }`}
        onClick={sendData}
      >
        Submit {loading && <Loading />}
      </Button>
    </>
  );
};

export default CustomFactorInputs;
