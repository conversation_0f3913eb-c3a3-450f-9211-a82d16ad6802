import React from 'react'


function BtnSummary({ TotalNum, AnsweredNum, ReviewNum}) {
  return (
    <>
    <div  className= "bg-white p-[1rem] rounded-lg shadow-2xl mb-3 mt-2">
      <button className= "font-bold text-[15px]">Total Questions: {TotalNum}</button>
    </div>
    <div  className= "bg-white p-[1rem] rounded-lg shadow-2xl mb-3 mt-2">
      <button className= "font-bold text-[15px]">Answered Questions: {AnsweredNum}</button>
    </div>
    <div  className= "bg-[#2A939C] text-white  p-[1rem] rounded-lg shadow-2xl mb-3 mt-2">
      <button className= "font-bold text-[15px]">Mark for Review: {ReviewNum}</button>
    </div>
    </>
  )
}


export default BtnSummary;
