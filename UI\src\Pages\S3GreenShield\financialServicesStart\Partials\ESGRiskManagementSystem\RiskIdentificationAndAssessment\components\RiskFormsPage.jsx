import { useEffect, useState } from "react";

import notes from "../../../../../../../../public/assets/Images/risk-identification-icons/note-2.png";
import impact from "../../../../../../../../public/assets/Images/risk-identification-icons/impact-icon.png";
import money from "../../../../../../../../public/assets/Images/risk-identification-icons/moneys.png";
import risk from "../../../../../../../../public/assets/Images/risk-identification-icons/danger.png";

import ImpactAssessmentForm from "./ImpactAssessmentForm";
import FinancialImpactForm from "./FinancialImpactForm";
import RiskAssessmentForm from "./RiskAssessmentForm";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import S3Layout from "@/Layout/S3Layout";
import NotesForm from "./NotesForm";
import ApiS3 from "@/Api/apiS3";
import Cookies from "js-cookie";
import axios from "axios";
import {
  ESGDangerIcon,
  ESGFinancialSolidIcon,
  ESGImpactSolidIcon,
  ESGNotesSolidIcon,
  ESGRiskSolidIcon,
  ESGTaskIcon,
  ESGShieldIcon,
  ESGStatusUpIcon,
  ESGClipboardIcon,
  ESGShieldCheckIcon,
} from "@/assets/icons";
import { showNotification } from "@mantine/notifications";
import SelectFormBtn from "./SelectFormBtn";
import { Link } from "react-router-dom";

const RiskFormsPage = () => {
  const { ESGRiskManagementMenu } = useSideBarRoute();

  const [activeTab, setActiveTab] = useState(2);
  const data = [
    {
      id: 1,
      name: "Universe and Governance",
      icon: <ESGDangerIcon active={activeTab === 1} />,
      path: "/green-shield/financial/ESG-risk-management/main/systems/1",
    },
    {
      id: 2,
      name: "IRO Assessment",
      icon: <ESGTaskIcon active={activeTab === 2} />,
      path: "/green-shield/financial/ESG-risk-management/main/systems/2",
    },
    {
      id: 3,
      name: "Mitigation Strategies",
      icon: <ESGShieldIcon active={activeTab === 3} />,
      path: "/green-shield/financial/ESG-risk-management/main/systems/3",
    },
    {
      id: 4,
      name: "Analytics",
      icon: <ESGStatusUpIcon active={activeTab === 4} />,
      path: "/green-shield/financial/ESG-risk-management/main/systems/4",
    },
    {
      id: 5,
      name: "Audit",
      icon: <ESGClipboardIcon active={activeTab === 5} />,
      path: "/green-shield/financial/ESG-risk-management/main/systems/5",
    },
    {
      id: 6,
      name: "Anti-Greenwashing",
      icon: <ESGShieldCheckIcon active={activeTab === 6} />,
      path: "/green-shield/financial/ESG-risk-management/main/systems/6",
    },
  ];
  const [Buttons, setButtons] = useState([
    {
      title: "Risk Identification",
      imgSrc: risk,
      sIcon: <ESGRiskSolidIcon />,
      progress: 0,
      id: 1,
    },
    {
      title: "Financial Impact",
      imgSrc: money,
      sIcon: <ESGFinancialSolidIcon />,
      progress: 0,
      id: 2,
    },
    {
      title: "ESG Impact Assessment",
      imgSrc: impact,
      sIcon: <ESGImpactSolidIcon />,
      progress: 0,
      id: 3,
    },
    {
      title: "Additional Information",
      imgSrc: notes,
      sIcon: <ESGNotesSolidIcon />,
      progress: 0,
      id: 4,
    },
  ]);
  const msg = (txt, color = "green") => {
    showNotification({ message: txt, color: color });
  };

  const [loading, setloading] = useState();

  const [selected, setSelected] = useState(1);

  // impact ai
  const [isImpactAiDone, setIsImpactAiDone] = useState(false);

  // financil ai
  const [isExposureAiDone, setIsExposureAiDone] = useState(false);
  const [isOpportunityAiDone, setIsOpportunityAiDone] = useState(false);

  // risk ai
  const [isRiskAiDone, setIsRiskAiDone] = useState(false);

  const [riskData, setRiskData] = useState({
    event: "",
    kri: "",
    threshold: "",
    thresholdLevel: "",
    frequency: "",
    riskOwner: "",
    division: "",
    likelihoodId: "",
    impactSeverity: "",
    consequences: "",
    inherentRisk: "",
    controlEffectiveness: "",
    residualRisk: "",
    riskResponse: "",
    trend: "",
  });

  const [financialData, setFinancialData] = useState({
    financialImpactType: "",
    financialExposure: "",
    financialImpactTimeframe: "",
    affectedStakeholders: "",
    reputationalImpact: "",
    financialExposureDescription: "",
    opportunityDescription: "",
    category: "",
    value: "",
  });

  const [impactData, setImpactData] = useState({
    esgImpactDomain: "",
    valueChainScope: "",
    impactDescription: "",
    rootCaseCategory: "",
    impactType: "",
    impactOnSDG: "",
    exposureLevel: "",
    actionStatus: "",
    progressTracking: 0,
  });

  const [notesData, setNotesData] = useState({
    attachment: "",
    alignsWithRiskAppetite: "",
    preparedBy: "",
    Reviewer: "",
    dateRecorded: "",
    comments: "",
  });

  const [dateValue, setDateValue] = useState("");

  const [likilyHoodData, setLikilyHoodData] = useState([]);
  const [impactSeverityData, setImpactSeverityData] = useState([]);
  // const[reputationalImpactData, setReputationalImpactData] = useState([]);

  const [allLikelyHoodData, setAllLikelyHoodData] = useState([]);
  const [allImpactSeverityData, setAllImpactSeverityData] = useState([]);
  // const [allReputationalImpactData, setAllReputationalImpactData] = useState([]);

  // async function
  const getData = async () => {
    const token = Cookies.get("level_user_token");
    const res = await axios.get(
      "https://portals3-h3bjdkhtbghye2aa.uksouth-01.azurewebsites.net/master-data/risk-identification-and-assessment",
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        withCredentials: true,
      }
    );
    setAllLikelyHoodData(res.data.likelihoods);
    setAllImpactSeverityData(res.data.impacts);
    // setAllReputationalImpactData(res.data.impact);
    const likilyData = res.data.likelihoods.map((ob) => {
      // value = ob._id - label = ob.name
      return { value: ob._id, label: ob.name };
    });
    const impactData = res.data.impacts.map((ob) => {
      // value = ob._id - label = ob.name
      return { value: ob._id, label: ob.name };
    });
    // const reputationalImpactData = res.data.impact.map((ob) => {
    //   // value = ob._id - label = ob.name
    //   return { value: ob._id, label: ob.name };
    // });
    setLikilyHoodData(likilyData);
    setImpactSeverityData(impactData);
    // setReputationalImpactData(reputationalImpactData);
  };

  useEffect(() => {
    getData();
  }, []);

  // console.log(financialData);

  const addRisk = async () => {
    if (riskData.likelihoodId === "") {
      msg("Please select likelihood", "red");
      return;
    }

    // Impact Severity
    if (impactData.impactSeverity === "") {
      msg("Please select impact severity", "red");
      return;
    }

    if (financialData.reputationalImpact === "") {
      msg("Please select Reputational Impact In Financial Impact", "red");
      return;
    }

    const formData = new FormData();

    // Risk Values
    if (riskData.risk) formData.append("riskDescription", riskData.risk);
    if (riskData.event) formData.append("event", riskData.event);
    if (riskData.kri) formData.append("KRI", riskData.kri);
    if (riskData.threshold) formData.append("threshold", riskData.threshold);
    if (riskData.thresholdLevel)
      formData.append("thresholdLevel", riskData.thresholdLevel);
    if (riskData.riskOwner) formData.append("riskOwner", riskData.riskOwner);
    if (riskData.frequency) formData.append("frequency", riskData.frequency);
    if (riskData.impactSeverity)
      formData.append("impactSeverity", riskData.impactSeverity);
    if (riskData.likelihoodId)
      formData.append("likelihoodId", riskData.likelihoodId);
    if (riskData.consequences)
      formData.append("consequences", riskData.consequences);
    if (riskData.controlEffectiveness)
      formData.append("controlEffectiveness", riskData.controlEffectiveness);
    if (riskData.division) formData.append("division", riskData.division);
    if (riskData.riskResponse)
      formData.append("riskResponse", riskData.riskResponse);
    if (riskData.trend) formData.append("trend", riskData.trend);

    // ######################
    if (financialData.financialExposure)
      formData.append("financialExposure", financialData.financialExposure);
    if (financialData.financialImpactTimeframe)
      formData.append(
        "financialImpactTimeframe",
        financialData.financialImpactTimeframe
      );
    if (financialData.financialImpactType)
      formData.append("financialImpactType", financialData.financialImpactType);
    formData.append("reputationalImpact", financialData.reputationalImpact);

    if (financialData.affectedStakeholders.length > 0) {
      financialData.affectedStakeholders.forEach((stakeholder) => {
        formData.append("affectedStakeholders[]", stakeholder);
      });
    }
    if (financialData.financialExposureDescription)
      formData.append(
        "financialExposureDescription",
        financialData.financialExposureDescription
      );

    if (financialData.opportunityDescription)
      formData.append(
        "opportunityDescription",
        financialData.opportunityDescription
      );
    if (financialData.category)
      formData.append("category", financialData.category);
    if (financialData.value) formData.append("value", financialData.value);

    if (impactData.esgImpactDomain)
      formData.append("esgImpactDomain", impactData.esgImpactDomain);

    if (impactData.valueChainScope.length > 0) {
      impactData.valueChainScope.forEach((value) => {
        formData.append("valueChainScope[]", value);
      });
    }

    if (impactData.impactDescription)
      formData.append("impactDescription", impactData.impactDescription);
    if (impactData.rootCaseCategory)
      formData.append("rootCaseCategory", impactData.rootCaseCategory);
    if (impactData.impactType)
      formData.append("impactType", impactData.impactType);

    if (impactData.impactOnSDG.length > 0) {
      impactData.impactOnSDG.forEach((sdg) => {
        formData.append("impactOnSDG[]", sdg);
      });
    }

    if (impactData.exposureLevel)
      formData.append("exposureLevel", impactData.exposureLevel);
    if (impactData.actionStatus)
      formData.append("actionStatus", impactData.actionStatus);
    if (impactData.progressTracking)
      formData.append("progressTracking", impactData.progressTracking);

    // Financial Values

    // // Notes Values
    if (notesData.attachment.length > 0) {
      notesData.attachment.forEach((file) => {
        formData.append(`attachments`, file);
      });
    }

    if (notesData.alignsWithRiskAppetite)
      formData.append(
        "alignsWithRiskAppetite",
        notesData.alignsWithRiskAppetite
      );
    if (notesData.preparedBy)
      formData.append("preparedBy", notesData.preparedBy);
    if (notesData.Reviewer) formData.append("Reviewer", notesData.Reviewer);
    if (notesData.comments) formData.append("comments", notesData.comments);
    if (notesData.dateRecorded)
      formData.append("dateRecorded", notesData.dateRecorded);

    setloading(true);
    try {
      await ApiS3.post("risk", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      msg("Risk added successfully", "green");
    } catch (er) {
      msg(`Something went wrong ${er?.response?.data?.message}`, "red");
      console.log("🚀 ~ addRisk ~ er:", er);
    }
    setloading(false);
  };

  const updateProgress = (id, newProgress) => {
    setButtons((prev) =>
      prev.map((btn) =>
        btn.id === id ? { ...btn, progress: newProgress } : btn
      )
    );
  };

  useEffect(() => {
    // Count non-empty fields
    const filledFields = Object.values(impactData).filter(Boolean).length;
    const totalFields = Object.keys(impactData).length;
    const progress = (filledFields / totalFields) * 100;

    // Update progress in parent
    updateProgress(3, progress);
  }, [impactData]);

  useEffect(() => {
    // Count non-empty fields
    const filledFields = Object.values(financialData).filter(Boolean).length;
    const totalFields = Object.keys(financialData).length;
    const progress = (filledFields / totalFields) * 100;

    // Update progress in parent
    updateProgress(2, progress);
  }, [financialData]);

  useEffect(() => {
    // Count non-empty fields
    const filledFields = Object.values(riskData).filter(Boolean).length;
    const totalFields = Object.keys(riskData).length;
    const progress = (filledFields / totalFields) * 100;

    // Update progress in parent
    updateProgress(1, progress);
  }, [riskData]);

  useEffect(() => {
    // Count non-empty fields
    const filledFields = Object.values(notesData).filter(Boolean).length;
    const totalFields = Object.keys(notesData).length;
    const progress = (filledFields / totalFields) * 100;

    // Update progress in parent
    updateProgress(4, progress);
  }, [notesData]);

  const changeImpact = (key, value) => {
    setImpactData((prev) => ({ ...prev, [key]: value }));
  };
  const changeFinancial = (key, value) => {
    setFinancialData((prev) => ({ ...prev, [key]: value }));
  };
  const changeRisk = (key, value) => {
    setRiskData((prev) => ({ ...prev, [key]: value }));
  };
  const changeNotes = (key, value) => {
    setNotesData((prev) => ({ ...prev, [key]: value }));
  };

  // Function to calculate residual risk
  const calculateResidualRisk = (inherentRisk, controlEffectiveness) => {
    // Map control effectiveness (1-5) to its corresponding percentage reduction
    const effectivenessMap = {
      10: 0.1, // Poor/None (10% reduction)
      30: 0.3, // Weak (30% reduction)
      50: 0.5, // Moderate (50% reduction)
      70: 0.7, // Effective (70% reduction)
      90: 0.9, // Very Effective (90% reduction)
    };

    // Get the percentage reduction based on the control effectiveness value
    const effectivenessPercentage = effectivenessMap[controlEffectiveness] || 0;

    // Calculate residual risk
    const residualRisk = inherentRisk * (1 - effectivenessPercentage);

    if (residualRisk < 1) {
      return Math.round(1);
    } else {
      // Round the result to the nearest integer
      return Math.round(residualRisk);
    }
  };
  // useEffect to calculate residual risk when inherentRisk or controlEffectiveness changes
  useEffect(() => {
    if (riskData.inherentRisk !== "" && riskData.controlEffectiveness !== "") {
      const residualRisk = calculateResidualRisk(
        riskData.inherentRisk,
        riskData.controlEffectiveness
      );
      setRiskData((prev) => ({ ...prev, residualRisk }));
    }
  }, [riskData.inherentRisk, riskData.controlEffectiveness]);

  // Calculate inherent risk value when likelihood or impact severity changes
  useEffect(() => {
    if (riskData.likelihoodId && riskData.impactSeverity) {
      const likelihoodValue = allLikelyHoodData.find(
        (item) => item._id === riskData.likelihoodId
      );
      const severityValue = allImpactSeverityData.find(
        (item) => item._id === riskData.impactSeverity
      );
      const inherentRiskValue = likelihoodValue.value * severityValue.value;
      setRiskData((prev) => ({
        ...prev,
        inherentRisk: inherentRiskValue,
      }));
    }
  }, [riskData.likelihoodId, riskData.impactSeverity]);

  const resetRiskAssessment = () => {
    setRiskData({
      event: "",
      kri: "",
      threshold: "",
      frequency: "",
      riskOwner: "",
      division: "",
      likelihoodId: "",
      impactSeverity: "",
      consequences: "",
      inherentRisk: "",
      controlEffectiveness: "",
      residualRisk: "",
      riskResponse: "",
      trend: "",
    });
  };

  const resetFinancialForm = () => {
    setFinancialData({
      financialImpactType: "",
      financialExposure: "",
      financialImpactTimeframe: "",
      affectedStakeholders: "",
      reputationalImpact: "",
      financialExposureDescription: "",
      opportunityDescription: "",
      category: "",
      value: "",
    });
  };

  return (
    <S3Layout
      menus={ESGRiskManagementMenu}
      navbarTitle="Risk Identification & Assessment"
    >
      <div
        className="flex gap-10 mb-10 pb-3 min-w-full overflow-x-auto [&::-webkit-scrollbar]:h-[8px] [&::-webkit-scrollbar-thumb]:bg-[#05808b] [&::-webkit-scrollbar-track]:bg-white [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:rounded-full"
        role="tablist"
      >
        {data.map((tab, idx) => (
          <div key={tab.id} className="flex w-full gap-7 items-center">
            <Link
              to={tab.path}
              className={`whitespace-nowrap p-2 text-xs rounded-xl flex w-full items-center gap-2 font-semibold transition-colors duration-200
                    ${tab.id === 2 ? "bg-white" : "hover:bg-gray-100"}
                    `}
              onClick={() => setActiveTab(tab.id)}
              role="tab"
              aria-selected={tab.id === 2}
              aria-controls={`tabpanel-${tab.id}`}
            >
              {tab.icon}
              <span className={`${tab.id === 2 ? "gradient-text" : ""}`}>
                {tab.name}
              </span>
            </Link>
            {idx !== data.length - 1 && (
              <span className="w-[1px] h-full bg-[#1C889C]"></span>
            )}
          </div>
        ))}
      </div>
      <div className="grid lg:grid-cols-4 gap-5">
        {Buttons.map((item, index) => (
          <SelectFormBtn
            key={index}
            item={item}
            selected={selected}
            setSelected={setSelected}
          />
        ))}
      </div>

      {selected == 1 && (
        <RiskAssessmentForm
          handleChange={changeRisk}
          formData={riskData}
          isRiskDone={isRiskAiDone}
          setIsRiskDone={setIsRiskAiDone}
          likelihoodLevels={likilyHoodData}
          impactSeverityData={impactSeverityData}
          addRiskFn={addRisk}
          loading={loading}
          resetRiskAssessment={resetRiskAssessment}
        />
      )}

      {selected == 2 && (
        <FinancialImpactForm
          handleChange={changeFinancial}
          formData={financialData}
          isAiDoneExposure={isExposureAiDone}
          setIsAiDoneExposure={setIsExposureAiDone}
          isAiDoneOpportunity={isOpportunityAiDone}
          setIsAiDoneOpportunity={setIsOpportunityAiDone}
          reputationalImpactData={impactSeverityData}
          resetFinancialForm={resetFinancialForm}
        />
      )}

      {selected == 3 && (
        <ImpactAssessmentForm
          handleChange={changeImpact}
          formData={impactData}
          isAiDone={isImpactAiDone}
          setIsAiDone={setIsImpactAiDone}
        />
      )}

      {selected == 4 && (
        <NotesForm
          handleChange={changeNotes}
          formData={notesData}
          addRiskFn={addRisk}
          loading={loading}
          dateValue={dateValue}
          setDateValue={setDateValue}
        />
      )}

      <br />
      <br />
    </S3Layout>
  );
};

export default RiskFormsPage;
