import { AcademicCapIcon, ClockIcon, StudentsIcon } from "@/assets/icons";

const CourseInfo = ({ students, lessons }) => {
  let allLessons = null;
  if (lessons) {
    allLessons = lessons
      .map((section) => section?.lectures?.map((lecture) => lecture))
      ?.flat(1)?.length;
  }
  const allStudents = students?.length;
  return (
    <p className="flex justify-start items-center gap-8 text-xs text-gray-800">
      <span className="flex items-center gap-1">
        {<ClockIcon />} 2 - 3 hours
      </span>
      <span className="flex items-center gap-1">
        {<AcademicCapIcon />} {allLessons || "-"} Lessons
      </span>
      <span className="flex items-center gap-1">
        {<StudentsIcon />} {allStudents || "-"} Students
      </span>
    </p>
  );
};

export default CourseInfo;
