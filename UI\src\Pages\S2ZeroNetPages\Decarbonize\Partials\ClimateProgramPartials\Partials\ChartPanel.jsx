import React, { useState } from 'react';
import Chart from './Chart';
import Graph<PERSON>ey from './GraphKey';
import { data } from './data';
const ChartPanel = () => {

  const [visibleSeries, setVisibleSeries] = useState({
    Scope1: true,
    Scope2: true,
    Scope3: true,
  });

  const handleSwitchChange = (seriesKey) => {
    setVisibleSeries((prevState) => ({
      ...prevState,
      [seriesKey]: !prevState[seriesKey],
    }));
  };

  return (
    <div className="flex flex-col md:flex-row justify-between gap-3">
      <Chart data={data} visibleSeries={visibleSeries} onSwitchChange={handleSwitchChange} />
      <GraphKey visibleSeries={visibleSeries} onSwitchChange={handleSwitchChange} />
    </div>
  );
};

export default ChartPanel;
