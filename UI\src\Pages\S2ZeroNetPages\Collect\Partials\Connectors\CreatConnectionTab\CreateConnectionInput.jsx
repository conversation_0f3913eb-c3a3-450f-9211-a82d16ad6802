import { Button, Image, Modal, Select, TextInput } from "@mantine/core";
import { DateInput } from "@mantine/dates";
import { useForm } from "@mantine/form";
import { useDisclosure } from "@mantine/hooks";
import { useCallback, useState } from "react";

export default function CreateConnectionInput({
 onTest,
 onSubmit,
 initialValues,
 validate,
 inputs,
 title,
 img,
}) {
 const [opened, { open, close }] = useDisclosure(false);
 const [canCreate, setCanCreate] = useState(false);
 const [loadingTest, setLoadingTest] = useState(false);
 const [loadingSubmit, setLoadingSubmit] = useState(false);

 const form = useForm({
  mode: "uncontrolled",
  initialValues,
  validate,
 });

 const handleSubmit = useCallback(async (values) => {
  setLoadingSubmit(true);

  console.log("🚀 ~ handleSubmit ~ values:", values)
  const result = await onSubmit(values);

  if (result?.success) {
   setLoadingSubmit(false);
   close();
   form.reset();
  } else {
   setLoadingSubmit(false);
  }
 }, []);

 const handleTest = useCallback(async (values) => {
  setLoadingTest(true);
  console.log(values);

  const result = await onTest(values);

  if (result?.success) {
   setCanCreate(true);
   setLoadingTest(false);
  } else {
   setCanCreate(false);
   setLoadingTest(false);
  }
 }, []);

 return (
  <>
   <Modal
    opened={opened}
    onClose={() => {
     close();
     form.reset();
     setCanCreate(false);
    }}
    title={title}
    size={"auto"}
    centered
    overlayProps={{ opacity: 0.55, blur: 3 }}
   >
    <form className="flex flex-col gap-4 items-end">
     <div className="grid md:grid-cols-2 gap-4 w-[80vw] sm:w-[70vw] md:w-[60vw] lg:w-[50vw]">
      {inputs?.map(
       ({ key, label, placeholder, formConnection, data }, index) => (
        <div key={index}>
         {key === "text" && (
          <TextInput
          id={label}
           withAsterisk
           label={label}
           placeholder={placeholder}
           {...form.getInputProps(formConnection)}
          />
         )}
         {key === "DropDown" && (
          <Select
           label={label}
           data={data}
           placeholder={placeholder}
           {...form.getInputProps(formConnection)}
          />
         )}
         {key === "date" && (
          <DateInput
           label={label}
           placeholder={placeholder}
           {...form.getInputProps(formConnection)}
          />
         )}
        </div>
       )
      )}
     </div>
     <div className="space-x-4">
      <Button
       onClick={form.onSubmit(handleTest)}
       className="hover:bg-primary bg-[#37868F] text-white px-4 py-2 rounded-md disabled:bg-[#9aa5b1] disabled:hover:bg-[#9aa5b1] disabled:cursor-not-allowed"
       loading={loadingTest}
       disabled={loadingTest || canCreate}
      >
       Test Connection
      </Button>

      <Button
       disabled={!canCreate || loadingSubmit}
       loading={loadingSubmit}
       className="bg-primary hover:bg-[#37868F] text-white px-4 py-2 rounded-md disabled:bg-[#9aa5b1] disabled:hover:bg-[#9aa5b1] disabled:cursor-not-allowed"
       onClick={form.onSubmit(handleSubmit)}
      >
       Connect
      </Button>
     </div>
    </form>
   </Modal>
   <button
    onClick={open}
    className="rounded-3xl bg-white drop-shadow-[0_4px_4px_#00000017] h-full items-center w-full flex justify-center"
   >
    <figure className="py-6 h-full flex flex-col gap-5 items-center w-52">
     <Image loading="lazy" src={img} alt={title} className={`${title == 'Salesforce' ? 'w-36': 'w-20'}`} />
     <figcaption className="font-bold md:text-[1.5vw] lg:text-[1.1vw] text-primary bg-secondary-primary-lite px-4 py-2 rounded-full mt-auto w-full text-center">
      {title}
     </figcaption>
    </figure>
   </button>
  </>
 );
}
