import React from "react";

import { Accordion } from "@mantine/core";
import { useTranslation } from "react-i18next";

const AskedQuestions = () => {
  const questions = [
    {
      question: "Where are the data stored and who has access to the outcomes?",
      answer:
        "At LevelUp ESG, we prioritize data security and privacy. All data is stored in secure, ISO 27001 certified cloud servers located in the EU, ensuring compliance with GDPR and other data protection regulations. Access to outcomes is strictly controlled through role-based permissions. Only authorized personnel within your organization can access the data and outcomes. Our system administrators have limited access for maintenance purposes only, and all access is logged and auditable. We never share your data with third parties without your explicit consent.",
    },
    {
      question:
        "Is there ongoing technical support available after implementation?",
      answer:
        "Yes, we provide 24/7 technical support through multiple channels including live chat, email, and phone. Our support team is well-versed in both the technical aspects of our platform and ESG best practices to address any queries you may have.  ",
    },
    {
      question: "How often does LevelUp ESG release updates and new features?",
      answer:
        "We follow an agile development process with regular updates. Major feature releases typically occur quarterly, while minor updates and improvements are rolled out continuously. All updates are automatically applied to ensure you always have access to the latest features and regulatory compliance measures.  ",
    },
    {
      question:
        "Can LevelUp ESG provide expert consultation on complex ESG issues?",
      answer:
        "Absolutely. In addition to our AI-powered platform, we have a team of ESG experts available for consultation on complex issues. Whether you need help with scenario analysis, data science, regulatory interpretation, or strategic ESG planning, our advisors are here to support you",
    },
    {
      question:
        "Does LevelUp ESG offer any resources for continuous learning and development in ESG? ",
      answer:
        "Yes, through our ESG Academy and Green Hub, we provide a wealth of resources for continuous learning. This includes webinars, e-learning modules, industry reports, and a community forum for peer-to-peer knowledge sharing. We also offer customized training programs to keep your team updated on the latest ESG trends and best practices. ",
    },
    {
      question:
        "How does LevelUp ESG's AI-powered platform differ from traditional ESG assessment tools?",
      answer:
        "Our AI-powered platform offers real-time data integration, predictive analytics, and automated insights, providing more accurate and timely ESG assessments compared to traditional tools. It adapts to your specific industry context and evolving regulations.",
    },
    {
      question: "Can LevelUp ESG help with CSRD and ISSB compliance?",
      answer:
        "Yes, our ESG Compass Suite includes specific modules for CSRD and ISSB readiness, ensuring your organization aligns with these new standards through automated gap analysis and tailored action plans.",
    },
    {
      question:
        "How does the Net Zero Navigator handle Scope 3 emissions calculations?",
      answer:
        "The Net Zero Navigator uses AI and machine learning to automate Scope 3 emissions calculations, integrating data from your value chain and applying industry-specific emission factors for comprehensive and accurate reporting.",
    },
    {
      question: "What measures does LevelUp ESG take to prevent greenwashing?",
      answer:
        "Our GreenShield solution uses AI algorithms to detect potential greenwashing risks in your data and claims. It also provides robust data validation and verification processes, ensuring transparent and auditable ESG disclosures.",
    },
    {
      question:
        "How often is the platform updated to reflect changes in ESG regulations and standards?",
      answer:
        "We continuously monitor global ESG regulations and standards, updating our platform in real-time. Our AI systems are designed to adapt to new requirements, ensuring you're always compliant with the latest standards.",
    },
    {
      question:
        "Can LevelUp ESG integrate with our existing ERP and data management systems?",
      answer:
        "Yes, our platform is designed for seamless integration with various ERP systems and data sources, allowing for automated data collection and reducing manual input requirements. ",
    },
    {
      question: "How does LevelUp ESG support double materiality assessments?",
      answer:
        "Our ESG Compass Suite includes a dedicated double materiality assessment module that uses AI to analyse both impact materiality and financial materiality, helping you identify and prioritize the most crucial ESG issues for your business and stakeholders.",
    },
    {
      question:
        "What kind of support does LevelUp ESG provide for ESG reporting and stakeholder communication?",
      answer:
        "We offer automated report generation aligned with various reporting frameworks, data visualization tools, and AI-powered insights to enhance your stakeholder communications. Our expert advisory services can also assist in crafting effective ESG double materiality, strategy and narratives.",
    },
    {
      question: "How does LevelUp ESG ensure data security and privacy?",
      answer:
        "We employ industry-leading encryption, regular security audits, and strict access controls. Our platform is designed to comply with GDPR and other data protection regulations, ensuring your sensitive ESG data remains secure.",
    },
    {
      question:
        "Can LevelUp ESG's solutions scale as our organization grows and our ESG needs evolve?",
      answer:
        "Absolutely. Our modular and cloud-based platform is designed to scale with your organization. You can start with core functionalities and easily add more advanced features as your ESG maturity increases and your needs evolve.",
    },
    {
      question: "Why should my company measure its carbon footprint?",
      answer:
        "In today's global business landscape, measuring your carbon footprint is no longer just an environmental gesture—it's a strategic imperative. Here's why:",
      bolluts: [
        "Meet Global Climate Objectives: Contribute to worldwide efforts to combat climate change.",
        "Consumer Expectations: Respond to the growing demand for eco-friendly products and services across markets.",
        "Cost Management: Identify opportunities to reduce operational costs through improved efficiency.",
        "Investor Appeal: Attract environmentally conscious investors who prioritize sustainable businesses.",
        "Brand Enhancement: Strengthen your reputation as a responsible, forward-thinking company. ",
        "Regulatory Preparedness: Stay ahead of evolving climate-related regulations worldwide.",
      ],
    },
  ];

  const items = questions.map((ques, i) => (
    <Accordion.Item className="border-none" key={i} value={ques.question}>
      <Accordion.Control className="text-lg font-semibold ">
        {ques.question}
      </Accordion.Control>
      <Accordion.Panel>{ques.answer}</Accordion.Panel>
      <Accordion.Panel>
        <ul>
          {ques.bolluts &&
            ques.bolluts.map((bull, i) => (
              <li className="mt-2" key={i}>
                {bull}
              </li>
            ))}
        </ul>
      </Accordion.Panel>
    </Accordion.Item>
  ));
  const { t } = useTranslation();
  return (
    <section className="p-6 bg-white f-q-section rounded-2xl max-h-[376px] overflow-y-auto ">
      <div className="flex flex-wrap items-center justify-between font-semibold text-primary">
        <h1 className="text-[1.4rem]">{t("Frequently Asked Questions")}</h1>
        {/* <a className="text-base underline" href="#">
          View All
        </a> */}
      </div>
      <h3 className="mt-5 text-sm font-medium mb-7">
        {t("Get to know our platform and find answers to common questions.")}
      </h3>
      <Accordion defaultValue="Apples">{items}</Accordion>
    </section>
  );
};

export default AskedQuestions;
