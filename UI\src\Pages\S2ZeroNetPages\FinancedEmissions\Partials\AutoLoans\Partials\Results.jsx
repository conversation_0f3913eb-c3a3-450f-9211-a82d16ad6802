import { useAutoLoanStore } from '@/Store/useAutoLoanStore';
import React from 'react'

const Results = () => {
    const {
        total_financed_emissions,
        attribution_percentage,
        emissions_breakdown,
        manufacturing_emissions,
        use_phase_emissions,
        end_of_use_emissions,
      } = useAutoLoanStore();
  return (
    <div>
        <div className='flex flex-col gap-4 rounded-xl bg-white p-4 border-[#E8E7EA] border-2'>
            <h1 className='text-xl font-bold p-2 rounded-xl'>
                Results
            </h1>
            <table className="w-full">
            <thead className="bg-[#F5F4F5]">
                <tr className='font-bold'>
                <th className="text-start p-3 ">Metric</th>
                <th className="text-start p-3 ">Value</th>
                </tr>
            </thead>
            <tbody>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Total Financed Emissions</td>
                    <td className="p-3 border border-gray-300">{total_financed_emissions} tCO2e</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Attribution Percentage (LTV)</td>
                    <td className="p-3 border border-gray-300">{attribution_percentage} %</td>
                </tr>
                {/* <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Emissions Breakdown:</td>
                    <td className="p-3 border border-gray-300">{emissions_breakdown.map((emission, index) => `${emission} (${index + 1})`)}</td>
                </tr> */} 
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Manufacturing Emissions</td>
                    <td className="p-3 border border-gray-300">{manufacturing_emissions} tCO2e</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Use Phase Emissions</td>
                    <td className="p-3 border border-gray-300">{use_phase_emissions} tCO2e</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">End-of-Use Emissions</td>
                    <td className="p-3 border border-gray-300">{end_of_use_emissions} tCO2e</td>
                </tr>
            </tbody>
            </table>
        </div>
    </div>
  )
}

export default Results