import React, { useEffect, useMemo } from "react";
import cx from "clsx";
import { useState } from "react";
import {
  Table,
  Checkbox,
  ScrollArea,
  rem,
  Button,
  TextInput,
  Pagination,
} from "@mantine/core";
import { MdDelete, MdModeEdit } from "react-icons/md";
import { RiDeleteBin6Line } from "react-icons/ri";
import { IoFilter } from "react-icons/io5";
import { CiSearch } from "react-icons/ci";
import { useTranslation } from "react-i18next";
import Loading from "@/Components/Loading";

export default function SupplierInputTable({data,loading}) {
  const { t } = useTranslation();
  const [selection, setSelection] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const rowsPerPage = 5;
  const totalPages = Math.ceil(data?.length / rowsPerPage);
 
  
  const currentData = useMemo(() => {
    return (
      data?.slice((currentPage - 1) * rowsPerPage, currentPage * rowsPerPage) ||
      []
    );
  }, [data, currentPage, rowsPerPage]);
  // console.log(currentData);
  const toggleRow = (id) =>
    setSelection((current) =>
      current.includes(id)
        ? current.filter((item) => item !== id)
        : [...current, id]
    );
  const toggleAll = () =>
    setSelection((current) =>
      current.length === data.length ? [] : data.map((item) => item.id)
    );

  const rows = currentData?.map((item) => {
    const selected = selection.includes(item.id);
    const del = (e) => {
      e.target.closest("tr").remove();
    };

    return (
      <Table.Tr
        key={item.id}
        className={`${cx({
          ["bg-[#07838F1A]"]: selected,
        })} text-sm font-bold text-[#626364] text-left`}
      >
        <Table.Td>
          <span
            className="flex justify-center items-center text-xl"
            onClick={del}
          >
            <RiDeleteBin6Line />
          </span>
        </Table.Td>
        <Table.Td>
          <Checkbox
            checked={selection.includes(item.id)}
            onChange={() => toggleRow(item.id)}
            color="#07838F"
          />
        </Table.Td>
        <Table.Td>{item.name}</Table.Td>
        <Table.Td>{item.category}</Table.Td>
        <Table.Td>{item.contact}</Table.Td>
        <Table.Td>{item.fromContactDate}</Table.Td>
        <Table.Td>{item.toContactDate}</Table.Td>
        <Table.Td>{item.region}</Table.Td>
        <Table.Td>{item.sector}</Table.Td>
        <Table.Td>{item.services}</Table.Td>
        {/* <Table.Td>...</Table.Td> */}
      </Table.Tr>
    );
  });

  return (
    <>
      {loading ? (
        <Loading />
      ) : (
        <div className="bg-[#F7F4F4] mt-7">
          <div className="p-2 my-1 bg-white shadow-lg rounded-xl">
            <div className="grid items-center justify-end px-4 bg-white grid-cols-1 lg:grid-cols-2 rounded-xl">
              <div className="sm:flex sm:justify-around lg:justify-start">
                <div className="Data-Table-Delete-Supplier">
                  <Button className="text-black bg-transparent hover:bg-transparent hover:text-black">
                    <MdDelete className="me-1" />
                    Delete
                  </Button>
                </div>
                <div className="Data-Table-edit-Supplier">
                  <Button
                    className="text-black bg-transparent hover:bg-transparent hover:text-black"
                    // onClick={''}
                  >
                    <MdModeEdit className="me-1" />
                    Edit
                  </Button>
                </div>
              </div>
              <div>
                <div className="grid items-start justify-center grid-cols-1 md:grid-cols-3 lg:grid-cols-4 sm:items-center gap-x-5">
                  <div className="col-span-1  m-3 w-full mx-auto">
                    <p className="font-semibold  bg-[#EBEBEB] p-2 text-[#00C0A9] rounded-lg shadow-sm flex items-center w-full justify-center">
                      <span className="text-black">
                        <IoFilter className="mx-2" />
                      </span>
                      Filter
                    </p>
                  </div>

                  <div className="col-span-1 m-3 py-2 px-4 bg-[#EBEBEB] rounded-lg shadow-sm w-full mx-auto">
                    <p className="font-semibold text-[#9C9C9C] text-center">
                      Columns
                    </p>
                  </div>

                  <TextInput
                    className="w-full col-span-1 lg:col-span-2"
                    placeholder="Search by Topic Area or Assessment Question"
                    rightSection={<CiSearch className="w-5 h-5" />}
                    // value={''}
                    // onChange={''}
                    // disabled
                  />
                </div>
              </div>
            </div>
          </div>
          <ScrollArea>
            <Table
              miw={800}
              verticalSpacing="sm"
              className="bg-white shadow-lg rounded-xl"
            >
              <Table.Thead className="border-b-2 border-primary pb-6 text-primary font-bold text-base text-center">
                <Table.Tr>
                  <Table.Th className="text-center">Delete</Table.Th>
                  <Table.Th style={{ width: rem(40) }}>
                    <Checkbox
                      onChange={toggleAll}
                      checked={selection?.length === data?.length}
                      indeterminate={
                        selection.length > 0 && selection.length !== data.length
                      }
                      color="#07838F"
                    />
                  </Table.Th>
                  <Table.Th className="text-left">Name</Table.Th>
                  <Table.Th className="text-left">Supplies Category</Table.Th>
                  <Table.Th className="text-left">Contact</Table.Th>
                  <Table.Th className="text-left">Date of contact</Table.Th>
                  <Table.Th className="text-left">To</Table.Th>
                  <Table.Th className="text-left">Region</Table.Th>
                  <Table.Th className="text-left">Sector</Table.Th>
                  <Table.Th className="text-left">Services</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>{rows}</Table.Tbody>
            </Table>
          </ScrollArea>
          <div className="md:flex justify-between mt-5">
            <p className="text-sm text-secondary-300">
              {t("showingData", {
                start: (currentPage - 1) * rowsPerPage + 1,
                end: Math.min(currentPage * rowsPerPage, data?.length),
                total: data?.length,
              })}
            </p>
            <Pagination
              page={currentPage}
              onChange={(e) => {
                setCurrentPage(e);
                // setEdit(false);
              }}
              total={totalPages}
              color="#00c0a9"
              // className={`mt-5 ${DueDateError||actionItemsError?'cursor-not-allowed':''}`}
              // disabled={DueDateError||actionItemsError}
              className="flex justify-center mt-5 md:mt-0"
            />
          </div>
        </div>
      )}
    </>
  );
}
