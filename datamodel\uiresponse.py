from pydantic import BaseModel, ValidationError
from decimal import Decimal
from typing import Dict, List


class QuestionCategory(BaseModel):
    categoryId: str
    categoryText: str
    categoryWeight: Decimal
    categoryDescription: str
    categoryOrder: int


class QuestionScope(BaseModel):
    scopeId: str
    categoryId: str
    scopeText: str
    scopeWeight: Decimal
    scopeDescription: str
    scopeOrder: int


class QuestionObject(BaseModel):
    questionId: str
    scopeId: str
    categoryId: str
    controlQuestion: bool
    questionText: str
    questionOption: List[Dict[int, str]]
    questionDescription: str
    questionMandatory: bool
    questionOrder: int
    responseOption: int
    responseNote: str
    responseDT: str


class QuestionSet(BaseModel):
    setId: str
    questionObjects: List[QuestionObject]
    scopeText: str
    categoryText: str
    scopeOrder: int
    categoryOrder: int


class UIResponse(BaseModel):
    responseId: str
    questionSet: List[QuestionSet]

    def model_dump(self):
        return self.dict()
