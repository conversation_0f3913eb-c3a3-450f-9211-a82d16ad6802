import {
    Box,
    Title,
    Text,
    List,
    Group,
    ThemeIcon,
    Stack,
    Paper,
} from "@mantine/core";
import { FaCheck } from "react-icons/fa";

export default function AntiGreenwashingRegulationsGuide() {
    return (
        <Box mx="auto" maxWidth="1200px" p="md">
            <Title order={1} mb="xl">
                Anti-Greenwashing Compliance Checker: Regulations Tracker User
                Guide
            </Title>

            {/* Overview Section */}
            <Stack spacing="lg">
                <Text size="lg" mb="sm">
                    <b>Overview</b>
                </Text>
                <Text c="dimmed">
                    Tracks relevant anti-greenwashing regulations and their
                    requirements for your region.
                </Text>

                {/* Page Structure Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Page Structure</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>Regulation Table:</Text>
                        <List listStyleType="circle" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={400}>
                                    Region: The applicable region (e.g., UK).
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={400}>
                                    Regulation: Name of the regulation (e.g.,
                                    FCA Anti-Greenwashing Rule, CSRD).
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={400}>
                                    Description: Brief overview of the
                                    regulation's purpose.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={400}>
                                    Effective Year: Year the regulation takes
                                    effect (e.g., 2023, 2024).
                                </Text>
                            </List.Item>
                        </List>
                    </List.Item>
                </List>

                {/* Interacting with the Page Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Interacting with the Dashboard Page</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>View Regulations:</Text>
                        <Text size="sm">
                            Review the list of regulations, their descriptions,
                            and effective years.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Filter by Region:</Text>
                        <Text size="sm">
                            Use the dropdown (e.g., "UK") to filter regulations
                            by region.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Assess Relevance:</Text>
                        <Text size="sm">
                            Identify which regulations apply to your
                            organization based on region and description.
                        </Text>
                    </List.Item>
                </List>

                {/* Understanding Progress Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Understanding Your Progress</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>Regulation Overview:</Text>
                        <List listStyleType="disc" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={400}>
                                    Regulatory Coverage: Lists key
                                    anti-greenwashing regulations.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={400}>
                                    Clarity: Descriptions help understand each
                                    regulation's scope.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={400}>
                                    Timeliness: Effective years indicate
                                    compliance deadlines.
                                </Text>
                            </List.Item>
                        </List>
                    </List.Item>
                </List>

                {/* Next Steps Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Next Steps</b>
                </Text>
                <List listStyleType="decimal" spacing="sm">
                    <List.Item>
                        <Text>
                            Review regulations to ensure awareness of
                            requirements.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Use the Compliance Assessment page to align
                            practices with regulations.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Revisit the Regulation Tracker regularly for updates
                            on new or amended regulations.
                        </Text>
                    </List.Item>
                </List>

                {/* Tip Section */}
                <Paper shadow="sm" p="md" radius="md" mt="xl" withBorder>
                    <Group position="apart">
                        <ThemeIcon variant="light" radius="xl" size="2rem">
                            <FaCheck size="1.2rem" />
                        </ThemeIcon>
                        <Text fw={500}>
                            Stay proactive by aligning with regulations before
                            their effective dates!
                        </Text>
                    </Group>
                </Paper>
            </Stack>
        </Box>
    );
}
