import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid } from "recharts";

function ChartCo() {
  const data = [
    { id: 1, month: "Jan", Electricity: 100, Transportation: 75, Gas: 40 },
    { id: 2, month: "Feb", Electricity: 100, Transportation: 80, Gas: 25 },
    { id: 3, month: "Mar", Electricity: 100, Transportation: 60, Gas: 20 },
    { id: 4, month: "Apr", Electricity: 100, Transportation: 70, Gas: 30 },
    { id: 5, month: "May", Electricity: 100, Transportation: 90, Gas: 30 },
    { id: 6, month: "Jun", Electricity: 100, Transportation: 90, Gas: 30 },
  ];

  const yAxisTicks = ["0%", "25%", "50%", "75%", "100%"];

  const formatYAxisTick = (tick) => `${tick}%`;

  return (
    <BarChart width={400} height={300} data={data}>
      <CartesianGrid strokeDasharray="" stroke="none" vertical={true} />
      <XAxis
        dataKey="month"
        axisLine={false}
        dy={0}
        tickLine={false}
        fontSize={14}
      />
      <YAxis
        type="number"
        domain={[0, 100]}
        ticks={yAxisTicks.map((tick) => parseInt(tick))}
        tickFormatter={formatYAxisTick}
        interval={null}
        yAxisId={0}
        width={50}
        height={300}
        axisLine={false}
        tickLine={false}
        fontSize={14}
      />

      <Bar
        dataKey="Gas"
        stackId="a"
        fill="#07838F"
        barSize={30}
        radius={[0, 0, 5, 5]}
      />
      <Bar dataKey="Transportation" stackId="a" fill="#70D162" barSize={30} />
      <Bar
        dataKey="Electricity"
        stackId="a"
        fill="#D4E9EB"
        barSize={30}
        radius={[5, 5, 0, 0]}
      />
    </BarChart>
  );
}

export default ChartCo;
