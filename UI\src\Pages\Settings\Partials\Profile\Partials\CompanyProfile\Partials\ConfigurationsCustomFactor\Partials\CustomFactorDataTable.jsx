import React, { useState } from "react";
import {
  Table,
  <PERSON>rollArea,
  Button,
  Checkbox,
  Input,
  Pagination,
} from "@mantine/core";

import { useTranslation } from "react-i18next";
import CustomFactorRow from "./CustomFactorRow";
import Loading from "@/Components/Loading";
import ApiS2 from "@/Api/apiS2Config";
import { showNotification } from "@mantine/notifications";
import { CiSearch } from "react-icons/ci";
import _ from "lodash";
import { BiTrash } from "react-icons/bi";

const CustomFactorDataTable = ({
  tableData,
  fetchAgain,
  companyAssets,
  assetsList,
}) => {
  const { t } = useTranslation();

  const msg = (txt, color = "green") => {
    showNotification({ message: txt, color: color });
  };

  const [selection, setSelection] = useState([]);

  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const [itemId, setItemId] = useState(0);

  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(8);

  const toggleRow = (id) =>
    setSelection((current) =>
      current.includes(id)
        ? current.filter((item) => item !== id)
        : [...current, id]
    );
  const toggleAll = () =>
    setSelection((current) => {
      if (tableData.length === 0) return [];
      return current.length === tableData.length
        ? []
        : tableData.map((item) => item.id);
    });

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      const { data } = await ApiS2.post("/admin/delete-custom-factors", {
        factor_ids: selection,
      });
      if (data) {
        msg(data.message);
      }
      setIsDeleting(false);
      setIsDeleteOpen(false);
      fetchAgain();
    } catch (err) {
      console.log(err);
      setIsDeleteOpen(false);
      setIsDeleting(false);
      msg(err.response.data.message, "red");
    }
  };

  const chunkedData = _.chunk(tableData, rowsPerPage);
  const totalPages = chunkedData.length || 0;
  const currentData = chunkedData[Math.min(currentPage, totalPages) - 1] || [];

  const rows = currentData?.map((item, idx) => {
    let emissionName = companyAssets.find(
      (em) => em.id == item.emissionSourceId
    )?.asset;
    // add condition if the asset does not exist (5/26/2025)
    const itemLinkedAssets = item.linked_company_assets.map(
      (asset) => asset.id
    );
    const filterdAssets = assetsList.map((a) => a.id);
    const remainingAssets = filterdAssets.filter(
      (id) => !itemLinkedAssets.includes(id)
    );
    const assetsData = assetsList.filter((el) =>
      remainingAssets.includes(el.id)
    );

    return (
      <CustomFactorRow
        emissionName={emissionName}
        idx={idx}
        key={item.id}
        item={item}
        setItemId={setItemId}
        itemId={itemId}
        selection={selection}
        toggleRow={toggleRow}
        fetchAgain={fetchAgain}
        multiAssetSelectionsData={assetsData}
      />
    );
  });

  return (
    <div className="my-8">
      <div className="shadow-lg rounded-2xl bg-white p-4">
        <div className="flex mb-5 justify-between items-center">
          <h5 className="text-2xl font-semibold Manage-custom-factors">Custom factor List</h5>

          <div className="flex gap-2 items-center Search-Custom-Factor">
            <Input
              type="text"
              placeholder={t("SearchPlaceholder")} // Translated placeholder
              className="border border-gray-300 rounded-md sm:w-auto focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              size="md"
              leftSection={<CiSearch />}
            />
          </div>
        </div>
        <ScrollArea>
          <Table miw={800} verticalSpacing="sm" withTableBorder>
            <Table.Thead className="bg-secondary-lite-gray pb-6 text-base font-bold text-center">
              <Table.Tr>
                <Table.Th>
                  <Checkbox
                    onChange={toggleAll}
                    checked={
                      tableData.length > 0 &&
                      selection.length === tableData.length
                    }
                    color="#07838F"
                  />
                </Table.Th>

                <Table.Th className="text-center">{t("Action")}</Table.Th>
                <Table.Th className="text-center">
                  {t("Specific Factor")}
                </Table.Th>
                <Table.Th className="text-center">{t("Assets")}</Table.Th>
                <Table.Th className="text-center">
                  {t("Emission Source")}
                </Table.Th>

                <Table.Th className="text-center">{t("Activity")}</Table.Th>
                <Table.Th className="text-center">{t("eFactors")}</Table.Th>
                <Table.Th className="text-center">{t("UOM")}</Table.Th>
                <Table.Th className="text-center">{t("Source")}</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>{rows}</Table.Tbody>
          </Table>
        </ScrollArea>

        <div className="flex justify-between mt-5">
          {isDeleteOpen ? (
            <Button
              disabled={isDeleting}
              title="Confirm Delete Selected"
              className={`font-bold text-xs bg-red-500 text-white hover:bg-red-400
                    hover:text-white px-5 
                    ${
                      isDeleting.length == 0
                        ? "opacity-50 cursor-not-allowed"
                        : "opacity-100"
                    }
                    `}
              onClick={handleDelete}
              type="submit"
            >
              Confirm Delete Selected {isDeleting && <Loading />}
            </Button>
          ) : (
            <Button
              disabled={selection.length == 0}
              title="Select to delete"
              className={`font-bold text-xs bg-gray-100 text-red-500
                hover:border-red-500 px-2 
                    ${
                      selection.length == 0
                        ? "opacity-50 cursor-not-allowed"
                        : "opacity-100"
                    }
                    `}
              onClick={() => setIsDeleteOpen(true)}
              type="submit"
            >
              <BiTrash size={20} />
            </Button>
          )}
          <div className="flex justify-end">
            <Pagination
              color="#05808b"
              value={Math.min(currentPage, totalPages)}
              onChange={setCurrentPage}
              total={totalPages}
            />
          </div>

        </div>
      </div>
    </div>
  );
};

export default React.memo(CustomFactorDataTable);
