import { BreakdownAssetIcon, BreakdownScopesIcon } from "@/assets/svg/ImageSVG";
import { FaRegEye } from "react-icons/fa";
import { Link } from "react-router-dom";
import Loading from "@/Components/Loading";
import { formatNumber } from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/StaticData";
import { useTranslation } from "react-i18next";
import { Tabs } from "@mantine/core";
import { useState } from "react";
import classNames from "classnames";
import DynamicForScopeData from "./DynamicForScopeData";

export default function Scopes({ ScopesData, loading, target }) {
 // console.log(ScopesData?.total_emissions);
 const { t } = useTranslation();
 const [activeTab, setActiveTab] = useState(target || "Scope 1");
 const tabs = ScopesData
  ? Object.entries(ScopesData)
     .filter(([_, value]) => typeof value === "object")
     .map(([item,val]) => ({
      value: item,
      label: t(item),
      component: <DynamicForScopeData data={val.categories} scopeName={item}/>,
     }))
  : [];

 // {
 //  value: "Scopes",
 //  label: t("Scopes"),
 //  //  icon: (
 //  //   <BreakdownScopesIcon
 //  //    color={activeTab === "Scopes" ? "#07838F" : "#5A5A5A"}
 //  //   />
 //  //  ),
 //  component: "",
 //  // <Scopes
 //  //  setActiveTab={setActiveTab}
 //  //  ScopesData={totalEmissions?.scope_breakdown}
 //  //  loading={loading}
 //  // />
 // },
 // {
 //  value: "Countries",
 //  label: t("Countries"),
 //  //  icon: (
 //  //   <BreakdownCountriesIcon
 //  //    color={activeTab === "Countries" ? "#07838F" : "#5A5A5A"}
 //  //   />
 //  //  ),
 //  component: "",
 //  // <Countries
 //  //  setActiveTab={setActiveTab}
 //  //  CountriesData={totalEmissions?.location_breakdown}
 //  //  loading={loading}
 //  // />
 // },
 return (
  <>
   {loading ? (
    <Loading />
   ) : (
    <div>
     <div className="flex justify-between items-center">
      <h1 className="font-medium text-3xl font-inter">Scopes</h1>
      <p className="p-2 bg-white rounded-lg font-medium text-base">
       Total Emission:{" "}
       <span className="text-primary font-bold">
        {formatNumber(ScopesData?.total_emission)}
       </span>
       <small>(T CO₂e)</small>
      </p>
     </div>
     <div className="py-3">
      <Tabs defaultValue={activeTab} onChange={setActiveTab}>
       <Tabs.List className="static justify-around gap-3  mb-3 overflow-hidden rounded-md shadow-md bg-white">
        {tabs &&
         tabs.map((tab) => (
          <Tabs.Tab
           key={tab?.value}
           value={tab?.value}
           className={classNames(
            "font-bold bg-white flex items-center justify-center border-0 text-base",
            {
             "text-primary border-b-2 border-primary": activeTab === tab?.value,
             "text-[#5A5A5A]": activeTab !== tab?.value,
            }
           )}
          >
           <div className="flex items-center justify-center gap-2">
            <p className="">{tab?.icon}</p>
            {tab?.label}
           </div>
          </Tabs.Tab>
         ))}
       </Tabs.List>
       {loading ? (
        <Loading />
       ) : (
        tabs &&
        tabs.map((tab) => (
         <Tabs.Panel key={tab?.value} value={tab?.value}>
          {tab?.component}
         </Tabs.Panel>
        ))
       )}
      </Tabs>
     </div>
    </div>
   )}
  </>
 );
}
