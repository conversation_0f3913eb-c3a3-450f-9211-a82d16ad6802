import Loading from "@/Components/Loading";
import { Button, Modal, ScrollArea, Table } from "@mantine/core";
// import { cx } from 'clsx';

export default function ReportHistory({ opened, onClose, data, loading }) {
 const rows = data?.map((item,idx) => (
  <Table.Tr
   key={`${item?.id}-${idx}`}
   //  className={`${cx({
   //   // ["bg-[#07838F1A]"]: selected,
   //  })} text-sm font-bold text-[#626364] text-center`}
  >
   <Table.Td className="text-center">
    <p>{item.created_at?.split("T")[0]}</p>
   </Table.Td>
   <Table.Td className="text-center">
    <p>{item.created_at?.split("T")[1].substring(0, 5)}</p>
   </Table.Td>
   <Table.Td className="text-center">
    <p>{item.reporting_year}</p>
   </Table.Td>
   <Table.Td className="text-center">
    {/* <p>{item.report_url}</p> */}
    <Button
    //  key={idx}
     variant="subtle"
     href={item.report_url}
     target="_blank"
     component="a"
    >
     {new URL(item.report_url).pathname.split("/").pop()}
    </Button>
   </Table.Td>
  </Table.Tr>
 ));
 return (
  <Modal
   opened={opened}
   onClose={onClose}
   centered
   size={"100%"}
   withCloseButton={false}
  >
   {loading ? (
    <Loading />
   ) : (
    <>
     <ScrollArea>
      <Table
       // miw={800}
       verticalSpacing="sm"
       className="p-2 my-1 bg-white  rounded-xl"
      >
       <Table.Thead className="border-b-2 border pb-6 text-secondary-500 font-bold text-base text-center">
        <Table.Tr>
         <Table.Th className="text-center">Date</Table.Th>
         <Table.Th className="text-center">Timestamp</Table.Th>
         <Table.Th className="text-center">report Year</Table.Th>
         <Table.Th className="text-center">report Url</Table.Th>
        </Table.Tr>
       </Table.Thead>
       <Table.Tbody>{rows}</Table.Tbody>
      </Table>
     </ScrollArea>
    </>
   )}
  </Modal>
 );
}
