import ApiS2 from "@/Api/apiS2Config";
import { Tabs } from "@mantine/core";
import { showNotification } from "@mantine/notifications";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import BatchInputsTable from "./Partials/BatchInputsTable";
import ManualInputsTable from "./Partials/ManualInputsTable";
const InputSource = {
 batch: {
  bg: "#e0e2e7",
  text: "#667085",
  border: "#667085",
 },
 manual: {
  bg: "#e9dff3",
  text: "#9160C1",
  border: "#9160C1",
 },
 Connectors: {
  bg: "#d4e8fb",
  text: "#298BED",
  border: "#298BED",
 },
 "Mobile App": {
  bg: "#fbd4fb",
  text: "#E929ED",
  border: "#E929ED",
 },
 "AI-Data Driven": {
  bg: "#ced6df",
  text: "#0C3360",
  border: "#0C3360",
 },
};

const Status = {
 Pending: {
  bg: "#ffeecd",
  text: "#FFAB07",
  // border: "#00C0A9",
 },
 Accepted: {
  bg: "#ccf2d7",
  text: "#01BD36",
  // border: "#FF6007",
 },
 Rejected: {
  bg: "#eecccc",
  text: "#AB0202",
  // border: "#FF6007",
 },
};
const UserType = {
 client: {
  bg: "#ccf2ee",
  text: "#00C0A9",
  border: "#00C0A9",
 },
 supplier: {
  bg: "#ffdfcd",
  text: "#FF6007",
  border: "#FF6007",
 },
};
export default function DataAnomalyView({}) {
 const [activeTab, setActiveTab] = useState("Review Manuel Input");
 const { t } = useTranslation();
 const [assetTypeAll, setAssetTypeAll] = useState([]);
 const [loadingTable, setLoadingTable] = useState(false);
 const [dataBatch, SetDataBatch] = useState();
 const [dataManual, SetDataManual] = useState();
 const [error, SetError] = useState();
 const [loadingStateManual, setLoadingApprovedManual] = useState({});
 const [loadingStateBatch, setLoadingApprovedBatch] = useState({});
 //console.log(dataManual);

 const toggleLoading = (id, value, inputTypes) => {
  inputTypes === "manual" &&
   setLoadingApprovedManual((prevState) => ({
    ...prevState,
    [id]: value,
   }));
  inputTypes === "batch" &&
   setLoadingApprovedBatch((prevState) => ({
    ...prevState,
    [id]: value,
   }));
 };

 const handelAssetType = async () => {
  try {
   const { data: getAllAssetTypes } = await ApiS2.get(
    "/carbon-factors/get-all-asset-types"
   );

   setAssetTypeAll(getAllAssetTypes);
  } catch (error) {
   SetError(error.response.data.message);
  }
 };

 const getTableData = async (inputTypes) => {
  setLoadingTable(true);
  try {
   setLoadingTable(true);
   const { data } = await ApiS2.get("/admin/detect_quantity_anomalies");
   //console.log(data);

   data.message !== "No data sets found" &&
    SetDataManual(
     data.anomalies_datasets.filter((item) => item.inputType === "manual")
    );

   data.message !== "No data sets found" &&
    SetDataBatch(
     data.anomalies_datasets.filter((item) => item.inputType === "batch")
    );
   //  //console.log(data);
   setLoadingTable(false);
   // //console.log(data);
  } catch (error) {
   setLoadingTable(false);
   error.response?.data.error ? SetError(error.response?.data.error) : "";
   error.response?.data.message ? SetError(error.response?.data.message) : "";
   //console.log(error);
  }
 };
 const approvedRow = async (RowId, inputTypes) => {
  try {
   const { data } = await ApiS2.post(
    "/batch_inputs/confirm_inputs",
    {},
    {
     headers: {
      datasetId: RowId,
     },
    }
   );
   showNotification({
    message: "The record approved successfully",
    color: "green",
   });
   getTableData(inputTypes);
   //console.log(data);
  } catch (error) {
   error.response?.data.message &&
    showNotification({
     message: error.response?.data.message,
     color: "red",
    });
   error.response?.data.error &&
    showNotification({
     message: error.response?.data.error,
     color: "red",
    });

   //console.log(error);
  }
 };
 useEffect(() => {
  !assetTypeAll.length && handelAssetType();
  getTableData();
 }, []);

 return (
  <Tabs className="" defaultValue={activeTab} onChange={setActiveTab}>
   <Tabs.List
    // justify="center"
    className="static grid md:grid-cols-2  justify-center gap-5  mb-6 rounded-md text-primary"
   >
    <Tabs.Tab
     value="Review Manuel Input"
     className={`text-lg  ${
      activeTab === "Review Manuel Input"
       ? "text-white bg-opacity-100"
       : "bg-opacity-10"
     } hover:opacity-70 rounded-lg py-3 font-semibold bg-primary focus:border-b-primary`}
    >
     {t("Anomaly of Manual Input")}
    </Tabs.Tab>

    <Tabs.Tab
     value="Review Batch Input"
     className={`text-lg ${
      activeTab === "Review Batch Input"
       ? "text-white bg-opacity-100"
       : "bg-opacity-10"
     } hover:opacity-70 rounded-lg py-3 font-semibold bg-primary focus:border-b-primary`}
    >
     {t("Anomaly of Batch Input")}
    </Tabs.Tab>
   </Tabs.List>

   <Tabs.Panel value="Review Manuel Input">
    <ManualInputsTable
     InputSource={InputSource}
     Status={Status}
     UserType={UserType}
     assetTypeAll={assetTypeAll}
     getTableData={getTableData}
     data={dataManual}
     loadingTable={loadingTable}
     approvedRow={approvedRow}
     loadingApprovedManual={loadingStateManual}
     toggleLoading={toggleLoading}
     error={error}
    />
   </Tabs.Panel>

   <Tabs.Panel value="Review Batch Input">
    <BatchInputsTable
     InputSource={InputSource}
     Status={Status}
     UserType={UserType}
     assetTypeAll={assetTypeAll}
     data={dataBatch}
     getTableData={getTableData}
     loading={loadingTable}
     approvedRow={approvedRow}
     loadingApprovedBatch={loadingStateBatch}
     toggleLoading={toggleLoading}
     error={error}
    />
   </Tabs.Panel>
  </Tabs>
 );
}
