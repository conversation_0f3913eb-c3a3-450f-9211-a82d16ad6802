import { useEffect, useState } from "react";
import PhotosForm from "./Partials/PhotosForm";
import axios from "axios";
import Cookies from "js-cookie";
import PhotosTable from "./Partials/PhotosTable";

const WebsitePhotos = () => {
  const [topUsers,setTopUsers] = useState([])
  const [wheelUsers,setWheelUsers] = useState([])
  const [loading,setLoading] = useState(false)

  const getUsers = async () => {
    setLoading(true)
    try {
      const { data } = await axios.get(
        "https://portal-auth-main-staging.azurewebsites.net/all_images",
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("level_user_token")}`,
          },
        }
      );
      setTopUsers([...data.vip])
      setWheelUsers([...data.commoner])
    } catch (error) {
      console.log(error);
    }
    setLoading(false)
  };

  useEffect(() => {
    getUsers();
  }, []);

  return (
    <div>
      <PhotosForm refetch={getUsers} />
      <h3 className="mt-10 mb-5 border-b-2 w-fit border-black text-center mx-auto text-2xl text-primary">Top</h3>

      <PhotosTable position={'Top'} loading={loading} tableData={topUsers} refetch={getUsers}/>

      <div className="h-1 bg-primary mt-10" />

      <h3 className="mt-10 mb-5 border-b-2 w-fit border-black text-center mx-auto text-2xl text-primary">Wheel</h3>
      <PhotosTable position={'Wheel'} loading={loading} tableData={wheelUsers} refetch={getUsers}/>
    </div>
  );
};

export default WebsitePhotos;
