import React, { useEffect, useState } from "react";
import { Tabs } from "@mantine/core";
import { useTranslation } from "react-i18next";
import PublicManualInputs from "./PublicManualInputs/PublicManualInputs";
import NotPrivateLayout from "@/Layout/NotPrivateLayout";
import PublicBatchInputs from "./publicBatchInputs/PublicBatchInputs";
import { useSearchParams } from "react-router-dom";
import { showNotification } from "@mantine/notifications";
import Loading from "@/Components/Loading";
import PublicSupplyChainConfig from "@/Api/PublicSupplyChainConfig";

export default function SupplyChainView({ target }) {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState(
    target ? target : "PublicManualInputs"
  );
  const [assetTypeDrop, setAssetTypeDrop] = useState([]);
  const [assetTypeAll, setAssetTypeAll] = useState([]);
  const [CompanyAssetAll, setCompanyAssetAll] = useState([]);
  const [companyAssetDrop, setCompanyAssetDrop] = useState([]);
  const [AssetsError, setAssetsError] = useState();
  const [loading, setLoading] = useState(false);
  const [allItemSpecificities, setAllItemSpecificities] = useState();
  const [[Scopes]] = useSearchParams();
  const PublicToken = Scopes?.find((item) => item === "token");
  let token;
  useEffect(() => {
    // && PublicToken
    if (Scopes?.length === 2 && PublicToken) {
      token = Scopes[1];
      sessionStorage.setItem("PublicSupplyChainToken", token);
    } else {
      //console.log("Token is invalid.");
      showNotification({
        message: "Token is invalid.",
        color: "red",
      });
      setTimeout(() => {
        // window.location.href = "/login";
      }, 1000);
    }
  }, []);

  const handelAssetType = async () => {
    setLoading(true);
    try {
      setLoading(true);
      const { data: getAllAssetTypes } = await PublicSupplyChainConfig.get(
        "/carbon-factors/supplier/get-all-asset-types"
      );
      const { data: getAllCompanyTypes } = await PublicSupplyChainConfig.get(
        "/carbon-factors/supplier/get-all-company-assets"
      );
      // //console.log(getAllAssetTypes);
      // //console.log(getAllCompanyTypes);
      setLoading(false);
      setAssetTypeDrop(getAllAssetTypes.map((item) => item.asset));
      setAssetTypeAll(getAllAssetTypes);
      const flirtedCompanyAsset = getAllCompanyTypes
        .map((item) => item.assetName)
        .filter((value, index, self) => self.indexOf(value) === index);
      setCompanyAssetDrop(flirtedCompanyAsset);
      setCompanyAssetAll(getAllCompanyTypes);
    } catch (error) {
      setLoading(false);
      setAssetsError(error.response.data.message);
    }
  };
  const get_item_specificities = async () => {
    try {
      const { data } = await PublicSupplyChainConfig.get(
        "/carbon-factors/get_item_specificities"
      );
      setAllItemSpecificities(data);

      // //console.log(data);
    } catch (error) {
      //console.log(error);
    }
  };

  useEffect(() => {
    handelAssetType();
    get_item_specificities();
  }, []);
  return (
    <NotPrivateLayout stopNotification navbarTitle={"Supply Chain"}>
      {!PublicToken ? (
        <Loading />
      ) : (
        <div className="bg-[#F7F4F4] font-inter">
          <Tabs className="" defaultValue={activeTab} onChange={setActiveTab}>
            <Tabs.List
              justify="center"
              className="mb-6 block  md:flex md:justify-around text-gray-500 py-3 rounded-md overflow-hidden bg-white before:border-none"
            >
              <Tabs.Tab
                value="PublicManualInputs"
                className={`text-lg  mx-auto
                ${activeTab == "PublicManualInputs"
                    ? "text-primary border-b-2 border-primary"
                    : " border-none"
                  } 
               py-3 font-semibold hover:bg-transparent hover:opacity-100`}
              >
                {t("tabs.manualInputs")}
              </Tabs.Tab>

              <Tabs.Tab
                value="PublicBatchInputs"
                className={`text-lg mx-auto ${activeTab == "PublicBatchInputs"
                  ? "text-primary border-b-2 border-primary"
                  : " border-none"
                  } 
           py-3 font-semibold hover:bg-transparent hover:opacity-100`}
              >
                {t("tabs.batchInputs")}
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="PublicManualInputs">
              <PublicManualInputs
                AssetsError={AssetsError}
                CompanyAssetAll={CompanyAssetAll}
                assetTypeAll={assetTypeAll}
                assetTypeDrop={assetTypeDrop}
                companyAssetDrop={companyAssetDrop}
                handelAssetType={handelAssetType}
                loading={loading}
                allItemSpecificities={allItemSpecificities}

              />
            </Tabs.Panel>

            <Tabs.Panel value="PublicBatchInputs">
              <PublicBatchInputs
                assetTypeAll={assetTypeAll}
                handelAssetType={handelAssetType}
                allItemSpecificities={allItemSpecificities}
              />
            </Tabs.Panel>
          </Tabs>
        </div>
      )}
    </NotPrivateLayout>
  );
}
