import { useContext } from 'react';
import Loading from '../Loading';
import CourseCard from './CourseCard';
import { useAuth } from '@/Contexts/AuthContext';
import { AcademyContext } from '@/Contexts/AcademyContext';

const defaultClasses =
  'text-2xl font-semibold tracking-tight text-black bg-gray-200 px-8 py-5 rounded-t-xl hover:border-b-2 hover:border-[#07838F] hover:text-[#07838F] hover:bg-white duration-300';
const activeClasses = 'text-2xl font-semibold tracking-tight text-[#07838F] border-b-2 border-[#07838F] bg-white px-5 py-5 rounded-t-xl';

export default function CoursesGridSection({ courses }) {
  const { section, loading, setSearchParams } = useContext(AcademyContext);
  const { user } = useAuth();
  let CoursesEnrolled;

  if (section === 'available') {
    CoursesEnrolled = courses.filter((course) => course?.students?.includes(user?.userId.toString()));
  }

  return (
    <section>
      <div className="flex items-center gap-5">
        <button
          onClick={() => {
            setSearchParams({ tab: 'available' });
          }}
          className={section === 'available' ? activeClasses : defaultClasses}
        >
          Available Courses
        </button>
        <button
          className={section === 'courses' ? activeClasses : defaultClasses}
          onClick={() => {
            setSearchParams({ tab: 'courses' });
          }}
        >
          <p className="flex justify-center items-center gap-1">
            My courses
            <span className="bg-teal-500/20 text-green-600 rounded px-2  text-xs  ">
              {section === 'available' && CoursesEnrolled?.length}
              {section === 'courses' && courses?.length}
            </span>
          </p>
        </button>
      </div>
      <div className="items-center w-full pt-6 m-auto mb-5  bg-white shadow-xl">
        <div className="px-4 pb-10 mx-auto sm:px-6 lg:px-8">
          <div className="flex flex-col mt-6 gap-6 ">
            {loading ? (
              <Loading />
            ) : (
              courses?.length > 0 &&
              courses?.map((course) => (
                <CourseCard
                  key={course._id}
                  course={section === 'courses' ? course?.courseId : course}
                  isEnrolled={section === 'available' ? course?.students?.includes(user?.userId.toString()) : null}
                  progressPercentage={section === 'courses' ? course?.progressPercentage : null}
                />
              ))
            )}
            {!loading && courses?.length <= 0 && 'No Enrolled on Courses'}
          </div>
        </div>
      </div>
    </section>
  );
}
