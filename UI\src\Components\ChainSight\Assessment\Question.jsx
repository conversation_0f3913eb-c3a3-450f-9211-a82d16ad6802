import { useState, useEffect, useRef } from "react";
import { Radio, Group, TextInput, Checkbox, Button } from "@mantine/core";
import Cookies from "js-cookie";
import Collaborators from "./Collaborators";

const Question = ({
  question,
  options,
  id,
  baseUrl,
  messages,
  is_flagged,
  targetQuestionId,
  collaborators,
  companyUsers,
}) => {
  const initialSelectedOption =
    options.find((option) => option.is_chosen)?.id || null;
  const [selectedOption, setSelectedOption] = useState(initialSelectedOption);
  const [isChecked, setIsChecked] = useState(is_flagged);
  const [comment, setComment] = useState("");
  const [localComments, setLocalComments] = useState(messages || []);
  const questionRef = useRef(null);

  useEffect(() => {
    setLocalComments(messages || []);
  }, [messages]);

  useEffect(() => {
    if (id === targetQuestionId && questionRef.current) {
      questionRef.current.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  }, [id, targetQuestionId]);

  const handleAnswerSelect = async (optionId) => {
    setSelectedOption(optionId);

    try {
      const response = await fetch(`${baseUrl}/api/questions/answers/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${Cookies.get("level_user_token")}`,
        },
        body: JSON.stringify([
          {
            question_id: id,
            option_id: optionId,
          },
        ]),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to submit answer");
      }
    } catch (error) {
      console.error("Error submitting answer:", error.message);
    }
  };

  const handleQuestionFlagged = async (questionId, checked) => {
    const endpoint = checked
      ? `/api/questions/${questionId}/flag`
      : `/api/questions/${questionId}/unflag`;

    try {
      const response = await fetch(`${baseUrl}${endpoint}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${Cookies.get("level_user_token")}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message ||
            `Failed to ${checked ? "flag" : "unflag"} question`
        );
      }
    } catch (error) {
      console.error(
        `Error ${checked ? "flagging" : "unflagging"} question:`,
        error.message
      );
    }
  };

  const handleCommentSubmit = async () => {
    if (!comment.trim()) {
      alert("Please enter a comment before submitting.");
      return;
    }

    try {
      const response = await fetch(`${baseUrl}/api/questions/comment/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${Cookies.get("level_user_token")}`,
        },
        body: JSON.stringify({
          questionId: id,
          message: comment,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to submit comment");
      }

      const newComment = await response.json();
      const commentData = newComment.id
        ? newComment
        : {
            id: `temp-${Date.now()}`,
            message: comment,
          };

      setLocalComments((prev) => [...prev, commentData]);
      setComment("");
    } catch (error) {
      console.error("Error submitting comment:", error.message);
    }
  };

  const handleDivClick = (optionId) => {
    handleAnswerSelect(optionId);
  };

  return (
    <div
      ref={questionRef}
      className={`w-full bg-white border border-[#ECECEC] rounded-lg p-4 ${
        id === targetQuestionId ? "border-[#07838F] border-2" : ""
      }`}
    >
      <div className="font-bold text-xl text-[#272727]">{question}</div>
      <div className="flex flex-col">
        <Radio.Group
          name={id}
          withAsterisk
          value={selectedOption}
          onChange={(value) => {
            setSelectedOption(value);
            handleAnswerSelect(value);
          }}
        >
          <Group className="flex flex-col items-start mt-4 gap-2">
            {options.map((option) => (
              <div
                key={option.id}
                onClick={() => handleDivClick(option.id)}
                className={`${
                  selectedOption === option.id
                    ? "bg-[#07838F1A] text-[#07838F] "
                    : "bg-[#FFFFFF] text-[#525252]"
                } border hover:bg-[#07838F33] border-[#E5E7EB] w-full p-3 rounded-lg  cursor-pointer  transition-colors`}
              >
                <Radio label={option.label} value={option.id} color="#07838F" />
              </div>
            ))}
          </Group>
        </Radio.Group>
      </div>
      <div className="flex flex-col w-full gap-4 mt-4">
        <Checkbox
          label="Flag for review"
          checked={isChecked}
          onChange={(event) => {
            const newCheckedState = event.currentTarget.checked;
            setIsChecked(newCheckedState);
            handleQuestionFlagged(id, newCheckedState);
          }}
          color="#07838F"
        />
        <Collaborators
          id={id}
          collaborators={collaborators}
          companyUsers={companyUsers}
        />
        <div className="w-full">
          <div className="flex items-center gap-4 justify-between">
            <TextInput
              placeholder="Type your comment here"
              className="w-full"
              value={comment}
              onChange={(event) => setComment(event.currentTarget.value)}
            />
            <Button onClick={handleCommentSubmit} color="#07838F" w={140}>
              Comment
            </Button>
          </div>
          <div className="mt-2">
            {localComments.map((message) => (
              <div
                key={message.id}
                className="bg-[#07838F1A] border border-[#E5E7EB] w-full p-2 rounded-lg text-[#07838F] mt-2"
              >
                <p className="w-full overflow-x-auto">{message.message}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Question;