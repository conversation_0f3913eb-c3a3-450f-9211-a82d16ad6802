import Logo from "@/assets/images/circular-primary-logo.png";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import { ToastContainer } from "react-toastify";

const GuestLayout = ({ links, activeChatbot = false, children,navbarTitle  }) => {
  const Person = {
    name: "company",
  };

  const { generalLinks } = useSideBarRoute();
  links = links ? links : generalLinks;

  return (
    <div className="container mx-auto px-4 py-3">
      <div data-aos="fade-right">
        <div className="container mx-auto px-4 py-3">
          <h1 className="text-2xl font-bold text-primary flex items-center gap-2 ">
            <img className="w-16 " src={Logo} alt="logo" />
            LevelUp ESG
          </h1>
        </div>
      </div>
      <div className="flex items-center px-6 py-[5vh] overflow-hidden overflow-y-auto max-md:text-center min-h-[85vh] bg-[#f7f4f4] ">
        {children}
      </div>
      <ToastContainer />
    </div>
  );
};

export default GuestLayout;
