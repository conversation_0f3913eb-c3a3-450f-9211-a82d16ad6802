// server.js
import express from 'express';
import { collectDefaultMetrics, register } from 'prom-client';

const app = express();
const port = 8001;

// Collect default metrics
collectDefaultMetrics();

// Define the /metrics endpoint
app.get('/metrics', async (req, res) => {
  res.set('Content-Type', register.contentType);
  res.end(await register.metrics());
});

app.listen(port, () => {
  //console.log(`Server is running on http://127.0.0.1:${port}`);
});