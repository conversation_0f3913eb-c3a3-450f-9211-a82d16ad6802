import React, { useState, useRef, useEffect } from "react";
import { MdClose } from "react-icons/md";
import { TiDelete } from "react-icons/ti";
import { FaPen } from "react-icons/fa";
import Draggable from "react-draggable";
import { MdInfoOutline } from "react-icons/md";

const StickyNotes = () => {
  const [notes, setNotes] = useState(() => {
    // Retrieve notes from localStorage on component mount
    const storedNotes = localStorage.getItem("stickyNotes");
    return storedNotes ? JSON.parse(storedNotes) : [];
  });
  const [inputValue, setInputValue] = useState("");
  const [isModalVisible, setModalVisible] = useState(false);
  const [expandedNote, setExpandedNote] = useState(null);
  const textareaRef = useRef(null);
  const randomColors = [
    "bg-yellow-300",
    "bg-pink-300",
    "bg-blue-300",
    "bg-green-300",
    "bg-purple-300",
  ];
  const randomMargins = ["-5", "1", "5", "10", "7"];
  const randomDegrees = [
    "rotate-3",
    "rotate-1",
    "rotate-1",
    "rotate-3",
    "rotate-5",
    "rotate-8",
  ];

  useEffect(() => {
    // Save notes to localStorage whenever it changes
    localStorage.setItem("stickyNotes", JSON.stringify(notes));
  }, [notes]);

  const addNote = () => {
    setModalVisible(true);
    setTimeout(() => {
      textareaRef.current.focus();
    }, 100);
  };

  const removeModal = () => {
    setModalVisible(false);
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter") {
      createStickyNote();
    }
  };

  const createStickyNote = () => {
    if (inputValue.trim() !== "") {
      setNotes((prevNotes) => [...prevNotes, inputValue]);
      setInputValue("");
      removeModal(); // Hide modal after adding note
    }
  };

  const deleteNote = (index) => {
    const newNotes = [...notes];
    newNotes.splice(index, 1);
    setNotes(newNotes);
  };

  const toggleExpandNote = (index) => {
    setExpandedNote(index === expandedNote ? null : index);
  };

  return (
    <div className="bg-[#FFFFFF] min-h-96 flex flex-col bg-dots p-6 rounded-xl">
      <div className="flex justify-between items-center px-4 py-2">
        <div className="flex items-center gap-2">
          <MdInfoOutline size="1.75em" className="text-[#969696]" />
          <h1 className="text-3xl">Work Space</h1>
        </div>
        <button
          className="text-blue-500 hover:text-blue-700 focus:outline-none"
          onClick={addNote}
        >
          <FaPen size="1.5em" className="text-[#2A939C]" />
        </button>
      </div>
      {isModalVisible && (
        <div
          id="modal"
          className="fixed inset-0 z-50 bg-black bg-opacity-50 flex justify-center items-center"
        >
          <div
            id="inner-modal"
            className="flex flex-col justify-center items-center p-4 rounded-lg bg-white"
          >
            <MdClose
              className="absolute top-2 right-2 text-gray-500 cursor-pointer"
              onClick={removeModal}
            />
            <textarea
              ref={textareaRef}
              placeholder="Write your note here..."
              id="user_input"
              maxLength="160"
              className="w-72 h-40 p-2 mb-2 border border-gray-500 rounded resize-none focus:outline-none"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyPress}
            ></textarea>
            <button
              className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
              onClick={createStickyNote}
            >
              Add Note
            </button>
          </div>
        </div>
      )}
      <div className="mt-4 flex flex-wrap justify-center">
        {notes.map((note, index) => (
          <Draggable key={index}>
            <div
              className={`relative p-4 m-2 rounded-lg shadow-lg ${
                randomColors[index % randomColors.length]
              } rotate-${
                randomDegrees[index % randomDegrees.length]
              } translate-y-${randomMargins[index % randomMargins.length]}`}
              style={{ width: "200px", height: "200px", cursor: "pointer" }}
              onClick={() => toggleExpandNote(index)}
            >
              {expandedNote === index ? (
                <p className="text-lg">{note}</p>
              ) : (
                <p className="text-lg">{note}</p>
              )}
              <TiDelete
                className="absolute top-2 right-2 text-red-500 cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  deleteNote(index);
                }}
              />
            </div>
          </Draggable>
        ))}
      </div>
    </div>
  );
};

export default StickyNotes;
