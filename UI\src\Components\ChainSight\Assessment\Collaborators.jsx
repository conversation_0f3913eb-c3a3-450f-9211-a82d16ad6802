import { useState, useEffect, useMemo } from "react";
import { MultiSelect } from "@mantine/core";
import Cookies from "js-cookie";
import { toast } from "react-toastify";

const Collaborators = ({ id, collaborators, companyUsers }) => {
  const [selectedCollaborators, setSelectedCollaborators] = useState([]);
  const url = "https://chain-sight-staging.azurewebsites.net";

  const handleAddCollaborator = async (userId) => {
    const token = Cookies.get("level_user_token");
    try {
      const response = await fetch(
        `${url}/api/questions/collaborator`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            userId: parseInt(userId),
            questionId: id,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to add collaborator: ${response.statusText}`);
      }

    } catch (error) {
      console.error("Error adding collaborator:", error);
      toast.error("Failed to add collaborator");
      setSelectedCollaborators((prev) =>
        prev.filter((id) => id !== userId)
      );
    }
  };

  const handleRemoveCollaborator = async (userId) => {
    const token = Cookies.get("level_user_token");
    try {
      const response = await fetch(
        `${url}/api/questions/collaborator/remove`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            userId: parseInt(userId),
            questionId: id,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to remove collaborator: ${response.statusText}`);
      }

    } catch (error) {
      console.error("Error removing collaborator:", error);
      toast.error("Failed to remove collaborator");
      setSelectedCollaborators((prev) => [...prev, userId]);
    }
  };

  // Handle MultiSelect changes
  const handleCollaboratorChange = (newValues) => {
    const added = newValues.find((val) => !selectedCollaborators.includes(val));
    const removed = selectedCollaborators.find(
      (val) => !newValues.includes(val)
    );

    setSelectedCollaborators(newValues);

    if (added) {
      handleAddCollaborator(added);
    } else if (removed) {
      handleRemoveCollaborator(removed);
    }
  };

  // Initialize selectedCollaborators from props
  useEffect(() => {
    if (collaborators && Array.isArray(collaborators)) {
      setSelectedCollaborators(
        collaborators.map((col) => col.id.toString())
      );
    }
  }, [collaborators]);

  // Prepare options for MultiSelect
  const userOptions = useMemo(() => {
    return (companyUsers || []).map((user) => ({
      value: user.user_id.toString(),
      label: user.user_name,
    }));
  }, [companyUsers]);

  return (
    <>
      <MultiSelect
        label="Collaborator"
        data={userOptions}
        placeholder="Select Colleague"
        searchable
        value={selectedCollaborators}
        onChange={handleCollaboratorChange}
        styles={{ dropdown: { zIndex: 1000 } }}
      />
    </>
  );
};

export default Collaborators;