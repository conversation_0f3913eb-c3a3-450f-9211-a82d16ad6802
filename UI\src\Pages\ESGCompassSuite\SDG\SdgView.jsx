import { useState } from "react";
import MainLayout from "@/Layout/MainLayout";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import SdgProvider from "@/Contexts/SdgContext";
import SdgAlignmentView from "./Partials/SdgAlignment/SdgAlignmentView";
import SdgImpactView from "./Partials/SdgImpact/SdgImpactView";
import { useTranslation } from "react-i18next";
import SdgReport from "./Partials/SdgReport/Partials/SdgReport";
import { IoMdHome } from "react-icons/io";
import GuideModalButton from "@/Components/Guide/GuideModalButton";
import MeasurementGuide from "./MeasurementGuide";
import ReportGuide from "./ReportGuide";

export default function SdgView() {
    const { sdgMenu } = useSideBarRoute();
    const { t } = useTranslation();

    const [active, setActive] = useState("Alignment Assessment");

    const renderContent = () => {
        switch (active) {
            case "Alignment Assessment":
                return (
                    <>
                        <SdgAlignmentView />
                    </>
                );
            case "Impact Measurement":
                return (
                    <>
                        <div className="w-full justify-between flex items-center py-5 px-3">
                            <GuideModalButton buttonText="Measurement Guide">
                                <MeasurementGuide />
                            </GuideModalButton>
                        </div>
                        <SdgImpactView />
                    </>
                );
            case "Reporting":
                return (
                    <>
                      <div className="w-full justify-between flex items-center py-5 px-3">
                          <GuideModalButton buttonText="Report Guide">
                              <ReportGuide />
                          </GuideModalButton>
                      </div>
                        <SdgReport />
                    </>
                );
            default:
                return null;
        }
    };

    return (
        <MainLayout
            menus={sdgMenu}
            navbarTitle={"SDG Alignment & Impact Measurement"}
            breadcrumbItems={[
                { title: <IoMdHome size={20} />, href: "/get-started" },
                { title: "SDG & Impact", href: "#" },
            ]}
        >
            <SdgProvider>
                {/* <Outlet /> */}
                <div className="grid md:grid-cols-3 justify-center bg-[#FFFFFF] border-2 border-[#E8E7EA] p-6 gap-5 py-3 mb-6 rounded-lg text-primary">
                    <button
                        className={`relative text-lg font-semibold py-3 px-6 transition-all ${
                            active === "Alignment Assessment"
                                ? "active-tab rounded"
                                : "text-[#5A5A5A] rounded-lg border-2"
                        }`}
                        onClick={() => setActive("Alignment Assessment")}
                    >
                        {t("Alignment Assessment")}
                    </button>

                    <button
                        className={`relative text-lg font-semibold py-3 px-6 transition-all ${
                            active === "Impact Measurement"
                                ? "active-tab rounded"
                                : "text-[#5A5A5A] rounded-lg border-2"
                        }`}
                        onClick={() => setActive("Impact Measurement")}
                    >
                        {t("Impact Measurement")}
                    </button>

                    <button
                        className={`relative text-lg font-semibold py-3 px-6 transition-all ${
                            active === "Reporting"
                                ? "active-tab rounded"
                                : "text-[#5A5A5A] rounded-lg border-2"
                        }`}
                        onClick={() => setActive("Reporting")}
                    >
                        {t("Reporting")}
                    </button>
                </div>

                {renderContent()}

                {/* <SdgAlignmentView /> */}
                {/* <SdgImpactView /> */}
            </SdgProvider>
        </MainLayout>
    );
}
