import ApiProfile from "@/Api/apiProfileConfig";
import Loading from "@/Components/Loading";
import { Button, Modal, TextInput } from "@mantine/core";
import { showNotification } from "@mantine/notifications";
import React from "react";

export default function ActionUserModal({
  close,
  opened,
  Action_user,
  selectedUserId,
  ActionLoading,
  SetSelectedUserId,
  mainTitle,
  ActionValue,
  setEditValue,
  setAccessUserValue,
}) {
  // console.log(selectedUserId);

  return (
    <Modal opened={opened} centered withCloseButton={false}>
      <h1 className="text-center text-xl font-bold capitalize">{mainTitle}</h1>
      <div className="flex justify-around">
        <Button
          className="bg-primary hover:bg-primary mt-5"
          onClick={() =>
            !ActionLoading && Action_user(selectedUserId, ActionValue)
          }
        >
          {ActionLoading ? <Loading /> : "Submit"}
        </Button>
        <Button
          className="bg-red-700 hover:bg-red-700 mt-5"
          onClick={() => {
            close();
            SetSelectedUserId(null);
            setAccessUserValue({});
            setEditValue({});
          }}
        >
          Close
        </Button>
      </div>
    </Modal>
  );
}
