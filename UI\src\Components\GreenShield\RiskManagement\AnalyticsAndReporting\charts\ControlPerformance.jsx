import { ExportIcon } from "@/assets/icons/ReportAndAnalytic";
import { DonutChart } from "@mantine/charts";
import { controlPerformanceBadges } from "../Constants";
import { useEffect, useRef, useState } from "react";
import ApiS3 from "@/Api/apiS3";
import Loading from "@/Components/Loading";
import * as htmlToImage from 'html-to-image';

const ControlPerformance = () => {
  const chartValues = [
    { title: "Effective", color: "#52F176" ,value: 0,id: 1},
    { title: "Partially Effective", color: "#B2E7BE" ,value: 0 ,id: 2},
    { title: "Ineffective", color: "#FBB90D" ,value: 0 , id: 3},
    { title: "Under Review", color: "#298BED" ,value: 0 , id : 4},
    { title: "Overdue Testing", color: "#E81E1E" ,value: 0 , id: 5 }
  ];
  const [chartData, setchartData] = useState([])

  const [loading, setloading] = useState(false)
  
  const getData = async () => {
    setloading(true)
    try {
      const res = await ApiS3.get("auditControls/controlsChart");
      const data = chartValues.map((item)=> {
        if(item.id == 1) {
          return {...item,value: res.data.effectiveNum }
        }
        if(item.id  ==2) {
          return {...item,value: res.data.partiallyEffectiveNum }
        }
        if(item.id  ==3) {
          return {...item,value: res.data.ineffectiveNum }
        }
        if(item.id  ==4) {
          return {...item,value: res.data.partiallyEffectiveNum }
        }
        if(item.id  ==5) {
          return {...item,value: res.data.overDueNum }
        }
      });
      setchartData(data);
    } catch (er) {
        console.log("🚀 ~ getData ~ er:", er)
    }
    setloading(false)
  };

  useEffect(() => {
    getData();
  }, []);

  const chart = useRef(null);  


  const handleExportPNG = () => {
    if (chart.current) {
        htmlToImage
            .toPng(chart.current, {
                quality: 0.95, // Image quality (0 to 1)
                pixelRatio: 2, // Increase resolution
                backgroundColor: '#f5f5f5', // Match the background color
            })
            .then((dataUrl) => {
                // Create a link element to trigger the download
                const link = document.createElement('a');
                link.href = dataUrl;
                link.download = 'Control Performance.png'; // File name for the download
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            })
            .catch((error) => {
                console.error('Error exporting PNG:', error);
                alert('Failed to export PNG. Please try again.');
            });
    }
  };
  return (
    <div className="mt-10 bg-white shadow-md rounded-2xl p-4">
      <div className="flex flex-wrap justify-between items-center mb-10">
        <h5 className="text-xl">Control Performance</h5>

        <button onClick={handleExportPNG} className="bg-bg-lite2 text-primary flex justify-between items-center rounded-xl p-3 gap-2">
          <ExportIcon />
          <span className="text-sm font-bold">PNG Export</span>
        </button>
      </div>

      <div ref={chart} className="grid grid-cols-2 gap-4">
       {loading ? <Loading /> : <DonutChart  data={chartData} />}

        <div className="flex flex-col gap-2">
            {controlPerformanceBadges.map((badge) => (
                <div className="flex items-center gap-2" key={badge.title}>
                    <div className="w-3 h-3 rounded-full" style={{ backgroundColor: badge.color }}></div>
                    <span className="text-sm font-bold">{badge.title}</span>
                </div>
            ))}
        </div>
      </div>
    </div>
  );
};

export default ControlPerformance;
