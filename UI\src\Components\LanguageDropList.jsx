import { useEffect } from "react";
import enJson from "../locale/en.json";
import { getTranslation } from "@/Pages/GreenHubPages/Partials/translationAPI";
import Cookies from "js-cookie";
import { useTranslation } from "react-i18next";

const dropListOptions = [
  { name: "English", value: "en", id: 1 },
  { name: "Arabic", value: "ar", id: 2 },
  { name: "Français", value: "fr", id: 3 },
  { name: "Deutch", value: "de", id: 4 },
  { name: "Русский", value: "ru", id: 5 },
  { name: "中文-Hans", value: "zh-Hans", id: 6 },
];

function LanguageDropList() {
  const { i18n } = useTranslation();

  const handleChangeLanguage = async (lnValue, lnName) => {
    try {
      const body = {
        lang: lnValue,
        data: enJson,
      };

      if (lnValue === "en") {
        Cookies.set("upLevelLang", lnValue, { expires: 1 });
        localStorage.removeItem("upLevelTranslation");
        i18n.changeLanguage("en");
        document.documentElement.setAttribute("dir", "ltr");
        return;
      }

      let translation;
      const cachedTranslation = localStorage.getItem("upLevelTranslation");
      const cachedLang = Cookies.get("upLevelLang");

      if (cachedTranslation && cachedLang === lnValue) {
        translation = JSON.parse(cachedTranslation);
      } else {
        translation = await getTranslation(body);
        if (!translation) throw new Error("Translation API returned no data.");


        localStorage.setItem("upLevelTranslation", JSON.stringify(translation));
        Cookies.set("upLevelLang", lnValue, { expires: 1 });
      }


      i18n.removeResourceBundle(lnValue, "translation");
      i18n.addResources(lnValue, "translation", translation);
      i18n.changeLanguage(lnValue);

      if (lnValue === "ar") {
        document.documentElement.setAttribute("dir", "rtl"); 
      } else {
        document.documentElement.setAttribute("dir", "ltr"); 
      }

      //console.log("Language successfully changed to:", lnName);
    } catch (error) {
      console.error("Error changing language:", error.message);
    }
  };

  useEffect(() => {
    const storedLang = Cookies.get("upLevelLang");
    const storedTranslation = localStorage.getItem("upLevelTranslation");

    if (storedLang && storedTranslation) {
      i18n.addResources(storedLang, "translation", JSON.parse(storedTranslation));
      i18n.changeLanguage(storedLang);

      if (storedLang === "ar") {
        document.documentElement.setAttribute("dir", "rtl");
      } else {
        document.documentElement.setAttribute("dir", "ltr");
      }
    }
  }, [i18n]);

  return (
    <ul className="language-list animate-burgerMenu absolute flex flex-col cursor-default items-center gap-1 py-1 px-1 text-black bg-slate-200 rounded-lg sm:gap-2 top-[169px] right-[15px]">
      {dropListOptions.map((option) => (
        <li
          key={option.id}
          className={`language-option animate-languageSelections px-4 w-full py-1 text-center rounded-md cursor-pointer ${
            i18n.language === option.value ? "bg-secondary-100" : "bg-transparent"
          } hover:bg-secondary hover:text-white`}
          onClick={() => handleChangeLanguage(option.value, option.name)}
        >
          {option.name}
        </li>
      ))}
    </ul>
  );
}

export default LanguageDropList;
