import { useAuth } from "@/Contexts/AuthContext";
import Loading from "../Loading";
import SearchBar from "./SearchBar";
import { useState } from "react";
import { useLocation } from "react-router";
import UserMenu from "./UserMenu";
import UserIcons from "./UserIcons";
import PopUpSearch from "./PopUpSearch";
import { Link } from "react-router-dom";

import logo from "../../assets/images/nav-bar-logo.png";

const Navbar = ({ stopNotification }) => {
  const [notifications, setNotifications] = useState(true);
  const [isSearchBarOpen, setIsSearchBarOpen] = useState(false);
  const [openMenu, setOpenMenu] = useState(false);

  const location = useLocation();
  const { user } = useAuth();

  return (
    <>
      <nav className="flex flex-col lg:flex-row gap-5 justify-between fixed w-full items-center dark:bg-[#282828] text-gray-700 dark:text-white bg-white text-gray-200 border-r border-[#E8E7EA]  pr-6 h-16 z-50">
        <div
          className={`flex gap-2 w-full ${stopNotification ? "hidden" : ""}`}
        >
          <Link to="/get-started" title="LaunchPad" className="w-[62px] mx-2">
            <img
              src={logo}
              width={20}
              height={20}
              alt="logo"
              className="w-28 h-16 object-contain"
            />
          </Link>

          {/* search with the right icons */}
          <div className="flex w-full flex-1 h-16 justify-between border-l-2 border-b dark:border-[#292929]">
            <div className="flex items-center justify-start gap-2 w-full pl-3">
              <SearchBar
                setIsSearchBarOpen={setIsSearchBarOpen}
                isSearchBarOpen={isSearchBarOpen}
              />
            </div>
            {!user && location.pathname === "get-started" ? (
              <div className="mx-auto">
                <Loading />
              </div>
            ) : (
              <>
                <UserIcons
                  notifications={notifications}
                  user={user}
                  setOpenMenu={setOpenMenu}
                />
              </>
            )}
          </div>
        </div>
      </nav>
      {
        <UserMenu
          openMenu={openMenu}
          notifications={notifications}
          setNotifications={setNotifications}
        />
      }
      {isSearchBarOpen && (
        <div className="bg-white border border-[#DCDCDC]   w-fit flex items-center justify-center m-auto absolute left-20 top-12 z-50 py-1 px-2 rounded-xl sm:hidden">
          <PopUpSearch />
        </div>
      )}
    </>
  );
};

export default Navbar;
