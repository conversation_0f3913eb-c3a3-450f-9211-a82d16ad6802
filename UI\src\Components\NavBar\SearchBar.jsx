import { SearchIconNavBar } from '@/assets/icons';

const SearchBar = ({ setIsSearchBarOpen }) => {
  return (
    <form
      className="max-w-full"
      onSubmit={(e) => {
        e.preventDefault();
      }}
    >
      <label htmlFor="default-search" className="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">
        Search
      </label>
      <div className="flex gap-1 items-center">
        <div
          className={`sm:hidden p-2 rounded-full cursor-pointer border border-[#DCDCDC] dark:border-[#292929] bg-transparent `}
          onClick={() => setIsSearchBarOpen((prev) => !prev)}
        >
          <SearchIconNavBar />
        </div>

        <div className={`relative hidden sm:block`}>
          <button type="submit" className="absolute inset-y-0 start-0 flex items-center ps-3 ">
            <SearchIconNavBar />
          </button>
          <input
            type="search"
            id="default-search"
            className="block w-full px-4 py-2 ps-10 text-sm border-2 outline-none bg-[#F7F9FB] border-[#DCDCDC] dark:border-[#292929] rounded-full text-black dark:bg-[#1E1E1E] text-gray-700 dark:text-white"
            placeholder="Search "
            required
          />
        </div>
      </div>
    </form>
  );
};

export default SearchBar;
