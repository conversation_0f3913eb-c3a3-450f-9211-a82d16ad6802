import React from 'react'
import  PropType from 'prop-types';


function ChatItems({img, name, massege, num, date}) {
  return (
    <div className="flex justify-between items-center border-b-[1px] pb-6 mt-4">
        <div className="flex items-center gap-[1rem]">
            <div className="w-[50px] h-[50px] rounded-full overflow-hidden">
                <img src={img} className="w-full h-full object-cover" />
            </div>
            <div>
                <div className='text-[16px] font-medium text-[#07838F]'>{name}</div>
                <div className="text-[12px] text-[#D9D9D9]">{massege}</div>
            </div>
        </div>
        <div className="relative">
            <div className="text-[13px]">{date}</div>
            <div className="w-[15px] h-[15px] rounded-full flex justify-center items-center bg-[#106D84] text-white text-[12px] absolute right-0 top-6">{num}</div>
        </div>
  </div>
  )
}

ChatItems.propTypes = {
  img: PropType.string,
  name: PropType.string,
  massege: PropType.string,
  num: PropType.number,
  date: PropType.string
};

export default ChatItems
