import S3Layout from "@/Layout/S3Layout";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import React from "react";
import IncidentTable from "./Partials/IncidentTable";
import { BsExclamationSquare } from "react-icons/bs";
import { But<PERSON> } from "@mantine/core";
import { AiOutlineFile } from "react-icons/ai";

export default function NonFiMyIncidents() {
  const { greenShieldNonFiESGIncidentManagement } = useSideBarRoute();

  return (
    <S3Layout menus={greenShieldNonFiESGIncidentManagement}>
      <div className="w-fill py-5 flex justify-center bg-white rounded-lg shadow-md">
        <h1 className="flex items-center text-4xl text-primary font-semibold">
          <BsExclamationSquare className="me-2" /> Incidents
        </h1>
      </div>
      <div className="mt-5">
        <Button className="text-xl bg-white border border-gray-400 text-gray-400 hover:bg-white hover:text-gray-400" size="md">
        <AiOutlineFile className="me-1"/>Export Incidences
        </Button>
      </div>
      <div>
        <IncidentTable />
      </div>
    </S3Layout>
  );
}
