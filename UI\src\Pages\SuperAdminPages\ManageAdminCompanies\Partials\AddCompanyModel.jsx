import ApiProfile from "@/Api/apiProfileConfig";
import Loading from "@/Components/Loading";
import { Button, Modal, NumberInput, TextInput } from "@mantine/core";
import { isNotEmpty, useForm } from "@mantine/form";
import { showNotification } from "@mantine/notifications";
import { useState } from "react";

export default function AddCompanyModel({
  close,
  opened,
  show_company_users,
  SetSelectedUserId,
}) {
  const [loading, setLoading] = useState(false);

  const form = useForm({
    mode: "uncontrolled",
    initialValues: {
      CompanyName: "",
      Country: "",
      email: "",
      size: "",
      user_limit: "",
    },
    validate: {
      CompanyName: isNotEmpty("User Name is required"),
      email: isNotEmpty("Email is required"),
      Country: isNotEmpty("Role is required"),
      size: isNotEmpty("Role is required"),
      user_limit: isNotEmpty("Role is required"),
    },
  });
  const add_new_user = async (value) => {
    const editValue = {
      companyName: value.CompanyName,
      email: value.email,
      country: value.Country,
      size: value.size,
      user_limit: value.user_limit,
    };
    console.log(editValue);

    try {
      setLoading(true);
      const { data } = await ApiProfile.post(
        "/admin/company/register",
        editValue
      );
      showNotification({
        message: data.massage,
        color: "green",
      });
      form.reset();
      setLoading(false);
      show_company_users();
      close();
      SetSelectedUserId(null);
      // setData(data);
      console.log(data);
    } catch (error) {
      setLoading(false);
      error.response?.data.message &&
        showNotification({
          message: error.response?.data.message,
          color: "red",
        });
      error.response?.data.error &&
        showNotification({
          message: error.response?.data.error,
          color: "red",
        });
      console.log(error);
    }
  };
  return (
    <Modal opened={opened} onClose={close} title="Add New User" centered>
      <TextInput
        {...form.getInputProps("CompanyName")}
        key={form.key("CompanyName")}
        placeholder="Enter Company Name ..."
        label={"Company Name"}
        className="mb-2"
      />
      <TextInput
        {...form.getInputProps("Country")}
        key={form.key("Country")}
        placeholder="Enter Country ..."
        label={"Country Name"}
        className="my-2"
      />
      <TextInput
        {...form.getInputProps("size")}
        key={form.key("size")}
        placeholder="Enter Company size ..."
        label={"Company Name"}
        className="my-2"
      />
      <NumberInput
        {...form.getInputProps("user_limit")}
        key={form.key("user_limit")}
        placeholder="Enter Number User Limit ..."
        label={"User Limit"}
        className="my-2"
        rightSection={' '}
      />
      
      <TextInput
        {...form.getInputProps("email")}
        key={form.key("email")}
        placeholder="Enter Email ..."
        label={"Email"}
      />
      <Button
        className="bg-primary hover:bg-primary mt-5"
        onClick={!loading && form.onSubmit(add_new_user)}
      >
        {loading ? <Loading /> : "Submit"}
      </Button>
    </Modal>
  );
}
