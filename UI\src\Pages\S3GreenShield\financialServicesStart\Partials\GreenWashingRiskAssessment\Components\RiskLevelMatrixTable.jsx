import React from "react";

import { ScrollArea, Table } from "@mantine/core";

const head = [
  "Likelihood | impact",
  "Insignificant (1)",
  "Minor (2)",
  "Moderate (3)",
  "Major (4)",
  "Catastrophic (5)",
];

// Almost Certain (5)
// Likely (4)
// Possible (3)
// Unlikely (2)
// Rare (1)

const data = [
  {
    id: "1",
    likelihood: "Almost Certain (5)",
    insignificant: "Medium (5)",
    minor: "High (10)",
    moderate: "High (15)",
    major: "Extreme (20)",
    catastrophic: "Extreme (25)",
  },
  {
    id: "2",
    likelihood: "Likely (4)",
    insignificant: "Medium (4)",
    minor: "Medium (8)",
    moderate: "High (12)",
    major: "High (16)",
    catastrophic: "Extreme (20)",
  },
  {
    id: "3",
    likelihood: "Possible (3)",
    insignificant: "Low (3)",
    minor: "Medium (6)",
    moderate: "Medium (9)",
    major: "High (12)",
    catastrophic: "High (15)",
  },
  {
    id: "4",
    likelihood: "Unlikely (2)",
    insignificant: "Low (2)",
    minor: "Low (4)",
    moderate: "Medium (6)",
    major: "Medium (8)",
    catastrophic: "High (10)",
  },
  {
    id: "5",
    likelihood: "Rare (1)",
    insignificant: "Low (1)",
    minor: "Low (2)",
    moderate: "Low (3)",
    major: "Medium (4)",
    catastrophic: "High (5)",
  },
];
const RiskLevelMatrixTable = () => {
  const rows = data.map((item) => {
    return (
      <Table.Tr
        key={item.id}
        className={`risk-matrix-table bg-white text-sm border-secondary-lite-100`}
      >
        <Table.Td className="bg-secondary-gray-100 p-2 text-center">
          {item.likelihood}
        </Table.Td>
        <Table.Td className="p-2 text-center">{item.insignificant}</Table.Td>
        <Table.Td className="p-2 text-center">{item.minor}</Table.Td>
        <Table.Td className="p-2 text-center">{item.moderate}</Table.Td>
        <Table.Td className="p-2 text-center">{item.major}</Table.Td>
        <Table.Td className="p-2 text-center">{item.catastrophic}</Table.Td>
      </Table.Tr>
    );
  });
  return (
    <>
      <ScrollArea>
        <Table withTableBorder withColumnBorders miw={800} verticalSpacing="lg">
          <Table.Thead className="border-b-2 border-secondary-lite-100 bg-white pb-6 text-base text-center">
            <Table.Tr>
              {head.map((el, i) => (
                <Table.Th key={i} className="bg-secondary-gray-100 p-2">
                  <div className="flex items-center justify-center gap-2">
                    <span className="font-medium">{el}</span>
                  </div>
                </Table.Th>
              ))}
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>{rows}</Table.Tbody>
        </Table>
      </ScrollArea>
    </>
  );
};

export default RiskLevelMatrixTable;
