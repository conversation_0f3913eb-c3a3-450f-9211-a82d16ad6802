import React from "react";
import { FaDotCircle } from "react-icons/fa";
import { GoDot } from "react-icons/go";

const SWOTStat = ({ title, stats }) => {
  
  return (
    <div className="mb-3  w-full">
      <div className="">
        <div className="  border-l-[2px] border-l-primary
            border-b-[2px] border-b-primary
            border-t-[8px] border-t-primary
            border-r-[8px] border-r-primary
            rounded-[1rem]
            p-[1rem] xl:h-[450px]">
          <h3 className="font-semibold text-lg mb-2">{title}</h3>
          <div className="flex flex-col justify-between gap-4 sm:gap-4 h-[90%]">

            {stats.flatMap(stat => stat.value).map((value, i) => (
                <div key={i} className="mb-1 flex items-start">
                  <GoDot className="basis-1/12" />
                  <p className="basis-10/12 text-[11px] md:text-[14px] lg:text-[14px]">{value}</p>
                </div>
              ))}
              {/* // <div key={i} className=" mb-1 flex items-start h-full ">
              //   <GoDot className=" basis-1/12" />
              //   <p className="basis-10/12 text-[11px] md:text-[14px] lg:text-[16px]">{stat}</p>
              // </div> */}
           
          </div>
        </div>
      </div>
    </div>
  );
};

export default SWOTStat;
