import { Table } from "@mantine/core";
import { useEffect, useState } from "react";
import { Badges, scores } from "./Constants";
import { BsArrowDownRight, BsArrowUpRight } from "react-icons/bs";


/* ES5 */

const TopRiskRow = ({ item, i }) => {
  const [score, setScore] = useState("");

  const scoreLevel = scores.find((level) => level.value === score);

//   return ''
  useEffect(() => {
    switch (true) {
      case item.residualRisk >= 1 && item.residualRisk <= 3:
        setScore("low");
        break;
      case item.residualRisk >= 4 && item.residualRisk <= 9:
        setScore("medium");
        break;
        case item.residualRisk >= 10 && item.residualRisk <= 15:
        setScore("high");
        break;
        case item.residualRisk >= 16 && item.residualRisk <= 25:
        setScore("critical");
        break;
      default:
        setScore("");
    }
  }, [item.residualRisk]);
  return (
    <Table.Tr key={i}>
      <Table.Td>{item.event}</Table.Td>
      <Table.Td>
        {
          scoreLevel && 
        <Badges level={scoreLevel} />
        }
      </Table.Td>
      <Table.Td>
        {item.trend == "Stable" && (
          <div className="w-6 h-6 bg-yellow-500 rounded-lg full-center" />
        )}
        {item.trend == "Increasing" && (
          <div className="w-6 h-6 bg-bg-red1 rounded-lg full-center">
            <BsArrowUpRight color="white" size={16} />
          </div>
        )}
        {item.trend == "Decreasing" && (
          <div className="w-6 h-6 bg-[#08B12D] rounded-lg full-center">
            <BsArrowDownRight color="white" size={16} />
          </div>
        )}
      </Table.Td>
    </Table.Tr>
  );
};

export default TopRiskRow;
