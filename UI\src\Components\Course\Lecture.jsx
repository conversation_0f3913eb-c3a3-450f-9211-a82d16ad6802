import { PlayVideoIcon } from "@/assets/icons";
import { CourseContext } from "@/Contexts/CourseContext";
import { useContext, useRef, useState } from "react";

export default function Lecture({ lecture, index }) {
  const { setCurrentVideo, progress, playLecture, currentVideo, isWatching } =
    useContext(CourseContext);

  const [duration, setDuration] = useState(null);
  const videoRef = useRef(null);

  const handleLoadedMetadata = () => {
    if (videoRef) {
      setDuration(videoRef.current.duration);
    }
  };
  const formatDuration = (seconds) => {
    const totalSeconds = Math.floor(seconds);
    if (totalSeconds >= 60) {
      const minutes = Math.floor(totalSeconds / 60);
      const remainingSeconds = totalSeconds % 60;
      return `${minutes}min ${remainingSeconds}s`;
    }
    return `${totalSeconds} seconds`;
  };

  return (
    <li
      className={`mb-2 cursor-pointer hover:text-[#1f585e] hover:bg-gray-100 rounded-xl text-base py-1 px-2 flex items-center justify-between duration-500 ${
        progress[lecture._id] ? "text-[#05808b] " : null
      } ${
        currentVideo?._id === lecture?._id && isWatching
          ? "bg-gray-100 font-semibold text-[#494949]  "
          : "font-normal "
      }`}
      onClick={() => {
        playLecture(lecture);
        setCurrentVideo(lecture);
      }}
    >
      <video
        ref={videoRef}
        src={lecture.lectureUrl}
        onLoadedMetadata={handleLoadedMetadata}
        className="hidden"
      />
      <span className="flex items-center gap-1">
        {<PlayVideoIcon />}
        Lecture {index + 1}: {lecture.lectureName || `Lecture ${index + 1}`}
      </span>
      <p className="text-sm text-gray-500 font-normal">
        {formatDuration(duration)}
      </p>
    </li>
  );
}
