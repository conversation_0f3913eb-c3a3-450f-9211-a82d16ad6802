import IssbCard from "@/Issb/IssbCard";
import S3Layout from "@/Layout/S3Layout";
import { EquatorIcon, IFCIcon } from "@/assets/icons/InsightsAndReporingIcons";
import { IoMdHome } from "react-icons/io";


const FinanceGuard = () => {
  const csrdReadinessMenu = [
    "Guided self-assessment that identifies gaps and delivers tailored improvement roadmaps",
    "Bespoke collaboration workspace with role-based dashboards for cross-departmental alignment",
    "Secure centralised document repository with audit trails and version control for regulatory scrutiny",
    "Intelligent action management with customisable escalation paths",
  ];
  const ISSBReadness = [
    "Risk categorisation, classification and audit-ready documentation",
    "Environmental and social risk management alignment",
    "Project impact monitoring and reporting tools",
    "Stakeholder engagement platform and grievance mechanisms",
  ];
  return (
    <S3Layout
      menus={[]}
      navbarTitle="FinanceGuard: Sustainable Finance Navigator"
      breadcrumbItems={[
        { title: <IoMdHome size={20}/>, href: "/get-started" },
        { title: "FinanceGuard", href: "#" },
      ]}
    >
      <div className="flex flex-col gap-5 w-full min-h-screen px-3">
        <IssbCard
          icon={
            <>
              <IFCIcon />
            </>
          }
          title="IFC & GCF Assessment"
          description="Comprehensive solution for streamlining your IFC Performance Standards compliance journey with intelligent analytics."
          items={csrdReadinessMenu}
          btnString="Start Assessment "
          link="/Insights-reporing/financeguard/ifc-gcf"
        />

        <IssbCard
          icon={
            <>
              <EquatorIcon />
            </>
          }
          title="Equator Principles Assessment"
          description="Comprehensive Equator Principles compliance with dynamic risk categorisation and monitoring capabilities"
          items={ISSBReadness}
          btnString="Start Assessment "
          link="/Insights-reporing/financeguard/equator-principles"
        />
      </div>

      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
    </S3Layout>
  );
};

export default FinanceGuard;
