{"CommunityPost": {"climateChangeActivist": "Climate Change Activist", "askedAgo": "asked {{seconds}} ago", "valueQuestion": "What is the value of addressing climate change in the community?", "sustainability": "Sustainability", "governance": "Governance", "sustainabilityTag": "Sustainability", "governanceTag": "Governance", "save": "Save", "share": "Share", "votes": "votes", "views": "views", "answers": "answers"}, "CourseCart": {"courseOverview": "Course Overview"}, "previous": "Previous", "next": "Next", "askPublicQuestion": "Ask a public question", "writingAGoodQuestion": "Writing a good question", "writingGuidance": "Make sure your question is clear, concise, and provides enough context.", "businessQuestion": "Is this a business-related question?", "businessQuestionGuidance": "If your question is business-specific, be sure to highlight that.", "steps": "Steps for writing a good question:", "stepsList": ["Summarize the problem", "Explain the background of the issue", "Describe what you've tried so far", "Include any relevant details"], "addTitle": "Add a title", "titlePlaceholder": "Title (e.g., How to create a sustainable strategy?)", "detailsQuestion": "Provide more details about your question", "detailsPlaceholder": "Details about your question", "tags": "Add tags", "tagsGuidance": "Add up to 5 tags to categorize your question", "tagsPlaceholder": "Select tags", "nothingFound": "No matching tags found", "PeersCommunity": "Peers Community", "Academy": "Academy", "Resources": "Resources", "SharedPost": "shared a post", "AskedQuestion": "asked a question", "AnsweredOnQuestion": "answered a question", "AllQuestions": "All Questions", "CreatePoll": "Create Poll", "AddQuestion": "Add Question", "QuestionsCount": "{{count}} questions", "Newest": "Newest", "Oldest": "Oldest", "Unanswered": "Unanswered", "Polls": "Polls", "AddFilters": "Add Filters", "ExploreByQuestion": "Explore by Question", "Sustainably": "Sustainably", "Governance": "Governance", "Environment": "Environment", "GreenShield": "GreenShield", "Society": "Society", "Marketing": "Marketing", "RecentActivities": "Recent Activities", "TryNewExperience": "Try the New Experience", "ResourcesHubTitle": "Resources Hub", "ResourcesHubSubtitle": "Explore a wide range of topics and resources to empower business leaders in areas such as governance, risk management, and internal controls.", "EmpoweringBusinessLeaders": "Empowering Business Leaders with Essential Knowledge", "ResourceDescription": "Our resources are designed to equip business leaders with the tools they need to drive sustainability and governance in their organizations.", "PickFavoriteTopic": "Pick Your Favorite Topic", "GreenHubTitle": "LevelUp ESG®: Empowering Green Growth: Connect, Learn, and Access Resources", "PeerCommunity": "Peer Community", "Sustainability": "Sustainability", "AnsweredQuestion": "Answered on your question", "LevelUpAcademyTitle": "LevelUp Academy", "AcademySubtitle": "Your Gateway to Sustainable Growth", "PickYourFavoriteCourse": "Pick Your Favorite Course"}