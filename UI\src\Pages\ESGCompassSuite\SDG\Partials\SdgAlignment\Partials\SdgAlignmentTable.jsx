import ApiS1Config from "@/Api/apiS1Config";
import Loading from "@/Components/Loading";
import useTableSearchingAndSorting from "@/hooks/useTableSearchingAndSorting";
import {
 Button,
 Checkbox,
 FileInput,
 Select,
 Table,
 TagsInput,
 TextInput,
} from "@mantine/core";
import { showNotification } from "@mantine/notifications";
import cx from "clsx";
import React, { useEffect, useState } from "react";
import { CiSearch } from "react-icons/ci";
import { FaArrowUp } from "react-icons/fa";
import { FaArrowDown } from "react-icons/fa6";
import { IoIosCheckmarkCircleOutline } from "react-icons/io";
import { IoCloseCircleOutline, IoLink } from "react-icons/io5";
import { MdModeEdit, MdOutlineFileDownload } from "react-icons/md";
import SdgAlignmentFilter from "./SdgAlignmentFilter";

const SdgAlignmentTable = ({
 prioritySelectColorMap,
 readinessColorMap,
 sdgAlignment,
 PriorityLevels,
 ReadinessLevel,
 postAnswers,
 postLoading,
}) => {
 const {
  selection,
  sortedData,
  search,
  reverseSortDirection,
  edit,
  value,
  toggleRow,
  toggleAll,
  handleSearchChange,
  handleSort,
  setEdit,
  setValue,
  setReverseSortDirection,
 } = useTableSearchingAndSorting(sdgAlignment);
 const [selectedReadinessLevel, setSelectedReadinessLevel] = useState({});
 const [selectedPriorityState, setSelectedPriorityState] = useState({});
 const [evidenceUrls, setEvidenceUrls] = useState({});
 const [evidenceFile, setEvidenceFile] = useState({});
 const [evidences, setEvidence] = useState({});
 const [backEndEvidences, setBackEndEvidence] = useState({});
 const [uploadLoading, setUploadLoading] = useState({});
 const [actionItems, setActionItems] = useState({});
 const [tags, setTags] = useState({});
 useEffect(() => {
  sortedData?.map((item) =>
   item.questions.map((question, idx) => {
    question?.evidence &&
     setBackEndEvidence((prev) => ({
      ...prev,
      [`${item.id}-${idx}`]: question?.evidence[0],
     }));
   })
  );
  // if (!sdgAlignment || !sdgAlignment.topics) return;

  // setSortedData(sdgAlignment.topics || []);
  // sdgAlignment.topics.forEach((item) =>
  //   item.questions.forEach((question, idx) => {
  //     handleSelectReadinessChange(
  //       idx,
  //       question.readinessLevel ? ReadinessLevel[question.readinessLevel] : ""
  //     );
  //     handleSelectPriorityChange(
  //       idx,
  //       question.priority ? PriorityLevels[question.priority] : ""
  //     );
  //     handleURLInputChange(
  //       idx,
  //       question.evidence ? question.evidence[1] : ""
  //     );
  //     handleActionItemsInputChange(
  //       idx,
  //       question.actionItems ? question.actionItems : ""
  //     );
  //   })
  // );
 }, [sortedData]);

 const updateEditState = () => {
  // Create an object where each rowId in selection is set to true
  const newEditState = selection.reduce((acc, rowId) => {
   acc[rowId] = true;
   return acc;
  }, {});

  // Update state
  setEdit((prevEditState) => ({
   ...prevEditState,
   ...newEditState,
  }));
 };

 const handleSelectReadinessChange = (id, value) => {
  setSelectedReadinessLevel((prev) => ({ ...prev, [id]: value }));
 };
 const handleSelectPriorityChange = (rowId, value) => {
  setSelectedPriorityState((prev) => ({ ...prev, [rowId]: value }));
 };
 const handleFileInputChange = (rowId, file, type) => {
  if (type === "delete") {
   // Remove the item from the state
   setEvidenceFile((prev) => {
    const updatedFiles = { ...prev };
    delete updatedFiles[rowId];
    return updatedFiles;
   });
   setEvidence((prev) => {
    const updatedFiles = { ...prev };
    delete updatedFiles[rowId];
    return updatedFiles;
   });
   setBackEndEvidence((prev) => {
    const updatedFiles = { ...prev };
    delete updatedFiles[rowId];
    return updatedFiles;
   });
  } else {
   // Update evidenceFile with the new file
   setEvidenceFile((prev) => ({ ...prev, [rowId]: file }));
  }
 };
 const handleURLInputChange = (rowId, file) => {
  // Update evidenceFile with the file
  setEvidenceUrls((prev) => ({ ...prev, [rowId]: file }));
 };
 const handleActionItemsInputChange = (rowId, value) => {
  setActionItems((prev) => ({ ...prev, [rowId]: value }));
 };
 const handleTagsChange = (rowId, tags) => {
  setTags((prev) => ({ ...prev, [rowId]: tags }));
 };
 // //console.log(backEndEvidences);
 const uploadEvidence = async (rowId) => {
  // let fileArray;
  setUploadLoading((prev) => ({ ...prev, [rowId]: true }));
  const formData = new FormData();

  if (evidenceFile[rowId]) {
   if (Array.isArray(evidenceFile[rowId])) {
    evidenceFile[rowId].forEach((file) =>
     formData.append("evidence_files", file)
    );
   } else {
    formData.append("evidence_files", evidenceFile[rowId]);
   }
  }

  if (evidenceFile[rowId]) {
   try {
    let { data: UploadedFile } = await ApiS1Config.post(
     "upload_evidence_files",
     formData,
     {
      headers: {
       "Content-Type": "multipart/form-data",
      },
     }
    );
    setUploadLoading((prev) => ({ ...prev, [rowId]: false }));

    //console.log(UploadedFile);
    showNotification({
     message: "File uploaded successfully",
     color: "green",
    });
    const fileArray = Object.entries(UploadedFile).map(([name, url]) => ({
     name,
     url,
    }));
    setEvidence((prev) => ({ ...prev, [rowId]: fileArray }));
   } catch (error) {
    setUploadLoading((prev) => ({ ...prev, [rowId]: false }));

    //console.log(error);
    showNotification({
     message: "File not uploaded",
     color: "red",
    });
   }
  }
 };
 const collectData = (assessmentName, categoryName) => {
  const data = {
   name: sdgAlignment.name,
   solved: sdgAlignment.solved,
   title: sdgAlignment.title,
   topics: sdgAlignment.topics.map((item) => {
    const questions = item.questions.map((question, idx) => {
     const readinessLevelEntry = Object.entries(ReadinessLevel).find(
      ([key, val]) => val === selectedReadinessLevel[`${item.id}-${idx}`]
     );

     const readinessLevel = readinessLevelEntry
      ? Number(readinessLevelEntry[0])
      : null;

     const selectedPriorityEntry = Object.entries(PriorityLevels).find(
      ([key, val]) => val === selectedPriorityState[`${item.id}-${idx}`]
     );

     const selectedPriority = selectedPriorityEntry
      ? Number(selectedPriorityEntry[0])
      : null;

     return {
      id: question.id,
      questionText: question.questionText,
      CSRDReference: question.CSRDReference,
      readinessLevel:
       readinessLevel === null ? question.readinessLevel : readinessLevel,
      evidence: [
       !evidences[`${item.id}-${idx}`]
        ? (question.evidence && question.evidence[0]) || null
        : evidences[`${item.id}-${idx}`],
       !evidenceUrls[`${item.id}-${idx}`]
        ? (question.evidence && question.evidence[1]) || null
        : evidenceUrls[`${item.id}-${idx}`],
      ],
      priority:
       selectedPriority === null ? question.priority : selectedPriority,
      actionItems: !actionItems[`${item.id}-${idx}`]
       ? question.actionItems
       : [actionItems[`${item.id}-${idx}`]],
      tags: [tags[`${item.id}-${idx}`]] || [],
     };
    });

    return {
     id: item.id,
     name: item.name,
     questions,
    };
   }),
   url: sdgAlignment.url,
  };

  // //console.log(data);
  postAnswers(data, assessmentName, categoryName);
 };

 const rows = sortedData?.map((item) =>
  item.questions.map((question, idx) => {
   const selected = selection?.includes(item.id);
   const rowId = `${item.id}-${idx}`;
   const isFirstQuestion = idx === 0;
   const isDisabled = question.solved === true;

   // //console.log(evidence);
   // //console.log(evidences);
   // const evidenceKey = evidence&&Object.keys(evidence);
   return (
    <React.Fragment key={rowId}>
     <Table.Tr
      className={`${cx({
       ["bg-[#07838F1A]"]: selected,
      })}`}
     >
      {isFirstQuestion && (
       <Table.Td rowSpan={item.questions.length}>
        <Checkbox
         checked={selected}
         onChange={() => toggleRow(item.id)}
         color="#07838F"
        />
       </Table.Td>
      )}

      {/* SDG column */}
      {isFirstQuestion && (
       <Table.Td rowSpan={item.questions.length}>
        <div
         className={`w-36 flex justify-center mx-auto ${
          !edit && isDisabled ? "cursor-not-allowed opacity-50" : ""
         }`}
        >
         <p className={`w-36 text-left`}>{question.SDG}</p>
        </div>
       </Table.Td>
      )}

      {/* Topic Area column */}
      {isFirstQuestion && (
       <Table.Td rowSpan={item.questions.length}>
        <div
         className={`w-36 flex justify-center mx-auto ${
          !edit && isDisabled ? "cursor-not-allowed opacity-50" : ""
         }`}
        >
         <p className={`w-36 text-left`}>{item.name}</p>
        </div>
       </Table.Td>
      )}

      {/* Topic question column */}
      <Table.Td>
       <div
        className={`w-72 text-left mx-auto ${
         !edit && isDisabled ? "cursor-not-allowed opacity-50" : ""
        }`}
       >
        <p className={`w-72`}>{question.questionText}</p>
       </div>
      </Table.Td>

      {/* Readiness Level column */}
      <Table.Td>
       <div
        className={`w-56 text-center mx-auto ${
         !edit && isDisabled ? "cursor-not-allowed opacity-50" : ""
        }`}
       >
        <Select
         defaultSearchValue={ReadinessLevel[question.readinessLevel]}
         value={selectedReadinessLevel[rowId]}
         disabled={!edit && isDisabled ? isDisabled : ""}
         onChange={(value) => {
          handleSelectReadinessChange(rowId, value);
         }}
         classNames={{
          root: "w-fit",
          input: "text-center",
         }}
         radius="xl"
         size="xs"
         className="w-full"
         withCheckIcon={false}
         allowDeselect={false}
         rightSectionWidth="0"
         placeholder="Select your level"
         data={Object.keys(readinessColorMap)}
         styles={(theme) => {
          const readinessLevelValue =
           selectedReadinessLevel[rowId] ||
           ReadinessLevel[question.readinessLevel];
          const readinessStyles = readinessColorMap[readinessLevelValue] || {
           bg: theme.colors.gray[0],
           text: "black",
           border: theme.colors.gray[3],
          };

          return {
           input: {
            backgroundColor: readinessStyles.bg,
            color: readinessStyles.text,
            border: `1px solid ${readinessStyles.border}`,
            padding: "16px 12px",
            borderRadius: "15px",
            fontSize: "14px",
            fontWeight: "500",
            boxShadow: `0 2px 4px rgba(0, 0, 0, 0.1)`,
           },
          };
         }}
        />
       </div>
      </Table.Td>

      {/* Evidence/Notes column */}
      <Table.Td
       className={` text-center ${!value.includes("Evidence") ? "hidden" : ""}`}
      >
       <div
        className={`w-56 text-center mx-auto ${
         !edit && isDisabled ? "cursor-not-allowed opacity-50" : ""
        }`}
       >
        {!backEndEvidences[rowId] &&
        !evidenceFile[rowId] &&
        !evidenceFile[rowId]?.length ? (
         <FileInput
          classNames={{ root: "w-fit", input: "px-12" }}
          leftSection={<MdOutlineFileDownload className="w-5 h-5" />}
          variant="unstyled"
          placeholder="Upload evidence"
          className="w-full bg-[#F2F2F2] mb-3"
          radius="md"
          leftSectionPointerEvents="none"
          disabled={!edit && isDisabled ? isDisabled : ""}
          onChange={(file) => handleFileInputChange(rowId, file)}
          multiple
         />
        ) : (
         <div className="flex items-center justify-around mb-1">
          <div className="text-start w-10/12 ">
           {(backEndEvidences &&
            backEndEvidences[rowId]?.map((item, indx) => (
             <Button
              key={indx}
              variant="subtle"
              href={item?.url}
              target="_blank"
              component="a"
              className="w-full py-0"
             >
              <p key={indx} className="truncate py-0">
               {item.name}
              </p>
             </Button>
            ))) ||
            evidenceFile[rowId]?.map((item, indx) => (
             <p key={indx} className="truncate ">
              {item.name}
             </p>
            ))}
          </div>
          <div className="flex">
           {uploadLoading[rowId] ? (
            <Loading />
           ) : (
            <>
             <IoCloseCircleOutline
              className={` text-red-600 ${
               !edit && isDisabled ? " cursor-not-allowed" : "cursor-pointer"
              }`}
              onClick={() => {
               if (!edit && isDisabled) return;
               if (evidenceFile[rowId] || backEndEvidences[rowId])
                handleFileInputChange(rowId, [], "delete");
               // if (evidence) question.evidence[0] = null;
              }}
             />
             {evidenceFile[rowId] && !evidences[rowId] && (
              <IoIosCheckmarkCircleOutline
               className={` text-green-600 ${
                !edit && isDisabled ? " cursor-not-allowed" : "cursor-pointer"
               }`}
               onClick={() => {
                // if (!edit && isDisabled) return;
                // if (evidenceFile[rowId])
                //   handleFileInputChange(rowId, [], "delete");
                // if (evidence) question.evidence[0] = null;
                uploadEvidence(rowId);
               }}
              />
             )}
            </>
           )}
          </div>
         </div>
        )}
        <TextInput
         classNames={{ root: "w-fit", input: "ps-8 pe-2" }}
         leftSection={<IoLink className="w-5 h-5" />}
         variant="unstyled"
         placeholder="enter an URL"
         className="w-full bg-[#e3f0fd]"
         radius="md"
         leftSectionPointerEvents="none"
         disabled={!edit && isDisabled ? isDisabled : ""}
         onChange={(e) => handleURLInputChange(rowId, e.target.value)}
         defaultValue={question?.evidence && question?.evidence[1]}
        />
       </div>
      </Table.Td>

      {/* Priority column */}
      <Table.Td>
       <div
        className={`w-38 text-center ${
         !edit && isDisabled ? "cursor-not-allowed opacity-50" : ""
        }`}
       >
        <Select
         disabled={!edit && isDisabled ? isDisabled : ""}
         defaultSearchValue={PriorityLevels[question.priority]}
         value={selectedPriorityState[rowId]}
         onChange={(value) => handleSelectPriorityChange(rowId, value)}
         classNames={{
          root: "w-fit",
          input: "text-center",
         }}
         radius="xl"
         size="xs"
         className="w-full"
         withCheckIcon={false}
         allowDeselect={false}
         rightSectionWidth="0"
         placeholder="Priority"
         data={Object.keys(prioritySelectColorMap)}
         styles={(theme) => {
          const priorityLevelValue =
           selectedPriorityState[rowId] || PriorityLevels[question.priority];

          const priorityStyles = prioritySelectColorMap[priorityLevelValue] || {
           bg: "rgba(0, 0, 0, 0.1)",
           text: "black",
          };

          return {
           input: {
            backgroundColor: priorityStyles.bg,
            color: priorityStyles.text,
            fontWeight: "500",
            border: `none`,
            padding: "16px 12px",
            borderRadius: "15px",
            fontSize: "14px",
            boxShadow: `0 2px 4px rgba(0, 0, 0, 0.1)`,
           },
          };
         }}
        />
       </div>
      </Table.Td>

      {/* Key Initiative/Achievement */}
      <Table.Td
       className={`w-1/5 text-center ${
        !value.includes("Key Initiative") ? "hidden" : ""
       }`}
      >
       <div
        className={`w-56 text-center mx-auto ${
         !edit && isDisabled ? "cursor-not-allowed opacity-50" : ""
        }`}
       >
        <p>a. xxx</p>
        <p>b. xxx</p>
        <p>c. xxx</p>
       </div>
      </Table.Td>

      {/* Tags colleagues */}
      <Table.Td
       className={`w-1/5 text-center ${
        !value.includes("Tags") ? "hidden" : ""
       }`}
      >
       <div
        className={`w-56 text-center mx-auto ${
         !edit && isDisabled ? "cursor-not-allowed opacity-50" : ""
        }`}
       >
        <TagsInput
         placeholder="@..."
         maxtags={4}
         disabled={!edit && isDisabled ? isDisabled : ""}
         classNames={{ input: "max-w-96" }}
         onChange={(tags) => handleTagsChange(rowId, tags)}
        />
       </div>
      </Table.Td>

      {/* Action Items columns */}
      <Table.Td
       className={`w-1/5 text-center ${
        !value.includes("Action Items") ? "hidden" : ""
       }`}
      >
       <div
        className={`w-56 text-center mx-auto ${
         !edit && isDisabled ? "cursor-not-allowed opacity-50" : ""
        }`}
       >
        <TagsInput
         placeholder="Press Enter to add item"
         disabled={!edit && isDisabled ? isDisabled : ""}
         classNames={{ input: "w-80" }}
         clearable
         defaultValue={
          Array.isArray(question?.actionItems)
           ? question.actionItems.filter(
              (item) => item && typeof item === "string"
             )
           : []
         }
         onChange={(tags) => handleActionItemsInputChange(rowId, tags)}
        />
       </div>
      </Table.Td>
     </Table.Tr>
    </React.Fragment>
   );
  })
 );
 return (
  <>
   {!search && !sortedData?.length ? (
    <Loading />
   ) : (
    <>
     <h1 className="hidden mb-3 text-center capitalize md:block">
      To scroll Right and left Hold Shift and Scroll using your mouse
     </h1>
     <div className="p-2 my-1 shadow-lg grid items-center  xl:justify-between px-4 bg-white xl:grid-cols-3 rounded-lg w-full">
      <div className="xl:col-span-1 w-full flex justify-center xl:justify-start">
       <Button
        className="text-black bg-transparent hover:bg-transparent hover:text-black border border-gray-600 w-full xl:w-auto"
        onClick={updateEditState}
       >
        <MdModeEdit className="me-1" />
        Edit
       </Button>
      </div>
      <div className="xl:col-span-2 grid items-start  justify-center grid-cols-1 lg:grid-cols-3 xl:grid-cols-4 sm:items-center w-full lg:gap-5">
       <div className="md:col-span-1 hidden xl:flex justify-center items-center m-3  p-2 rounded-lg shadow-sm ms-auto "></div>

       <div className=" md:col-span-1 m-3  bg-[#EBEBEB] rounded-lg shadow-sm md:ms-auto mx-auto w-full">
        <SdgAlignmentFilter setValue={setValue} value={value} />
       </div>

       <TextInput
        className="w-full col-span-2"
        placeholder="Search by Topic Area or Assessment Question"
        rightSection={<CiSearch className="w-5 h-5" />}
        value={search}
        onChange={handleSearchChange}
        // disabled
       />
      </div>
     </div>
     {search && sortedData?.length === 0 ? (
      <h1 className="capitalize text-center mt-5">Your Search is not Found</h1>
     ) : (
      <div className="bg-white p-2 my-1 rounded-xl shadow-lg">
       <Table.ScrollContainer className="scrollable-container" maw={"99%"}>
        <Table
         verticalSpacing="sm"
         horizontalSpacing={""}
         className="scrollable-container"
        >
         <Table.Thead className="pb-6 text-base font-thin">
          <Table.Tr className="text-secondary-500">
           <Table.Th className="">
            <Checkbox
             onChange={toggleAll}
             checked={selection.length === sdgAlignment?.topics.length}
             indeterminate={
              selection.length > 0 &&
              selection.length !== sdgAlignment?.topics.length
             }
             color="#07838F"
            />
           </Table.Th>

           <Table.Th className="text-center cursor-pointer">
            <h1 className="flex items-center justify-center ms-4 gap-3">
             SDG
             {reverseSortDirection === true ? (
              <FaArrowUp className="ms-1 text-[#00C0A9]" />
             ) : (
              <FaArrowDown className="ms-1 text-[#00C0A9]" />
             )}
            </h1>
           </Table.Th>

           <Table.Th
            className="text-center cursor-pointer"
            onClick={() => {
             handleSort("name");
             setReverseSortDirection((prev) => !prev);
            }}
           >
            <h1 className="flex items-center justify-center ms-4 gap-3">
             Topic Area
             {reverseSortDirection === true ? (
              <FaArrowUp className="ms-1 text-[#00C0A9]" />
             ) : (
              <FaArrowDown className="ms-1 text-[#00C0A9]" />
             )}
            </h1>
           </Table.Th>

           <Table.Th className="text-center w-1/4 cursor-pointer">
            <h1 className="flex items-center justify-center ms-4 gap-3">
             Assessment Question
            </h1>
           </Table.Th>

           <Table.Th className="text-center w-1/5">
            <h1 className="flex items-center justify-center ms-4 gap-3">
             Assessment Level
             <FaArrowDown className="ms-1 text-[#00C0A9]" />
            </h1>
           </Table.Th>

           <Table.Th
            className={`w-1/5 text-center ${
             !value.includes("Evidence") ? "hidden" : ""
            }`}
           >
            <h1 className="flex items-center justify-center gap-3 ms-4">
             Evidence/Notes
             <FaArrowDown className="ms-1 text-[#00C0A9]" />
            </h1>
           </Table.Th>

           <Table.Th className="text-center w-1/5">
            <h1 className="flex items-center justify-center ms-4 gap-3">
             Priority
             <FaArrowDown className="ms-1 text-[#00C0A9]" />
            </h1>
           </Table.Th>

           <Table.Th
            className={`w-1/5 text-center ${
             !value.includes("Key Initiative") ? "hidden" : ""
            }`}
           >
            <h1 className="flex items-center justify-center ms- gap-3">
             Key Initiative/Achievement
            </h1>
           </Table.Th>

           <Table.Th
            className={`w-1/5 text-center ${
             !value.includes("Tags") ? "hidden" : ""
            }`}
           >
            <h1 className="flex items-center justify-center ms- gap-3">
             Tag Colleagues
             <FaArrowDown className="ms-1 text-[#00C0A9]" />
            </h1>
           </Table.Th>

           <Table.Th
            className={`w-1/5 text-center ${
             !value.includes("Action Items") ? "hidden" : ""
            }`}
           >
            <h1 className="flex items-center justify-center gap-3 ms-4">
             Action Items
             <FaArrowDown className="ms-1 text-[#00C0A9]" />
            </h1>
           </Table.Th>
          </Table.Tr>
         </Table.Thead>
         <Table.Tbody className="text-gray-600 text-base font-semibold">
          {rows}
         </Table.Tbody>
        </Table>
       </Table.ScrollContainer>
       <div className="flex items-center justify-end">
        <Button
         className="bg-primary text-white hover:bg-primary mt-5"
         onClick={
          postLoading
           ? ""
           : () => {
              collectData("SDG Assessment", "SDG Alignment Assessment");
             }
         }
        >
         {postLoading ? <Loading /> : "Save"}
        </Button>
       </div>
      </div>
     )}
    </>
   )}
  </>
 );
};

export default SdgAlignmentTable;
