pipeline {
     agent any
     options {
        buildDiscarder logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '5', daysToKeepStr: '', numToKeepStr: '5')
            }

        
        environment {
        registryName = "portalACRStaging"
        registryCredential = 'portalACRStaging'
        dockerImage = ''
        registryUrl = 'portalacrstaging.azurecr.io'
    }
    
    stages {

        stage ('checkout') {
            // when {
            //     branch 'main'
            // }
            steps {
            git branch: 'main', credentialsId: 'githubaccesstokenclassic', url: 'https://github.com/levelupesg/esg-diagnostic-assessment'
            //checkout scmGit(branches: [[name: '*/main']], extensions: [], userRemoteConfigs: [[credentialsId: 'githubaccesstokenclassic', url: 'https://github.com/levelupesg/esg-diagnostic-assessment.git']])
            }
        }
       
        stage ('Build Docker image') {
            // when {
            //     branch 'main'
            // }
            steps {
                
                script {
                    dockerImage = docker.build("diagnostic-ui-jenkins:v1", "-f UI/Dockerfile ./UI")
                }
            }
        }
       
    // Uploading Docker images into ACR
    stage('Upload Image to ACR') {
        // when {
        //         branch 'main'
        //     }
     steps{   
         script {
            docker.withRegistry( "http://${registryUrl}", registryCredential ) {
            dockerImage.push()
            }
        }
      }
    }
 }
} 
