import { useState } from "react";
import MainLayout from "@/Layout/MainLayout";

// Import tab components

import SurveyDistribute from "./SurveyDistribute";
import SurveyResults from "./SurveyResults";

export default function SurveyDetails() {
  const [activeTab, setActiveTab] = useState("Distribute");

  const renderTabContent = () => {
    switch (activeTab) {
      case "Distribute":
        return <SurveyDistribute />;
      case "Results":
        return <SurveyResults />;
      default:
        return null;
    }
  };

  return (
    <MainLayout >
        <div className="container px-3 mx-auto my-8 flex flex-col sm:flex-row items-center justify-between ">
    <h1 className="text-2xl font-semibold font-inter leading-none text-primary">
      Survey Settings
    </h1>
      <div className="flex gap-4 mt-4 sm:mt-0">
        {["Distribute", "Results"].map((tab, i) => (
          <button
            key={i}
            onClick={() => setActiveTab(tab)}
            className={`px-4 py-2 rounded border transition ${
              activeTab === tab
                ? "bg-[#e0f0f8] border-[#3b9cc4] font-bold text-[#07838F]"
                : "bg-white border-gray-300"
            }`}
          >
            {tab}
          </button>
        ))}
      </div>
      </div>

      {renderTabContent()}
    </MainLayout>
  );
}
