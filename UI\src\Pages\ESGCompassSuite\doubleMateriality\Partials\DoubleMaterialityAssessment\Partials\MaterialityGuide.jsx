import { AssessmentGuidIcon } from "@/assets/svg/ImageSVG";
import { Button, Modal, ScrollArea } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";

const MaterialityGuide = () => {
  const [opened, { open, close }] = useDisclosure(false);

  const priorityLevels = [
    { singleRange: "≥ 160", doubleRange: "≥ 240", level: "Top Priority" },
    { singleRange: "120 – 159", doubleRange: "180 – 239", level: "High Priority" },
    { singleRange: "80 – 119", doubleRange: "120 – 179", level: "Medium Priority" },
    { singleRange: "< 80", doubleRange: "< 120", level: "Low Priority" },
  ];

  const features = [
    "Click topics in the list to select them for assessment",
    "Filter topics by category (Environmental, Social, Governance, Economic)",
    "View selected topics in both matrix and table format",
    "Export selected topics to Excel using the Export button"
  ];

  const bestPractices = [
    "Review your double materiality assessment regularly",
    "Involve diverse stakeholders in the assessment process",
    "Use the matrix to inform strategic decision-making and sustainability reporting",
    "Focus resources on high and top priority topics",
    "Compare results against industry benchmarks"
  ];

  const colorCodes = [
    { color: "orange-400", label: "Social" },
    { color: "green-500", label: "Environmental" },
    { color: "blue-500", label: "Governance" },
    { color: "yellow-400", label: "Economic" }
  ]

  return (
    <>
      <div className="flex justify-end mb-5">
        <Button
          className="text-[#07838F] flex gap-3 bg-[#07838F1A] rounded-full underline font-semibold"
          variant="transparent"
          onClick={open}
        >
          <AssessmentGuidIcon /> Assessment guide
        </Button>
      </div>
      <Modal
        size={"90%"}
        opened={opened}
        onClose={close}
        withCloseButton={true}
        scrollAreaComponent={ScrollArea.Autosize}
        className="rounded-2xl"
      >
            <div className="max-w-4xl mx-auto p-6 font-sans">
      <h1 className="text-3xl font-bold text-gray-800 mb-6">
        Double Materiality Assessment User Guide
      </h1>

      <section className="mb-8">
        <h2 className="text-xl font-semibold text-gray-700 mb-4">Overview</h2>
        <p className="text-gray-600 mb-4">
          The Materiality Assessment tool helps organisations identify and prioritize sustainability topics based on their impact on the business and importance to stakeholders. This guide explains how to use the Double Materiality Assessment tool effectively.
        </p>
      </section>

      <section className="mb-8">
        <h2 className="text-xl font-semibold text-gray-700 mb-4">Getting Started</h2>
        
        <h3 className="text-lg font-medium text-gray-700 mb-3">Understanding the Matrix</h3>
        <p className="text-gray-600 mb-4">
          The Double Materiality Assessment is presented as an interactive matrix where each topic receives assessment <strong>scores from 0 to 100</strong>:
        </p>
        <ul className="list-disc pl-6 text-gray-600 mb-4">
          <li className="mb-1"><strong>X-axis:</strong> Impact on Environment, Social and Governance (Low to High)</li>
          <li className="mb-1"><strong>Y-axis:</strong> Impact on Business (Low to High)</li>
          <li className="mb-1"><strong>Colour coding:</strong> Topics are represented by coloured dots based on category:</li>
        </ul>
        
        <div className="flex flex-col gap-4 mb-6">
          {colorCodes.map((item, index) => (
            <div key={index} className="flex items-center">
              <div className={`w-2 h-2 rounded-full bg-gray-600 mr-2`}></div>
              <span className="text-gray-600">{item.label}</span>
            </div>
          ))}
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-xl font-semibold text-gray-700 mb-4">Priority Calculation Table</h2>
        <table className="w-full border-collapse mb-6">
          <thead>
            <tr className="bg-gray-100">
              <th className="border border-gray-300 p-3 text-left text-gray-700">Total Score Range - Single Materiality</th>
              <th className="border border-gray-300 p-3 text-left text-gray-700">Total Score Range - Double Materiality</th>
              <th className="border border-gray-300 p-3 text-left text-gray-700">Priority Level</th>
            </tr>
          </thead>
          <tbody>
            {priorityLevels.map((item, index) => (
              <tr key={index}>
                <td className="border border-gray-300 p-3 text-gray-600">{item.singleRange}</td>
                <td className="border border-gray-300 p-3 text-gray-600">{item.doubleRange}</td>
                <td className="border border-gray-300 p-3 font-medium text-gray-700">{item.level}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </section>

      <section className="mb-8">
        <h2 className="text-xl font-semibold text-gray-700 mb-4">Key Features</h2>
        <h3 className="text-lg font-medium text-gray-700 mb-3">Topic Management</h3>
        <ul className="list-disc pl-6 text-gray-600 mb-4">
          {features.map((item, index) => (
            <li key={index} className="mb-1">{item}</li>
          ))}
        </ul>
      </section>

      <section className="mb-8">
        <h2 className="text-xl font-semibold text-gray-700 mb-4">Best Practices</h2>
        <ul className="list-disc pl-6 text-gray-600">
          {bestPractices.map((item, index) => (
            <li key={index} className="mb-1">{item}</li>
          ))}
        </ul>
      </section>
    </div>

      </Modal>
    </>
  );
};

export default MaterialityGuide;
