import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import sectionImage9 from "@/assets/images/loginAIbk.png";
import Logo from "@/assets/images/circular-primary-logo.png";
import { useAuth } from "@/Contexts/AuthContext";
import { useNavigate } from "react-router";
import { Button } from "@mantine/core";
import { FaRegEye, FaRegEyeSlash } from "react-icons/fa";

const Login = () => {
 const { t } = useTranslation();
 const { login, token } = useAuth();
 const [loading, setLoading] = useState(false);
 const navigate = useNavigate();
 const [showPassword, setShowPassword] = useState(false);
 const [rememberMe, setRememberMe] = useState(false);
 const [loginState, setLoginState] = useState({
  user_email: "",
  password: "",
  rememberMe,
 });

 // Load saved credentials if available

 useEffect(() => {
  if (token) return navigate("/get-started");
 }, [token, navigate]);

 useEffect(() => {
  function handleKeyPress(e) {
   if (e.key === "Enter") {
    handleLogin();
   }
  }

  window.addEventListener("keypress", handleKeyPress);

  return () => {
   window.removeEventListener("keypress", handleKeyPress);
  };
 }, [loginState, login]);

 const loginInputHandle = (e) => {
  const { name, value } = e.target;
  setLoginState((prevState) => ({
   ...prevState,
   [name]: value,
  }));
 };

 const togglePasswordVisibility = () => {
  setShowPassword(!showPassword);
 };

 const handleRememberMeChange = () => {
  setRememberMe(!rememberMe);
 };

 const handleLogin = () => {
  setLoading(true);
  
  login(loginState, rememberMe) // Pass rememberMe flag to your login handler
    .finally(() => setLoading(false));
 };

  return (
    <section className="flex items-center justify-center min-h-screen px-4 py-8 bg-white relative">
      <div className="absolute inset-0 bg-[linear-gradient(#1C889C_1px,transparent_1px),linear-gradient(90deg,#1C889C_1px,transparent_1px)] bg-[size:100px_100px] opacity-10"></div>
      <div className="w-full max-w-8xl flex flex-col justify-around md:flex-row gap-4 md:gap-6 p-14 overflow-hidden relative z-10">
        {/* Left Section (Login Form) */}
        <div className="w-full md:w-1/3 basis-full md:basis-2/5 rounded-xl shadow-lg m-4 bg-white p-6 md:p-10 flex flex-col justify-center">
          <div className="flex flex-col h-full">
            <h1 className="flex items-center gap-2">
              <img className="w-12 md:w-16" src={Logo} alt="LevelUp ESG Logo" />
              <span className="text-xl md:text-2xl font-bold text-primary">
                LevelUp ESG<sup>&reg;</sup>
              </span>
            </h1>

            <h2 className="my-4 md:my-6 pb-2 md:pb-4 text-xl md:text-2xl font-medium text-center text-[#525252]">
              {t("Connect to Your LevelUp ESG")} <sup>&reg;</sup> <br/> {t("Intelligent Platform")}
            </h2>

            <div className="flex flex-col mb-4">
              <label className="font-bold text-[#272727]" htmlFor="email">
                {t("login.email")}
              </label>
              <input
                value={loginState.user_email}
                onChange={loginInputHandle}
                className="p-3 mt-1 border rounded-md focus:border-primary outline-0"
                type="email"
                name="user_email"
                placeholder="Enter your registered email"
                id="email"
              />
            </div>

            <div className="flex flex-col mb-4">
              <label className="font-bold text-[#272727]" htmlFor="password">
                {t("login.password")}
              </label>
              <div className="flex border rounded-md items-center justify-between">
                <input
                  value={loginState.password}
                  onChange={loginInputHandle}
                  className="p-3 w-full rounded-md focus:border-primary outline-0"
                  type={showPassword ? "text" : "password"}
                  name="password"
                  placeholder="Enter your password"
                  id="password"
                />
                <div className="flex mr-4 cursor-pointer" onClick={togglePasswordVisibility}>
                  {showPassword ? 
                    <FaRegEye className="w-5 h-5 md:w-6 md:h-6 text-primary"/> : 
                    <FaRegEyeSlash className="w-5 h-5 md:w-6 md:h-6 text-gray-600"/>
                  }
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <input 
                type="checkbox" 
                id="rememberMe"
                checked={rememberMe}
                onChange={handleRememberMeChange}
                className="focus:ring-primary w-4 h-4 cursor-pointer accent-primary"
              />
              <label htmlFor="rememberMe" className="font-medium text-[#666666] cursor-pointer">
                Remember me
              </label>
            </div>

            <div className="flex items-center justify-center gap-3 mt-6">
              <Button
                disabled={loading}
                loading={loading}
                onClick={handleLogin}
                className="w-full text-lg text-white rounded-md bg-primary hover:bg-secondary"
              >
                {t("login.loginButton")}
              </Button>
            </div>

            <p className="mt-5 text-sm text-center">
              {t("login.terms")}
              <a
                className="pl-1 font-semibold underline underline-offset-2"
                href="https://levelupesg.co/legal/privacy-policy"
              >
                {t("login.privacyPolicy")}
              </a>
            </p>
          </div>
        </div>
        
        {/* Right Section (Image) */}
        <div className="w-full md:w-1/3 basis-full md:basis-2/5 overflow-hidden rounded-xl flex items-center justify-center">
          <img 
            src={sectionImage9} 
            className="w-full h-full object-contain" 
            alt="AI photo" 
          />
        </div>
      </div>
    </section>
  );
};

export default Login;