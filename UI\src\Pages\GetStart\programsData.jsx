import {
  AntiGreenwashingHomeIcon,
  ConnectorsHomeIcon,
  CSRDReadinessHomeIcon,
  DecarbonisationHomeIcon,
  DecisionSightHomeIcon,
  DocVaultHomeIcon,
  DoubleMaterialityHomeIcon,
  EmissionsCalculationHomeIcon,
  ESGDueDiligenceHomeIcon,
  FinancedEmissionsHomeIcon,
  GreenHubHomeIcon,
  GreenSightHomeIcon,
  HumanRightsDueDiligenceHomeIcon,
  IntegratedRiskHomeIcon,
  // ISSBReadinessHomeIcon,
  SDGAlignmentHomeIcon,
  StakeholderEngagementHomeIcon,
  SupplyChainHomeIcon,
  NewIssbReportingSuiteIcon,
  DataValidationIcon,
  FinanceGuardIcon,
  ChainSightIcon,
  // TNFDIcon,
} from "@/assets/icons";

export const getProgramsData = (auth) => {
  const {
    S1CurrentAssessment,
    S1Access: S1,
    S2Access: S2,
    S3Access: S3,
    EnterpriseBackboneAccess: EnterpriseBackbone,
  } = auth;

  return [
    {
      number: 2,
      isActive: S2 && Object.values(S2).includes(true),
      title: "Carbon Intelligence",
      // supTitle: 'Powered by LevelUp NetZero',
      titleColor: "#e3f3f4",
      children: [
        {
          title: "Emissions Calculation",
          supTitle: "Track and calculate your carbon footprint",
          link: "/net-zero/general",
          icon: <EmissionsCalculationHomeIcon />,
          isActive: S2 && S2["Emissions Calculation"],
        },
        {
          title: "Scope3Connect",
          supTitle: "Manage Scope 3 emissions data",
          link: "/net-zero/suppliers/Add-Supplier",
          icon: <SupplyChainHomeIcon />,
          isActive: S2 && S2["Supply Chain"],
        },
        {
          title: "Decarbonisation",
          link: "/net-zero/decarbonize",
          supTitle: "Plan and track emissions reduction",
          icon: <DecarbonisationHomeIcon />,
          isActive: S2 && S2["Decarbonisation"],
        },
        {
          title: "Financed Emissions",
          link: "/net-zero/financed-emissions",
          supTitle: "PCAF-aligned emission analysis",
          icon: <FinancedEmissionsHomeIcon />,
          isActive: "S2 && S2['Financed Emissions']",
        },
      ],
    },
    {
      number: 1,
      isActive: S1 && Object.values(S1).includes(true),
      title: "ESG Insights & Reporting",
      // supTitle: 'Powered by ESG Compass Suite',
      titleColor: "#cbe6e9",
      children: [
        {
          title: "Sustain360",
          supTitle: "AI-powered ESG performance insights",
          link: "/Insights-reporing/greensight",
          icon: <GreenSightHomeIcon />,
          isActive: S1 && S1["GreenSight AI"],
          // havCurrentAssessment:
          //   (S1CurrentAssessment && S1CurrentAssessment["GreenSight AI"]) ||
          //   false,
          assessmentType: "GreenSight AI",
          path: "/Insights-reporing/greensight",
          summaryPath: "/Insights-reporing/greensight",
          // popup: true,
        },
        {
          title: "Stakeholder Engagement",
          supTitle: "Protected and engaging stakeholder communications",
          link: "/Insights-reporing/stakholder-engagment",
          icon: <StakeholderEngagementHomeIcon />,
          isActive: S1 && S1["Stakeholder Engagement"],
        },
        {
          title: "Materiality Assessment",
          supTitle: "Identify and prioritise material ESG topics",
          link: "/Insights-reporing/materiality-assessment",
          icon: <DoubleMaterialityHomeIcon />,
          isActive: S1 && S1["Double Materiality"],
          havCurrentAssessment:
            (S1CurrentAssessment &&
              S1CurrentAssessment["Double Materiality"]) ||
            false,
          assessmentType: "Double Materiality",
          summaryPath: "/Insights-reporing/materiality-assessment",
          popup: true,
        },
        {
          title: "FinanceGuard",
          supTitle:
            "Comprehensive IFC/GCF/ Equator Principles  Assessment & Reporting",
          link: "/Insights-reporing/financeguard",
          icon: <FinanceGuardIcon />,
          isActive: S1 && S1["CSRD Readiness"],
          havCurrentAssessment:
            (S1CurrentAssessment && S1CurrentAssessment["CSRD Readiness"]) ||
            false,
          assessmentType: "CSRD Readiness",
          summaryPath: "/Insights-reporing/financeguard",
          popup: true,
        },
        {
          title: "ESG Reporting Suite",
          supTitle: "Streamline reporting across frameworks and standards",
          link: "/Insights-reporing/issb-reporting-suite",
          icon: <NewIssbReportingSuiteIcon />,
          isActive: S1 && S1["ISSB Readiness"],
          havCurrentAssessment:
            (S1CurrentAssessment && S1CurrentAssessment["ISSB Readiness"]) ||
            false,
          assessmentType: "ISSB Readiness",
          summaryPath: "/Insights-reporing/issb-reporting-suite",
          popup: true,
        },
        {
          title: "SDG & Impact",
          supTitle: "Track contributions to UN SDGs",
          link: "/Insights-reporing/sdg-impact",
          icon: <SDGAlignmentHomeIcon />,
          isActive: S1 && S1["SDG Alignment & Impact Measurement"],
          havCurrentAssessment:
            (S1CurrentAssessment && S1CurrentAssessment["SDG Assessment"]) ||
            false,
          assessmentType: "SDG Assessment",
          summaryPath: "/Insights-reporing/sdg-impact",
          popup: true,
        },
        // {
        //   title: 'Anti-greenwashing Rule',
        //   supTitle: 'Verify sustainability claims',
        //   link: '/green-shield/compilance/CompilanceView',
        //   icon: <AntiGreenwashingHomeIcon />,
        //   isActive: S3 && S3['Anti-greenwashing Compliance'],
        // },
        {
          title: "DecisionSight",
          supTitle: "Transform data into actionable strategic insights",
          link: "/dashboard/DecisionSight",
          icon: <DecisionSightHomeIcon />,
          isActive: EnterpriseBackbone && EnterpriseBackbone["DecisionSight"],
          havCurrentAssessment:
            (S1CurrentAssessment && S1CurrentAssessment["DecisionSight"]) ||
            false,
          assessmentType: "DecisionSight",
          summaryPath: "/Grc/DecisionSight",
        },
      ],
    },
    {
      number: 3,
      isActive: S3 && Object.values(S3).includes(true),
      title: "Risk & Compliance Management",
      // supTitle: 'Powered by GreenShield',
      titleColor: "#b2dadd",
      children: [
        {
          title: "Integrated Risk Management",
          supTitle: "Holistically manage risks and strengthen resilience",
          link: "/green-shield/financial/ESG-risk-management/main",
          icon: <IntegratedRiskHomeIcon />,
          isActive: S3 && S3["Integrated Risk Management"],
        },
        {
          title: "Regulatory Readiness",
          supTitle: "Comply with ISSB, CSRD & global standards",
          link: "/Grc/regulatory-readiness",
          icon: <CSRDReadinessHomeIcon />,
          isActive: S1 && S1["CSRD Readiness"],
          havCurrentAssessment:
            (S1CurrentAssessment && S1CurrentAssessment["CSRD Readiness"]) ||
            false,
          assessmentType: "CSRD Readiness",
          summaryPath: "/Grc/regulatory-readiness",
          popup: true,
        },
        {
          title: "Due Diligence",
          supTitle:
            "Assess and mitigate human rights and third-party ESG compliance risks",
          link: "/Grc/due-diligence",
          icon: <ESGDueDiligenceHomeIcon />,
          isActive: S3 && S3["ESG Due Diligence"],
          havCurrentAssessment:
            (S1CurrentAssessment && S1CurrentAssessment["ESG Due Diligence"]) ||
            false,
          assessmentType: "ESG Due Diligence",
          summaryPath: "/Grc/due-diligence",
          popup: true,
        },
        {
          title: "ChainSight",
          supTitle: "GRI & ISO-aligned sustainable supply chain assessment",
          link: "/Grc/chain-sight",
          icon: <ChainSightIcon />,
          isActive: S3 && S3["Human Rights Due Diligence"],
          havCurrentAssessment:
            (S1CurrentAssessment &&
              S1CurrentAssessment["Human Rights Due Diligence (HRDD)"]) ||
            false,
          assessmentType: "Human Rights Due Diligence (HRDD)",
          summaryPath: "/Grc/chain-sight",
          popup: true,
        },
        // {
        //   title: 'DecisionSight',
        //   supTitle: 'Strategic ESG insights',
        //   link: '/DecisionSight/Dashboard',
        //   icon: <DecisionSightHomeIcon />,
        //   isActive: EnterpriseBackbone && EnterpriseBackbone['DecisionSight'],
        //   havCurrentAssessment: (S1CurrentAssessment && S1CurrentAssessment['DecisionSight']) || false,
        //   assessmentType: 'DecisionSight',
        //   summaryPath: '/DecisionSight/Dashboard',
        // },
        {
          title: "Anti-greenwashing Rule",
          supTitle: "Comply with FCA anti-greenwashing regulation",
          link: "/Grc/anti-green-washing",
          icon: <AntiGreenwashingHomeIcon />,
          isActive: S3 && S3["Anti-greenwashing Compliance"],
        },
      ],
    },
    {
      number: 4,
      isActive:
        EnterpriseBackbone && Object.values(EnterpriseBackbone)?.includes(true),
      title: "Data Foundation",
      // supTitle: 'Powered by Enterprise Backbone',
      titleColor: "#9acdd2",
      children: [
        {
          title: "Data Collection",
          supTitle: "Unifying data sources",
          link: "/Data-foundation/data-collection",
          icon: <ConnectorsHomeIcon />,
          isActive: EnterpriseBackbone && EnterpriseBackbone["Connectors"],
        },
        {
          title: "Data Validation",
          supTitle: "Data integrity at every step",
          link: "/net-zero/data-validation",
          icon: <DataValidationIcon />,
          isActive: EnterpriseBackbone && EnterpriseBackbone["Connectors"],
        },
        // {
        //   title: 'DocVault',
        //   supTitle: 'Document management',
        //   // abslouteLink: 'http://40.81.137.64:8000/',
        //   link: '/docvault',
        //   icon: <DocVaultHomeIcon />,
        //   // isActive: EnterpriseBackbone && EnterpriseBackbone['DocVault'],
        //   isActive: true,
        // },
        // {
        //   title: 'GreenHub',
        //   supTitle: 'Sustainability knowledge center',
        //   link: '/green-hub',
        //   icon: <GreenHubHomeIcon />,
        //   isActive: EnterpriseBackbone && Object.values(EnterpriseBackbone?.['GreenHub'])?.includes(true),
        // },
      ],
    },
    {
      number: 5,
      isActive: true,
      title: "Document Center",
      titleColor: "#9acdd2",
      children: [
        {
          title: "DocVault",
          supTitle: "Document management",
          // abslouteLink: 'http://40.81.137.64:8000/',
          link: "/Document-Center/docvault",
          icon: <DocVaultHomeIcon />,
          // isActive: EnterpriseBackbone && EnterpriseBackbone['DocVault'],
          isActive: true,
        },
        // {
        //   title: 'GreenHub',
        //   supTitle: 'Sustainability knowledge center',
        //   link: '/green-hub',
        //   icon: <GreenHubHomeIcon />,
        //   isActive: EnterpriseBackbone && Object.values(EnterpriseBackbone?.['GreenHub'])?.includes(true),
        // },
      ],
    },
    {
      number: 6,
      isActive: true,
      title: "Knowledge Hub",
      titleColor: "#9acdd2",
      children: [
        {
          title: "GreenHub",
          supTitle: "Sustainability knowledge center",
          link: "/green-shield/main-page",
          icon: <GreenHubHomeIcon />,
          isActive:
            EnterpriseBackbone &&
            Object.values(EnterpriseBackbone?.["GreenHub"])?.includes(true),
        },
      ],
    },
  ];
};
