import { useTranslation } from "react-i18next";
import { useState } from "react";
import { Button, Tabs } from "@mantine/core";

import { AnomomlisIcon, AssessmentGuidIcon, DataQualityIcon } from "@/assets/svg/ImageSVG";
import MainLayout from "@/Layout/MainLayout";
import { IoMdHome } from "react-icons/io";
import { useDisclosure } from "@mantine/hooks";
import {  Modal, ScrollArea } from "@mantine/core";
import DataQualityJuid from "./Partials/DataQualityJuid";
import GeneralDataQuality from "./Partials/GeneralDataQuality";
export default function DataValidation() {
  const { t } = useTranslation();
  const [active, setActive] = useState("General Data Quality");
  const [opened, { open, close }] = useDisclosure(false);
  return (
    <MainLayout
      navbarTitle={"Data Validation"}
      breadcrumbItems={[
        { title: <IoMdHome size={20} />, href: "/get-started" },
        { title: "Data Validation", href: "#" },
      ]}
    >
      <div className="flex flex-col">
        {active === "General Data Quality" && (
          <div>
            <div className="flex justify-start px-6">
              <Button
                className="text-[#07838F] bg-[#07838F1A] rounded-full underline font-semibold"
                variant="transparent"
                onClick={open}
              >
                <AssessmentGuidIcon /> Assessment guide
              </Button>
            </div>
            <Modal
              size={"90%"}
              opened={opened}
              onClose={close}
              withCloseButton={true}
              scrollAreaComponent={ScrollArea.Autosize}
              className="rounded-2xl"
            >
              <DataQualityJuid />
            </Modal>
          </div>
        )}

        <Tabs
          defaultValue="General Data Quality"
          variant="none"
          className="py-10 "
        >
          <Tabs.List className="grid md:grid-cols-2 justify-center bg-[#FFFFFF] border-2 border-[#E8E7EA] p-6 gap-5 py-3 mb-6 rounded-lg text-primary">
            <Tabs.Tab
              value="General Data Quality"
              leftSection={
                <DataQualityIcon
                  color={active === "General Data Quality" ? "" : "#5A5A5A"}
                />
              }
              className={`relative text-lg font-semibold py-3 px-6 transition-all  ${
                active === "General Data Quality"
                  ? "active-tab rounded"
                  : " text-[#5A5A5A]  bg-[#FFFFFF] rounded-lg border-[#E8E7EA] border-2"
              }`}
              onClick={() => {
                setActive("General Data Quality");
              }}
            >
              {t("General Data Quality")}
            </Tabs.Tab>

            <Tabs.Tab
              value="General Anomalies"
              leftSection={
                <AnomomlisIcon
                  color={active === "General Anomalies" ? "#07838F" : ""}
                />
              }
              className={`relative text-lg font-semibold py-3 px-6 transition-all  ${
                active === "General Anomalies"
                  ? "active-tab rounded"
                  : " text-[#5A5A5A]  bg-[#FFFFFF] rounded-lg border-[#E8E7EA] border-2"
              }`}
              onClick={() => {
                setActive("General Anomalies");
              }}
            >
              {t("General Anomalies")}
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="General Data Quality">
              <GeneralDataQuality />
          </Tabs.Panel>
        </Tabs>


      </div>
    </MainLayout>
  );
}
