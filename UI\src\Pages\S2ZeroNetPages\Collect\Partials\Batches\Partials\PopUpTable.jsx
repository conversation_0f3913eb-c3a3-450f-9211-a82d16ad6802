import useFormatTextKey from "@/hooks/useFormatTextKey";
import { Button, ScrollArea, Table, TextInput } from "@mantine/core";
import cx from "clsx";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { CiSearch } from "react-icons/ci";

export default function PopUpTable({ data, Status, assetTypeAll }) {
  const { t } = useTranslation();
  const [search, setSearch] = useState("");
  const [sortedData, setSortedData] = useState();
  useEffect(() => {
    setSortedData(data);
  }, [data]);

  const handleSearchChange = (event) => {
    const value = event;
    setSearch(value);
    if (value === "") {
      setSortedData(data);
      return;
    }

    const filteredData = sortedData?.filter((data) =>
      data.items.some(
        (item) =>
          item.companyAsset.assetName
            .toLowerCase()
            .includes(search.toLowerCase()) ||
          item.customFactor.activity
            .toLowerCase()
            .includes(search.toLowerCase()) ||
          item.customFactor.eFactors
            .toLowerCase()
            .includes(search.toLowerCase()) ||
          item.customFactor.uom.toLowerCase().includes(search.toLowerCase()) ||
          item.reportingYear.toString().includes(search)
      )
    );

    setSortedData(filteredData);
  };

  const rows = sortedData?.map((item) =>
    item.items.map((items) => {
      const del = (e) => {
        e.target.closest("tr").remove();
      };
      const assets = assetTypeAll.find(
        (asset) => asset.id === items?.customFactor?.emissionSourceId
      );
      const assetType = assets?.asset;
      const evidence = item?.uploadFileLinks?.template;
      const evidenceKey = Object.keys(evidence);

      return (
        <Table.Tr
          key={item.id}
          className={`${cx({
            // ["bg-[#07838F1A]"]: selected,
          })} text-sm font-bold text-[#626364] text-center`}
        >
          <Table.Td>
            <div className="w-20 block mx-auto">
              <p className="">
                {item.uploadedDT?.split("T")[1]?.substring(0, 5) || "Not Found"}
              </p>
            </div>
          </Table.Td>
          <Table.Td>
            <div className="w-20 block mx-auto">
              <p className="">{items.fromDate?.split(" ")[0] || "Not Found"}</p>
            </div>
          </Table.Td>
          <Table.Td>
            <div className="w-20 block mx-auto">
              <p className="">{items.toDate?.split(" ")[0] || "Not Found"}</p>
            </div>
          </Table.Td>
          <Table.Td>
            <div className="w-32 block mx-auto">
              <p className="">{assetType || "Not Found"}</p>
            </div>
          </Table.Td>
          <Table.Td>
            <div className="w-24 block mx-auto">
              <p className="">
                {items?.companyAsset?.assetName || "Not Found"}
              </p>
            </div>
          </Table.Td>
          <Table.Td>
            <div className="block mx-auto min-w-36">
              {items?.customFactor?.activity &&
                Object.entries(items?.customFactor?.activity).map(
                  ([key, value]) => (
                    <p className="mt-2">
                      {useFormatTextKey(key)}: {value}
                    </p>
                  )
                )}
            </div>
          </Table.Td>
          <Table.Td>
            <div className="block mx-auto min-w-36">
              {/* <p className="">{eFactors}</p> */}
              {items?.customFactor?.eFactors &&
                Object.entries(items?.customFactor?.eFactors).map(
                  ([key, value]) => (
                    <p className="mt-2">
                      {useFormatTextKey(key)}: {value}
                    </p>
                  )
                )}
            </div>
          </Table.Td>
          <Table.Td>
            <div className="block mx-auto text-nowrap min-w-36">
              {/* <p className="">{uom}</p> */}
              {items?.customFactor?.uom &&
                Object.entries(items?.customFactor?.uom).map(([key, value]) => (
                  <p className="mt-2">
                    {useFormatTextKey(key)}: {value}
                  </p>
                ))}
            </div>
          </Table.Td>
          <Table.Td>
            <div className="block mx-auto min-w-36">
              {/* <p className="">{quantity}</p> */}
              {items?.quantity &&
                Object.entries(items?.quantity).map(([key, value]) => (
                  <p className="mt-2">
                    {useFormatTextKey(key)}: {value}
                  </p>
                ))}
            </div>
          </Table.Td>
          <Table.Td>
            <div className="w-24 block mx-auto">
              <p className="">{items?.reportingYear || "Not Found"}</p>
            </div>
          </Table.Td>
          <Table.Td>
            <div className=" block mx-auto">
              <p
                className={`py-2 px-10 rounded-2xl`}
                style={{
                  backgroundColor:
                    item.reviewState === null
                      ? Status.Pending.bg
                      : item.reviewState === true
                      ? Status.Accepted.bg
                      : item.reviewState === false
                      ? Status.Rejected.bg
                      : "",
                  color:
                    item.reviewState === null
                      ? Status.Pending.text
                      : item.reviewState === true
                      ? Status.Accepted.text
                      : item.reviewState === false
                      ? Status.Rejected.text
                      : "",
                }}
              >
                {item.reviewState === null
                  ? "Pending"
                  : item.reviewState === true
                  ? "Accepted"
                  : item.reviewState === false
                  ? "Rejected"
                  : "Not Found"}
              </p>
            </div>
          </Table.Td>
          <Table.Td>
            <div className="">
              <p className="w-full">
                {evidenceKey.map((item, idx) => (
                  <Button
                    key={idx}
                    variant="subtle"
                    href={evidence[item]}
                    target="_blank"
                    component="a"
                  >
                    {item}
                  </Button>
                )) || "Not Found"}
              </p>
            </div>
          </Table.Td>
        </Table.Tr>
      );
    })
  );

  return (
    <div className="mt-7">
      <div className="p-2 my-1 shadow-lg grid items-center  xl:justify-between px-4 bg-white xl:grid-cols-3 rounded-lg w-full">
        <div className="xl:col-span-1 w-full flex justify-center xl:justify-start"></div>
        <div className="xl:col-span-2 grid items-start  justify-center grid-cols-1 lg:grid-cols-3 xl:grid-cols-4 sm:items-center w-full lg:gap-5">
          <div className="md:col-span-1 hidden xl:flex justify-center items-center m-3  p-2 rounded-lg shadow-sm ms-auto "></div>

          <div className=" md:col-span-1 m-3  bg-[#EBEBEB] rounded-lg shadow-sm md:ms-auto mx-auto w-full">
            {/* <CollectMultiselectFilter setValue={setValue} value={value} /> */}
          </div>

          <TextInput
            className="w-full col-span-2"
            placeholder="Search by Topic Area or Assessment Question"
            rightSection={<CiSearch className="w-5 h-5" />}
            value={search}
            onChange={(e) => handleSearchChange(e.target.value)}
            // disabled
          />
        </div>
      </div>
      <>
        {search && sortedData?.length === 0 ? (
          <h1 className="mt-5 text-center capitalize">
            Your Search is not Found
          </h1>
        ) : (
          <>
            <ScrollArea>
              <Table
                // miw={800}
                verticalSpacing="sm"
                className="p-2 my-1 bg-white shadow-lg rounded-xl"
              >
                <Table.Thead className="border-b-2 border pb-6 text-secondary-500 font-bold text-base text-center">
                  <Table.Tr>
                    {/* <Table.Th className="text-center">Delete</Table.Th>
              <Table.Th>
                <Checkbox
                  //   onChange={toggleAll}
                  checked={selection?.length === data?.length}
                  indeterminate={
                    selection?.length > 0 && selection?.length !== data?.length
                  }
                  color="#07838F"
                />
              </Table.Th> */}
                    <Table.Th className="text-center">Timestamp</Table.Th>
                    <Table.Th className="text-center">From</Table.Th>
                    <Table.Th className="text-center">To</Table.Th>
                    <Table.Th className="text-center">Assets Type</Table.Th>
                    <Table.Th className="text-center">Assets</Table.Th>
                    <Table.Th className="text-center">Activity</Table.Th>
                    <Table.Th className="text-center">E-Factors</Table.Th>
                    <Table.Th className="text-center">UOM</Table.Th>
                    <Table.Th className="text-center">Quantity</Table.Th>
                    <Table.Th className="text-center text-nowrap">
                      Reporting Year
                    </Table.Th>
                    <Table.Th className="text-center text-nowrap">
                      Status
                    </Table.Th>
                    <Table.Th className="text-center">Evidence/Notes</Table.Th>
                    {/* <Table.Th className="text-center">
                <span className="flex gap-3 justify-center">
                  <span className="bg-white text-[#404B52] rounded-md w-8 h-8 flex justify-center items-center text-sm font-medium cursor-pointer">
                    <MdKeyboardArrowLeft />
                  </span>
                  <span className="bg-white text-[#404B52] rounded-md w-8 h-8 flex justify-center items-center text-sm font-medium cursor-pointer">
                    <MdKeyboardArrowRight />
                  </span>
                </span>
              </Table.Th> */}
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{rows}</Table.Tbody>
              </Table>
            </ScrollArea>
          </>
        )}
      </>
    </div>
  );
}
