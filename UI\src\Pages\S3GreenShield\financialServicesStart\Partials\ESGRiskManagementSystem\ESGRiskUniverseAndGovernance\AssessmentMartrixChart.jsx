import React, { useState } from "react";
import ReactApexChart from "react-apexcharts";
// import generateData from "./HeatMap/generateData";
const getChartOptions = (highlightValue) => ({
    chart: {
      height: 350,
      type: "heatmap",
    },
    plotOptions: {
      heatmap: {
        shadeIntensity: 0,
        radius: 0,
        useFillColorAsStroke: false,
        colorScale: {
          ranges: [
            { from: 1, to: 3, name: "Low", color: "#86FFB0" },
            { from: 4, to: 9, name: "Medium", color: "#FEF089" },
            { from: 10, to: 15, name: "High", color: "#FFC787" },
            { from: 16, to: 25, name: "Critical", color: "#FBC8C8" },
          ],
        },
      },
    },
    tooltip: {
      enabled: true,
      custom: function ({ series, seriesIndex, dataPointIndex, w }) {
        const value = w.globals.series[seriesIndex][dataPointIndex];
        let riskLevel = '';
        if (value >= 1 && value <= 3) riskLevel = 'Low Risk';
        else if (value >= 4 && value <= 9) riskLevel = 'Medium Risk';
        else if (value >= 10 && value <= 15) riskLevel = 'High Risk';
        else if (value >= 16 && value <= 25) riskLevel = 'Critical Risk';
  
        const isHighlighted = value === highlightValue;
        const highlightStyle = isHighlighted ? 'font-weight: bold; color: #FF0000;' : '';
  
        return (
          `<div class="arrow_box">
            <span style="${highlightStyle}">
              ${w.globals.labels[dataPointIndex]} - ${w.globals.seriesNames[seriesIndex]}
            </span><br>
            <span style="${highlightStyle}">Risk Value: ${value}</span><br>
            <span style="${highlightStyle}">Risk Level: ${riskLevel}</span>
            ${isHighlighted ? '<br><span style="color: #FF0000;">★ HIGHLIGHTED ★</span>' : ''}
          </div>`
        );
      }
    },
    dataLabels: {
      enabled: true,
      style: {
        colors: ['#000'],
        fontSize: '12px',
      },
      formatter: function (val) {
        if (val === highlightValue) {
          return `★ ${val} ★`;
        }
        return val;
      }
    },
    xaxis: {
      categories: ["Insignificant", "Minor", "Moderate", "Major", "Catastrophic"],
    },
    stroke: {
      width: 2,
      colors: ['#fff']
    },
    fill: {
      opacity: 1
    },
    title: {
      text: "Risk Assessment Matrix",
      align: 'center',
      style: {
        fontSize: '16px',
        fontWeight: 'bold'
      }
    },
    legend: {
      position: "bottom",
    },
  });
const generateData = (xValues) => {
    const staticData = [
        [1, 2, 3, 4, 5],       // Rare (1)
        [2, 4, 6, 8, 10],     // Unlikely (2)
        [3, 6, 9, 12, 15],    // Possible (3)
        [4, 8, 12, 16, 20],   // Likely (4)
        [5, 10, 15, 20, 25],  // Almost Certain (5)
    ];
  
    return staticData.map((row, rowIndex) => {
      return row.map((y, columnIndex) => ({
        x: xValues[columnIndex],
        y,
      }));
    });
  };
  

const ApexChart = () => {
  const xValues = [
    "Insignificant",
    "Minor",
    "Moderate",
    "Major",
    "Catastrophic",
  ];

  // Manually create the series with the desired colors
  const series = [
    { name: "Rare", data: generateData(xValues)[0] },
    { name: "Unlikely", data: generateData(xValues)[1] },
    { name: "Possible", data: generateData(xValues)[2] },
    { name: "Likely", data: generateData(xValues)[3] },
    { name: "Almost Certain", data: generateData(xValues)[4] },
  ];

  // Get chart options from a separate file
  const options = getChartOptions();

  return (
    <div>
      <div id="chart">
        <ReactApexChart
          options={options}
          series={series}
          type="heatmap"
          height={470}

        />
      </div>
      <div id="html-dist"></div>
    </div>
  );
};

export default ApexChart;