import "react-toastify/dist/ReactToastify.css";
import "aos/dist/aos.css";
import "@mantine/core/styles.css";
import "@mantine/charts/styles.css";
import "@mantine/dates/styles.css";
import "@mantine/notifications/styles.css";
import "@mantine/carousel/styles.css";
import "@/assets/css/index.css";
import "@react-pdf-viewer/core/lib/styles/index.css";

// import 'bootstrap/dist/css/bootstrap.min.css';

import { BrowserRouter, Routes, Route } from "react-router-dom";
import { useEffect } from "react";
import Aos from "aos";
import "./i18n";
import AuthProvider from "./Contexts/AuthContext.jsx";
import PrivateRoute from "./Components/PrivateRoute.jsx";
import ESGPulseRoutes from "./Routes/ESGPulseRoutes.jsx";
import NetZeroRoutes from "./Routes/NetZeroRoutes.jsx";
import AuthRoutes from "./Routes/AuthRoutes.jsx";
import { MantineProvider } from "@mantine/core";
import GreenHubRoutes from "./Routes/GreenHubRoutes.jsx";
import NotPrivateRoutes from "./Routes/NotPrivateRoutes";
import { ToastContainer } from "react-toastify";
import { SocketProvider } from "./providers/SocketProvider";
import { Notifications } from "@mantine/notifications";
import ThemeProvider  from "./Components/theme-provider";
import { useTheme } from "next-themes";



export default function App() {
  useEffect(() => {
    Aos.init();
  }, []);
const { theme } = useTheme();
  // useEffect(() => {
  //   if (
  //     localStorage.levelUpESGTheme === "dark" ||
  //     (!("levelUpESGTheme" in localStorage) &&
  //       window.matchMedia("(prefers-color-scheme: dark)").matches)
  //   ) {
  //     document.documentElement.classList.add("dark");
  //   } else {
  //     document.documentElement.classList.remove("dark");
  //   }
  //    console.log("HTML classes now:", document.documentElement.classList);
  // }, []);

  return (
    
    <ThemeProvider attribute="class">
      
    <MantineProvider >
      
      <ToastContainer />
      <BrowserRouter>
        {window.location.pathname === "/PublicSupplyChain" ? (
          <Routes>{NotPrivateRoutes()}</Routes>
        ) : (
          <AuthProvider>
            <Notifications position="bottom-right" className="mr-24" />
            <Routes>
              {AuthRoutes()}
              <Route element={<PrivateRoute />}>
                <Route element={<SocketProvider />}>
                  {ESGPulseRoutes()}
                  {NetZeroRoutes()}
                  {GreenHubRoutes()}
                </Route>
              </Route>
            </Routes>
          </AuthProvider>
        )}
      </BrowserRouter>
    
    </MantineProvider>
      
    </ThemeProvider>
   
    
  );
}
