import { useEffect, useState } from "react";
import { Button } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { MdOutlineFileUpload } from "react-icons/md";
import TableOfData from "./SupplierData/TableOfData";
// import { driver } from "driver.js";
// import "driver.js/dist/driver.css";
// import "@/assets/css/index.css";
// import { FaQuestionCircle } from "react-icons/fa";

const SupplierData = ({ activeTab }) => {
  const { t } = useTranslation();
  // const [elementsReady, setElementsReady] = useState(false);

  // const getGuideSteps = () => [
  //   {
  //     element: ".View-All-Suppliers-Information",
  //     popover: {
  //       title: t("View All Suppliers Information"),
  //       description: t("Access a full table showing detailed information about each supplier."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Edit-Suppliers-Information",
  //     popover: {
  //       title: t("Edit Supplier"),
  //       description: t("Edit supplier details if needed."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Delete-Suppliers-Information",
  //     popover: {
  //       title: t("Delete Supplier"),
  //       description: t("Delete suppliers who are no longer active."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Send-req-Suppliers-Information",
  //     popover: {
  //       title: t("Send requests"),
  //       description: t("Send requests directly to suppliers from the system."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Export-Suppliers-Information",
  //     popover: {
  //       title: t("Export Full Suppliers Information"),
  //       description: t("Download all suppliers’ data easily for reporting or record-keeping."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  // ];

  // const checkElementsExist = () => {
  //   const steps = getGuideSteps();
  //   const allElementsExist = steps.every((step) =>
  //     document.querySelector(step.element)
  //   );
  //   return allElementsExist;
  // };

  // useEffect(() => {
  //   if (activeTab === "Suppliers-Information") {
  //     const observer = new MutationObserver(() => {
  //       if (checkElementsExist()) {
  //         setElementsReady(true);
  //         observer.disconnect();
  //       }
  //     });

  //     observer.observe(document.body, {
  //       childList: true,
  //       subtree: true,
  //     });

  //     return () => observer.disconnect();
  //   }
  // }, [activeTab]);

  // const startGuide = () => {
  //   const driverObj = driver({
  //     showProgress: true,
  //     popoverClass: "my-custom-popover-class",
  //     nextBtnText: "Next",
  //     prevBtnText: "Back",
  //     doneBtnText: "Done",
  //     overlayColor: "rgba(0, 0, 0, 0)",
  //     progressText: "Step {{current}} of {{total}}",
  //     steps: getGuideSteps(),
  //     onDestroyStarted: () => {
  //       localStorage.setItem("hasSeenSuppliersInformationGuide", "true");
  //       driverObj.destroy();
  //     },
  //   });
  //   driverObj.drive();
  // };

  // useEffect(() => {
  //   const hasSeenGuide = localStorage.getItem(
  //     "hasSeenSuppliersInformationGuide"
  //   );
  //   if (!hasSeenGuide) {
  //     startGuide();
  //   }
  //   return () => {
  //     const driverObj = driver();
  //     if (driverObj.isActive()) {
  //       driverObj.destroy();
  //     }
  //   };
  // }, [activeTab, elementsReady]);

  return (
    <>
      {/* <div
        onClick={startGuide}
        style={{
          position: "fixed",
          bottom: 20,
          right: 20,
          cursor: "pointer",
          zIndex: 1000,
        }}
      >
        <div className="h-12 w-12 flex items-center justify-center bg-gradient-to-tr from-[#2C5A8C] via-[#1C889C] to-[#13B1A8] p-1  cursor-pointer rounded-full">
          <FaQuestionCircle
            size={34}
            color="#ffffff"
            className="mx-auto cursor-pointer"
          />
        </div>
      </div> */}
      <div className="md:flex items-center justify-between p-3 mt-5 bg-[#e0e9ea] border-2 border-primary rounded-lg shadow-md">
        <div>
          <h1 className="font-bold text-lg text-primary">
            {t("Recent upload manual input data")}
          </h1>
        </div>
        <Button className="Export-Suppliers-Information bg-white border-2 border-[#00C0A9] text-[#00C0A9] hover:bg-white hover:text-[#00C0A9] rounded-lg">
          <MdOutlineFileUpload className="me-2" />
          {t("export")}
        </Button>
      </div>
      <TableOfData />
    </>
  );
};

export default SupplierData;
