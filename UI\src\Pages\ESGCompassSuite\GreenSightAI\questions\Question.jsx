import React from "react";
import bot from "/assets/Images/bot.png";
import { Progress } from "@mantine/core";
import { useSustain360Store } from "@/Store/Assessment/useSustain360Store";

const Question = () => { 
  const {assessmentData, updateQuestionAnswer,getActiveAssesmentSection,activeQuestionIndex,activeQuestion } = useSustain360Store();
  const question = getActiveAssesmentSection().questions[activeQuestionIndex];
  const handleAnswerChangeLocal = (optionId) => {
    updateQuestionAnswer(question?.questionId, optionId);
    console.log(question?.questionId,optionId);
  };

  const progressPercentage =
    ((activeQuestionIndex + 1) / getActiveAssesmentSection().questions.length) * 100;

  return (
    <div>
      <div className="rounded-xl">
        <div className=" bg-white shadow-lg rounded-xl">
          <Progress
            classNames={{
              root: "bg-r rounded-t-xl",
              section: "bg-secondary ",
            }}
            value={progressPercentage}
            // max={10}
            // max={scope.questionObjects.length}
            aria-valuemax={10}
            size="lg"
            transitionDuration={200}
          />
          {/* <Progress.Section
            value={100} 
            withAria={false}
            />
          </Progress> */}
          <h2 className="p-5 mb-12 text-center font-medium bg-white shadow-lg rounded-xl">{`Q. ${question?.questionId} ${question?.text}`}</h2>
        </div>
        <div>
          {question?.options?.map((option, index) => (
            <div key={index} className="rounded-xl shadow-lg bg-white my-5 p-3">
              <label className="cursor-pointer">
                <input
                  type="radio"
                  name={`answer-${question?.questionId}`}
                  value={index}
                  checked={option?.is_chosen}
                  onChange={(event) => handleAnswerChangeLocal(option?.id)}
                  className="mr-2 cursor-pointer"
                />
                {option.label}
              </label>
            </div>
          ))}
        </div>
      </div>
      {question?.questionDescription && (
        <div className="bg-white py-4 px-6 rounded-2xl max-h-56">
          <div className="flex justify-between items-center mb-5">
            <h1 className="font-bold text-[18px]">Learn more</h1>
          </div>
          <div className="flex gap-4">
            <div>
              <img src={bot} alt="bot img" />
            </div>
            <div>
              <p className="text-lg max-w-xl">{question?.questionDescription}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};


export default Question;