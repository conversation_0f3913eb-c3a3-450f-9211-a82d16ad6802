import { useState } from "react";
import { useForm } from "@mantine/form";
import { IconX, IconCheck } from "@tabler/icons-react";
import {
  PasswordInput,
  Progress,
  Text,
  Popover,
  Box,
  Button,
  rem,
} from "@mantine/core";
import { showNotification } from "@mantine/notifications";
import {
  IconCheck as IconCheckNotification,
  IconX as IconXNotification,
} from "@tabler/icons-react";
import ApiProfile from "@/Api/apiProfileConfig";
import { useAuth } from "@/Contexts/AuthContext";
import { PiSpinnerLight } from "react-icons/pi";

function PasswordRequirement({ meets, label }) {
  return (
    <Text
      color={meets ? "teal" : "red"}
      style={{ display: "flex", alignItems: "center" }}
      mt={7}
      size="sm"
    >
      {meets ? (
        <IconCheck style={{ width: rem(14), height: rem(14) }} />
      ) : (
        <IconX style={{ width: rem(14), height: rem(14) }} />
      )}{" "}
      <Box ml={10}>{label}</Box>
    </Text>
  );
}

const requirements = [
  { re: /[0-9]/, label: "Includes number" },
  { re: /[a-z]/, label: "Includes lowercase letter" },
  { re: /[A-Z]/, label: "Includes uppercase letter" },
  { re: /[$&+,:;=?@#|'<>.^*()%!-]/, label: "Includes special symbol" },
];

function getStrength(password) {
  let multiplier = password.length > 5 ? 0 : 1;

  requirements.forEach((requirement) => {
    if (!requirement.re.test(password)) {
      multiplier += 1;
    }
  });

  return Math.max(100 - (100 / (requirements.length + 1)) * multiplier, 10);
}

export default function UpdatePassword() {
  const [popoverOpened, setPopoverOpened] = useState(false);
  const { logout } = useAuth();
  const [loading, setLoading] = useState(false);

  const formPass = useForm({
    mode: "controlled",
    initialValues: {
      new_password: "",
    },
    validate: {
      new_password: (value) =>
        value.length > 0 ? null : "New Password is required",
    },
  });

  const updateUserPass = async (values) => {
    // console.log(values);
    setLoading(true);
    try {
      setLoading(true);
      const { data } = await ApiProfile.post("/change-password", values);
      setLoading(true);
      if (data.error) {
        setLoading(false);

        showNotification({
          title: "Error",
          message: data.error,
          color: "red",
          icon: <IconXNotification />,
        });
        formPass.setFieldError("new_password", data.error);
      } else if (data.message === "Password updated successfully") {
        setLoading(false);

        showNotification({
          title: "Success",
          message: data.message,
          color: "teal",
          icon: <IconCheckNotification />,
        });
        logout();
        formPass.setFieldError("new_password", data.message);
      }
    } catch (error) {
      setLoading(false);

      console.log(error);
      showNotification({
        title: "Error",
        message: error.response.data.message,
        color: "red",
        icon: <IconXNotification />,
      });
    }
  };

  const checks = requirements.map((requirement, index) => (
    <PasswordRequirement
      key={index}
      label={requirement.label}
      meets={requirement.re.test(formPass.values.new_password)}
    />
  ));

  const strength = getStrength(formPass.values.new_password);
  const color = strength === 100 ? "teal" : strength > 50 ? "yellow" : "red";

  return (
    <form
      onSubmit={formPass.onSubmit(updateUserPass)}
      className=" gap-x-10 mt-5"
    >
      <Popover
        opened={popoverOpened}
        position="bottom"
        width="target"
        transitionProps={{ transition: "pop" }}
      >
        <Popover.Target>
          <div
            onFocusCapture={() => setPopoverOpened(true)}
            onBlurCapture={() => setPopoverOpened(false)}
          >
            <PasswordInput
              withAsterisk
              label="Your password"
              placeholder="Your password"
              {...formPass.getInputProps("new_password")}
              size="md"
            />
          </div>
        </Popover.Target>
        <Popover.Dropdown>
          <Progress color={color} value={strength} size={5} mb="xs" />
          <PasswordRequirement
            label="Includes at least 6 characters"
            meets={formPass.values.new_password.length > 5}
          />
          {checks}
        </Popover.Dropdown>
      </Popover>
      <div className="flex justify-center items-end mt-5">
        <Button
          className="bg-primary hover:bg-primary"
          size="md"
          type="submit"
        >
          {loading ? (
            <PiSpinnerLight className="animate-spin text-2xl" />
          ) : (
            "Update Password"
          )}
        </Button>
      </div>
    </form>
  );
}
