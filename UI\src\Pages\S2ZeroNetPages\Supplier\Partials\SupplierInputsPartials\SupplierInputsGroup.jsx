import ApiS2 from "@/Api/apiS2Config";
import Loading from "@/Components/Loading";
import { Button, Select, TextInput } from "@mantine/core";
import { DateInput } from "@mantine/dates";
import { isNotEmpty, useForm } from "@mantine/form";
import { showNotification } from "@mantine/notifications";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { FaCalendarDay } from "react-icons/fa";
import { FaRegCircleCheck } from "react-icons/fa6";
import countries from "world-countries";
// import { driver } from "driver.js";
// import "driver.js/dist/driver.css";
// import "@/assets/css/index.css";
// import { FaQuestionCircle } from "react-icons/fa";

const prioritized = ['United Kingdom', 'United Arab Emirates', 'United States', 'Saudi Arabia', 'Qatar'];

// Sort countries: prioritized first, then the rest
const sortedCountries = [
  ...countries.filter(c => prioritized.includes(c.name.common)),
  ...countries
    .filter(c => !prioritized.includes(c.name.common))
    .sort((a, b) => a.name.common.localeCompare(b.name.common))
];

export default function SupplierInputsGroup({ getTableData, activeTab }) {
  const [loading, setLoading] = useState(false);
  const [startDateValue, setStartDateValue] = useState(null);
  const [endDateValue, setEndDateValue] = useState(null);
  const [elementsReady, setElementsReady] = useState(false)


  const form = useForm({
    mode: "uncontrolled",
    initialValues: {
      category: "",
      contact: "",
      fromContactDate: "",
      name: "",
      origin: "test sector",
      qtyOfProduct: 10,
      region: "",
      sector: "",
      services: "",
      toContactDate: "",
      totalSpend: 100,
      units: "test sector",
      // factors: "test sector",
    },
    validate: {
      name: isNotEmpty("Name is required"),
      category: isNotEmpty("Family Name is required"),
      contact: isNotEmpty("Contact is required"),
      fromContactDate: isNotEmpty("Start Date of Contact is required"),
      toContactDate: isNotEmpty("End Date of Contact is required"),
      region: isNotEmpty("Region is required"),
      sector: isNotEmpty("Sector is required"),
      services: isNotEmpty("Services is required"),
      // qty_of_product: isNotEmpty("Quantity of Product is required"),
      // total_spend: isNotEmpty("Total Spend is required"),
      // factors: isNotEmpty("Factors is required"),
      // units: isNotEmpty("Units is required"),
    },
  });

  const handelSubmit = async (values) => {
    setLoading(true);
    try {
      setLoading(true);
      //console.log(values);
      const { data } = await ApiS2.post("/suppliers/add-new-supplier", values);
      setLoading(false);
      // //console.log(data);
      getTableData();
      showNotification({
        message: "Supplier Added Successfully",
        color: "green",
      });
      form.reset();
      restInputs();
    } catch (error) {
      setLoading(false);
      showNotification({
        message: "Supplier Not Added",
        color: "red",
      });
      //console.log(error);
    }
  };
  useEffect(() => {
    form.setValues({
      fromContactDate: startDateValue
        ? dayjs(startDateValue).format("DD/MM/YYYY")
        : "",
      toContactDate: endDateValue
        ? dayjs(endDateValue).format("DD/MM/YYYY")
        : "",
    });
  }, [endDateValue, startDateValue]);
  const restInputs = () => {
    setStartDateValue(null);
    setEndDateValue(null);
  };

  const { t } = useTranslation();

    //   const getGuideSteps = () => [
    //   {
    //     element: ".Enter-Supplier-Name",
    //     popover: {
    //       title: t("Enter Supplier Name"),
    //       description: t(
    //         "Type the name of the supplier you are adding."
    //       ),
    //       side: "left",
    //       align: "center",
    //     },
    //   },
    //   {
    //     element: ".Enter-Supplier-Category",
    //     popover: {
    //       title: t("Enter Supplies Category"),
    //       description: t(
    //         "Specify what type of supplies or services the supplier provides."
    //       ),
    //       side: "left",
    //       align: "center",
    //     },
    //   },
    //   {
    //     element: ".Enter-Contact-Information",
    //     popover: {
    //       title: t("Enter Contact Information"),
    //       description: t(
    //         "Add the main contact details (e.g., phone number or email)."
    //       ),
    //       side: "left",
    //       align: "center",
    //     },
    //   },
    //   {
    //     element: ".Select-Date-of-Contact-From",
    //     popover: {
    //       title: t("Select Date of Contact"),
    //       description: t(
    //         "Choose the period when you contacted the supplier From."
    //       ),
    //       side: "left",
    //       align: "center",
    //     },
    //   },
    //   {
    //     element: ".Select-Date-of-Contact-To",
    //     popover: {
    //       title: t("Select Date of Contact"),
    //       description: t(
    //         "Choose the period when you contacted the supplier To."
    //       ),
    //       side: "left",
    //       align: "center",
    //     },
    //   },
    //   {
    //     element: ".Select-Region",
    //     popover: {
    //       title: t("Select Region"),
    //       description: t(
    //         "Pick the supplier’s operating region (e.g., Europe, Asia, etc.)."
    //       ),
    //       side: "left",
    //       align: "center",
    //     },
    //   },
    //   {
    //     element: ".Enter-Sector",
    //     popover: {
    //       title: t("Enter Sector"),
    //       description: t(
    //         "Mention the industry sector the supplier belongs to (e.g., Construction, IT Services)."
    //       ),
    //       side: "left",
    //       align: "center",
    //     },
    //   },
    //   {
    //     element: ".Enter-Services",
    //     popover: {
    //       title: t("Enter Services"),
    //       description: t(
    //         "Describe the specific services offered by the supplier."
    //       ),
    //       side: "left",
    //       align: "center",
    //     },
    //   },
    //   {
    //     element: ".Confirm-Button-Supplier",
    //     popover: {
    //       title: t("Click “Confirm” to save supplier’s details to the table."),
    //       side: "left",
    //       align: "center",
    //     },
    //   },
    //   {
    //     element: ".Data-Table-View-Supplier",
    //     popover: {
    //       title: t("Data Table View"),
    //       description: t("Uploaded data appears in a structured table with the following actions:"),
    //       side: "left",
    //       align: "center",
    //     },
    //   },
    //   {
    //     element: ".Data-Table-edit-Supplier",
    //     popover: {
    //       title: t("Edit"),
    //       description: t("Correct or update any field."),
    //       side: "left",
    //       align: "center",
    //     },
    //   },
    //   {
    //     element: ".Data-Table-Delete-Supplier",
    //     popover: {
    //       title: t("Delete"),
    //       description: t("Remove any wrong entry."),
    //       side: "left",
    //       align: "center",
    //     },
    //   },
    //   // {
    //   //   element: ".Data-Table-Status-Overview-Supplier",
    //   //   popover: {
    //   //     title: t("Status Overview"),
    //   //     description: t("Showing number of entries (e.g., 2 entries currently shown)."),
    //   //     side: "left",
    //   //     align: "center",
    //   //   },
    //   // },
    //   // {
    //   //   element: ".Data-Table-Export-Full-Suppliers-Information-Supplier",
    //   //   popover: {
    //   //     title: t("Export Full Suppliers Information"),
    //   //     description: t("Download all suppliers’ data easily for reporting or record-keeping."),
    //   //     side: "left",
    //   //     align: "center",
    //   //   },
    //   // },
    // ];
  
    // const checkElementsExist = () => {
    //   const steps = getGuideSteps();
    //   const allElementsExist = steps.every((step) =>
    //     document.querySelector(step.element)
    //   );
    //   return allElementsExist;
    // };
  
    // useEffect(() => {
    //   if (activeTab === "Add-Supplier") {
    //     const observer = new MutationObserver(() => {
    //       if (checkElementsExist()) {
    //         setElementsReady(true);
    //         observer.disconnect();
    //       }
    //     });
  
    //     observer.observe(document.body, {
    //       childList: true,
    //       subtree: true,
    //     });
  
    //     return () => observer.disconnect();
    //   }
    // }, [activeTab]);
  
    // const startGuide = () => {
    //   const driverObj = driver({
    //     showProgress: true,
    //     popoverClass: "my-custom-popover-class",
    //     nextBtnText: "Next",
    //     prevBtnText: "Back",
    //     doneBtnText: "Done",
    //     overlayColor: "rgba(0, 0, 0, 0)",
    //     progressText: "Step {{current}} of {{total}}",
    //     steps: getGuideSteps(),
    //     onDestroyStarted: () => {
    //       localStorage.setItem("hasSeenSuppliersInputGuide", "true");
    //       driverObj.destroy();
    //     },
    //   });
    //   driverObj.drive();
    // };
  
    // useEffect(() => {
    //   const hasSeenGuide = localStorage.getItem("hasSeenSuppliersInputGuide");
    //   if (!hasSeenGuide) {
    //     startGuide();
    //   }
    //   return () => {
    //     const driverObj = driver();
    //     if (driverObj.isActive()) {
    //       driverObj.destroy();
    //     }
    //   };
    // }, [activeTab, elementsReady]);

  return (
    <>
            {/* <div
            onClick={startGuide}
            style={{
              position: "fixed",
              bottom: 20,
              right: 20,
              cursor: "pointer",
              zIndex: 1000,
            }}
          >
            <div className="h-12 w-12 flex items-center justify-center bg-gradient-to-tr from-[#2C5A8C] via-[#1C889C] to-[#13B1A8] p-1  cursor-pointer rounded-full">
              <FaQuestionCircle
                size={34}
                color="#ffffff"
                className="mx-auto cursor-pointer"
              />
            </div>
          </div> */}
      <div className="SupplierInputsGroup font-inter">
        <form onSubmit={form.onSubmit(handelSubmit)} onReset={form.reset}>
          <div className="p-5 bg-white md:py-10 md:px-8 rounded-xl">
            {/* inputs Group */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 sm:gap-x-5 md:gap-x-10 lg:gap-x-28 text-start">
              <TextInput
                {...form.getInputProps("name")}
                key={form.key("name")}
                type="text"
                id="name"
                name="name"
                label="Name"
                placeholder="Enter your Name"
                mb="md"
                className="Enter-Supplier-Name"
              />

              <TextInput
                {...form.getInputProps("category")}
                key={form.key("category")}
                type="text"
                id="category"
                name="category"
                label="Supplies category"
                placeholder="Enter your Supplies category"
                mb="md"
                className="Enter-Supplier-Category"
              />

              <TextInput
                {...form.getInputProps("contact")}
                key={form.key("contact")}
                type={"email" || "number"}
                id="contact"
                name="contact"
                label="Contact"
                placeholder="Enter your Contact"
                mb="md"
                className="Enter-Contact-Information"
              />

              <DateInput
                {...form.getInputProps("fromContactDate")}
                key={form.key("fromContactDate")}
                placeholder="dd/MM/YYYY"
                valueFormat="DD/MM/YYYY"
                rightSection={<FaCalendarDay />}
                id="fromContactDate"
                name="fromContactDate"
                label="Date of Contact"
                mb="md"
                value={startDateValue}
                onChange={setStartDateValue}
                className="Select-Date-of-Contact-From"
              />

              <DateInput
                {...form.getInputProps("toContactDate")}
                key={form.key("toContactDate")}
                placeholder="dd/MM/YYYY"
                valueFormat="DD/MM/YYYY"
                rightSection={<FaCalendarDay />}
                id="toContactDate"
                name="toContactDate"
                label="To"
                mb="md"
                value={endDateValue}
                onChange={setEndDateValue}
                className="Select-Date-of-Contact-To"
              />

              <Select
                {...form.getInputProps("region")}
                key={form.key("region")}
                mb="md"
                id="region"
                name="region"
                comboboxProps={{ withinPortal: false }}
                data={sortedCountries.map((country) => country.name.common)}
                placeholder="Select your Region"
                label="Region"
                searchable
                // withCheckIcon={false}
                // allowDeselect={false}
                rightSectionWidth="0"
                className="Select-Region"
              />

              <TextInput
                {...form.getInputProps("sector")}
                key={form.key("sector")}
                type="text"
                id="sector"
                name="sector"
                label="Sector"
                placeholder="Enter your sector"
                mb="md"
                className="Enter-Sector"
              />

              <TextInput
                {...form.getInputProps("services")}
                key={form.key("services")}
                id="services"
                name="services"
                label="Services"
                placeholder="Enter your Services"
                mb="md"
                className="Enter-Services"
              />

              <div className="flex items-center">
                <Button
                  className="bg-primary Confirm-Button-Supplier hover:bg-primary w-full py-[8px] text-white rounded-lg flex justify-center items-center text-lg"
                  type="submit"
                >
                  {loading ? (
                    <Loading />
                  ) : (
                    <>
                      <FaRegCircleCheck className="me-4" /> {t("Confirm")}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </>
  );
}

{
  /* <TextInput
                {...form.getInputProps("qty_of_product")}
                key={form.key("qty_of_product")}
                type="text"
                id="qty_of_product"
                name="qty_of_product"
                label="Qty of product"
                placeholder="Enter your Qty of product"
                mb="md"
              /> */
}

{
  /* <TextInput
                {...form.getInputProps("total_spend")}
                key={form.key("total_spend")}
                type="text"
                id="total_spend"
                name="total_spend"
                label="Total spend"
                placeholder="Enter your Total spend"
                mb="md"
              /> */
}

{
  /* <TextInput
                {...form.getInputProps("factors")}
                key={form.key("factors")}
                type="text"
                id="factors"
                name="factors"
                label="Factors"
                placeholder="Enter your Factors"
                mb="md"
              /> */
}

{
  /* <TextInput
                {...form.getInputProps("units")}
                key={form.key("units")}
                type="text"
                id="units"
                name="units"
                label="Units"
                placeholder="Enter your Units"
                mb="md"
              /> */
}
{
  /* End inputs Group */
}
{
  /* <div className="grid items-center grid-cols-1 md:grid-cols-2 lg:grid-cols-3 sm:gap-x-5 md:gap-x-10 lg:gap-x-28"> */
}
{
  /* upload file */
}
{
  /* <div className="flex col-span-2 ">
                <FileInput
                  {...form.getInputProps("uploadValue")}
                  key={form.key("uploadValue")}
                  size="md"
                  variant=""
                  className="top-0 border-2 rounded-e-none border-primary rounded-[10px] lg:w-9/12 overflow-hidden"
                  radius="xs"
                  withAsterisk
                  id="upload_file"
                  type="file"
                  // error={}
                  placeholder={"Upload File.pdf"}
                  value={uploadValue}
                  onChange={setUploadValue}
                  leftSection={<FiUpload />}
                  rightSection={
                    <CloseButton
                      aria-label="Clear input"
                      onClick={() => setUploadValue(null)}
                      style={{ display: uploadValue ? undefined : "none" }}
                    />
                  }
                />
                <label
                  htmlFor="upload_file"
                  className="rounded-s-none border-primary border-2 rounded-[10px] flex justify-center  leading-[23.8px] cursor-pointer lg:w-3/12"
                >
                  <span className="p-2 font-bold text-center text-primary">
                    {t("Upload Evidence")}
                  </span>
                </label>
              </div> */
}
{
  /* End upload file */
}
{
  /* </div> */
}
