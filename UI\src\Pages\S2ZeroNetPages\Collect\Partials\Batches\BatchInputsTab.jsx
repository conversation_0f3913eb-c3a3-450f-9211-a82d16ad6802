import { useEffect, useState } from "react";
// import { useTranslation } from "react-i18next";
import BatchTable from "./BatchTable";
import DownloadTemplate from "./DownloadTemplate";
import UploadTemplate from "./UploadTemplate";
// import { driver } from "driver.js";
// import "driver.js/dist/driver.css";
// import "@/assets/css/index.css";
// import { FaQuestionCircle } from "react-icons/fa";

export default function BatchInputsTab({
  activeTab,
  assetTypeAll,
  handelAssetType,
  getTableData,
  data,
  loadingApprove,
  error,
  Status,
  allItemSpecificities,
}) {
  // const { t } = useTranslation();
  // const [elementsReady, setElementsReady] = useState(false);

  useEffect(() => {
    !assetTypeAll && handelAssetType();
  }, []);

  // const checkElementsExist = () => {
  //   const steps = getGuideSteps();
  //   const allElementsExist = steps.every((step) =>
  //     document.querySelector(step.element)
  //   );
  //   return allElementsExist;
  // };

  // useEffect(() => {
  //   if (activeTab === "batch-input") {
  //     const observer = new MutationObserver(() => {
  //       if (checkElementsExist()) {
  //         setElementsReady(true);
  //         observer.disconnect();
  //       }
  //     });

  //     observer.observe(document.body, {
  //       childList: true,
  //       subtree: true,
  //     });

  //     return () => observer.disconnect();
  //   }
  // }, [activeTab]);

  // const getGuideSteps = () => [
  //   {
  //     element: ".Download-Batch-Template",
  //     popover: {
  //       title: t("Download Batch Template"),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Select-Emission-Source",
  //     popover: {
  //       title: t("Select Emission Source"),
  //       description: t(
  //         "Choose the emission source you want to upload data for (e.g., Purchased Electricity, Stationary Combustion)."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Download-Empty-Template",
  //     popover: {
  //       title: t("Download Empty Template"),
  //       description: t(
  //         "Based on the selected emission source, download a pre-configured Excel template file (.xlsx) that matches the required data format."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Fill-Template",
  //     popover: {
  //       title: t("Fill in the Template"),
  //       description: t(
  //         "Fill the downloaded Excel sheet with the required data."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".NOTE",
  //     popover: {
  //       title: t("NOTE"),
  //       description: t(
  //         "Make sure the custom factors you select inside the file are ones previously added by the user, not global ones."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Upload-Completed-Template",
  //     popover: {
  //       title: t("Upload Completed Template"),
  //       description: t(
  //         "After completing the template, upload it back into the system using the Upload button."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Manage-Uploaded-Record",
  //     popover: {
  //       title: t("Manage Uploaded Records"),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".SEARCH",
  //     popover: {
  //       title: t("Search"),
  //       description: t(
  //         "Use the search bar to quickly find specific records by topic area or assessment question."
  //       ),
  //       side: "top",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Filter-Batch-collect",
  //     popover: {
  //       title: t("Filter"),
  //       description: t(
  //         "Filter records by their status: ◦ Pending ◦ Accepted ◦ Rejected"
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Edit-Delete-Records",
  //     popover: {
  //       title: t("Edit or Delete Records"),
  //       description: t(
  //         "Update or remove any input entry easily from the actions available next to each record."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".STatus-Overview",
  //     popover: {
  //       title: t("Status Overview"),
  //       description: t(
  //         "Showing number of entries (e.g., 2 entries currently shown)."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Export-Records-Batch",
  //     popover: {
  //       title: t("Export Records"),
  //       description: t(
  //         "Use the Export button to download all or filtered records into a CSV or Excel file for reporting, analysis, or backup."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".note-batch-1",
  //     popover: {
  //       title: t("NOTE 1"),
  //       description: t(
  //         "Batch input is best suited for large datasets (e.g., monthly energy bills, fleet fuel records)."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".note-batch-2",
  //     popover: {
  //       title: t("NOTE 2"),
  //       description: t(
  //         "You must only use the provided template for uploading; manually altering the structure may cause upload failure."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  // ];

  // const startGuide = () => {
  //   const driverObj = driver({
  //     showProgress: true,
  //     popoverClass: "my-custom-popover-class",
  //     nextBtnText: "Next",
  //     prevBtnText: "Back",
  //     doneBtnText: "Done",
  //     progressText: "Step {{current}} of {{total}}",
  //     overlayColor: "rgba(0, 0, 0, 0)",
  //     steps: getGuideSteps(),
  //     onDestroyStarted: () => {
  //       localStorage.setItem("hasSeenBatchCollectGuide", "true");
  //       driverObj.destroy();
  //     },
  //   });
  //   driverObj.drive();
  // };

  // useEffect(() => {
  //   const hasSeenGuide = localStorage.getItem("hasSeenBatchCollectGuide");
  //   if (!hasSeenGuide) {
  //     startGuide();
  //   }
  //   return () => {
  //     const driverObj = driver();
  //     if (driverObj.isActive()) {
  //       driverObj.destroy();
  //     }
  //   };
  // }, [activeTab, elementsReady]);

  return (
    <div className="">
      {/* <div
        onClick={startGuide}
        style={{
          position: "fixed",
          bottom: 20,
          right: 20,
          cursor: "pointer",
          zIndex: 1000,
        }}
      >
        <div className="h-12 w-12 flex items-center justify-center bg-gradient-to-tr from-[#2C5A8C] via-[#1C889C] to-[#13B1A8] p-1  cursor-pointer rounded-full">
          <FaQuestionCircle
            size={34}
            color="#ffffff"
            className="mx-auto cursor-pointer"
          />
        </div>
      </div> */}
      <div className="bg-white rounded-lg p-5">
        <DownloadTemplate />

        <UploadTemplate
          assetTypeAll={assetTypeAll}
          getTableData={getTableData}
          allItemSpecificities={allItemSpecificities}
        />
      </div>

      <div className="w-full">
        <BatchTable
          assetTypeAll={assetTypeAll}
          data={data}
          error={error}
          getTableData={getTableData}
          loading={loadingApprove}
          Status={Status}
        />
      </div>
    </div>
  );
}
