import useSideBarRoute from "@/hooks/useSideBarRoute";
import MainLayout from "@/Layout/MainLayout";

import green1 from "@/assets/images/image.png";
import green2 from "@/assets/images/image37.png";
import green3 from "@/assets/images/image38.png";
import green4 from "@/assets/images/image40.png";
import green5 from "@/assets/images/image41.png";
import green6 from "@/assets/images/image42.png";
import green7 from "@/assets/images/image43.png";
import green8 from "@/assets/images/image44.png";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import CoursesPagination from "./Partials/CoursesPagination";
import TopicCart from "./Partials/TopicCart";
import { IoMdHome } from "react-icons/io";
const resources = [
  { image: green1, title: "Sustainability" },
  { image: green2, title: "Leadership" },
  { image: green3, title: "Internal Controls" },
  { image: green4, title: "Corporate Governance" },
  { image: green5, title: "Risk Management" },
  { image: green6, title: "Internal Audit" },
  { image: green6, title: "Internal Audit 222 " },
  { image: green7, title: "Sustainability" },
  { image: green8, title: "Leadership" },
  { image: green3, title: "Internal Controls" },
];

import ApiS3 from "@/Api/apiS3";
// const data = require("./resources")

function Resources() {
  const { GreenHubMenu } = useSideBarRoute();
  const [topics, setTopics] = useState([]);
  const [page, setPage] = useState(1);
  const { t } = useTranslation();

  const getTopics = async () => {
    try {
      const response = await ApiS3.get("/greenhub-resourses");
      setTopics(response?.data);
    } catch (error) {
      //console.log(error);
    }
  };
  useEffect(() => {
    getTopics();
  }, []);

  let firstItem = 6 * (page - 1);
  let currentResources = topics.slice(firstItem, firstItem + 6);

  return (
    <>
      <MainLayout menus={GreenHubMenu} 
        navbarTitle="Resources"
        breadcrumbItems={[
            { title: <IoMdHome size={20}/>, href: "/get-started" },
            { title: "Resources", href: "#" },
        ]}
      >
        <section className="">
          <div className="flex flex-col items-start pt-6 pl-8  my-5 bg-white rounded-2xl">
            <h3 className="text-3xl max-md:text-3xl md:w-[70%] font-bold text-[#0f0f0f]  ">
              {t("ResourcesHubTitle")}
            </h3>
            <p className="my-4 md:w-[60%] leading-[1.5] text-xl  text-gray-600 text-start">
              <p>Your Gateway to Sustainable Growth </p>
              {/* {t("ResourcesHubSubtitle")} */}
            </p>
          </div>
        </section>

        {/* <section className="p-4 border-4 rounded-3xl border-secondary-400 ">
          <div className="flex flex-col items-center justify-center lg:flex-row">
            <div className="lg:w-[70%] lg:pr-10 mb-10 lg:mb-0 mx-auto">
              <FaQuoteLeft className="size-6 text-secondary-400" />
              <h1 className="mb-4 mt-4 text-2xl max-w-[1000px] font-bold tracking-tight leading-[1.2] text-gray-900">
                {t("EmpoweringBusinessLeaders")}
              </h1>
              <p className="mb-8 text-xl font-normal leading-[2] max-w-[600px] text-gray-500">
                {t("ResourceDescription")}
              </p>
            </div>
            <div className="lg:w-[40%] ">
              <img src={resource} alt="Hero Image" className="w-[100%] lg:w-[70%] rounded-3xl shadow-2xl mr-[-50px]" />
            </div>
          </div>
        </section> */}

        <section className="">
          <div className="items-center   my-5 bg-gradient-to-b  rounded-3xl">
            <div className="px-2 pb-10  lg:max-w-[95%] mx-auto">
              {/* <h2 className="text-2xl font-bold tracking-tight text-white">
                {t("PickFavoriteTopic")}
              </h2> */}

              <div className="grid grid-cols-1 mt-6 gap-x-6 gap-y-10 sm:grid-cols-2 xl:grid-cols-3 lg:gap-10 md:gap-x-4">
                {currentResources.map((topic, i) => (
                  <TopicCart
                    image={topic.image}
                    topicName={topic.topicName}
                    key={topic._id}
                    id={topic._id}
                  />
                ))}
              </div>
            </div>
          </div>
        </section>
        <hr />
        <section className="max-w-[70%] mx-auto">
          <CoursesPagination
            numOfResourses={resources.length}
            page={page}
            setPage={setPage}
          />
        </section>
      </MainLayout>
    </>
  );
}

export default Resources;
