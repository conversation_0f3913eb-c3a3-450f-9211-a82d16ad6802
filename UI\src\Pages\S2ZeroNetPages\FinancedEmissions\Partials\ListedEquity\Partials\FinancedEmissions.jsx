import Api_PCAF from '@/Api/apiS2_PCAF';
import { useListedEquityStore } from '@/Store/useListedEquityStore';
import { Spinner } from '@react-pdf-viewer/core';
import axios from 'axios';
import Cookies from 'js-cookie';
import React from 'react';
import { BsStars } from "react-icons/bs";

const FinancedEmissions = () => {
    const {
        investment_value, 
        evic, 
        company_emissions, 
        company_name,
        company_industry, 
        data_quality_score,
        loading,

        setInvestmentValue,
        setEvic,
        setCompanyEmissions,
        setCompanyName,
        setCompanyIndustry,
        setDataQualityScore,
        setLoading,
        setResults,

        estimate_evic_loaging,
        estimate_company_emissions_loading,
        setEstimateEvicLoading,
        setEstimateCompanyEmissionsLoading,
    } = useListedEquityStore();

    const handleCalculate = async () => {
        setLoading(true);
        Api_PCAF.post("/equity-corporate-bonds", {
            "investment_value": investment_value,
            "evic": evic,
            "company_emissions": company_emissions,
            "company_name": company_name,
            "company_industry": company_industry,
            "data_quality_score": data_quality_score
        }).then((res) => {
            setLoading(false);
            const financedEmissions = res.data;
            setResults(financedEmissions);
        }).catch((error) => {
            console.log(error);
            setLoading(false);
        });
    }

    const Estimate_EVIC = async ()=>{
        const questions = getAnsweredQuestions();
        setEstimateEvicLoading(true);
        axios.post("https://gen-ai0-staging.azurewebsites.net/process_request",
            {
                "processor": "pcaf",
                "resources":{
                    "qa_context": questions,
                    "estimation_needed": "Enterprise Value Including Cash “EVIC”"
                }
            },
            {
                headers:{ "Authorization": `Bearer ${Cookies.get("level_user_token")}`}
            }
        ).then((res)=>{
            setEstimateEvicLoading(false);
            setEvic(res.data.ai_response.value);
        }).catch((err)=>{
            setEstimateEvicLoading(false);
            console.log(err);
        })
    }
    const Estimate_CompanyEmission = async ()=>{
        const questions = getAnsweredQuestions();
        setEstimateCompanyEmissionsLoading(true);
        axios.post("https://gen-ai0-staging.azurewebsites.net/process_request",
            {
                "processor": "pcaf",
                "resources":{
                    "qa_context": questions,
                    "estimation_needed": "Company's Emissions (tCO2e)"
                }
            },
            {
                headers:{ "Authorization": `Bearer ${Cookies.get("level_user_token")}`}
            }
        ).then((res)=>{
            setEstimateCompanyEmissionsLoading(false);
            setCompanyEmissions(res.data.ai_response.value);
        }).catch((err)=>{
            setEstimateCompanyEmissionsLoading(false);
            console.log(err);
        })
    }

    const getAnsweredQuestions = ()=>{
        let questions = []
        if (investment_value !== "") {
            questions.push({
                question: "Value of Investment",
                user_answer: investment_value
            });
        }
        if (evic !== "") {
            questions.push({
                question: "EVIC",
                user_answer: evic
            });
        }
        if (company_emissions !== "") {
            questions.push({
                question: "Company Emissions",
                user_answer: company_emissions
            });
        }
        if (company_name !== "") {
            questions.push({
                question: "Company Name",
                user_answer: company_name
            });
        }
        if (company_industry !== "") {
            questions.push({
                question: "Company Industry",
                user_answer: company_industry
            });
        }
        if (data_quality_score !== "") {
            questions.push({
                question: "Data Quality Score",
                user_answer: data_quality_score
            });
        }
        return questions
    }
  return (
    <div>
        <div className='flex flex-col gap-4 rounded-xl bg-white p-4 border-[#E8E7EA] border-2'>
            <h1 className='flex justify-center text-center bg-[#E6F3F4] text-[#8F8F8F] p-2 rounded-xl'>
                Financed Emissions=(Value of Investment/EVIC) Company&apos;s Total Emissions
            </h1>
            <table className="w-full">
            <thead className="bg-[#F5F4F5]">
                <tr className='font-bold'>
                <th className="text-start p-3 ">Parameter</th>
                <th className="text-start p-3 ">Value</th>
                <th className="text-start p-3 ">AI Assistant</th>
                </tr>
            </thead>
            <tbody>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Value of Investment</td>
                    <td className="p-3 border border-gray-300">
                        <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="e.g., 500,000" onChange={(e) => setInvestmentValue(e.target.value)} value={investment_value}/>
                    </td>
                    <td className="p-3 border border-gray-300">Required Input</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Enterprise Value Including Cash “EVIC”</td>
                    <td className="p-3 border border-gray-300">
                        <input disabled={estimate_evic_loaging} type="text" className={"border border-gray-400 rounded-lg p-2 w-full" + (estimate_evic_loaging ? " cursor-not-allowed text-gray-500" : "")} placeholder="e.g., XYZ Corporation" onChange={(e) => setEvic(e.target.value)} value={evic}/>
                    </td>
                    <td className="  p-3 border border-gray-300">
                     <button disabled={estimate_evic_loaging} className='flex flex-row items-center justify-center gap-2 rounded-md bg-[#1C889C] text-[#fff] p-2 w-full' onClick={Estimate_EVIC}> 
                        {estimate_evic_loaging ? <Spinner size='24px' /> : <BsStars /> } Estimate
                    </button>
                    </td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Company Name</td>
                    <td className="p-3 border border-gray-300">
                        <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="e.g., Apple Inc. " onChange={(e) => setCompanyName(e.target.value)} value={company_name}/>
                    </td>
                    <td className="p-3 border border-gray-300">For AI Assist</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Company Industry</td>
                    <td className="p-3 border border-gray-300">
                        <select type="text" className="border border-gray-400 rounded-lg p-2 w-full" onChange={(e) => setCompanyIndustry(e.target.value)} value={company_industry}>
                            <option value="Technology">Technology</option>
                            <option value="Finance">Finance</option>
                        </select>
                    </td>
                    <td className="p-3 border border-gray-300">Required</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Company&apos;s Emissions (tCO2e)</td>
                    <td className="p-3 border border-gray-300">
                        <input disabled={estimate_company_emissions_loading} type="text" className={"border border-gray-400 rounded-lg p-2 w-full" + (estimate_company_emissions_loading ? " cursor-not-allowed text-gray-500" : "")} placeholder="e.g., 20,000" onChange={(e) => setCompanyEmissions(e.target.value)} value={company_emissions}/>
                    </td>
                    <td className="p-3 border border-gray-300">
                        <button disabled={estimate_company_emissions_loading} className={'flex flex-row items-center justify-center gap-2 rounded-md bg-[#1C889C] text-[#fff] p-2 w-full'} onClick={Estimate_CompanyEmission}>
                            {estimate_company_emissions_loading ? <Spinner size='24px' /> : <BsStars /> } Estimate
                        </button>
                    </td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Data Quality Score</td>
                    <td className="p-3 border border-gray-300">
                        <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="3 - Sector average emissions Number" onChange={(e) => setDataQualityScore(e.target.value)} value={data_quality_score}/>
                    </td>
                    <td className="p-3 border border-gray-300">Auto-updates</td>
                </tr>
            </tbody>
            </table>

             <button disabled={loading} className='flex w-full justify-center text-center text-base font-semibold bg-[#07838F] text-[#ffffff] p-2 rounded-lg' onClick={handleCalculate}>
                {
                    loading ? <Spinner size='24px' /> : "Calculate Financed Emissions"
                }
            </button>
        </div>
    </div>
  )
}

export default FinancedEmissions