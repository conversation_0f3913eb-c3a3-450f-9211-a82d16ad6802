import { IoEyeOutline } from "react-icons/io5";
import { CiEdit } from "react-icons/ci";
import { Link } from "react-router-dom";
import DeleteModal from "@/Components/Modals/DeleteModal";
import { thresholdLevels } from "./StaticData";
import { useEffect, useState } from "react";
const RiskRow = ({ item, refetchFn }) => {
  console.log("🚀 ~ RiskRow ~ item:", item)
  const ThresholdBadge = ({ level }) => {
    return (
      <div
        className="flex items-center justify-center gap-2 px-4 py-1 rounded-full w-fit"
        style={{ backgroundColor: level.bgColor }}
      >
        <div
          className="w-2 h-2 rounded-full"
          style={{ backgroundColor: level.color }}
        />
        <span
          className="font-semibold uppercase text-sm"
          style={{ color: level.color }}
        >
          {level.label}
        </span>
      </div>
    );
  };



  const statusLevels = [
    {
      value: "Pending",
      bgColor: "#FEE2E2",
    },
    {
      value: "In Progress",
      bgColor: "#FEF3C7",
    },
    {
      value: "Completed",
      bgColor: "#C6F6D5",
    },
  ];

  const StatusLevelsCompo = ({ level }) => {
    return (
      <div
        className="flex items-center justify-center gap-2 px-4 py-1 rounded-full w-fit"
        style={{ backgroundColor: level.bgColor }}
      >
        <span
          className="font-semibold uppercase text-sm"
        >
          {level.value}
        </span>
      </div>
    );
  };


  const status =
  statusLevels.find((level) => level.value == item.actionStatus) || "";
  const [riskLevel, setRiskLevel] = useState('')

  useEffect(() => {
    switch(true) {
      case item.inherentRisk >= 1 && item.inherentRisk <= 3:
        setRiskLevel('low');
        break;
      case item.inherentRisk >= 4 && item.inherentRisk <= 9:
        setRiskLevel('medium');
        break;
      case item.inherentRisk >= 10 && item.inherentRisk <= 15:
        setRiskLevel('high');
        break;
      case item.inherentRisk >= 16 && item.inherentRisk <= 25:
        setRiskLevel('critical');
        break;
      default:
        setRiskLevel('');
    }
  }, [item.inherentRisk]);
  
  const selectedLevel =
    thresholdLevels.find((level) => level.value == riskLevel) || "";


  return (
    <tr className="bg-white text-sm text-[#626364] text-center">
      {/* risk id*/}

      <td>
        {item.impactId}

      </td>

      <td>{item.event}</td>

      <td className="line-clamp-3 w-32"> {item.impactDescription} </td>

      <td>{item.impactSeverity.name}</td>

      <td>{item.likelihoodId.name}</td>

      <td>
        <ThresholdBadge level={selectedLevel} />
      </td>

      <td>{item.riskOwner}</td>

      <td>{item.dateRecorded}</td>

      <td> <StatusLevelsCompo level={status} /> </td>

      <td className="w-fit">
        <div className="flex w-fit gap-2 p-3">
          <Link
            to={`/green-shield/financial/ESG-risk-management/main/systems/iro-assessment/${item._id}?type=view`}
            className="w-8 h-8 bg-[#F5F4F5] rounded-md flex items-center justify-center"
          >
            <IoEyeOutline className="text-primary" size={15} />
          </Link>

          <Link
            to={`/green-shield/financial/ESG-risk-management/main/systems/iro-assessment/${item._id}?type=edit`}
            className="w-8 h-8 bg-[#F5F4F5] rounded-md flex items-center justify-center"
          >
            <CiEdit className="text-primary" size={15} />
          </Link>

          <DeleteModal
            sType="s3"
            apiLink={`/risk/${item._id}`}
            refreshFn={refetchFn}
          />
        </div>
      </td>
    </tr>
  );
};

export default RiskRow;
