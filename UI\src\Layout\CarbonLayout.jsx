import Navbar from "@/Components/NavBar/Navbar";
import Sidebar from "@/Components/SideBar/Sidebar";

import { Breadcrumbs } from "@mantine/core";
import { useState } from "react";
const CarbonLayout = ({
  menus,
  activeChatbot = false,
  children,
  navbarTitle,
  subTitle,
  items,
}) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  return (
    <div className="flex flex-col">
      <Navbar
        navbarSubtitle={subTitle}
        isSidebarOpen={isSidebarOpen}
        setIsSidebarOpen={setIsSidebarOpen}
      />

      <div className="flex-grow flex h-screen overflow-x-hidden max-md:text-center z-10 bg-[#f7f4f4]  dark:bg-[#282828]">
        <Sidebar
          menus={menus}
          isOpen={isSidebarOpen}
          setIsOpen={setIsSidebarOpen}
          //  sidebarStyle={sidebarStyle}
        />
        <div className={"px-2 mx-auto"}>
          <div className="text-2xl font-semibold font-inter leading-none text-primary   dark:text-white mx-auto my-8 px-2 flex flex-col sm:flex-row items-center justify-between">
            <div>{navbarTitle}</div>
            
            <p className="text-base font-normal mt-5 sm:mt-0">
              {items?.length > 0 && (
                <Breadcrumbs className="" separator="→">
                  {items}
                </Breadcrumbs>
              )}
            </p>
          </div>
          {children}
          
        </div>
      </div>

    </div>
  );
};

export default CarbonLayout;
