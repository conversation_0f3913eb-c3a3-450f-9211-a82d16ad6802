const Quantitative = [
  {
    type: "Environmental Management Metrics",
    name: "Environmental policy robustness",
    measurement_approach: "Scale assessment",
    measurement_values: ["Basic", "Moderate", "Comprehensive"],
  },
  {
    type: "Environmental Management Metrics",
    name: "Climate change strategy",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Environmental Management Metrics",
    name: "Climate risk assessment process",
    measurement_approach: "Presence and comprehensiveness evaluation",
    measurement_values: [
      "Not present",
      "Basic",
      "Moderate",
      "Comprehensive",
      "Industry-leading",
    ],
  },
  {
    type: "Environmental Management Metrics",
    name: "Environmental management system",
    measurement_approach: "Certification status",
    measurement_values: ["None", "Partial", "Full"],
  },
  {
    type: "Environmental Management Metrics",
    name: "Science-based targets adoption",
    measurement_approach: "Status assessment",
    measurement_values: ["Not considered", "In progress", "Approved"],
  },
  {
    type: "Environmental Management Metrics",
    name: "Low-carbon transition plan",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Environmental Management Metrics",
    name: "Environmental due diligence process",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Environmental Management Metrics",
    name: "Circular economy measurement_approach",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Environmental Management Metrics",
    name: "Water stewardship commitment",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Environmental Management Metrics",
    name: "Biodiversity impact assessment",
    measurement_approach: "Presence and comprehensiveness evaluation",
    measurement_values: [
      "Not present",
      "Basic",
      "Moderate",
      "Comprehensive",
      "Industry-leading",
    ],
  },
  {
    type: "Environmental Management Metrics",
    name: "Biodiversity action plans",
    measurement_approach: "Quality and implementation assessment",
    measurement_values: [
      "No plans",
      "Basic plans",
      "Developed plans",
      "Well-implemented",
      "Fully integrated and effective",
    ],
  },
  {
    type: "Environmental Management Metrics",
    name: "Environmental compliance management",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Environmental Management Metrics",
    name: "Sustainable procurement measurement_approach",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Social Responsibility Metrics",
    name: "Human rights policy",
    measurement_approach: "Scale assessment",
    measurement_values: ["Basic", "Moderate", "Comprehensive"],
  },
  {
    type: "Social Responsibility Metrics",
    name: "Human rights due diligence process",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Social Responsibility Metrics",
    name: "Labor rights commitment",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Social Responsibility Metrics",
    name: "Diversity & inclusion strategy",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Social Responsibility Metrics",
    name: "Employee wellbeing programs",
    measurement_approach: "Comprehensiveness evaluation",
    measurement_values: [
      "None",
      "Limited",
      "Basic",
      "Comprehensive",
      "Holistic",
    ],
  },
  {
    type: "Social Responsibility Metrics",
    name: "Health & safety management system",
    measurement_approach: "Certification status",
    measurement_values: ["None", "Partial", "Full"],
  },
  {
    type: "Social Responsibility Metrics",
    name: "Community engagement measurement_approach",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Social Responsibility Metrics",
    name: "Indigenous peoples engagement",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Social Responsibility Metrics",
    name: "Social impact assessment process",
    measurement_approach: "Presence and comprehensiveness evaluation",
    measurement_values: [
      "Not present",
      "Basic",
      "Moderate",
      "Comprehensive",
      "Industry-leading",
    ],
  },
  {
    type: "Social Responsibility Metrics",
    name: "Modern slavery response",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Social Responsibility Metrics",
    name: "Fair compensation commitment",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Social Responsibility Metrics",
    name: "Employee development programs",
    measurement_approach: "Comprehensiveness evaluation",
    measurement_values: [
      "None",
      "Limited",
      "Basic",
      "Comprehensive",
      "Holistic",
    ],
  },
  {
    type: "Social Responsibility Metrics",
    name: "Responsible marketing practices",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Social Responsibility Metrics",
    name: "Social value creation measurement_approach",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Governance & Ethics Metrics",
    name: "Board sustainability oversight",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Governance & Ethics Metrics",
    name: "ESG risk management integration",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Governance & Ethics Metrics",
    name: "Executive sustainability accountability",
    measurement_approach: "Presence and strength evaluation",
    measurement_values: ["None", "Limited", "Moderate", "Strong", "Exemplary"],
  },
  {
    type: "Governance & Ethics Metrics",
    name: "Code of conduct comprehensiveness",
    measurement_approach: "Scale assessment",
    measurement_values: ["Basic", "Moderate", "Comprehensive"],
  },
  {
    type: "Governance & Ethics Metrics",
    name: "Anti-corruption program",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Governance & Ethics Metrics",
    name: "Whistleblower protection mechanism",
    measurement_approach: "Presence and effectiveness evaluation",
    measurement_values: [
      "Absent",
      "Present but ineffective",
      "Moderately effective",
      "Effective",
      "Highly effective",
    ],
  },
  {
    type: "Governance & Ethics Metrics",
    name: "Political engagement transparency",
    measurement_approach: "Scale assessment",
    measurement_values: ["Basic", "Moderate", "Comprehensive"],
  },
  {
    type: "Governance & Ethics Metrics",
    name: "Responsible tax measurement_approach",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Governance & Ethics Metrics",
    name: "Sustainability committee quality",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Governance & Ethics Metrics",
    name: "Business ethics training",
    measurement_approach: "Comprehensiveness evaluation",
    measurement_values: [
      "None",
      "Limited",
      "Basic",
      "Comprehensive",
      "Holistic",
    ],
  },
  {
    type: "Governance & Ethics Metrics",
    name: "ESG performance incentives",
    measurement_approach: "Presence and significance evaluation",
    measurement_values: [
      "None",
      "Minimal",
      "Moderate",
      "Significant",
      "Substantial",
    ],
  },
  {
    type: "Governance & Ethics Metrics",
    name: "Board expertise in sustainability",
    measurement_approach: "Qualitative assessment",
    measurement_values: ["None", "Limited", "Adequate", "Strong", "Expert"],
  },
  {
    type: "Governance & Ethics Metrics",
    name: "Stakeholder engagement quality",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Governance & Ethics Metrics",
    name: "Decision-making transparency",
    measurement_approach: "Scale assessment",
    measurement_values: ["Basic", "Moderate", "Comprehensive"],
  },
  {
    type: "Strategic & Management Metrics",
    name: "Sustainability integration in strategy",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Strategic & Management Metrics",
    name: "ESG materiality assessment process",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Strategic & Management Metrics",
    name: "Long-term value creation model",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Strategic & Management Metrics",
    name: "Sustainability goals ambition",
    measurement_approach: "Scale assessment",
    measurement_values: ["Basic", "Moderate", "Comprehensive"],
  },
  {
    type: "Strategic & Management Metrics",
    name: "ESG-related scenario analysis",
    measurement_approach: "Presence and comprehensiveness evaluation",
    measurement_values: [
      "Not present",
      "Basic",
      "Moderate",
      "Comprehensive",
      "Industry-leading",
    ],
  },
  {
    type: "Strategic & Management Metrics",
    name: "Purpose alignment with sustainability",
    measurement_approach: "Qualitative assessment",
    measurement_values: [
      "Unaligned",
      "Partially aligned",
      "Aligned",
      "Fully integrated",
      "Leading",
    ],
  },
  {
    type: "Strategic & Management Metrics",
    name: "Stakeholder capitalism measurement_approach",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Strategic & Management Metrics",
    name: "ESG risk identification process",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Strategic & Management Metrics",
    name: "ESG opportunity identification process",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Strategic & Management Metrics",
    name: "Triple bottom line commitment",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Strategic & Management Metrics",
    name: "Cultural alignment with sustainability",
    measurement_approach: "Qualitative assessment",
    measurement_values: [
      "Unaligned",
      "Partially aligned",
      "Aligned",
      "Fully integrated",
      "Leading",
    ],
  },
  {
    type: "Strategic & Management Metrics",
    name: "Cross-functional sustainability governance",
    measurement_approach: "Presence and effectiveness evaluation",
    measurement_values: [
      "Absent",
      "Present but ineffective",
      "Moderately effective",
      "Effective",
      "Highly effective",
    ],
  },
  {
    type: "Strategic & Management Metrics",
    name: "Future-readiness assessment",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Strategic & Management Metrics",
    name: "Sustainability leadership positioning",
    measurement_approach: "Qualitative assessment",
    measurement_values: ["Laggard", "Follower", "Average", "Leader", "Pioneer"],
  },
  {
    type: "Product & Innovation Metrics",
    name: "Sustainable product definition",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Product & Innovation Metrics",
    name: "Sustainable design process",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Product & Innovation Metrics",
    name: "Product lifecycle assessment measurement_approach",
    measurement_approach: "Presence and comprehensiveness evaluation",
    measurement_values: [
      "Not present",
      "Basic",
      "Moderate",
      "Comprehensive",
      "Industry-leading",
    ],
  },
  {
    type: "Product & Innovation Metrics",
    name: "Green chemistry commitment",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Product & Innovation Metrics",
    name: "Sustainable packaging strategy",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Product & Innovation Metrics",
    name: "Circular design principles",
    measurement_approach: "Presence and implementation evaluation",
    measurement_values: [
      "Not implemented",
      "Partially implemented",
      "Moderately implemented",
      "Well implemented",
      "Fully integrated",
    ],
  },
  {
    type: "Product & Innovation Metrics",
    name: "Sustainability innovation measurement_approach",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Product & Innovation Metrics",
    name: "Product environmental impact reduction",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Product & Innovation Metrics",
    name: "Product social impact enhancement",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Product & Innovation Metrics",
    name: "Green marketing guidelines",
    measurement_approach: "Presence and comprehensiveness evaluation",
    measurement_values: [
      "Not present",
      "Basic",
      "Moderate",
      "Comprehensive",
      "Industry-leading",
    ],
  },
  {
    type: "Product & Innovation Metrics",
    name: "Responsible technology development",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Product & Innovation Metrics",
    name: "Hazardous substance management",
    measurement_approach: "Presence and effectiveness evaluation",
    measurement_values: [
      "Absent",
      "Present but ineffective",
      "Moderately effective",
      "Effective",
      "Highly effective",
    ],
  },
  {
    type: "Product & Innovation Metrics",
    name: "Product end-of-life management",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Product & Innovation Metrics",
    name: "Eco-design integration",
    measurement_approach: "Scale assessment",
    measurement_values: ["Basic", "Moderate", "Comprehensive"],
  },
  {
    type: "Supply Chain & Procurement Metrics",
    name: "Supplier code of conduct",
    measurement_approach: "Presence and comprehensiveness evaluation",
    measurement_values: [
      "Not present",
      "Basic",
      "Moderate",
      "Comprehensive",
      "Industry-leading",
    ],
  },
  {
    type: "Supply Chain & Procurement Metrics",
    name: "Supplier ESG screening process",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Supply Chain & Procurement Metrics",
    name: "Supply chain transparency measurement_approach",
    measurement_approach: "Scale assessment",
    measurement_values: ["Basic", "Moderate", "Comprehensive"],
  },
  {
    type: "Supply Chain & Procurement Metrics",
    name: "Supply chain human rights program",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Supply Chain & Procurement Metrics",
    name: "Responsible sourcing policy",
    measurement_approach: "Scale assessment",
    measurement_values: ["Basic", "Moderate", "Comprehensive"],
  },
  {
    type: "Supply Chain & Procurement Metrics",
    name: "Conflict minerals measurement_approach",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Supply Chain & Procurement Metrics",
    name: "Supply chain resilience assessment",
    measurement_approach: "Presence and comprehensiveness evaluation",
    measurement_values: [
      "Not present",
      "Basic",
      "Moderate",
      "Comprehensive",
      "Industry-leading",
    ],
  },
  {
    type: "Supply Chain & Procurement Metrics",
    name: "Supply chain decarbonization strategy",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Supply Chain & Procurement Metrics",
    name: "Supply chain water risk management",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Supply Chain & Procurement Metrics",
    name: "Sustainable logistics measurement_approach",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Supply Chain & Procurement Metrics",
    name: "Supplier diversity program",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Supply Chain & Procurement Metrics",
    name: "Supplier capacity building",
    measurement_approach: "Presence and comprehensiveness evaluation",
    measurement_values: [
      "Not present",
      "Basic",
      "Moderate",
      "Comprehensive",
      "Industry-leading",
    ],
  },
  {
    type: "Supply Chain & Procurement Metrics",
    name: "Supplier relationship management",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Supply Chain & Procurement Metrics",
    name: "Supplier audit program",
    measurement_approach: "Presence and effectiveness evaluation",
    measurement_values: [
      "Absent",
      "Present but ineffective",
      "Moderately effective",
      "Effective",
      "Highly effective",
    ],
  },
  {
    type: "Customer & Consumer Metrics",
    name: "Customer sustainability engagement",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Customer & Consumer Metrics",
    name: "Sustainable consumption promotion",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Customer & Consumer Metrics",
    name: "Consumer education programs",
    measurement_approach: "Presence and comprehensiveness evaluation",
    measurement_values: [
      "Not present",
      "Basic",
      "Moderate",
      "Comprehensive",
      "Industry-leading",
    ],
  },
  {
    type: "Customer & Consumer Metrics",
    name: "Product transparency measurement_approach",
    measurement_approach: "Scale assessment",
    measurement_values: ["Basic", "Moderate", "Comprehensive"],
  },
  {
    type: "Customer & Consumer Metrics",
    name: "Responsible digital practices",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Customer & Consumer Metrics",
    name: "Customer inclusion & accessibility",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Customer & Consumer Metrics",
    name: "Consumer health & wellbeing impact",
    measurement_approach: "Presence and effectiveness evaluation",
    measurement_values: [
      "Absent",
      "Present but ineffective",
      "Moderately effective",
      "Effective",
      "Highly effective",
    ],
  },
  {
    type: "Customer & Consumer Metrics",
    name: "Customer sustainability partnership",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Customer & Consumer Metrics",
    name: "Consumer privacy measurement_approach",
    measurement_approach: "Scale assessment",
    measurement_values: ["Basic", "Moderate", "Comprehensive"],
  },
  {
    type: "Customer & Consumer Metrics",
    name: "Responsible marketing guidelines",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Customer & Consumer Metrics",
    name: "Consumer behavior change initiatives",
    measurement_approach: "Presence and comprehensiveness evaluation",
    measurement_values: [
      "Not present",
      "Basic",
      "Moderate",
      "Comprehensive",
      "Industry-leading",
    ],
  },
  {
    type: "Customer & Consumer Metrics",
    name: "Product labeling transparency",
    measurement_approach: "Scale assessment",
    measurement_values: ["Basic", "Moderate", "Comprehensive"],
  },
  {
    type: "Customer & Consumer Metrics",
    name: "Customer ESG awareness building",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Customer & Consumer Metrics",
    name: "Sustainability benefits communication",
    measurement_approach: "Presence and effectiveness evaluation",
    measurement_values: [
      "Absent",
      "Present but ineffective",
      "Moderately effective",
      "Effective",
      "Highly effective",
    ],
  },
  {
    type: "Industry-Specific Metrics",
    name: "Sectoral climate commitment",
    measurement_approach: "Presence and ambition evaluation",
    measurement_values: [
      "None",
      "Low ambition",
      "Moderate ambition",
      "High ambition",
      "Leading ambition",
    ],
  },
  {
    type: "Industry-Specific Metrics",
    name: "Industry collaboration for sustainability",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Industry-Specific Metrics",
    name: "Sector-specific ESG best practices",
    measurement_approach: "Alignment assessment",
    measurement_values: [
      "Below sector standard",
      "At sector standard",
      "Above sector standard",
    ],
  },
  {
    type: "Industry-Specific Metrics",
    name: "Industry transformation leadership",
    measurement_approach: "Qualitative assessment",
    measurement_values: [
      "Not contributing",
      "Minor contribution",
      "Average contribution",
      "Significant contribution",
      "Transformative leadership",
    ],
  },
  {
    type: "Industry-Specific Metrics",
    name: "Sector decarbonization measurement_approach",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Industry-Specific Metrics",
    name: "Industry-specific ESG risks management",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Industry-Specific Metrics",
    name: "Industry-specific social impact management",
    measurement_approach: "Presence and effectiveness evaluation",
    measurement_values: [
      "Absent",
      "Present but ineffective",
      "Moderately effective",
      "Effective",
      "Highly effective",
    ],
  },
  {
    type: "Industry-Specific Metrics",
    name: "Sector sustainability reporting leadership",
    measurement_approach: "Qualitative assessment",
    measurement_values: ["Laggard", "Follower", "Average", "Leader", "Pioneer"],
  },
  {
    type: "Industry-Specific Metrics",
    name: "Industry sustainability standards adoption",
    measurement_approach: "Scale assessment",
    measurement_values: ["Basic", "Moderate", "Comprehensive"],
  },
  {
    type: "Industry-Specific Metrics",
    name: "Participation in industry sustainability initiatives",
    measurement_approach: "Presence and significance evaluation",
    measurement_values: [
      "None",
      "Minimal",
      "Moderate",
      "Significant",
      "Substantial",
    ],
  },
  {
    type: "Industry-Specific Metrics",
    name: "Sector-specific environmental management",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Industry-Specific Metrics",
    name: "Sector-specific stakeholder engagement",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Industry-Specific Metrics",
    name: "Industry policy advocacy",
    measurement_approach: "Direction and transparency assessment",
    measurement_values: [
      "Regressive and opaque",
      "Neutral and limited transparency",
      "Progressive and transparent",
      "Highly progressive and fully transparent",
    ],
  },
  {
    type: "Industry-Specific Metrics",
    name: "Sector transformation strategy",
    measurement_approach: "Presence and comprehensiveness evaluation",
    measurement_values: [
      "Not present",
      "Basic",
      "Moderate",
      "Comprehensive",
      "Industry-leading",
    ],
  },
  {
    type: "Policy & Advocacy Metrics",
    name: "Climate policy position",
    measurement_approach: "Alignment evaluation",
    measurement_values: ["Opposed", "Neutral", "Supportive", "Leading"],
  },
  {
    type: "Policy & Advocacy Metrics",
    name: "Sustainable finance policy engagement",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Policy & Advocacy Metrics",
    name: "Trade association sustainability alignment",
    measurement_approach: "Assessment of alignment",
    measurement_values: ["Misaligned", "Partially aligned", "Fully aligned"],
  },
  {
    type: "Policy & Advocacy Metrics",
    name: "Public policy disclosure",
    measurement_approach: "Scale assessment",
    measurement_values: ["Basic", "Moderate", "Comprehensive"],
  },
  {
    type: "Policy & Advocacy Metrics",
    name: "ESG regulation readiness",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Policy & Advocacy Metrics",
    name: "Advocacy effectiveness",
    measurement_approach: "Qualitative assessment",
    measurement_values: [
      "Ineffective",
      "Somewhat effective",
      "Moderately effective",
      "Effective",
      "Highly effective",
    ],
  },
  {
    type: "Policy & Advocacy Metrics",
    name: "Responsible lobbying measurement_approach",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Policy & Advocacy Metrics",
    name: "Policy influence transparency",
    measurement_approach: "Scale assessment",
    measurement_values: ["Basic", "Moderate", "Comprehensive"],
  },
  {
    type: "Policy & Advocacy Metrics",
    name: "Climate lobbying alignment",
    measurement_approach: "Assessment of alignment",
    measurement_values: ["Misaligned", "Partially aligned", "Fully aligned"],
  },
  {
    type: "Policy & Advocacy Metrics",
    name: "Industry coalition participation",
    measurement_approach: "Presence and significance evaluation",
    measurement_values: [
      "None",
      "Minimal",
      "Moderate",
      "Significant",
      "Substantial",
    ],
  },
  {
    type: "Policy & Advocacy Metrics",
    name: "Political contributions governance",
    measurement_approach: "Presence and effectiveness evaluation",
    measurement_values: [
      "Absent",
      "Present but ineffective",
      "Moderately effective",
      "Effective",
      "Highly effective",
    ],
  },
  {
    type: "Policy & Advocacy Metrics",
    name: "Stakeholder coalition building",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Policy & Advocacy Metrics",
    name: "Policy engagement governance",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Policy & Advocacy Metrics",
    name: "Legislative affairs transparency",
    measurement_approach: "Scale assessment",
    measurement_values: ["Basic", "Moderate", "Comprehensive"],
  },
  {
    type: "Reporting & Transparency Metrics",
    name: "Sustainability reporting quality",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Reporting & Transparency Metrics",
    name: "Reporting framework alignment",
    measurement_approach: "Scale assessment",
    measurement_values: ["Basic", "Moderate", "Comprehensive"],
  },
  {
    type: "Reporting & Transparency Metrics",
    name: "External assurance measurement_approach",
    measurement_approach: "Scope and rigor assessment",
    measurement_values: [
      "No assurance",
      "Limited scope",
      "Moderate scope",
      "Comprehensive scope",
      "Rigorous and comprehensive",
    ],
  },
  {
    type: "Reporting & Transparency Metrics",
    name: "Stakeholder input in reporting",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Reporting & Transparency Metrics",
    name: "Disclosure effectiveness",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Reporting & Transparency Metrics",
    name: "Transparency on challenges",
    measurement_approach: "Scale assessment",
    measurement_values: ["Basic", "Moderate", "Comprehensive"],
  },
  {
    type: "Reporting & Transparency Metrics",
    name: "ESG data governance",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Reporting & Transparency Metrics",
    name: "Double materiality assessment",
    measurement_approach: "Presence and comprehensiveness evaluation",
    measurement_values: [
      "Not present",
      "Basic",
      "Moderate",
      "Comprehensive",
      "Industry-leading",
    ],
  },
  {
    type: "Reporting & Transparency Metrics",
    name: "Forward-looking disclosure",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Reporting & Transparency Metrics",
    name: "Reporting on targets vs performance",
    measurement_approach: "Comprehensiveness evaluation",
    measurement_values: [
      "None",
      "Limited",
      "Basic",
      "Comprehensive",
      "Holistic",
    ],
  },
  {
    type: "Reporting & Transparency Metrics",
    name: "Value chain transparency",
    measurement_approach: "Scale assessment",
    measurement_values: ["Basic", "Moderate", "Comprehensive"],
  },
  {
    type: "Reporting & Transparency Metrics",
    name: "Integrated reporting measurement_approach",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Reporting & Transparency Metrics",
    name: "ESG data controls",
    measurement_approach: "Presence and effectiveness evaluation",
    measurement_values: [
      "Absent",
      "Present but ineffective",
      "Moderately effective",
      "Effective",
      "Highly effective",
    ],
  },
  {
    type: "Reporting & Transparency Metrics",
    name: "Sustainability narrative quality",
    measurement_approach: "Qualitative assessment",
    measurement_values: ["Poor", "Basic", "Good", "Excellent", "Outstanding"],
  },
  {
    type: "Strategic Partnerships & Collaboration Metrics",
    name: "Multi-stakeholder initiative participation",
    measurement_approach: "Presence and significance evaluation",
    measurement_values: [
      "None",
      "Minimal",
      "Moderate",
      "Significant",
      "Substantial",
    ],
  },
  {
    type: "Strategic Partnerships & Collaboration Metrics",
    name: "Pre-competitive collaboration",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Strategic Partnerships & Collaboration Metrics",
    name: "NGO partnership measurement_approach",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Strategic Partnerships & Collaboration Metrics",
    name: "Academic research collaboration",
    measurement_approach: "Presence and significance evaluation",
    measurement_values: [
      "None",
      "Minimal",
      "Moderate",
      "Significant",
      "Substantial",
    ],
  },
  {
    type: "Strategic Partnerships & Collaboration Metrics",
    name: "Public-private partnerships",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Strategic Partnerships & Collaboration Metrics",
    name: "Cross-sector collaboration",
    measurement_approach: "Presence and effectiveness evaluation",
    measurement_values: [
      "Absent",
      "Present but ineffective",
      "Moderately effective",
      "Effective",
      "Highly effective",
    ],
  },
  {
    type: "Strategic Partnerships & Collaboration Metrics",
    name: "Sustainability coalition leadership",
    measurement_approach: "Qualitative assessment",
    measurement_values: [
      "No involvement",
      "Member only",
      "Active participant",
      "Leader",
      "Founding/driving force",
    ],
  },
  {
    type: "Strategic Partnerships & Collaboration Metrics",
    name: "Collective impact initiatives",
    measurement_approach: "Presence and significance evaluation",
    measurement_values: [
      "None",
      "Minimal",
      "Moderate",
      "Significant",
      "Substantial",
    ],
  },
  {
    type: "Strategic Partnerships & Collaboration Metrics",
    name: "Supply chain collaboration",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Strategic Partnerships & Collaboration Metrics",
    name: "Community partnership measurement_approach",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Strategic Partnerships & Collaboration Metrics",
    name: "Innovation ecosystem engagement",
    measurement_approach: "Presence and effectiveness evaluation",
    measurement_values: [
      "Absent",
      "Present but ineffective",
      "Moderately effective",
      "Effective",
      "Highly effective",
    ],
  },
  {
    type: "Strategic Partnerships & Collaboration Metrics",
    name: "Systems change initiatives",
    measurement_approach: "Maturity assessment",
    measurement_values: [1, 2, 3, 4, 5],
  },
  {
    type: "Strategic Partnerships & Collaboration Metrics",
    name: "Partnership governance",
    measurement_approach: "Presence and quality evaluation",
    measurement_values: [
      "Absent",
      "Present but limited",
      "Adequate",
      "Robust",
      "Exemplary",
    ],
  },
  {
    type: "Strategic Partnerships & Collaboration Metrics",
    name: "Partnership impact measurement",
    measurement_approach: "Presence and comprehensiveness evaluation",
    measurement_values: [
      "Not present",
      "Basic",
      "Moderate",
      "Comprehensive",
      "Industry-leading",
    ],
  },
];

export default Quantitative;
