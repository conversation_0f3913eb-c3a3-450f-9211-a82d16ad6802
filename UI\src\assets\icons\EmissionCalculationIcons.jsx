export const DefaultColoredIcon = () => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.19 2H7.81C4.17 2 2 4.17 2 7.81V16.18C2 19.83 4.17 22 7.81 22H16.18C19.82 22 21.99 19.83 21.99 16.19V7.81C22 4.17 19.83 2 16.19 2ZM9.91 16.19C9.91 16.83 9.39 17.35 8.74 17.35C8.1 17.35 7.58 16.83 7.58 16.19V12.93C7.58 12.29 8.1 11.77 8.74 11.77C9.39 11.77 9.91 12.29 9.91 12.93V16.19ZM16.42 16.19C16.42 16.83 15.9 17.35 15.26 17.35C14.61 17.35 14.09 16.83 14.09 16.19V7.81C14.09 7.17 14.61 6.65 15.26 6.65C15.9 6.65 16.42 7.17 16.42 7.81V16.19Z"
        fill="url(#paint0_linear_40008422_110097)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40008422_110097"
          x1="1.62425"
          y1="24.9487"
          x2="26.0589"
          y2="22.8193"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={`#2C5A8C`} />
          <stop offset="0.46" stopColor={`#1C889C`} />
          <stop offset="1" stopColor={`#13B1A8'`} />
        </linearGradient>
      </defs>
    </svg>
  );
};
export const DefaultUnColoredIcon = () => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.19 2H7.81C4.17 2 2 4.17 2 7.81V16.18C2 19.83 4.17 22 7.81 22H16.18C19.82 22 21.99 19.83 21.99 16.19V7.81C22 4.17 19.83 2 16.19 2ZM9.91 16.19C9.91 16.83 9.39 17.35 8.74 17.35C8.1 17.35 7.58 16.83 7.58 16.19V12.93C7.58 12.29 8.1 11.77 8.74 11.77C9.39 11.77 9.91 12.29 9.91 12.93V16.19ZM16.42 16.19C16.42 16.83 15.9 17.35 15.26 17.35C14.61 17.35 14.09 16.83 14.09 16.19V7.81C14.09 7.17 14.61 6.65 15.26 6.65C15.9 6.65 16.42 7.17 16.42 7.81V16.19Z"
        fill="#8F8F8F"
      />
    </svg>
  );
};

export const CollectIconColord = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6.17969 7.5H9.34961C9.48913 7.5 9.59961 7.61179 9.59961 7.75V9.91992C9.59961 11.9871 11.2749 13.6504 13.3301 13.6504H15.5C15.6382 13.6504 15.75 13.7609 15.75 13.9004V17.8203C15.7499 19.0374 15.3285 19.9454 14.6836 20.5508C14.0338 21.1606 13.1179 21.4999 12.0703 21.5H6.17969C5.13211 21.4999 4.21621 21.1606 3.56641 20.5508C2.92155 19.9454 2.50007 19.0374 2.5 17.8203V11.1797L2.50488 10.9551C2.55357 9.85029 2.96186 9.0168 3.56641 8.44922C4.1757 7.87737 5.01895 7.54337 5.98535 7.50391L6.17969 7.5Z"
      fill="url(#paint0_linear_40008422_110546)"
      stroke="url(#paint1_linear_40008422_110546)"
    />
    <path
      d="M11.9316 2.5H17.8213C18.8691 2.5 19.7857 2.83922 20.4355 3.44922C21.0804 4.05464 21.5019 4.96257 21.502 6.17969V12.8301C21.5019 14.0151 21.1009 14.9031 20.4844 15.5039C19.9738 16.0013 19.2898 16.325 18.502 16.4424V10.1797C18.5019 8.71714 17.9876 7.53517 17.1201 6.7207C16.2575 5.91085 15.0834 5.5 13.8213 5.5H8.30762C8.43797 4.59689 8.82554 3.90135 9.36523 3.41113C10.01 2.82547 10.9069 2.50002 11.9316 2.5Z"
      fill="url(#paint2_linear_40008422_110546)"
      stroke="url(#paint3_linear_40008422_110546)"
    />
    <path
      d="M11.6406 7.51465C12.7267 8.62025 14.6091 10.5121 15.7031 11.6064C15.2938 11.6064 14.8559 11.6076 14.4346 11.6064L13.2178 11.5977H13.2109C12.331 11.5977 11.6406 10.9057 11.6406 10.0977V7.51465Z"
      fill="url(#paint4_linear_40008422_110546)"
      stroke="url(#paint5_linear_40008422_110546)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_40008422_110546"
        x1="1.73214"
        y1="24.2115"
        x2="19.1632"
        y2="22.7677"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#2C5A8C" />
        <stop offset="0.46" stopColor="#1C889C" />
        <stop offset="1" stopColor="#13B1A8" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_40008422_110546"
        x1="1.73214"
        y1="24.2115"
        x2="19.1632"
        y2="22.7677"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#2C5A8C" />
        <stop offset="0.46" stopColor="#1C889C" />
        <stop offset="1" stopColor="#13B1A8" />
      </linearGradient>
      <linearGradient
        id="paint2_linear_40008422_110546"
        x1="7.49405"
        y1="19.2001"
        x2="24.9129"
        y2="17.7573"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#2C5A8C" />
        <stop offset="0.46" stopColor="#1C889C" />
        <stop offset="1" stopColor="#13B1A8" />
      </linearGradient>
      <linearGradient
        id="paint3_linear_40008422_110546"
        x1="7.49405"
        y1="19.2001"
        x2="24.9129"
        y2="17.7573"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#2C5A8C" />
        <stop offset="0.46" stopColor="#1C889C" />
        <stop offset="1" stopColor="#13B1A8" />
      </linearGradient>
      <linearGradient
        id="paint4_linear_40008422_110546"
        x1="11.0446"
        y1="12.8609"
        x2="17.2888"
        y2="12.3164"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#2C5A8C" />
        <stop offset="0.46" stopColor="#1C889C" />
        <stop offset="1" stopColor="#13B1A8" />
      </linearGradient>
      <linearGradient
        id="paint5_linear_40008422_110546"
        x1="11.0446"
        y1="12.8609"
        x2="17.2888"
        y2="12.3164"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#2C5A8C" />
        <stop offset="0.46" stopColor="#1C889C" />
        <stop offset="1" stopColor="#13B1A8" />
      </linearGradient>
    </defs>
  </svg>
);

export const CollectIconUnColord = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6.17969 7.5H9.34961C9.48913 7.5 9.59961 7.61179 9.59961 7.75V9.91992C9.59961 11.9871 11.2749 13.6504 13.3301 13.6504H15.5C15.6382 13.6504 15.75 13.7609 15.75 13.9004V17.8203C15.7499 19.0374 15.3285 19.9454 14.6836 20.5508C14.0338 21.1606 13.1179 21.4999 12.0703 21.5H6.17969C5.13211 21.4999 4.21621 21.1606 3.56641 20.5508C2.92155 19.9454 2.50007 19.0374 2.5 17.8203V11.1797L2.50488 10.9551C2.55357 9.85029 2.96186 9.0168 3.56641 8.44922C4.1757 7.87737 5.01895 7.54337 5.98535 7.50391L6.17969 7.5Z"
      fill="#8F8F8F"
      stroke="#8F8F8F"
    />
    <path
      d="M11.9316 2.5H17.8213C18.8691 2.5 19.7857 2.83922 20.4355 3.44922C21.0804 4.05464 21.5019 4.96257 21.502 6.17969V12.8301C21.5019 14.0151 21.1009 14.9031 20.4844 15.5039C19.9738 16.0013 19.2898 16.325 18.502 16.4424V10.1797C18.5019 8.71714 17.9876 7.53517 17.1201 6.7207C16.2575 5.91085 15.0834 5.5 13.8213 5.5H8.30762C8.43797 4.59689 8.82554 3.90135 9.36523 3.41113C10.01 2.82547 10.9069 2.50002 11.9316 2.5Z"
      fill="#8F8F8F"
      stroke="#8F8F8F"
    />
    <path
      d="M11.6406 7.51465C12.7267 8.62025 14.6091 10.5121 15.7031 11.6064C15.2938 11.6064 14.8559 11.6076 14.4346 11.6064L13.2178 11.5977H13.2109C12.331 11.5977 11.6406 10.9057 11.6406 10.0977V7.51465Z"
      fill="#8F8F8F"
      stroke="#8F8F8F"
    />
  </svg>
);

export const ReviewIconColord = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.3517 2H9.65172C8.61172 2 7.76172 2.84 7.76172 3.88V4.82C7.76172 5.86 8.60172 6.7 9.64172 6.7H14.3517C15.3917 6.7 16.2317 5.86 16.2317 4.82V3.88C16.2417 2.84 15.3917 2 14.3517 2Z"
      fill="url(#paint0_linear_40008422_110678)"
    />
    <path
      d="M17.2391 4.8234C17.2391 6.4134 15.9391 7.7134 14.3491 7.7134H9.64906C8.05906 7.7134 6.75906 6.4134 6.75906 4.8234C6.75906 4.2634 6.15906 3.9134 5.65906 4.1734C4.24906 4.9234 3.28906 6.4134 3.28906 8.1234V17.5334C3.28906 19.9934 5.29906 22.0034 7.75906 22.0034H16.2391C18.6991 22.0034 20.7091 19.9934 20.7091 17.5334V8.1234C20.7091 6.4134 19.7491 4.9234 18.3391 4.1734C17.8391 3.9134 17.2391 4.2634 17.2391 4.8234ZM15.3391 12.7334L11.3391 16.7334C11.1891 16.8834 10.9991 16.9534 10.8091 16.9534C10.6191 16.9534 10.4291 16.8834 10.2791 16.7334L8.77906 15.2334C8.48906 14.9434 8.48906 14.4634 8.77906 14.1734C9.06906 13.8834 9.54906 13.8834 9.83906 14.1734L10.8091 15.1434L14.2791 11.6734C14.5691 11.3834 15.0491 11.3834 15.3391 11.6734C15.6291 11.9634 15.6291 12.4434 15.3391 12.7334Z"
      fill="url(#paint1_linear_40008422_110678)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_40008422_110678"
        x1="7.60251"
        y1="7.39295"
        x2="17.7831"
        y2="5.79327"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#2C5A8C" />
        <stop offset="0.46" stopColor="#1C889C" />
        <stop offset="1" stopColor="#13B1A8" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_40008422_110678"
        x1="2.96162"
        y1="24.6451"
        x2="24.2634"
        y2="22.8393"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#2C5A8C" />
        <stop offset="0.46" stopColor="#1C889C" />
        <stop offset="1" stopColor="#13B1A8" />
      </linearGradient>
    </defs>
  </svg>
);

export const ReviewIconUncolord = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.3517 2H9.65172C8.61172 2 7.76172 2.84 7.76172 3.88V4.82C7.76172 5.86 8.60172 6.7 9.64172 6.7H14.3517C15.3917 6.7 16.2317 5.86 16.2317 4.82V3.88C16.2417 2.84 15.3917 2 14.3517 2Z"
      fill="#8F8F8F"
    />
    <path
      d="M17.2391 4.8234C17.2391 6.4134 15.9391 7.7134 14.3491 7.7134H9.64906C8.05906 7.7134 6.75906 6.4134 6.75906 4.8234C6.75906 4.2634 6.15906 3.9134 5.65906 4.1734C4.24906 4.9234 3.28906 6.4134 3.28906 8.1234V17.5334C3.28906 19.9934 5.29906 22.0034 7.75906 22.0034H16.2391C18.6991 22.0034 20.7091 19.9934 20.7091 17.5334V8.1234C20.7091 6.4134 19.7491 4.9234 18.3391 4.1734C17.8391 3.9134 17.2391 4.2634 17.2391 4.8234ZM15.3391 12.7334L11.3391 16.7334C11.1891 16.8834 10.9991 16.9534 10.8091 16.9534C10.6191 16.9534 10.4291 16.8834 10.2791 16.7334L8.77906 15.2334C8.48906 14.9434 8.48906 14.4634 8.77906 14.1734C9.06906 13.8834 9.54906 13.8834 9.83906 14.1734L10.8091 15.1434L14.2791 11.6734C14.5691 11.3834 15.0491 11.3834 15.3391 11.6734C15.6291 11.9634 15.6291 12.4434 15.3391 12.7334Z"
      fill="#8F8F8F"
    />
  </svg>
);

export const MeasureIconColord = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.6133 1.75293C15.0843 1.76138 16.304 1.79524 17.3115 1.93066C18.6882 2.11575 19.7805 2.50313 20.6387 3.36133C21.4969 4.21953 21.8842 5.31182 22.0693 6.68848C22.2499 8.03174 22.25 9.75212 22.25 11.9424V12.0576C22.25 14.2479 22.2499 15.9683 22.0693 17.3115C21.8842 18.6882 21.4969 19.7805 20.6387 20.6387C19.7805 21.4969 18.6882 21.8842 17.3115 22.0693C15.9683 22.2499 14.2479 22.25 12.0576 22.25H11.9424C9.75214 22.25 8.03173 22.2499 6.68848 22.0693C5.31182 21.8842 4.21953 21.4969 3.36133 20.6387C2.50313 19.7805 2.11575 18.6882 1.93066 17.3115C1.75012 15.9683 1.74999 14.2479 1.75 12.0576V11.9424L1.75293 10.3867C1.76138 8.91571 1.79524 7.69601 1.93066 6.68848C2.11575 5.31182 2.50313 4.21953 3.36133 3.36133C4.21953 2.50313 5.31182 2.11575 6.68848 1.93066C8.03174 1.75012 9.75213 1.74999 11.9424 1.75H12.0576L13.6133 1.75293ZM17.4941 8.43555C17.1825 8.16283 16.7083 8.19427 16.4355 8.50586L12.9971 12.4365C12.688 12.3163 12.3516 12.25 12 12.25C10.4812 12.25 9.25001 13.4812 9.25 15C9.25 16.5188 10.4812 17.75 12 17.75C13.5188 17.75 14.75 16.5188 14.75 15C14.75 14.3782 14.5433 13.8044 14.1953 13.3438L17.5645 9.49414C17.8372 9.18246 17.8057 8.70833 17.4941 8.43555ZM12 5.25C8.27208 5.25 5.25 8.27208 5.25 12C5.25 12.4142 5.58579 12.75 6 12.75C6.41421 12.75 6.75 12.4142 6.75 12C6.75 9.1005 9.1005 6.75 12 6.75C12.9574 6.75 13.8526 7.00597 14.624 7.45215C14.9825 7.65951 15.4419 7.53705 15.6494 7.17871C15.8568 6.82025 15.7343 6.3608 15.376 6.15332C14.3824 5.57855 13.2282 5.25 12 5.25Z"
      fill="url(#paint0_linear_40008422_110929)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_40008422_110929"
        x1="1.36466"
        y1="25.2724"
        x2="26.4225"
        y2="23.0876"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#2C5A8C" />
        <stop offset="0.46" stopColor="#1C889C" />
        <stop offset="1" stopColor="#13B1A8" />
      </linearGradient>
    </defs>
  </svg>
);

export const MeasureIconUnColord = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.6133 1.75293C15.0843 1.76138 16.304 1.79524 17.3115 1.93066C18.6882 2.11575 19.7805 2.50313 20.6387 3.36133C21.4969 4.21953 21.8842 5.31182 22.0693 6.68848C22.2499 8.03174 22.25 9.75212 22.25 11.9424V12.0576C22.25 14.2479 22.2499 15.9683 22.0693 17.3115C21.8842 18.6882 21.4969 19.7805 20.6387 20.6387C19.7805 21.4969 18.6882 21.8842 17.3115 22.0693C15.9683 22.2499 14.2479 22.25 12.0576 22.25H11.9424C9.75214 22.25 8.03173 22.2499 6.68848 22.0693C5.31182 21.8842 4.21953 21.4969 3.36133 20.6387C2.50313 19.7805 2.11575 18.6882 1.93066 17.3115C1.75012 15.9683 1.74999 14.2479 1.75 12.0576V11.9424L1.75293 10.3867C1.76138 8.91571 1.79524 7.69601 1.93066 6.68848C2.11575 5.31182 2.50313 4.21953 3.36133 3.36133C4.21953 2.50313 5.31182 2.11575 6.68848 1.93066C8.03174 1.75012 9.75213 1.74999 11.9424 1.75H12.0576L13.6133 1.75293ZM17.4941 8.43555C17.1825 8.16283 16.7083 8.19427 16.4355 8.50586L12.9971 12.4365C12.688 12.3163 12.3516 12.25 12 12.25C10.4812 12.25 9.25001 13.4812 9.25 15C9.25 16.5188 10.4812 17.75 12 17.75C13.5188 17.75 14.75 16.5188 14.75 15C14.75 14.3782 14.5433 13.8044 14.1953 13.3438L17.5645 9.49414C17.8372 9.18246 17.8057 8.70833 17.4941 8.43555ZM12 5.25C8.27208 5.25 5.25 8.27208 5.25 12C5.25 12.4142 5.58579 12.75 6 12.75C6.41421 12.75 6.75 12.4142 6.75 12C6.75 9.1005 9.1005 6.75 12 6.75C12.9574 6.75 13.8526 7.00597 14.624 7.45215C14.9825 7.65951 15.4419 7.53705 15.6494 7.17871C15.8568 6.82025 15.7343 6.3608 15.376 6.15332C14.3824 5.57855 13.2282 5.25 12 5.25Z"
      fill="#8F8F8F"
    />
  </svg>
);

export const ReportIconUnColord = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16 2H8C4.5 2 3 4 3 7V17C3 20 4.5 22 8 22H16C19.5 22 21 20 21 17V7C21 4 19.5 2 16 2ZM8 12.25H12C12.41 12.25 12.75 12.59 12.75 13C12.75 13.41 12.41 13.75 12 13.75H8C7.59 13.75 7.25 13.41 7.25 13C7.25 12.59 7.59 12.25 8 12.25ZM16 17.75H8C7.59 17.75 7.25 17.41 7.25 17C7.25 16.59 7.59 16.25 8 16.25H16C16.41 16.25 16.75 16.59 16.75 17C16.75 17.41 16.41 17.75 16 17.75ZM18.5 9.25H16.5C14.98 9.25 13.75 8.02 13.75 6.5V4.5C13.75 4.09 14.09 3.75 14.5 3.75C14.91 3.75 15.25 4.09 15.25 4.5V6.5C15.25 7.19 15.81 7.75 16.5 7.75H18.5C18.91 7.75 19.25 8.09 19.25 8.5C19.25 8.91 18.91 9.25 18.5 9.25Z"
      fill="#8F8F8F"
    />
  </svg>
);

export const ReportIconColord = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16 2H8C4.5 2 3 4 3 7V17C3 20 4.5 22 8 22H16C19.5 22 21 20 21 17V7C21 4 19.5 2 16 2ZM8 12.25H12C12.41 12.25 12.75 12.59 12.75 13C12.75 13.41 12.41 13.75 12 13.75H8C7.59 13.75 7.25 13.41 7.25 13C7.25 12.59 7.59 12.25 8 12.25ZM16 17.75H8C7.59 17.75 7.25 17.41 7.25 17C7.25 16.59 7.59 16.25 8 16.25H16C16.41 16.25 16.75 16.59 16.75 17C16.75 17.41 16.41 17.75 16 17.75ZM18.5 9.25H16.5C14.98 9.25 13.75 8.02 13.75 6.5V4.5C13.75 4.09 14.09 3.75 14.5 3.75C14.91 3.75 15.25 4.09 15.25 4.5V6.5C15.25 7.19 15.81 7.75 16.5 7.75H18.5C18.91 7.75 19.25 8.09 19.25 8.5C19.25 8.91 18.91 9.25 18.5 9.25Z"
      fill="url(#paint0_linear_40008422_111181)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_40008422_111181"
        x1="2.66165"
        y1="24.9487"
        x2="24.6952"
        y2="23.2197"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#2C5A8C" />
        <stop offset="0.46" stopColor="#1C889C" />
        <stop offset="1" stopColor="#13B1A8" />
      </linearGradient>
    </defs>
  </svg>
);

export const DataQualiltyIconUnColord = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 15C15.7279 15 18.75 12.0899 18.75 8.5C18.75 4.91015 15.7279 2 12 2C8.27208 2 5.25 4.91015 5.25 8.5C5.25 12.0899 8.27208 15 12 15Z"
      fill="#8F8F8F"
    />
    <path
      d="M15.79 15.6091C16.12 15.4391 16.5 15.6891 16.5 16.0591V20.9091C16.5 21.8091 15.87 22.2491 15.09 21.8791L12.41 20.6091C12.18 20.5091 11.82 20.5091 11.59 20.6091L8.91 21.8791C8.13 22.2391 7.5 21.7991 7.5 20.8991L7.52 16.0591C7.52 15.6891 7.91 15.4491 8.23 15.6091C9.36 16.1791 10.64 16.4991 12 16.4991C13.36 16.4991 14.65 16.1791 15.79 15.6091Z"
      fill="#8F8F8F"
    />
  </svg>
);

export const DataQualiltyIconColord = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 15C15.7279 15 18.75 12.0899 18.75 8.5C18.75 4.91015 15.7279 2 12 2C8.27208 2 5.25 4.91015 5.25 8.5C5.25 12.0899 8.27208 15 12 15Z"
      fill="url(#paint0_linear_40008515_174167)"
    />
    <path
      d="M15.79 15.6091C16.12 15.4391 16.5 15.6891 16.5 16.0591V20.9091C16.5 21.8091 15.87 22.2491 15.09 21.8791L12.41 20.6091C12.18 20.5091 11.82 20.5091 11.59 20.6091L8.91 21.8791C8.13 22.2391 7.5 21.7991 7.5 20.8991L7.52 16.0591C7.52 15.6891 7.91 15.4491 8.23 15.6091C9.36 16.1791 10.64 16.4991 12 16.4991C13.36 16.4991 14.65 16.1791 15.79 15.6091Z"
      fill="url(#paint1_linear_40008515_174167)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_40008515_174167"
        x1="4.99624"
        y1="16.9167"
        x2="21.488"
        y2="15.4234"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#2C5A8C" />
        <stop offset="0.46" stopColor="#1C889C" />
        <stop offset="1" stopColor="#13B1A8" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_40008515_174167"
        x1="7.33083"
        y1="22.9622"
        x2="18.2541"
        y2="21.6344"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#2C5A8C" />
        <stop offset="0.46" stopColor="#1C889C" />
        <stop offset="1" stopColor="#13B1A8" />
      </linearGradient>
    </defs>
  </svg>
);

export const TransportationIconColord = () => (
  <svg
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16.44 2H8.06C4.42 2 2.25 4.17 2.25 7.81V16.18C2.25 19.83 4.42 22 8.06 22H16.43C20.07 22 22.24 19.83 22.24 16.19V7.81C22.25 4.17 20.08 2 16.44 2ZM10.16 16.19C10.16 16.83 9.64 17.35 8.99 17.35C8.35 17.35 7.83 16.83 7.83 16.19V12.93C7.83 12.29 8.35 11.77 8.99 11.77C9.64 11.77 10.16 12.29 10.16 12.93V16.19ZM16.67 16.19C16.67 16.83 16.15 17.35 15.51 17.35C14.86 17.35 14.34 16.83 14.34 16.19V7.81C14.34 7.17 14.86 6.65 15.51 6.65C16.15 6.65 16.67 7.17 16.67 7.81V16.19Z"
      fill="url(#paint0_linear_42_11749)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_42_11749"
        x1="1.87425"
        y1="24.9487"
        x2="26.3089"
        y2="22.8193"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#2C5A8C" />
        <stop offset="0.46" stopColor="#1C889C" />
        <stop offset="1" stopColor="#13B1A8" />
      </linearGradient>
    </defs>
  </svg>
);

export const TransportationIconUnColrd = () => (
  <svg
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16.44 2H8.06C4.42 2 2.25 4.17 2.25 7.81V16.18C2.25 19.83 4.42 22 8.06 22H16.43C20.07 22 22.24 19.83 22.24 16.19V7.81C22.25 4.17 20.08 2 16.44 2ZM10.16 16.19C10.16 16.83 9.64 17.35 8.99 17.35C8.35 17.35 7.83 16.83 7.83 16.19V12.93C7.83 12.29 8.35 11.77 8.99 11.77C9.64 11.77 10.16 12.29 10.16 12.93V16.19ZM16.67 16.19C16.67 16.83 16.15 17.35 15.51 17.35C14.86 17.35 14.34 16.83 14.34 16.19V7.81C14.34 7.17 14.86 6.65 15.51 6.65C16.15 6.65 16.67 7.17 16.67 7.81V16.19Z"
      fill="#8F8F8F"
    />
  </svg>
);
