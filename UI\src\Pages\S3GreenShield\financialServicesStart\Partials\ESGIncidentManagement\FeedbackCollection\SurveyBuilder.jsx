import React from "react";
import {
  TextInput,
  Switch,
  Button,
  Title,
  Text,
  Select,
  Textarea,
} from "@mantine/core";
import {
  IconCheck,
  IconX,
  IconTrash,
  IconArrowUp,
  IconArrowDown,
  IconPlus,
} from "@tabler/icons-react";

const SurveyBuilderTab = ({
  register,
  errors,
  control,
  oneTimeresponse,
  setValue,
  groups,
  handleGroupTitleChange,
  handleGroupDescriptionChange,
  handleAddGroup,
  removeGroup,
  handleAddQuestion,
  handleQuestionChange,
  handleOptionChange,
  handleAddOption,
  removeQuestion,
  moveQuestionUp,
  moveQuestionDown,
  onSubmit,
}) => {
  return (
    <>
    
      <div className="mx-auto bg-white p-6 rounded-xl border border-gray-200 shadow-sm">
        <Title order={3} className="mb-4">
          Create Survey
        </Title>

        <form>
          <TextInput
            label="Survey Title"
            placeholder="My Survey"
            {...register("title", { required: "Survey title is required" })}
            error={errors.title?.message}
            className="mb-4"
          />

          <TextInput
            label="Description"
            placeholder="Please answer the following questions"
            {...register("description", {
              required: "Description is required",
            })}
            error={errors.description?.message}
            className="mb-4"
          />

          <Select
            label="Target audience"
            placeholder="Who is this survey for?"
            data={["internal", "external"]}
            error={errors.target?.message}
            className="mb-4"
            value={control._formValues.target || ""}
            onChange={(value) => setValue("target", value)}
          />

          <div className="flex items-center gap-4 mb-6 mt-5">
            <Text className="font-medium">One Time Response</Text>
            <Switch
              checked={oneTimeresponse}
              onChange={(e) =>
                setValue("oneTimeresponse", e.currentTarget.checked)
              }
              onLabel="True"
              offLabel="False"
              size="md"
              color="#07838F"
            />
            <Text className="text-xs">
              {oneTimeresponse
                ? "This survey will accept one response per user"
                : "This survey will accept multiple responses per user"}
            </Text>
          </div>
        </form>
      </div>

      {groups.map((group, groupIndex) => (
        <div
          key={group.id}
          className="space-y-6 mx-auto bg-white p-6 rounded-xl border border-gray-200 shadow-sm mt-6"
        >
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Group {groupIndex + 1}</h2>
              <div className="flex gap-2">
                <Button
                  className="bg-[#07838F] hover:bg-[#07848fad] text-white text-sm"
                  size="xs"
                  onClick={handleAddGroup}
                >
                  Add Group
                </Button>
                {groups.length > 1 && (
                  <Button
                    className="bg-red-500 hover:bg-red-400 text-white text-sm"
                    size="xs"
                    onClick={() => removeGroup(group.id)}
                  >
                    Remove Group
                  </Button>
                )}
              </div>
            </div>
            <TextInput
              label="Group Title"
              placeholder="General Questions"
              value={group.groupTitle}
              onChange={(e) => handleGroupTitleChange(group.id, e.target.value)}
            />
            <Textarea
              label="Group Description (Optional)"
              placeholder="Please answer the following questions about sustainability"
              value={group.groupDescription}
              onChange={(e) =>
                handleGroupDescriptionChange(group.id, e.target.value)
              }
              autosize
              minRows={2}
            />
          </div>
          <hr />
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Questions</h2>
            <Button
              className="bg-[#07838F] hover:bg-[#07848fad] text-white text-sm px-4 py-1 rounded-md"
              onClick={() => handleAddQuestion(group.id)}
            >
              Add Question
            </Button>
          </div>
          <div className="flex flex-col gap-6 px-10">
            {group.questions.map((q, questionIndex) => (
              <div
                key={questionIndex}
                className="bg-white border rounded-xl p-6 shadow-sm space-y-4"
              >
                <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
                  <div className="flex-1">
                    <label className="block text-sm mb-1 font-medium">
                      Your Question
                    </label>
                    <TextInput
                      placeholder="Enter Question"
                      value={q.text}
                      onChange={(e) =>
                        handleQuestionChange(
                          group.id,
                          questionIndex,
                          "text",
                          e.target.value
                        )
                      }
                    />
                  </div>
                  <div className="w-full sm:w-[200px]">
                    <label className="block text-sm mb-1 font-medium">
                      Question Type
                    </label>
                    <Select
                      value={q.type}
                      onChange={(value) =>
                        handleQuestionChange(
                          group.id,
                          questionIndex,
                          "type",
                          value
                        )
                      }
                      data={["radio", "text", "checkbox"]}
                    />
                  </div>
                </div>

                {(q.type === "radio" || q.type === "checkbox") && (
                  <div className="space-y-2">
                    {q.options.map((opt, optIndex) => (
                      <div
                        key={optIndex}
                        className="flex items-center space-x-2"
                      >
                        {q.type === "checkbox" && (
                          <input
                            type="checkbox"
                            disabled
                            className="w-4 h-4 bg-gray-100 border-gray-300 rounded"
                          />
                        )}
                        {q.type === "radio" && (
                          <input
                            type="radio"
                            disabled
                            className="w-4 h-4 bg-gray-100 border-gray-300 rounded"
                            name={`question-${group.id}-${questionIndex}`}
                          />
                        )}
                        <TextInput
                          className="flex-1"
                          value={opt}
                          placeholder={`Option ${optIndex + 1}`}
                          onChange={(e) =>
                            handleOptionChange(
                              group.id,
                              questionIndex,
                              optIndex,
                              e.target.value
                            )
                          }
                        />
                      </div>
                    ))}

                    <div
                      onClick={() => handleAddOption(group.id, questionIndex)}
                      className="flex items-center space-x-2 cursor-pointer text-sm text-[#27272766]"
                    >
                      {q.type === "checkbox" && (
                        <input
                          type="checkbox"
                          disabled
                          className="w-4 h-4 bg-gray-100 border-gray-300 rounded"
                        />
                      )}
                      {q.type === "radio" && (
                        <input
                          type="radio"
                          disabled
                          className="w-4 h-4 bg-gray-100 border-gray-300 rounded-full"
                          name={`question-${group.id}-${questionIndex}`}
                        />
                      )}
                      <span>Add option</span>
                    </div>
                  </div>
                )}

                <div className="flex justify-end items-center">
                  <div className="flex">
                    <Button
                      color="gray"
                      variant="subtle"
                      size="xs"
                      className="hover:bg-transparent shadow-none px-0"
                      leftSection={<IconArrowUp size={16} />}
                      onClick={() => moveQuestionUp(group.id, questionIndex)}
                    />
                    <Button
                      color="gray"
                      variant="subtle"
                      size="xs"
                      className="hover:bg-transparent shadow-none px-1"
                      leftSection={<IconArrowDown size={16} />}
                      onClick={() => moveQuestionDown(group.id, questionIndex)}
                    />
                    <Button
                      color="red"
                      variant="subtle"
                      size="xs"
                      className="hover:bg-transparent shadow-none px-1"
                      leftSection={<IconTrash size={16} />}
                      onClick={() => removeQuestion(group.id, questionIndex)}
                    />
                  </div>
                  <div className="h-5 w-px bg-gray-400 mx-2" />

                  <div className="flex items-center ml-1">
                    <label className="text-sm mr-1">Required</label>
                    <Switch
                      checked={q.required}
                      onChange={(e) =>
                        handleQuestionChange(
                          group.id,
                          questionIndex,
                          "required",
                          e.currentTarget.checked
                        )
                      }
                      color="#07838F"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}

      <div className="flex justify-center gap-8  mt-12  ">
        <Button
          size="md"
          className="bg-[#07838F] hover:bg-[#07848fad] text-white px-6 py-2 rounded-full"
          onClick={onSubmit}
        >
          Save Survey
        </Button>
      </div>
    </>
  );
};

export default SurveyBuilderTab;
