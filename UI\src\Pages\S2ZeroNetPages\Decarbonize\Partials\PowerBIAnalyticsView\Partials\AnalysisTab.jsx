import { <PERSON><PERSON>, <PERSON> } from "@mantine/core";
import sectionImage10 from "@/assets/svg/document-text.svg";
import { FaArrowDown } from "react-icons/fa6";
import { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@mantine/charts";

const data = [
  {
    date: "Mar 22",
    Scope1: 70,
    Scope2: 65,
    Scope3: 65,
  },
  {
    date: "Mar 23",
    Scope1: 80,
    Scope2: 90,
    Scope3: 65,
  },
  {
    date: "Mar 24",
    Scope1: 42,
    Scope2: 78,
    Scope3: 65,
  },
  {
    date: "Mar 25",
    Scope1: 78,
    Scope2: 60,
    Scope3: 70,
  },
  {
    date: "Mar 26",
    Scope1: 62,
    Scope2: 78,
    Scope3: 75,
  },
  {
    date: "Mar 26",
    Scope1: 18,
    Scope2: 16,
    Scope3: 80,
  },
];

const RadarData = [
  {
    month: "Jan",
    Scope1: 120,
    Scope2: 100,
  },
  {
    month: "Feb",
    Scope1: 98,
    Scope2: 90,
  },
  {
    month: "Mar",
    Scope1: 86,
    Scope2: 70,
  },
  {
    month: "Apr",
    Scope1: 99,
    Scope2: 80,
  },
  {
    month: "May",
    Scope1: 85,
    Scope2: 120,
  },
  {
    month: "Jun",
    Scope1: 65,
    Scope2: 150,
  },
];

export default function AnalysisTab() {
  const [climateTab, setClimateTab] = useState("Financial");
  const [emissionsTab, setEmissionsTab] = useState("Total Emissions");

  const climateCharts = {
    Financial: (
      <AreaChart
        h={400}
        data={data}
        dataKey="date"
        series={[
          { name: "Scope1", color: "#FF928A" },
          { name: "Scope2", color: "#8979FF" },
        ]}
        curveType="linear"
        tickLine="none"
        gridAxis="xy"
        strokeWidth={1}
      />
    ),
    Emissions: (
      <BarChart
        h={400}
        data={data}
        dataKey="date"
        type="stacked"
        withLegend
        legendProps={{ verticalAlign: "bottom" }}
        series={[
          { name: "Scope1", color: "#CDE6E9" },
          { name: "Scope2", color: "#6AB5BC" },
          { name: "Scope3", color: "#044F56" },
        ]}
        withXAxis
      />
    ),
    Risks: (
      <RadarChart
        h={400}
        data={RadarData}
        dataKey="month"
        withPolarRadiusAxis={false}
        withTooltip
        withDots
        withLegend
        series={[
          { name: "Scope1", color: "#FF928A", opacity: 0.5 },
          { name: "Scope2", color: "#8979FF", opacity: 0.5 },
        ]}
      />
    ),
    Opportunities: (
      <div className="flex justify-center">
        <DonutChart
          withLabelsLine
          labelsType="value"
          withLabels
          data={[
            { name: "Scope1", value: 500, color: "#8979FF" },
            { name: "Scope2", value: 200, color: "#537FF1" },
            { name: "Scope3", value: 800, color: "#FFAE4C" },
            { name: "Scope4", value: 600, color: "#3CC3DF" },
            { name: "Scope5", value: 700, color: "#FF928A" },
          ]}
          chartLabel="739"
          size={300}
          thickness={60}
          strokeWidth={0}
        />
      </div>
    ),
  };

  const renderClimateChart = () => climateCharts[climateTab] || null;

  const emissionsCharts = {
    "Total Emissions": (
      <LineChart
        h={400}
        data={data}
        dataKey="date"
        series={[{ name: "Scope3", color: "blue.6" }]}
        curveType="linear"
        tickLine="none"
        gridAxis="xy"
        strokeWidth={3}
      />
    ),
    "By Scope": (
      <AreaChart
        h={400}
        data={data}
        dataKey="date"
        withGradient={false}
        series={[
          { name: "Scope1", color: "#d2c1f9" },
          { name: "Scope2", color: "#ffc9b1" },
          { name: "Scope3", color: "#fcb9bb" },
        ]}
        curveType="monotone"
        withDots={false}
        withLegend
        legendProps={{ verticalAlign: 'bottom', height: 50 }}
      />
    ),
    "vs. Scenario": (
      <LineChart
        h={400}
        data={data}
        dataKey="date"
        series={[
          { name: "Scope1", color: "#01a76f" },
          { name: 'Scope2', color: '#006de9' },
        ]}
        curveType="natural"
        tickLine="none"
        gridAxis="xy"
        strokeWidth={4}
      />
    ),
  };

  const renderEmissionsChart = () => emissionsCharts[emissionsTab] || null;

  return (
    <div className="w-full gap-4 overflow-hidden overflow-y-auto flex flex-col pb-6">
      <Button className="flex justify-center w-1/3 rounded-lg border-[#E1DEE0] bg-[#F5F4F5] hover:bg-[#E1DEE0] text-[#555F62] hover:text-[#555F62] self-end">
        <img src={sectionImage10} className="w-6 h-6 mr-2" alt="AI photo" />
        Export report
      </Button>
      
      <div className="flex flex-row w-full gap-4">
        <Card className="w-1/4 items-center justify-center gap-2 p-4">
          <h1 className="text-[#08B12D] font-bold text-4xl">$24.2M</h1>
          <p className="text-[#433F59] font-medium">Total Opportunity Value</p>
          <button className="bg-[#D1FAE5] text-[#065F46] w-full rounded-sm text-xs mt-2">
            +12% from last analysis
          </button>
        </Card>
        <Card className="w-1/4 items-center justify-center gap-2 p-4">
          <h1 className="text-[#EA0B0B] font-bold text-4xl">-$8.5M</h1>
          <p className="text-[#433F59] font-medium">Financial Impact (10yr)</p>
          <button className="bg-[#FEE2E2] text-[#991B1B] w-full rounded-sm text-xs mt-2">
            +12% from last analysis
          </button>
        </Card>
        <Card className="w-1/4 items-center justify-center gap-2 p-4">
          <h1 className="text-[#EA0B0B] font-bold text-4xl">72/100</h1>
          <p className="text-[#433F59] font-medium">Overall Risk Score</p>
          <button className="bg-[#FEE2E2] text-[#991B1B] w-full rounded-sm text-xs mt-2">
            +12% from last analysis
          </button>
        </Card>
        <Card className="w-1/4 items-center justify-center gap-2 p-4">
          <h1 className="text-[#08B12D] font-bold text-4xl">-48%</h1>
          <p className="text-[#433F59] font-medium">Required Reduction</p>
          <button className="bg-[#D1FAE5] text-[#065F46] w-full rounded-sm text-xs mt-2">
            SBT aligned
          </button>
        </Card>
      </div>

      {/* Climate Impact Analysis Section */}
      <div className="bg-white w-full rounded-lg shadow-md flex flex-col p-4 gap-8">
        <div className="flex flex-row gap-2 justify-between w-full">
          <h1 className="self-start font-bold text-lg">
            Climate Impact Analysis
          </h1>
          <div className="flex flex-row gap-2">
            <Button className="bg-[#07838F] hover:bg-[#07838F] rounded-md p-2">
              <FaArrowDown />
            </Button>
            {["Financial", "Emissions", "Risks", "Opportunities"].map((tab) => (
              <Button
                key={tab}
                className={`rounded-md p-2 ${
                  climateTab === tab
                    ? "bg-[#07838F] hover:bg-[#07838F] text-white"
                    : "bg-[#F5F4F5] text-[#555F62] hover:text-[#555F62] hover:bg-[#F5F4F5]"
                }`}
                onClick={() => setClimateTab(tab)}
              >
                {tab}
              </Button>
            ))}
          </div>
        </div>
        {renderClimateChart()}
      </div>

      {/* Projected Emissions Timeline Section */}
      <div className="bg-white w-full rounded-lg shadow-md flex flex-col p-4 gap-8">
        <div className="flex flex-row gap-2 justify-between w-full">
          <h1 className="self-start font-bold text-lg">
            Projected Emissions Timeline
          </h1>
          <div className="flex flex-row gap-2">
            <Button className="bg-[#07838F] hover:bg-[#07838F] rounded-md p-2">
              <FaArrowDown />
            </Button>
            {["Total Emissions", "By Scope", "vs. Scenario"].map((tab) => (
              <Button
                key={tab}
                className={`rounded-md p-2 ${
                  emissionsTab === tab
                    ? "bg-[#07838F] hover:bg-[#07838F] text-white"
                    : "bg-[#F5F4F5] text-[#555F62] hover:text-[#555F62] hover:bg-[#F5F4F5]"
                }`}
                onClick={() => setEmissionsTab(tab)}
              >
                {tab}
              </Button>
            ))}
          </div>
        </div>
        {renderEmissionsChart()}
      </div>
    </div>
  );
}