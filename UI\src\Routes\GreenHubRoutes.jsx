import { Route } from "react-router";
import useRoutes from "./useRoutes";

const GreenHubRoutes = () => {
  const { GreenHubMap } = useRoutes();

  return (
    <>
      {Object.keys(GreenHubMap).map((route, i) => (
        <Route
          key={i}
          path={GreenHubMap[route].path}
          element={GreenHubMap[route].element}
        />
      ))}
    </>
  );
};

export default GreenHubRoutes;
