import React from "react";
import { RadarChart } from "@mantine/charts";

const Scenario2 = () => {
  const data = [
    {
      product: "Energy Sources",
      sales: 120,
    },
    {
      product: "Manufacturing",
      sales: 98,
    },
    {
      product: "Distribution",
      sales: 86,
    },
    {
      product: "Retail Operations",
      sales: 99,
    },
    {
      product: "Product Use",
      sales: 85,
    },
  ];
  return (
    <div>
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-4 rounded-xl bg-[#dfedf0] p-4 border-[#E8E7EA] border-2 dark:bg-[#282828] dark:text-white dark:border-black">
          ANALYSIS RESULTS Scenario 2
        </div>
        <div className="flex flex-row w-full justify-between p-4 items-center gap-4 ">
          <div className="w-1/2 bg-[#dfedf0] p-4 rounded-xl flex justify-center dark:bg-[#282828] dark:text-white dark:border-black">
            <RadarChart
              h={500}
              w={500}
              data={data}
              dataKey="product"
              withTooltip
              withDots
              series={[{ name: "sales", color: "blue", strokeColor: "blue" }]}
            />
          </div>
          <div className="w-1/2 bg-[#dfedf0] p-4 rounded-xl flex justify-center dark:bg-[#282828] dark:text-white dark:border-black">
          < RadarChart
              h={500}
              w={500}
              data={data}
              dataKey="product"
              withTooltip
              withDots
              series={[{ name: "sales", color: "green", strokeColor: "green" }]}
            />
          </div>
          {/* <div className="w-1/4 bg-[#dfedf0] p-4 rounded-xl flex justify-center">
          < RadarChart
              h={250}
              w={300}
              data={data}
              dataKey="product"
              withTooltip
              withDots
              series={[{ name: "sales", color: "red", strokeColor: "red" }]}
            />
          </div>
          <div className="w-1/4 bg-[#dfedf0] p-4 rounded-xl flex justify-center">
          < RadarChart
              h={250}
              w={300}
              data={data}
              dataKey="product"
              withTooltip
              withDots
              series={[{ name: "sales", color: "yellow", strokeColor: "yellow" }]}
            />
          </div> */}
        </div>
      </div>
    </div>
  );
};

export default Scenario2;
