import React, { useEffect } from "react";
import Quiz from "./Quiz";
import CategorySidebar from "./CategorySidebar";
import { useSustain360Store } from "@/Store/Assessment/useSustain360Store";
import Loading from "../../../../Components/Loading";

const QuizLayout = () => {
  const { QuizLoading } = useSustain360Store();
  return (
    <div>
      <div>
        <CategorySidebar />
      </div>

      <div>
        <div>
          {QuizLoading ? <Loading /> : <Quiz />}
        </div>
      </div>
    </div>
  );
};

export default QuizLayout;
