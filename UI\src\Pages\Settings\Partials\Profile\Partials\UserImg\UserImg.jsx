import { Text, Group } from '@mantine/core';
import ApiProfile from "@/Api/apiProfileConfig";
import classes from './UserInfoIcons.module.css';
import { UserAvatar } from './UserAvatar';
import Cookies from 'js-cookie';
import { useUserStore } from '@/Store/useUser';
import { showNotification } from "@mantine/notifications";
import { IconX as IconXNotification } from "@tabler/icons-react";

const Ripple = ({delay,reverse}) => {
    return (
        <div
            className={`${classes.avatarRipple} absolute inset-0 rounded-full border-2 border-blue-500 opacity-50 animate-ripple pointer-events-none delay-${delay} ${reverse ? 'animate-[reverse]' : ''}`}
        ></div>
    );
}
export function UserImg({ userName , userEmail, isEdit , setProfileUpdates}) {
    const { updateAvatar,setLoading ,setTempAvatar} = useUserStore((state) => state);
    const onAvatarClick = async () => {
        if(!isEdit) return;
        const input = document.createElement("input");
        input.type = "file";
        input.accept = "image/*";

        input.onchange = async (e) => {
            const file = e.target.files[0];
            if (!file) return;
            const acceptedTypes = ["image/jpeg", "image/png","image/jpg","image/webp"];
            if (!acceptedTypes.includes(file.type)) {
                showNotification({
                    title: "Error",
                    message: "Invalid file type. Only JPEG, PNG, JPG, and WebP are allowed.",
                    color: "red",
                    icon: <IconXNotification />,
                });
                return;
            }
            try {
                setLoading(true);
                setProfileUpdates({ avatar: file });
                const reader = new FileReader();
                reader.onloadend = () => {
                    setTempAvatar(reader.result);
                }
                reader.readAsDataURL(file);

                setLoading(false);
            } catch (error) {
            console.error("Upload failed", error);
            showNotification({
                title: "Error",
                message: error.response?.data?.message || "Failed to update avatar",
                color: "red",
                icon: <IconXNotification />,
            });
            }
            
        };

        input.click();
    }
    return (
        <div>
            <Group wrap="nowrap">
                {/* <Avatar
                    src="https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-2.png"
                    size={94} className='rounded-full'
                /> */}
                <div className={'' + (isEdit ? 'cursor-pointer rounded-full relative' : 'relative')} onClick={onAvatarClick}>
                    <UserAvatar userName={userName} size={94}/>
                    {isEdit && <><Ripple delay={0.2}/><Ripple delay={0.5} reverse={true}/><Ripple delay={0.8}/></> }
                </div>
                <div>

                    <Text fz="lg" fw={500} className={classes.name}>
                        {userName}
                    </Text>

                    <Group wrap="nowrap" gap={10} mt={3}>
                        {/* <IconAt stroke={1.5} size="1rem" className={classes.icon} /> */}
                        <Text fz="xs" c="dimmed">
                            {userEmail}
                        </Text>
                    </Group>
                </div>
                </Group>
        </div>
    );
}