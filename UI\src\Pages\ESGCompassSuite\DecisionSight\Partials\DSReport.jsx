import MainLayout from "@/Layout/MainLayout";
import React from "react";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import { RingProgress, Text } from "@mantine/core";
import { AreaChart } from "@mantine/charts";
import { YearPickerInput } from "@mantine/dates";
import { IoIosArrowDown } from "react-icons/io";
import { Alert } from "@mantine/core";
import { CiCircleCheck, CiShare2 } from "react-icons/ci";
import { MdOutlineRemoveRedEye } from "react-icons/md";
import { FiDownload } from "react-icons/fi";
import reportImg from "@/assets/images/mountain-logo.png";
import { useTranslation } from "react-i18next";

const data = [
  {
    date: "Jan",
    Progress: 2.5,
  },
  {
    date: "Feb",
    Progress: 4,
  },
  {
    date: "Mar",
    Progress: 3.8,
  },
  {
    date: "Apr",
    Progress: 3,
  },
  {
    date: "May",
    Progress: 2.5,
  },
  {
    date: "Jun",
    Progress: 3.3,
  },
  {
    date: "Jul",
    Progress: 4.5,
  },
  {
    date: "Aug",
    Progress: 4,
  },
  {
    date: "Sep",
    Progress: 2.2,
  },
  {
    date: "Oct",
    Progress: 2.3,
  },
  {
    date: "Nov",
    Progress: 2.2,
  },
  {
    date: "Dec",
    Progress: 1.8,
  },
];

export default function DSReport() {
  const { DecisionSightMenu } = useSideBarRoute();
  const { t } = useTranslation();

  return (
    <MainLayout menus={DecisionSightMenu}>
      <div className="flex flex-col w-full left-section min-h-svh">
        <div className="report-page flex justify-between gap-y-3 flex-wrap mb-[38px]">
          <div className="year-pick bg-secondary-300 px-3 flex items-center overflow-hidden rounded-xl lg:w-[214px] lg:h-[52px] border-[1px] text-white">
            <YearPickerInput
              w={"100%"}
              bg={"#00C0A9"}
              radius={"sm"}
              variant="unstyled"
              size="lg"
              placeholder={t("reportingYear")}
            />
            <IoIosArrowDown size={14} />
          </div>

          <div className="flex flex-wrap items-center gap-6">
            <button className="border-[1px] flex items-center px-4 justify-between w-[195px] h-[56px] text-[17px] font-bold rounded-lg text-secondary-300 border-secondary-300 bg-white hover:text-white hover:bg-secondary-300">
              <span>{t("viewReport")}</span>
              <span>
                <MdOutlineRemoveRedEye className="text-lg" />
              </span>
            </button>
            <button className="border-[1px] flex items-center px-4 justify-between w-[155px] h-[56px] text-[17px] font-bold rounded-lg text-secondary-300 border-secondary-300 bg-white hover:text-white hover:bg-secondary-300">
              <span>{t("share")}</span>
              <span>
                <CiShare2 className="text-lg" />
              </span>
            </button>
            <button className="border-[1px] flex items-center px-4 justify-between w-[195px] h-[56px] text-sm font-bold rounded-lg text-white bg-secondary-300 ease-out duration-200 hover:translate-y-2 hover:bg-secondary-400">
              <span>{t("downloadReport")}</span>
              <span>
                <FiDownload className="text-lg" />
              </span>
            </button>
          </div>
        </div>

        {/* alert */}
        <Alert
          variant="light"
          color="rgba(0, 191, 185, 0.11)"
          icon={
            <CiCircleCheck className="text-[#00BFB9] text-[19px] mt-2 w-[30px] h-[30px]" />
          }
          className="text-[#00BFB9]"
        >
          <span className="text-[#00BFB9] text-[19px]">{t("reviewReady")}</span>
        </Alert>

        <div className="flex flex-wrap mt-5 mb-7 gap-9 md:flex-nowrap">
          {/* donut chart */}
          <div className="flex bg-white p-5 flex-grow md:w-1/2 justify-center flex-col items-center rounded-lg shadow-[0px_0px_10px_0px_#0000001A]">
            <h5 className="font-bold">{t("overallReadinessScore")}</h5>
            <span className="flex items-center justify-center w-full">
              <RingProgress
                size={180}
                thickness={20}
                roundCaps
                className="w-full aspect-square text-center flex justify-center items-center scale-x-[-1] rotate-90"
                sections={[{ value: 80, color: "#29919B" }]}
                rootColor="#D4E9EB"
                label={
                  <Text
                    c="black"
                    fw={700}
                    ta="center"
                    size="xl"
                    className="rotate-90 scale-x-[-1] text-5xl flex justify-center items-center"
                  >
                    4
                  </Text>
                }
              />
            </span>
          </div>

          {/* line chart */}
          <div className="bg-white flex-grow md:w-1/2 text-center p-5 rounded-lg shadow-[0px_0px_10px_0px_#0000001A]">
            <h5 className="mb-6 font-bold">{t("progressTracker")}</h5>
            <AreaChart
              h={150}
              data={data}
              dataKey="date"
              series={[{ name: "Progress", color: "#07838F" }]}
              curveType="natural"
              yAxisProps={{ domain: [0, 5] }}
            />
          </div>
        </div>
        <div className="flex items-center justify-center w-full mx-auto mt-auto img-wrapper max-w-7xl">
          <img src={reportImg} className="object-center w-full" alt="Report" />
        </div>
      </div>
    </MainLayout>
  );
}
