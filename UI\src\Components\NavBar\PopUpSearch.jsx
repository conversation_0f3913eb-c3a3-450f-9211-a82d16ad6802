import { SearchIconNavBar } from '@/assets/icons';

const PopUpSearch = () => {
  return (
    <form className="flex px-4 py-3 overflow-hidden max-w-md mx-auto font-[sans-serif] rounded-full border " onSubmit={(e) => e.preventDefault()}>
      <input required type="text" placeholder="Search " className="w-full outline-none text-black text-sm bg-[#F7F9FB]" />
      <button type="submit" className="p-0 m-0">
        <SearchIconNavBar />
      </button>
    </form>
  );
};

export default PopUpSearch;
