import ApiS1Config from "@/Api/apiS1Config";
import Loading from "@/Components/Loading";
import { useCsrdContext } from "@/Contexts/CsrdContext";
import useTableSearchingAndSorting from "@/hooks/useTableSearchingAndSorting";
import {
 Button,
 Checkbox,
 FileInput,
 MultiSelect,
 Select,
 Table,
 TagsInput,
 TextInput,
 // Pagination,
 Textarea,
 Tooltip,
} from "@mantine/core";
import { showNotification } from "@mantine/notifications";
import cx from "clsx";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { CiSearch } from "react-icons/ci";
import { FaArrowUp } from "react-icons/fa";
import { FaArrowDown } from "react-icons/fa6";
import { GoShare } from "react-icons/go";
import { IoIosCheckmarkCircleOutline } from "react-icons/io";
import { IoCloseCircleOutline, IoLink } from "react-icons/io5";
import { MdModeEdit, MdOutlineFileDownload } from "react-icons/md";
import { Link, useNavigate, useParams } from "react-router-dom";
import ISSBMultiselectFilter from "../../ISSB/Partials/ISSBMultiselectFilter";
import CSRDEnvironmentalTable from "./CSRDEnvironmentalTable";
import CSRDStakeholderTable from "./CSRDStakeholderTable";
import UpgradePopup from "./UpgradePopup";
import { useDisclosure } from "@mantine/hooks";
import axios from "axios";
import Cookies from "js-cookie";
import Owners from "@/Components/Owner&Taging/Owners";

const CsrdTable = () => {
 const navigate = useNavigate();
 
 const {
  selectedScope,
  setSelectedScope,
  CSRDScopesData,
  PriorityLevels,
  ReadinessLevel,
  postAnswers,
  prioritySelectColorMap,
  readinessColorMap,
  setSelectedCategory,
  postLoading,
  reportLoading,
  getReport,
 } = useCsrdContext();
 const {
  selection,
  sortedData,
  search,
  reverseSortDirection,
  edit,
  value,
  toggleRow,
  toggleAll,
  handleSearchChange,
  handleSort,
  setEdit,
  setValue,
  setReverseSortDirection,
 } = useTableSearchingAndSorting(selectedScope);
 if (selectedScope?.solved) {
  navigate(`/csrd-dashboard`);
 }
 const [assessmentData, setAssessmentData] = useState();
 const [opened, { open, close }] = useDisclosure(false);
 //dropdown states
 const [selectedReadinessLevel, setSelectedReadinessLevel] = useState({});
 const [selectedPriorityState, setSelectedPriorityState] = useState({});
 const { assessmentTitle, scopeTitle } = useParams();
 const [evidenceFile, setEvidenceFile] = useState({});
 const [evidences, setEvidence] = useState({});
 const [backEndEvidences, setBackEndEvidence] = useState({});
 const [uploadLoading, setUploadLoading] = useState({});
 const [evidenceUrls, setEvidenceUrls] = useState({});
 const [actionItems, setActionItems] = useState({});
 const [tags, setTags] = useState({});
 const [Owner, setOwner] = useState({});
 console.log("🚀 ~ CsrdTable ~ Owner:", Owner)
 const [DueDate, setDueDate] = useState({});
 const { t } = useTranslation();
 //console.log("daaa", selectedScope);

 const getTableData = () => {
  // find the specific selected scope and assessment from the data
  const scope = CSRDScopesData?.find((scope) => scope?.category === scopeTitle);
  // //console.log(scope);

  if (!scope) {
   // //console.log("scope not found");
   return [];
  }

  const assessment = scope.scope.find(
   (assessment) => assessment.title === assessmentTitle
  );

  if (!assessment) {
   //console.log("Assessment not found");
   return [];
  }

  setSelectedCategory(scope.category);
  setAssessmentData(assessment);
  setSelectedScope(assessment);
  // setSortedData(assessment.topics); // Initialize sortedData with the topics from the assessment
 };
 useEffect(() => {
  sortedData?.map((item) =>
   item?.questions?.map((question, idx) => {
    question?.evidence &&
     setBackEndEvidence((prev) => ({
      ...prev,
      [`${item.id}-${idx}`]: question?.evidence[0],
     }));
   })
  );
 }, [sortedData]);
 useEffect(() => {
  getTableData();
  setEdit(false);
 }, [CSRDScopesData]);
 const [assignees, setAssignees] = useState([]);
 const [assigneesArray, setAssigneesArray] = useState([]);
 const assigneesData = async () => {
   try {
     const assignees = await axios.get(
       "https://portal-auth-main.azurewebsites.net/get-all-company-users",
       {
         headers: {
           Authorization: `Bearer ${Cookies.get("level_user_token")}`,
         },
       }
     );
     setAssigneesArray(assignees.data)
     const uniqueNames = [...new Set(assignees.data.map((item) => item.user_name))];
     setAssignees(uniqueNames);
   } catch (error) {
     //console.log(error);
   }
 };

 useEffect(() => {
   assigneesData();
 }, [])
 

 const handleSelectReadinessChange = (id, value) => {
  setSelectedReadinessLevel((prev) => ({ ...prev, [id]: value }));
 };

 const handleSelectPriorityChange = (rowId, value) => {
  setSelectedPriorityState((prev) => ({ ...prev, [rowId]: value }));
 };

 const handleFileInputChange = (rowId, file, type) => {
  if (type === "delete") {
   // Remove the item from the state
   setEvidenceFile((prev) => {
    const updatedFiles = { ...prev };
    delete updatedFiles[rowId];
    return updatedFiles;
   });
   setEvidence((prev) => {
    const updatedFiles = { ...prev };
    delete updatedFiles[rowId];
    return updatedFiles;
   });
   setBackEndEvidence((prev) => {
    const updatedFiles = { ...prev };
    delete updatedFiles[rowId];
    return updatedFiles;
   });
  } else {
   // Update evidenceFile with the new file
   setEvidenceFile((prev) => ({ ...prev, [rowId]: file }));
  }
 };

 const handleURLInputChange = (rowId, file) => {
  // Update evidenceFile with the file
  setEvidenceUrls((prev) => ({ ...prev, [rowId]: file }));
 };

 const handleActionItemsInputChange = (rowId, value) => {
  setActionItems((prev) => ({ ...prev, [rowId]: value }));
 };

 const handleTagsChange = (rowId, tags) => {
  setTags((prev) => ({ ...prev, [rowId]: tags }));
 };

 const handleOwnerChange = (rowId, tags) => {
  setOwner((prev) => ({ ...prev, [rowId]: tags }));
 };

 const handleDueDateChange = (rowId, tags) => {
  setDueDate((prev) => ({ ...prev, [rowId]: tags }));
 };

 const updateEditState = () => {
  // Create an object where each rowId in selection is set to true
  const newEditState = selection.reduce((acc, rowId) => {
   acc[rowId] = true;
   return acc;
  }, {});

  // Update state
  setEdit((prevEditState) => ({
   ...prevEditState,
   ...newEditState,
  }));
 };
 
 const uploadEvidence = async (rowId) => {
  // let fileArray;
  setUploadLoading((prev) => ({ ...prev, [rowId]: true }));
  const formData = new FormData();

  if (evidenceFile[rowId]) {
   if (Array.isArray(evidenceFile[rowId])) {
    evidenceFile[rowId].forEach((file) =>
     formData.append("evidence_files", file)
    );
   } else {
    formData.append("evidence_files", evidenceFile[rowId]);
   }
  }

  if (evidenceFile[rowId]) {
   try {
    let { data: UploadedFile } = await ApiS1Config.post(
     "upload_evidence_files",
     formData,
     {
      headers: {
       "Content-Type": "multipart/form-data",
      },
     }
    );
    setUploadLoading((prev) => ({ ...prev, [rowId]: false }));

    //console.log(UploadedFile);
    showNotification({
     message: "File uploaded successfully",
     color: "green",
    });
    const fileArray = Object.entries(UploadedFile).map(([name, url]) => ({
     name,
     url,
    }));
    setEvidence((prev) => ({ ...prev, [rowId]: fileArray }));
   } catch (error) {
    setUploadLoading((prev) => ({ ...prev, [rowId]: false }));

    //console.log(error);
    showNotification({
     message: "File not uploaded",
     color: "red",
    });
   }
  }
 };
 const collectData = () => {
  const data = {
   name: selectedScope.name,
   solved: selectedScope.solved,
   title: selectedScope.title,
   topics: selectedScope.topics.map((item) => {
    return {
     id: item.id,
     name: item.name,
     questions: item.questions.map((question, idx) => {
      const readinessLevelEntry = Object.entries(ReadinessLevel).find(
       ([key, val]) => val === selectedReadinessLevel[`${item.id}-${idx}`]
      );

      const readinessLevel = readinessLevelEntry
       ? Number(readinessLevelEntry[0])
       : null;
      const selectedPriorityEntry = Object.entries(PriorityLevels).find(
       ([key, val]) => val === selectedPriorityState[`${item.id}-${idx}`]
      );

      const selectedPriority = selectedPriorityEntry
       ? Number(selectedPriorityEntry[0])
       : null;

      return {
       id: question.id,
       questionText: question.questionText,
       CSRDReference: question.CSRDReference,
       readinessLevel:
        readinessLevel === null ? question.readinessLevel : readinessLevel,
       evidence: [
        !evidences[`${item.id}-${idx}`]
         ? (question.evidence && question.evidence[0]) || null
         : evidences[`${item.id}-${idx}`],
        !evidenceUrls[`${item.id}-${idx}`]
         ? (question.evidence && question.evidence[1]) || null
         : evidenceUrls[`${item.id}-${idx}`],
       ],
       priority:
        selectedPriority === null ? question.priority : selectedPriority,
       actionItems: !actionItems[`${item.id}-${idx}`]
        ? question.actionItems
        : [actionItems[`${item.id}-${idx}`]],

       owner: !Owner[`${item.id}-${idx}`]
        ? question.owner
        : Owner[`${item.id}-${idx}`],
       date: !DueDate[`${item.id}-${idx}`]
        ? question.date
        : [DueDate[`${item.id}-${idx}`]],
       tags: [tags[`${item.id}-${idx}`]] || [],
      };
     }),
    };
   }),
   url: selectedScope.url,
  };
  // //console.log(data);
  postAnswers(data);
 };

 let DueDateError = false;
 let actionItemsError = false;
 const actionItemsKey = Object.keys(actionItems);
 const DueDateKey = Object.keys(DueDate);

 actionItemsKey.forEach((key) => {
  if (DueDateKey.includes(key)) {
   const length1 = actionItems[key]?.length;
   const length2 = DueDate[key]?.length;
   if (length1 === length2) {
    // //console.log(`Key ${key} has equal length in both objects: ${length1}`);
    DueDateError = false;
   } else {
    DueDateError = true;
   }
  } else {
   DueDateError = true;
  }
 });
 DueDateKey.forEach((key) => {
  if (actionItemsKey.includes(key)) {
   const length1 = actionItems[key]?.length;
   const length2 = DueDate[key]?.length;
   if (length2 === length1) {
    // //console.log(`Key ${key} has equal length in both objects: ${length1}`);
    actionItemsError = false;
    // //console.log(actionItemsError);
   } else {
    actionItemsError = true;
    // //console.log(actionItemsError);
   }
  } else {
   actionItemsError = true;
   // //console.log(actionItemsError);
  }
 });

 const rows = sortedData?.map((item) =>
  item.questions.map((question, idx) => {
   const selected = selection?.includes(item.id);
   const rowId = `${item.id}-${idx}`;
   const isFirstQuestion = idx === 0;
   const isDisabled = question.solved === true;
   const ownerStrings = question.owner?.map(o=> o?.user_name)

   const currentActionItem = actionItems[`${item.id}-${idx}`]?.length;
   const currentDueDate = DueDate[`${item.id}-${idx}`]?.length;

   return (
    <React.Fragment key={rowId}>
     <Table.Tr
      className={`${cx({
       ["bg-[#07838F1A]"]: selected,
      })}`}
     >
      {isFirstQuestion && (
       <Table.Td rowSpan={item.questions.length}>
        <Checkbox
         checked={selected}
         onChange={() => toggleRow(item.id)}
         color="#07838F"
        />
       </Table.Td>
      )}
      {isFirstQuestion && (
       <Table.Td rowSpan={item.questions.length}>
        <p className="text-center w-36">{item.name}</p>
       </Table.Td>
      )}
      {/* Topic question column */}
      <Table.Td>
       <p
        className={`w-80 text-center mx-auto ${
         !edit && isDisabled ? "cursor-not-allowed opacity-50" : ""
        }`}
       >
        {question.questionText}
       </p>
      </Table.Td>

      {/* Readiness Level column */}
      <Table.Td>
       <div className="w-52 mx-auto">
        <Select
         defaultSearchValue={ReadinessLevel[question.readinessLevel]}
         value={selectedReadinessLevel[rowId]}
         disabled={!edit && isDisabled ? isDisabled : ""}
         onChange={(value) => {
          handleSelectReadinessChange(rowId, value);
         }}
         classNames={{
          root: "w-fit",
          input: "text-center",
         }}
         radius="xl"
         size="xs"
         className="w-full"
         withCheckIcon={false}
         allowDeselect={false}
         rightSectionWidth="0"
         placeholder="Select your level"
         data={Object.keys(readinessColorMap)}
         styles={(theme) => {
          const readinessLevelValue =
           selectedReadinessLevel[rowId] ||
           ReadinessLevel[question.readinessLevel];

          const readinessStyles = readinessColorMap[readinessLevelValue] || {
           bg: theme.colors.gray[0],
           text: "black",
           border: theme.colors.gray[3],
          };

          return {
           input: {
            backgroundColor: readinessStyles.bg,
            color: readinessStyles.text,
            border: `1px solid ${readinessStyles.border}`,
            padding: "16px 12px",
            borderRadius: "15px",
            fontSize: "14px",
            fontWeight: "500",
            boxShadow: `0 2px 4px rgba(0, 0, 0, 0.1)`,
           },
          };
         }}
        />
       </div>
      </Table.Td>

      {/* Evidence/Notes column */}
      <Table.Td
       className={`w-1/5 text-center ${
        !value.includes("Evidence") ? "hidden" : ""
       }  ${!edit && isDisabled ? "cursor-not-allowed opacity-50" : ""}`}
      >
       <div
        className={`w-56 text-center mx-auto ${
         !edit && isDisabled ? "cursor-not-allowed opacity-50" : ""
        }`}
       >
        {!backEndEvidences[rowId] &&
        !evidenceFile[rowId] &&
        !evidenceFile[rowId]?.length ? (
         <FileInput
          classNames={{ root: "w-fit", input: "px-12" }}
          leftSection={<MdOutlineFileDownload className="w-5 h-5" />}
          variant="unstyled"
          placeholder="Upload evidence"
          className="w-full bg-[#F2F2F2] mb-3"
          radius="md"
          leftSectionPointerEvents="none"
          disabled={!edit && isDisabled ? isDisabled : ""}
          onChange={(file) => handleFileInputChange(rowId, file)}
          multiple
         />
        ) : (
         <div className="flex items-center justify-around mb-1">
          <div className="text-start w-10/12 ">
           {(backEndEvidences &&
            backEndEvidences[rowId]?.map((item, indx) => (
             <Button
              key={indx}
              variant="subtle"
              href={item?.url}
              target="_blank"
              component="a"
              className="w-full py-0"
             >
              <p key={indx} className="truncate py-0">
               {item.name}
              </p>
             </Button>
            ))) ||
            evidenceFile[rowId]?.map((item, indx) => (
             <p key={indx} className="truncate ">
              {item.name}
             </p>
            ))}
          </div>
          <div className="flex">
           {uploadLoading[rowId] ? (
            <Loading />
           ) : (
            <>
             <IoCloseCircleOutline
              className={` text-red-600 ${
               !edit && isDisabled ? " cursor-not-allowed" : "cursor-pointer"
              }`}
              onClick={() => {
               if (!edit && isDisabled) return;
               if (evidenceFile[rowId] || backEndEvidences[rowId])
                handleFileInputChange(rowId, [], "delete");
               // if (evidence) question.evidence[0] = null;
              }}
             />
             {evidenceFile[rowId] && !evidences[rowId] && (
              <IoIosCheckmarkCircleOutline
               className={` text-green-600 ${
                !edit && isDisabled ? " cursor-not-allowed" : "cursor-pointer"
               }`}
               onClick={() => {
                // if (!edit && isDisabled) return;
                // if (evidenceFile[rowId])
                //   handleFileInputChange(rowId, [], "delete");
                // if (evidence) question.evidence[0] = null;
                uploadEvidence(rowId);
               }}
              />
             )}
            </>
           )}
          </div>
         </div>
        )}
        <TextInput
         classNames={{ root: "w-fit", input: "ps-8 pe-2" }}
         leftSection={<IoLink className="w-5 h-5" />}
         variant="unstyled"
         placeholder="enter an URL"
         className="w-full bg-[#e3f0fd]"
         radius="md"
         leftSectionPointerEvents="none"
         disabled={!edit && isDisabled ? isDisabled : ""}
         onChange={(e) => handleURLInputChange(rowId, e.target.value)}
         defaultValue={question?.evidence && question?.evidence[1]}
        />
       </div>
      </Table.Td>

      {/* Priority column */}
      <Table.Td>
       <div className="w-38">
        <Select
         disabled={!edit && isDisabled ? isDisabled : ""}
         defaultSearchValue={PriorityLevels[question.priority]}
         value={selectedPriorityState[rowId]}
         onChange={(value) => handleSelectPriorityChange(rowId, value)}
         classNames={{
          root: "w-fit",
          input: "text-center",
         }}
         radius="xl"
         size="xs"
         className="w-full"
         withCheckIcon={false}
         allowDeselect={false}
         rightSectionWidth="0"
         placeholder="Priority"
         data={Object.keys(prioritySelectColorMap)}
         styles={(theme) => {
          const priorityLevelValue =
           selectedPriorityState[rowId] || PriorityLevels[question.priority];

          const priorityStyles = prioritySelectColorMap[priorityLevelValue] || {
           bg: "rgba(0, 0, 0, 0.1)",
           text: "black",
          };

          return {
           input: {
            backgroundColor: priorityStyles.bg,
            color: priorityStyles.text,
            fontWeight: "500",
            border: `none`,
            padding: "16px 12px",
            borderRadius: "15px",
            fontSize: "14px",
            boxShadow: `0 2px 4px rgba(0, 0, 0, 0.1)`,
           },
          };
         }}
        />
       </div>
      </Table.Td>
      {/* Action Items columns */}
      <Table.Td
       className={`w-1/5 text-center mx-auto ${
        !value.includes("Action Items") ? "hidden" : ""
       }`}
      >
      <div className="mx-auto flex justify-center items-center">

       <TagsInput
        placeholder="Press Enter to add item"
        disabled={!edit && isDisabled ? isDisabled : ""}
        classNames={{ input: "w-80" }}
        clearable
        defaultValue={
         Array.isArray(question?.actionItems)
          ? question.actionItems.filter(
             (item) => item && typeof item === "string"
            )
          : []
        }
        onChange={(tags) => handleActionItemsInputChange(rowId, tags)}
        className={` ${
         currentActionItem !== currentDueDate
          ? "border-2 border-red-700 rounded-md"
          : ""
        }`}
       />
       {currentDueDate !== currentActionItem ? (
        <p className="text-xs text-center text-red-500">
         action items must equal the number of Due Dates.
        </p>
       ) : (
        ""
       )}
      </div>
      </Table.Td>
      <Table.Td>
       <div className="w-52 mx-auto">
        <p
         className={`text-center ${
          !edit && isDisabled ? "cursor-not-allowed opacity-50" : ""
         }`}
        >
         {question.CSRDReference}
        </p>
       </div>
      </Table.Td>
      {/* Tags input */}
      <Table.Td
       className={`bg-gray-300 cursor-not-allowed text-center  ${
        !value.includes("Tags") ? "hidden" : ""
       }`}
       onClick={open}
       //  aria-disabled="true"
      >
       <div className="bg-gray-300 cursor-not-allowed relative">
        {/* الغطاء فوق الـ input لمنع التفاعل */}
        <div className="absolute top-0 left-0 w-full h-full z-10"></div>

        <TagsInput
         placeholder="Press Enter to add a tag"
         maxtags={4}
         disabled
         classNames={{ input: "cursor-not-allowed" }}
         onChange={(tags) => handleTagsChange(rowId, tags)}
        />
       </div>
      </Table.Td>

      {/* Owner input */}
      <Table.Td
       className={`w-1/5 text-center ${
        !value.includes("Owner") ? "hidden" : ""
       }`}
      >

        <Owners currentOwners={ownerStrings} handleOwners={(ownersList)=> handleOwnerChange(rowId, ownersList)} />

      </Table.Td>
      {/* DueDate input */}
      <Table.Td
       className={`w-1/5 text-center ${
        !value.includes("DueDate") ? "hidden" : ""
       }`}
      >
      <div className="mx-auto flex justify-center items-center">

       <TagsInput
        placeholder="Press Enter to add a item"
        disabled={!edit && isDisabled ? isDisabled : ""}
        classNames={{ input: "w-80" }}
        className={`${
         currentActionItem !== currentDueDate
          ? "border-2 border-red-700 rounded-md"
          : ""
        }`}
        clearable
        defaultValue={
         Array.isArray(question?.date)
          ? question.date.filter((item) => item && typeof item === "string")
          : []
        }
        onChange={(tags) => handleDueDateChange(rowId, tags)}
       />
       {currentActionItem !== currentDueDate ? (
        <p className="text-xs text-center text-red-500">
         Due Date must equal the length of action items.
        </p>
       ) : (
        ""
       )}
      </div>
      </Table.Td>
     </Table.Tr>
    </React.Fragment>
   );
  })
 );

 return (
  <>
   {!assessmentData ? (
    <Loading />
   ) : (
    <>
     <h1 className="capitalize text-center hidden md:block">
      {t("pleaseScrollHint")}
     </h1>

     <div className="p-2 my-1 shadow-lg grid items-center  xl:justify-between px-4 bg-white xl:grid-cols-3 rounded-lg w-full">
      <div className="xl:col-span-1 w-full flex justify-center xl:justify-start">
       <Button
        className="text-black bg-transparent hover:bg-transparent hover:text-black border border-gray-600 w-full xl:w-auto"
        onClick={updateEditState}
       >
        <MdModeEdit className="me-1" />
        Edit
       </Button>
      </div>
      <div className="xl:col-span-2 grid items-start  justify-center grid-cols-1 lg:grid-cols-3 xl:grid-cols-4 sm:items-center w-full lg:gap-5">
       <div className="md:col-span-1 hidden xl:flex justify-center items-center m-3  p-2 rounded-lg shadow-sm ms-auto "></div>

       <div className=" md:col-span-1 m-3  bg-[#EBEBEB] rounded-lg shadow-sm md:ms-auto mx-auto w-full">
        <ISSBMultiselectFilter setValue={setValue} value={value} />
       </div>

       <TextInput
        className="w-full col-span-2"
        placeholder="Search by Topic Area or Assessment Question"
        rightSection={<CiSearch className="w-5 h-5" />}
        value={search}
        onChange={handleSearchChange}
        // disabled
       />
      </div>
     </div>

     {search && sortedData?.length === 0 ? (
      <h1 className="capitalize text-center mt-5">Your Search is not Found</h1>
     ) : (
      <div className="p-2 my-1 bg-white shadow-lg rounded-xl">
       <Table.ScrollContainer className="scrollable-container" maw={"99%"}>
        <Table
         verticalSpacing="sm"
         horizontalSpacing={""}
         className="scrollable-container"
        >
         <Table.Thead className="pb-6 text-base font-thin">
          <Table.Tr className="text-secondary-500">
           <Table.Th className="">
            <Checkbox
             onChange={toggleAll}
             checked={selection.length === selectedScope?.topics.length}
             indeterminate={
              selection.length > 0 &&
              selection.length !== selectedScope?.topics.length
             }
             color="#07838F"
            />
           </Table.Th>
           <Table.Th
            className="text-center cursor-pointer"
            onClick={() => {
             handleSort("name");
             setReverseSortDirection((prev) => !prev);
            }}
           >
            <h1 className="flex items-center justify-center gap-3 ms-4">
             Topic Area
             {reverseSortDirection === true ? (
              <FaArrowUp className="ms-1 text-[#00C0A9]" />
             ) : (
              <FaArrowDown className="ms-1 text-[#00C0A9]" />
             )}
            </h1>
           </Table.Th>
           <Table.Th
            className="w-1/4 text-center cursor-pointer"
            onClick={() => {
             handleSort("questionText");
             setReverseSortDirection((prev) => !prev);
            }}
           >
            <h1 className="flex items-center justify-center gap-3 ms-4">
             Assessment Question
             {reverseSortDirection === true ? (
              <FaArrowUp className="ms-1 text-[#00C0A9]" />
             ) : (
              <FaArrowDown className="ms-1 text-[#00C0A9]" />
             )}
            </h1>
           </Table.Th>
           <Table.Th className="w-1/5 text-center">
            <h1 className="flex items-center justify-center gap-3 ms-4">
             Assessment Level
             <FaArrowDown className="ms-1 text-[#00C0A9]" />
            </h1>
           </Table.Th>
           <Table.Th
            className={`w-1/5 text-center ${
             !value.includes("Evidence") ? "hidden" : ""
            }`}
           >
            <h1 className="flex items-center justify-center gap-3 ms-4">
             Evidence/Notes
             <FaArrowDown className="ms-1 text-[#00C0A9]" />
            </h1>
           </Table.Th>
           <Table.Th className="w-1/5 text-center">
            <h1 className="flex items-center justify-center gap-3 ms-4">
             Priority
             <FaArrowDown className="ms-1 text-[#00C0A9]" />
            </h1>
           </Table.Th>
           <Table.Th
            className={`w-1/5 text-center ${
             !value.includes("Action Items") ? "hidden" : ""
            }`}
           >
            <h1 className="flex items-center justify-center gap-3 ms-4">
             Action Items
             <FaArrowDown className="ms-1 text-[#00C0A9]" />
            </h1>
           </Table.Th>
           <Table.Th className="w-1/5 text-center">
            <h1 className="flex items-center justify-center gap-3 ms-4">
             CSRD Reference
             <FaArrowDown className="ms-1 text-[#00C0A9]" />
            </h1>
           </Table.Th>
           <Table.Th
            className={`w-1/5 text-center ${
             !value.includes("Tags") ? "hidden" : ""
            }`}
           >
            <h1 className="flex items-center justify-center gap-3 ms-4">
             Tag Colleagues
             <FaArrowDown className="ms-1 text-[#00C0A9]" />
            </h1>
           </Table.Th>
           <Table.Th
            className={`w-1/5 text-center ${
             !value.includes("Owner") ? "hidden" : ""
            }`}
           >
            <h1 className="flex items-center justify-center gap-3 ms-4">
             Owner
             <FaArrowDown className="ms-1 text-[#00C0A9]" />
            </h1>
           </Table.Th>
           <Table.Th
            className={`w-1/5 text-center ${
             !value.includes("DueDate") ? "hidden" : ""
            }`}
           >
            <h1 className="flex items-center justify-center gap-3 ms-4">
             Due Date
             <FaArrowDown className="ms-1 text-[#00C0A9]" />
            </h1>
           </Table.Th>
          </Table.Tr>
         </Table.Thead>
         <Table.Tbody className="text-base font-semibold text-gray-600">
          {rows}
         </Table.Tbody>
        </Table>
       </Table.ScrollContainer>
       <div className="flex items-center justify-end gap-5">
        {/* <Pagination
                page={currentPage}
                onChange={(e) => {
                  setCurrentPage(e);
                  setEdit(false);
                }}
                total={totalPages}
                className={`mt-5 ${
                  ""
                  // DueDateError || actionItemsError ? "cursor-not-allowed" : ""
                }`}
                disabled={DueDateError || actionItemsError}
              /> */}

        <Tooltip
         multiline
         w={220}
         radius={"md"}
         withArrow
         transitionProps={{ duration: 200 }}
         label={
          DueDateError || actionItemsError ? (
           <span className="capitalize flex justify-center">
            the count of action items must equal Due Date items.
           </span>
          ) : null
         }
         className={DueDateError || actionItemsError ? "" : "hidden"}
        >
         <Button
          className={`text-white bg-primary hover:bg-primary ${
           DueDateError || actionItemsError || selectedScope?.solved
            ? "cursor-not-allowed opacity-50"
            : ""
          }`}
          disabled={DueDateError || actionItemsError || selectedScope?.solved}
          onClick={
           postLoading
            ? ""
            : () => {
               collectData();
              }
          }
         >
          {postLoading ? <Loading /> : "Save"}
         </Button>
        </Tooltip>
       </div>
      </div>
     )}

     {assessmentData?.title === "ESRS 2" ? (
      <div>
       <div className="flex justify-end mb-2 mt-14">
        <button className="text-sm underline text-sky-600">
         {t("howIsRiskCalculated")}
        </button>
       </div>

       <div className="items-center justify-between w-full px-4 py-6 bg-white rounded-lg shadow-md sm:flex ">
        <div>
         <h1 className="text-lg font-bold text-black">
          {t("doubleMaterialityReadiness")}
         </h1>
         <p className="font-normal text-sm text-[#667085]">
          {t("descriptiveBodyText")}
         </p>
        </div>

        <div className="mt-5 sm:mt-0">
         <Button
          className="bg-transparent me-3 text-primary border-2 border-[#00C0A9] rounded-lg hover:bg-primary hover:text-white"
          size="md"
         >
          <GoShare className="me-2" />
          {t("export")}
         </Button>
         <Button
          className="text-white border-2 border-[#00C0A9] rounded-lg bg-primary hover:opacity-90 hover:bg-primary"
          size="md"
         >
          {t("assess")}
         </Button>
        </div>
       </div>

       <div className="mt-5">
        <h1 className="font-medium text-lg text-[#00C0A9] text-start">
         {t("stakeholderImpactAssessment")}
        </h1>
        <div className="items-center justify-between w-full mt-5 bg-white rounded-lg shadow-md sm:flex">
         <CSRDStakeholderTable />
        </div>
        <Link className="font-medium text-xs text-[#9C9C9C] flex justify-end mt-2">
         {t("wantToAddSomething")}
        </Link>
       </div>

       <div className="mt-5">
        <h1 className="font-medium text-lg text-[#00C0A9] text-start">
         {t("environmentalFootprint")}
        </h1>
        <div className="items-center justify-between w-full mt-5 bg-white rounded-lg shadow-md sm:flex">
         <CSRDEnvironmentalTable />
        </div>
        <Link className="font-medium text-xs text-[#9C9C9C] flex justify-end mt-2">
         {t("wantToAddSomething")}
        </Link>
       </div>
      </div>
     ) : null}
    </>
   )}
   <UpgradePopup close={close} opened={opened} />
  </>
 );
};

export default CsrdTable;
