import { useEffect, useState } from "react";
import ApiProfile from "@/Api/apiProfileConfig";
import Loading from "./../../../../Components/Loading";
import { BiBuildings, BiSolidUserCircle } from "react-icons/bi";
import MainSection from "./Partials/MainSection";
import CompanyProfile from "./Partials/CompanyProfile/CompanyProfile";
import UserProfile from "./Partials/UserProfile/UserProfile";
import Cookies from "js-cookie";
import { useUserStore } from "@/Store/useUser";

const GetAvatar = async (user,updateAvatar) => {
  try {
    if(user.avatar){
      let localavatar = localStorage.getItem("avatar");
      updateAvatar(localavatar);
      console.log(localavatar);
      if(!localavatar){
        const {data:{avatar_url}} = await ApiProfile.get("/avatar", {headers:{Authorization: `Bearer ${Cookies.get("level_user_token")}`}});
        const response = await fetch(avatar_url);
        const blob = await response.blob();
        
        const reader = new FileReader();
        reader.onloadend = () => {
          localStorage.setItem("avatar", reader.result);
          updateAvatar(reader.result);
        };
        reader.readAsDataURL(blob);
      }
    }
  } catch (error) {
    console.log(error);
  }
}

export default function Profile() {
  // const navigate = useNavigate()
  const [profileData, setProfileData] = useState(null);
  const [showUserProfile, setShowUSerProfile] = useState(true);
  const [disabled, setDisabled] = useState(true);
  // const [loading, setLoading] = useState(false);
  const { updateAvatar,clearTempAvatar } = useUserStore((state) => state);
  // Get Data From Api
  const getUserData = async () => {
    try {
      const { data } = await ApiProfile.get("/account_information");
      
      if(data.avatar){
        GetAvatar(data,updateAvatar);
      }
      setProfileData(data);
    } catch (error) {
      clearTempAvatar();
      console.log(error);
    }
  };

  useEffect(() => {
    getUserData();
  }, []);
  // End Get Data From Api

  return (
    <>
      {!profileData ? (
        <Loading />
      ) : (
        <div className="Profile w-full rounded-lg overflow-hidden bg-white grid lg:grid-cols-2 ">
          {!showUserProfile ? (
            <MainSection
              icon={<BiSolidUserCircle className="text-8xl text-white" />}
              ButtonName={"Manage User"}
              setShowUSerProfile={() => setShowUSerProfile(true)}
            />
          ) : (
            <div data-aos="fade-left">
              <UserProfile profileData={profileData}  getUserData={getUserData}/>
            </div>
          )}
          {showUserProfile ? (
            <MainSection
              icon={<BiBuildings className="text-8xl text-white" />}
              ButtonName={"Manage Company"}
              setShowUSerProfile={() => setShowUSerProfile(false)}
            />
          ) : (
            <div data-aos="fade-right">
              <CompanyProfile />
            </div>
          )}
        </div>
      )}
    </>
  );
}
