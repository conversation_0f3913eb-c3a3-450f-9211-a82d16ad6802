import { Textarea } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useState } from "react";
import ApiS3 from "@/Api/apiS3";

const SupportRequest = () => {
  const { t } = useTranslation();
  const [description, setDescription] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [loading, setLoading] = useState(false); // added loading state

  const handleSubmit = async () => {
    setLoading(true);
    try {
      const response = await ApiS3.post("support/request-support", {
        description: description,
      });
      if (response.success) {
        setIsSubmitted(true);
      }
    } catch (err) {
      console.error("Support request failed:", err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <section className="p-6 support-request bg-white rounded-2xl min-h-[376px] flex flex-col items-center justify-center">
      {isSubmitted ? (
        <div className="flex flex-col items-center">
          {/* Animated Checkmark */}
          <div className="relative w-16 h-16">
            <svg
              className="w-full h-full text-green-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                className="animate-draw-check"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <p className="mt-4 text-sm text-gray-600 text-center">
            {t("SupportRequestConfirmation") ||
              "Usually, a response is received within one to two business days."}
          </p>
        </div>
      ) : (
        <div className="flex flex-col">
          <div className="flex flex-wrap items-center justify-between font-semibold text-primary w-full">
            <h1 className="text-[1.4rem]">{t("SupportRequestTitle")}</h1>
          </div>
          <h3 className="mt-5 mb-3 text-sm font-medium">
            {t("SupportRequestSubtitle")}
          </h3>

          <Textarea
            className="text-sm w-full"
            size="md"
            label={t("DescriptionLabel")}
            value={description}
            onChange={(event) => setDescription(event.currentTarget.value)}
          />

          <p className="text-[0.6rem] mt-2 text-secondary-lite-100">
            {t("SupportRequestNote")}
          </p>

          <button
            onClick={handleSubmit}
            className="bg-secondary mx-auto w-[89px] h-[27px] rounded-lg block mt-4 text-center text-white hover:bg-secondary-dark transition-colors"
          >
            {t("SubmitButton")}
          </button>
        </div>
      )}
    </section>
  );
};

export default SupportRequest;
