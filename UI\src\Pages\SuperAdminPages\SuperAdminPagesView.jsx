import MainLayout from '@/Layout/MainLayout'
import { useState } from 'react'
import ManageAdminCompanies from './ManageAdminCompanies/ManageAdminCompanies'
import { useTranslation } from 'react-i18next';
import { Tabs } from '@mantine/core';
import ManageCompanyLogs from './ManageCompanyLogs/ManageCompanyLogs';
import WebsitePhotos from './WebsitePhotos/WebsitePhotos';
import { IoMdHome } from 'react-icons/io';
import SystemTools from './SystemTools/SystemTools';

export default function SuperAdminPagesView() {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState("Admin Companies Management");
  return (
    <MainLayout
      breadcrumbItems={[
        { title: <IoMdHome size={20}/>, href: "/get-started" },
        { title: `Admin Pages`, href: "#" },
      ]}
      navbarTitle={t("Admin Pages")}
    >
      <Tabs className="" defaultValue={activeTab} onChange={setActiveTab}>
        <Tabs.List
          justify="center"
          className="mb-6 block  md:flex md:justify-around text-gray-500 py-3 rounded-md overflow-hidden bg-white before:border-none"
        >
          <Tabs.Tab
            value="Website Photos"
            className={`text-lg  mx-auto
            ${activeTab == "Website Photos"
                ? "text-primary border-b-2 border-primary"
                : " border-none"
              } 
           py-3 font-semibold hover:bg-transparent hover:opacity-100`}
          >
            {t("Website Photos")}
          </Tabs.Tab>
          <Tabs.Tab
            value="Admin Companies Management"
            className={`text-lg  mx-auto
            ${activeTab == "Admin Companies Management"
                ? "text-primary border-b-2 border-primary"
                : " border-none"
              } 
           py-3 font-semibold hover:bg-transparent hover:opacity-100`}
          >
            {t("Admin Companies Management")}
          </Tabs.Tab>

          <Tabs.Tab
            value="Manage Companies Logs"
            className={`text-lg mx-auto ${activeTab == "Manage Companies Logs"
                ? "text-primary border-b-2 border-primary"
                : " border-none"
              } 
       py-3 font-semibold hover:bg-transparent hover:opacity-100`}
          >
            {t("Manage Companies Logs")}
          </Tabs.Tab>

          <Tabs.Tab
            value="System Tools"
            className={`text-lg mx-auto ${activeTab == "System Tools"
                ? "text-primary border-b-2 border-primary"
                : " border-none"
              } 
       py-3 font-semibold hover:bg-transparent hover:opacity-100`}
          >
            {t("System Tools")}
          </Tabs.Tab>

        </Tabs.List>

        <Tabs.Panel value="Admin Companies Management">
          <ManageAdminCompanies />
        </Tabs.Panel>

        <Tabs.Panel value="Manage Companies Logs">
          <ManageCompanyLogs />
        </Tabs.Panel>

        <Tabs.Panel value="Website Photos">
          <WebsitePhotos />
        </Tabs.Panel>

        <Tabs.Panel value="System Tools">
          <SystemTools />
        </Tabs.Panel>

      </Tabs>
    </MainLayout>
  )
}
