export const IssbData = [
    {
      id: 1,
      topicTitle: "Governance",
      owner: "<PERSON>, Finance Department",
      questions: [
        "Has the board established clear oversight responsibilities for all material sustainability-related risks and opportunities?",
        "Are sustainability considerations integrated into board committees' terms of reference and mandates?",
        "Does management have clearly defined roles and responsibilities for managing sustainability-related issues?",
      ],
      due_date: ['a) 2023-12-31','b) 2024-03-31']
    },
    {
      id: 2,
      owner: "xxxxx",
      topicTitle: "Strategy",
      questions: [
        "Has the organization identified its key sustainability-related risks and opportunities over short, medium and long-term time horizons?",
        "Has the organization assessed the impacts of sustainability-related risks and opportunities on its business model and value chain?",
        "Has the organization analyzed the resilience of its strategy to sustainability-related changes and uncertainties?",
        "Has the organization quantified the current and anticipated financial impacts of material sustainability-related risks and opportunities?",
      ],
      due_date: ['a) 2023-12-31','b) 2024-03-31']
    },
    {
      id: 3,
      owner: "xxxxx",
      topicTitle: "Risk Management",
      questions: [
        "Are processes in place to identify, assess and prioritize sustainability-related risks?",
        "Are sustainability-related risks integrated into the organization's overall risk management processes?",
      ],
      due_date: ['a) 2023-12-31','b) 2024-03-31']
    },
    {
      id: 4,
      owner: "xxxxx",
      topicTitle: "Metrics & Targets",
      questions: [
        "Has the organization identified appropriate cross-industry and industry-specific sustainability-related metrics?",
        "Has the organization set targets for material sustainability-related risks and opportunities?",
        "Does the organization have systems in place to collect reliable data for sustainability-related metrics and targets?",
      ],
      due_date: ['a) 2023-12-31','b) 2024-03-31']
    },
    {
      id: 5,
      owner: "xxxxx",
      topicTitle: "Climate-specific",
      questions: [
        "Has the organization calculated its Scope 1, 2 and 3 greenhouse gas emissions in accordance with the GHG Protocol?",
        "Has the organization conducted climate scenario analysis, including at least one scenario aligned with the latest international climate agreement?",
        "Has the organization developed a climate transition plan aligned with its climate-related targets?",
        "Has the organization implemented internal carbon pricing in its decision-making and/or scenario analysis?",
      ],
      due_date: ['a) 2023-12-31','b) 2024-03-31']
    },
    {
      id: 6,
      owner: "xxxxx",
      topicTitle: "Reporting & Disclosure",
      questions: [
        "Can the organization provide sustainability-related disclosures at the same time as its financial reporting?",
        "Has the organization identified all applicable industry-based disclosure topics and metrics relevant to its sector?",
        "Does the organization have processes to ensure connectivity between sustainability-related and financial disclosures?",
        "Has the organization established processes to avoid unnecessary duplication in sustainability-related disclosures?",
      ],
      due_date: ['a) 2023-12-31','b) 2024-03-31']
    },
    {
      id: 7,
      owner: "xxxxx",
      topicTitle: "Financial Impact Assessment",
      questions: [
        "Has the organization assessed sustainability-related risks for which there is a significant risk of material financial statement impacts in the next year?",
        "Has the organization quantified how it expects sustainability-related issues to impact its financial position and performance over time?",
      ],
      due_date: ['a) 2023-12-31','b) 2024-03-31']
    },
    {
      id: 8,
      owner: "xxxxx",
      topicTitle: "Materiality",
      questions: [
        "Has the organization established processes to identify material sustainability-related information?",
        "Does the organization regularly reassess its materiality judgements?",
      ],
      due_date: ['a) 2023-12-31','b) 2024-03-31']
    },
    {
      id: 9,
      owner: "xxxxx",
      topicTitle: "Data & Assumptions",
      questions: [
        "Are the data and assumptions used in sustainability-related disclosures consistent with those used in financial statements?",
      ],
      due_date: ['a) 2023-12-31','b) 2024-03-31']
    },
    {
      id: 10,
      owner: "xxxxx",
      topicTitle: "Comparative Information",
      questions: [
        "Is the organization prepared to provide comparative information for sustainability-related disclosures?",
      ],
      due_date: ['a) 2023-12-31','b) 2024-03-31']
    },
    {
      id: 11,
      owner: "xxxxx",
      topicTitle: "Location of Disclosures",
      questions: [
        "Has the organization determined where to disclose sustainability-related information within its general purpose financial reporting?",
      ],
      due_date: ['a) 2023-12-31','b) 2024-03-31']
    },
    {
      id: 12,
      owner: "xxxxx",
      topicTitle: "Assurance",
      questions: [
        "Has the organization considered obtaining external assurance over its sustainability-related disclosures?",
      ],
      due_date: ['a) 2023-12-31','b) 2024-03-31']
    },
  ];


export const chartData = [
    {
      date: "Jan",
      Progress: 2.5,
    },
    {
      date: "Feb",
      Progress: 4,
    },
    {
      date: "Mar",
      Progress: 3.8,
    },
    {
      date: "Apr",
      Progress: 3,
    },
    {
      date: "May",
      Progress: 2.5,
    },
    {
      date: "Jun",
      Progress: 3.3,
    },
    {
      date: "Jul",
      Progress: 4.5,
    },
    {
      date: "Aug",
      Progress: 4,
    },
    {
      date: "Sep",
      Progress: 2.2,
    },
    {
      date: "Oct",
      Progress: 2.3,
    },
    {
      date: "Nov",
      Progress: 2.2,
    },
    {
      date: "Dec",
      Progress: 1.8,
    },
  ];


export const assessData = ['#9BFFF3','#01F9DB','#00E3C8','#00D4BB','#00C0A9']


export const ReadinessLevel =  [
  {id:"1",level: "Not Started"},
  {id:"2",level: "Initial Planning"},
  {id:"3",level: "In Development"},
  {id:"4",level: "Partially Implemented"},
  {id:"5",level: "Fully Implemented"},
  {id:"6",level: "Not Applicable"},
]


export const readinessColorMap = {
  "Not Started": {
    bg: "#AB020233",
    border: "#AB0202",
    text: "#AB0202",
  },
  "Initial Planning": {
    bg: "#e9dff3",
    border: "#9160C1",
    text: "#9160C1",
  },
  "In Development": {
    bg: "#ffeecd",
    border: "#FFAB07",
    text: "#FFAB07",
  },
  "Partially Implemented": {
    bg: "#d4e8fb",
    border: "#298BED",
    text: "#298BED",
  },
  "Fully Implemented": {
    bg: "#e2f6e7",
    border: "#01BD36",
    text: "#01BD36",
  },
  "Not Applicable": {
    bg: "#e0e2e7",
    border: "#667085",
    text: "#667085",
  },
};

export const prioritySelectColorMap = {
  Top: {
    bg: "rgba(171, 2, 2, 0.2)",
    border: "rgba(171, 2, 2, 1)",
    text: "rgba(171, 2, 2, 1)",
  },
  High: {
    bg: "rgba(255, 171, 7, 0.2)",
    border: "rgba(255, 171, 7, 1)",
    text: "rgba(255, 171, 7, 1)",
  },
  Medium: {
    bg: "rgba(1, 189, 54, 0.2)",
    border: "rgba(0, 192, 169, 1)",
    text: "rgba(0, 192, 169, 1)",
  },
  low: {
    bg: "#01BD3638",
    border: "#01BD36",
    text: "#01BD36",
  },
};
export const priorityBadgeColorMap = {
  Extreme: "rgba(171, 2, 2)",
  High: "rgba(255, 171, 7,1)",
  Medium: "rgba(0, 192, 169, 1)",
};

export const scopes = [
  {id:1, name: 'Environment'},
  {id:2, name: "Social"},
  {id:3, name: "Business_Integrity_and_Corporate_Governance"},
]



export const riskCategory = [
  {id:1,name: 'LOW (A)',color: '#00C0A9',bg: '#00C0A936'},
  {id:2,name: 'Medium (B)',color: '#FFAB07',bg: '#FFAB0736'},
  {id:3,name: 'High (C)',color: '#FF6007',bg: '#FF600736'},
  {id:4,name: 'Critical (D)',color: '#AB0202',bg: '#AB020236'},
]


export const assessmentLevel = [
  {id:1,name: 'Excellent',color: '#01BD36',bg: '#01BD3636'},
  {id:2,name: 'Good',color: '#9160C1',bg: '#9160C136'},
  {id:3,name: 'Average',color: '#298BED',bg: '#298BED21'},
  {id:4,name: 'Below Average',color: '#FFAB07',bg: '#FFAB0736'},
  {id:5,name: 'Poor',color: '#AB0202',bg: '#AB020236'},
]


export const confidenceLevel = [
  {id:1,name: 'Very High', color: '#01BD36',bg: '#9BFFF3'},
  {id:2,name: 'High', color: '#9160C1',bg: '#9160C136'},
  {id:3,name: 'Moderate', color: '#298BED',bg: '#298BED21'},
  {id:4,name: 'Low', color: '#FFAB07',bg: '#FFAB0736'},
  {id:5,name: 'Very Low', color: '#AB0202',bg: '#AB020236'},
]

