import { useTranslation } from "react-i18next";
import AnalyticsSortSection from "./Partials/AnalyticsSortSection";

const AnalyticsView = () => {
  const { t } = useTranslation();
  return (
    <>
      <div className="">
        {/* <div className="absolute inset-0 bg-[#f7f4f4f7] bg-opacity-100 flex items-start pt-20 justify-center z-10 rounded-lg">
          <h2 className="flex items-center justify-center gap-3 py-12 text-2xl text-center animate-pulse">
            <CgSandClock /> {t("Coming Soon")}
          </h2>
        </div> */}
        <AnalyticsSortSection />
        {/* <AnalyticsTabs /> */}
        <div className="overflow-auto rounded-lg bg-[#eaeaea] w-[1280px] h-[1100px] mx-auto ">
          <iframe title="Decarbonisez.Analytics" src="https://app.powerbi.com/reportEmbed?reportId=42d40d45-0c18-4fd7-83dc-705650c6ceb5&autoAuth=true&ctid=4186c49a-aa0e-4556-b34a-94af1bb566a5&navContentPaneEnabled=false&filterPaneEnabled=false&pageName=c65841f0a4e260ea45dd" frameBorder="0" allowFullScreen={false} className="w-full h-full "></iframe>
        </div>
      </div>
    </>
  );
};

export default AnalyticsView;
