import { create } from "zustand";

export const useDocvaultStore = create((set, get) => ({
    apiFiles: [],
  files: [],
  filteredFiles: [],
  searchTerm: "",
  searchType: "",
    setApiFiles: (apiFiles) => set(() => ({ apiFiles })),
    
  setFiles: (files) => set(() => ({ files })),
  updateFile: (index, updatedFile) =>
    set((state) => ({
      files: [
        ...state.files.slice(0, index),
        updatedFile,
        ...state.files.slice(index + 1),
      ],
    })),
  addFile: (file) => set((state) => ({ files: [...state.files, file] })),
  updateFolder: (folder_id, files) =>
    set((state) => ({
      files: [
        ...state.files.filter((file) => file.id !== folder_id),
        {
          ...state.files.find((file) => file.id === folder_id),
          children: files,
        },
      ],
    })),
  setSearchTerm: (searchTerm) => set(() => ({ searchTerm })),
  setFilteredFiles: (filteredFiles) => set(() => ({ filteredFiles })),
  setSearchType: (searchType) => set(() => ({ searchType })),
  resetFilters: () => set(() => ({ searchTerm: "", searchType: "" })),

  applyFilters: () => {
    const { files, searchTerm, searchType } = get();
    const filtered = files.filter(file => {
      console.log("file: ", file);
      const matchesSearchTerm = file.label.toLowerCase().includes(searchTerm.toLowerCase());
      const extension = file.label.split('.').pop().toLowerCase();
      const matchesSearchType = searchType ? extension === searchType.toLowerCase() : true;
      return matchesSearchTerm && matchesSearchType;
    }
    );
    console.log("searchTerm: ", searchTerm);
    console.log("Filtered files:", filtered);
    set({ filteredFiles: filtered });
  },
  clearFilteredFiles: () => set(() => ({ filteredFiles: [] })),
  getFilteredFiles: () => {
    const { files, searchTerm, searchType } = get();
    return files.filter(file =>
      file.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
      (searchType ? file.type === searchType : true)
    );
  },
}));

