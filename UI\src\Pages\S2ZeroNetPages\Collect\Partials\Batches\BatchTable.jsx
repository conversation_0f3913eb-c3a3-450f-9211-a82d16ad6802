import Loading from "@/Components/Loading";
import {
  Button,
  Pagination,
  ScrollArea,
  Table,
  TextInput,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import cx from "clsx";
import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { AiOutlineExport } from "react-icons/ai";
import { CiSearch } from "react-icons/ci";
import { GoDatabase } from "react-icons/go";
import CollectMultiselectFilter from "../CollectMultiselectFilter";
import BatchInputPopUp from "./Partials/BatchInputPopUp";

export default function BatchTable({
  assetTypeAll,
  getTableData,
  data,
  loading,
  error,
  Status,
}) {
  useEffect(() => {
    !data && getTableData("batch");
  }, []);
  const { t } = useTranslation();
  const [opened, { open, close }] = useDisclosure(false);
  const [currentBatchId, setCurrentBatchId] = useState();
  const [value, setValue] = useState([]);
  const [search, setSearch] = useState("");
  const [sortedData, setSortedData] = useState();
  const [currentPage, setCurrentPage] = useState(1);
  const rowsPerPage = 10;
  const totalPages = Math.ceil(data?.length / rowsPerPage);

  const currentData = useMemo(() => {
    return (
      (data &&
        data?.slice(
          (currentPage - 1) * rowsPerPage,
          currentPage * rowsPerPage
        )) ||
      []
    );
  }, [data, currentPage, rowsPerPage]);
  // console.log(sortedData);
  useEffect(() => {
    setSortedData(currentData);
    let filteredData;
    if (value[0] === "Pending") {
      filteredData = currentData.filter((item) => item.reviewState === null);
    } else if (value[0] === "Accepted") {
      filteredData = currentData.filter((item) => item.reviewState === true);
    } else if (value[0] === "Rejected") {
      filteredData = currentData.filter((item) => item.reviewState === false);
    } else {
      filteredData = currentData;
    }

    if (search) {
      filteredData = filteredData.filter((data) =>
        data.items.some(
          (item) =>
            item?.companyAsset?.assetName
              .toLowerCase()
              .includes(search.toLowerCase()) ||
            // item.customFactor.activity.toLowerCase().includes(search.toLowerCase()) ||
            // item.customFactor.eFactors.toLowerCase().includes(search.toLowerCase()) ||
            // item.customFactor.uom.toLowerCase().includes(search.toLowerCase()) ||
            item.reportingYear.toString().includes(search)
        )
      );
    }

    setSortedData(filteredData);
  }, [value, search, currentData]);

  const handleSearchChange = (event) => {
    setSearch(event);
  };

  const rows = sortedData?.map((item, indx) => {
    let assetType, asset, dataSet, evidenceKey, evidence;
    evidence = item?.uploadFileLinks?.template;
    evidenceKey = evidence ? Object.keys(evidence) : [];
    item.items.forEach((items) => {
      const assets = assetTypeAll.find(
        (asset) => asset.id === items?.customFactor?.emissionSourceId
      );
      assetType = assets?.asset;
      asset = items?.companyAsset?.assetName;
      dataSet = items?.dataset;

      // isLoadingManual = loadingApprovedBatch[item.id];
    });
    return (
      <Table.Tr
        key={`${indx}`}
        className={`${cx({
          // ["bg-[#07838F1A]"]: selected,
        })} text-sm font-bold text-[#626364] text-center`}
      >
        <Table.Td>
          <div className="w-20 block mx-auto">
            <p className="">
              {item.uploadedDT?.split("T")[1]?.substring(0, 5) || "Not Found"}
            </p>
          </div>
        </Table.Td>
        <Table.Td>
          <div className="w-32 block mx-auto">
            <p className="">{assetType || "Not Found"}</p>
          </div>
        </Table.Td>
        <Table.Td>
          <div className="w-24 block mx-auto">
            <p className="">{asset || "Not Found"}</p>
          </div>
        </Table.Td>
        <Table.Td className="flex justify-center">
          <div className=" text-center  ">
            <Button
              className=" bg-gray-300 hover:bg-gray-300 text-gray-700 hover:text-gray-700 
          rounded-full"
              onClick={() => {
                open();
                setCurrentBatchId(item.id);
              }}
            >
              <GoDatabase className="me-2" /> View Data
            </Button>
          </div>
        </Table.Td>
        <Table.Td>
          <div className=" block mx-auto">
            <p
              className={`py-2 px-10 rounded-2xl`}
              style={{
                backgroundColor:
                  item.reviewState === null
                    ? Status.Pending.bg
                    : item.reviewState === true
                    ? Status.Accepted.bg
                    : item.reviewState === false
                    ? Status.Rejected.bg
                    : "",
                color:
                  item.reviewState === null
                    ? Status.Pending.text
                    : item.reviewState === true
                    ? Status.Accepted.text
                    : item.reviewState === false
                    ? Status.Rejected.text
                    : "",
              }}
            >
              {item.reviewState === null
                ? "Pending"
                : item.reviewState === true
                ? "Accepted"
                : item.reviewState === false
                ? "Rejected"
                : "Not Found"}
            </p>
          </div>
        </Table.Td>
        <Table.Td>
          <div className="">
            <p className="w-full">
              {evidenceKey?.map((item, idx) => (
                <Button
                  key={idx}
                  variant="subtle"
                  href={evidence[item]}
                  target="_blank"
                  component="a"
                >
                  {item}
                </Button>
              )) || "Not Found"}{" "}
            </p>
          </div>
        </Table.Td>
      </Table.Tr>
    );
  });
  return (
    <>
      {loading ? (
        <Loading />
      ) : (
        <div className="bg-white mt-7 p-1 rounded-lg overflow-hidden">
          <div className="p-2 mt-1  grid items-center  xl:justify-between px-4 bg-white xl:grid-cols-3 rounded-t-lg w-full">
            <div className="xl:col-span-1 Manage-Uploaded-Record w-full flex justify-center xl:justify-start">
              <h1 className="font-bold text-lg text-black">
                {t("Recent upload manual input data")}
              </h1>
            </div>
            <div className="xl:col-span-2 grid items-center  justify-center grid-cols-1 lg:grid-cols-3 xl:grid-cols-4 sm:items-center w-full lg:gap-5">
              <TextInput
                className="w-full col-span-2 SEARCH"
                placeholder="Search by Topic Area or Assessment Question"
                leftSection={<CiSearch className="w-5 h-5" />}
                leftSectionPointerEvents="none"
                value={search}
                onChange={(e) => handleSearchChange(e.target.value)}
                // disabled
              />{" "}
              <div className="Filter-Batch-collect md:col-span-1 m-3 bg-white hover:bg-white border-2 border-[#E8E7EA] rounded-lg shadow-sm md:ms-auto mx-auto w-full">
                <CollectMultiselectFilter
                  setValue={setValue}
                  value={value}
                  style={
                    "text-[#9E939A] font-semibold  w-full flex justify-center items-center cursor-pointer py-2 text-nowrap"
                  }
                />
              </div>
              <div className="md:col-span-1 Export-Records-Batch  justify-center items-center  p-2 rounded-lg shadow-sm w-full">
                <Button className="bg-[#e6f3f4]  text-primary hover:bg-[#e6f3f4] hover:text-[#00C0A9] rounded-lg w-full">
                  {t("export")}
                  <AiOutlineExport className="ms-2" />
                </Button>
              </div>
            </div>
          </div>
          <>
            {(search && sortedData?.length === 0) ||
            (value.length && sortedData?.length === 0) ? (
              <h1 className="mt-5 text-center capitalize">
                Your Search is not Found
              </h1>
            ) : (
              <>
                <ScrollArea>
                  <Table
                    // miw={800}
                    verticalSpacing="sm"
                    className="p-2  bg-white shadow-lg"
                    withTableBorder
                    highlightOnHover
                  >
                    <Table.Thead className="border-b-2 border pb-6 bg-[#f5f4f5] text-secondary-500 font-bold text-base text-center">
                      <Table.Tr>
                        <Table.Th className="text-center">Timestamp</Table.Th>
                        <Table.Th className="text-center">Assets Type</Table.Th>
                        <Table.Th className="text-center">Assets</Table.Th>
                        <Table.Th className="text-center">
                          Uploaded Data{" "}
                        </Table.Th>
                        <Table.Th className="text-center STatus-Overview">status</Table.Th>

                        <Table.Th className="text-center">
                          Evidence/Notes
                        </Table.Th>
                      </Table.Tr>
                    </Table.Thead>
                    <Table.Tbody>{rows}</Table.Tbody>
                  </Table>
                </ScrollArea>
                <div className="md:flex justify-between mt-5">
                  <p
                    className="text-sm text-black"
                    hidden={!sortedData?.length}
                  >
                    {t("showingData", {
                      start: (currentPage - 1) * rowsPerPage + 1,
                      end: Math.min(currentPage * rowsPerPage, data?.length),
                      total: data?.length,
                    })}
                  </p>
                  <Pagination
                    page={currentPage}
                    onChange={(e) => {
                      setCurrentPage(e);
                    }}
                    total={totalPages}
                    color="#dde7e9"
                    autoContrast
                    className={`flex justify-center mt-5 gap-0 md:mt-0 ${
                      !sortedData?.length && "hidden"
                    }`}
                    classNames={{ control: "gap-0 rounded-none" }}
                  />
                </div>
              </>
            )}
          </>
        </div>
      )}
      <BatchInputPopUp
        opened={opened}
        onClose={close}
        currentBatchId={currentBatchId}
        data={data}
        assetTypeAll={assetTypeAll}
      />
    </>
  );
}
