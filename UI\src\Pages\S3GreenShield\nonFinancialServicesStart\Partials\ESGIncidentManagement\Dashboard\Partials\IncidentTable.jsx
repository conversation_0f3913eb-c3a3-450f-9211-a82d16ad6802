import React, { useEffect, useState } from "react";
import { Table, <PERSON>rollA<PERSON>, Button } from "@mantine/core";
import { useNavigate } from "react-router-dom";
import ApiS3 from "@/Api/apiS3";
import { RiDeleteBin6Line } from "react-icons/ri";
import { showNotification } from "@mantine/notifications";
import {
  IconCheck as IconCheckNotification,
  IconX as IconXNotification,
} from "@tabler/icons-react";
import { GrFormPrevious } from "react-icons/gr";
import { MdNavigateNext } from "react-icons/md";
import Loading from "@/Components/Loading";

export default function IncidentTable({ SETData }) {
  const navigate = useNavigate();
  const [data, setData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(false);

  const rowsPerPage = 6;

  const IncidentTableGetData = async () => {
    setLoading(true);

    try {
      setLoading(true);
      const { data } = await ApiS3.get("/incident-management/incidents");
      setLoading(false);
      setData(data.incidents);
      console.log(data.incidents);
    } catch (error) {
      setLoading(false);
      console.log(error);
    }
  };

  useEffect(() => {
    IncidentTableGetData();
  }, []);

  const handleDeleteRow = async (id) => {
    try {
      const { data } = await ApiS3.delete(
        `/incident-management/incidents/${id}`
      );
      if (data.message === "Incident's deleted successfully") {
        showNotification({
          title: "Success",
          message: data.message,
          color: "teal",
          icon: <IconCheckNotification />,
        });
        IncidentTableGetData();
        SETData();
      }
    } catch (error) {
      showNotification({
        title: "Error",
        message: error.errors[0].msg,
        color: "red",
        icon: <IconXNotification />,
      });
    }
  };

  const currentData = data.slice(
    (currentPage - 1) * rowsPerPage,
    currentPage * rowsPerPage
  );

  const totalPages = Math.ceil(data.length / rowsPerPage);

  const rows = currentData.map((item) => (
    <Table.Tr
      key={item._id}
      className="text-sm font-bold text-[#626364] text-center cursor-pointer"
      onClick={() =>
        navigate(
          `/green-shield/nonFinancial/ESG-incident-management/SupIncidents/${item._id}`
        )
      }
    >
      <Table.Td>
        <span
          className="flex justify-center items-center text-xl cursor-pointer"
          onClick={(e) => {
            e.stopPropagation();
            handleDeleteRow(item._id);
          }}
        >
          <RiDeleteBin6Line />
        </span>
      </Table.Td>
      <Table.Td>{item.title}</Table.Td>
      <Table.Td>{item.createdAt.split("T")[0]}</Table.Td>
      <Table.Td className="border-primary rounded-lg">
        <span
          className={`border-primary py-2 rounded-lg w-full capitalize
            ${
              item.status.includes("in_progress")
                ? "bg-[#fff0b8] text-[#FFAB07] px-2"
                : item.status.includes("new")
                ? "bg-[#cafac2] text-[#70D162] px-7"
                : item.status.includes("resolved")
                ? "bg-[#cea7f6] text-[#9160C1] px-3"
                : "bg-red-300 text-red-800 px-5"
            }
            `}
        >
          {item.status.includes("in_progress")
            ? item.status.replace("_", " ")
            : item.status}
        </span>
      </Table.Td>
      <Table.Td>
        {item.involvedIndividuals.length > 0 ? "true" : "false"}
      </Table.Td>
      <Table.Td className="capitalize">
        {item.details.city}, {item.details.country}
      </Table.Td>
      <Table.Td>{item.details.impactAssessment}</Table.Td>
    </Table.Tr>
  ));

  return (
    <>
      {loading ? (
        <div className="m-auto flex justify-center w-full ">
          <Loading />
        </div>
      ) : (
        <div className="bg-[#F7F4F4] mt-7">
          <div className="md:flex justify-between">
            <p className="font-bold text-lg">Recent Incidences</p>
            <Button
              className="bg-transparent text-primary hover:bg-transparent hover:text-primary mt-3 md:mt-0 underline"
              size="md"
            >
              View all
            </Button>
          </div>
          <ScrollArea>
            <Table miw={800} verticalSpacing="sm">
              <Table.Thead className="border-b-2 border-primary pb-6 text-primary font-bold text-base text-center">
                <Table.Tr>
                  <Table.Th className="text-center">Delete</Table.Th>
                  <Table.Th className="text-center">Title</Table.Th>
                  <Table.Th className="text-center">Date</Table.Th>
                  <Table.Th className="text-center">Status</Table.Th>
                  <Table.Th className="text-center">People involved</Table.Th>
                  <Table.Th className="text-center">Location</Table.Th>
                  <Table.Th className="text-center">Impact assessment</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>{rows}</Table.Tbody>
            </Table>
          </ScrollArea>

          {/* Pagination */}
          <div className="flex items-center justify-between mt-4 mr-10">
            <p className="text-sm text-gray-600">
              Showing data {(currentPage - 1) * rowsPerPage + 1} to{" "}
              {Math.min(currentPage * rowsPerPage, data.length)} of{" "}
              {data.length} entries
            </p>
            <div className="flex items-center space-x-2">
              <button
                className="p-1 border rounded-md border-primary hover:bg-primary hover:text-white"
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
              >
                <GrFormPrevious />

                {/* <i className="fa fa-chevron-left"></i> */}
              </button>
              {[...Array(totalPages).keys()].map((page) => (
                <button
                  key={page + 1}
                  className={`px-3 py-1 border rounded-md ${
                    currentPage === page + 1 ? "bg-teal-500 text-white" : ""
                  }`}
                  onClick={() => setCurrentPage(page + 1)}
                >
                  {page + 1}
                </button>
              ))}
              <button
                className="p-1 border rounded-md border-primary hover:bg-primary hover:text-white"
                onClick={() =>
                  setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                }
                disabled={currentPage === totalPages}
              >
                <MdNavigateNext />

                {/* <i className="fa fa-chevron-right "></i> */}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
