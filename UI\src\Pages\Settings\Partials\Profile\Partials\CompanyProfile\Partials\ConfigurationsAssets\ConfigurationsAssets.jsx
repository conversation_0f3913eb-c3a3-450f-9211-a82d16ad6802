import ApiS2 from "@/Api/apiS2Config";
import Loading from "@/Components/Loading";
import MainLayout from "@/Layout/MainLayout";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import { Button, Input, Modal, Pagination } from "@mantine/core";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next"; // Import i18next hook
import { useLocation, useNavigate } from "react-router";
import ConfigurationDataTable from "./Partials/ConfigurationDataTable";
import { useAuth } from "@/Contexts/AuthContext";
import { CiSearch } from "react-icons/ci";
import { FaPlus } from "react-icons/fa";
import { useDisclosure } from "@mantine/hooks";
import AssetsForm from "./Partials/AssetsForm";
import _ from "lodash";
import { driver } from "driver.js";
import "driver.js/dist/driver.css";
import "@/assets/css/index.css";
import { FaQuestionCircle } from "react-icons/fa";
import { IoMdHome } from "react-icons/io";

export default function ConfigurationsAssets() {
  const { user } = useAuth();
  const { getStartMenu } = useSideBarRoute();
  const { pathname } = useLocation();
  const [opened, { open, close }] = useDisclosure(false);
  const navigate = useNavigate();
  const { t } = useTranslation(); // Use translation function
  const [assetTypeDrop, setAssetTypeDrop] = useState([]);
  const [assetTypeAll, setAssetTypeAll] = useState([]);
  const [loading, setLoading] = useState(false);
  const [AllAsset, setAllAsset] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(8);
  const getAssets = async () => {
    setLoading(true);
    try {
      const { data } = await ApiS2.get("carbon-factors/get-all-company-assets");
      setLoading(false);
      setAllAsset(data);
    } catch (error) {
      setLoading(false);
    }
  };
  const handelAssetType = async () => {
    setLoading(true);
    try {
      setLoading(true);
      const { data } = await ApiS2.get("carbon-factors/get-all-asset-types");
      setLoading(false);
      setAssetTypeDrop(data.map((item) => item.asset));
      setAssetTypeAll(data);
    } catch (error) {
      setLoading(false);
    }
  };

  const getGuideSteps = () => [
    {
      element: ".add-asset",
      popover: {
        title: t("Your asset table is currently empty."),
        description: t("Click 'Add Assets' to start adding your assets. "),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".manage-asset",
      popover: {
        title: t("Manage your assets by editing and deleting them."),

        side: "left",
        align: "center",
      },
    },
    {
      element: ".Search-assets",
      popover: {
        title: t("Search for Assets"),
        description: t("Use the “Search” feature to find your assets easily."),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".emission-factor",
      popover: {
        title: t(
          "Once you have added your assets you can define custom emission factors for precise calculations that can be connected to the asset in Manual Input."
        ),

        side: "left",
        align: "center",
      },
    },
  ];

  const startGuide = () => {
    const driverObj = driver({
      showProgress: true,
      popoverClass: "my-custom-popover-class",
      nextBtnText: "Next",
      prevBtnText: "Back",
      doneBtnText: "Done",
      overlayColor: "rgba(0, 0, 0, 0)",
      progressText: "Step {{current}} of {{total}}",
      steps: getGuideSteps(),
      onDestroyStarted: () => {
        localStorage.setItem("hasSeenAssetGuide", "true");
        driverObj.destroy();
      },
    });
    driverObj.drive();
  };

  useEffect(() => {
    const hasSeenGuide = localStorage.getItem("hasSeenAssetGuide");
    if (!hasSeenGuide) {
      startGuide();
    }
    return () => {
      const driverObj = driver();
      if (driverObj.isActive()) {
        driverObj.destroy();
      }
    };
  }, []);

  useEffect(() => {
    handelAssetType();
    getAssets();
  }, []);

  const chunkedData = _.chunk(AllAsset, rowsPerPage);
  const totalPages = chunkedData.length || 0;
  const currentData = chunkedData[Math.min(currentPage, totalPages) - 1] || [];
  return (
    <>
      <MainLayout menus={getStartMenu} navbarTitle={t("Configurations")}
      breadcrumbItems={[
        { title: <IoMdHome size={20} />, href: "/get-started" },
        { title: t("Settings"), href: "/Settings" },
        { title: t("Configurations"), href: "" },
      ]}
      >
        <>
          <div
            onClick={startGuide}
            style={{
              position: "fixed",
              bottom: 20,
              right: 20,
              cursor: "pointer",
              zIndex: 1000,
            }}
          >
            <div className="h-12 w-12 flex items-center justify-center bg-gradient-to-tr from-[#2C5A8C] via-[#1C889C] to-[#13B1A8] p-1  cursor-pointer rounded-full">
              <FaQuestionCircle
                size={34}
                color="#ffffff"
                className="mx-auto cursor-pointer"
              />
            </div>
          </div>
          <div className="w-full p-5 text-center bg-white rounded-lg">
            <h1 className="text-3xl font-bold text-primary">{t("Assets")}</h1>
          </div>
          <div className="justify-around  p-5 mt-5 text-center rounded-lg xl:flex">
            {user?.userRole === "Admin" && (
              <Button
                className={`px-16 hover:bg-primary hover:text-white  ${
                  pathname.includes("/Configurations/CompanyUserManage")
                    ? "bg-primary text-white"
                    : "bg-[#07838F33] border border-primary text-primary"
                }`}
                size="lg"
                onClick={() =>
                  navigate(
                    "/Settings/CompanyProfile/Configurations/CompanyUserManage"
                  )
                }
              >
                {t("Manage Users")}
              </Button>
            )}
            {user?.userRole === "Admin" && (
              <Button
                className={`px-16 hover:bg-primary hover:text-white  block mx-auto xl:mx-0 mt-5 xl:mt-0  ${
                  pathname.includes("/Configurations/CompanyLogs")
                    ? "bg-primary text-white"
                    : "bg-[#07838F33] border border-primary text-primary"
                }`}
                size="lg"
                onClick={() =>
                  navigate(
                    "/Settings/CompanyProfile/Configurations/CompanyLogs"
                  )
                }
              >
                {t("Company Logs")}
              </Button>
            )}
            {user?.userRole === "Admin" && (
              <Button
                className={`hover:bg-primary hover:text-white block mx-auto xl:mx-0 mt-5 xl:mt-0 ${
                  pathname.includes("/Configurations/DepartmentsandProjects")
                    ? "bg-primary text-white"
                    : "bg-[#07838F33] border border-primary text-primary"
                }`}
                size="lg"
                onClick={() =>
                  navigate(
                    "/Settings/CompanyProfile/Configurations/DepartmentsandProjects"
                  )
                }
              >
                {t("Departments and Projects")}
              </Button>
            )}
            <Button
              className={`px-24 hover:bg-primary hover:text-white block mx-auto xl:mx-0 mt-5 xl:mt-0 ${
                pathname.includes("/Configurations/Assets")
                  ? "bg-primary text-white"
                  : "bg-[#07838F33] border border-primary text-primary"
              }`}
              size="lg"
              onClick={() =>
                navigate("/Settings/CompanyProfile/Configurations/Assets")
              }
            >
              {t("Assets")}
            </Button>
            <Button
              className={` hover:bg-primary hover:text-white block mx-auto xl:mx-0 mt-5 xl:mt-0 emission-factor ${
                pathname.includes("/Configurations/CustomFactor")
                  ? "bg-primary text-white"
                  : "bg-[#07838F33] border border-primary text-primary"
              }`}
              size="lg"
              onClick={() =>
                navigate("/Settings/CompanyProfile/Configurations/CustomFactor")
              }
            >
              {t("Emission Factor Selection")}
            </Button>
          </div>
        </>

        {/* modal */}
        <Modal
          size={"xl"}
          opened={opened}
          onClose={close}
          title=""
          key={Date.now()}
        >
          <div className="p-6">
            <div className="flex items-center gap-2 mb-6">
              <div className="w-6 h-6 rounded-xl bg-primary/50 flex items-center justify-center">
                <FaPlus size={10} className="text-primary" />
              </div>
              <h1 className="text-2xl font-bold text-navy-900">Add Assets</h1>
            </div>
            <hr className="mt-1 mb-3 bg-[#D1D1D1] h-[2px]" />

            <h2 className="bg-gray-100 py-1 px-1 text-xl font-bold rounded-xl text-navy-900">
              Assets Details
            </h2>
            {/* Add your form fields here */}

            {/* form component */}
            <AssetsForm
              fetchAgain={getAssets}
              assetTypeAll={assetTypeAll}
              assetTypeDrop={assetTypeDrop}
            />
          </div>
        </Modal>

        <div className="shadow-lg rounded-2xl bg-white p-4">
          <div className="flex mb-5 justify-between items-center">
            <h5 className="text-2xl font-semibold">Assets List</h5>

            <div className="flex gap-2 items-center">
              <Input
                type="text"
                placeholder={t("SearchPlaceholder")} // Translated placeholder
                className="border Search-assets border-gray-300 rounded-md sm:w-auto focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                size="md"
                leftSection={<CiSearch />}
              />
              <Button
                onClick={open}
                size="md"
                className="bg-primary flex items-center gap-3 add-asset"
              >
                <FaPlus size={10} />
                Add Assets
              </Button>
            </div>
          </div>
          {loading ? (
            <Loading />
          ) : (
            <ConfigurationDataTable
              assetTypeDrop={assetTypeDrop}
              fetchAgain={getAssets}
              tableData={currentData || []}
              assetTypeAll={assetTypeAll}
            />
          )}
          <div className="flex justify-end">
            <Pagination
              color="#05808b"
              value={Math.min(currentPage, totalPages)}
              onChange={setCurrentPage}
              total={totalPages}
            />
          </div>
        </div>
      </MainLayout>
    </>
  );
}
