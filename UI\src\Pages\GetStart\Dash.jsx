import Loading from "@/Components/Loading";
import { useAuth } from "@/Contexts/AuthContext";
import { useDisclosure } from "@mantine/hooks";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import CustomPopup from "../ESGCompassSuite/AssessmentType/Partials/CustomPopup";
import { getProgramsData } from "./programsData";
import { Accordion } from "@mantine/core";
import Chatbot from "@/Components/Chatbot";
import { useTheme } from "next-themes";


const Dash = () => {
  const { theme } = useTheme();

  const {
    CompanyAccess,
    S1CurrentAssessment,
    S1Access: S1,
    S2Access: S2,
    S3Access: S3,
    greenHubAccess: GreenHub,
    EnterpriseBackboneAccess: EnterpriseBackbone,
    launchQuiz,
    launchQuizLoading: loading,
    getCompanyAccess,
    checkProgramStatus,
  } = useAuth();
  const auth = useAuth();
  const programsData = getProgramsData(auth);
  const [opened, { open, close }] = useDisclosure(false);
  const [selectedChild, setSelectedChild] = useState();

  const navigation = useNavigate();
  useEffect(() => {
    if (!CompanyAccess) {
      getCompanyAccess();
    }
    if (S1CurrentAssessment) {
      checkProgramStatus();
    }
  }, []);

  const handleClick = (child) => {
    if (!child.isActive) return;
    if (child.popup) {
      if (!child.havCurrentAssessment) {
        if (child.assessmentType === "GreenSight AI") {
          open();
          setSelectedChild(child);
        } else {
          launchQuiz(child);
        }
      } else {
        navigation(child.summaryPath || child.link);
      }
    } else {
      if (child.abslouteLink) {
        window.open(child.abslouteLink, "_self");
      } else {
        navigation(child.link);
      }
    }
  };

  const allValues = programsData.map((v) => v.title);

  return (
    <div className="pb-10 min-h-screen">
      {!Object.keys(CompanyAccess).length ||
        !S1 ||
        !S2 ||
        !S3 ||
        !EnterpriseBackbone ? (
        <Loading />
      ) : (
        <div>
          <Accordion

            multiple={true}
            variant="separated"
            defaultValue={allValues || []}
            transitionDuration={800}
          >
            {[programsData[0]].map((program, index) => {
              const children = program.children;
              const totalItems = children.length;
              const fullRows = Math.floor(totalItems / 4);
              const remaining = totalItems % 4;

              return (
                <Accordion.Item
                  p={0}
                  className="p-0 rounded-2xl bg-white dark:bg-[#1e1e1e] dark:border dark:border-black"
                  key={index}
                  value={program.title}
                >
                  <Accordion.Control
                    className="rounded-t-2xl "
                    style={{
                      backgroundColor: theme === "dark" ? "#07838F" : program.titleColor,
                      
                    }}
                  >
                    <div className="flex justify-between items-center ">
                      <h1 className="Carbon-Intelligence-dashboard font-bold text-base  md:text-lg lg:text-xl text-center lg:text-start">
                        {program.title}
                      </h1>
                    </div>
                  </Accordion.Control>
                  <Accordion.Panel p={0}>
                    <div className="bg-white rounded-lg mb-5 overflow-hidden dark:bg-[#1E1E1E]">
                      <div className="p-3 flex flex-col gap-5 ">
                        {[...Array(fullRows)].map((_, rowIndex) => (
                          <div
                            key={rowIndex}
                            className="grid lg:grid-cols-2 xl:grid-cols-4 gap-5 "
                          >
                            {children
                              .slice(rowIndex * 4, rowIndex * 4 + 4)
                              .map((child, idx) => (
                                <ProgramCard
                                  key={idx}
                                  child={child}
                                  handleClick={handleClick}
                                />
                              ))}
                          </div>
                        ))}

                        {remaining > 0 && (
                          <div
                            className={`grid lg:grid-cols-2  xl:grid-cols-${remaining} gap-5`}
                          >
                            {children.slice(fullRows * 4).map((child, idx) => (
                              <ProgramCard
                                key={idx}
                                child={child}
                                handleClick={handleClick}
                              />
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </Accordion.Panel>
                </Accordion.Item>
              );
            })}
            {[programsData[1]].map((program, index) => {
              const children = program.children;
              const totalItems = children.length;
              const fullRows = Math.floor(totalItems / 4);
              const remaining = totalItems % 4;

              return (
                <Accordion.Item
                  p={0}
                  className="p-0 rounded-2xl bg-white dark:bg-[#1e1e1e] dark:border dark:border-black"
                  key={index}
                  value={program.title}
                >
                  <Accordion.Control
                    className="rounded-t-2xl"
                   style={{
                      backgroundColor: theme === "dark" ? "#07838F" : program.titleColor,
                    }}
                  >
                    <div className="flex justify-between items-center">
                      <h1 className="ESGInsights-Reporting-dashboard font-bold text-base md:text-lg lg:text-xl text-center lg:text-start">
                        {program.title}
                      </h1>
                    </div>
                  </Accordion.Control>
                  <Accordion.Panel p={0}>
                    <div className="bg-white rounded-lg mb-5 overflow-hidden dark:bg-[#1E1E1E]">
                      <div className="p-3 flex flex-col gap-5 ">
                        {[...Array(fullRows)].map((_, rowIndex) => (
                          <div
                            key={rowIndex}
                            className="grid lg:grid-cols-2 xl:grid-cols-4 gap-5  "
                          >
                            {children
                              .slice(rowIndex * 4, rowIndex * 4 + 4)
                              .map((child, idx) => (
                                <ProgramCard
                                  key={idx}
                                  child={child}
                                  handleClick={handleClick}
                                />
                              ))}
                          </div>
                        ))}

                        {remaining > 0 && (
                          <div
                            className={`grid lg:grid-cols-2  xl:grid-cols-${remaining} gap-5`}
                          >
                            {children.slice(fullRows * 4).map((child, idx) => (
                              <ProgramCard
                                key={idx}
                                child={child}
                                handleClick={handleClick}
                              />
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </Accordion.Panel>
                </Accordion.Item>
              );
            })}
            {[programsData[2]].map((program, index) => {
              const children = program.children;

              return (
                <Accordion.Item
                  p={0}
                  className="p-0 rounded-2xl bg-white dark:bg-[#1e1e1e] dark:border dark:border-black"
                  key={index}
                  value={program.title}
                >
                  <Accordion.Control
                    className="rounded-t-2xl"
                    style={{
                      backgroundColor: theme === "dark" ? "#07838F" : program.titleColor,
                    }}
                  >
                    <div className="flex justify-between items-center">
                      <h1 className="Risk-Compliance-dashboard font-bold text-base md:text-lg lg:text-xl text-center lg:text-start">
                        {program.title}
                      </h1>
                    </div>
                  </Accordion.Control>
                  <Accordion.Panel p={0}>
                    <div className="bg-white rounded-lg mb-5 overflow-hidden dark:bg-[#1E1E1E]">
                      <div className="p-3 flex flex-col gap-5 ">
                        <div className="grid lg:grid-cols-2 gap-5 ">
                          {children.slice(0, 2).map((child, idx) => (
                            <ProgramCard
                              key={idx}
                              child={child}
                              handleClick={handleClick}
                            />
                          ))}
                        </div>
                        <div className="grid lg:grid-cols-3 gap-5 ">
                          {children.slice(2).map((child, idx) => (
                            <ProgramCard
                              key={idx}
                              child={child}
                              handleClick={handleClick}
                            />
                          ))}
                        </div>
                      </div>
                    </div>
                  </Accordion.Panel>
                </Accordion.Item>
              );
            })}
          </Accordion>

          <div className="grid gap-5 lg:grid-cols-3 mt-5">
            <Accordion
              multiple={true}
              variant="separated"
              defaultValue={allValues || []}
              transitionDuration={800}
            >
              {[programsData[3]].map((program, index) => {
                const children = program.children;

                return (
                  <Accordion.Item
                    p={0}
                    className="p-0 rounded-2xl bg-white dark:bg-[#1e1e1e] dark:border dark:border-black"
                    key={index}
                    value={program.title}
                  >
                    <Accordion.Control
                      className="rounded-t-2xl"
                      style={{
                      backgroundColor: theme === "dark" ? "#07838F" : program.titleColor,
                    }}
                    >
                      <div className="flex justify-between items-center">
                        <h1 className="Data-Foundation-dashboard font-bold text-base md:text-lg lg:text-xl text-center lg:text-start">
                          {program.title}
                        </h1>
                      </div>
                    </Accordion.Control>
                    <Accordion.Panel p={0}>
                      <div className="bg-white rounded-lg mb-5 overflow-hidden dark:bg-[#1E1E1E]">
                        <div className="p-3 flex flex-col gap-5 ">
                          <div className="grid lg:grid-cols-2 gap-5 ">
                            {children.map((child, idx) => (
                              <ProgramCard
                                key={idx}
                                child={child}
                                handleClick={handleClick}
                              />
                            ))}
                          </div>
                        </div>
                      </div>
                    </Accordion.Panel>
                  </Accordion.Item>
                );
              })}
            </Accordion>
            <Accordion
              multiple={true}
              variant="separated"
              defaultValue={allValues || []}
              transitionDuration={800}
            >
              {[programsData[4]].map((program, index) => {
                const children = program.children;

                return (
                  <Accordion.Item
                    p={0}
                    className="p-0 rounded-2xl bg-white dark:bg-[#1e1e1e] dark:border dark:border-black"
                    key={index}
                    value={program.title}
                  >
                    <Accordion.Control
                      className="rounded-t-2xl "
                     style={{
                      backgroundColor: theme === "dark" ? "#07838F" : program.titleColor,
                    }}
                    >
                      <div className="flex justify-between items-center ">
                        <h1 className="DocVault-dashboard font-bold text-base md:text-lg lg:text-xl text-center lg:text-start ">
                          {program.title}
                        </h1>
                      </div>
                    </Accordion.Control>
                    <Accordion.Panel p={0}>
                      <div className="bg-white dark:bg-[#1e1e1e] flex flex-col gap-5 p-3 rounded-lg mb-5 overflow-hidden">
                        {children.map((child, idx) => (
                          <ProgramCard
                            key={idx}
                            child={child}
                            handleClick={handleClick}
                          />
                        ))}
                      </div>
                    </Accordion.Panel>
                  </Accordion.Item>
                );
              })}
            </Accordion>
            <Accordion
              multiple={true}
              variant="separated"
              defaultValue={allValues || []}
              transitionDuration={800}
            >
              {[programsData[5]].map((program, index) => {
                const children = program.children;

                return (
                  <Accordion.Item
                    p={0}
                    className="p-0 rounded-2xl bg-white dark:bg-[#1e1e1e] dark:border dark:border-black"
                    key={index}
                    value={program.title}
                  >
                    <Accordion.Control
                      className="rounded-t-2xl"
                     style={{
                      backgroundColor: theme === "dark" ? "#07838F" : program.titleColor,
                    }}
                    >
                      <div className="flex justify-between items-center">
                        <h1 className="GreenHub-dashboard font-bold text-base md:text-lg lg:text-xl text-center lg:text-start">
                          {program.title}
                        </h1>
                      </div>
                    </Accordion.Control>
                    <Accordion.Panel p={0}>
                      <div className="bg-white dark:bg-[#1E1E1E]  flex flex-col gap-5 p-3 rounded-lg mb-5 overflow-hidden">
                        {children.map((child, idx) => (
                          <ProgramCard
                            key={idx}
                            child={child}
                            handleClick={handleClick}
                          />
                        ))}
                      </div>
                    </Accordion.Panel>
                  </Accordion.Item>
                );
              })}
            </Accordion>
          </div>
        </div>
      )}

      {/* {opened && selectedChild && (
        <CustomPopup
          opened={opened}
          close={close}
          Active={selectedChild.isActive}
          path={selectedChild.path || selectedChild.link}
          assessmentType={selectedChild.assessmentType}
          loading={loading}
          launchQuiz={() => launchQuiz(selectedChild)}
        />
      )} */}

      <br />
      <br />
      <br />
      {<Chatbot />}
    </div>
  );
};
const ProgramCard = ({ child, handleClick }) => (
  <button
    title={child.title}
    className={`p-3 rounded-lg flex flex-col  items-start w-full
    ${child.isActive
        ? "cursor-pointer bg-[#f9f9f9]  dark:bg-[#292929] dark:text-white hover:scale-105 duration-150"
        : "cursor-not-allowed bg-[#a5a5a5]"
      }`}
    onClick={() => handleClick(child)}
  >
    <div className="bg-[#07838F1A] w-10 h-10 flex items-center justify-center rounded-lg p-1">
      {child.icon}
    </div>
    <h1 className="font-bold text-base md:text-lg lg:text-xl text-[#494949] dark:text-white mt-3 text-center">
      {child.title}
    </h1>
    <p className="text-start font-medium text-[#6B7280]  text-xs ">
      {child.supTitle}
    </p>
  </button>
);

export default Dash;
