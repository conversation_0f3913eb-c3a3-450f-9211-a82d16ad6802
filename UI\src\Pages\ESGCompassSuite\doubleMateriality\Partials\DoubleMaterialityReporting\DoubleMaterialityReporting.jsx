import Loading from "@/Components/Loading";
import { useDoubleMateriality } from "@/Contexts/DoubleMaterialityContext";
import HistoryPopUp from "@/Pages/ESGCompassSuite/AssessmentType/Partials/HistoryPopUp";
import Share from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/Share";
import { btnStyle, linkStyle } from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/StaticData";
import ViewPDF from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/ViewPDF";
import { Area<PERSON>hart, DonutChart } from "@mantine/charts";
import { Button, RingProgress, Text } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { CiShare2 } from "react-icons/ci";
import { FaCircle } from "react-icons/fa";
import { FiDownload } from "react-icons/fi";
import { MdHistory, MdOutlineRemoveRedEye } from "react-icons/md";

export default function DoubleMaterialityReporting() {
 const { getDynamicData, GetDynamicData, lineData } = useDoubleMateriality();
 const [historyOpened, { open: historyOpen, close: historyClose }] =
  useDisclosure(false);
 useEffect(() => {
  getDynamicData();
 }, []);

 const DonutData = [
  {
   name: "Governance",
   value: GetDynamicData?.categories.Governance,
   color: "red",
  },
  {
   name: "Environment",
   value: GetDynamicData?.categories.Environmental,
   color: "yellow.5",
  },
  {
   name: "Social",
   value: GetDynamicData?.categories.Social,
   color: "green.5",
  },
  {
   name: "Economic",
   value: GetDynamicData?.categories.Economic,
   color: "blue.5",
  },
 ];
 const { t } = useTranslation();
 return (
  <>
   {!GetDynamicData ? (
    <Loading />
   ) : (
    <div className="flex flex-col w-full left-section min-h-svh">
     <div className="report-page flex flex-col lg:flex-row items-center lg:justify-between gap-y-3 flex-wrap mb-[38px]">
      <div>
       <Button className={linkStyle} size="md" onClick={historyOpen}>
        <MdHistory className="me-1" />
        Assessment History
       </Button>
      </div>
      <div className="flex flex-col lg:flex-row items-center justify-center gap-6">
       
       <ViewPDF
        btnStyle={
         GetDynamicData?.pdf_url
          ? btnStyle
          : "border-[1px] flex items-center px-2  h-[56px] w-[195px]  justify-center text-[17px] font-bold rounded-lg text-white border-gray-300 bg-gray-300 hover:text-white hover:bg-gray-300 cursor-not-allowed"
        }
        pdfUrl={GetDynamicData?.pdf_url}
        text={"View Report"}
        disabled={!GetDynamicData?.pdf_url && true}
       />
      
       <Share
        link={GetDynamicData?.pdf_url}
        btnStyle={
         GetDynamicData?.pdf_url
          ? btnStyle
          : "border-[1px] flex items-center px-2  h-[56px] w-[195px]  justify-center text-[17px] font-bold rounded-lg text-white border-gray-300 bg-gray-300 hover:text-white hover:bg-gray-300 cursor-not-allowed"
        }
        disabled={!GetDynamicData?.pdf_url && true}
       />
       <Button
        component="a"
        className={
         GetDynamicData?.pdf_url
          ? linkStyle
          : "border-[1px] flex items-center px-2  h-[56px] w-[195px]  justify-center text-[17px] font-bold rounded-lg text-white border-gray-300 bg-gray-300 hover:text-white hover:bg-gray-300 cursor-not-allowed"
        }
        href={GetDynamicData?.pdf_url}
        download
        disabled={!GetDynamicData?.pdf_url ? true : false}
       >
        Download Report
        <span>
         <FiDownload className="text-lg ms-1" />
        </span>
       </Button>
      </div>
     </div>

     <div className="flex flex-wrap mt-5 mb-7 gap-9 md:flex-nowrap">
      {/* donut chart */}
      <div className="flex bg-white p-5 flex-grow md:w-1/2 justify-center flex-col items-center rounded-lg shadow-[0px_0px_10px_0px_#0000001A]">
       <h5 className="font-bold">{t("overallReadinessScore")}</h5>
       <span className="flex items-center justify-center w-full">
        <RingProgress
         size={180}
         thickness={20}
         roundCaps
         className="w-full aspect-square text-center flex justify-center items-center scale-x-[-1] rotate-90"
         sections={[
          {
           value: (GetDynamicData?.overall_score || 0) * 20,
           color: "#29919B",
          },
         ]}
         rootColor="#D4E9EB"
         label={
          <Text
           c="black"
           fw={700}
           ta="center"
           size="xl"
           className="rotate-90 scale-x-[-1] text-3xl flex justify-center items-center"
          >
           {GetDynamicData?.overall_score || 0}
          </Text>
         }
        />
       </span>
      </div>

      {/* line chart */}
      {lineData?.length > 0 ? (
       <div className="bg-white flex-grow md:w-1/2 text-center p-5 rounded-lg shadow-[0px_0px_10px_0px_#0000001A]">
        <h5 className="mb-6 font-bold">{t("progressTracker")}</h5>
        <AreaChart
         h={200}
         data={lineData}
         dataKey="date"
         series={[{ name: "Month", color: "blue.6" }]}
         curveType="natural"
         withDots={false}
        />
       </div>
      ) : (
       <Loading />
      )}
     </div>
     <div className="grid md:grid-cols-3 gap-5 justify-center w-full bg-white rounded-lg p-5">
      <div className="col-span-1 border-e-2 border-gray-400 ">
       <h1 className="text-center font-bold text-base">Topics - Category</h1>
       <div className="md:flex justify-around items-center mt-5">
        <div className="">
         <p className="flex items-center text-base font-bold">
          <FaCircle className="text-red-500 me-2" /> {t("governance")}
         </p>
         <p className="flex items-center text-base font-bold">
          <FaCircle className="text-yellow-500 me-2" /> {t("environment")}
         </p>
         <p className="flex items-center text-base font-bold">
          <FaCircle className="text-green-500 me-2" /> {t("social")}
         </p>
         <p className="flex items-center text-base font-bold">
          <FaCircle className="text-blue-500 me-2" /> {t("economic")}
         </p>
        </div>

        <div className="mt-5 md:mt-0">
         <DonutChart data={DonutData} />
        </div>
       </div>
      </div>
      <div className="col-span-2">
       <h1 className="text-start font-bold text-base">Top Topics</h1>
       <div>
        {GetDynamicData?.top_5_topics?.map((item, idx) => (
         <p className="flex items-center text-base font-bold" key={idx}>
          {`${idx + 1} - ${t(item)}`}
         </p>
        ))}
        {/* <p className="flex items-center text-base font-bold">
                  2- {t("governance")}
                </p>
                <p className="flex items-center text-base font-bold">
                  3- {t("governance")}
                </p>
                <p className="flex items-center text-base font-bold">
                  4- {t("governance")}
                </p> */}
       </div>
      </div>
     </div>
    </div>
   )}
   {historyOpened && (
    <HistoryPopUp
     opened={historyOpened}
     close={historyClose}
     assessmentType={"Double Materiality"}
    />
   )}
  </>
 );
}
