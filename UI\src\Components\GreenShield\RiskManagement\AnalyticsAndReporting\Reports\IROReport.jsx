import {
  ESGReportingDownloadIcon,
  ESGReportingIROIcon,
} from "@/assets/icons";
import { useEffect, useState } from "react";
import ApiS3 from "@/Api/apiS3";
import { showNotification } from "@mantine/notifications";
import { Spinner } from "@react-pdf-viewer/core";
import ViewPDF from "@/Components/PDF_Viewer";

const IROReport = () => {
  const [loading, setLoading] = useState(false);
  const [pdfUrl, setPdfUrl] = useState(null);
  const [docxUrl, setDocxUrl] = useState(null);


  const fetchAuditControles = async () => {
    try {
      setLoading(true);
      const response = await ApiS3.get(`risk/IRO-report`);
      if (response?.data) {
        setPdfUrl(response.data);
      } else {
        showNotification({
          title: "Error",
          message: "No report URL available",
          color: "red",
        });
      }
    } catch (error) {
      showNotification({
        title: "Error",
        message: "Failed to fetch PDF URL",
        color: "red",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateDocx = async () => {
    try {
      setLoading(true);
      const response = await ApiS3.get(`risk/IRO-reportdocs`);
      if (response?.data) {
        setDocxUrl(response.data);
      } else {
        showNotification({
          title: "Error",
          message: "No DOCX URL available",
          color: "red",
        });
      }
    } catch (error) {
      showNotification({
        title: "Error",
        message: "Failed to fetch DOCX URL",
        color: "red",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadDocx = () => {
    if (!docxUrl) {
      showNotification({
        title: "Error",
        message: "No DOCX URL available",
        color: "red",
      });
      return;
    }
    const link = document.createElement("a");
    link.href = docxUrl;
    link.setAttribute("download", "IRO_Report.docx");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

    useEffect(() => {
    fetchAuditControles();
    handleGenerateDocx();
  }, []);

  return (
    <div className="flex flex-col bg-white w-full py-4 px-6 rounded-2xl">
      <div
        className={`full-center rounded-full h-14 w-14 bg-[#07838F1A] mt-3 mb-2`}
      >
        <ESGReportingIROIcon />
      </div>
      <h2 className="text-[#494949] font-semibold text-xl mb-3">
        IRO Assessment Report
      </h2>
      <hr className="bg-[#07848f62] h-[2px] rounded-3xl my-3" />
      <button
        disabled={loading || !docxUrl}
        onClick={handleDownloadDocx}
        className={`${
          loading || !docxUrl ? "bg-gray-300" : "bg-[#07838F] hover:bg-[#2b6d74]"
        } rounded-full text-white text-bold text-sm py-3 px-5 flex justify-center items-center gap-1 duration-300 my-2`}
      >
        <ESGReportingDownloadIcon /> {loading && <Spinner size={15} />}
        Download Docx
      </button>
      <ViewPDF
        pdfUrl={pdfUrl}
        text="View Report"
        disabled={loading || !pdfUrl}
        btnStyle="border border-[#07838F] rounded-full text-[#07838F] text-bold text-sm py-3 px-5 flex justify-center items-center gap-1 hover:border-[#2b6d74] hover:text-[#2b6d74] duration-300"
      />
    </div>
  );
};

export default IROReport;
