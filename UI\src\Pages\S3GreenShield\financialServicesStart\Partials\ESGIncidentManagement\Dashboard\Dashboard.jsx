import { <PERSON><PERSON><PERSON> } from "@mantine/charts";
import { <PERSON><PERSON>, Pagination } from "@mantine/core";
import { customAlphabet } from "nanoid";
import { FaCalendar} from "react-icons/fa";
import { LuSigma } from "react-icons/lu";
import { MdNotificationsActive } from "react-icons/md";
import IncidentTable from "./Partials/IncidentTable";
import StatisticsCard from "./Partials/StatisticsCard";
import ApiS3 from "@/Api/apiS3";
import _ from "lodash";

import { useState, useEffect } from "react";
// import { totalIncidentData } from "./data";

// icons
import { FaArrowUpLong } from "react-icons/fa6";
import { BiTrash } from "react-icons/bi";
import Loading from "@/Components/Loading";

export default function Dashboard({ filteredData, loading }) {
  const [selection, setSelection] = useState([]);

  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(8);
  const generateNumericID = customAlphabet("0123456789", 8);
  const [statisticsData, setStatisticsData] = useState([]);
  console.log("🚀 ~ statisticsData:", statisticsData)
  const statistics = async () => {
    try {
      const { data } = await ApiS3.get(
        "/stakeholder-interactions/InteractionsInfo"
      );
      if (data) {
        setStatisticsData(data);
        console.log(data);
      }
    } catch (error) {
      console.log(error);
    }
  };
  useEffect(() => {
    statistics();
  }, []);
  const id = generateNumericID();
  const chunkedData = _.chunk(statisticsData, rowsPerPage);
  const totalPages = chunkedData.length || 0;
  const currentData = chunkedData[Math.min(currentPage, totalPages) - 1] || [];

  return (
    <div className="w-full pb-20">
      {/* Statistics Cards */}
      {/* {loading ? (
        <Loading />
      ) : ( */}
      <div className="w-ful grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
        <StatisticsCard
          arrow={<FaArrowUpLong size={12} />}
          icon={
            <LuSigma className="text-5xl text-blue-400 bg-blue-300 rounded-2xl font-bold" />
          }
          title={statisticsData.totalInteractions}
          paragraph={"Total Interactions"}
          className={`h-[155px] shadow-lg rounded-2xl bg-white flex flex-col justify-between p-5`}
        />
        <StatisticsCard
          arrow={<FaArrowUpLong size={12} />}
          icon={
            <div className="bg-[#00C7BE33] p-2 flex justify-center items-center rounded-2xl">
              <FaCalendar className="text-3xl text-[#00C7BE] font-bold" />
            </div>
          }
          title={statisticsData.openInteractions}
          paragraph={"Open Interactions"}
          className={`h-[155px] shadow-lg rounded-2xl bg-white flex flex-col justify-between p-5`}
        />
        <StatisticsCard
          arrow={<FaArrowUpLong size={12} />}
          icon={
            <div className="bg-[#FFCC0033] p-2 flex justify-center items-center rounded-2xl">
              <MdNotificationsActive className="text-3xl text-[#F4B351] font-bold" />
            </div>
          }
          title={statisticsData.closedInteractions}
          paragraph={"Closed Interaction"}
          className={`h-[155px] shadow-lg rounded-2xl bg-white flex flex-col justify-between p-5`}
        />
      </div>

      <div className="bg-[#fff] rounded-lg shadow-lg mt-4 py-2 px-8">
        <p className="mb-2">Total Interactions</p>
        {statisticsData?.monthlyData?.length > 0 && (
          <BarChart
            h={300}
            withBarValueLabel
            valueLabelProps={{ position: "inside", fill: "white" }}
            barProps={{ radius: [50, 50, 0, 0] }}
            data={statisticsData.monthlyData}
            dataKey="month"
            withLegend
            legendProps={{
              verticalAlign: "bottom",
              align: "center",
              height: 50,
            }}
            gridProps={{ style: { stroke: "#CCCCCC", strokeDasharray: "0" } }}
            series={[
              { name: "closedInteractions", color: "#00C0A9" },
              { name: "totalInteractions", color: "#25E7A1" },
            ]}
            tickLine="xy"
            gridAxis="xy"
          />
        )}
      </div>

      {/* Buttons */}
      {/* <div className="mt-14  md:flex justify-between">
        <Button
          className="bg-transparent text-primary border-2 border-primary rounded-lg hover:bg-primary hover:text-white block mx-auto md:mx-0"
          size="md"
        >
          <GrAnalytics className="text-2xl me-1" /> Reporting/Analytics
        </Button>

      </div> */}
      <div className="shadow-lg rounded-2xl bg-white p-4 mt-5">
        <div className="mt-8">
          <IncidentTable filteredData={filteredData} loading={loading} />
        </div>
        <div className="flex justify-between mt-8">

        {isDeleteOpen ? (
            <Button
              disabled={isDeleting}
              title="Confirm Delete Selected"
              className={`font-bold text-xs bg-red-500 text-white hover:bg-red-400
                    hover:text-white px-5 
                    ${
                      isDeleting.length == 0
                        ? "opacity-50 cursor-not-allowed"
                        : "opacity-100"
                    }
                    `}
              // onClick={handleDelete}
              type="submit"
            >
              Confirm Delete Selected {isDeleting && <Loading />}
            </Button>
          ) : (
            <Button
              disabled={selection.length == 0}
              title="Select to delete"
              className={`font-bold text-xs bg-gray-100 text-red-500
                hover:border-red-500 px-2 
                    ${
                      selection.length == 0
                        ? "opacity-50 cursor-not-allowed"
                        : "opacity-100"
                    }
                    `}
              onClick={() => setIsDeleteOpen(true)}
              type="submit"
            >
              <BiTrash size={20} />
            </Button>
          )}
          <Pagination
            color="#05808b"
            value={Math.min(currentData, totalPages)}
            onChange={setCurrentPage}
            total={totalPages}
          />
        </div>
      </div>
    </div>
  );
}
