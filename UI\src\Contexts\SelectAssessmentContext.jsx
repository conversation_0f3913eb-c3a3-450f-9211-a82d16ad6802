import ApiS1Config from "@/Api/apiS1Config";
import iSSBReadinessIcon from "@/assets/images/ISSB.png";
import cSRDReadinessIcon from "@/assets/images/csrd.png";
import DecisionSightIcon from "@/assets/images/decisionSight.png";
import eSGDueDiligenceIon from "@/assets/images/eSGDueDiligence.png";
import greenSightIcon from "@/assets/images/greenSightAI.png";
import humanRightsIcon from "@/assets/images/humanRights.png";
import sDGAlignmentIcon from "@/assets/images/sDGAlignment.png";
import doubleMaterialityIcon from "@/assets/svg/66ff99ad70fb28d809f12cd5_6528a6939ec9911fdd497c6d_impact 1.svg";
import { createContext, useContext, useEffect, useState } from "react";
import { useAuth } from "./AuthContext";

const SelectAssessmentContext = createContext();

export default function SelectAssessmentProvider({ children }) {
  const [summaryType, setSummary] = useState();
  const [activeAssessment, setActiveAssessment] = useState({});
  const [S1CurrentAssessment, setS1CurrentAssessment] = useState();
  const { CompanyAccess } = useAuth();
  const checkProgramStatus = async () => {
    try {
      const { data: checkCurrentAssessment } = await ApiS1Config.get(
        "/check_current_assessment"
      );
      setS1CurrentAssessment(checkCurrentAssessment);
      setActiveAssessment(CompanyAccess["ESG Compass Suite"]);
    } catch (error) {
      //console.log(error);
    }
  };

  useEffect(() => {
    checkProgramStatus();
  }, []);

  const assessmentTypes = [
    {
      title: "GreenSight AI",
      popUp: true,
      quizNum: 29,
      time: 30,
      isActive: activeAssessment["GreenSight AI"] || false,
      havCurrentAssessment:
        (S1CurrentAssessment && S1CurrentAssessment["GreenSight AI"]) || false,
      img: greenSightIcon,
      worked: true,
      path: "/diagnosis/General/question/1",
      assessmentType: "GreenSight AI",
      summaryPath: "/summary",
    },
    {
      title: "ISSB Readiness",
      popUp: true,
      quizNum: 29,
      time: 30,
      isActive: activeAssessment["ISSB Readiness"] || false,
      havCurrentAssessment:
        (S1CurrentAssessment && S1CurrentAssessment["ISSB Readiness"]) || false,
      img: iSSBReadinessIcon,
      worked: false,
      path: "/issb",
      assessmentType: "ISSB Readiness",
      summaryPath: "/issb",
      historyPath: "/issb-report",
    },
    {
      title: "CSRD Readiness",
      popUp: true,
      quizNum: 29,
      time: 30,
      isActive: activeAssessment["CSRD Readiness"] || false,
      havCurrentAssessment:
        (S1CurrentAssessment && S1CurrentAssessment["CSRD Readiness"]) || false,
      img: cSRDReadinessIcon,
      worked: true,
      path: "/csrd-dashboard",
      // modal: ''
      assessmentType: "CSRD Readiness",
      summaryPath: "/csrd-dashboard",
      historyPath: "/csrd-report",
    },
    {
      title: "Double Materiality",
      assessmentType: "Double Materiality",
      popUp: true,
      quizNum: 29,
      time: 30,
      isActive: activeAssessment["Double Materiality"] || false,
      havCurrentAssessment:
        (S1CurrentAssessment && S1CurrentAssessment["Double Materiality"]) ||
        false,
      img: doubleMaterialityIcon,
      worked: true,
      path: "/doubleMateriality",
      // modal: <DoubleMaterialityView />
      summaryPath: "/doubleMateriality",
      historyPath: "/doubleMateriality/Reporting",
    },
    {
      title: "SDG Alignment & Impact Measurement",
      assessmentType: "SDG Assessment",
      popUp: true,
      quizNum: 29,
      time: 30,
      isActive: activeAssessment["SDG Alignment & Impact Measurement"] || false,
      havCurrentAssessment:
        (S1CurrentAssessment && S1CurrentAssessment["SDG Assessment"]) || false,
      img: sDGAlignmentIcon,
      worked: true,
      path: "/sdg",
      summaryPath: "/sdg",
      historyPath: "/sdg/report",
    },
    {
      title: "Human Rights Due Diligence (HRDD)",
      assessmentType: "Human Rights Due Diligence (HRDD)",
      popUp: true,
      quizNum: 29,
      time: 30,
      isActive: activeAssessment["Human Rights Due Diligence (HRDD)"] || false,
      havCurrentAssessment:
        (S1CurrentAssessment &&
          S1CurrentAssessment["Human Rights Due Diligence (HRDD)"]) ||
        false,
      img: humanRightsIcon,
      worked: true,
      path: "/hrdd",
      // modal: ''
      summaryPath: "/hrdd",
      historyPath: "/hrdd-report",
    },
    {
      title: "ESG Due Diligence",
      assessmentType: "ESG Due Diligence",
      popUp: true,
      quizNum: 29,
      time: 30,
      isActive: activeAssessment["ESG Due Diligence"] || false,
      havCurrentAssessment:
        (S1CurrentAssessment && S1CurrentAssessment["ESG Due Diligence"]) ||
        false,
      img: eSGDueDiligenceIon,
      worked: true,
      path: "/due-diligence",
      // modal: ''
      summaryPath: "/due-diligence",
      historyPath: "/dueDiligence-report",
    },
    {
      title: "DecisionSight",
      assessmentType: "DecisionSight",
      popUp: false,
      quizNum: 29,
      time: 30,
      isActive: activeAssessment["DecisionSight"] || false,
      havCurrentAssessment:
        (S1CurrentAssessment && S1CurrentAssessment["DecisionSight"]) || false,
      img: DecisionSightIcon,
      worked: false,
      path: "/DecisionSight/Dashboard",
      // modal: ''
      summaryPath: "/DecisionSight/Dashboard",
    },
  ];

  return (
    <SelectAssessmentContext.Provider
      value={{
        assessmentTypes,
        checkProgramStatus,
        setSummary,
        summaryType,
        activeAssessment,
      }}
    >
      {children}
    </SelectAssessmentContext.Provider>
  );
}

export const useSelectAssessment = () => useContext(SelectAssessmentContext);
