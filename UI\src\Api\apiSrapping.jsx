// https://web-scraping1-staging.azurewebsites.net/
import axios from "axios";
import Cookies from "js-cookie";

const baseURL = "https://web-scraping1-staging.azurewebsites.net";
const ApiScrapping = axios.create({
  baseURL,
  // withCredentials: true, // Send cookies with requests
});

ApiScrapping.interceptors.request.use(
  (config) => {
    const token = Cookies.get("level_user_token");
    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

ApiScrapping.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response && error.response.status === 401) {
      const errorMessage = error.response.data.message.toLowerCase();
      const currentPath = window.location.pathname;

      if (
        (errorMessage.includes("token is expired") ||
          errorMessage.includes("token is invalid")) &&
        currentPath !== "/login"
      ) {
        console.log("Token is invalid. Redirecting to login...");
        Cookies.remove("level_user_token");
        localStorage.clear();
        window.location.href = "/login"; // Redirect to login page
      }
    }
    return Promise.reject(error);
  }
);

export default ApiScrapping;
