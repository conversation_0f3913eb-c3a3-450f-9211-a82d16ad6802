import { Checkbox, rem, ScrollArea, Table } from "@mantine/core";
import { RiDeleteBin6Line } from "react-icons/ri";
import cx from "clsx";
import { useEffect, useState } from "react";
import axios from "axios";
import Cookies from "js-cookie";
import Loading from "@/Components/Loading";

export default function ShowConnectors({ data, loading ,getTableData}) {
 const [selection, setSelection] = useState([]);

 const [DelLoading, setLoading] = useState(false);
 
 useEffect(() => {
  getTableData();
 }, []);

 // دالة لحذف عنصر من الجدول
 const del = async (connector_id) => {
  try {
   setLoading(true);
   const { data } = await axios.delete(
    `https://connectors0.azurewebsites.net/connector/${connector_id}`,
    {
     headers: { Authorization: `Bearer ${Cookies.get("level_user_token")}` },
    }
   );
   setLoading(false);
   // setData(data);
   getTableData();
  } catch (error) {
   setLoading(false);
   console.error("Error fetching data:", error);
  }
 };

 // إنشاء الصفوف بناءً على البيانات
 const rows = data.map((item) => {
  const selected = selection.includes(item.id);
  const date = new Date(item.created_at);

  return (
   <Table.Tr
    key={item.id}
    className={`${cx({
     ["bg-[#07838F1A]"]: selected,
    })} text-sm font-bold text-[#626364] text-center`}
   >
    <Table.Td>
     <span
      className="flex justify-center items-center text-xl cursor-pointer"
      onClick={() => del(item.id)}
     >
      <RiDeleteBin6Line />
     </span>
    </Table.Td>
    {/* <Table.Td>
     <Checkbox
      checked={selected}
      onChange={() => toggleRow(item.id)}
      color="#07838F"
     />
    </Table.Td> */}
    <Table.Td>{item.client}</Table.Td>
    <Table.Td>{item.name}</Table.Td>
    <Table.Td>{`${date.getDate()}/${
     date.getMonth() + 1
    }/${date.getFullYear()}`}</Table.Td>
    <Table.Td>{item.created_by}</Table.Td>
    {/* <Table.Td>{item.usage}</Table.Td>
    <Table.Td>{item.action}</Table.Td> */}
   </Table.Tr>
  );
 });

 return (
  <>
   {loading || DelLoading ? (
    <Loading />
   ) : (
    <ScrollArea>
     <Table className="p-2 my-1 bg-white shadow-lg rounded-xl">
      <Table.Thead className="border-b-2 border pb-6 text-secondary-500 font-bold text-base text-center">
       <Table.Tr>
        <Table.Th className="text-center">Delete</Table.Th>
        {/* <Table.Th style={{ width: rem(40) }}>
         <Checkbox
          onChange={toggleAll}
          checked={selection.length === data.length}
          indeterminate={
           selection.length > 0 && selection.length !== data.length
          }
          color="#07838F"
         />
        </Table.Th> */}
        <Table.Th className="text-center">Name</Table.Th>
        <Table.Th className="text-center">Type</Table.Th>
        <Table.Th className="text-center">Date Added</Table.Th>
        <Table.Th className="text-center">Added by</Table.Th>
        {/* <Table.Th className="text-center">Usage</Table.Th> 
        <Table.Th className="text-center">Actions</Table.Th> */}
       </Table.Tr>
      </Table.Thead>
      <Table.Tbody>{rows}</Table.Tbody>
     </Table>
    </ScrollArea>
   )}
  </>
 );
}
