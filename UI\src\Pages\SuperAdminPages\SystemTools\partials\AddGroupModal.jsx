import ApiProfile from "@/Api/apiProfileConfig";
import Loading from "@/Components/Loading";
import { Button, Modal, TextInput } from "@mantine/core";
import { isNotEmpty, useForm } from "@mantine/form";
import { showNotification } from "@mantine/notifications";
import { useState } from "react";

const AddGroupModal = ({ close, opened, refreshGroups }) => {
  const [loading, setLoading] = useState(false);

  const form = useForm({
    initialValues: {
      name: "",
    },
    validate: {
      name: isNotEmpty("Group Name is required"),
    },
  });

  const handleSubmit = async (values) => {
    try {
      setLoading(true);
      // Use the provided API endpoint
      const { data } = await ApiProfile.post(
        "https://portal-auth-main.azurewebsites.net/admin/component-groups",
        values
      );
      showNotification({
        message: data.message || "Group added successfully",
        color: "green",
      });
      form.reset();
      refreshGroups();
      close();
    } catch (error) {
      error.response?.data.message &&
        showNotification({
          message: error.response?.data.message,
          color: "red",
        });
      error.response?.data.error &&
        showNotification({
          message: error.response?.data.error,
          color: "red",
        });
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal opened={opened} onClose={close} title="Add New Group" centered>
      <form onSubmit={form.onSubmit(handleSubmit)}>
        <TextInput
          {...form.getInputProps("name")}
          placeholder="Enter Group Name..."
          label="Group Name"
          className="mb-4"
          required
        />
        <Button
          type="submit"
          className="bg-primary hover:bg-primary mt-3 w-full"
          disabled={loading}
        >
          {loading ? <Loading /> : "Submit"}
        </Button>
      </form>
    </Modal>
  );
};

export default AddGroupModal;