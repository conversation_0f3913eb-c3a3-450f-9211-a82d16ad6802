import { useEffect, useState } from "react";
import { Button, FileInput, TextInput } from "@mantine/core";

const EvidenceEditor = ({
    evidences: evidencesProp,
    onAddEvidence,
    onDeleteEvidence,
}) => {
    const [evidences, setEvidences] = useState(evidencesProp);
    const [file, setFile] = useState(null);
    const [url, setUrl] = useState("");
    const [name, setName] = useState("");

    useEffect(() => {
        setEvidences(evidencesProp);
    }, [evidencesProp]);

    const handleFileChange = (newFiles) => {
        setFile(newFiles);
    };

    const handleUrlChange = (event) => {
        setUrl(event.target.value);
    };

    const handleNameChange = (event) => {
        setName(event.target.value);
    };

    const handleSubmit = () => {
        if (file || url) {
            const evidenceData = {
                name: name || file?.name || url,
                url,
                tagged_user_ids: [],
            };
            onAddEvidence(evidenceData);
            setFile(null);
            setUrl("");
            setName("");
        }
    };

    return (
        <div className="flex flex-wrap gap-4">
            <TextInput
                radius="md"
                placeholder="Enter Name"
                value={name}
                className="min-w-56 flex-grow"
                onChange={handleNameChange}
            />
            <TextInput
                radius="md"
                placeholder="Enter URL"
                value={url}
                className="min-w-56 flex-grow"
                onChange={handleUrlChange}
            />
            <FileInput
                clearable
                value={file}
                onChange={handleFileChange}
                placeholder="Upload Evidence"
                className="w-full"
                classNames={{ input: "bg-gray-200 text-center rounded-full" }}
            />
            <Button onClick={handleSubmit} radius="xl" className="w-full">
                Add
            </Button>

            <div className="flex flex-wrap items-center w-full gap-2 h-16 overflow-hidden overflow-y-auto">
                {evidences
                    .filter((evidence) => evidence != null)
                    .map((evidence, index) => (
                        <div
                            key={index}
                            className="flex w-fit gap-4 rounded-full px-3 items-center bg-gray-300"
                        >
                            <a href={evidence.url}>{evidence.name}</a>
                            <button
                                onClick={() => onDeleteEvidence(evidence.id)}
                                className="w-3 relative aspect-square group"
                            >
                                <span className="w-full h-0.5 rounded-full bg-black group-hover:rotate-0 rotate-45 transition-all duration-200 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
                                <span className="w-full h-0.5 rounded-full bg-black group-hover:rotate-0 -rotate-45 transition-all duration-200 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
                            </button>
                        </div>
                    ))}
            </div>
        </div>
    );
};

export default EvidenceEditor;
