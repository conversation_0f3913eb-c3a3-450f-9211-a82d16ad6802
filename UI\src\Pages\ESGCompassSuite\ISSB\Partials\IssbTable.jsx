import ApiS1Config from "@/Api/apiS1Config";
import Loading from "@/Components/Loading";
import useTableSearchingAndSorting from "@/hooks/useTableSearchingAndSorting";
import {
  Button,
  Checkbox,
  FileInput,
  Select,
  Table,
  TagsInput,
  TextInput,
  Textarea,
  Tooltip,
  Modal,
} from "@mantine/core";
import { showNotification } from "@mantine/notifications";
import cx from "clsx";
import React, { useEffect, useState } from "react";
import { CiSearch } from "react-icons/ci";
import { FaArrowUp } from "react-icons/fa";
import { FaArrowDown } from "react-icons/fa6";
import { IoIosCheckmarkCircleOutline } from "react-icons/io";
import { IoCloseCircleOutline, IoLink } from "react-icons/io5";
import { MdModeEdit, MdOutlineFileDownload } from "react-icons/md";
import ISSBMultiselectFilter from "./ISSBMultiselectFilter";

const IssbTable = ({
  getISSBData,
  prioritySelectColorMap,
  readinessColorMap,
  selectedScope,
  PriorityLevels,
  ReadinessLevel,
  postAnswers,
  postLoading,
  reportLoading,
  getReport,
}) => {
  const {
    selection,
    sortedData,
    search,
    reverseSortDirection,
    edit,
    value,
    toggleRow,
    toggleAll,
    handleSearchChange,
    handleSort,
    setEdit,
    setValue,
    setReverseSortDirection,
  } = useTableSearchingAndSorting(selectedScope);
  const [selectedReadinessLevel, setSelectedReadinessLevel] = useState({});
  const [selectedPriorityState, setSelectedPriorityState] = useState({});
  const [evidenceUrls, setEvidenceUrls] = useState({});
  const [evidenceFile, setEvidenceFile] = useState({});
  const [evidences, setEvidence] = useState({});
  const [backEndEvidences, setBackEndEvidence] = useState({});
  const [uploadLoading, setUploadLoading] = useState({});
  const [actionItems, setActionItems] = useState({});
  const [Owner, setOwner] = useState({});
  const [DueDate, setDueDate] = useState({});
  const [tags, setTags] = useState({});
  const [modalRowId, setModalRowId] = useState(null);

  useEffect(() => {
    getISSBData();
  }, []);
  useEffect(() => {
    sortedData?.map((item) =>
      item?.questions?.map((question, idx) => {
        question?.evidence &&
          setBackEndEvidence((prev) => ({
            ...prev,
            [`${item.id}-${idx}`]: question?.evidence[0],
          }));
      })
    );
  }, [sortedData]);

  const updateEditState = () => {
    const newEditState = selection.reduce((acc, rowId) => {
      acc[rowId] = true;
      return acc;
    }, {});
    setEdit((prevEditState) => ({
      ...prevEditState,
      ...newEditState,
    }));
  };

  let DueDateError = false;
  let actionItemsError = false;
  const actionItemsKey = Object.keys(actionItems);
  const DueDateKey = Object.keys(DueDate);

  actionItemsKey.forEach((key) => {
    if (DueDateKey.includes(key)) {
      const length1 = actionItems[key]?.length;
      const length2 = DueDate[key]?.length;
      if (length1 === length2) {
        DueDateError = false;
      } else {
        DueDateError = true;
      }
    } else {
      DueDateError = true;
    }
  });
  DueDateKey.forEach((key) => {
    if (actionItemsKey.includes(key)) {
      const length1 = actionItems[key]?.length;
      const length2 = DueDate[key]?.length;
      if (length2 === length1) {
        actionItemsError = false;
      } else {
        actionItemsError = true;
      }
    } else {
      actionItemsError = true;
    }
  });

  const handleSelectReadinessChange = (id, value) => {
    setSelectedReadinessLevel((prev) => ({ ...prev, [id]: value }));
  };
  const handleSelectPriorityChange = (rowId, value) => {
    setSelectedPriorityState((prev) => ({ ...prev, [rowId]: value }));
  };
  const handleFileInputChange = (rowId, file, type) => {
    if (type === "delete") {
      setEvidenceFile((prev) => {
        const updatedFiles = { ...prev };
        delete updatedFiles[rowId];
        return updatedFiles;
      });
      setEvidence((prev) => {
        const updatedFiles = { ...prev };
        delete updatedFiles[rowId];
        return updatedFiles;
      });
      setBackEndEvidence((prev) => {
        const updatedFiles = { ...prev };
        delete updatedFiles[rowId];
        return updatedFiles;
      });
    } else {
      setEvidenceFile((prev) => ({ ...prev, [rowId]: file }));
    }
  };
  const handleURLInputChange = (rowId, file) => {
    setEvidenceUrls((prev) => ({ ...prev, [rowId]: file }));
  };
  const handleActionItemsInputChange = (rowId, value) => {
    setActionItems((prev) => ({ ...prev, [rowId]: value }));
  };
  const handleTagsChange = (rowId, tags) => {
    setTags((prev) => ({ ...prev, [rowId]: tags }));
  };
  const handleOwnerChange = (rowId, tags) => {
    setOwner((prev) => ({ ...prev, [rowId]: tags }));
  };
  const handleDueDateChange = (rowId, tags) => {
    setDueDate((prev) => ({ ...prev, [rowId]: tags }));
  };
  const uploadEvidence = async (rowId) => {
    setUploadLoading((prev) => ({ ...prev, [rowId]: true }));
    const formData = new FormData();

    if (evidenceFile[rowId]) {
      if (Array.isArray(evidenceFile[rowId])) {
        evidenceFile[rowId].forEach((file) =>
          formData.append("evidence_files", file)
        );
      } else {
        formData.append("evidence_files", evidenceFile[rowId]);
      }
    }

    if (evidenceFile[rowId]) {
      try {
        let { data: UploadedFile } = await ApiS1Config.post(
          "upload_evidence_files",
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );
        setUploadLoading((prev) => ({ ...prev, [rowId]: false }));
        showNotification({
          message: "File uploaded successfully",
          color: "green",
        });
        const fileArray = Object.entries(UploadedFile).map(([name, url]) => ({
          name,
          url,
        }));
        setEvidence((prev) => ({ ...prev, [rowId]: fileArray }));
      } catch (error) {
        setUploadLoading((prev) => ({ ...prev, [rowId]: false }));
        showNotification({
          message: "File not uploaded",
          color: "red",
        });
      }
    }
  };
  const getQuestionByRowId = (rowId) => {
    if (!rowId) return null;
    const [topicId, questionIdx] = rowId.split("-");
    const topic = sortedData.find((item) => item.id === topicId);
    return topic?.questions[parseInt(questionIdx)];
  };
  const collectData = () => {
    const data = {
      name: selectedScope.name,
      solved: selectedScope.solved,
      title: selectedScope.title,
      topics: selectedScope.topics.map((item) => {
        return {
          id: item.id,
          name: item.name,
          questions: item.questions.map((question, idx) => {
            const readinessLevelEntry = Object.entries(ReadinessLevel).find(
              ([key, val]) =>
                val === selectedReadinessLevel[`${item.id}-${idx}`]
            );
            const readinessLevel = readinessLevelEntry
              ? Number(readinessLevelEntry[0])
              : null;
            const selectedPriorityEntry = Object.entries(PriorityLevels).find(
              ([key, val]) => val === selectedPriorityState[`${item.id}-${idx}`]
            );
            const selectedPriority = selectedPriorityEntry
              ? Number(selectedPriorityEntry[0])
              : null;

            return {
              id: question.id,
              questionText: question.questionText,
              CSRDReference: question.CSRDReference,
              readinessLevel:
                readinessLevel === null
                  ? question.readinessLevel
                  : readinessLevel,
              evidence: [
                !evidences[`${item.id}-${idx}`]
                  ? (question.evidence && question.evidence[0]) || null
                  : evidences[`${item.id}-${idx}`],
                !evidenceUrls[`${item.id}-${idx}`]
                  ? (question.evidence && question.evidence[1]) || null
                  : evidenceUrls[`${item.id}-${idx}`],
              ],
              priority:
                selectedPriority === null
                  ? question.priority
                  : selectedPriority,
              actionItems: !actionItems[`${item.id}-${idx}`]
                ? question.actionItems
                : actionItems[`${item.id}-${idx}`],
              owner: !Owner[`${item.id}-${idx}`]
                ? question.owner
                : Owner[`${item.id}-${idx}`],
              date: !DueDate[`${item.id}-${idx}`]
                ? question.date
                : DueDate[`${item.id}-${idx}`],
              tags: tags[`${item.id}-${idx}`] || [],
            };
          }),
        };
      }),
      url: selectedScope.url,
    };
    postAnswers(data);
  };

  const rows = sortedData?.map((item) =>
    item.questions.map((question, idx) => {
      const selected = selection?.includes(item.id);
      const rowId = `${item.id}-${idx}`;
      const isFirstQuestion = idx === 0;
      const isDisabled = question.solved === true;

      return (
        <React.Fragment key={rowId}>
          <Table.Tr className={`${cx({ ["bg-[#07838F1A]"]: selected })}`}>
            {isFirstQuestion && (
              <Table.Td rowSpan={item.questions.length}>
                <Checkbox
                  checked={selected}
                  onChange={() => toggleRow(item.id)}
                  color="#07838F"
                />
              </Table.Td>
            )}
            {isFirstQuestion && (
              <Table.Td rowSpan={item.questions.length}>
                <p className="text-left w-36">{item.name}</p>
              </Table.Td>
            )}
            <Table.Td className="text-center">
              <p
                className={`w-80 text-left mx-auto ${
                  !edit && isDisabled ? "cursor-not-allowed opacity-50" : ""
                }`}
              >
                {question.questionText}
              </p>
            </Table.Td>
            <Table.Td>
              <div className="w-52 mx-auto">
                <Select
                  value={
                    selectedReadinessLevel[rowId] ||
                    ReadinessLevel[question.readinessLevel]
                  }
                  disabled={!edit && isDisabled}
                  onChange={(value) =>
                    handleSelectReadinessChange(rowId, value)
                  }
                  classNames={{ root: "w-fit", input: "text-center" }}
                  radius="xl"
                  size="xs"
                  className="w-full"
                  withCheckIcon={false}
                  allowDeselect={false}
                  rightSectionWidth="0"
                  placeholder="Select your level"
                  data={Object.keys(readinessColorMap)}
                  styles={(theme) => {
                    const readinessLevelValue =
                      selectedReadinessLevel[rowId] ||
                      ReadinessLevel[question.readinessLevel];
                    const readinessStyles = readinessColorMap[
                      readinessLevelValue
                    ] || {
                      bg: theme.colors.gray[0],
                      text: "black",
                      border: theme.colors.gray[3],
                    };
                    return {
                      input: {
                        backgroundColor: readinessStyles.bg,
                        color: readinessStyles.text,
                        border: `1px solid ${readinessStyles.border}`,
                        padding: "16px 12px",
                        borderRadius: "15px",
                        fontSize: "14px",
                        fontWeight: "500",
                        boxShadow: `0 2px 4px rgba(0, 0, 0, 0.1)`,
                      },
                    };
                  }}
                />
              </div>
            </Table.Td>
            <Table.Td
              className={`w-1/5 text-center mx-auto ${
                !value.includes("Evidence") ? "hidden" : ""
              } ${!edit && isDisabled ? "cursor-not-allowed opacity-50" : ""}`}
            >
              <div
                className={`w-56 text-center mx-auto ${
                  !edit && isDisabled ? "cursor-not-allowed opacity-50" : ""
                }`}
              >
                {!backEndEvidences[rowId] &&
                !evidenceFile[rowId] &&
                !evidenceFile[rowId]?.length ? (
                  <FileInput
                    classNames={{ root: "w-fit", input: "px-12" }}
                    leftSection={<MdOutlineFileDownload className="w-5 h-5" />}
                    variant="unstyled"
                    placeholder="Upload evidence"
                    className="w-full bg-[#F2F2F2] mb-3"
                    radius="md"
                    leftSectionPointerEvents="none"
                    disabled={!edit && isDisabled}
                    onChange={(file) => handleFileInputChange(rowId, file)}
                    multiple
                  />
                ) : (
                  <div className="flex items-center justify-around mb-1">
                    <div className="text-start w-10/12">
                      {(backEndEvidences[rowId] &&
                        backEndEvidences[rowId]?.map((item, indx) => (
                          <Button
                            key={indx}
                            variant="subtle"
                            href={item?.url}
                            target="_blank"
                            component="a"
                            className="w-full py-0"
                          >
                            <p key={indx} className="truncate py-0">
                              {item.name}
                            </p>
                          </Button>
                        ))) ||
                        evidenceFile[rowId]?.map((item, indx) => (
                          <p key={indx} className="truncate">
                            {item.name}
                          </p>
                        ))}
                    </div>
                    <div className="flex">
                      {uploadLoading[rowId] ? (
                        <Loading />
                      ) : (
                        <>
                          <IoCloseCircleOutline
                            className={`text-red-600 ${
                              !edit && isDisabled
                                ? "cursor-not-allowed"
                                : "cursor-pointer"
                            }`}
                            onClick={() => {
                              if (!edit && isDisabled) return;
                              if (
                                evidenceFile[rowId] ||
                                backEndEvidences[rowId]
                              )
                                handleFileInputChange(rowId, [], "delete");
                            }}
                          />
                          {evidenceFile[rowId] && !evidences[rowId] && (
                            <IoIosCheckmarkCircleOutline
                              className={`text-green-600 ${
                                !edit && isDisabled
                                  ? "cursor-not-allowed"
                                  : "cursor-pointer"
                              }`}
                              onClick={() => uploadEvidence(rowId)}
                            />
                          )}
                        </>
                      )}
                    </div>
                  </div>
                )}
                <TextInput
                  classNames={{ root: "w-fit", input: "ps-8 pe-2" }}
                  leftSection={<IoLink className="w-5 h-5" />}
                  variant="unstyled"
                  placeholder="enter an URL"
                  className="w-full bg-[#e3f0fd]"
                  radius="md"
                  leftSectionPointerEvents="none"
                  disabled={!edit && isDisabled}
                  onChange={(e) => handleURLInputChange(rowId, e.target.value)}
                  defaultValue={question?.evidence && question?.evidence[1]}
                />
              </div>
            </Table.Td>
            <Table.Td>
              <div className="w-38 mx-auto">
                <Select
                  disabled={!edit && isDisabled}
                  value={selectedPriorityState[rowId]}
                  onChange={(value) => handleSelectPriorityChange(rowId, value)}
                  classNames={{ root: "w-fit", input: "text-center" }}
                  radius="xl"
                  size="xs"
                  className="w-full"
                  withCheckIcon={false}
                  allowDeselect={false}
                  rightSectionWidth="0"
                  placeholder="Priority"
                  data={Object.keys(prioritySelectColorMap)}
                  styles={(theme) => {
                    const priorityLevelValue =
                      selectedPriorityState[rowId] ||
                      PriorityLevels[question.priority];
                    const priorityStyles = prioritySelectColorMap[
                      priorityLevelValue
                    ] || {
                      bg: "rgba(0, 0, 0, 0.1)",
                      text: "black",
                    };
                    return {
                      input: {
                        backgroundColor: priorityStyles.bg,
                        color: priorityStyles.text,
                        fontWeight: "500",
                        border: "none",
                        padding: "16px 12px",
                        borderRadius: "15px",
                        fontSize: "14px",
                        boxShadow: `0 2px 4px rgba(0, 0, 0, 0.1)`,
                      },
                    };
                  }}
                />
              </div>
            </Table.Td>
            <Table.Td>
              <div className="w-52 mx-auto">
                <p
                  className={`text-center ${
                    !edit && isDisabled ? "cursor-not-allowed opacity-50" : ""
                  }`}
                >
                  {question.CSRDReference}
                </p>
              </div>
            </Table.Td>
            <Table.Td>
              <Button
                onClick={() => setModalRowId(rowId)}
                color="#07838F"
                className="w-full"
              >
                +
              </Button>
            </Table.Td>
          </Table.Tr>
        </React.Fragment>
      );
    })
  );

  return (
    <>
      {!search && !sortedData?.length ? (
        <Loading />
      ) : (
        <>
          <h1 className="hidden mb-3 text-center capitalize md:block">
            To scroll Right and left Hold Shift and Scroll using your mouse
          </h1>
          <div className="p-2 my-1 shadow-lg grid items-center xl:justify-between px-4 bg-white xl:grid-cols-3 rounded-lg w-full">
            <div className="xl:col-span-1 w-full flex justify-center xl:justify-start">
              <Button
                className="text-black bg-transparent hover:bg-transparent hover:text-black border border-gray-600 w-full xl:w-auto"
                onClick={updateEditState}
              >
                <MdModeEdit className="me-1" />
                Edit
              </Button>
            </div>
            <div className="xl:col-span-2 grid items-start justify-center grid-cols-1 lg:grid-cols-3 xl:grid-cols-4 sm:items-center w-full lg:gap-5">
              <div className="md:col-span-1 hidden xl:flex justify-center items-center m-3 p-2 rounded-lg shadow-sm ms-auto"></div>
              <div className="md:col-span-1 m-3 bg-[#EBEBEB] rounded-lg shadow-sm md:ms-auto mx-auto w-full">
                <ISSBMultiselectFilter setValue={setValue} value={value} />
              </div>
              <TextInput
                className="w-full col-span-2"
                placeholder="Search by Topic Area or Assessment Question"
                rightSection={<CiSearch className="w-5 h-5" />}
                value={search}
                onChange={handleSearchChange}
              />
            </div>
          </div>
          {search && sortedData?.length === 0 ? (
            <h1 className="mt-5 text-center capitalize">
              Your Search is not Found
            </h1>
          ) : (
            <div className="p-2 my-1 bg-white shadow-lg rounded-xl">
              <Table.ScrollContainer
                className="scrollable-container"
                maw={"99%"}
              >
                <Table
                  verticalSpacing="sm"
                  horizontalSpacing={""}
                  className="scrollable-container"
                >
                  <Table.Thead className="pb-6 text-base font-thin">
                    <Table.Tr className="text-secondary-500">
                      <Table.Th>
                        <Checkbox
                          onChange={toggleAll}
                          checked={
                            selection.length === selectedScope?.topics.length
                          }
                          indeterminate={
                            selection.length > 0 &&
                            selection.length !== selectedScope?.topics.length
                          }
                          color="#07838F"
                        />
                      </Table.Th>
                      <Table.Th
                        className="text-center cursor-pointer"
                        onClick={() => {
                          handleSort("name");
                          setReverseSortDirection((prev) => !prev);
                        }}
                      >
                        <h1 className="flex items-center justify-center gap-3 ms-4">
                          Topic Area
                          {reverseSortDirection === true ? (
                            <FaArrowUp className="ms-1 text-[#00C0A9]" />
                          ) : (
                            <FaArrowDown className="ms-1 text-[#00C0A9]" />
                          )}
                        </h1>
                      </Table.Th>
                      <Table.Th
                        className="w-1/4 text-center cursor-pointer"
                        onClick={() => {
                          handleSort("questionText");
                          setReverseSortDirection((prev) => !prev);
                        }}
                      >
                        <h1 className="flex items-center justify-left gap-3 ms-4">
                          Assessment Question
                          {reverseSortDirection === true ? (
                            <FaArrowUp className="ms-1 text-[#00C0A9]" />
                          ) : (
                            <FaArrowDown className="ms-1 text-[#00C0A9]" />
                          )}
                        </h1>
                      </Table.Th>
                      <Table.Th className="w-1/5 text-center">
                        <h1 className="flex items-center justify-center gap-3 ms-4">
                          Assessment Level
                          <FaArrowDown className="ms-1 text-[#00C0A9]" />
                        </h1>
                      </Table.Th>
                      <Table.Th
                        className={`w-1/5 text-center ${
                          !value.includes("Evidence") ? "hidden" : ""
                        }`}
                      >
                        <h1 className="flex items-center justify-center gap-3 ms-4">
                          Evidence/Notes
                          <FaArrowDown className="ms-1 text-[#00C0A9]" />
                        </h1>
                      </Table.Th>
                      <Table.Th className="w-1/5 text-center">
                        <h1 className="flex items-center justify-center gap-3 ms-4">
                          Priority
                          <FaArrowDown className="ms-1 text-[#00C0A9]" />
                        </h1>
                      </Table.Th>
                      <Table.Th className="w-1/5 text-center">
                        <h1 className="flex items-center justify-center gap-3 ms-4">
                          ISSB Reference
                          <FaArrowDown className="ms-1 text-[#00C0A9]" />
                        </h1>
                      </Table.Th>
                      <Table.Th className="w-1/5 text-center">
                        <h1 className="flex items-center justify-center">
                          Tags
                        </h1>
                      </Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody className="text-base font-semibold text-gray-600">
                    {rows}
                  </Table.Tbody>
                </Table>
              </Table.ScrollContainer>
              {modalRowId && (
                <Modal
                  opened={!!modalRowId}
                  onClose={() => setModalRowId(null)}
                  title="Edit Details"
                >
                  {(() => {
                    const question = getQuestionByRowId(modalRowId);
                    if (!question) return null;
                    const isDisabled = question.solved === true;
                    const actionItemsValue =
                      actionItems[modalRowId] || question.actionItems || [];
                    const dueDateValue =
                      DueDate[modalRowId] || question.date || [];
                    const isValid =
                      actionItemsValue.length === dueDateValue.length;

                    return (
                      <>
                        <TagsInput
                          label="Action Items"
                          placeholder="Press Enter to add item"
                          value={actionItemsValue}
                          onChange={(tags) =>
                            handleActionItemsInputChange(modalRowId, tags)
                          }
                          disabled={!edit && isDisabled}
                          classNames={{ input: "w-full" }}
                          clearable
                          className={
                            !isValid ? "border-2 border-red-700 rounded-md" : ""
                          }
                        />
                        {!isValid && (
                          <p className="text-xs text-red-500">
                            The number of action items must equal the number of
                            due dates.
                          </p>
                        )}
                        <TagsInput
                          label="Tag Colleagues"
                          placeholder="Press Enter to add a tag"
                          value={tags[modalRowId] || question.tags || []}
                          onChange={(tags) =>
                            handleTagsChange(modalRowId, tags)
                          }
                          disabled={!edit && isDisabled}
                          classNames={{ input: "w-full" }}
                        />
                        <Textarea
                          label="Owner"
                          placeholder="Enter Owner"
                          value={Owner[modalRowId] || question.owner || ""}
                          onChange={(e) =>
                            handleOwnerChange(modalRowId, e.target.value)
                          }
                          disabled={!edit && isDisabled}
                          classNames={{ input: "w-full" }}
                        />
                        <TagsInput
                          label="Due Date"
                          placeholder="Press Enter to add item"
                          value={dueDateValue}
                          onChange={(tags) =>
                            handleDueDateChange(modalRowId, tags)
                          }
                          disabled={!edit && isDisabled}
                          classNames={{ input: "w-full" }}
                          clearable
                          className={
                            !isValid ? "border-2 border-red-700 rounded-md" : ""
                          }
                        />
                        {!isValid && (
                          <p className="text-xs text-red-500">
                            The number of due dates must equal the number of
                            action items.
                          </p>
                        )}
                      </>
                    );
                  })()}
                </Modal>
              )}
              <div className="flex items-center justify-end gap-5">
                <Tooltip
                  multiline
                  w={200}
                  radius={"md"}
                  withArrow
                  transitionProps={{ duration: 200 }}
                  label={
                    !selectedScope?.solved ? (
                      <span className="capitalize flex justify-center text-center">
                        Complete Assessment First
                      </span>
                    ) : null
                  }
                  className={!selectedScope?.solved ? "" : "hidden"}
                >
                  <Button
                    className={`text-white rounded-md bg-primary ${
                      DueDateError || actionItemsError || !selectedScope?.solved
                        ? "cursor-not-allowed opacity-50"
                        : "hover:bg-primary hover:opacity-90"
                    }`}
                    size="sm"
                    disabled={
                      DueDateError || actionItemsError || !selectedScope?.solved
                    }
                    onClick={reportLoading ? "" : () => getReport()}
                  >
                    {reportLoading ? <Loading /> : "Assess"}
                  </Button>
                </Tooltip>
                <Tooltip
                  multiline
                  w={220}
                  radius={"md"}
                  withArrow
                  transitionProps={{ duration: 200 }}
                  label={
                    DueDateError || actionItemsError ? (
                      <span className="capitalize flex justify-center">
                        the count of action items must equal Due Date items.
                      </span>
                    ) : null
                  }
                  className={DueDateError || actionItemsError ? "" : "hidden"}
                >
                  <Button
                    className={`text-white bg-primary hover:bg-primary ${
                      DueDateError || actionItemsError || selectedScope?.solved
                        ? "cursor-not-allowed opacity-50"
                        : ""
                    }`}
                    disabled={
                      DueDateError || actionItemsError || selectedScope?.solved
                    }
                    onClick={postLoading ? "" : () => collectData()}
                  >
                    {postLoading ? <Loading /> : "Save"}
                  </Button>
                </Tooltip>
              </div>
            </div>
          )}
        </>
      )}
    </>
  );
};

export default IssbTable;
