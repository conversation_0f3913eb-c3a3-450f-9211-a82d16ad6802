import { useState } from "react";
import { Button, Select, NumberInput, TextInput } from "@mantine/core";

const PropertyGroup = ({ group, onRemove, onChange }) => {
  const selectOptions = {
    regions: [
      "Northeast US",
      "Southeast US",
      "Midwest US",
      "West US",
      "Europe",
      "Asia Pacific",
    ],
    buildingAges: ["< 5 Years", "5-15 Years", "15-30 Years", "30-50 Years", "> 50 Years"],
    heatingTypes: ["Natural Gas", "Electricity", "Oil", "Heat Pump", "District Heating"],
    dataSources: [
      "Actual Energy Bills (Score 1)",
      "EPC Rating (Score 2)",
      "Building Characteristics (Score 3-4)",
      "Regional Averages (Score 5)",
    ],
  };

  const energyRatings = [
    { label: "A", color: "#04AF2A" },
    { label: "B", color: "#0AD738" },
    { label: "C", color: "#14EB44" },
    { label: "D", color: "#FBB90D" },
    { label: "E", color: "#FF9500" },
    { label: "F", color: "#FF3B30" },
    { label: "G", color: "#D71616" },
  ];

  const inputFields = [
    { label: "Number of Properties", field: "vehicleType", placeholder: "e.g., 5000" },
    { label: "Total Outstanding ($M)", field: "totalOutstanding", placeholder: "e.g., 5000" },
    { label: "Avg Property Value ($)", field: "avgPropertyValue", placeholder: "e.g., 25.5" },
    {
      label: "Avg Property Size (m²)",
      field: "avgPropertySize",
      placeholder: "e.g., 50",
      description: "For EVIC calculation",
    },
  ];

  return (
    <div className="border border-[#E8E7EA] p-4 rounded-[11px]">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg text-[#272727] font-semibold">{group.name}</h3>
        <Button
          onClick={() => onRemove(group.id)}
          className="bg-[#FFE9E9] text-[#EA0B0B] hover:bg-[#EA0B0B] hover:text-white px-3 py-1 rounded-md"
        >
          Remove
        </Button>
      </div>
      <div className="grid grid-cols-5 gap-4 mb-3">
        {inputFields.map(({ label, field, placeholder, description }) => (
          <TextInput
            key={field}
            label={label}
            value={group[field] || ""}
            onChange={(e) => onChange(group.id, field, e.target.value)}
            placeholder={placeholder}
            description={description}
            inputWrapperOrder={description ? ["label", "input", "description"] : undefined}
          />
        ))}
        <Select
          label="Primary Region"
          placeholder="Select Region"
          data={selectOptions.regions}
          value={group.primaryRegion || ""}
          onChange={(value) => onChange(group.id, "primaryRegion", value)}
        />
      </div>
      <div className="grid grid-cols-3 gap-4">
        <Select
          label="Avg Building Age"
          placeholder="Select age range"
          data={selectOptions.buildingAges}
          value={group.avgBuildingAge || ""}
          onChange={(value) => onChange(group.id, "avgBuildingAge", value)}
        />
        <Select
          label="Heating Type"
          placeholder="Select heating type"
          data={selectOptions.heatingTypes}
          value={group.heatingType || ""}
          onChange={(value) => onChange(group.id, "heatingType", value)}
        />
        <Select
          label="Data Source"
          placeholder="Select source"
          data={selectOptions.dataSources}
          value={group.dataSource || ""}
          onChange={(value) => onChange(group.id, "dataSource", value)}
        />
      </div>
      <div className="flex flex-col gap-2 mt-4">
        <h3 className="text-xl text-[#272727]">Energy Performance Distribution (if known)</h3>
        <div className="flex gap-4">
          {energyRatings.map(({ label, color }) => (
            <div key={label} className={`p-3 rounded-md`} style={{ backgroundColor: color }}>
              <label className="text-[#FFFFFF]">{label}</label>
              <NumberInput
                placeholder="%"
                className="rounded-md"
                value={group[`energyRating${label}`] || ""}
                onChange={(value) => onChange(group.id, `energyRating${label}`, value)}
              />
            </div>
          ))}
        </div>
        <p className="text-sm text-[#8D8D8D]">
          Leave blank if unknown - will use regional averages
        </p>
      </div>
    </div>
  );
};

const ResidentialProperties = () => {
  const [familyHouse, setFamilyHouse] = useState([
    {
      id: 1,
      name: "Single Family Home 1",
    },
  ]);

  const addGroup = () => {
    const newGroup = {
      id: familyHouse.length + 1,
      name: `Single Family Home ${familyHouse.length + 1}`,
    };
    setFamilyHouse([...familyHouse, newGroup]);
  };

  const removeGroup = (id) => {
    setFamilyHouse(familyHouse.filter((group) => group.id !== id));
  };

  const handleInputChange = (id, field, value) => {
    setFamilyHouse(
      familyHouse.map((group) =>
        group.id === id ? { ...group, [field]: value } : group
      )
    );
  };

  return (
    <div className="bg-[#FFFFFF] p-4 rounded-[10px] border border-[#E8E7EA]">
      <h2 className="text-2xl text-[#272727] font-bold">
        Residential Properties (Mortgages)
      </h2>
      <div className="flex flex-col mt-4 gap-4">
        {familyHouse.map((group) => (
          <PropertyGroup
            key={group.id}
            group={group}
            onRemove={removeGroup}
            onChange={handleInputChange}
          />
        ))}
      </div>
      <Button
        onClick={addGroup}
        className="mt-4 bg-[#07838F1A] hover:bg-[#07838F] text-[#07838F] px-4 py-2 rounded"
      >
        + Add Residential Category
      </Button>
    </div>
  );
};

export default ResidentialProperties;