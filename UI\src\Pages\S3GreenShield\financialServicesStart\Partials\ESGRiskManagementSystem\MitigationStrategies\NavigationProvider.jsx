// import { createContext, useState, useContext } from "react";

// const NavigationContext = createContext();

// export const NavigationProvider = ({ children }) => {
//   const [currentPage, setCurrentPage] = useState("StrategyLibrary");

//   const navigateTo = (page) => {
//     setCurrentPage(page);
//   };

//   return (
//     <NavigationContext.Provider value={{ currentPage, navigateTo }}>
//       {children}
//     </NavigationContext.Provider>
//   );
// };

// export const useNavigation = () => useContext(NavigationContext);
