import { Button, Progress } from "@mantine/core";
import {
  ESGAuditSearchSolidIcon,
  ESGAuditSearchOutlineIcon,
  ESGAuditTaskSquareSolidIcon,
  ESGAuditTaskSquareOutlineIcon,
} from "@/assets/icons";
import { useEffect, useState } from "react";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import S3Layout from "@/Layout/S3Layout";
import ControlsEvaluationForm from "@/Components/GreenShield/RiskManagement/Audit/ControlsEvaluationForm";
import FindingsForm from "@/Components/GreenShield/RiskManagement/Audit/FindingsForm";

export default function Audit() {
  const { ESGRiskManagementMenu } = useSideBarRoute();
  const [isAiDone, setIsAiDone] = useState(false);
  const [activeTab, setActiveTab] = useState(1);
  const [Buttons, setButtons] = useState([
    {
      title: "Controls Evaluation",
      icon: <ESGAuditTaskSquareOutlineIcon />,
      sIcon: <ESGAuditTaskSquareSolidIcon />,
      progress: 0,
      id: 1,
    },
    {
      title: "Findings",
      icon: <ESGAuditSearchOutlineIcon />,
      sIcon: <ESGAuditSearchSolidIcon />,
      progress: 0,
      id: 2,
    },
  ]);

  const controlsInitial = {
    id: "Auto",
    controlDescription: "",
    evidence: null,
    implementationStatus: "",
    designEffectiveness: "",
    operatingEffectiveness: "",
    evaluation: "",
    testDate: "",
    evaluator: "",
    nextReviewDate: "",
  };

  const findingsInital = {
    id: "Auto",
    controlDescription: "",
    findingDescription: "",
    gapIdentified: "",
    riskLevel: "",
    recommendations: "",
    recommendationPlan: "",
    owner: "",
    status: "",
    dueDate: "",
  };

  const [controlsData, setControlsData] = useState(controlsInitial);

  const [findings, setFindings] = useState(findingsInital);

  const updateProgress = (id, newProgress) => {
    setButtons((prev) =>
      prev.map((btn) =>
        btn.id === id ? { ...btn, progress: newProgress } : btn
      )
    );
  };

  useEffect(() => {
    // Count non-empty fields
    const filledFields = Object.values(controlsData).filter(Boolean).length;
    const totalFields = Object.keys(controlsData).length;
    const progress = (filledFields / totalFields) * 100;

    // Update progress in parent
    updateProgress(1, progress);
  }, [controlsData]);

  useEffect(() => {
    // Count non-empty fields
    const filledFields = Object.values(findings).filter(Boolean).length;
    const totalFields = Object.keys(findings).length;
    const progress = (filledFields / totalFields) * 100;

    // Update progress in parent
    updateProgress(2, progress);
  }, [findings]);

  const changeControls = (key, value) => {
    setControlsData((prev) => ({ ...prev, [key]: value }));
  };
  const resetControls = () => {
    setControlsData(controlsInitial);
  };

  const changeFindings = (key, value) => {
    setFindings((prev) => ({ ...prev, [key]: value }));
  };

  const resetFindings = () => {
    setFindings(findingsInital);
  };

  return (
    <>
      <div className="flex items-center justify-between gap-4">
        {Buttons.map((item, index) => (
          <Button
            onClick={() => setActiveTab(item.id)}
            key={index}
            variant="default"
            className="w-full"
            unstyled
          >
            <div
              className={`mb-5 flex items-center gap-3  rounded-xl h-16 overflow-hidden shadow-md hover:bg-[#56A1A91A] pr-6 duration-300 ${
                item.id == activeTab ? " bg-[#56A1A91A] " : "bg-white"
              } `}
            >
              <div
                className={`bg-primary  duration-500 ${
                  item.id == activeTab ? "h-full w-2" : "h-0 w-0"
                }`}
              ></div>
              <div
                className={`full-center rounded-full h-11 w-11 bg-bg-lite1 ${
                  activeTab == item.id ? "bg-white" : "ml-4"
                } `}
              >
                {activeTab == item.id ? item.sIcon : item.icon}
              </div>
              <h5 className="font-bold">{item.title}</h5>
            </div>
            <Progress value={item.progress} color="#05808B" />
          </Button>
        ))}
      </div>

      {activeTab === 1 && (
        <ControlsEvaluationForm
          formData={controlsData}
          handleChange={changeControls}
          isAiDone={isAiDone}
          setIsAiDone={setIsAiDone}
          resetControls={resetControls}
          setActiveTab={setActiveTab}
        />
      )}
      {activeTab === 2 && (
        <FindingsForm
          formData={findings}
          handleChange={changeFindings}
          isAiDone={isAiDone}
          setIsAiDone={setIsAiDone}
          resetFindings={resetFindings}
        />
      )}
    </>
  );
}
