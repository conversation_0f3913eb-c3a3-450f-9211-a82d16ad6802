import Loading from "@/Components/Loading";
import { Pagination, ScrollArea, Table, TextInput } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { FaAngleDown, FaAngleUp } from "react-icons/fa";
import { IoIosSearch } from "react-icons/io";
import { useNavigate } from "react-router-dom";
import StakeholderInteractionsPopUp from "./Partials/StakeholderInteractionsPopUp";
import StakeholderMultiSelect from "./Partials/StakeholderMultiSelect";

export default function StakeholderInteractions({ data, loading }) {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [currentPage, setCurrentPage] = useState(1);
  const [isNewestFirst, setIsNewestFirst] = useState(true);
  const [selectedColumns, setSelectedColumns] = useState([]);
  const [sortedData, setSortedData] = useState([]);
  const [searchQuery, setSearch] = useState("");
  const [opened, { open, close }] = useDisclosure(false);
  const [selectedIncident, setSelectedIncidentId] = useState({});

  const rowsPerPage = 6;
  const totalPages = Math.ceil(data.length / rowsPerPage);
  const currentData = useMemo(() => {
    return (
      (data &&
        data?.slice(
          (currentPage - 1) * rowsPerPage,
          currentPage * rowsPerPage
        )) ||
      []
    );
  }, [data, currentPage, rowsPerPage]);
  useEffect(() => {
    setSortedData(currentData);
  }, [currentData]);

  function sortArrayByDate(order = "newest") {
    const filterData = currentData.sort((a, b) => {
      const dateA = new Date(a.closureDate);
      const dateB = new Date(b.closureDate);

      if (order === "newest") {
        return dateB - dateA;
      } else {
        return dateA - dateB;
      }
    });
    setSortedData(filterData);
  }
  const handleSearchChange = (event) => {
    const value = event.target.value;
    setSearch(value);

    if (value === "") {
      setSortedData(currentData || []);
      return;
    }

    const filteredData = currentData.filter(
      (item) =>
        item.title.toLowerCase().includes(value.toLowerCase()) ||
        item.interactionDescription
          .toLowerCase()
          .includes(value.toLowerCase()) ||
        item.interactionLocation.toLowerCase().includes(value.toLowerCase()) ||
        item.priority.toLowerCase().includes(value.toLowerCase()) ||
        item.status.toLowerCase().includes(value.toLowerCase()) ||
        item.involvedPersons.some((individual) =>
          individual.fullName.toLowerCase().includes(value.toLowerCase())
        )
    );

    setSortedData(filteredData);
  };
  const rows = sortedData.map((item, index) => (
    <Table.Tr
      key={item._id}
      className="text-sm font-bold text-[#626364] text-center cursor-pointer"
      onClick={() => {
        open();
        setSelectedIncidentId(item);
      }}
    >
      <Table.Td>{index + 1}</Table.Td>
      <Table.Td>{item.title}</Table.Td>

      <Table.Td>
        {item.createdAt ? item.createdAt.split("T")[0] : "N/A"}
      </Table.Td>

      <Table.Td className="border-primary rounded-lg">
        <span
          className={`border-primary py-2 rounded-lg w-full capitalize
              ${
                item.status.trim().toLowerCase() === "in_progress"
                  ? "bg-[#fff0b8] text-[#FFAB07] px-2"
                  : item.status.trim().toLowerCase() === "new"
                  ? "bg-[#cafac2] text-[#70D162] px-7"
                  : item.status.trim().toLowerCase() === "resolved"
                  ? "bg-[#cea7f6] text-[#9160C1] px-3"
                  : "bg-red-300 text-red-800 px-5"
              }
              `}
        >
          {item.status.includes("in_progress")
            ? item.status.replace("_", " ")
            : item.status}
        </span>
      </Table.Td>

      <Table.Td>
        {item?.involvedPersons?.length > 0 ? item.involvedPersons.length : 0}
      </Table.Td>

      <Table.Td className="capitalize">
        <span
          className={`border-primary py-2 rounded-2xl w-full capitalize px-5
        ${
          item.priority.trim().toLowerCase() === "high"
            ? "bg-[#FFEDCA] text-[#FFAB07]"
            : item.priority.trim().toLowerCase() === "medium"
            ? "bg-[#00C0A9] text-[#00C0A9] bg-opacity-20 px-7"
            : item.priority.trim().toLowerCase() === "low"
            ? "bg-[#01BD36] text-[#01BD36] bg-opacity-20 px-3"
            : "bg-gray-200 text-black"
        }
      `}
        >
          {item.priority.trim().charAt(0).toUpperCase() +
            item.priority.trim().slice(1).toLowerCase() || "unknown"}
        </span>
      </Table.Td>

      <Table.Td>{item.interactionType}</Table.Td>

      <Table.Td>{item.interactionLocation}</Table.Td>

      <Table.Td>
        {item.anonymous
          ? "Anonymous"
          : item.personInfo?.firstName || "Anonymous"}
      </Table.Td>

      <Table.Td>
        {item.closureDate ? item.closureDate.split("T")[0] : "N/A"}
      </Table.Td>
    </Table.Tr>
  ));

  // Table Header: Adjust the header to only display selected columns
  const headers = (
    <Table.Tr className="text-sm text-[#626364] text-center">
      <Table.Th className="text-center">UID</Table.Th>
      <Table.Th className="text-center">Title</Table.Th>

      <Table.Th className="text-center">Report Date</Table.Th>

      <Table.Th className="text-center">Status</Table.Th>
      <Table.Th className="text-center">People involved</Table.Th>
      <Table.Th className="text-center">Priority</Table.Th>
      <Table.Th className="text-center">Category</Table.Th>
      <Table.Th className="text-center">Location</Table.Th>
      <Table.Th className="text-center">Source</Table.Th>

      <Table.Th className="text-center">Closure Date</Table.Th>
    </Table.Tr>
  );
  return (
    <div className="w-full pb-20">
      {loading ? (
        <div className="m-auto flex justify-center w-full ">
          <Loading />
        </div>
      ) : (
        <div className=" mt-14">
          <div className="md:flex flex-row items-center justify-between font-semibold text-[#000]  py-2 pr-4 mb-4">
            <div className=" flex flex-row text-lg  font-normal mb-5 md:mb-0 md:w-1/2">
              <div className="flex flex-row items-center bg-[#F9FBFF] rounded-xl px-2 w-full">
                <IoIosSearch className="text-[#626364] w-8 h-8 ml-2" />
                <TextInput
                  variant={`unstyled`}
                  type="text"
                  id="potentialFinancialLoss"
                  placeholder="Search"
                  value={searchQuery}
                  onChange={handleSearchChange}
                  className="bg-transparent ml-2 w-full"
                />
              </div>
            </div>

            <div className="md:flex flex-row mr-4 gap-4">
              <div className="flex flex-row items-center shadow-none rounded-xl bg-[#FFFFFF] text-[#000000] mb-5 md:mb-0">
                <StakeholderMultiSelect
                  selectedColumns={selectedColumns}
                  setSelectedColumns={setSelectedColumns}
                />
              </div>

              <div
                className="flex flex-row items-center shadow-none gap-2 py-2 px-4 rounded-xl bg-[#FFFFFF] text-[#00C0A9] cursor-pointer"
                onClick={() => {
                  const newOrder = isNewestFirst ? "oldest" : "newest";
                  sortArrayByDate(newOrder);
                  setIsNewestFirst(!isNewestFirst);
                }}
              >
                <p className="text-[#7E7E7E] text-sm">Sort by :</p>
                <p className="text-[#000000]">
                  {isNewestFirst ? "Newest" : "Oldest"}
                </p>
                {isNewestFirst ? (
                  <FaAngleDown className="text-[#212121]" />
                ) : (
                  <FaAngleUp className="text-[#212121]" />
                )}
              </div>
            </div>
          </div>
          {searchQuery && sortedData?.length === 0 ? (
            <h1 className="mt-5 text-center capitalize">
              Your Search is not Found
            </h1>
          ) : (
            <ScrollArea>
              <Table miw={800} verticalSpacing="sm" stickyHeader>
                <Table.Thead className="border-b-2 border-primary pb-6 text-primary font-bold text-base">
                  {headers}
                </Table.Thead>
                <Table.Tbody>{rows}</Table.Tbody>
              </Table>
            </ScrollArea>
          )}
          {/* Pagination */}
          <div className="md:flex items-center justify-between mt-4 mr-10">
            <p className="text-sm text-gray-600">
              {t("showingData", {
                start: (currentPage - 1) * rowsPerPage + 1,
                end: Math.min(currentPage * rowsPerPage, data?.length),
                total: data?.length,
              })}
            </p>
            <Pagination
              page={currentPage}
              onChange={(e) => {
                setCurrentPage(e);
              }}
              total={totalPages}
              color="#00c0a9"
              className="flex justify-center mt-5 md:mt-0"
            />
          </div>
          {/* Modal for Interaction Details */}
          <StakeholderInteractionsPopUp
            selectedIncident={selectedIncident}
            close={close}
            opened={opened}
          />
        </div>
      )}
    </div>
  );
}

// Call sortData whenever isNewestFirst changes

// Function to open modal with specific incident data
// const openModal = (incidentId) => {
//   setSelectedIncidentId(incidentId);
//   setModalOpen(true);
// };

// // Function to handle modal close
// const closeModal = () => {
//   setModalOpen(false);
//   setSelectedIncidentId(null);
// };

// const updateIncident = (updatedData) => {
//   // Update incident in the local state
//   setIncidentData((prevData) =>
//     prevData.map((incident) =>
//       incident.id === updatedData.id ? updatedData : incident
//     )
//   );

// Optional: Re-fetch data from the API if you want to ensure data consistency
//   fetchIncidentData();
// };

// useEffect(() => {
//   // Filter data based on search query
//   const filtered = data.filter((item) => {
//     const uid = item._id?.toString().toLowerCase();
//     const title = item.title?.toLowerCase();
//     const date = item.createdAt?.split("T")[0].toLowerCase();
//     const status = item.status?.toLowerCase();
//     const peopleInvolved =
//       item.involvedIndividuals?.length > 0 ? "true" : "false";
//     const priority = item.priority?.trim().toLowerCase();
//     const location = `${item.location?.toLowerCase()}, ${item.country?.toLowerCase()}`;
//     const category = "environmental";
//     const assignedTo = "";
//     const Source = item.anonymousDetails.name?.toLowerCase();
//     const closureDate = "";

//     // Return true if any field matches the query
//     return (
//       uid.includes(searchQuery) ||
//       title.includes(searchQuery) ||
//       date.includes(searchQuery) ||
//       status.includes(searchQuery) ||
//       peopleInvolved.includes(searchQuery) ||
//       priority.includes(searchQuery) ||
//       location.includes(searchQuery) ||
//       category.includes(searchQuery) ||
//       assignedTo.includes(searchQuery) ||
//       Source.includes(searchQuery) ||
//       closureDate.includes(searchQuery)
//     );
//   });

//   setFilteredData(filtered);
// }, [searchQuery, data]);
// Function to sort the data
// const sortData = () => {
//   const sortedData = [...filteredData].sort((a, b) => {
//     const dateA = new Date(a.createdAt);
//     const dateB = new Date(b.createdAt);

//     if (isNewestFirst) {
//       return dateB - dateA; // Newest first
//     } else {
//       return dateA - dateB; // Oldest first
//     }
//   });

//   setFilteredData(sortedData);
// };
