import React from "react";
import { AreaChart } from "@mantine/charts";
import { Select } from "@mantine/core";

const EmissionsByCategory = () => {
  const data = [
    {
      date: "2012",
      waste: 50000,
      electricity: 60000,
      commute: 40000,
      maintenance:30000,
    },
    {
      date: "2013",
      waste: 42000,
      electricity: 52000,
      commute: 32000,
      maintenance:22000,
    },
    {
      date: "2014",
      waste: 49000,
      electricity: 59000,
      commute: 39000,
      maintenance:29000,
    },
    {
      date: "2015",
      waste: 53000,
      electricity: 63000,
      commute: 43000,
      maintenance:33000,
    },
    {
      date: "2016",
      waste: 41000,
      electricity: 51000,
      commute: 31000,
      maintenance:21000,
    },
    {
      date: "2017",
      waste: 34000,
      electricity: 44000,
      commute: 24000,
      maintenance:14000,
    },
    {
      date: "2018",
      waste: 30000,
      electricity: 40000,
      commute: 20000,
      maintenance:10000,
    },
    {
      date: "2019",
      waste: 33000,
      electricity: 43000,
      commute: 23000,
      maintenance:13000,
    },
    {
      date: "2020",
      waste: 28000,
      electricity: 38000,
      commute: 18000,
      maintenance:8000,
    },
    {
      date: "2021",
      waste: 41000,
      electricity: 51000,
      commute: 31000,
      maintenance:21000,
    },
    {
      date: "2022",
      waste: 45000,
      electricity: 55000,
      commute: 35000,
      maintenance:25000,
    },
    {
      date: "2023",
      waste: 41200,
      electricity: 51200,
      commute: 31200,
      maintenance:21200,
    },
    {
      date: "2024",
      waste: 30000,
      electricity: 40000,
      commute: 20000,
      maintenance:10000,
    },
  ];

  return (
    <div>
      <div className="flex items-center justify-between p-2 mb-4">
        <h3 className="text-2xl font-semibold me-9">Emissions by Category</h3>
        <Select
          classNames={{
            label: "py-2",
            root: "rounded-lg focus:border-primary",
          }}
          // label="Categories"
          placeholder="Pick value"
          defaultValue="Yearly"
          data={["Yearly", "Year", "Years"]}
        />
      </div>

      <AreaChart
        className="bg-white py-8 px-4 rounded-lg shadow-lg"
        h={350}
        data={data}
        dataKey="date"
        withDots={false}
        curveType="bump"
        // dotProps={{ r: 2 }}
        // activeDotProps={{ r: 3, strokeWidth: 1 }}
        series={[
          { name: "electricity", color: "teal" },
          { name: "commute", color: "yellow" },
          { name: "maintenance", color: "red",  },
          { name: "waste", color: "cyan" },
        ]}
      />
    </div>
  );
};

export default EmissionsByCategory;
