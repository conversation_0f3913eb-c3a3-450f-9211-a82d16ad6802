import { ExportIcon } from "@/assets/icons/ReportAndAnalytic";
import { BarChart } from "@mantine/charts";
import ApiS3 from "@/Api/apiS3";
import { useEffect, useRef, useState } from "react";

import * as htmlToImage from 'html-to-image';


const RiskByCateg = () => {
  const [categories, setCategories] = useState([])

  const getCategories = async () => {
    try {
      const res = await ApiS3.get("risk/CategoriesStatistics");
      setCategories(res.data);
    } catch (er) {
        console.log("🚀 ~ getData ~ er:", er)
    }
  };

  useEffect(() => {
    getCategories();
  }, []);


// Convert backend data to chart format
const chartData = categories?.map(item => ({
    _id: item._id,
    Extreme: item.risks.find(r => r.level === "Extreme")?.count || 0,
    Medium: item.risks.find(r => r.level === "Medium")?.count || 0,
    // Add other risk levels if needed
    Low: item.risks.find(r => r.level === "Low")?.count || 0,
    High: item.risks.find(r => r.level === "High")?.count || 0
}));

const chart = useRef(null);  


const handleExportPNG = () => {
  if (chart.current) {
      htmlToImage
          .toPng(chart.current, {
              quality: 0.95, // Image quality (0 to 1)
              pixelRatio: 2, // Increase resolution
              backgroundColor: '#f5f5f5', // Match the background color
          })
          .then((dataUrl) => {
              // Create a link element to trigger the download
              const link = document.createElement('a');
              link.href = dataUrl;
              link.download = 'Risk By Category.png'; // File name for the download
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
          })
          .catch((error) => {
              console.error('Error exporting PNG:', error);
              alert('Failed to export PNG. Please try again.');
          });
  }
};


  return (
    <div className="mt-10 bg-white shadow-md rounded-2xl p-4">
      <div className="flex flex-wrap justify-between items-center mb-10">
        <h5 className="text-xl">Risk by Category</h5>

        <button onClick={handleExportPNG} className="bg-bg-lite2 text-primary flex justify-between items-center rounded-xl p-3 gap-2">
          <ExportIcon />
          <span className="text-sm font-bold">PNG Export</span>
        </button>
      </div>
      <BarChart
      ref={chart}
        h={200}
        data={chartData}
        dataKey="_id"
        orientation="vertical"
        yAxisProps={{ width: 100 }}
        barProps={{ radius: 5 }}
        type="stacked"
        series={[
          { name: "Low", color: "#EA0B0B" },
          { name: "High", color: "#08B12D" },
          { name: "Medium", color: "#F4B351" },
          { name: "Extreme", color: "#FFF504" },
        ]}
      />
    </div>
  );
};

export default RiskByCateg;
