// useAutoLoanStore.js
import { create } from "zustand";

export const useProjectFinanceStore = create((set) => ({
  financing: "",
  project_cost: "",
  project_annual_emissions: "",
  project_name: "",
  project_type: "Renewable Energy",
  project_phase: "Greenfield (New Project)",
  project_size: "",
  data_quality_score: "",
  lifetime: 20.0,

  // results
  attribution_percentage: 0,
  emissions_per_sm_invested: 0,
  lifetime_financed_emissions: 0,
  project_emissions: 0,

  loading: false,
  estimate_project_annual_emissions_loading:false,
  // Setters
  setFinancing: (v) => set({ financing: v }),
  setProjectCost: (v) => set({ project_cost: v }),
  setProjectAnnualEmissions: (v) => set({ project_annual_emissions: v }),
  setProjectName: (v) => set({ project_name: v }),
  setProjectType: (v) => set({ project_type: v }),
  setProjectPhase: (v) => set({ project_phase: v }),
  setProjectSize: (v) => set({ project_size: v }),
  setDataQualityScore: (v) => set({ data_quality_score: v }),
  setLifetime: (v) => set({ lifetime: v }),
  setLoading: (v) => set({ loading: v }),
  setEstimateProjectAnnualEmissionsLoading: (v) => set({ estimate_project_annual_emissions_loading: v }),

  setResults: (results) => set({
    attribution_percentage: results.attribution_percentage,
    emissions_per_sm_invested: results.emissions_per_sm_invested,
    lifetime_financed_emissions: results.lifetime_financed_emissions,
    project_emissions: results.project_emissions
  }),
}));
