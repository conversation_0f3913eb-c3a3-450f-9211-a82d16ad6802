import { BreakdownScopesIcon } from "@/assets/svg/ImageSVG";
import { formatNumber } from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/StaticData";
import React from "react";
import { FaRegEye } from "react-icons/fa";
import { Link } from "react-router-dom";

export default function DynamicForScopeData({ data: ScopesData,scopeName }) {
  console.log(ScopesData);
  
 return (
  <>
   {ScopesData &&
    Object.entries(ScopesData)?.map(
     ([item, value], index) =>
      typeof value === "object" && (
       <div
        key={index}
        className="p-3 bg-white rounded-lg shadow-md mt-3 flex flex-col md:flex-row justify-between items-center gap-5"
       >
        <div className="flex items-center gap-3">
         <p className="p-3 bg-[#07838F1A] rounded-lg">
          <BreakdownScopesIcon color={"#07838F"} />
         </p>
         <div className="">
          <p className="text-base font-medium">{item}</p>
          <p className="text-sm font-normal">
           {formatNumber(value?.total_emission)}
          </p>
         </div>
        </div>
        <Link
         className="bg-[#07838F1A] text-primary hover:bg-[#07838F1A] hover:text-primary flex items-center
      rounded-3xl border-2 border-primary px-12 py-2"
         to={`/net-zero/measure/scope_breakdown/${item}/${scopeName}`}
        >
         <FaRegEye className="me-1" />
         View Details
        </Link>
       </div>
      )
    )}
  </>
 );
}
