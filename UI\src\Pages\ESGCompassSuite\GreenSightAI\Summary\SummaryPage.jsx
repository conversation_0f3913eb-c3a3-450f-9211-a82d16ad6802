import Summary from "./Partials/Summary";
import MainLayout from "@/Layout/MainLayout";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import Dashboard from "../Dashboard";
import Result from "../ResultPage/Result";
import { IoMdHome } from "react-icons/io";
import ApiSustain360 from "@/Api/apiSustain360";
import { useSustain360Store } from "@/Store/Assessment/useSustain360Store";

const SummaryPage = () => {
  const { t } = useTranslation();
  const [active, setActive] = useState("Assessment");
  const { setAssessmentData, setAssessmentSummery } = useSustain360Store();

  useEffect(() => {
    fetchAssessmentData();
    fetchAssessmentSummery();
  }, []);

  const fetchAssessmentData = async () => {
    try {
      const response = await ApiSustain360.get("/api/assessments/in-progress/last")
      const assessmentData = response.data;
      setAssessmentData(assessmentData);
    } catch (error) {
      const response = await ApiSustain360.post("/api/assessments");
      const assessmentData = response.data;
      setAssessmentData(assessmentData);
    }
  };
  const fetchAssessmentSummery = async () => {
    try {
      const response = await ApiSustain360.get("/api/assessments/summery", {
        headers: { assessmentName: "GreenSight AI" },
      });
      const assessmentSummery = response.data;
      setAssessmentSummery(assessmentSummery);
    } catch (error) {
      console.error("Error fetching assessment summery:", error);
    }
  };


  const renderContent = () => {
    switch (active) {
      case "Assessment":
        return (
          <>
            <Summary setActive={setActive}/>
          </>
        );
      case "Dashboard":
        return (
          <>
            <Dashboard setActive={setActive}/>
          </>
        );
      case "Reporting":
        return (
          <>
            <Result setActive={setActive}/>
          </>
        );
      default:
        return null;
    }
  };
  // /Insights-reporing/greensight
  return (
    <MainLayout
      navbarTitle={"Sustain360 AI"}
      breadcrumbItems={[
        { title: <IoMdHome size={20} />, href: "/get-started" },
        { title: "Sustain360 AI", href: "/Insights-reporing/greensight" },
        { title: "Sustain360 AI Assessement", href: "#" },
      ]}
    >
      <div className="grid md:grid-cols-3 justify-center bg-[#FFFFFF] border-2 border-[#E8E7EA] p-6 gap-5 py-3 mb-6 rounded-lg text-primary">
        <button
          className={`relative text-lg font-semibold py-3 px-6 transition-all ${
            active === "Assessment"
              ? "active-tab rounded"
              : "text-[#5A5A5A] rounded-lg border-2"
          }`}
          onClick={() => setActive("Assessment")}
        >
          {t("Assessment")}
        </button>

        <button
          className={`relative text-lg font-semibold py-3 px-6 transition-all ${
            active === "Dashboard"
              ? "active-tab rounded"
              : "text-[#5A5A5A] rounded-lg border-2"
          }`}
          onClick={() => setActive("Dashboard")}
        >
          {t("Dashboard")}
        </button>

        <button
          className={`relative text-lg font-semibold py-3 px-6 transition-all ${
            active === "Reporting"
              ? "active-tab rounded"
              : "text-[#5A5A5A] rounded-lg border-2"
          }`}
          onClick={() => setActive("Reporting")}
        >
          {t("Reporting")}
        </button>
      </div>

      {renderContent()}
    </MainLayout>
  );
};

export default SummaryPage;
