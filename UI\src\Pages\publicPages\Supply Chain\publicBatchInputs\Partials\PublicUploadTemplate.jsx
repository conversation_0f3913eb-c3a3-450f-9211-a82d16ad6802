import PublicSupplyChainConfig from "@/Api/PublicSupplyChainConfig";
import Loading from "@/Components/Loading";
import { Button, FileInput, Select, Tooltip } from "@mantine/core";
import { showNotification } from "@mantine/notifications";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { FiUpload } from "react-icons/fi";
import { IoIosArrowDown, IoMdClose } from "react-icons/io";

export default function UploadTemplate({ assetTypeAll,allItemSpecificities }) {
  const { t } = useTranslation();
  const [file, setFile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [Evidence, setEvidence] = useState([]);
  const [SelectedAssetType, setSelectedAssetType] = useState();
  const [SelectedLocation, setSelectedLocation] = useState();
  const [SelectedSourcePhysical, setSelectedSourcePhysical] = useState();
  const [SelectedSourceService, setSelectedSourceService] = useState();
  // //console.log(file);
  const handleRemove = (indexToRemove) => {
    setEvidence((prevEvidence) =>
      prevEvidence.filter((_, idx) => idx !== indexToRemove)
    );
  };
  const store_filled_template = async () => {
    const filesCollect = {
      filled_template: file,
      evidence_files: Evidence,
    };

    const formData = new FormData();

    for (const key in filesCollect) {
      if (filesCollect[key]) {
        formData.append(key, filesCollect[key]);
      }
    }
    try {
      setLoading(true);
      const { data } = await PublicSupplyChainConfig.post(
        "/batch_inputs/supplier/store_filled_template",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            "asset-type": SelectedAssetType,
            "Location-Specificity": SelectedLocation,
            "Source-Service": SelectedSourceService,
            "Source-Physical": SelectedSourcePhysical,
          },
        }
      );
      getTableData("batch");
      setFile(null);
      setEvidence(null);
      setSelectedAssetType(null);
      setLoading(false);
      showNotification({
        message: "batch input added successfully",
        color: "green",
      });
      //console.log(data);
    } catch (error) {
      setLoading(false);
      showNotification({
        message: error.response.data.message,
        color: "red",
      });
      //console.log(error);
    }
  };
  useEffect(() => {
    if (!file) {
      setEvidence([]);
    }
  }, [file]);
  const step2 = file;
  const step3 =
    SelectedLocation && SelectedSourcePhysical && SelectedSourceService;
  return (
    <>
      <div className="md:flex justify-between items-start mt-4 ">
        <p className="font-semibold text-[16px] leading-[24px] ">
          <span className="text-secondary-300 capitalize me-1">
            {t("uploadTemplate.step2")}
          </span>
          {t(
            "Add data in the template, save the file, and upload the template."
          )}
        </p>
        <div className="">
          <div className="md:flex items-center ">
            <Select
              id="Template"
              name="Template"
              data={assetTypeAll.map((item) => item?.asset)}
              placeholder="Select the Asset Type"
              className="md:me-10 w-60 md:w-72 mx-auto"
              radius={10}
              value={SelectedAssetType}
              onChange={(e) => {
                setSelectedAssetType(e);
                setFile(null);
                setEvidence(null);
              }}
            />
            <div>
              <FileInput
                id="upload_File"
                type="file"
                className="hidden"
                value={file}
                onChange={(newFile) => setFile(newFile)}
                accept=".xlsx,.xls"
                disabled={!SelectedAssetType}
              />
              <Tooltip
                multiline
                w={220}
                radius={"md"}
                withArrow
                transitionProps={{ duration: 200 }}
                label={
                  !SelectedAssetType && (
                    <span className="capitalize flex justify-center">
                      please select Asset Type first
                    </span>
                  )
                }
                className={!SelectedAssetType ? "" : "hidden"}
              >
                <label
                  htmlFor="upload_File"
                  className={`flex items-center justify-center gap-4 p-2 px-9 text-white shadow-md cursor-pointer bg-secondary-300 
        rounded-xl mt-5 md:mt-0 ${
          !SelectedAssetType
            ? "cursor-not-allowed opacity-20 bg-slate-800"
            : "cursor-pointer bg-secondary-300"
        }`}
                >
                  <span>
                    <FiUpload />
                  </span>
                  <span>{t("Upload Template")}</span>
                </label>
              </Tooltip>
            </div>
          </div>

          <div className="mt-5 md:flex justify-end">
            <FileInput
              id="upload_template"
              type="file"
              className="hidden"
              value={Evidence}
              onChange={(newFile) => setEvidence(newFile)}
              disabled={!file}
              multiple
              accept=".pdf"
            />
            <Tooltip
              multiline
              w={220}
              radius={"md"}
              withArrow
              transitionProps={{ duration: 200 }}
              label={
                !file ? (
                  <span className="capitalize flex justify-center">
                    please Upload Template first
                  </span>
                ) : (
                  <span className="capitalize">
                    You can Select many Files Click CTRL From your Keyboard then
                    choose your files
                  </span>
                )
              }
            >
              <label
                htmlFor="upload_template"
                className={`flex items-center justify-center gap-4 p-2 px-9 text-white shadow-md   
              rounded-xl mt-5 md:mt-0 
              ${
                !file
                  ? "cursor-not-allowed opacity-20 bg-slate-800"
                  : "cursor-pointer bg-secondary-300"
              }
              `}
              >
                <span>
                  <FiUpload />
                </span>
                <span>{t("Upload Evidence")}</span>
              </label>
            </Tooltip>
          </div>
        </div>
      </div>
      <div
        className={` ${
          !file ? "hidden" : "md:flex"
        } justify-between items-center mt-5 md:mt-3`}
      >
        <div className="bg-[#e7f3f4] md:flex flex-col gap-5 p-5 rounded-lg md:w-1/2">
          <div
            className={`justify-between items-center w-full ${
              !file ? "hidden" : "md:flex"
            }`}
          >
            <div>
              <h1 className="text-sm font-bold">{file?.name}</h1>
              <p className="font-medium text-sm text-primary">
                Upload complete
              </p>
            </div>
            <Button
              className="bg-white text-red-600 rounded-2xl hover:bg-white hover:text-red-600 mt-5 md:mt-0"
              onClick={() => {
                setFile(null); // Reset file state
                setEvidence([]); // Reset any associated data
                // setInputKey(Date.now()); // Force re-render of FileInput
              }}
            >
              Remove <IoMdClose className="text-neutral-700" />
            </Button>
          </div>
          {Evidence?.map((item, idx) => (
            <div
              className={`mt-3 ${
                Evidence?.length === 0 ? "hidden" : "md:flex"
              } justify-between items-center w-full`}
              key={idx}
            >
              <div>
                <h1 className="text-sm font-bold">{item?.name}</h1>
                <p className="font-medium text-sm text-primary">
                  Upload complete
                </p>
              </div>
              <Button
                className="bg-white text-red-600 rounded-2xl hover:bg-white hover:text-red-600 mt-5 md:mt-0"
                onClick={() => handleRemove(idx)}
              >
                Remove <IoMdClose className="text-neutral-700" />
              </Button>
            </div>
          ))}
        </div>
      </div>
      <hr className="h-[2px] bg-gray-300  mx-auto mt-3" />
      <div className="md:flex justify-between items-start mt-4 ">
        <p className="font-semibold text-[16px] leading-[24px] ">
          <span className="text-secondary-300 capitalize me-1">
            {t("Step3 :")}
          </span>
          {t(
            "Add Location Specificity, Source Physical, Source Service for Data Quality Assessment."
          )}
        </p>
      </div>
      <div className="mt-5">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-5 items-center ">
          <Select
            value={SelectedLocation}
            onChange={(e) => {
              setSelectedLocation(e);
            }}
            rightSection={<IoIosArrowDown />}
            label="Location Specificity"
            radius={10}
            size="md"
            placeholder="Choose"
            data={allItemSpecificities?.location_specificity?.map(
              (item) => item.location
            )}
            clearable
          />
          <Select
            value={SelectedSourcePhysical}
            onChange={(e) => {
              setSelectedSourcePhysical(e);
            }}
            rightSection={<IoIosArrowDown />}
            label="Source Physical"
            radius={10}
            size="md"
            placeholder="Choose"
            data={allItemSpecificities?.source_physical?.map(
              (item) => item.source
            )}
          />
          <Select
            value={SelectedSourceService}
            onChange={(e) => {
              setSelectedSourceService(e);
            }}
            rightSection={<IoIosArrowDown />}
            label="Source Service"
            radius={10}
            size="md"
            placeholder="Choose"
            data={allItemSpecificities?.source_service?.map(
              (item) => item.source
            )}
          />
        </div>
      </div>
      <div className={`md:flex justify-end items-center mt-5 md:mt-3`}>
        <Tooltip
          multiline
          w={220}
          radius={"md"}
          withArrow
          transitionProps={{ duration: 200 }}
          label={
            (!step2 || !step3) && (
              <span className="capitalize flex justify-center">
                please finish {!step2 && <span className="ms-1"> step2 </span>}{" "}
                {!step3 && <span className="ms-1">step3</span>}
              </span>
            )
          }
          hidden={step2 && step3}
        >
          <Button
            className={`py-2 px-[62px]   flex gap-1 items-center rounded-xl shadow-md  
        border-2 text-white mx-auto md:mx-0 mt-5 md:mt-0 ${
          !step2 || !step3
            ? "cursor-not-allowed opacity-20 bg-slate-800 hover:bg-slate-800"
            : "bg-primary hover:bg-primary"
        }`}
            size="md"
            onClick={
              loading
                ? ""
                : () => {
                    store_filled_template();
                  }
            }
            disabled={!file}
          >
            {loading ? <Loading /> : <span>{t("Submit Data")}</span>}
          </Button>
        </Tooltip>
      </div>
    </>
  );
}
