import React, { useEffect, useState } from "react";
import { useGreenSighAssessment } from "@/Contexts/GreenSighContext";
import { useNavigate } from "react-router";
import { useSustain360Store } from "@/Store/Assessment/useSustain360Store";

const CategorySidebar = () => {
  const Navigation = useNavigate();
  const [activeCategory, setActiveCategory] = useState(null);

  const { setActiveAssesmentSection, activeAssesmentSection } = useSustain360Store();

  useEffect(() => {
    setActiveCategory(activeAssesmentSection);
    
  }, [activeAssesmentSection]);
  
  const handleCategoryClick = (category) => {
    setActiveAssesmentSection(category);
  };

  const getCategoryClassName = (category) => {
    return `flex gap- justify-center items-center mb-5 py-5 px-4 ${
      activeCategory === category
        ? "text-white bg-[#29919B] translate-y-2"
        : "text-black bg-[#FFFFFF] hover:bg-[#29919B] hover:text-white"
    } lg:w-full shadow-lg rounded-xl cursor-pointer transition-all duration-300 ease-in-out`;
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 xl:grid-cols-5 gap-4">
      <div
        className={getCategoryClassName("General")}
        onClick={() => {
          // Navigation("/Insights-reporing/greensight/diagnosis/General/question/1");
          handleCategoryClick("General");
        }}
      >
        <span className="text-lg font-semibold">General</span>
      </div>

      <div
        className={getCategoryClassName("Environment")}
        onClick={() => {
          // Navigation("/Insights-reporing/greensight/diagnosis/Environment/question/1");
          handleCategoryClick("Environment");
        }}
      >
        <span className="text-lg font-semibold">Environmental</span>
      </div>

      <div
        className={getCategoryClassName("Social")}
        onClick={() => {
          // Navigation("/Insights-reporing/greensight/diagnosis/Social/question/1");
          handleCategoryClick("Social");
        }}
      >
        <span className="text-lg font-semibold">Social</span>
      </div>

      <div
        className={getCategoryClassName("Governance")}
        onClick={() => {
          // Navigation("/Insights-reporing/greensight/diagnosis/Governance/question/1");
          handleCategoryClick("Governance");
        }}
      >
        <span className="text-lg font-semibold">Governance</span>
      </div>

      <div
        className={getCategoryClassName("Finalize")}
        onClick={() => {
          // Navigation("/Insights-reporing/greensight/diagnosis/Finalize/question/1");
          handleCategoryClick("Finalize");
        }}
      >
        <span className="text-lg font-semibold">Finalize</span>
      </div>
    </div>
  );
};

export default CategorySidebar;
