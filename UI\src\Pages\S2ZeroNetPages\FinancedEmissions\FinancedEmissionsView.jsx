import S2Layout from "@/Layout/S2Layout";
import { Tabs } from "@mantine/core";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import ProfileAnalysisView from "./Partials/ProfileAnalysis/ProfileAnalysisView";
import ListedEquityView from "./Partials/ListedEquity/ListedEquityView";
import BusinessLoansView from "./Partials/BusinessLoans/BusinessLoansView";
import RealEstateView from "./Partials/RealEstate/RealEstateView";
import ProjectFinanceView from "./Partials/ProjectFinance/ProjectFinanceView";
import SovereignBondsView from "./Partials/SovereignBonds/SovereignBondsView";
import AutoLoansView from "./Partials/AutoLoans/AutoLoansView";
import  {
    ProfileAnalytics, 
    ListedEquity, 
    ProjectFinance, 
    AutoLoans, 
    BusinessLoans,
    SovereignBonds, 
    RealEstate
} from "@/assets/svg/ImageSVG.jsx";
import { IoMdHome } from "react-icons/io";

const FinancedEmissionsView = () => {
  const { t } = useTranslation();

  const [activeTab, setActiveTab] = useState("ProfileAnalysis");
  return (
    <S2Layout navbarTitle={'Financed Emissions'}
    
    breadcrumbItems={[
      { title: <IoMdHome size={20} />, href: "/get-started" },
      { title: "Financed Emissions", href: "#" },
    ]}>
        <p className="-mt-8 mb-6 text-[#8F8F8F] text-sm">PCAF-aligned emission analysis</p>
      <div className="pb-8">
        <Tabs className="" defaultValue={activeTab} onChange={setActiveTab}>
          <Tabs.List
            className="flex flex-row justify-center gap-2 p-2 mb-6 rounded-xl shadow-lg md:justify-around md:flex-row before:hidden bg-white "
          >
            <Tabs.Tab
              value="ProfileAnalysis"
              className={`flex flex-row justify-start items-center text-base border-none outline-none rounded-lg font-normal hover:bg-transparent  ${
                activeTab === "ProfileAnalysis" ? "text-primary bg-[#E6F3F4]" : "text-[#8F8F8F] bg-[#ffffff]"
              } `}
            >
            <div className="flex flex-row items-center space-x-2">
              <ProfileAnalytics color={activeTab === "ProfileAnalysis" ? "#07838F" : "#8F8F8F"}/> 
              <span>{t("Profile Analysis")}</span>
            </div>
            </Tabs.Tab>

            <Tabs.Tab
                value="ListedEquity"
                className={`border-none outline-none rounded-lg font-normal hover:bg-transparent ${
                    activeTab === "ListedEquity" ? "text-primary bg-[#E6F3F4]" : "text-[#8F8F8F] bg-[#ffffff]"
                }`}
                >
                <div className="flex flex-row items-center space-x-2">
                    <ListedEquity color={activeTab === "ListedEquity" ? "#07838F" : "#8F8F8F"} />
                    <span>{t("Listed Equity")}</span>
                </div>
            </Tabs.Tab>

            <Tabs.Tab
              value="BusinessLoans"
              className={`flex flex-row text-base border-none outline-none rounded-lg font-normal hover:bg-transparent  ${
                activeTab === "BusinessLoans" ? "text-primary bg-[#E6F3F4]" : "text-[#8F8F8F] bg-[#ffffff]"
              } `}
            >
                <div className="flex flex-row items-center space-x-2">
                    <BusinessLoans color={activeTab === "BusinessLoans" ? "#07838F" : "#8F8F8F"} />
                    <span>{t("Business Loans")}</span>
                </div>
            </Tabs.Tab>
            
            <Tabs.Tab
              value="RealEstate"
              className={`flex flex-row text-base border-none outline-none rounded-lg font-normal hover:bg-transparent  ${
                activeTab === "RealEstate" ? "text-primary bg-[#E6F3F4]" : "text-[#8F8F8F] bg-[#ffffff]"
              } `}
            >
                <div className="flex flex-row items-center space-x-2">
                    <RealEstate color={activeTab === "RealEstate" ? "#07838F" : "#8F8F8F"} />
                    <span>{t("Real Estate")}</span>
                </div>
            </Tabs.Tab>
            
            <Tabs.Tab
              value="ProjectFinance"
              className={`flex flex-row text-base border-none outline-none rounded-lg font-normal hover:bg-transparent  ${
                activeTab === "ProjectFinance" ? "text-primary bg-[#E6F3F4]" : "text-[#8F8F8F] bg-[#ffffff]"
              } `}
            >
                <div className="flex flex-row items-center space-x-2">
                    <ProjectFinance color={activeTab === "ProjectFinance" ? "#07838F" : "#8F8F8F"} />
                    <span>{t("Project Finance")}</span>
                </div>
            </Tabs.Tab>
            
            <Tabs.Tab
              value="SovereignBonds"
              className={`flex flex-row text-base border-none outline-none rounded-lg font-normal hover:bg-transparent  ${
                activeTab === "SovereignBonds" ? "text-primary bg-[#E6F3F4]" : "text-[#8F8F8F] bg-[#ffffff]"
              } `}
            >
                <div className="flex flex-row items-center space-x-2">
                    <SovereignBonds color={activeTab === "SovereignBonds" ? "#07838F" : "#8F8F8F"} />
                    <span>{t("Sovereign Bonds")}</span>
                </div>
            </Tabs.Tab>
            
            <Tabs.Tab
              value="AutoLoans"
              className={`flex flex-row text-base border-none outline-none rounded-lg font-normal hover:bg-transparent  ${
                activeTab === "AutoLoans" ? "text-primary bg-[#E6F3F4]" : "text-[#8F8F8F] bg-[#ffffff]"
              } `}
            >
                <div className="flex flex-row items-center space-x-2">
                    <AutoLoans color={activeTab === "AutoLoans" ? "#07838F" : "#8F8F8F"} />
                    <span>{t("Auto Loans")}</span>
                </div>
            </Tabs.Tab> 
          </Tabs.List>

          <Tabs.Panel value="ProfileAnalysis">
            <ProfileAnalysisView />
          </Tabs.Panel>

          <Tabs.Panel value="ListedEquity">
            <ListedEquityView />
          </Tabs.Panel>

          <Tabs.Panel value="BusinessLoans">
            <BusinessLoansView />
          </Tabs.Panel>
          
          <Tabs.Panel value="RealEstate">
            <RealEstateView />
          </Tabs.Panel>
          
          <Tabs.Panel value="ProjectFinance">
            <ProjectFinanceView />
          </Tabs.Panel>
          
          <Tabs.Panel value="SovereignBonds">
            <SovereignBondsView />
          </Tabs.Panel>
          
          <Tabs.Panel value="AutoLoans">
            <AutoLoansView />
          </Tabs.Panel>
        </Tabs>
      </div>
    </S2Layout>
  );
};

export default FinancedEmissionsView;
