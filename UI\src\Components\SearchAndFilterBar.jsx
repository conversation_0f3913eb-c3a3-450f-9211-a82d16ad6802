import React from "react";
import SearchBox from "@/Components/SearchBox";
import { Select } from "@mantine/core";
import { FaAngleDown } from "react-icons/fa6";
import { CiFilter } from "react-icons/ci";
import { FaPlus } from "react-icons/fa6";

const SearchAndFilterBar = (props) => {
  const icon = <FaAngleDown className="text-black" />;
  return (
    <div className={`flex flex-col sm:flex sm:flex-row justify-between items-center ${props.className}`}>
      <SearchBox classNames={{ input: "border-none rounded-lg shadow-sm" }} />
      <div className="flex flex-col sm:flex sm:flex-row sm:items-center items-start">
        <div className="m-3 py-2 px-4 bg-white rounded-lg shadow-sm">
          <p className="font-semibold">Columns</p>
        </div>
        <div className="flex justify-start items-center m-3 bg-white p-2 rounded-lg shadow-sm">
          <CiFilter className="mx-2" />
          <FaPlus className="mx-2" />
          <p className="font-semibold mx-2">Filter</p>
        </div>
        <Select
          className="flex items-center bg-white font-semibold rounded-lg shadow-sm p-0.5"
          classNames={{
            input: "border-none w-28",
            dropdown: "border-none",
            label: "text-xs text-slate-400 w-11 ms-1",
          }}
          label="Sort by:"
          placeholder="Pick value"
          data={["Newest", "Oldest"]}
          defaultValue="Newest"
          rightSection={icon}
          allowDeselect={false}
        />
      </div>
    </div>
  );
};

export default SearchAndFilterBar;
