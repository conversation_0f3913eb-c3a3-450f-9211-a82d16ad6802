export const ClockIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={1.5}
      stroke="currentColor"
      className="size-6"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
      />
    </svg>
  );
};

export const AcademicCapIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={1.5}
      stroke="currentColor"
      className="size-6"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5"
      />
    </svg>
  );
};

export const StudentsIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={1.5}
      stroke="currentColor"
      className="size-6"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"
      />
    </svg>
  );
};

export const CheckListIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className="size-5 text-[#05808b]"
    >
      <path
        fillRule="evenodd"
        d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z"
        clipRule="evenodd"
      />
    </svg>
  );
};

export const ArrowRightIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className="size-6"
    >
      <path
        fillRule="evenodd"
        d="M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z"
        clipRule="evenodd"
      />
    </svg>
  );
};

export const ArrowDownIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className="size-6 hover:text-[#05808b]"
    >
      <path
        fillRule="evenodd"
        d="M12.53 16.28a.75.75 0 0 1-1.06 0l-7.5-7.5a.75.75 0 0 1 1.06-1.06L12 14.69l6.97-6.97a.75.75 0 1 1 1.06 1.06l-7.5 7.5Z"
        clipRule="evenodd"
      />
    </svg>
  );
};

export const PlayVideoIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={1.5}
      stroke="currentColor"
      className="size-6 text-[#05808b]"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
      />
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M15.91 11.672a.375.375 0 0 1 0 .656l-5.603 3.113a.375.375 0 0 1-.557-.328V8.887c0-.286.307-.466.557-.327l5.603 3.112Z"
      />
    </svg>
  );
};

export const BookIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={1.5}
      stroke="currentColor"
      className="size-12 text-center  text-[#05808b] rounded-full"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"
      />
    </svg>
  );
};

export const CloseIcon = ({ onClick }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={1.5}
      stroke="currentColor"
      className="size-6 cursor-pointer"
      onClick={onClick}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
      />
    </svg>
  );
};

export const ArrowLeftIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={1.5}
      stroke="currentColor"
      className="size-6"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M15.75 19.5 8.25 12l7.5-7.5"
      />
    </svg>
  );
};

export const ESGDangerIcon = ({ active }) => {
  return (
    <svg
      className={`size-7`}
      viewBox="0 0 50 50"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M45.341 33.1719L32.0076 9.17188C30.216 5.94271 27.7368 4.17188 25.0076 4.17188C22.2785 4.17188 19.7993 5.94271 18.0076 9.17188L4.67431 33.1719C2.98681 36.2344 2.79931 39.1719 4.15348 41.4844C5.50764 43.7969 8.17431 45.0677 11.6743 45.0677H38.341C41.841 45.0677 44.5076 43.7969 45.8618 41.4844C47.216 39.1719 47.0285 36.2135 45.341 33.1719ZM23.4451 18.7552C23.4451 17.901 24.1535 17.1927 25.0076 17.1927C25.8618 17.1927 26.5701 17.901 26.5701 18.7552V29.1719C26.5701 30.026 25.8618 30.7344 25.0076 30.7344C24.1535 30.7344 23.4451 30.026 23.4451 29.1719V18.7552ZM26.4868 36.901C26.3826 36.9844 26.2785 37.0677 26.1743 37.151C26.0493 37.2344 25.9243 37.2969 25.7993 37.3385C25.6743 37.401 25.5493 37.4427 25.4035 37.4635C25.2785 37.4844 25.1326 37.5052 25.0076 37.5052C24.8826 37.5052 24.7368 37.4844 24.591 37.4635C24.466 37.4427 24.341 37.401 24.216 37.3385C24.091 37.2969 23.966 37.2344 23.841 37.151C23.7368 37.0677 23.6326 36.9844 23.5285 36.901C23.1535 36.5052 22.9243 35.9635 22.9243 35.4219C22.9243 34.8802 23.1535 34.3385 23.5285 33.9427C23.6326 33.8594 23.7368 33.776 23.841 33.6927C23.966 33.6094 24.091 33.5469 24.216 33.5052C24.341 33.4427 24.466 33.401 24.591 33.3802C24.8618 33.3177 25.1535 33.3177 25.4035 33.3802C25.5493 33.401 25.6743 33.4427 25.7993 33.5052C25.9243 33.5469 26.0493 33.6094 26.1743 33.6927C26.2785 33.776 26.3826 33.8594 26.4868 33.9427C26.8618 34.3385 27.091 34.8802 27.091 35.4219C27.091 35.9635 26.8618 36.5052 26.4868 36.901Z"
        fill={active ? "url(#paint0_linear_40002321_27545)" : "#8F8F8F"}
      />
      <defs>
        <linearGradient
          id="paint0_linear_40002321_27545"
          x1="2.44015"
          y1="51.0972"
          x2="55.5585"
          y2="46.1709"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5085" />
          <stop offset="0.433104" stopColor="#236D8B" />
          <stop offset="1" stopColor="#12B6AD" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const ESGTaskIcon = ({ active }) => {
  return (
    <svg
      className={`size-7`}
      viewBox="0 0 50 50"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M33.7266 4.16406H16.2682C8.6849 4.16406 4.16406 8.6849 4.16406 16.2682V33.7266C4.16406 41.3099 8.6849 45.8307 16.2682 45.8307H33.7266C41.3099 45.8307 45.8307 41.3099 45.8307 33.7266V16.2682C45.8307 8.6849 41.3099 4.16406 33.7266 4.16406ZM20.7682 31.0391L16.0807 35.7266C15.7682 36.0391 15.3724 36.1849 14.9766 36.1849C14.5807 36.1849 14.1641 36.0391 13.8724 35.7266L12.3099 34.1641C11.6849 33.5599 11.6849 32.5599 12.3099 31.9557C12.9141 31.3516 13.8932 31.3516 14.5182 31.9557L14.9766 32.4141L18.5599 28.8307C19.1641 28.2266 20.1432 28.2266 20.7682 28.8307C21.3724 29.4349 21.3724 30.4349 20.7682 31.0391ZM20.7682 16.4557L16.0807 21.1432C15.7682 21.4557 15.3724 21.6016 14.9766 21.6016C14.5807 21.6016 14.1641 21.4557 13.8724 21.1432L12.3099 19.5807C11.6849 18.9766 11.6849 17.9766 12.3099 17.3724C12.9141 16.7682 13.8932 16.7682 14.5182 17.3724L14.9766 17.8307L18.5599 14.2474C19.1641 13.6432 20.1432 13.6432 20.7682 14.2474C21.3724 14.8516 21.3724 15.8516 20.7682 16.4557ZM36.5807 34.6224H25.6432C24.7891 34.6224 24.0807 33.9141 24.0807 33.0599C24.0807 32.2057 24.7891 31.4974 25.6432 31.4974H36.5807C37.4557 31.4974 38.1432 32.2057 38.1432 33.0599C38.1432 33.9141 37.4557 34.6224 36.5807 34.6224ZM36.5807 20.0391H25.6432C24.7891 20.0391 24.0807 19.3307 24.0807 18.4766C24.0807 17.6224 24.7891 16.9141 25.6432 16.9141H36.5807C37.4557 16.9141 38.1432 17.6224 38.1432 18.4766C38.1432 19.3307 37.4557 20.0391 36.5807 20.0391Z"
        fill={active ? "url(#paint0_linear_40002321_27586)" : "#8F8F8F"}
      />
      <defs>
        <linearGradient
          id="paint0_linear_40002321_27586"
          x1="3.38085"
          y1="51.9739"
          x2="54.3114"
          y2="47.5332"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5085" />
          <stop offset="0.433104" stopColor="#236D8B" />
          <stop offset="1" stopColor="#12B6AD" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const ESGShieldIcon = ({ active }) => {
  return (
    <svg
      className={`size-7`}
      viewBox="0 0 50 50"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M40.5182 14.4609V19.6901C40.5182 21.1693 39.0182 22.1276 37.6224 21.6068C35.8724 20.9609 33.9349 20.7109 31.8932 20.9193C26.9349 21.4609 21.8516 26.2318 21.0182 31.1693C20.3307 35.2734 21.6432 39.1068 24.1641 41.8151C25.3099 43.0651 24.5391 45.0859 22.8516 45.2734C21.4141 45.4401 19.9974 45.3984 19.2057 44.8151L7.7474 36.2526C6.39323 35.2318 5.28906 33.0234 5.28906 31.3151V14.4609C5.28906 12.1068 7.08073 9.52344 9.26823 8.6901L20.7266 4.39844C21.9141 3.96094 23.8724 3.96094 25.0599 4.39844L36.5182 8.6901C38.7266 9.52344 40.5182 12.1068 40.5182 14.4609Z"
        fill={active ? "url(#paint0_linear_40002321_27592)" : "#8F8F8F"}
      />
      <path
        d="M33.3359 23.9766C28.1693 23.9766 23.9609 28.1849 23.9609 33.3516C23.9609 38.5182 28.1693 42.7266 33.3359 42.7266C38.5026 42.7266 42.7109 38.5182 42.7109 33.3516C42.7109 28.1641 38.5026 23.9766 33.3359 23.9766Z"
        fill={active ? "url(#paint1_linear_40002321_27592)" : "#8F8F8F"}
      />
      <path
        d="M43.7474 45.8326C43.1849 45.8326 42.6641 45.6034 42.2682 45.2284C42.1849 45.1242 42.0807 45.0201 42.0182 44.8951C41.9349 44.7909 41.8724 44.6659 41.8307 44.5409C41.7682 44.4159 41.7266 44.2909 41.7057 44.1659C41.6849 44.0201 41.6641 43.8951 41.6641 43.7492C41.6641 43.4784 41.7266 43.2076 41.8307 42.9576C41.9349 42.6867 42.0807 42.4784 42.2682 42.2701C42.7474 41.7909 43.4766 41.5617 44.1432 41.7076C44.2891 41.7284 44.4141 41.7701 44.5391 41.8326C44.6641 41.8742 44.7891 41.9367 44.8932 42.0201C45.0182 42.0826 45.1224 42.1867 45.2266 42.2701C45.4141 42.4784 45.5599 42.6867 45.6641 42.9576C45.7682 43.2076 45.8307 43.4784 45.8307 43.7492C45.8307 44.2909 45.6016 44.8326 45.2266 45.2284C45.1224 45.3117 45.0182 45.3951 44.8932 45.4784C44.7891 45.5617 44.6641 45.6242 44.5391 45.6659C44.4141 45.7284 44.2891 45.7701 44.1432 45.7909C44.0182 45.8117 43.8724 45.8326 43.7474 45.8326Z"
        fill={active ? "url(#paint2_linear_40002321_27592)" : "#8F8F8F"}
      />
      <defs>
        <linearGradient
          id="paint0_linear_40002321_27592"
          x1="4.62686"
          y1="51.4459"
          x2="47.7771"
          y2="48.2357"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5085" />
          <stop offset="0.433104" stopColor="#236D8B" />
          <stop offset="1" stopColor="#12B6AD" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_40002321_27592"
          x1="23.6085"
          y1="45.491"
          x2="46.5272"
          y2="43.4927"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5085" />
          <stop offset="0.433104" stopColor="#236D8B" />
          <stop offset="1" stopColor="#12B6AD" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_40002321_27592"
          x1="41.5857"
          y1="46.4472"
          x2="46.6788"
          y2="46.0033"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5085" />
          <stop offset="0.433104" stopColor="#236D8B" />
          <stop offset="1" stopColor="#12B6AD" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const ESGStatusUpIcon = ({ active }) => {
  return (
    <svg
      className={`size-7`}
      viewBox="0 0 50 50"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M33.7266 4.16406H16.2682C8.6849 4.16406 4.16406 8.6849 4.16406 16.2682V33.7057C4.16406 41.3099 8.6849 45.8307 16.2682 45.8307H33.7057C41.2891 45.8307 45.8099 41.3099 45.8099 33.7266V16.2682C45.8307 8.6849 41.3099 4.16406 33.7266 4.16406ZM15.8932 37.8099C15.8932 38.6641 15.1849 39.3724 14.3307 39.3724C13.4766 39.3724 12.7682 38.6641 12.7682 37.8099V33.4974C12.7682 32.6432 13.4766 31.9349 14.3307 31.9349C15.1849 31.9349 15.8932 32.6432 15.8932 33.4974V37.8099ZM26.5599 37.8099C26.5599 38.6641 25.8516 39.3724 24.9974 39.3724C24.1432 39.3724 23.4349 38.6641 23.4349 37.8099V29.1641C23.4349 28.3099 24.1432 27.6016 24.9974 27.6016C25.8516 27.6016 26.5599 28.3099 26.5599 29.1641V37.8099ZM37.2266 37.8099C37.2266 38.6641 36.5182 39.3724 35.6641 39.3724C34.8099 39.3724 34.1016 38.6641 34.1016 37.8099V24.8516C34.1016 23.9974 34.8099 23.2891 35.6641 23.2891C36.5182 23.2891 37.2266 23.9974 37.2266 24.8516V37.8099ZM37.2266 18.2682C37.2266 19.1224 36.5182 19.8307 35.6641 19.8307C34.8099 19.8307 34.1016 19.1224 34.1016 18.2682V16.2474C28.7891 21.7057 22.1432 25.5599 14.7057 27.4141C14.5807 27.4557 14.4557 27.4557 14.3307 27.4557C13.6224 27.4557 12.9974 26.9766 12.8099 26.2682C12.6016 25.4349 13.1016 24.5807 13.9557 24.3724C20.9766 22.6224 27.2266 18.9349 32.1849 13.7266H29.5807C28.7266 13.7266 28.0182 13.0182 28.0182 12.1641C28.0182 11.3099 28.7266 10.6016 29.5807 10.6016H35.6849C35.7682 10.6016 35.8307 10.6432 35.9141 10.6432C36.0182 10.6641 36.1224 10.6641 36.2266 10.7057C36.3307 10.7474 36.4141 10.8099 36.5182 10.8724C36.5807 10.9141 36.6432 10.9349 36.7057 10.9766C36.7266 10.9974 36.7266 11.0182 36.7474 11.0182C36.8307 11.1016 36.8932 11.1849 36.9557 11.2682C37.0182 11.3516 37.0807 11.4141 37.1016 11.4974C37.1432 11.5807 37.1432 11.6641 37.1641 11.7682C37.1849 11.8724 37.2266 11.9766 37.2266 12.1016C37.2266 12.1224 37.2474 12.1432 37.2474 12.1641V18.2682H37.2266Z"
        fill={active ? "url(#paint0_linear_40002321_27599)" : "#8F8F8F"}
      />
      <defs>
        <linearGradient
          id="paint0_linear_40002321_27599"
          x1="3.38124"
          y1="51.9739"
          x2="54.2868"
          y2="47.5376"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5085" />
          <stop offset="0.433104" stopColor="#236D8B" />
          <stop offset="1" stopColor="#12B6AD" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const ESGClipboardIcon = ({ active }) => {
  return (
    <svg
      className={`size-6`}
      viewBox="0 0 50 50"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M29.8932 4.16406H20.1016C17.9349 4.16406 16.1641 5.91406 16.1641 8.08073V10.0391C16.1641 12.2057 17.9141 13.9557 20.0807 13.9557H29.8932C32.0599 13.9557 33.8099 12.2057 33.8099 10.0391V8.08073C33.8307 5.91406 32.0599 4.16406 29.8932 4.16406Z"
        fill={active ? "url(#paint0_linear_40002888_2730)" : "#8F8F8F"}
      />
      <path
        d="M35.9141 10.0442C35.9141 13.3567 33.2057 16.065 29.8932 16.065H20.1016C16.7891 16.065 14.0807 13.3567 14.0807 10.0442C14.0807 8.87752 12.8307 8.14835 11.7891 8.69002C8.85156 10.2525 6.85156 13.3567 6.85156 16.9192V36.5234C6.85156 41.6484 11.0391 45.8359 16.1641 45.8359H33.8307C38.9557 45.8359 43.1432 41.6484 43.1432 36.5234V16.9192C43.1432 13.3567 41.1432 10.2525 38.2057 8.69002C37.1641 8.14835 35.9141 8.87752 35.9141 10.0442ZM31.9557 26.5234L23.6224 34.8567C23.3099 35.1692 22.9141 35.315 22.5182 35.315C22.1224 35.315 21.7266 35.1692 21.4141 34.8567L18.2891 31.7317C17.6849 31.1275 17.6849 30.1275 18.2891 29.5234C18.8932 28.9192 19.8932 28.9192 20.4974 29.5234L22.5182 31.5442L29.7474 24.315C30.3516 23.7109 31.3516 23.7109 31.9557 24.315C32.5599 24.9192 32.5599 25.9192 31.9557 26.5234Z"
        fill={active ? "url(#paint1_linear_40002888_2730)" : "#8F8F8F"}
      />
      <defs>
        <linearGradient
          id="paint0_linear_40002888_2730"
          x1="15.8324"
          y1="15.3994"
          x2="37.042"
          y2="12.0667"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5085" />
          <stop offset="0.433104" stopColor="#236D8B" />
          <stop offset="1" stopColor="#12B6AD" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_40002888_2730"
          x1="6.16939"
          y1="51.3393"
          x2="50.5482"
          y2="47.5774"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5085" />
          <stop offset="0.433104" stopColor="#236D8B" />
          <stop offset="1" stopColor="#12B6AD" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const ESGShieldCheckIcon = ({ active }) => {
  return (
    <svg
      className={`size-7`}
      viewBox="0 0 50 50"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M38.6276 8.58073L27.1693 4.28906C25.9818 3.85156 24.0443 3.85156 22.8568 4.28906L11.3984 8.58073C9.1901 9.41406 7.39844 11.9974 7.39844 14.3516V31.2266C7.39844 32.9141 8.5026 35.1432 9.85677 36.1432L21.3151 44.7057C23.3359 46.2266 26.6484 46.2266 28.6693 44.7057L40.1276 36.1432C41.4818 35.1224 42.5859 32.9141 42.5859 31.2266V14.3516C42.6068 11.9974 40.8151 9.41406 38.6276 8.58073ZM32.2526 20.2474L23.2943 29.2057C22.9818 29.5182 22.5859 29.6641 22.1901 29.6641C21.7943 29.6641 21.3984 29.5182 21.0859 29.2057L17.7526 25.8307C17.1484 25.2266 17.1484 24.2266 17.7526 23.6224C18.3568 23.0182 19.3568 23.0182 19.9609 23.6224L22.2109 25.8724L30.0651 18.0182C30.6693 17.4141 31.6693 17.4141 32.2734 18.0182C32.8776 18.6224 32.8776 19.6432 32.2526 20.2474Z"
        fill={active ? "url(#paint0_linear_40002321_27612)" : "#5A5A5A"}
      />
      <defs>
        <linearGradient
          id="paint0_linear_40002321_27612"
          x1="6.73701"
          y1="52.0218"
          x2="49.8437"
          y2="48.8643"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5085" />
          <stop offset="0.433104" stopColor="#236D8B" />
          <stop offset="1" stopColor="#12B6AD" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const ClipboardText = () => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.3556 2H9.65562C8.61562 2 7.76562 2.84 7.76562 3.88V4.82C7.76562 5.86 8.60563 6.7 9.64563 6.7H14.3556C15.3956 6.7 16.2356 5.86 16.2356 4.82V3.88C16.2456 2.84 15.3956 2 14.3556 2Z"
        fill="white"
      />
      <path
        d="M17.2391 4.81949C17.2391 6.40949 15.9391 7.70949 14.3491 7.70949H9.64906C8.05906 7.70949 6.75906 6.40949 6.75906 4.81949C6.75906 4.25949 6.15906 3.90949 5.65906 4.16949C4.24906 4.91949 3.28906 6.40949 3.28906 8.11949V17.5295C3.28906 19.9895 5.29906 21.9995 7.75906 21.9995H16.2391C18.6991 21.9995 20.7091 19.9895 20.7091 17.5295V8.11949C20.7091 6.40949 19.7491 4.91949 18.3391 4.16949C17.8391 3.90949 17.2391 4.25949 17.2391 4.81949ZM12.3791 16.9495H7.99906C7.58906 16.9495 7.24906 16.6095 7.24906 16.1995C7.24906 15.7895 7.58906 15.4495 7.99906 15.4495H12.3791C12.7891 15.4495 13.1291 15.7895 13.1291 16.1995C13.1291 16.6095 12.7891 16.9495 12.3791 16.9495ZM14.9991 12.9495H7.99906C7.58906 12.9495 7.24906 12.6095 7.24906 12.1995C7.24906 11.7895 7.58906 11.4495 7.99906 11.4495H14.9991C15.4091 11.4495 15.7491 11.7895 15.7491 12.1995C15.7491 12.6095 15.4091 12.9495 14.9991 12.9495Z"
        fill="white"
      />
    </svg>
  );
};

export const ESGCircleRightArrow = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={1.5}
      stroke="currentColor"
      className="size-9 font-normal"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
      />
    </svg>
  );
};

export const ESGAuditSearchOutlineIcon = () => {
  return (
    <svg
      width="29"
      height="29"
      viewBox="0 0 29 29"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17.0781 6.14844H24.0781"
        stroke="#07838F"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17.0781 9.64844H20.5781"
        stroke="#07838F"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M25.2448 13.7318C25.2448 19.8568 20.2865 24.8151 14.1615 24.8151C8.03646 24.8151 3.07812 19.8568 3.07812 13.7318C3.07812 7.60677 8.03646 2.64844 14.1615 2.64844"
        stroke="#07838F"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M26.4115 25.9818L24.0781 23.6484"
        stroke="#07838F"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const ESGAuditSearchSolidIcon = () => {
  return (
    <svg
      width="29"
      height="29"
      viewBox="0 0 29 29"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.1615 2.64844C8.04813 2.64844 3.07812 7.61844 3.07812 13.7318C3.07812 19.8451 8.04813 24.8151 14.1615 24.8151C20.2748 24.8151 25.2448 19.8451 25.2448 13.7318C25.2448 7.61844 20.2748 2.64844 14.1615 2.64844ZM14.1615 16.3568H10.6615C10.1831 16.3568 9.78646 15.9601 9.78646 15.4818C9.78646 15.0034 10.1831 14.6068 10.6615 14.6068H14.1615C14.6398 14.6068 15.0365 15.0034 15.0365 15.4818C15.0365 15.9601 14.6398 16.3568 14.1615 16.3568ZM17.6615 12.8568H10.6615C10.1831 12.8568 9.78646 12.4601 9.78646 11.9818C9.78646 11.5034 10.1831 11.1068 10.6615 11.1068H17.6615C18.1398 11.1068 18.5365 11.5034 18.5365 11.9818C18.5365 12.4601 18.1398 12.8568 17.6615 12.8568Z"
        fill="#07838F"
      />
      <path
        d="M25.5951 25.9743C25.3851 25.9743 25.1751 25.8926 25.0234 25.7409L22.8534 23.5709C22.5384 23.2559 22.5384 22.7426 22.8534 22.4159C23.1684 22.1009 23.6818 22.1009 24.0084 22.4159L26.1784 24.5859C26.4934 24.9009 26.4934 25.4143 26.1784 25.7409C26.0151 25.8926 25.8051 25.9743 25.5951 25.9743Z"
        fill="#07838F"
      />
    </svg>
  );
};

export const ESGAuditTaskSquareSolidIcon = () => {
  return (
    <svg
      width="28"
      height="28"
      viewBox="0 0 28 28"
      fill="#07838F"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M18.8909 2.33594H9.11427C4.8676 2.33594 2.33594 4.8676 2.33594 9.11427V18.8909C2.33594 23.1376 4.8676 25.6693 9.11427 25.6693H18.8909C23.1376 25.6693 25.6693 23.1376 25.6693 18.8909V9.11427C25.6693 4.8676 23.1376 2.33594 18.8909 2.33594ZM11.6343 17.3859L9.00927 20.0109C8.83427 20.1859 8.6126 20.2676 8.39094 20.2676C8.16927 20.2676 7.93594 20.1859 7.7726 20.0109L6.8976 19.1359C6.5476 18.7976 6.5476 18.2376 6.8976 17.8993C7.23594 17.5609 7.78427 17.5609 8.13427 17.8993L8.39094 18.1559L10.3976 16.1493C10.7359 15.8109 11.2843 15.8109 11.6343 16.1493C11.9726 16.4876 11.9726 17.0476 11.6343 17.3859ZM11.6343 9.21927L9.00927 11.8443C8.83427 12.0193 8.6126 12.1009 8.39094 12.1009C8.16927 12.1009 7.93594 12.0193 7.7726 11.8443L6.8976 10.9693C6.5476 10.6309 6.5476 10.0709 6.8976 9.7326C7.23594 9.39427 7.78427 9.39427 8.13427 9.7326L8.39094 9.98927L10.3976 7.9826C10.7359 7.64427 11.2843 7.64427 11.6343 7.9826C11.9726 8.32094 11.9726 8.88094 11.6343 9.21927ZM20.4893 19.3926H14.3643C13.8859 19.3926 13.4893 18.9959 13.4893 18.5176C13.4893 18.0393 13.8859 17.6426 14.3643 17.6426H20.4893C20.9793 17.6426 21.3643 18.0393 21.3643 18.5176C21.3643 18.9959 20.9793 19.3926 20.4893 19.3926ZM20.4893 11.2259H14.3643C13.8859 11.2259 13.4893 10.8293 13.4893 10.3509C13.4893 9.8726 13.8859 9.47594 14.3643 9.47594H20.4893C20.9793 9.47594 21.3643 9.8726 21.3643 10.3509C21.3643 10.8293 20.9793 11.2259 20.4893 11.2259Z" />
    </svg>
  );
};

export const ESGAuditTaskSquareOutlineIcon = () => {
  return (
    <svg
      width="28"
      height="28"
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.4297 10.3594H20.5547"
        stroke="#07838F"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.44531 10.3594L8.32031 11.2344L10.9453 8.60938"
        stroke="#07838F"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14.4297 18.5234H20.5547"
        stroke="#07838F"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.44531 18.5234L8.32031 19.3984L10.9453 16.7734"
        stroke="#07838F"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.5026 25.6693H17.5026C23.3359 25.6693 25.6693 23.3359 25.6693 17.5026V10.5026C25.6693 4.66927 23.3359 2.33594 17.5026 2.33594H10.5026C4.66927 2.33594 2.33594 4.66927 2.33594 10.5026V17.5026C2.33594 23.3359 4.66927 25.6693 10.5026 25.6693Z"
        stroke="#07838F"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const ESGAuditFileAddIcon = () => {
  return (
    <svg
      width="25"
      height="24"
      viewBox="0 0 25 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.9609 2H14.2337C17.4949 2 19.1255 2 20.2579 2.79784C20.5823 3.02643 20.8704 3.29752 21.1132 3.60289C21.9609 4.66867 21.9609 6.20336 21.9609 9.27273V11.8182C21.9609 14.7814 21.9609 16.2629 21.492 17.4462C20.7381 19.3486 19.1438 20.8491 17.1226 21.5586C15.8653 22 14.2911 22 11.1428 22C9.34369 22 8.44415 22 7.72571 21.7478C6.57073 21.3423 5.65969 20.4849 5.2289 19.3979C4.96094 18.7217 4.96094 17.8751 4.96094 16.1818V12"
        stroke="#07838F"
        strokeWidth="1.4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M21.9531 12C21.9531 13.8409 20.4607 15.3333 18.6198 15.3333C17.954 15.3333 17.1691 15.2167 16.5218 15.3901C15.9466 15.5442 15.4974 15.9935 15.3432 16.5686C15.1698 17.216 15.2865 18.0009 15.2865 18.6667C15.2865 20.5076 13.7941 22 11.9531 22"
        stroke="#07838F"
        strokeWidth="1.4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.9531 6L3.95312 6M7.95312 2V10"
        stroke="#07838F"
        strokeWidth="1.4"
        strokeLinecap="round"
      />
    </svg>
  );
};

export const ESGAuditCalenderIcon = () => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8 2V5"
        stroke="#B6B5BF"
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16 2V5"
        stroke="#B6B5BF"
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.5 9.09375H20.5"
        stroke="#B6B5BF"
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M21 8.5V17C21 20 19.5 22 16 22H8C4.5 22 3 20 3 17V8.5C3 5.5 4.5 3.5 8 3.5H16C19.5 3.5 21 5.5 21 8.5Z"
        stroke="#B6B5BF"
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.9945 13.7031H12.0035"
        stroke="#B6B5BF"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.29138 13.7031H8.30036"
        stroke="#B6B5BF"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.29138 16.7031H8.30036"
        stroke="#B6B5BF"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const ESGImpactSolidIcon = () => {
  return (
    <svg
      width="29"
      height="28"
      viewBox="0 0 29 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.6188 9.44702C22.4232 12.2514 22.4232 16.8027 19.6188 19.6071C16.8144 22.4115 12.2631 22.4115 9.45874 19.6071C6.65438 16.8027 6.65438 12.2514 9.45874 9.44702C12.2631 6.64266 16.8144 6.64266 19.6188 9.44702Z"
        fill="#07838F"
      />
      <path
        d="M10.2284 26.0401C10.125 26.0401 10.01 26.0171 9.90658 25.9827C7.32059 24.9483 5.22881 23.1208 3.82663 20.7072C2.47042 18.3511 1.93024 15.6847 2.28653 12.9723C2.344 12.501 2.79223 12.1677 3.25197 12.2252C3.72319 12.2827 4.0565 12.7194 3.99903 13.1906C3.7002 15.5238 4.15994 17.8224 5.32076 19.8452C6.51606 21.914 8.32051 23.4886 10.5387 24.3736C10.9755 24.5575 11.1938 25.0517 11.0214 25.4999C10.895 25.8332 10.5617 26.0401 10.2284 26.0401Z"
        fill="#07838F"
      />
      <path
        d="M7.46972 6.32463C7.21687 6.32463 6.96401 6.2097 6.79161 5.99133C6.49279 5.62354 6.56175 5.08336 6.94103 4.78453C9.12475 3.07203 11.7567 2.16406 14.5381 2.16406C17.2505 2.16406 19.8365 3.03755 22.0087 4.69259C22.388 4.97992 22.457 5.5201 22.1696 5.89938C21.8823 6.27866 21.3421 6.34762 20.9628 6.06029C19.0894 4.63512 16.8712 3.88806 14.5381 3.88806C12.1475 3.88806 9.88331 4.6696 7.99841 6.14074C7.8375 6.26717 7.65361 6.32463 7.46972 6.32463Z"
        fill="#07838F"
      />
      <path
        d="M18.8494 26.0443C18.5046 26.0443 18.1828 25.8374 18.0449 25.5041C17.8725 25.0673 18.0794 24.5616 18.5276 24.3777C20.7458 23.4813 22.5502 21.9182 23.7455 19.8494C24.9179 17.8266 25.3776 15.5279 25.0673 13.2063C25.0098 12.735 25.3431 12.2983 25.8143 12.2408C26.2741 12.1834 26.7223 12.5167 26.7798 12.9879C27.1246 15.6888 26.5959 18.3667 25.2397 20.7229C23.849 23.1365 21.7457 24.9524 19.1597 25.9983C19.0678 26.0213 18.9643 26.0443 18.8494 26.0443Z"
        fill="#07838F"
      />
    </svg>
  );
};

export const ESGFinancialSolidIcon = () => {
  return (
    <svg
      width="28"
      height="28"
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M22.4579 7.94528C21.9637 5.45124 20.1133 4.35938 17.5388 4.35938H7.44767C4.41344 4.35938 2.39062 5.87649 2.39062 9.41642V15.3355C2.39062 17.887 3.43651 19.3811 5.16051 20.0247C5.41336 20.1167 5.6892 20.1971 5.97653 20.2431C6.43626 20.3465 6.93047 20.3925 7.44767 20.3925H17.5503C20.5845 20.3925 22.6073 18.8754 22.6073 15.3355V9.41642C22.6073 8.87624 22.5613 8.39352 22.4579 7.94528ZM6.78106 14.1057C6.78106 14.5769 6.39029 14.9677 5.91906 14.9677C5.44784 14.9677 5.05707 14.5769 5.05707 14.1057V10.6577C5.05707 10.1865 5.44784 9.7957 5.91906 9.7957C6.39029 9.7957 6.78106 10.1865 6.78106 10.6577V14.1057ZM12.4932 15.4159C10.8152 15.4159 9.459 14.0597 9.459 12.3817C9.459 10.7037 10.8152 9.34746 12.4932 9.34746C14.1712 9.34746 15.5275 10.7037 15.5275 12.3817C15.5275 14.0597 14.1712 15.4159 12.4932 15.4159ZM19.9179 14.1057C19.9179 14.5769 19.5271 14.9677 19.0559 14.9677C18.5847 14.9677 18.1939 14.5769 18.1939 14.1057V10.6577C18.1939 10.1865 18.5847 9.7957 19.0559 9.7957C19.5271 9.7957 19.9179 10.1865 19.9179 10.6577V14.1057Z"
        fill="#07838F"
      />
      <path
        d="M26.0558 12.8666V18.7856C26.0558 22.3255 24.033 23.8541 20.9873 23.8541H10.8962C10.0342 23.8541 9.26415 23.7277 8.59754 23.4749C8.05735 23.2795 7.58613 22.9921 7.20685 22.6244C6.99997 22.429 7.16087 22.1187 7.44821 22.1187H17.5393C21.7918 22.1187 24.3204 19.5901 24.3204 15.3491V9.41857C24.3204 9.14273 24.6307 8.97034 24.8261 9.17721C25.6076 10.0047 26.0558 11.2115 26.0558 12.8666Z"
        fill="#07838F"
      />
    </svg>
  );
};

export const ESGRiskSolidIcon = () => {
  return (
    <svg
      width="28"
      height="28"
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M25.1109 18.608L17.7552 5.36776C16.7668 3.5863 15.3991 2.60938 13.8934 2.60938C12.3878 2.60938 11.0201 3.5863 10.0317 5.36776L2.67598 18.608C1.74503 20.2975 1.64159 21.9181 2.38865 23.1939C3.13571 24.4696 4.60685 25.1707 6.53773 25.1707H21.2491C23.18 25.1707 24.6511 24.4696 25.3982 23.1939C26.1453 21.9181 26.0418 20.2861 25.1109 18.608ZM13.0314 10.6547C13.0314 10.1835 13.4222 9.79268 13.8934 9.79268C14.3647 9.79268 14.7554 10.1835 14.7554 10.6547V16.4013C14.7554 16.8725 14.3647 17.2633 13.8934 17.2633C13.4222 17.2633 13.0314 16.8725 13.0314 16.4013V10.6547ZM14.7095 20.6653C14.652 20.7113 14.5945 20.7573 14.5371 20.8033C14.4681 20.8492 14.3991 20.8837 14.3302 20.9067C14.2612 20.9412 14.1923 20.9642 14.1118 20.9756C14.0428 20.9871 13.9624 20.9986 13.8934 20.9986C13.8245 20.9986 13.744 20.9871 13.6636 20.9756C13.5946 20.9642 13.5256 20.9412 13.4567 20.9067C13.3877 20.8837 13.3188 20.8492 13.2498 20.8033C13.1923 20.7573 13.1349 20.7113 13.0774 20.6653C12.8705 20.447 12.7441 20.1481 12.7441 19.8493C12.7441 19.5505 12.8705 19.2517 13.0774 19.0333C13.1349 18.9873 13.1923 18.9413 13.2498 18.8954C13.3188 18.8494 13.3877 18.8149 13.4567 18.7919C13.5256 18.7574 13.5946 18.7345 13.6636 18.723C13.813 18.6885 13.9739 18.6885 14.1118 18.723C14.1923 18.7345 14.2612 18.7574 14.3302 18.7919C14.3991 18.8149 14.4681 18.8494 14.5371 18.8954C14.5945 18.9413 14.652 18.9873 14.7095 19.0333C14.9163 19.2517 15.0428 19.5505 15.0428 19.8493C15.0428 20.1481 14.9163 20.447 14.7095 20.6653Z"
        fill="#07838F"
      />
    </svg>
  );
};

export const ESGNotesSolidIcon = () => {
  return (
    <svg
      width="29"
      height="28"
      viewBox="0 0 29 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.3547 23.2469C15.6705 23.3204 15.6994 23.7339 15.3918 23.8365V23.8365L13.5759 24.4341C9.01305 25.9053 6.61095 24.6755 5.12832 20.1126L3.65718 15.5728C2.18604 11.01 3.40432 8.59636 7.96716 7.12522L8.80295 6.84843C9.17337 6.72576 9.53063 7.09605 9.41966 7.47014C9.32964 7.77358 9.24474 8.09536 9.16246 8.43546L8.03612 13.2511C6.77186 18.6645 8.62228 21.6527 14.0356 22.94L15.3547 23.2469Z"
        fill="#07838F"
      />
      <path
        d="M20.5139 4.00185L18.5945 3.55361C14.7557 2.64564 12.4686 3.3927 11.1238 6.17408C10.779 6.87517 10.5032 7.72567 10.2733 8.7026L9.147 13.5183C8.02066 18.3225 9.50329 20.6901 14.296 21.8279L16.2269 22.2877C16.8935 22.4486 17.5141 22.552 18.0888 22.598C21.6747 22.9428 23.5826 21.2648 24.548 17.1157L25.6743 12.3115C26.8007 7.5073 25.3295 5.12819 20.5139 4.00185ZM18.3531 15.6331C18.2497 16.0238 17.9049 16.2767 17.5141 16.2767C17.4452 16.2767 17.3762 16.2652 17.2957 16.2537L13.9512 15.4032C13.4915 15.2883 13.2156 14.817 13.3306 14.3573C13.4455 13.8976 13.9167 13.6217 14.3764 13.7367L17.721 14.5872C18.1922 14.7021 18.4681 15.1733 18.3531 15.6331ZM21.7207 11.7483C21.6172 12.1391 21.2724 12.392 20.8816 12.392C20.8127 12.392 20.7437 12.3805 20.6633 12.369L15.089 10.9553C14.6293 10.8404 14.3535 10.3691 14.4684 9.9094C14.5833 9.44967 15.0545 9.17383 15.5143 9.28876L21.0885 10.7024C21.5597 10.8059 21.8356 11.2771 21.7207 11.7483Z"
        fill="#07838F"
      />
    </svg>
  );
};

export const ESGAnalyticsStatusUpOutlineIcon = () => {
  return (
    <svg
      width="28"
      height="29"
      viewBox="0 0 28 29"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.02734 22.1572V19.7422"
        stroke="#07838F"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M14 22.1581V17.3281"
        stroke="#07838F"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M19.9727 22.1629V14.9062"
        stroke="#07838F"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M19.974 7.8125L19.4373 8.4425C16.4623 11.9192 12.4723 14.3808 8.02734 15.4892"
        stroke="#07838F"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M16.5547 7.8125H19.973V11.2192"
        stroke="#07838F"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.4987 26.6536H17.4987C23.332 26.6536 25.6654 24.3203 25.6654 18.487V11.487C25.6654 5.65365 23.332 3.32031 17.4987 3.32031H10.4987C4.66536 3.32031 2.33203 5.65365 2.33203 11.487V18.487C2.33203 24.3203 4.66536 26.6536 10.4987 26.6536Z"
        stroke="#07838F"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const ESGAnalyticsStatusUpSolidIcon = () => {
  return (
    <svg
      width="28"
      height="29"
      viewBox="0 0 28 29"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M18.8909 3.32031H9.11427C4.8676 3.32031 2.33594 5.85198 2.33594 10.0986V19.8636C2.33594 24.122 4.8676 26.6536 9.11427 26.6536H18.8793C23.1259 26.6536 25.6576 24.122 25.6576 19.8753V10.0986C25.6693 5.85198 23.1376 3.32031 18.8909 3.32031ZM8.90427 22.162C8.90427 22.6403 8.5076 23.037 8.02927 23.037C7.55094 23.037 7.15427 22.6403 7.15427 22.162V19.747C7.15427 19.2686 7.55094 18.872 8.02927 18.872C8.5076 18.872 8.90427 19.2686 8.90427 19.747V22.162ZM14.8776 22.162C14.8776 22.6403 14.4809 23.037 14.0026 23.037C13.5243 23.037 13.1276 22.6403 13.1276 22.162V17.3203C13.1276 16.842 13.5243 16.4453 14.0026 16.4453C14.4809 16.4453 14.8776 16.842 14.8776 17.3203V22.162ZM20.8509 22.162C20.8509 22.6403 20.4543 23.037 19.9759 23.037C19.4976 23.037 19.1009 22.6403 19.1009 22.162V14.9053C19.1009 14.427 19.4976 14.0303 19.9759 14.0303C20.4543 14.0303 20.8509 14.427 20.8509 14.9053V22.162ZM20.8509 11.2186C20.8509 11.697 20.4543 12.0936 19.9759 12.0936C19.4976 12.0936 19.1009 11.697 19.1009 11.2186V10.087C16.1259 13.1436 12.4043 15.302 8.23927 16.3403C8.16927 16.3636 8.09927 16.3636 8.02927 16.3636C7.6326 16.3636 7.2826 16.0953 7.1776 15.6986C7.06094 15.232 7.34094 14.7536 7.81927 14.637C11.7509 13.657 15.2509 11.592 18.0276 8.67531H16.5693C16.0909 8.67531 15.6943 8.27865 15.6943 7.80031C15.6943 7.32198 16.0909 6.92531 16.5693 6.92531H19.9876C20.0343 6.92531 20.0693 6.94865 20.1159 6.94865C20.1743 6.96031 20.2326 6.96031 20.2909 6.98365C20.3493 7.00698 20.3959 7.04198 20.4543 7.07698C20.4893 7.10031 20.5243 7.11198 20.5593 7.13531C20.5709 7.14698 20.5709 7.15865 20.5826 7.15865C20.6293 7.20531 20.6643 7.25198 20.6993 7.29865C20.7343 7.34531 20.7693 7.38031 20.7809 7.42698C20.8043 7.47365 20.8043 7.52031 20.8159 7.57865C20.8276 7.63698 20.8509 7.69531 20.8509 7.76531C20.8509 7.77698 20.8626 7.78865 20.8626 7.80031V11.2186H20.8509Z"
        fill="#07838F"
      />
    </svg>
  );
};

export const ESGReportingNoteOutlineIcon = () => {
  return (
    <svg
      width="28"
      height="29"
      viewBox="0 0 28 29"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M23.3307 10.612V21.987C23.3307 25.487 21.2424 26.6536 18.6641 26.6536H9.33073C6.7524 26.6536 4.66406 25.487 4.66406 21.987V10.612C4.66406 6.82031 6.7524 5.94531 9.33073 5.94531C9.33073 6.66865 9.62236 7.32197 10.1007 7.80031C10.579 8.27864 11.2324 8.57031 11.9557 8.57031H16.0391C17.4857 8.57031 18.6641 7.39198 18.6641 5.94531C21.2424 5.94531 23.3307 6.82031 23.3307 10.612Z"
        stroke="#07838F"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M18.6693 5.94531C18.6693 7.39198 17.4909 8.57031 16.0443 8.57031H11.9609C11.2376 8.57031 10.5842 8.27864 10.1059 7.80031C9.62757 7.32197 9.33594 6.66865 9.33594 5.94531C9.33594 4.49865 10.5143 3.32031 11.9609 3.32031H16.0443C16.7676 3.32031 17.421 3.61198 17.8993 4.09032C18.3776 4.56865 18.6693 5.22198 18.6693 5.94531Z"
        stroke="#07838F"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.33594 16.1484H14.0026"
        stroke="#07838F"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.33594 20.8203H18.6693"
        stroke="#07838F"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const ESGReportingNoteSolidIcon = () => {
  return (
    <svg
      width="28"
      height="29"
      viewBox="0 0 28 29"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M18.6654 5.94531C18.6654 7.39198 17.487 8.57031 16.0404 8.57031H11.957C11.2337 8.57031 10.5804 8.27865 10.102 7.80031C9.6237 7.32198 9.33203 6.66865 9.33203 5.94531C9.33203 4.49865 10.5104 3.32031 11.957 3.32031H16.0404C16.7637 3.32031 17.417 3.61198 17.8954 4.09031C18.3737 4.56865 18.6654 5.22198 18.6654 5.94531Z"
        fill="#07838F"
      />
      <path
        d="M21.9696 6.8545C21.7013 6.63283 21.398 6.45783 21.0713 6.3295C20.733 6.20116 20.3946 6.4695 20.3246 6.8195C19.928 8.8145 18.1663 10.3195 16.043 10.3195H11.9596C10.793 10.3195 9.6963 9.8645 8.86797 9.03616C8.2613 8.4295 7.8413 7.6595 7.67797 6.83116C7.60797 6.48116 7.25797 6.20116 6.91964 6.34116C5.5663 6.8895 4.66797 8.12616 4.66797 10.6112V21.9862C4.66797 25.4862 6.7563 26.6528 9.33464 26.6528H18.668C21.2463 26.6528 23.3346 25.4862 23.3346 21.9862V10.6112C23.3346 8.7095 22.8096 7.54283 21.9696 6.8545ZM9.33464 15.2778H14.0013C14.4796 15.2778 14.8763 15.6745 14.8763 16.1528C14.8763 16.6312 14.4796 17.0278 14.0013 17.0278H9.33464C8.8563 17.0278 8.45964 16.6312 8.45964 16.1528C8.45964 15.6745 8.8563 15.2778 9.33464 15.2778ZM18.668 21.6945H9.33464C8.8563 21.6945 8.45964 21.2978 8.45964 20.8195C8.45964 20.3412 8.8563 19.9445 9.33464 19.9445H18.668C19.1463 19.9445 19.543 20.3412 19.543 20.8195C19.543 21.2978 19.1463 21.6945 18.668 21.6945Z"
        fill="#07838F"
      />
    </svg>
  );
};

export const ESGReportingDownloadIcon = ({ main }) => {
  return (
    <svg
      width="21"
      height="20"
      viewBox="0 0 21 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.1997 7.41406C17.1997 7.6724 18.4247 9.21406 18.4247 12.5891V12.6974C18.4247 16.4224 16.9331 17.9141 13.2081 17.9141H7.78307C4.05807 17.9141 2.56641 16.4224 2.56641 12.6974V12.5891C2.56641 9.23906 3.77474 7.6974 6.72474 7.4224"
        stroke={main ? "#07838F" : "white"}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.5 1.66406V12.3974"
        stroke={main ? "#07838F" : "white"}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.2904 10.5391L10.4987 13.3307L7.70703 10.5391"
        stroke={main ? "#07838F" : "white"}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const ESGReportingEyeIcon = () => {
  return (
    <svg
      width="21"
      height="20"
      viewBox="0 0 21 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.4823 9.99896C13.4823 11.649 12.149 12.9823 10.499 12.9823C8.84896 12.9823 7.51562 11.649 7.51562 9.99896C7.51562 8.34896 8.84896 7.01562 10.499 7.01562C12.149 7.01562 13.4823 8.34896 13.4823 9.99896Z"
        stroke="#07838F"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.5018 16.8932C13.4435 16.8932 16.1852 15.1599 18.0935 12.1599C18.8435 10.9849 18.8435 9.0099 18.0935 7.8349C16.1852 4.8349 13.4435 3.10156 10.5018 3.10156C7.56016 3.10156 4.81849 4.8349 2.91016 7.8349C2.16016 9.0099 2.16016 10.9849 2.91016 12.1599C4.81849 15.1599 7.56016 16.8932 10.5018 16.8932Z"
        stroke="#07838F"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const ESGReportingIROIcon = () => {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M21.8947 10.5962C25.148 13.8496 25.148 19.1295 21.8947 22.3829C18.6413 25.6362 13.3613 25.6362 10.108 22.3829C6.85464 19.1295 6.85464 13.8496 10.108 10.5962C13.3613 7.34292 18.6413 7.34292 21.8947 10.5962Z"
        stroke="#07838F"
        strokeWidth="1.37919"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11 28.8493C8.33333 27.7826 5.99999 25.8493 4.45332 23.1693C2.93332 20.5426 2.42665 17.6226 2.78665 14.8359"
        stroke="#07838F"
        strokeWidth="1.37919"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.80078 5.96729C10.0674 4.19395 12.9074 3.14062 16.0008 3.14062C19.0274 3.14062 21.8141 4.16728 24.0541 5.87394"
        stroke="#07838F"
        strokeWidth="1.37919"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M21 28.8493C23.6667 27.7826 26 25.8493 27.5467 23.1693C29.0667 20.5426 29.5733 17.6226 29.2133 14.8359"
        stroke="#07838F"
        strokeWidth="1.37919"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const ESGReportingDangerIcon = () => {
  return (
    <svg
      width="33"
      height="32"
      viewBox="0 0 33 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.5 12V18.6667"
        stroke="#07838F"
        strokeWidth="1.37919"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.5006 28.5431H8.42056C3.79389 28.5431 1.86056 25.2365 4.10056 21.1965L8.26056 13.7031L12.1806 6.66313C14.5539 2.38312 18.4472 2.38312 20.8206 6.66313L24.7406 13.7165L28.9006 21.2098C31.1406 25.2498 29.1939 28.5565 24.5806 28.5565H16.5006V28.5431Z"
        stroke="#07838F"
        strokeWidth="1.37919"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.4922 22.6641H16.5042"
        stroke="#07838F"
        strokeWidth="1.83893"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const ESGReportingPlantIcon = () => {
  return (
    <svg
      width="28"
      height="32"
      viewBox="0 0 28 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.68862 17.4642C7.56441 16.704 5.34762 14.875 3.66448 12.3113C1.65788 9.25541 2.26231 5.07069 2.83135 2.67856C2.91521 2.32734 3.13574 2.02349 3.4434 1.83454C3.75106 1.64559 4.12189 1.58678 4.47311 1.67118C6.83692 2.23913 10.8174 3.51714 13.1355 6.21693C13.8537 7.05333 14.4075 7.95125 14.8099 8.86552C14.9913 9.27828 15.4737 9.46614 15.8865 9.28427C16.2987 9.10294 16.4865 8.62048 16.3052 8.20773C15.8413 7.15297 15.2031 6.11727 14.3749 5.15291C11.8188 2.17596 7.4604 0.708452 4.85428 0.0827857C4.08159 -0.102899 3.26643 0.0266989 2.58903 0.442176C1.91163 0.858198 1.427 1.52634 1.24241 2.29903V2.30011C0.597137 5.01079 0.0242908 9.7444 2.2988 13.2082C4.20248 16.1067 6.73618 18.1427 9.1381 19.0025C9.56229 19.1544 10.03 18.9328 10.182 18.5086C10.3339 18.0839 10.1128 17.6161 9.68862 17.4642Z"
        fill="#07838F"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M24.6748 10.1584C22.0109 9.86051 17.4134 9.75705 13.9971 11.6869C10.7446 13.5241 8.82946 16.4978 8.3029 19.2373C7.9299 21.1797 8.25389 23.0104 9.23133 24.3162C10.2202 25.6372 11.8157 26.3701 13.7362 26.478C16.3767 26.6266 19.6629 25.5778 22.6115 23.3703C25.9294 20.8873 27.001 16.2413 27.3168 13.4724C27.4061 12.6823 27.178 11.8889 26.6824 11.2676C26.1869 10.6457 25.4649 10.2471 24.6748 10.1584ZM24.4929 11.7822C24.8517 11.8225 25.1801 12.0038 25.4055 12.2858C25.6304 12.5685 25.7344 12.9289 25.6936 13.2878C25.4153 15.7306 24.5599 19.8717 21.633 22.0624C19.0345 24.0074 16.1545 24.9778 13.8277 24.8471C12.437 24.7687 11.2553 24.2938 10.5387 23.3371C9.81125 22.3651 9.62938 20.9918 9.90709 19.5455C10.3574 17.2035 12.0199 14.6802 14.8002 13.1092C17.8992 11.3591 22.0768 11.5115 24.4929 11.7822Z"
        fill="#07838F"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M18.5575 15.7138C18.5575 15.7138 10.5649 17.6938 6.76245 30.9563C6.63776 31.3893 6.88878 31.8423 7.32223 31.9665C7.75568 32.0906 8.20818 31.8396 8.33234 31.4061C11.819 19.2468 18.9681 17.2952 18.9681 17.2952C19.4043 17.1819 19.6667 16.7359 19.5535 16.2992C19.4402 15.863 18.9937 15.6006 18.5575 15.7138Z"
        fill="#07838F"
      />
    </svg>
  );
};

export const ESGReportingBuildingIcon = () => {
  return (
    <svg
      width="28"
      height="28"
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.33203 25.6641H25.6654"
        stroke="#07838F"
        strokeWidth="1.21307"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M19.8333 2.32812H8.16667C4.66667 2.32812 3.5 4.41646 3.5 6.99479V25.6615H24.5V6.99479C24.5 4.41646 23.3333 2.32812 19.8333 2.32812Z"
        stroke="#07838F"
        strokeWidth="1.21307"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.16797 19.25H11.668"
        stroke="#07838F"
        strokeWidth="1.21307"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.332 19.25H19.832"
        stroke="#07838F"
        strokeWidth="1.21307"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.16797 14H11.668"
        stroke="#07838F"
        strokeWidth="1.21307"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.332 14H19.832"
        stroke="#07838F"
        strokeWidth="1.21307"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.16797 8.75H11.668"
        stroke="#07838F"
        strokeWidth="1.21307"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.332 8.75H19.832"
        stroke="#07838F"
        strokeWidth="1.21307"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const EmissionsCalculationHomeIcon = () => {
  return (
    <svg
      width="33"
      height="41"
      viewBox="0 0 33 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.0206 18.0308C9.29411 17.6607 8.48537 17.4688 7.66291 17.4688C1.24778 17.9212 1.24778 27.2565 7.66291 27.7088H22.8646C24.7151 27.7225 26.4971 27.0371 27.8541 25.7897C32.3639 21.8554 29.9514 13.9458 24.016 13.1919C21.8776 0.347288 3.33133 5.2274 7.73145 17.4688"
        stroke="url(#paint0_linear_40003463_39356)"
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M21.7812 13.7964C22.494 13.44 23.2754 13.2481 24.0704 13.2344"
        stroke="url(#paint1_linear_40003463_39356)"
        strokeWidth="1.5"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M22.1377 18.8359H14.827C12.3048 18.8359 10.2578 21.011 10.2578 23.6909V33.4009C10.2578 36.0808 12.3048 38.2559 14.827 38.2559H22.1377C24.6599 38.2559 26.7069 36.0808 26.7069 33.4009V23.6909C26.7069 21.011 24.6599 18.8359 22.1377 18.8359ZM15.6129 34.9836C15.4393 35.1681 15.2108 35.2652 14.9732 35.2652C14.7265 35.2652 14.498 35.1681 14.3244 34.9836C14.1508 34.7991 14.0502 34.5564 14.0502 34.2942C14.0502 34.0417 14.1508 33.7893 14.3244 33.6048C14.4066 33.5174 14.5072 33.4494 14.6168 33.4009C14.8453 33.3038 15.092 33.3038 15.3205 33.4009C15.3753 33.4203 15.4301 33.4494 15.4758 33.4883C15.5307 33.5174 15.5763 33.566 15.6129 33.6048C15.7865 33.7893 15.887 34.0417 15.887 34.2942C15.887 34.5564 15.7865 34.7991 15.6129 34.9836ZM14.0502 30.4102C14.0502 30.284 14.0777 30.1578 14.1233 30.0412C14.169 29.915 14.233 29.8179 14.3244 29.7208C14.5346 29.4975 14.8544 29.3907 15.1468 29.4586C15.2017 29.4683 15.2656 29.4878 15.3205 29.5169C15.3753 29.5363 15.4301 29.5654 15.4758 29.6043C15.5307 29.6334 15.5763 29.682 15.6129 29.7208C15.6951 29.8179 15.7682 29.915 15.8139 30.0412C15.8596 30.1578 15.8779 30.284 15.8779 30.4102C15.8779 30.6724 15.7865 30.9151 15.6129 31.0996C15.4393 31.2841 15.2108 31.3812 14.9732 31.3812C14.8453 31.3812 14.7265 31.3521 14.6168 31.3035C14.5072 31.255 14.4066 31.187 14.3244 31.0996C14.1508 30.9151 14.0502 30.6724 14.0502 30.4102ZM19.2682 34.9836C19.186 35.071 19.0855 35.139 18.9758 35.1875C18.8662 35.2361 18.7474 35.2652 18.6194 35.2652C18.3818 35.2652 18.1534 35.1681 17.9797 34.9836C17.8061 34.7991 17.7056 34.5564 17.7056 34.2942C17.7056 34.2262 17.7147 34.168 17.7239 34.1C17.7421 34.0417 17.7604 33.9835 17.7787 33.9252C17.8061 33.867 17.8335 33.8087 17.8609 33.7504C17.8975 33.7019 17.934 33.6533 17.9797 33.6048C18.062 33.5174 18.1625 33.4494 18.2722 33.4009C18.6103 33.2552 19.0124 33.3329 19.2682 33.6048C19.4419 33.7893 19.5333 34.0417 19.5333 34.2942C19.5333 34.5564 19.4419 34.7991 19.2682 34.9836ZM19.2682 31.0996C19.0946 31.2841 18.8662 31.3812 18.6194 31.3812C18.3818 31.3812 18.1534 31.2841 17.9797 31.0996C17.8061 30.9151 17.7056 30.6724 17.7056 30.4102C17.7056 30.1578 17.8061 29.9053 17.9797 29.7208C18.3179 29.3615 18.9301 29.3615 19.2682 29.7208C19.3505 29.8179 19.4236 29.915 19.4693 30.0412C19.515 30.1578 19.5333 30.284 19.5333 30.4102C19.5333 30.6724 19.4419 30.9151 19.2682 31.0996ZM15.7408 27.0506C14.7996 27.0506 14.0228 26.2349 14.0228 25.2251V24.2541C14.0228 23.254 14.7904 22.4286 15.7408 22.4286H21.2239C22.1651 22.4286 22.9419 23.2443 22.9419 24.2541V25.2251C22.9419 26.2252 22.1742 27.0506 21.2239 27.0506H15.7408ZM22.9236 34.9836C22.75 35.1681 22.5215 35.2652 22.2748 35.2652C22.156 35.2652 22.0372 35.2361 21.9275 35.1875C21.8179 35.139 21.7173 35.071 21.6351 34.9836C21.4615 34.7991 21.3701 34.5564 21.3701 34.2942C21.3701 34.0417 21.4615 33.7893 21.6351 33.6048C21.8818 33.3329 22.293 33.2552 22.6312 33.4009C22.7408 33.4494 22.8413 33.5174 22.9236 33.6048C23.0972 33.7893 23.1886 34.0417 23.1886 34.2942C23.1886 34.5564 23.0972 34.7991 22.9236 34.9836ZM23.1246 30.7792C23.0789 30.8957 23.015 31.0025 22.9236 31.0996C22.75 31.2841 22.5215 31.3812 22.2748 31.3812C22.0372 31.3812 21.8087 31.2841 21.6351 31.0996C21.4615 30.9151 21.3609 30.6724 21.3609 30.4102C21.3609 30.1578 21.4615 29.9053 21.6351 29.7208C21.9732 29.3615 22.5855 29.3615 22.9236 29.7208C23.0972 29.9053 23.1977 30.1578 23.1977 30.4102C23.1977 30.5364 23.1703 30.6627 23.1246 30.7792Z"
        fill="url(#paint2_linear_40003463_39356)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40003463_39356"
          x1="2.33764"
          y1="30.9662"
          x2="35.6233"
          y2="27.3744"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_40003463_39356"
          x1="21.7382"
          y1="13.8793"
          x2="24.2419"
          y2="12.9902"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_40003463_39356"
          x1="9.94862"
          y1="41.1191"
          x2="30.0978"
          y2="39.631"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const SupplyChainHomeIcon = () => {
  return (
    <svg
      width="41"
      height="40"
      viewBox="0 0 41 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M25.5016 37.9177C25.0516 37.9177 24.635 37.6677 24.4183 37.2844C24.2016 36.8844 24.2016 36.4177 24.435 36.0177L26.185 33.101C26.535 32.5177 27.3016 32.3177 27.9016 32.6677C28.5016 33.0177 28.685 33.7844 28.335 34.3844L27.885 35.1344C32.485 34.051 35.935 29.9177 35.935 24.9844C35.935 24.301 36.5016 23.7344 37.185 23.7344C37.8683 23.7344 38.435 24.301 38.435 24.9844C38.4183 32.1177 32.6183 37.9177 25.5016 37.9177Z"
        fill="url(#paint0_linear_40003726_8294)"
      />
      <path
        d="M3.83594 16.2526C3.1526 16.2526 2.58594 15.6859 2.58594 15.0026C2.58594 7.88594 8.38594 2.08594 15.5026 2.08594C15.9526 2.08594 16.3693 2.33594 16.5859 2.71927C16.8026 3.11927 16.8026 3.58594 16.5693 3.98594L14.8193 6.9026C14.4693 7.48594 13.7026 7.68594 13.1026 7.31927C12.5193 6.96927 12.3193 6.2026 12.6859 5.6026L13.1359 4.8526C8.51927 5.93594 5.08594 10.0693 5.08594 15.0026C5.08594 15.6859 4.51927 16.2526 3.83594 16.2526Z"
        fill="url(#paint1_linear_40003726_8294)"
      />
      <path
        d="M27.8297 13.6849L21.563 10.3016C20.8964 9.95156 20.113 9.95156 19.4464 10.3016L13.163 13.6849C12.713 13.9349 12.4297 14.4182 12.4297 14.9516C12.4297 15.5016 12.713 15.9849 13.163 16.2349L19.4297 19.6182C19.763 19.8016 20.1297 19.8849 20.4964 19.8849C20.863 19.8849 21.2297 19.8016 21.563 19.6182L27.8297 16.2349C28.2797 15.9849 28.563 15.5016 28.563 14.9516C28.563 14.4182 28.2797 13.9349 27.8297 13.6849Z"
        fill="url(#paint2_linear_40003726_8294)"
      />
      <path
        d="M18.4 20.7847L12.5667 17.868C12.1167 17.6513 11.6 17.668 11.1667 17.9347C10.75 18.2013 10.5 18.6513 10.5 19.1513V24.668C10.5 25.618 11.0333 26.4847 11.8833 26.9013L17.7167 29.818C17.9167 29.918 18.1333 29.968 18.3667 29.968C18.6333 29.968 18.8833 29.9013 19.1167 29.7513C19.55 29.4847 19.8 29.0347 19.8 28.5347V23.018C19.7833 22.068 19.2667 21.218 18.4 20.7847Z"
        fill="url(#paint3_linear_40003726_8294)"
      />
      <path
        d="M29.8198 17.9347C29.3865 17.668 28.8698 17.6513 28.4198 17.868L22.5865 20.7847C21.7365 21.218 21.2031 22.068 21.2031 23.018V28.5347C21.2031 29.0347 21.4531 29.4847 21.8865 29.7513C22.1198 29.9013 22.3698 29.968 22.6365 29.968C22.8531 29.968 23.0698 29.918 23.2865 29.818L29.1198 26.9013C29.9698 26.468 30.5031 25.618 30.5031 24.668V19.1513C30.5031 18.6513 30.2531 18.2013 29.8198 17.9347Z"
        fill="url(#paint4_linear_40003726_8294)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40003726_8294"
          x1="23.9913"
          y1="40.0088"
          x2="41.3206"
          y2="38.4986"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_40003726_8294"
          x1="2.31976"
          y1="18.3413"
          x2="19.6287"
          y2="16.8328"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_40003726_8294"
          x1="12.1264"
          y1="21.3365"
          x2="31.5992"
          y2="18.5545"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_40003726_8294"
          x1="10.3252"
          y1="31.774"
          x2="21.7293"
          y2="31.0191"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_40003726_8294"
          x1="21.0283"
          y1="31.774"
          x2="32.4324"
          y2="31.0191"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const DecarbonisationHomeIcon = () => {
  return (
    <svg
      width="34"
      height="33"
      viewBox="0 0 34 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M18.1253 11.0078C16.7358 11.0078 15.6133 12.1303 15.6133 13.5197C15.6133 14.9017 16.7358 16.0316 18.1253 16.0316C19.5147 16.0316 20.6372 14.9017 20.6372 13.5197C20.6365 12.1303 19.5147 11.0078 18.1253 11.0078Z"
        fill="url(#paint0_linear_40003463_39371)"
      />
      <path
        d="M27.0502 7.25964C25.7602 3.22114 22.0188 0.312478 17.7744 0.29701C13.3847 0.274144 9.66682 3.15994 8.42261 7.12985C8.22421 7.74789 7.66666 8.16822 7.02572 8.16822C3.63608 8.16822 0.604904 10.5732 0.132102 13.9243C-0.455706 18.0772 2.7584 21.6348 6.79706 21.6348H28.3341C30.9832 21.6348 33.1206 19.4437 33.0446 16.7792C33.0063 15.4893 32.4265 14.344 31.5327 13.5269C30.6322 12.7024 29.4256 12.206 28.1357 12.206C27.6851 12.206 27.3414 11.8093 27.4026 11.3587C27.5775 10.0688 27.5089 8.69481 27.0502 7.25964ZM13.3389 17.3367C12.9112 17.482 12.4687 17.558 12.0181 17.558C9.78855 17.558 7.9794 15.7489 7.9794 13.5195C7.9794 11.2901 9.78855 9.48098 12.0181 9.48098C12.4687 9.48098 12.9112 9.54958 13.3309 9.69485C13.7277 9.83204 13.9415 10.2672 13.8043 10.6646C13.6671 11.0695 13.232 11.2753 12.8345 11.1381C12.5749 11.054 12.2998 11.0083 12.0174 11.0083C10.6279 11.0083 9.50541 12.1307 9.50541 13.5202C9.50541 14.9022 10.6279 16.032 12.0174 16.032C12.2998 16.032 12.5749 15.9863 12.8345 15.8948C13.2313 15.7576 13.6665 15.9708 13.8043 16.3683C13.9422 16.7637 13.7364 17.1989 13.3389 17.3367ZM18.1261 17.558C15.8966 17.558 14.0875 15.7489 14.0875 13.5195C14.0875 11.2901 15.8966 9.48098 18.1261 9.48098C20.3556 9.48098 22.1648 11.2901 22.1648 13.5195C22.1648 15.7489 20.355 17.558 18.1261 17.558ZM25.8826 19.856H23.142C22.8293 19.856 22.5468 19.665 22.4318 19.3751C22.3175 19.0853 22.3861 18.7571 22.6073 18.5432L24.7299 16.5048C24.8671 16.3676 24.8671 16.1766 24.814 16.0542C24.7756 15.9547 24.6996 15.8404 24.5241 15.833C24.1804 15.833 23.906 16.108 23.906 16.4436C23.906 16.8633 23.5623 17.2069 23.1427 17.2069C22.723 17.2069 22.3793 16.8633 22.3793 16.4436C22.3793 15.268 23.3337 14.3063 24.5167 14.3063C25.311 14.3218 25.9519 14.7717 26.2344 15.4893C26.5243 16.2298 26.3487 17.0543 25.7838 17.5963L25.0359 18.3294H25.8833C26.3111 18.3294 26.6467 18.673 26.6467 19.0927C26.646 19.5123 26.3097 19.856 25.8826 19.856Z"
        fill="url(#paint1_linear_40003463_39371)"
      />
      <path
        d="M10.4941 23.1641V27.4433H8.41797L12.0558 32.2983L15.6943 27.4433H13.6181V23.1641H10.4941Z"
        fill="url(#paint2_linear_40003463_39371)"
      />
      <path
        d="M20.9004 23.1641V27.4433H18.8242L22.4627 32.2983L26.1005 27.4433H24.0244V23.1641H20.9004Z"
        fill="url(#paint3_linear_40003463_39371)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40003463_39371"
          x1="15.5188"
          y1="16.7722"
          x2="21.6598"
          y2="16.2368"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_40003463_39371"
          x1="-0.557501"
          y1="24.7808"
          x2="39.3417"
          y2="19.4032"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_40003463_39371"
          x1="8.2812"
          y1="33.645"
          x2="17.1998"
          y2="33.0255"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_40003463_39371"
          x1="18.6874"
          y1="33.645"
          x2="27.6061"
          y2="33.0255"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const GreenSightHomeIcon = () => {
  return (
    <svg
      width="41"
      height="23"
      viewBox="0 0 41 23"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.73287 11.7298C8.73287 5.5728 13.7197 0.585938 19.8767 0.585938C9.10898 0.585938 0.375 11.7298 0.375 11.7298C0.375 11.7298 9.10898 22.8736 19.8767 22.8736C13.7197 22.8736 8.73287 17.8867 8.73287 11.7298Z"
        fill="url(#paint0_linear_40003463_39258)"
      />
      <path
        d="M28.3414 10.7224C27.1087 9.9702 25.3117 10.0607 23.7725 10.9453C22.3586 11.7602 21.4253 13.0905 21.2999 14.4208C21.2512 14.4695 21.1955 14.5183 21.1467 14.567V12.1572C22.1984 11.3701 22.867 9.92144 22.867 8.31952C22.867 6.54347 22.0521 4.94154 20.7845 4.24505C20.5756 4.12665 20.3248 4.12665 20.1159 4.24505C18.8483 4.94154 18.0334 6.54347 18.0334 8.31952C18.0334 9.92144 18.702 11.3701 19.7537 12.1572V14.5461C19.698 14.4974 19.6353 14.4486 19.5726 14.3999C19.3985 13.0765 18.4165 11.7741 16.9678 11.0149C15.3937 10.1861 13.5968 10.1722 12.3918 10.9662C12.1899 11.0985 12.0715 11.3214 12.0784 11.5582C12.1063 12.9999 13.144 14.4695 14.7181 15.2983C15.526 15.7232 16.3827 15.9321 17.1976 15.9321C17.7896 15.9321 18.3468 15.8207 18.8413 15.5978C19.2731 15.953 19.5866 16.3082 19.7537 16.6426V18.6067C19.7537 18.9897 20.0671 19.3032 20.4502 19.3032C20.8333 19.3032 21.1467 18.9897 21.1467 18.6067V16.6774C21.4044 16.3361 21.7735 15.8973 22.08 15.5909C22.5327 15.772 23.0272 15.8625 23.5496 15.8625C24.4132 15.8625 25.3326 15.6187 26.1754 15.1312C27.7146 14.2466 28.6966 12.7422 28.6758 11.2935C28.6758 11.0567 28.5434 10.8338 28.3414 10.7085V10.7224Z"
        fill="url(#paint1_linear_40003463_39258)"
      />
      <path
        d="M20.875 0.585938C27.032 0.585938 32.0188 5.5728 32.0188 11.7298C32.0188 17.8867 27.032 22.8736 20.875 22.8736C31.6427 22.8736 40.3767 11.7298 40.3767 11.7298C40.3767 11.7298 31.6427 0.585938 20.875 0.585938Z"
        fill="url(#paint2_linear_40003463_39258)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40003463_39258"
          x1="0.00842668"
          y1="26.1596"
          x2="23.8882"
          y2="24.3378"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_40003463_39258"
          x1="11.7661"
          y1="21.5364"
          x2="32.0237"
          y2="19.6009"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_40003463_39258"
          x1="20.5084"
          y1="26.1596"
          x2="44.3882"
          y2="24.3378"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const StakeholderEngagementHomeIcon = () => {
  return (
    <svg
      width="37"
      height="37"
      viewBox="0 0 37 37"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.625 3.1875C9.695 3.1875 6.5 6.3825 6.5 10.3125C6.5 14.1675 9.515 17.2875 13.445 17.4225C13.565 17.4075 13.685 17.4075 13.775 17.4225C13.805 17.4225 13.82 17.4225 13.85 17.4225C13.865 17.4225 13.865 17.4225 13.88 17.4225C17.72 17.2875 20.735 14.1675 20.75 10.3125C20.75 6.3825 17.555 3.1875 13.625 3.1875Z"
        fill="url(#paint0_linear_40003726_25283)"
      />
      <path
        d="M21.2425 21.4128C17.0575 18.6228 10.2325 18.6228 6.0175 21.4128C4.1125 22.6878 3.0625 24.4128 3.0625 26.2578C3.0625 28.1028 4.1125 29.8128 6.0025 31.0728C8.1025 32.4828 10.8625 33.1878 13.6225 33.1878C16.3825 33.1878 19.1425 32.4828 21.2425 31.0728C23.1325 29.7978 24.1825 28.0878 24.1825 26.2278C24.1675 24.3828 23.1325 22.6728 21.2425 21.4128Z"
        fill="url(#paint1_linear_40003726_25283)"
      />
      <path
        d="M30.1091 11.1986C30.3491 14.1086 28.2791 16.6586 25.4141 17.0036C25.3991 17.0036 25.3991 17.0036 25.3841 17.0036H25.3391C25.2491 17.0036 25.1591 17.0036 25.0841 17.0336C23.6291 17.1086 22.2941 16.6436 21.2891 15.7886C22.8341 14.4086 23.7191 12.3386 23.5391 10.0886C23.4341 8.87363 23.0141 7.76363 22.3841 6.81863C22.9541 6.53363 23.6141 6.35363 24.2891 6.29363C27.2291 6.03863 29.8541 8.22863 30.1091 11.1986Z"
        fill="url(#paint2_linear_40003726_25283)"
      />
      <path
        d="M33.1094 25.0731C32.9894 26.5281 32.0594 27.7881 30.4994 28.6431C28.9994 29.4681 27.1094 29.8581 25.2344 29.8131C26.3144 28.8381 26.9444 27.6231 27.0644 26.3331C27.2144 24.4731 26.3294 22.6881 24.5594 21.2631C23.5544 20.4681 22.3844 19.8381 21.1094 19.3731C24.4244 18.4131 28.5944 19.0581 31.1594 21.1281C32.5394 22.2381 33.2444 23.6331 33.1094 25.0731Z"
        fill="url(#paint3_linear_40003726_25283)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40003726_25283"
          x1="6.23214"
          y1="19.5213"
          x2="23.6501"
          y2="18.001"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_40003726_25283"
          x1="2.66551"
          y1="35.2324"
          x2="28.2267"
          y2="31.8381"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_40003726_25283"
          x1="21.1229"
          y1="18.6291"
          x2="31.9537"
          y2="17.8539"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_40003726_25283"
          x1="20.8835"
          y1="31.4206"
          x2="35.5471"
          y2="30.0084"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const DoubleMaterialityHomeIcon = () => {
  return (
    <svg
      width="41"
      height="41"
      viewBox="0 0 41 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M25.8766 38.1042C25.4266 38.1042 25.01 37.8542 24.7933 37.4708C24.5766 37.0875 24.5766 36.6042 24.81 36.2208L26.56 33.3042C26.91 32.7042 27.6766 32.5208 28.2766 32.8708C28.8766 33.2208 29.06 33.9875 28.71 34.5875L28.26 35.3375C32.86 34.2542 36.31 30.1208 36.31 25.1875C36.31 24.5042 36.8766 23.9375 37.56 23.9375C38.2433 23.9375 38.81 24.5042 38.81 25.1875C38.7933 32.3042 32.9933 38.1042 25.8766 38.1042Z"
        fill="url(#paint0_linear_40003463_39273)"
      />
      <path
        d="M4.21094 16.4401C3.5276 16.4401 2.96094 15.8734 2.96094 15.1901C2.96094 8.07344 8.76094 2.27344 15.8776 2.27344C16.3276 2.27344 16.7443 2.52344 16.9609 2.90677C17.1776 3.2901 17.1776 3.77344 16.9443 4.15677L15.1943 7.0901C14.8443 7.67344 14.0776 7.87344 13.4776 7.50677C12.8943 7.15677 12.6943 6.3901 13.0609 5.7901L13.5109 5.0401C8.89427 6.12344 5.46094 10.2568 5.46094 15.1901C5.46094 15.8734 4.89427 16.4401 4.21094 16.4401Z"
        fill="url(#paint1_linear_40003463_39273)"
      />
      <path
        d="M18.6547 23.2682L13.4214 20.4516C12.8714 20.1516 12.2047 20.1516 11.6547 20.4516L6.42135 23.2682C6.03802 23.4682 5.80469 23.8849 5.80469 24.3349C5.80469 24.7849 6.03802 25.2016 6.42135 25.4016L11.6547 28.2182C11.938 28.3682 12.238 28.4349 12.538 28.4349C12.838 28.4349 13.138 28.3682 13.4214 28.2182L18.6547 25.4016C19.038 25.2016 19.2714 24.7849 19.2714 24.3349C19.2714 23.8849 19.0214 23.4849 18.6547 23.2682Z"
        fill="url(#paint2_linear_40003463_39273)"
      />
      <path
        d="M10.7943 29.2078L5.9276 26.7745C5.56094 26.5911 5.1276 26.6078 4.76094 26.8245C4.4276 27.0411 4.21094 27.4245 4.21094 27.8411V32.4411C4.21094 33.2411 4.64427 33.9578 5.36094 34.3078L10.2276 36.7411C10.3943 36.8078 10.5776 36.8578 10.7609 36.8578C10.9776 36.8578 11.1943 36.7911 11.3943 36.6745C11.7443 36.4578 11.9609 36.0745 11.9609 35.6578V31.0578C11.9609 30.2745 11.5109 29.5578 10.7943 29.2078Z"
        fill="url(#paint3_linear_40003463_39273)"
      />
      <path
        d="M20.3083 26.8245C19.9583 26.6078 19.525 26.5911 19.1417 26.7745L14.275 29.2078C13.5583 29.5578 13.125 30.2745 13.125 31.0745V35.6745C13.125 36.0911 13.3417 36.4745 13.6917 36.6911C13.8917 36.7911 14.1083 36.8578 14.325 36.8578C14.5083 36.8578 14.6917 36.8078 14.8583 36.7245L19.725 34.2911C20.4417 33.9411 20.875 33.2245 20.875 32.4245V27.8245C20.875 27.4245 20.6583 27.0411 20.3083 26.8245Z"
        fill="url(#paint4_linear_40003463_39273)"
      />
      <path
        d="M35.3266 6.57292L30.0932 3.75625C29.5432 3.45625 28.8766 3.45625 28.3266 3.75625L23.0932 6.57292C22.7099 6.77292 22.4766 7.18958 22.4766 7.63958C22.4766 8.08958 22.7099 8.50625 23.0932 8.70625L28.3266 11.5229C28.6099 11.6729 28.9099 11.7396 29.2099 11.7396C29.5099 11.7396 29.8099 11.6729 30.0932 11.5229L35.3266 8.70625C35.7099 8.50625 35.9432 8.08958 35.9432 7.63958C35.9432 7.17292 35.6932 6.77292 35.3266 6.57292Z"
        fill="url(#paint5_linear_40003463_39273)"
      />
      <path
        d="M27.4583 12.4891L22.5917 10.0557C22.225 9.8724 21.7917 9.88906 21.425 10.1057C21.0917 10.3224 20.875 10.7057 20.875 11.1224V15.7224C20.875 16.5224 21.3083 17.2391 22.025 17.5891L26.8917 20.0224C27.0583 20.1057 27.2417 20.1557 27.425 20.1557C27.6417 20.1557 27.8583 20.0891 28.0583 19.9724C28.4083 19.7557 28.625 19.3724 28.625 18.9557V14.3557C28.625 13.5557 28.175 12.8391 27.4583 12.4891Z"
        fill="url(#paint6_linear_40003463_39273)"
      />
      <path
        d="M36.9724 10.1057C36.6224 9.88906 36.1891 9.8724 35.8057 10.0557L30.9391 12.4891C30.2224 12.8391 29.7891 13.5557 29.7891 14.3557V18.9557C29.7891 19.3724 30.0057 19.7557 30.3557 19.9724C30.5557 20.0891 30.7724 20.1557 30.9891 20.1557C31.1724 20.1557 31.3557 20.1057 31.5224 20.0224L36.3891 17.5891C37.1057 17.2224 37.5391 16.5057 37.5391 15.7224V11.1224C37.5391 10.7057 37.3224 10.3224 36.9724 10.1057Z"
        fill="url(#paint7_linear_40003463_39273)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40003463_39273"
          x1="24.3663"
          y1="40.1928"
          x2="41.6953"
          y2="38.6808"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_40003463_39273"
          x1="2.69476"
          y1="18.5288"
          x2="20.0037"
          y2="17.0203"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_40003463_39273"
          x1="5.55155"
          y1="29.6451"
          x2="21.8049"
          y2="27.3201"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_40003463_39273"
          x1="4.06526"
          y1="38.363"
          x2="13.5687"
          y2="37.734"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_40003463_39273"
          x1="12.9793"
          y1="38.363"
          x2="22.4828"
          y2="37.734"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint5_linear_40003463_39273"
          x1="22.2234"
          y1="12.9498"
          x2="38.4767"
          y2="10.6248"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint6_linear_40003463_39273"
          x1="20.7293"
          y1="21.6634"
          x2="30.2329"
          y2="21.0354"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint7_linear_40003463_39273"
          x1="29.6434"
          y1="21.6634"
          x2="39.147"
          y2="21.0354"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const FinancedEmissionsHomeIcon = () => {
  return (
    <svg
      width="41"
      height="41"
      viewBox="0 0 41 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M21.875 15.347V10.0383C22.4755 10.2216 23.0997 10.3155 23.7275 10.317C24.8185 10.3062 25.8935 10.0535 26.875 9.57705C28.0364 9.0984 29.0375 8.29903 29.7614 7.27238C30.4852 6.24573 30.9018 5.03424 30.9625 3.77955C30.96 3.60257 30.9199 3.42815 30.8449 3.26782C30.7699 3.1075 30.6617 2.96493 30.5275 2.84955C29.527 2.08973 28.3304 1.63109 27.0783 1.52747C25.8263 1.42385 24.5706 1.67954 23.4588 2.26455C22.3286 2.76992 21.3475 3.55769 20.61 4.55205C19.835 3.4946 18.7981 2.65713 17.6013 2.12205C16.4387 1.5111 15.1262 1.24379 13.8173 1.35137C12.5084 1.45895 11.2572 1.93698 10.21 2.72955C10.076 2.84479 9.96786 2.98716 9.89288 3.14726C9.81789 3.30735 9.77772 3.48153 9.77501 3.6583C9.83781 4.97012 10.273 6.2369 11.0297 7.31035C11.7863 8.38379 12.8331 9.21947 14.0475 9.71955C15.0772 10.2184 16.2046 10.4831 17.3488 10.4945C18.0365 10.4929 18.72 10.3854 19.375 10.1758V15.347C16.3508 15.6672 13.5647 17.1353 11.5909 19.4489C9.61717 21.7624 8.60624 24.7451 8.76652 27.7819C8.9268 30.8188 10.2461 33.6785 12.4524 35.7714C14.6587 37.8643 17.5839 39.0311 20.625 39.0311C23.6661 39.0311 26.5913 37.8643 28.7976 35.7714C31.004 33.6785 32.3232 30.8188 32.4835 27.7819C32.6438 24.7451 31.6329 21.7624 29.6591 19.4489C27.6854 17.1353 24.8992 15.6672 21.875 15.347ZM20.625 25.9045C21.4672 25.903 22.283 26.1979 22.9295 26.7375C23.576 27.2772 24.012 28.0271 24.1611 28.856C24.3102 29.6848 24.1628 30.5397 23.7448 31.2708C23.3268 32.0019 22.6649 32.5625 21.875 32.8545V33.0133C21.875 33.3448 21.7433 33.6628 21.5089 33.8972C21.2745 34.1316 20.9565 34.2633 20.625 34.2633C20.2935 34.2633 19.9756 34.1316 19.7411 33.8972C19.5067 33.6628 19.375 33.3448 19.375 33.0133V32.8545C18.6888 32.6007 18.0966 32.1432 17.6777 31.5434C17.2588 30.9435 17.0332 30.2299 17.0313 29.4983C17.0313 29.1668 17.163 28.8488 17.3974 28.6144C17.6318 28.38 17.9497 28.2483 18.2813 28.2483C18.6128 28.2483 18.9307 28.38 19.1651 28.6144C19.3996 28.8488 19.5313 29.1668 19.5313 29.4983C19.5313 29.7146 19.5954 29.9261 19.7156 30.106C19.8358 30.2858 20.0066 30.426 20.2065 30.5088C20.4063 30.5916 20.6262 30.6132 20.8384 30.571C21.0506 30.5288 21.2454 30.4247 21.3984 30.2717C21.5514 30.1187 21.6555 29.9238 21.6977 29.7117C21.7399 29.4995 21.7183 29.2796 21.6355 29.0797C21.5527 28.8799 21.4125 28.7091 21.2327 28.5889C21.0528 28.4687 20.8413 28.4045 20.625 28.4045C19.7823 28.4069 18.9656 28.1123 18.3184 27.5726C17.6711 27.0329 17.2346 26.2825 17.0855 25.453C16.9363 24.6236 17.0841 23.7681 17.5027 23.0367C17.9214 22.3053 18.5843 21.7447 19.375 21.4533V21.2945C19.375 20.963 19.5067 20.6451 19.7411 20.4107C19.9756 20.1762 20.2935 20.0445 20.625 20.0445C20.9565 20.0445 21.2745 20.1762 21.5089 20.4107C21.7433 20.6451 21.875 20.963 21.875 21.2945V21.4533C22.5621 21.7076 23.155 22.1661 23.5739 22.7672C23.9929 23.3683 24.2179 24.0831 24.2188 24.8158C24.2188 25.1473 24.0871 25.4653 23.8526 25.6997C23.6182 25.9341 23.3003 26.0658 22.9688 26.0658C22.6372 26.0658 22.3193 25.9341 22.0849 25.6997C21.8505 25.4653 21.7188 25.1473 21.7188 24.8158C21.7188 24.5995 21.6546 24.388 21.5344 24.2081C21.4142 24.0283 21.2434 23.8881 21.0436 23.8053C20.8437 23.7225 20.6238 23.7009 20.4116 23.7431C20.1995 23.7853 20.0046 23.8894 19.8516 24.0424C19.6987 24.1954 19.5945 24.3903 19.5523 24.6024C19.5101 24.8146 19.5317 25.0345 19.6145 25.2344C19.6973 25.4342 19.8375 25.605 20.0174 25.7252C20.1972 25.8454 20.4087 25.9095 20.625 25.9095V25.9045Z"
        fill="url(#paint0_linear_40003463_39279)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40003463_39279"
          x1="8.30357"
          y1="44.5898"
          x2="37.4667"
          y2="42.9881"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};
export const ISSBReadinessHomeIcon = () => {
  return (
    <svg
      width="57"
      height="43"
      viewBox="0 0 57 43"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.4291 15.6303H12.4158C11.5225 15.6303 10.7982 14.9061 10.7982 14.0127V11.6801C10.7982 10.7867 10.074 10.0625 9.18067 10.0625H3.28125"
        stroke="url(#paint0_linear_40004975_51131)"
        strokeWidth="1.29406"
      />
      <path
        d="M19.4291 27.2955H12.4158C11.5225 27.2955 10.7982 28.0197 10.7982 28.9131V31.2457C10.7982 32.1391 10.074 32.8633 9.18067 32.8633H3.28125"
        stroke="url(#paint1_linear_40004975_51131)"
        strokeWidth="1.29406"
      />
      <ellipse
        cx="3.33256"
        cy="10.1607"
        rx="2.53569"
        ry="2.53567"
        fill="url(#paint2_linear_40004975_51131)"
      />
      <ellipse
        cx="2.53569"
        cy="2.53567"
        rx="2.53569"
        ry="2.53567"
        transform="matrix(1 0 0 -1 0.796875 35.293)"
        fill="url(#paint3_linear_40004975_51131)"
      />
      <path
        d="M19.4564 21.5273H2.12891"
        stroke="url(#paint4_linear_40004975_51131)"
        strokeWidth="1.29406"
      />
      <ellipse
        cx="3.33256"
        cy="21.6411"
        rx="2.53569"
        ry="2.53567"
        fill="url(#paint5_linear_40004975_51131)"
      />
      <path
        d="M50.7358 18.5164H45.6699C41.5155 18.5164 38.1324 15.1333 38.1324 10.9789V5.91306C38.1324 4.94896 37.3436 4.16016 36.3795 4.16016H28.9472C23.5483 4.16016 19.1836 7.66595 19.1836 13.9238V29.4545C19.1836 35.7123 23.5483 39.2181 28.9472 39.2181H42.725C48.124 39.2181 52.4887 35.7123 52.4887 29.4545V20.2693C52.4887 19.3052 51.6999 18.5164 50.7358 18.5164Z"
        fill="url(#paint6_linear_40004975_51131)"
      />
      <path
        d="M42.4945 4.52906C41.7758 3.81037 40.5312 4.30119 40.5312 5.30034V11.418C40.5312 13.9772 42.7048 16.0982 45.3517 16.0982C47.017 16.1157 49.3308 16.1157 51.3116 16.1157C52.3107 16.1157 52.8366 14.9413 52.1354 14.2401C49.6113 11.6984 45.0888 7.12335 42.4945 4.52906Z"
        fill="url(#paint7_linear_40004975_51131)"
      />
      <path
        d="M26.4936 32.6172H23.8572L27.8743 20.9808H31.0447L35.0561 32.6172H32.4197L29.505 23.6399H29.4141L26.4936 32.6172ZM26.3288 28.0433H32.5561V29.9638H26.3288V28.0433ZM38.9169 20.9808V32.6172H36.4567V20.9808H38.9169Z"
        fill="white"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40004975_51131"
          x1="2.97772"
          y1="16.4512"
          x2="21.6705"
          y2="11.7243"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_40004975_51131"
          x1="2.97772"
          y1="26.4746"
          x2="21.6705"
          y2="31.2015"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_40004975_51131"
          x1="0.701548"
          y1="13.444"
          x2="6.90045"
          y2="12.9035"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_40004975_51131"
          x1="-0.0953266"
          y1="5.81904"
          x2="6.10358"
          y2="5.27855"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_40004975_51131"
          x1="1.8032"
          y1="22.6748"
          x2="8.30464"
          y2="12.8525"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint5_linear_40004975_51131"
          x1="0.701548"
          y1="24.9245"
          x2="6.90045"
          y2="24.384"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint6_linear_40004975_51131"
          x1="18.5576"
          y1="44.3869"
          x2="59.2974"
          y2="41.0124"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint7_linear_40004975_51131"
          x1="40.3071"
          y1="17.8744"
          x2="54.8836"
          y2="16.6038"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const CSRDReadinessHomeIcon = () => {
  return (
    <svg
      width="49"
      height="49"
      viewBox="0 0 49 49"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.835938"
        y="0.71875"
        width="48"
        height="47.3333"
        rx="12"
        fill="#07838F"
        fillOpacity="0.1"
      />
      <rect
        x="4.83594"
        y="4.71875"
        width="40"
        height="39.3333"
        rx="19.6667"
        fill="url(#paint0_linear_40003463_39461)"
      />
      <path
        d="M19.299 23.7464H17.6967C17.6854 23.6139 17.6551 23.4936 17.6058 23.3857C17.5585 23.2777 17.4922 23.1849 17.407 23.1072C17.3236 23.0277 17.2223 22.9671 17.103 22.9254C16.9837 22.8819 16.8482 22.8601 16.6967 22.8601C16.4316 22.8601 16.2071 22.9245 16.0234 23.0533C15.8416 23.1821 15.7034 23.3667 15.6087 23.6072C15.5159 23.8478 15.4695 24.1366 15.4695 24.4737C15.4695 24.8298 15.5168 25.1281 15.6115 25.3686C15.7081 25.6072 15.8473 25.7872 16.0291 25.9084C16.2109 26.0277 16.4297 26.0874 16.6854 26.0874C16.8312 26.0874 16.9619 26.0694 17.0774 26.0334C17.1929 25.9955 17.2933 25.9415 17.3786 25.8714C17.4638 25.8014 17.5329 25.7171 17.5859 25.6186C17.6409 25.5182 17.6778 25.4055 17.6967 25.2805L19.299 25.2919C19.2801 25.5381 19.2109 25.7891 19.0916 26.0447C18.9723 26.2985 18.8028 26.5334 18.5831 26.7493C18.3653 26.9633 18.0954 27.1357 17.7734 27.2663C17.4515 27.397 17.0774 27.4624 16.6513 27.4624C16.1172 27.4624 15.638 27.3478 15.2138 27.1186C14.7914 26.8894 14.4571 26.5523 14.2109 26.1072C13.9666 25.6622 13.8445 25.1177 13.8445 24.4737C13.8445 23.826 13.9695 23.2805 14.2195 22.8374C14.4695 22.3923 14.8066 22.0561 15.2308 21.8288C15.6551 21.5997 16.1286 21.4851 16.6513 21.4851C17.0187 21.4851 17.3568 21.5353 17.6655 21.6357C17.9742 21.736 18.245 21.8828 18.478 22.076C18.7109 22.2673 18.8984 22.5031 19.0405 22.7834C19.1825 23.0637 19.2687 23.3847 19.299 23.7464ZM23.152 23.3828C23.1368 23.1934 23.0658 23.0457 22.9389 22.9396C22.8139 22.8336 22.6236 22.7805 22.3679 22.7805C22.205 22.7805 22.0715 22.8004 21.9673 22.8402C21.8651 22.8781 21.7893 22.9302 21.7401 22.9964C21.6908 23.0627 21.6652 23.1385 21.6634 23.2237C21.6596 23.2938 21.6719 23.3572 21.7003 23.4141C21.7306 23.469 21.7779 23.5192 21.8423 23.5646C21.9067 23.6082 21.9891 23.648 22.0895 23.6839C22.1899 23.7199 22.3092 23.7521 22.4474 23.7805L22.9247 23.8828C23.2467 23.951 23.5223 24.041 23.7514 24.1527C23.9806 24.2644 24.1681 24.3961 24.3139 24.5476C24.4598 24.6972 24.5668 24.8658 24.6349 25.0533C24.705 25.2408 24.741 25.4453 24.7429 25.6669C24.741 26.0495 24.6454 26.3733 24.456 26.6385C24.2666 26.9036 23.9957 27.1054 23.6435 27.2436C23.2931 27.3819 22.8717 27.451 22.3793 27.451C21.8736 27.451 21.4323 27.3762 21.0554 27.2266C20.6804 27.0769 20.3887 26.8468 20.1804 26.5362C19.974 26.2237 19.8698 25.8241 19.8679 25.3374H21.3679C21.3774 25.5154 21.4219 25.665 21.5014 25.7862C21.581 25.9074 21.6927 25.9993 21.8366 26.0618C21.9825 26.1243 22.1558 26.1555 22.3565 26.1555C22.5251 26.1555 22.6662 26.1347 22.7798 26.093C22.8935 26.0514 22.9796 25.9936 23.0384 25.9197C23.0971 25.8459 23.1274 25.7616 23.1293 25.6669C23.1274 25.5779 23.098 25.5002 23.0412 25.4339C22.9863 25.3658 22.8954 25.3052 22.7685 25.2521C22.6416 25.1972 22.4702 25.1461 22.2543 25.0987L21.6747 24.9737C21.1596 24.862 20.7533 24.6754 20.456 24.4141C20.1605 24.1508 20.0137 23.7919 20.0156 23.3374C20.0137 22.968 20.1122 22.6451 20.3111 22.3686C20.5118 22.0902 20.7893 21.8733 21.1435 21.718C21.4995 21.5627 21.9077 21.4851 22.3679 21.4851C22.8376 21.4851 23.2438 21.5637 23.5866 21.7209C23.9295 21.8781 24.1937 22.0997 24.3793 22.3857C24.5668 22.6697 24.6615 23.0021 24.6634 23.3828H23.152ZM25.3438 27.3828V21.5646H27.8551C28.2869 21.5646 28.6648 21.6432 28.9886 21.8004C29.3125 21.9576 29.5644 22.1839 29.7443 22.4794C29.9242 22.7749 30.0142 23.129 30.0142 23.5419C30.0142 23.9586 29.9214 24.3099 29.7358 24.5959C29.5521 24.8819 29.2936 25.0978 28.9602 25.2436C28.6288 25.3894 28.2415 25.4624 27.7983 25.4624H26.2983V24.2351H27.4801C27.6657 24.2351 27.8239 24.2124 27.9545 24.1669C28.0871 24.1196 28.1884 24.0447 28.2585 23.9425C28.3305 23.8402 28.3665 23.7067 28.3665 23.5419C28.3665 23.3752 28.3305 23.2398 28.2585 23.1357C28.1884 23.0296 28.0871 22.9519 27.9545 22.9027C27.8239 22.8516 27.6657 22.826 27.4801 22.826H26.9233V27.3828H25.3438ZM28.7528 24.7124L30.2074 27.3828H28.4915L27.071 24.7124H28.7528ZM32.9261 27.3828H30.6875V21.5646H32.9034C33.5019 21.5646 34.0189 21.6811 34.4545 21.9141C34.892 22.1451 35.2292 22.4785 35.4659 22.9141C35.7045 23.3478 35.8239 23.8677 35.8239 24.4737C35.8239 25.0798 35.7055 25.6006 35.4688 26.0362C35.232 26.4699 34.8968 26.8033 34.4631 27.0362C34.0294 27.2673 33.517 27.3828 32.9261 27.3828ZM32.267 26.0419H32.8693C33.1572 26.0419 33.4025 25.9955 33.6051 25.9027C33.8097 25.8099 33.965 25.6499 34.071 25.4226C34.179 25.1953 34.233 24.879 34.233 24.4737C34.233 24.0684 34.178 23.7521 34.0682 23.5249C33.9602 23.2976 33.8011 23.1375 33.5909 23.0447C33.3826 22.9519 33.1269 22.9055 32.8239 22.9055H32.267V26.0419Z"
        fill="white"
      />
      <path
        d="M39.0755 33.8262L37.8701 34.4598L38.1006 33.1181L37.125 32.167L38.4728 31.9713L39.0755 30.75L39.6782 31.9713L41.0264 32.167L40.0512 33.1181L40.2813 34.4602L39.0755 33.8262Z"
        fill="white"
      />
      <path
        d="M15.8815 10.6489L14.9062 9.69823L16.2541 9.5025L16.8572 8.28125L17.4598 9.5025L18.8077 9.69823L17.832 10.6489L18.0625 11.9915L16.8572 11.3578L15.6514 11.9915L15.8815 10.6489Z"
        fill="white"
      />
      <path
        d="M10.1546 16.7352L9.17969 15.7845L10.5275 15.5888L11.1302 14.3672L11.7329 15.5888L13.0807 15.7842L12.1055 16.7352L12.3356 18.0774L11.1302 17.4437L9.92483 18.0774L10.1546 16.7352Z"
        fill="white"
      />
      <path
        d="M41.4271 25.6312L40.2213 26.2649L40.4518 24.9224L39.4766 23.9717L40.8244 23.7759L41.4271 22.5547L42.0298 23.7759L43.3776 23.9717L42.4023 24.9224L42.6325 26.2649L41.4271 25.6312Z"
        fill="white"
      />
      <path
        d="M33.3572 39.6551L32.1514 40.2887L32.3819 38.9462L31.4062 37.9955L32.7541 37.7994L33.3572 36.5781L33.9598 37.7994L35.3077 37.9955L34.3324 38.9462L34.5625 40.2887L33.3572 39.6551Z"
        fill="white"
      />
      <path
        d="M25.1072 9.13124L23.9014 9.76529L24.1315 8.42275L23.1562 7.47167L24.5041 7.27631L25.1072 6.05469L25.7098 7.27631L27.0577 7.47167L26.0824 8.42275L26.3125 9.76529L25.1072 9.13124Z"
        fill="white"
      />
      <path
        d="M33.3572 11.3578L32.1514 11.9915L32.3819 10.6489L31.4062 9.69823L32.7541 9.5025L33.3572 8.28125L33.9598 9.5025L35.3077 9.69823L34.3324 10.6489L34.5625 11.9915L33.3572 11.3578Z"
        fill="white"
      />
      <path
        d="M39.0755 17.4437L37.8701 18.0774L38.1006 16.7352L37.125 15.7842L38.4728 15.5888L39.0755 14.3672L39.6782 15.5888L41.0264 15.7845L40.0512 16.7352L40.2813 18.0774L39.0755 17.4437Z"
        fill="white"
      />
      <path
        d="M25.1072 41.9512L23.9014 42.5856L24.1315 41.2431L23.1562 40.2924L24.5041 40.0966L25.1072 38.875L25.7098 40.0966L27.0577 40.2924L26.0824 41.2431L26.3125 42.5856L25.1072 41.9512Z"
        fill="white"
      />
      <path
        d="M15.8815 38.9462L14.9062 37.9955L16.2541 37.7994L16.8572 36.5781L17.4598 37.7994L18.8077 37.9955L17.832 38.9462L18.0625 40.2887L16.8572 39.6551L15.6514 40.2887L15.8815 38.9462Z"
        fill="white"
      />
      <path
        d="M10.1546 33.1181L9.17969 32.167L10.5275 31.9713L11.1302 30.75L11.7329 31.9713L13.0807 32.167L12.1055 33.1181L12.3356 34.4598L11.1302 33.8262L9.92483 34.4602L10.1546 33.1181Z"
        fill="white"
      />
      <path
        d="M7.8112 24.9224L6.83594 23.9717L8.18377 23.7759L8.78684 22.5547L9.38953 23.7759L10.7374 23.9717L9.76172 24.9224L9.99222 26.2649L8.78684 25.6312L7.58108 26.2649L7.8112 24.9224Z"
        fill="white"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40003463_39461"
          x1="4.08406"
          y1="49.8512"
          x2="52.9647"
          y2="45.5171"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const SDGAlignmentHomeIcon = () => {
  return (
    <svg
      width="41"
      height="41"
      viewBox="0 0 41 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle
        cx="20.5"
        cy="20.3828"
        r="15.5"
        stroke="url(#paint0_linear_40003463_39313)"
        strokeWidth="9"
        strokeDasharray="8 3"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40003463_39313"
          x1="-0.25188"
          y1="46.2802"
          x2="48.6414"
          y2="42.0172"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const AntiGreenwashingHomeIcon = () => {
  return (
    <svg
      width="41"
      height="41"
      viewBox="0 0 41 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.65899 19.1328H12.6915C13.3392 19.1328 13.8795 19.6275 13.9366 20.2727C14.2263 23.5459 16.9218 26.0781 20.1633 26.0781C23.4049 26.0781 26.1003 23.5459 26.39 20.2727C26.4471 19.6275 26.9875 19.1328 27.6351 19.1328L33.7232 19.1328C34.0307 19.1327 34.3765 19.1325 34.6706 19.1732C35.022 19.2219 35.4787 19.349 35.8591 19.7426C36.2338 20.1303 36.349 20.5825 36.3891 20.9343C36.4225 21.2272 36.4127 21.6247 36.4053 21.9282C36.3874 22.6722 36.3443 23.438 36.2379 24.2149C36.222 24.3325 36.193 24.5463 36.1497 24.7086C36.0965 24.9079 35.9692 25.2686 35.6123 25.5478C35.218 25.8562 34.7865 25.8654 34.6158 25.864C34.4452 25.8626 34.2172 25.8357 34.0736 25.8188C32.8598 25.6769 31.6439 26.6956 31.6439 28.0226C31.6439 28.6958 31.9329 29.2934 32.3867 29.6999C32.4931 29.795 32.6623 29.9463 32.7753 30.0719C32.8903 30.1996 33.1612 30.5207 33.1982 31.0078C33.232 31.4542 33.0563 31.7885 32.9463 31.9626C32.857 32.1039 32.7207 32.2694 32.6458 32.3604C31.9102 33.2562 31.083 34.0718 30.1783 34.7923C30.1 34.8549 29.9502 34.9745 29.8206 35.0547C29.6567 35.1561 29.3576 35.3092 28.9574 35.2976C28.5216 35.2851 28.2135 35.0788 28.0816 34.9833C27.9579 34.8937 27.8078 34.7593 27.7177 34.6785C27.3356 34.3368 26.8417 34.1343 26.3017 34.1343C25.1731 34.1343 24.1959 35.1032 24.1445 36.2798C24.1393 36.4008 24.1308 36.6002 24.1091 36.7498C24.086 36.9085 24.021 37.2663 23.7362 37.5851C23.474 37.8788 23.1636 37.992 22.9777 38.0428C22.8316 38.0827 22.6439 38.1105 22.5462 38.1249C20.9888 38.3577 19.3378 38.3577 17.7804 38.1249C17.6828 38.1105 17.495 38.0827 17.349 38.0428C17.163 37.992 16.8527 37.8788 16.5904 37.5851C16.3056 37.2663 16.2406 36.9085 16.2176 36.7498C16.1958 36.6002 16.1873 36.4008 16.1821 36.2798C16.1307 35.1032 15.1535 34.1343 14.025 34.1343C13.4849 34.1343 12.991 34.3368 12.6089 34.6785C12.5188 34.7593 12.3687 34.8937 12.245 34.9833C12.1131 35.0788 11.805 35.2851 11.3692 35.2976C10.9691 35.3092 10.6699 35.1561 10.506 35.0547C10.3764 34.9745 10.2267 34.8549 10.1483 34.7923C9.24368 34.0718 8.41639 33.2562 7.68085 32.3604C7.60591 32.2694 7.46961 32.1039 7.38032 31.9626C7.27033 31.7885 7.09461 31.4542 7.12847 31.0078C7.16542 30.5207 7.43638 30.1996 7.55133 30.0719C7.66437 29.9463 7.83351 29.795 7.93993 29.6999C8.39377 29.2934 8.68277 28.6958 8.68277 28.0226C8.68277 26.6956 7.46683 25.6769 6.25305 25.8188C6.10941 25.8357 5.88143 25.8626 5.71088 25.864C5.54012 25.8654 5.10862 25.8562 4.71434 25.5478C4.35743 25.2686 4.23012 24.908 4.17695 24.7086C4.13364 24.5463 4.10467 24.3325 4.08875 24.2149C3.98235 23.438 3.9393 22.6722 3.92139 21.9282C3.91393 21.6247 3.90418 21.2272 3.93756 20.9343C3.97765 20.5825 4.09287 20.1303 4.46751 19.7426C4.84796 19.349 5.30469 19.2219 5.65605 19.1732C5.95009 19.1325 6.35148 19.1327 6.65899 19.1328Z"
        fill="url(#paint0_linear_40003463_39319)"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.1749 2.97239C13.162 1.90658 16.6122 2.52508 18.9846 4.89748C20.2267 6.13967 20.9881 7.67732 21.2786 9.28927C23.3015 7.47154 26.113 7.02797 28.5584 7.9005C28.9116 8.02654 29.1896 8.30451 29.3156 8.65773C30.229 11.2176 29.7002 14.1787 27.6625 16.2164C25.9652 17.9137 23.6272 18.5642 21.4141 18.2062V20.3854C21.4141 21.0758 20.8544 21.6354 20.1641 21.6354C19.4737 21.6354 18.9141 21.0758 18.9141 20.3854L18.9141 14.8051C16.2484 15.3299 13.3931 14.5896 11.3428 12.5392C8.97039 10.1669 8.35189 6.7167 9.41771 3.72962C9.54374 3.3764 9.82171 3.09843 10.1749 2.97239Z"
        fill="url(#paint1_linear_40003463_39319)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40003463_39319"
          x1="3.30319"
          y1="41.1254"
          x2="42.4731"
          y2="35.3346"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_40003463_39319"
          x1="8.52246"
          y1="24.4613"
          x2="33.9529"
          y2="22.0512"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const IntegratedRiskHomeIcon = () => {
  return (
    <svg
      width="41"
      height="41"
      viewBox="0 0 41 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M36.3104 18.6625L34.0604 16.0292C33.6437 15.5292 33.2938 14.5958 33.2938 13.9292V11.0958C33.2938 9.32917 31.8437 7.87917 30.0771 7.87917H27.2438C26.5771 7.87917 25.6271 7.52917 25.1271 7.1125L22.4937 4.8625C21.3438 3.87917 19.4604 3.87917 18.3104 4.8625L15.6437 7.1125C15.1437 7.52917 14.2104 7.87917 13.5437 7.87917H10.6604C8.89375 7.87917 7.44375 9.32917 7.44375 11.0958V13.9292C7.44375 14.5792 7.11042 15.5125 6.69375 16.0125L4.44375 18.6625C3.47708 19.8292 3.47708 21.6958 4.44375 22.8292L6.69375 25.4792C7.11042 25.9625 7.44375 26.9125 7.44375 27.5625V30.4125C7.44375 32.1792 8.89375 33.6292 10.6604 33.6292H13.5604C14.2104 33.6292 15.1604 33.9792 15.6604 34.3958L18.2937 36.6458C19.4437 37.6292 21.3271 37.6292 22.4771 36.6458L25.1104 34.3958C25.6104 33.9792 26.5438 33.6292 27.2104 33.6292H30.0437C31.8104 33.6292 33.2604 32.1792 33.2604 30.4125V27.5792C33.2604 26.9125 33.6104 25.9792 34.0271 25.4792L36.2771 22.8458C37.2938 21.7125 37.2938 19.8292 36.3104 18.6625ZM19.1271 14.3125C19.1271 13.6292 19.6937 13.0625 20.3771 13.0625C21.0604 13.0625 21.6271 13.6292 21.6271 14.3125V22.3625C21.6271 23.0458 21.0604 23.6125 20.3771 23.6125C19.6937 23.6125 19.1271 23.0458 19.1271 22.3625V14.3125ZM20.3771 28.8792C19.4604 28.8792 18.7104 28.1292 18.7104 27.2125C18.7104 26.2958 19.4437 25.5458 20.3771 25.5458C21.2937 25.5458 22.0437 26.2958 22.0437 27.2125C22.0437 28.1292 21.3104 28.8792 20.3771 28.8792Z"
        fill="url(#paint0_linear_40003463_39217)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40003463_39217"
          x1="3.09234"
          y1="42.2868"
          x2="43.8254"
          y2="38.7281"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const ESGDueDiligenceHomeIcon = () => {
  return (
    <svg
      width="41"
      height="41"
      viewBox="0 0 41 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17.7895 26.8734C18.162 27.2459 18.162 27.85 17.7895 28.2225C17.417 28.595 16.8129 28.595 16.4403 28.2225L16.2155 27.9977L8.45797 35.7552C7.40238 36.8108 5.69102 36.8108 4.63544 35.7552C3.57985 34.6996 3.57985 32.9882 4.63544 31.9327L12.393 24.1751L12.1681 23.9503C11.7956 23.5778 11.7956 22.9738 12.1681 22.6013C12.5407 22.2287 13.1447 22.2287 13.5172 22.6013L17.7895 26.8734ZM36.4062 14.4781C36.4062 20.2644 31.6988 24.9718 25.9125 24.9718C23.3621 24.9718 21.0217 24.057 19.2011 22.5386L17.677 24.0627L16.3279 22.7136L17.852 21.1895C16.3337 19.3691 15.4188 17.0285 15.4188 14.4781C15.4188 8.69181 20.1262 3.98438 25.9125 3.98438C31.6988 3.98438 36.4062 8.69181 36.4062 14.4781ZM31.9544 11.2982C31.9544 10.7714 31.5273 10.3442 31.0003 10.3442H29.7284C28.1697 10.3442 26.7834 11.0958 25.9124 12.2555C25.0414 11.0958 23.6551 10.3442 22.0965 10.3442H20.8245C20.2977 10.3442 19.8705 10.7713 19.8705 11.2982V12.5702C19.8705 15.2003 22.0102 17.34 24.6403 17.34H24.9583V19.566C24.9583 20.0929 25.3854 20.5201 25.9123 20.5201C26.4391 20.5201 26.8663 20.093 26.8663 19.566V17.34H27.1843C29.8144 17.34 31.9541 15.2003 31.9541 12.5702V11.2982H31.9544ZM26.8664 15.1142V15.4321H27.1844C28.7625 15.4321 30.0463 14.1483 30.0463 12.5702V12.2522H29.7284C28.1503 12.2522 26.8664 13.5361 26.8664 15.1142ZM22.0966 12.2522H21.7786V12.5702C21.7786 14.1483 23.0624 15.4321 24.6405 15.4321H24.9585V15.1142C24.9585 13.5361 23.6747 12.2522 22.0966 12.2522Z"
        fill="url(#paint0_linear_40003463_39225)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40003463_39225"
          x1="3.23167"
          y1="41.3478"
          x2="43.0339"
          y2="37.8774"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const HumanRightsDueDiligenceHomeIcon = () => {
  return (
    <svg
      width="41"
      height="41"
      viewBox="0 0 41 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M20.9468 12.7243C20.5223 12.7243 20.1126 12.7873 19.7262 12.9041L20.0891 7.14766C20.3369 7.2318 20.6024 7.27742 20.8786 7.27742C21.1548 7.27742 21.4203 7.2318 21.6681 7.14766L22.0285 12.8648C21.6833 12.7733 21.3208 12.7243 20.9468 12.7243ZM21.5047 2.44867V1.95422C21.5047 1.60984 21.223 1.32812 20.8786 1.32812C20.5342 1.32812 20.2524 1.60992 20.2524 1.95422V2.44867C20.4523 2.39609 20.6621 2.36797 20.8785 2.36797C21.0949 2.36797 21.3048 2.39609 21.5046 2.44867H21.5047ZM36.7415 13.8272C36.7415 16.2809 34.7523 18.27 32.2987 18.27C29.845 18.27 27.8559 16.2809 27.8559 13.8272H28.0392L31.8263 5.95602C31.5941 5.85875 31.3986 5.69172 31.2643 5.48187C27.9296 6.10773 25.9811 4.50812 23.3084 5.1718C23.3246 5.05773 23.3332 4.94117 23.3332 4.82266C23.3332 4.24695 23.1349 3.71766 22.8031 3.29898C25.8249 2.57297 28.382 4.73328 31.1501 4.39172C31.3254 3.92695 31.7738 3.5943 32.2987 3.5943C32.9752 3.5943 33.527 4.14609 33.527 4.82266C33.527 5.33195 33.2142 5.77031 32.7711 5.95602L36.5581 13.8272H36.7415ZM35.522 13.8272L32.2987 7.12773L29.0754 13.8272H35.522H35.522ZM9.45844 18.2701C7.00477 18.2701 5.01562 16.2809 5.01562 13.8273H5.19906L8.98609 5.95609C8.54289 5.77039 8.23016 5.33203 8.23016 4.82273C8.23016 4.14617 8.78195 3.59437 9.45852 3.59437C9.98336 3.59437 10.4318 3.92703 10.6071 4.3918C13.3752 4.73336 15.9322 2.57305 18.9541 3.29906C18.6222 3.71766 18.424 4.24703 18.424 4.82273C18.424 4.94125 18.4326 5.05781 18.4488 5.17187C15.7761 4.5082 13.8277 6.10781 10.493 5.48195C10.3586 5.6918 10.1631 5.85883 9.93102 5.95609L13.718 13.8273H13.9015C13.9015 16.2809 11.9123 18.2701 9.45867 18.2701H9.45844ZM12.6817 13.8273L9.45844 7.12781L6.23516 13.8273H12.6817ZM20.8785 6.18367C21.6281 6.18367 22.2395 5.57227 22.2395 4.82273C22.2395 4.0732 21.6281 3.4618 20.8785 3.4618C20.1289 3.4618 19.5176 4.0732 19.5176 4.82273C19.5176 5.57227 20.129 6.18367 20.8785 6.18367ZM20.8785 34.1903C17.2799 34.1903 14.3627 31.273 14.3627 27.6744H9.35516V29.593L11.6724 30.05L12.69 32.5032L11.3737 34.4661L14.087 37.1795L16.0498 35.8631L18.503 36.8808L18.96 39.198H22.7972L23.2541 36.8808L25.7073 35.8631L27.6702 37.1795L30.3835 34.4661L29.0672 32.5032L30.0848 30.05L32.402 29.593V27.6744H27.3945C27.3945 31.273 24.4772 34.1903 20.8785 34.1903ZM14.4233 26.0341H27.4702C27.1694 22.6952 24.3638 20.0786 20.9467 20.0786C17.5296 20.0786 14.7241 22.6952 14.4233 26.0341ZM20.9467 13.818C19.2284 13.818 17.827 15.2195 17.827 16.9378C17.827 18.6562 19.2284 20.0575 20.9467 20.0575C22.665 20.0575 24.0665 18.6561 24.0665 16.9378C24.0665 15.2195 22.6651 13.818 20.9467 13.818Z"
        fill="url(#paint0_linear_40003463_39232)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40003463_39232"
          x1="4.41927"
          y1="44.7813"
          x2="43.2863"
          y2="41.9423"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const DecisionSightHomeIcon = () => {
  return (
    <svg
      width="41"
      height="41"
      viewBox="0 0 41 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M30.55 13.6641C30.9196 13.8604 31.1024 13.9335 31.2595 14.0432C34.146 16.0551 37.0312 18.0697 39.911 20.0911C40.6381 20.6016 40.6286 20.7207 39.8921 21.2379C37.0285 23.243 34.1636 25.2455 31.2974 27.2479C31.1227 27.3698 30.9345 27.4727 30.6353 27.6555C30.5988 27.2872 30.5568 27.0543 30.5554 26.8201C30.5473 26.0308 30.5297 25.2401 30.5595 24.4508C30.5798 23.9308 30.4146 23.7494 29.869 23.7575C26.4531 23.8117 23.7344 26.6563 23.7114 30.2794C23.691 33.5058 23.6924 36.7322 23.7181 39.9599C23.7235 40.5678 23.5665 40.7844 22.9302 40.7682C21.3515 40.7262 19.7715 40.7438 18.1928 40.7601C17.7568 40.7641 17.5334 40.6721 17.5375 40.1684C17.5646 36.5128 17.4725 32.8545 17.6052 29.2044C17.8137 23.4989 22.4766 18.4935 28.1306 17.7367C28.733 17.6554 29.3423 17.5931 29.9475 17.5986C30.4417 17.604 30.5649 17.4022 30.5568 16.9554C30.5365 15.9224 30.55 14.8921 30.55 13.6641Z"
        fill="url(#paint0_linear_40003463_39239)"
      />
      <path
        d="M19.6 20.8328C18.1567 22.5143 17.1467 24.3814 16.5361 26.572C16.3628 26.3784 16.2355 26.243 16.115 26.1009C14.8667 24.6278 13.284 23.8479 11.3506 23.7518C10.8401 23.7261 10.6723 23.9102 10.6912 24.4071C10.721 25.1951 10.702 25.9858 10.6966 26.7751C10.6953 27.0134 10.6682 27.2503 10.6438 27.6335C10.3257 27.4534 10.1144 27.3532 9.92624 27.2219C7.05999 25.2235 4.19646 23.2224 1.33427 21.2173C0.628874 20.7231 0.623457 20.5931 1.31531 20.1084C4.19375 18.0884 7.07624 16.0724 9.96009 14.0604C10.1334 13.94 10.3365 13.8601 10.6993 13.6719C10.6993 14.269 10.6993 14.7076 10.6993 15.1449C10.6993 15.7312 10.7305 16.3201 10.6912 16.9037C10.6547 17.4507 10.8794 17.5847 11.3966 17.5888C14.2723 17.6131 16.8272 18.5595 19.0693 20.3548C19.2385 20.4929 19.3956 20.6486 19.6 20.8328Z"
        fill="url(#paint1_linear_40003463_39239)"
      />
      <path
        d="M13.7031 10.5496C14.1012 9.97415 14.4153 9.51788 14.7321 9.06432C16.5518 6.45802 18.3728 3.85172 20.1952 1.24813C20.6447 0.606372 20.8234 0.603663 21.2742 1.24677C23.3065 4.14416 25.3333 7.04562 27.3588 9.94978C27.4684 10.1068 27.5388 10.2923 27.681 10.5685C27.3899 10.6078 27.2044 10.6525 27.0189 10.6552C26.1619 10.6633 25.3022 10.6971 24.4478 10.647C23.8291 10.6105 23.6856 10.8488 23.6951 11.4242C23.7248 13.2953 23.6937 15.1678 23.7154 17.0403C23.7208 17.4735 23.5894 17.7226 23.209 17.9555C22.4427 18.4253 21.7089 18.952 20.994 19.5003C20.7002 19.7251 20.5323 19.7129 20.2466 19.4949C19.5114 18.9371 18.7587 18.3996 17.9774 17.9108C17.6376 17.6983 17.5347 17.4695 17.5374 17.0944C17.551 15.1773 17.5293 13.2601 17.5523 11.343C17.5591 10.8176 17.3899 10.624 16.8551 10.6498C16.0671 10.689 15.2764 10.666 14.487 10.6565C14.2772 10.6525 14.0673 10.601 13.7031 10.5496Z"
        fill="url(#paint2_linear_40003463_39239)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40003463_39239"
          x1="17.1004"
          y1="44.7653"
          x2="45.1757"
          y2="42.6954"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_40003463_39239"
          x1="0.447413"
          y1="29.6919"
          x2="23.2862"
          y2="27.0106"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_40003463_39239"
          x1="13.4404"
          y1="22.45"
          x2="30.5846"
          y2="21.3443"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const ConnectorsHomeIcon = () => {
  return (
    <svg
      width="41"
      height="41"
      viewBox="0 0 41 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_40003463_39431)">
        <path
          d="M20.5 33.2874C18.0852 33.2874 15.7809 32.9344 14.0116 32.2932C13.4035 32.0729 12.8896 31.8286 12.4531 31.5703V34.1634C12.4531 35.7737 16.0559 37.0792 20.5 37.0792C24.9441 37.0792 28.5469 35.7737 28.5469 34.1634V31.5703C28.1105 31.8287 27.5965 32.073 26.9884 32.2933C25.2191 32.9344 22.9148 33.2874 20.5 33.2874Z"
          fill="url(#paint0_linear_40003463_39431)"
        />
        <path
          d="M20.5 26.7209C18.0911 26.7209 15.7884 26.3672 14.0161 25.725C13.4061 25.504 12.8906 25.259 12.4531 25V28.0268C12.4531 29.6371 16.0559 30.9426 20.5 30.9426C24.9441 30.9426 28.5469 29.6371 28.5469 28.0268V25.0001C28.1094 25.2591 27.5939 25.5041 26.984 25.7251C25.2116 26.3672 22.909 26.7209 20.5 26.7209Z"
          fill="url(#paint1_linear_40003463_39431)"
        />
        <path
          d="M20.5 24.3784C24.9442 24.3784 28.5469 23.073 28.5469 21.4627C28.5469 19.8523 24.9442 18.5469 20.5 18.5469C16.0558 18.5469 12.4531 19.8523 12.4531 21.4627C12.4531 23.073 16.0558 24.3784 20.5 24.3784Z"
          fill="url(#paint2_linear_40003463_39431)"
        />
        <path
          d="M30.9688 8.4218C30.6384 8.4218 30.3074 8.43898 29.9784 8.47313C29.0884 6.99273 27.8616 5.7368 26.3922 4.80938C24.6302 3.69719 22.5927 3.10938 20.5 3.10938C14.7427 3.10938 9.99906 7.53367 9.49047 13.1609C8.92758 13.0236 8.34781 12.9531 7.76562 12.9531C3.75938 12.9531 0.5 16.2124 0.5 20.2188C0.5 24.2251 3.75938 27.4844 7.76562 27.4844H10.1094V21.4604C10.1094 20.3967 10.617 18.4284 14.0161 17.1967C15.7884 16.5545 18.0911 16.2009 20.5 16.2009C22.9089 16.2009 25.2116 16.5545 26.9839 17.1967C30.3831 18.4284 30.8906 20.3967 30.8906 21.4604V27.4844H30.9688C36.2243 27.4844 40.5 23.2087 40.5 17.9531C40.5 12.6976 36.2243 8.4218 30.9688 8.4218Z"
          fill="url(#paint3_linear_40003463_39431)"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_40003463_39431"
          x1="12.1506"
          y1="37.8914"
          x2="30.7644"
          y2="33.1502"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_40003463_39431"
          x1="12.1506"
          y1="31.8187"
          x2="30.9252"
          y2="27.3855"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_40003463_39431"
          x1="12.1506"
          y1="25.2382"
          x2="30.8872"
          y2="20.7297"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_40003463_39431"
          x1="-0.25188"
          y1="31.0781"
          x2="48.0248"
          y2="24.1706"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <clipPath id="clip0_40003463_39431">
          <rect
            width="40"
            height="40"
            fill="white"
            transform="translate(0.5 0.09375)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const DocVaultHomeIcon = () => {
  return (
    <svg
      width="41"
      height="41"
      viewBox="0 0 41 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M35.5997 19.7937L35.333 19.4271C34.8663 18.8604 34.3163 18.4104 33.683 18.0771C32.833 17.5937 31.8663 17.3438 30.8663 17.3438H10.1163C9.11634 17.3438 8.16634 17.5937 7.29968 18.0771C6.64968 18.4271 6.06634 18.9104 5.58301 19.5104C4.63301 20.7271 4.18301 22.2271 4.33301 23.7271L4.94968 31.5104C5.16634 33.8604 5.44968 36.7604 10.733 36.7604H30.2663C35.5497 36.7604 35.8163 33.8604 36.0497 31.4938L36.6663 23.7437C36.8163 22.3437 36.4497 20.9437 35.5997 19.7937ZM24.483 28.9938H16.4997C15.8497 28.9938 15.333 28.4604 15.333 27.8271C15.333 27.1937 15.8497 26.6604 16.4997 26.6604H24.483C25.133 26.6604 25.6497 27.1937 25.6497 27.8271C25.6497 28.4771 25.133 28.9938 24.483 28.9938Z"
        fill="url(#paint0_linear_40003463_39425)"
      />
      <path
        d="M34.8138 14.9968C34.838 15.3808 34.4253 15.6362 34.0703 15.4881C33.0709 15.0714 32.0029 14.863 30.8828 14.863H10.1161C8.99362 14.863 7.89202 15.0828 6.89427 15.4974C6.54319 15.6433 6.13281 15.3981 6.13281 15.0179V11.1964C6.13281 5.24635 7.94948 3.42969 13.8995 3.42969H15.8661C18.2495 3.42969 18.9995 4.19635 19.9661 5.44635L21.9661 8.11302C22.3828 8.67969 22.3995 8.71302 23.1328 8.71302H27.0995C32.533 8.71302 34.5138 10.231 34.8138 14.9968Z"
        fill="url(#paint1_linear_40003463_39425)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40003463_39425"
          x1="3.69577"
          y1="39.6231"
          x2="42.7668"
          y2="33.9396"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_40003463_39425"
          x1="5.59303"
          y1="17.6961"
          x2="39.5828"
          y2="10.8513"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const GreenHubHomeIcon = () => {
  return (
    <svg
      width="33"
      height="36"
      viewBox="0 0 33 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M30.3077 14.587C30.7154 14.6283 31.1743 14.7611 31.571 15.1438C31.9726 15.5314 32.1195 15.9918 32.1687 16.4048C32.2079 16.7344 32.1891 17.1772 32.1748 17.5149C32.0344 20.8669 31.2126 25.0208 29.0049 27.8241C27.8757 29.2579 26.3632 30.3614 24.4024 30.772C22.8102 31.1055 21.0142 30.963 19.0069 30.2883C21.3804 27.3286 23.8107 25.4472 25.4441 24.3578C26.1834 23.8648 26.3831 22.8655 25.8903 22.126C25.3975 21.3864 24.3987 21.1865 23.6594 21.6796C21.8481 22.8876 19.1943 24.9437 16.5995 28.147C15.7993 26.0377 15.6014 24.1484 15.9421 22.4742C16.3385 20.5261 17.4348 19.0143 18.8611 17.8784C21.6514 15.6563 25.8111 14.7779 29.1924 14.5962C29.5359 14.5776 29.978 14.5536 30.3077 14.587ZM13.4974 32.6784C14.4785 30.9608 15.5314 29.4562 16.5943 28.1441C16.7192 28.4734 16.8588 28.808 17.0133 29.148C17.1369 29.4199 17.3568 29.6363 17.6305 29.7554C18.0981 29.9588 18.5551 30.1352 19.0017 30.2853C18.0723 31.4443 17.1516 32.7686 16.2909 34.2754C15.8501 35.0471 14.8674 35.3153 14.096 34.8743C13.3246 34.4333 13.0566 33.4502 13.4974 32.6784Z"
        fill="url(#paint0_linear_40003463_39442)"
      />
      <path
        d="M19.3427 1.81546C16.8159 -0.480153 12.9618 -0.480153 10.435 1.81546C8.49454 3.5784 6.11306 6.01175 4.20865 8.80636C2.31487 11.5854 0.8125 14.8387 0.8125 18.2161C0.8125 23.6205 4.46107 29.4822 10.7068 31.2675C11.0255 31.3586 11.1849 31.4042 11.337 31.3443C11.4891 31.2844 11.5807 31.1326 11.7639 30.8289C12.3577 29.8443 12.9807 28.9295 13.6057 28.0772C13.7449 27.8874 13.8145 27.7925 13.8351 27.6864C13.8558 27.5803 13.826 27.4627 13.7665 27.2275C13.3102 25.4258 13.2266 23.6743 13.5669 22.002C14.0973 19.3951 15.5676 17.4166 17.3476 15.999C19.8609 13.9975 23.1471 12.9734 26.1333 12.5019C26.6544 12.4197 26.915 12.3785 27.0325 12.2202C27.0648 12.1766 27.088 12.1314 27.1046 12.0798C27.1648 11.8919 27.0495 11.6622 26.8189 11.2027C24.8391 7.41406 21.83 4.07513 19.3427 1.81546Z"
        fill="url(#paint1_linear_40003463_39442)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40003463_39442"
          x1="12.9298"
          y1="38.1114"
          x2="36.0666"
          y2="36.2524"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_40003463_39442"
          x1="0.317991"
          y1="35.9828"
          x2="32.5461"
          y2="33.6193"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const BarsNavBarIcon = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 25 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.5 5.5L14.5 5.5"
        stroke="#9B9B9B"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.5 12.5L20.5 12.5"
        stroke="#9B9B9B"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.5 19.5L20.5 19.5"
        stroke="#9B9B9B"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const SearchIconNavBar = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.55 19.1C15.272 19.1 19.1 15.272 19.1 10.55C19.1 5.82796 15.272 2 10.55 2C5.82797 2 2 5.82796 2 10.55C2 15.272 5.82797 19.1 10.55 19.1Z"
        stroke="#B6B5BF"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M20.0002 20.0031L18.2002 18.2031"
        stroke="#B6B5BF"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const NotificationIconNavBar = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 19 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg "
    >
      <path
        d="M18.5 5.00001C18.5 6.93001 16.93 8.50001 15 8.50001C13.07 8.50001 11.5 6.93001 11.5 5.00001C11.5 3.07001 13.07 1.50001 15 1.50001C16.93 1.50001 18.5 3.07001 18.5 5.00001ZM16.5 10.29C16 10.42 15.5 10.5 15 10.5C13.5421 10.4974 12.1447 9.91705 11.1138 8.88617C10.083 7.8553 9.50264 6.45788 9.5 5.00001C9.5 3.53001 10.08 2.20001 11 1.21001C10.8185 0.987546 10.5897 0.808375 10.3302 0.685549C10.0707 0.562723 9.78709 0.499335 9.5 0.500005C8.4 0.500005 7.5 1.40001 7.5 2.50001V2.79001C4.53 3.67001 2.5 6.40001 2.5 9.50001V15.5L0.5 17.5V18.5H18.5V17.5L16.5 15.5V10.29ZM9.5 21.5C10.61 21.5 11.5 20.61 11.5 19.5H7.5C7.5 20.0304 7.71071 20.5391 8.08579 20.9142C8.46086 21.2893 8.96957 21.5 9.5 21.5Z"
        fill="#9B9B9B"
      />
      {/* <circle cx="15" cy="5" r="3.5" fill="#AB0202" /> */}
    </svg>
  );
};

export const QuestionMarkIconNavBar = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 15 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.49996 5.07386C6.24986 5.07386 5.23646 6.08726 5.23646 7.33736C5.23646 8.58745 4.22306 9.60085 2.97297 9.60085C1.72287 9.60085 0.709473 8.58745 0.709473 7.33736C0.709473 3.58708 3.74968 0.546875 7.49996 0.546875C11.2502 0.546875 14.2904 3.58708 14.2904 7.33736C14.2904 8.6858 13.8951 9.9488 13.2131 11.0085C12.804 11.6444 12.3296 12.2455 11.9071 12.7705C11.8281 12.8687 11.7511 12.9641 11.6759 13.0572C11.3262 13.49 11.0156 13.8746 10.7278 14.2701C10.0123 15.2534 9.76345 15.8866 9.76345 16.3913C9.76345 17.6414 8.75005 18.6548 7.49996 18.6548C6.24986 18.6548 5.23646 17.6414 5.23646 16.3913C5.23646 14.3959 6.21602 12.7765 7.0672 11.6067C7.42901 11.1094 7.8262 10.6181 8.17733 10.1838C8.24696 10.0977 8.31478 10.0138 8.38015 9.9326C8.79887 9.41222 9.1389 8.97431 9.4062 8.5589C9.63214 8.20776 9.76345 7.79135 9.76345 7.33736C9.76345 6.08726 8.75005 5.07386 7.49996 5.07386Z"
        fill="#9B9B9B"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.23682 23.1854C5.23682 21.9353 6.24569 20.9219 7.49019 20.9219H7.51043C8.75493 20.9219 9.7638 21.9353 9.7638 23.1854C9.7638 24.4355 8.75493 25.4489 7.51043 25.4489H7.49019C6.24569 25.4489 5.23682 24.4355 5.23682 23.1854Z"
        fill="#9B9B9B"
      />
    </svg>
  );
};

export const MoonIconNavBar = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 28 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.625 20.5801C10.625 21.4901 10.7713 22.3784 11.0413 23.2017C6.72129 21.7609 3.45879 17.9367 3.12129 13.4626C2.78379 8.70673 5.63004 4.26506 10.2313 2.40173C11.4238 1.92506 12.0313 2.27173 12.29 2.5209C12.5375 2.75923 12.8863 3.3334 12.3913 4.42756C11.885 5.55423 11.6375 6.74589 11.6375 7.9809C11.6488 10.1909 12.5488 12.2384 14.0113 13.8092C11.9525 15.3909 10.625 17.8392 10.625 20.5801Z"
        fill="#9B9B9B"
      />
      <path
        d="M24.3612 19.1967C22.1337 22.1108 18.6012 23.8225 14.8325 23.8225C14.6525 23.8225 14.4725 23.8117 14.2925 23.8008C13.1675 23.7575 12.0762 23.5517 11.0412 23.205C10.7712 22.3817 10.625 21.4933 10.625 20.5833C10.625 17.8425 11.9525 15.3942 14.0113 13.8125C15.665 15.6 18.0388 16.7592 20.66 16.8675C21.3687 16.9 22.0775 16.8458 22.775 16.7267C24.035 16.51 24.5413 16.965 24.7213 17.2575C24.9125 17.55 25.115 18.1892 24.3612 19.1967Z"
        fill="#9B9B9B"
      />
    </svg>
  );
};

export const EditProfileIcon = () => {
  return (
    <svg
      className="size-5"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1.5 18C1.5 18 0 18 0 16.5C0 15 1.5 10.5 9 10.5C16.5 10.5 18 15 18 16.5C18 18 16.5 18 16.5 18H1.5ZM9 9C10.1935 9 11.3381 8.52589 12.182 7.68198C13.0259 6.83807 13.5 5.69347 13.5 4.5C13.5 3.30653 13.0259 2.16193 12.182 1.31802C11.3381 0.474106 10.1935 0 9 0C7.80653 0 6.66193 0.474106 5.81802 1.31802C4.97411 2.16193 4.5 3.30653 4.5 4.5C4.5 5.69347 4.97411 6.83807 5.81802 7.68198C6.66193 8.52589 7.80653 9 9 9Z"
        fill="#07838F"
      />
    </svg>
  );
};

export const CheckListGSIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className="size-6 text-[#05808b]"
    >
      <path
        fillRule="evenodd"
        d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z"
        clipRule="evenodd"
      />
    </svg>
  );
};

export const EcoPowerGSIcon = () => {
  return (
    <svg
      width="50"
      height="50"
      viewBox="0 0 50 50"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M42.8698 21.1154C43.3977 21.1688 43.992 21.3407 44.5058 21.8362C45.0259 22.3378 45.2161 22.9338 45.2798 23.4685C45.3305 23.8951 45.3062 24.4683 45.2876 24.9055C45.1059 29.2445 44.0416 34.6217 41.1826 38.2505C39.7203 40.1064 37.7616 41.5349 35.2224 42.0664C33.1604 42.4981 30.8345 42.3136 28.235 41.4402C31.3087 37.609 34.4561 35.1736 36.5713 33.7634C37.5287 33.1251 37.7874 31.8317 37.1492 30.8743C36.5109 29.917 35.2174 29.6583 34.2601 30.2965C31.9145 31.8602 28.4777 34.5218 25.1174 38.6684C24.0811 35.938 23.8249 33.4923 24.266 31.3252C24.7794 28.8033 26.1991 26.8463 28.0462 25.376C31.6597 22.4996 37.0466 21.3625 41.4255 21.1273C41.8703 21.1032 42.4428 21.0721 42.8698 21.1154ZM21.1069 44.5414C22.3774 42.318 23.741 40.3704 25.1174 38.6719C25.2792 39.0981 25.46 39.5313 25.6601 39.9714C25.8201 40.3234 26.1049 40.6035 26.4594 40.7577C27.0649 41.021 27.6567 41.2494 28.235 41.4437C27.0314 42.9439 25.8392 44.6581 24.7246 46.6087C24.1537 47.6077 22.8811 47.9548 21.8821 47.3839C20.8831 46.8131 20.536 45.5404 21.1069 44.5414Z"
        fill="url(#paint0_linear_40005563_57571)"
      />
      <path
        d="M28.6845 4.57246C25.4123 1.60085 20.4211 1.60085 17.1488 4.57245C14.6359 6.85453 11.5518 10.0044 9.08558 13.622C6.6331 17.2193 4.6875 21.4307 4.6875 25.8026C4.6875 32.7985 9.41246 40.3863 17.5008 42.6973C17.9136 42.8153 18.1199 42.8742 18.3169 42.7967C18.5139 42.7193 18.6325 42.5227 18.8697 42.1296C19.6388 40.855 20.4456 39.6708 21.255 38.5675C21.4352 38.3219 21.5253 38.199 21.5521 38.0617C21.5788 37.9243 21.5403 37.7721 21.4631 37.4677C20.8723 35.1354 20.764 32.8681 21.2047 30.7034C21.8916 27.3288 23.7956 24.7677 26.1008 22.9327C29.3556 20.3418 33.6113 19.0161 37.4784 18.4058C38.1533 18.2993 38.4907 18.2461 38.6429 18.0411C38.6848 17.9847 38.7148 17.9262 38.7363 17.8593C38.8142 17.6162 38.6649 17.3188 38.3662 16.724C35.8024 11.8197 31.9055 7.49754 28.6845 4.57246Z"
        fill="url(#paint1_linear_40005563_57571)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40005563_57571"
          x1="20.3719"
          y1="51.5754"
          x2="50.3263"
          y2="49.1688"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_40005563_57571"
          x1="4.0471"
          y1="48.8012"
          x2="45.7829"
          y2="45.7391"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const BioEnergyGSIcon = () => {
  return (
    <svg
      width="50"
      height="50"
      viewBox="0 0 50 50"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.11866 23.4375H15.6593C16.4689 23.4375 17.1443 24.0559 17.2157 24.8623C17.5778 28.9539 20.9471 32.1191 24.9991 32.1191C29.051 32.1191 32.4203 28.9539 32.7824 24.8623C32.8538 24.0559 33.5293 23.4375 34.3389 23.4375L41.9489 23.4375C42.3333 23.4373 42.7656 23.4371 43.1331 23.488C43.5724 23.5488 44.1433 23.7077 44.6188 24.1998C45.0871 24.6844 45.2312 25.2496 45.2813 25.6893C45.323 26.0555 45.3108 26.5523 45.3015 26.9317C45.2791 27.8617 45.2253 28.819 45.0923 29.7901C45.0724 29.9371 45.0362 30.2044 44.982 30.4073C44.9156 30.6564 44.7564 31.1072 44.3103 31.4562C43.8174 31.8417 43.2781 31.8532 43.0646 31.8515C42.8514 31.8497 42.5665 31.8162 42.3869 31.795C40.8697 31.6176 39.3498 32.8909 39.3498 34.5498C39.3498 35.3913 39.711 36.1382 40.2783 36.6463C40.4113 36.7653 40.6227 36.9543 40.7641 37.1113C40.9077 37.271 41.2464 37.6724 41.2926 38.2812C41.335 38.8392 41.1153 39.2571 40.9778 39.4747C40.8662 39.6514 40.6958 39.8582 40.6022 39.972C39.6827 41.0917 38.6486 42.1112 37.5178 43.0118C37.4199 43.0901 37.2327 43.2397 37.0707 43.3399C36.8658 43.4666 36.4919 43.6579 35.9917 43.6435C35.447 43.6279 35.0618 43.37 34.897 43.2506C34.7423 43.1386 34.5547 42.9706 34.442 42.8696C33.9644 42.4425 33.3471 42.1893 32.672 42.1893C31.2613 42.1893 30.0398 43.4005 29.9756 44.8712C29.9691 45.0225 29.9584 45.2718 29.9313 45.4587C29.9024 45.6571 29.8212 46.1044 29.4652 46.5029C29.1374 46.87 28.7494 47.0115 28.517 47.075C28.3344 47.1249 28.0998 47.1596 27.9777 47.1776C26.0309 47.4686 23.9672 47.4686 22.0205 47.1776C21.8984 47.1596 21.6637 47.1249 21.4811 47.075C21.2487 47.0115 20.8608 46.87 20.5329 46.5029C20.1769 46.1044 20.0957 45.6571 20.0669 45.4588C20.0397 45.2718 20.029 45.0225 20.0226 44.8712C19.9584 43.4005 18.7368 42.1893 17.3262 42.1893C16.6511 42.1893 16.0337 42.4425 15.5561 42.8696C15.4434 42.9706 15.2559 43.1386 15.1012 43.2506C14.9363 43.37 14.5512 43.6279 14.0065 43.6435C13.5063 43.6579 13.1323 43.4666 12.9275 43.3399C12.7654 43.2397 12.5783 43.0901 12.4803 43.0118C11.3495 42.1112 10.3154 41.0917 9.39598 39.972C9.30231 39.8582 9.13194 39.6514 9.02032 39.4747C8.88284 39.2571 8.66318 38.8392 8.70551 38.2812C8.7517 37.6724 9.09039 37.271 9.23409 37.1113C9.37539 36.9543 9.58681 36.7653 9.71984 36.6463C10.2871 36.1382 10.6484 35.3913 10.6484 34.5498C10.6484 32.8909 9.12846 31.6176 7.61123 31.795C7.43168 31.8162 7.14671 31.8497 6.93352 31.8515C6.72007 31.8532 6.1807 31.8417 5.68785 31.4562C5.24172 31.1072 5.08258 30.6564 5.01611 30.4073C4.96197 30.2044 4.92577 29.9371 4.90586 29.7901C4.77286 28.819 4.71904 27.8617 4.69666 26.9317C4.68734 26.5523 4.67514 26.0555 4.71687 25.6893C4.76698 25.2496 4.91101 24.6844 5.37931 24.1998C5.85487 23.7077 6.42578 23.5488 6.86499 23.488C7.23254 23.4371 7.73428 23.4374 8.11866 23.4375Z"
        fill="url(#paint0_linear_40005563_57578)"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.5136 3.23502C16.2474 1.90275 20.5601 2.67588 23.5256 5.64138C25.0784 7.19411 26.03 9.11619 26.3931 11.1311C28.9218 8.85896 32.4362 8.30449 35.4929 9.39516C35.9345 9.5527 36.2819 9.90017 36.4395 10.3417C37.5812 13.5416 36.9201 17.2429 34.373 19.79C32.2514 21.9117 29.3289 22.7247 26.5625 22.2773V25.0013C26.5625 25.8642 25.8629 26.5638 25 26.5638C24.1371 26.5638 23.4375 25.8642 23.4375 25.0013L23.4375 18.0259C20.1054 18.6819 16.5363 17.7565 13.9734 15.1936C11.0079 12.2281 10.2348 7.9154 11.5671 4.18156C11.7246 3.74003 12.0721 3.39256 12.5136 3.23502Z"
        fill="url(#paint1_linear_40005563_57578)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40005563_57578"
          x1="3.92391"
          y1="50.9282"
          x2="52.8862"
          y2="43.6897"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_40005563_57578"
          x1="10.448"
          y1="30.0961"
          x2="42.236"
          y2="27.0835"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const AIIcon = ({ className }) => {
  return (
    <svg
      width="25"
      height="25"
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <rect
        width="29.3984"
        height="29.3984"
        rx="14.6992"
        fill="url(#paint0_linear_40005153_53784)"
      />
      <path
        d="M15.0268 12.8091L18.4734 14.6953L15.0268 16.5799L13.1418 20.0257L11.2552 16.5799L7.80859 14.6953L11.2552 12.8091L13.1418 9.36328L15.0268 12.8091Z"
        fill="white"
      />
      <path
        d="M19.7976 9.09995L21.5503 10.0586L19.7976 11.0172L18.8388 12.7679L17.8816 11.0172L16.1289 10.0586L17.8816 9.09995L18.8388 7.34766L19.7976 9.09995Z"
        fill="white"
      />
      <path
        d="M19.7976 18.3796L21.5503 19.3382L19.7976 20.2968L18.8388 22.0491L17.8816 20.2968L16.1289 19.3382L17.8816 18.3796L18.8388 16.6289L19.7976 18.3796Z"
        fill="white"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40005153_53784"
          x1="-0.552602"
          y1="33.7328"
          x2="35.3821"
          y2="30.5997"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const NewIssbReportingSuiteIcon = ({ className }) => {
  return (
    <svg
      width="57"
      height="43"
      viewBox="0 0 57 43"
      fill="none"
      xmlns="http://www.w3.org/2000/svg "
      className={className}
    >
      <path
        d="M19.4291 15.6303H12.4158C11.5225 15.6303 10.7982 14.9061 10.7982 14.0127V11.6801C10.7982 10.7867 10.074 10.0625 9.18067 10.0625H3.28125"
        stroke="url(#paint0_linear_40004975_51131)"
        strokeWidth="1.29406"
      />
      <path
        d="M19.4291 27.2955H12.4158C11.5225 27.2955 10.7982 28.0197 10.7982 28.9131V31.2457C10.7982 32.1391 10.074 32.8633 9.18067 32.8633H3.28125"
        stroke="url(#paint1_linear_40004975_51131)"
        strokeWidth="1.29406"
      />
      <ellipse
        cx="3.33256"
        cy="10.1607"
        rx="2.53569"
        ry="2.53567"
        fill="url(#paint2_linear_40004975_51131)"
      />
      <ellipse
        cx="2.53569"
        cy="2.53567"
        rx="2.53569"
        ry="2.53567"
        transform="matrix(1 0 0 -1 0.796875 35.293)"
        fill="url(#paint3_linear_40004975_51131)"
      />
      <path
        d="M19.4564 21.5273H2.12891"
        stroke="url(#paint4_linear_40004975_51131)"
        strokeWidth="1.29406"
      />
      <ellipse
        cx="3.33256"
        cy="21.6411"
        rx="2.53569"
        ry="2.53567"
        fill="url(#paint5_linear_40004975_51131)"
      />
      <path
        d="M50.7358 18.5164H45.6699C41.5155 18.5164 38.1324 15.1333 38.1324 10.9789V5.91306C38.1324 4.94896 37.3436 4.16016 36.3795 4.16016H28.9472C23.5483 4.16016 19.1836 7.66595 19.1836 13.9238V29.4545C19.1836 35.7123 23.5483 39.2181 28.9472 39.2181H42.725C48.124 39.2181 52.4887 35.7123 52.4887 29.4545V20.2693C52.4887 19.3052 51.6999 18.5164 50.7358 18.5164Z"
        fill="url(#paint6_linear_40004975_51131)"
      />
      <path
        d="M42.4945 4.52906C41.7758 3.81037 40.5312 4.30119 40.5312 5.30034V11.418C40.5312 13.9772 42.7048 16.0982 45.3517 16.0982C47.017 16.1157 49.3308 16.1157 51.3116 16.1157C52.3107 16.1157 52.8366 14.9413 52.1354 14.2401C49.6113 11.6984 45.0888 7.12335 42.4945 4.52906Z"
        fill="url(#paint7_linear_40004975_51131)"
      />
      <path
        d="M26.4936 32.6172H23.8572L27.8743 20.9808H31.0447L35.0561 32.6172H32.4197L29.505 23.6399H29.4141L26.4936 32.6172ZM26.3288 28.0433H32.5561V29.9638H26.3288V28.0433ZM38.9169 20.9808V32.6172H36.4567V20.9808H38.9169Z"
        fill="white"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40004975_51131"
          x1="2.97772"
          y1="16.4512"
          x2="21.6705"
          y2="11.7243"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_40004975_51131"
          x1="2.97772"
          y1="26.4746"
          x2="21.6705"
          y2="31.2015"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_40004975_51131"
          x1="0.701548"
          y1="13.444"
          x2="6.90045"
          y2="12.9035"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_40004975_51131"
          x1="-0.0953266"
          y1="5.81904"
          x2="6.10358"
          y2="5.27855"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_40004975_51131"
          x1="1.8032"
          y1="22.6748"
          x2="8.30464"
          y2="12.8525"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint5_linear_40004975_51131"
          x1="0.701548"
          y1="24.9245"
          x2="6.90045"
          y2="24.384"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint6_linear_40004975_51131"
          x1="18.5576"
          y1="44.3869"
          x2="59.2974"
          y2="41.0124"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint7_linear_40004975_51131"
          x1="40.3071"
          y1="17.8744"
          x2="54.8836"
          y2="16.6038"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const DataValidationIcon = ({ className }) => {
  return (
    <svg
      width="44"
      height="45"
      viewBox="0 0 44 45"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M31.3486 4.05078H23.6486C17.6801 4.05078 15.0582 6.28732 14.72 11.7463C14.6855 12.3022 15.1386 12.7591 15.6955 12.7591H20.3486C28.0486 12.7591 31.6236 16.3341 31.6236 24.0341V28.6872C31.6236 29.2441 32.0805 29.6972 32.6364 29.6627C38.0954 29.3245 40.3319 26.7027 40.3319 20.7341V13.0341C40.3319 6.61745 37.7653 4.05078 31.3486 4.05078Z"
        fill="url(#paint0_linear_40005975_36480)"
      />
      <path
        d="M20.3513 15.0508H12.6513C6.23464 15.0508 3.66797 17.6174 3.66797 24.0341V31.7341C3.66797 38.1508 6.23464 40.7174 12.6513 40.7174H20.3513C26.768 40.7174 29.3346 38.1508 29.3346 31.7341V24.0341C29.3346 17.6174 26.768 15.0508 20.3513 15.0508ZM22.533 25.4091L15.7313 32.2108C15.4746 32.4674 15.1446 32.5958 14.7963 32.5958C14.448 32.5958 14.118 32.4674 13.8613 32.2108L10.4513 28.8008C9.93797 28.2874 9.93797 27.4624 10.4513 26.9491C10.9646 26.4358 11.7896 26.4358 12.303 26.9491L14.778 29.4241L20.663 23.5391C21.1763 23.0258 22.0013 23.0258 22.5146 23.5391C23.028 24.0524 23.0463 24.8958 22.533 25.4091Z"
        fill="url(#paint1_linear_40005975_36480)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40005975_36480"
          x1="14.2015"
          y1="33.4806"
          x2="45.5523"
          y2="30.7471"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_40005975_36480"
          x1="3.18551"
          y1="44.5016"
          x2="34.5587"
          y2="41.7662"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const TNFDIcon = ({ className }) => {
  return (
    <svg
      width="58"
      height="43"
      viewBox="0 0 58 43"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M19.6205 15.6498H12.6072C11.7139 15.6498 10.9896 14.9256 10.9896 14.0323V11.6996C10.9896 10.8062 10.2654 10.082 9.37208 10.082H3.47266"
        stroke="url(#paint0_linear_40005900_85222)"
        strokeWidth="1.29406"
      />
      <path
        d="M19.6205 27.315H12.6072C11.7139 27.315 10.9896 28.0392 10.9896 28.9326V31.2652C10.9896 32.1586 10.2654 32.8828 9.37208 32.8828H3.47266"
        stroke="url(#paint1_linear_40005900_85222)"
        strokeWidth="1.29406"
      />
      <ellipse
        cx="3.52397"
        cy="10.1802"
        rx="2.53569"
        ry="2.53567"
        fill="url(#paint2_linear_40005900_85222)"
      />
      <ellipse
        cx="2.53569"
        cy="2.53567"
        rx="2.53569"
        ry="2.53567"
        transform="matrix(1 0 0 -1 0.988281 35.3125)"
        fill="url(#paint3_linear_40005900_85222)"
      />
      <path
        d="M19.6478 21.5469H2.32031"
        stroke="url(#paint4_linear_40005900_85222)"
        strokeWidth="1.29406"
      />
      <ellipse
        cx="3.52397"
        cy="21.6607"
        rx="2.53569"
        ry="2.53567"
        fill="url(#paint5_linear_40005900_85222)"
      />
      <path
        d="M50.8803 18.1336H45.8144C41.6601 18.1336 38.277 14.7505 38.277 10.5961V5.53024C38.277 4.56615 37.4882 3.77734 36.5241 3.77734H29.0918C23.6928 3.77734 19.3281 7.28314 19.3281 13.541V29.0717C19.3281 35.3295 23.6928 38.8353 29.0918 38.8353H42.8696C48.2685 38.8353 52.6332 35.3295 52.6332 29.0717V19.8865C52.6332 18.9224 51.8444 18.1336 50.8803 18.1336Z"
        fill="url(#paint6_linear_40005900_85222)"
      />
      <path
        d="M42.639 4.14625C41.9203 3.42756 40.6758 3.91837 40.6758 4.91753V11.0351C40.6758 13.5944 42.8494 15.7154 45.4963 15.7154C47.1615 15.7329 49.4753 15.7329 51.4561 15.7329C52.4553 15.7329 52.9811 14.5585 52.28 13.8573C49.7558 11.3156 45.2333 6.74054 42.639 4.14625Z"
        fill="url(#paint7_linear_40005900_85222)"
      />
      <path
        d="M24.0373 18.293H21.8952L25.1591 8.83842H27.7351L30.9943 18.293H28.8523L26.484 10.9989H26.4102L24.0373 18.293ZM23.9034 14.5767H28.9631V16.1371H23.9034V14.5767ZM34.1312 8.83842V18.293H32.1323V8.83842H34.1312Z"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M47.9434 27.0424C48.119 27.0602 48.3168 27.1174 48.4877 27.2823C48.6607 27.4494 48.724 27.6478 48.7452 27.8258C48.7621 27.9679 48.754 28.1587 48.7478 28.3042C48.6874 29.7489 48.3333 31.5392 47.382 32.7474C46.8955 33.3653 46.2438 33.8409 45.3989 34.0179C44.7129 34.1616 43.939 34.1002 43.0741 33.8094C44.0968 32.5338 45.144 31.7229 45.8478 31.2534C46.1663 31.0409 46.2524 30.6103 46.04 30.2915C45.8277 29.9728 45.3973 29.8867 45.0788 30.0991C44.2983 30.6198 43.1549 31.5059 42.0368 32.8865C41.692 31.9774 41.6068 31.1632 41.7535 30.4416C41.9244 29.602 42.3967 28.9504 43.0113 28.4609C44.2136 27.5032 46.0059 27.1246 47.4628 27.0463C47.6108 27.0383 47.8013 27.028 47.9434 27.0424ZM40.7008 34.8386C41.1236 34.0983 41.5772 33.4498 42.0352 32.8843C42.089 33.0263 42.1492 33.1705 42.2158 33.317C42.269 33.4342 42.3637 33.5275 42.4817 33.5788C42.6832 33.6665 42.8801 33.7425 43.0725 33.8072C42.672 34.3067 42.2753 34.8774 41.9045 35.5269C41.7146 35.8595 41.2911 35.975 40.9588 35.785C40.6264 35.5949 40.5109 35.1712 40.7008 34.8386Z"
        fill="white"
      />
      <path
        d="M43.2186 21.535C42.1299 20.5456 40.4692 20.5456 39.3805 21.535C38.5444 22.2948 37.5183 23.3435 36.6977 24.548C35.8817 25.7457 35.2344 27.1478 35.2344 28.6034C35.2344 30.9327 36.8065 33.459 39.4976 34.2284C39.6349 34.2677 39.7036 34.2873 39.7691 34.2615C39.8347 34.2357 39.8741 34.1702 39.9531 34.0394C40.2089 33.615 40.4774 33.2207 40.7467 32.8534C40.8066 32.7716 40.8366 32.7307 40.8455 32.685C40.8544 32.6393 40.8416 32.5886 40.8159 32.4872C40.6194 31.7107 40.5833 30.9558 40.7299 30.2351C40.9585 29.1116 41.592 28.2589 42.359 27.6479C43.4419 26.7853 44.8579 26.3439 46.1445 26.1407C46.3691 26.1053 46.4813 26.0875 46.532 26.0193C46.5459 26.0005 46.5559 25.981 46.563 25.9588C46.589 25.8778 46.5393 25.7788 46.4399 25.5808C45.5869 23.9479 44.2903 22.5089 43.2186 21.535Z"
        fill="white"
      />
      <defs>
        <linearGradient
          id="paint0_linear_40005900_85222"
          x1="3.16913"
          y1="16.4707"
          x2="21.8619"
          y2="11.7438"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_40005900_85222"
          x1="3.16913"
          y1="26.4941"
          x2="21.8619"
          y2="31.221"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_40005900_85222"
          x1="0.892955"
          y1="13.4636"
          x2="7.09186"
          y2="12.9231"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_40005900_85222"
          x1="-0.0953266"
          y1="5.81904"
          x2="6.10358"
          y2="5.27855"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_40005900_85222"
          x1="1.99461"
          y1="22.6943"
          x2="8.49605"
          y2="12.872"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint5_linear_40005900_85222"
          x1="0.892955"
          y1="24.944"
          x2="7.09186"
          y2="24.4035"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint6_linear_40005900_85222"
          x1="18.7021"
          y1="44.0041"
          x2="59.4419"
          y2="40.6296"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
        <linearGradient
          id="paint7_linear_40005900_85222"
          x1="40.4516"
          y1="17.4916"
          x2="55.0281"
          y2="16.221"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2C5A8C" />
          <stop offset="0.46" stopColor="#1C889C" />
          <stop offset="1" stopColor="#13B1A8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const FinanceGuardIcon = () => (
  <svg
    width="29"
    height="29"
    viewBox="0 0 32 36"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.5352 2.58594H10.7852V0.0859375H20.7852V2.58594H17.0352V6.33594H14.5352V2.58594Z"
      fill="url(#paint0_linear_40008317_96381)"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.788 4.25C6.81002 4.25 0.166016 12.295 0.166016 20.9167C0.166016 29.7266 6.99406 35.9167 15.788 35.9167C24.6322 35.9167 31.8327 28.8447 31.8327 20.0833C31.8327 11.3219 24.6322 4.25 15.788 4.25ZM8.29102 17.6429L21.6243 13.8333L17.8161 27.1667L14.9585 20.5L8.29102 17.6429Z"
      fill="url(#paint1_linear_40008317_96381)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_40008317_96381"
        x1="10.5972"
        y1="7.25741"
        x2="22.6783"
        y2="5.57204"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#2C5A8C" />
        <stop offset="0.46" stopColor="#1C889C" />
        <stop offset="1" stopColor="#13B1A8" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_40008317_96381"
        x1="-0.429222"
        y1="40.5855"
        x2="38.278"
        y2="37.2106"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#2C5A8C" />
        <stop offset="0.46" stopColor="#1C889C" />
        <stop offset="1" stopColor="#13B1A8" />
      </linearGradient>
    </defs>
  </svg>
);

export const ChainSightIcon = () => (
  <svg
    width="41"
    height="41"
    viewBox="0 0 41 41"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M27.318 3.83594H13.3513C7.28464 3.83594 3.66797 7.4526 3.66797 13.5193V27.4693C3.66797 33.5526 7.28464 37.1693 13.3513 37.1693H27.3013C33.368 37.1693 36.9846 33.5526 36.9846 27.4859V13.5193C37.0013 7.4526 33.3846 3.83594 27.318 3.83594ZM13.968 28.4526C13.9346 28.4526 13.8846 28.4526 13.8513 28.4526C12.2346 28.3026 10.718 27.5526 9.58464 26.3526C6.91797 23.5526 6.91797 19.0026 9.58464 16.2026L13.2346 12.3693C14.5346 11.0026 16.2846 10.2359 18.1513 10.2359C20.018 10.2359 21.768 10.9859 23.068 12.3693C25.7346 15.1693 25.7346 19.7193 23.068 22.5193L21.2513 24.4359C20.768 24.9359 19.9846 24.9526 19.4846 24.4859C18.9846 24.0026 18.968 23.2193 19.4346 22.7193L21.2513 20.8026C23.018 18.9526 23.018 15.9359 21.2513 14.1026C19.6013 12.3693 16.7013 12.3693 15.0346 14.1026L11.3846 17.9359C9.61797 19.7859 9.61797 22.8026 11.3846 24.6359C12.1013 25.4026 13.068 25.8693 14.0846 25.9693C14.768 26.0359 15.268 26.6526 15.2013 27.3359C15.1513 27.9693 14.6013 28.4526 13.968 28.4526ZM31.0846 24.8193L27.4346 28.6526C26.1346 30.0193 24.3846 30.7859 22.518 30.7859C20.6513 30.7859 18.9013 30.0359 17.6013 28.6526C14.9346 25.8526 14.9346 21.3026 17.6013 18.5026L19.418 16.5859C19.9013 16.0859 20.6846 16.0693 21.1846 16.5359C21.6846 17.0193 21.7013 17.8026 21.2346 18.3026L19.418 20.2193C17.6513 22.0693 17.6513 25.0859 19.418 26.9193C21.068 28.6526 23.968 28.6693 25.6346 26.9193L29.2846 23.0859C31.0513 21.2359 31.0513 18.2193 29.2846 16.3859C28.568 15.6193 27.6013 15.1526 26.5846 15.0526C25.9013 14.9859 25.4013 14.3693 25.468 13.6859C25.5346 13.0026 26.1346 12.4859 26.8346 12.5693C28.4513 12.7359 29.968 13.4693 31.1013 14.6693C33.7513 17.4526 33.7513 22.0193 31.0846 24.8193Z"
      fill="url(#paint0_linear_40008151_103223)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_40008151_103223"
        x1="3.04171"
        y1="42.0838"
        x2="43.7661"
        y2="38.5348"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#2C5A8C" />
        <stop offset="0.46" stopColor="#1C889C" />
        <stop offset="1" stopColor="#13B1A8" />
      </linearGradient>
    </defs>
  </svg>
);

export const GenerateDocument = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16 2H8C4.5 2 3 4 3 7V17C3 20 4.5 22 8 22H16C19.5 22 21 20 21 17V7C21 4 19.5 2 16 2ZM8 12.25H12C12.41 12.25 12.75 12.59 12.75 13C12.75 13.41 12.41 13.75 12 13.75H8C7.59 13.75 7.25 13.41 7.25 13C7.25 12.59 7.59 12.25 8 12.25ZM16 17.75H8C7.59 17.75 7.25 17.41 7.25 17C7.25 16.59 7.59 16.25 8 16.25H16C16.41 16.25 16.75 16.59 16.75 17C16.75 17.41 16.41 17.75 16 17.75ZM18.5 9.25H16.5C14.98 9.25 13.75 8.02 13.75 6.5V4.5C13.75 4.09 14.09 3.75 14.5 3.75C14.91 3.75 15.25 4.09 15.25 4.5V6.5C15.25 7.19 15.81 7.75 16.5 7.75H18.5C18.91 7.75 19.25 8.09 19.25 8.5C19.25 8.91 18.91 9.25 18.5 9.25Z"
      fill="white"
    />
  </svg>
);

export const Filter = () => (
  <svg
    width="19"
    height="19"
    viewBox="0 0 19 19"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4.38431 1.74219H14.7583C15.6228 1.74219 16.3301 2.4495 16.3301 3.314V5.043C16.3301 5.67173 15.9371 6.45763 15.5442 6.85059L12.1648 9.83704C11.6932 10.23 11.3789 11.0159 11.3789 11.6446V15.024C11.3789 15.4956 11.0645 16.1243 10.6716 16.3601L9.5713 17.0674C8.54962 17.6961 7.13499 16.9888 7.13499 15.7313V11.566C7.13499 11.0159 6.82062 10.3086 6.50626 9.91563L3.51982 6.772C3.12686 6.37904 2.8125 5.67173 2.8125 5.20018V3.39259C2.8125 2.4495 3.51982 1.74219 4.38431 1.74219Z"
      stroke="#494949"
      strokeWidth="1.41463"
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8.73389 1.74219L4.85938 7.95086"
      stroke="#494949"
      strokeWidth="1.41463"
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ReportID = () => (
  <svg
    width="16"
    height="17"
    viewBox="0 0 16 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.6663 6.69027V10.0236C14.6663 13.3569 13.333 14.6903 9.99967 14.6903H5.99967C2.66634 14.6903 1.33301 13.3569 1.33301 10.0236V6.0236C1.33301 2.69027 2.66634 1.35693 5.99967 1.35693H9.33301"
      stroke="#525252"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.6663 6.69027H11.9997C9.99967 6.69027 9.33301 6.0236 9.33301 4.0236V1.35693L14.6663 6.69027Z"
      stroke="#525252"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.00252 12.6516L6.8703 7.3623H7.80007L6.93229 12.6516H6.00252ZM3.64453 11.3913L3.80207 10.4615H7.99635L7.83881 11.3913H3.64453ZM4.14299 12.6516L5.01077 7.3623H5.94054L5.07275 12.6516H4.14299ZM3.94671 9.55242L4.10425 8.62265H8.29853L8.14098 9.55242H3.94671Z"
      fill="#525252"
    />
  </svg>
);

export const NoAssessmentIcon = () => (
  <svg
    width="75"
    height="75"
    viewBox="0 0 75 75"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M63.8074 34.9726V21.7881C63.8074 9.3154 60.8981 6.18945 49.1991 6.18945H25.8011C14.1021 6.18945 11.1929 9.3154 11.1929 21.7881V56.6374C11.1929 64.8701 15.7116 66.8199 21.1897 60.9395L21.2205 60.9085C23.7584 58.2159 27.6271 58.4325 29.8246 61.3727L32.9505 65.551"
      stroke="#525252"
      strokeOpacity="0.5"
      strokeWidth="4.64246"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M56.6889 66.2316C62.1586 66.2316 66.5927 61.7975 66.5927 56.3278C66.5927 50.858 62.1586 46.4238 56.6889 46.4238C51.2191 46.4238 46.7849 50.858 46.7849 56.3278C46.7849 61.7975 51.2191 66.2316 56.6889 66.2316Z"
      stroke="#525252"
      strokeOpacity="0.5"
      strokeWidth="4.64246"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M68.4497 68.0891L65.3547 64.9941"
      stroke="#525252"
      strokeOpacity="0.5"
      strokeWidth="4.64246"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M25.1201 21.6641H49.8799"
      stroke="#525252"
      strokeOpacity="0.5"
      strokeWidth="4.64246"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M28.2151 34.0449H46.7849"
      stroke="#525252"
      strokeOpacity="0.5"
      strokeWidth="4.64246"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const DeleteIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18.3965 9.64453L17.8091 17.6861C17.6876 19.3495 16.3026 20.6384 14.6335 20.6384H8.8802C7.21211 20.6384 5.82608 19.3495 5.70467 17.6851L5.11719 9.64453"
      stroke="#2D2D3A"
      strokeWidth="1.44"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M19.6965 6.625H3.82812"
      stroke="#2D2D3A"
      strokeWidth="1.44"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.378 6.62457L14.8952 4.24759C14.7551 3.72363 14.2797 3.35938 13.738 3.35938H9.78163C9.23712 3.35751 8.75985 3.72176 8.61882 4.24759L8.14062 6.62457"
      stroke="#2D2D3A"
      strokeWidth="1.44"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.9375 11.7891V16.1171M13.1827 11.7891V16.1171"
      stroke="#2D2D3A"
      strokeWidth="1.44"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const FileSavedIcon = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.8545 27.9845C25.3289 27.9845 27.9651 25.3472 27.9651 20.8741L28 14.6665C28 10.1028 26.3445 7.82025 21.8597 7.82025H18.322C17.4243 7.81766 16.5797 7.39536 16.0395 6.679L14.8983 5.16078C14.3593 4.44313 13.5148 4.01953 12.617 4.01953H10.1169C5.64257 4.01953 4 6.65569 4 11.1235V20.8741C4 25.3472 6.64133 27.9845 11.126 27.9845H20.8545Z"
      stroke="#52D176"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M11.8906 17.6795L14.5889 20.3777L20.11 14.8555"
      stroke="#52D176"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export const UploadFileIcon = () => (
  <svg
    width="38"
    height="38"
    viewBox="0 0 38 38"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.5859 3.16797H21.0178C26.1813 3.16797 28.7631 3.16797 30.5561 4.43121C31.0698 4.79315 31.5259 5.22238 31.9104 5.70588C33.2526 7.39337 33.2526 9.82328 33.2526 14.6831V18.7134C33.2526 23.4051 33.2526 25.751 32.5101 27.6245C31.3165 30.6366 28.7921 33.0124 25.5919 34.1358C23.6012 34.8346 21.1087 34.8346 16.1238 34.8346C13.2753 34.8346 11.851 34.8346 10.7135 34.4353C8.88477 33.7934 7.44229 32.4357 6.76021 30.7146C6.33594 29.644 6.33594 28.3035 6.33594 25.6225V19.0013"
      stroke="#07838F"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M33.2474 19C33.2474 21.9148 30.8845 24.2778 27.9696 24.2778C26.9155 24.2778 25.6727 24.0931 24.6477 24.3677C23.7371 24.6117 23.0258 25.323 22.7818 26.2337C22.5071 27.2586 22.6918 28.5014 22.6918 29.5556C22.6918 32.4704 20.3289 34.8333 17.4141 34.8333"
      stroke="#07838F"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17.4167 9.5013L4.75 9.5013M11.0833 3.16797V15.8346"
      stroke="#07838F"
      strokeWidth="2"
      strokeLinecap="round"
    />
  </svg>
);
