import { create } from "zustand";

import axios from "axios";
import Cookies from "js-cookie";

const api = axios.create({
  baseURL: "https://notificationsys-staging.azurewebsites.net/api/v1", // e.g., /api/notifications
});

// Add auth interceptor
api.interceptors.request.use((config) => {
  const token = Cookies.get("level_user_token");
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export const useNotificationStore = create((set) => ({
  notifications: [],
  isOnline: true,
  isLoading: false,
  error: null,
  addNotification: (notification) =>
    set((state) => ({
      notifications: [notification, ...state.notifications],
    })),
  markMessageAsSeen: (notificationId) =>
    set((state) => ({
      notifications: state.notifications.map((notification) =>
        notification.notificationId === notificationId
          ? { ...notification, seen: true }
          : notification
      ),
    })),
  setOffline: () => set({ isOnline: false }),
  setOnline: () => set({ isOnline: true }),
  clearNotifications: () => set({ notifications: [] }),
  fetchNotifications: async () => {
    set({ isLoading: true, error: null });
    try {
      const response = await api.get("/notifications");
      const sortedNotifications = response.data.sort(
        (a, b) => new Date(b.createdDate) - new Date(a.createdDate)
      );
      set({
        notifications: sortedNotifications,
        isLoading: false,
      });
    } catch (err) {
      set({
        error: err.message,
        isLoading: false,
      });
      console.error("Notification fetch error: ", err);
    }
  },
  ReadAllNotifications: () =>
    set((state) => ({
      notifications: state.notifications.map((notification) => ({
        ...notification,
        seen: true,
      })),
    })),
}));
