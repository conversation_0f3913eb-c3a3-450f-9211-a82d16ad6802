import React from "react";
import { Table } from "@mantine/core";

const ScoringMetrics = () => {
  const head = ["Metric", "Calculation"];
  const head2 = ["Metric", "Minor (2)", "Score"];

  const d = [
    "Base Score",
    "Red Flag Penalties",
    "Evidence Bonus",
    "Compliance Score",
    "Action Progress Score",
  ];

  const s = [
    "100 points (starting point for all assessments)",
    "-5 points for each NO answer (red flag)",
    "+2 points for each YES answer with provided evidence",
    "(Number of YES answers for regulatory items / Total regulatory items) x 100",
    "(Number of completed action items / Total action items) x 100",
  ];

  const rows = d.map((item, i) => {
    return (
      <Table.Tr key={i} className={`risk-matrix-table bg-white text-sm`}>
        <Table.Td className="bg-secondary-gray-100 text-xs p-2">
          {item}
        </Table.Td>
        <Table.Td className="bg-secondary-gray-100 p-2">
          <ul className="ml-5 li-circle">
            <li className="text-xs font-bold">{s[i]}</li>
          </ul>
        </Table.Td>
      </Table.Tr>
    );
  });

  const fsc = [
    "Start with Base Score (100 points)",
    "Apply Red Flag Penalties: -5 points for each NO answer",
    "Add Evidence Bonuses: +2 points for each YES answer with evidence",
    "Calculate Compliance Score (separate percentage)",
    "Calculate Action Progress Score (separate percentage)",
  ];
  const ors = [
    "90-100: Excellent (Low greenwashing risk)",
    "80-89: Good (Some areas for improvement)",
    "70-79: Fair (Significant areas needing attention)",
    "Below 70: Poor (High greenwashing risk, immediate action required)",
  ];

  const t2 = [
    { metric: "Base Score", score: "100", minor: "Starting point" },
    {
      metric: "Red Flag Penalties",
      score: "-XX",
      minor: "XX NO answers (-5 each)",
    },
    {
      metric: "Evidence Bonus",
      score: "+XX",
      minor: "XX YES answers with evidence (+2 each)",
    },
    {
      metric: "Final Score",
      score: "XX",
      minor: "100 - Red Flag Penalties + Evidence Bonus",
    },
    {
      metric: "Compliance Score",
      score: "XX%",
      minor: "XX out of XX regulatory items compliant",
    },
    {
      metric: "Action Progress Score",
      score: "XX%",
      minor: "XX out of XX action items completed",
    },
  ];
  

  const rows2 = t2.map((item, i) => {
    return (
      <Table.Tr key={i} className={`risk-matrix-table bg-white text-sm`}>
        <Table.Td className="bg-secondary-gray-100 text-xs p-2">
          {item.metric}
        </Table.Td>
        <Table.Td className="p-2">{item.score}</Table.Td>
        <Table.Td className="p-2">{item.minor}</Table.Td>
      </Table.Tr>
    );
  });

  return (
    <div className="flex flex-col gap-3 basis-[48%]">
      <h1 className="text-2xl font-bold">Greenwashing Assessment Scoring System Guide</h1>
      <h2 className="text-[1rem] font-semibold ">Scoring Metrics</h2>
      <Table withTableBorder withColumnBorders miw={"50%"} verticalSpacing="lg">
        <Table.Thead className="border-b-2 bg-white pb-6 text-base ">
          <Table.Tr>
            {head.map((el, i) => (
              <Table.Th
                key={i}
                className="bg-secondary-gray-100 font-medium text-xs p-2"
              >
                {el}
              </Table.Th>
            ))}
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>{rows} </Table.Tbody>
      </Table>

      <h2 className="text-base font-semibold">Final Score Calculation</h2>
      {fsc.map((f, i) => (
        <h4 key={i} className="text-secondary-gray-200 text-sm">
          {" "}
          {i + 1}. {f}
        </h4>
      ))}
      <h2 className="text-base font-semibold">Overall Rating Scale</h2>
      {ors.map((o, i) => (
        <ul key={i} className="ml-5 li-circle">
          <li className="text-secondary-gray-200 text-sm">{o}</li>
        </ul>
      ))}

      <Table withTableBorder withColumnBorders miw={"50%"} verticalSpacing="lg">
        <Table.Thead className="border-b-2 bg-white pb-6 text-base text-center">
          <Table.Tr>
            {head2.map((el, i) => (
              <Table.Th
                key={i}
                className="bg-secondary-gray-100 font-medium text-xs p-2"
              >
                {el}
              </Table.Th>
            ))}
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>{rows2} </Table.Tbody>
      </Table>
    </div>
  );
};

export default ScoringMetrics;
