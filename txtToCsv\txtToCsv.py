import csv


def read_input_file(input_file):
    with open(input_file, 'r') as file:
        lines = [line.strip() for line in file if line.strip()]
    rows = [lines[i:i + 8] for i in range(0, len(lines), 8)]
    return rows


def write_to_csv(rows, output_file):
    with open(output_file, 'w', newline='') as csv_file:
        csv_writer = csv.writer(csv_file)
        csv_writer.writerow(
            ['questionText', 'categoryText', 'option0', 'option1', 'option2', 'option3', 'option4', 'option5'])
        csv_writer.writerows(rows)


def convert_to_csv(input_file, output_file):
    rows = read_input_file(input_file)
    write_to_csv(rows, output_file)
    print(f"Conversion successful. CSV file '{output_file}' created.")


convert_to_csv('input.txt', 'output.csv')
