import {
  EsgDueDiligenceIcon,
  HumanRightsDueDiligenceIcon,
} from "@/assets/icons/GRCIcons";
import IssbCard from "@/Issb/IssbCard";
import S2Layout from "@/Layout/S2Layout";
import { IoMdHome } from "react-icons/io";

const DueDiligence = () => {
  const DueDiligenceMenu = [
    "Customisable scoring methodology",
    "Portfolio company assessment",
    "Automated tracking of corrective actions",
    "Exportable outputs for stakeholder submissions",
  ];

  const HumanRightsMenu = [
    "Guided impact assessment",
    "Value chain risk mapping and prioritisation",
    "Automated tracking of corrective actions",
    "Document management capabilities",
  ];

  return (
    <S2Layout
      navbarTitle="Due Diligence"
      breadcrumbItems={[
        { title: <IoMdHome size={20}/>, href: "/get-started" },
        { title: "Due Diligence", href: "#" },
      ]}
    >
      <div className="flex flex-col gap-5 w-full h-full px-3">
        <IssbCard
          icon={<EsgDueDiligenceIcon />}
          title="ESG Due Diligence"
          description="Comprehensive platform to assess and manage third-party ESG risks."
          items={DueDiligenceMenu}
          btnString="Start Assessment →"
          link="esg-due-diligence"
        />

        <IssbCard
          icon={<HumanRightsDueDiligenceIcon />}
          title="Human Rights Due Diligence"
          description="Comprehensive platform to identify and mitigate human rights risks."
          items={HumanRightsMenu}
          btnString="Start Assessment →"
          link="human-rights-due-diligence"
        />
      </div>
    </S2Layout>
  );
};

export default DueDiligence;
