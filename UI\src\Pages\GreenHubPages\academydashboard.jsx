import{ useEffect, useState } from 'react';
import { Modal, Button, TextInput, Textarea, FileInput, Progress, Select } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { useForm, Controller } from 'react-hook-form';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import 'react-lazy-load-image-component/src/effects/blur.css';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import ApiS3 from '@/Api/apiS3';
import useSideBarRoute from '@/hooks/useSideBarRoute';
import MainLayout from '@/Layout/MainLayout';
import useRoutes from '@/Routes/useRoutes';
import { IoMdHome } from 'react-icons/io';

// Custom Hook for Fetching Courses
const useCourses = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const getCourses = async () => {
    setLoading(true);
    try {
      const response = await ApiS3.get('/academy/dashboard');
      if (response) {
        setData(response.data);
      }
    } catch (err) {
      setError('Failed to load courses. Please try again later.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getCourses();
  }, []);

  return { data, loading, error, refetchCourses: getCourses };
};

// Custom Hook for Deleting a Course
const useDeleteCourse = (refetchCourses) => {
  const [loading, setLoading] = useState(false);

  const deleteCourse = async (id) => {
    const isConfirmed = window.confirm('Are you sure you want to delete this course?');
    if (!isConfirmed) return;

    setLoading(true);
    try {
      await ApiS3.delete('/academy/' + id);
      await refetchCourses();
    } catch (err) {
      console.error('Failed to delete course. Please try again.', err);
    } finally {
      setLoading(false);
    }
  };

  return { deleteCourse, loading };
};

export default function AcademyDashboard() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { GreenHubMenu } = useSideBarRoute();
  const { GreenHubResourcesDashboard, GreenHubAcademyDashboard, GreenHubCommunityDashboard } = useRoutes().GreenHubMap;
  const location = useLocation();
  const [opened, { open, close }] = useDisclosure(false);

  const { data, loading, error, refetchCourses } = useCourses();
  const { deleteCourse } = useDeleteCourse(refetchCourses);

  return (
    <MainLayout menus={GreenHubMenu} navbarTitle={t('GreenHub')}
    breadcrumbItems={[
      { title: <IoMdHome size={20} />, href: "/get-started" },
      { title: "GreenHub Academy", href: "#" },
    ]}>
      <div className="overflow-y-auto">
        <h1 className="px-8 py-4 text-xl font-bold bg-white rounded-2xl text-blue-950 mb-7">{t('Manage Green Hub Resources')}</h1>

        <NavigationLinks
          location={location}
          links={[
            {
              to: GreenHubAcademyDashboard.path,
              label: t('GreenHub Academy'),
              key: 'academy',
            },
            {
              to: GreenHubResourcesDashboard.path,
              label: t('GreenHub Resources'),
              key: 'resourses',
            },
            {
              to: GreenHubCommunityDashboard.path,
              label: t('GreenHub Peers Community'),
              key: 'community',
            },
          ]}
        />

        <div className="flex w-full flex-end px-4">
          <Button onClick={open} className="bg-[#05808b] hover:bg-[#05808b]">
            Add Course
          </Button>
        </div>

        <Modal opened={opened} onClose={close} title="Add New Course" size="lg">
          <CourseForm onClose={close} refetchCourses={refetchCourses} />
        </Modal>

        {loading && <p className="text-center">Loading courses...</p>}
        {error && <p className="text-red-500 text-center">{error}</p>}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-8">
          {data?.map((course) => (
            <CourseCard
              key={course._id}
              course={course}
              onDelete={() => deleteCourse(course._id)}
              onClick={() => navigate(`/admin-pages/green-hub/dashboard/dash/academy/${course._id}`)}
            />
          ))}
        </div>
      </div>
    </MainLayout>
  );
}

// Navigation Links Component
const NavigationLinks = ({ location, links }) => (
  <div className="px-8 py-4 bg-white rounded-2xl text-blue-950 mb-7 flex justify-around">
    {links.map(({ to, label, key }) => {
      const isActive = location.pathname.split('/')[2] === key;
      const baseClasses = 'lg:p-3 w-[200px] lg:w-[400px] text-center rounded-xl shadow-xl lg:text-[24px] font-bold';
      const activeClasses = isActive ? 'bg-[#05808b] text-white' : 'bg-[#07838F33] text-[#05808b]';
      return (
        <Link key={key} to={to} className={`${baseClasses} ${activeClasses}`}>
          {label}
        </Link>
      );
    })}
  </div>
);

// Course Form Component
const CourseForm = ({ onClose, refetchCourses }) => {
  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    getValues,
    watch,
  } = useForm({
    defaultValues: {
      courseName: '',
      description: '',
      portalOrWebsite: '',
      courseImage: null,
      whatWillYouLearn: [],
      prerequisets: '',
    },
  });

  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const whatWillYouLearn = watch('whatWillYouLearn', []);
  const newLearningPoint = watch('newLearningPoint', '');

  const addLearningPoint = () => {
    if (newLearningPoint.trim()) {
      setValue('whatWillYouLearn', [...whatWillYouLearn, newLearningPoint]);
      setValue('newLearningPoint', '');
    }
  };

  const removeLearningPoint = (index) => {
    setValue(
      'whatWillYouLearn',
      whatWillYouLearn.filter((_, i) => i !== index)
    );
  };

  const onSubmit = async (data) => {
    try {
      setUploading(true);
      setUploadProgress(0);

      const formData = new FormData();
      formData.append('courseName', data.courseName);
      formData.append('description', data.description);
      formData.append('image', data.courseImage);
      formData.append('portalOrWebsite', data.portalOrWebsite);
      formData.append('prerequisets', data.prerequisets);
      formData.append('whatWillYouLearn', JSON.stringify(data.whatWillYouLearn));
      //console.log(data);
      await ApiS3.post('/academy', formData, {
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(percentCompleted);
        },
      });

      setUploading(false);
      onClose();
      refetchCourses();
    } catch (err) {
      console.error('Error creating course:', err);
      setUploading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <Controller
        name="courseName"
        control={control}
        rules={{ required: 'Course name is required' }}
        render={({ field }) => <TextInput {...field} label="Course Name" placeholder="Enter course name" error={errors.courseName?.message} />}
      />

      <Controller
        name="description"
        control={control}
        rules={{ required: 'Description is required' }}
        render={({ field }) => <Textarea {...field} label="Description" placeholder="Enter course description" error={errors.description?.message} />}
      />

      <Controller
        name="courseImage"
        control={control}
        rules={{ required: 'Course image is required' }}
        render={({ field }) => (
          <FileInput {...field} label="Course Image" placeholder="Upload course image" accept="image/*" error={errors.courseImage?.message} />
        )}
      />

      <Controller
        name="portalOrWebsite"
        control={control}
        rules={{ required: 'Selection is required' }}
        render={({ field }) => (
          <Select
            {...field}
            label="Portal or Website"
            placeholder="Select an option"
            data={[
              { value: 'website', label: 'Website' },
              { value: 'portal', label: 'Portal' },
              { value: 'both', label: 'Both' },
            ]}
            error={errors.portalOrWebsite?.message}
          />
        )}
      />
      <Controller
        name="prerequisets"
        rules={{ required: 'Prerequisites is required' }}
        control={control}
        render={({ field }) => <Textarea {...field} label="Prerequisites" placeholder="Enter prerequisites" error={errors.prerequisets?.message} />}
      />

      <div>
        <label className="font-medium">What Will You Learn?</label>
        <div className="flex space-x-2">
          <Controller
            name="newLearningPoint"
            control={control}
            rules={{ required: whatWillYouLearn.length <= 0 }}
            render={({ field }) => <TextInput {...field} placeholder="Enter a learning point" error={errors.whatWillYouLearn?.message} />}
          />
          <Button type="button" className="bg-[#05808b] hover:bg-[#05808b]" onClick={addLearningPoint}>
            Add
          </Button>
        </div>

        <div className="mt-2 space-y-2">
          {whatWillYouLearn.map((point, index) => (
            <div key={index} className="bg-gray-100 p-2 rounded-md flex justify-between items-center">
              <span>{point}</span>
              <Button type="button" variant="subtle" color="teal" onClick={() => removeLearningPoint(index)}>
                X
              </Button>
            </div>
          ))}
        </div>
      </div>

      {uploading && <Progress value={uploadProgress} label={`${uploadProgress}%`} size="lg" radius="xl" striped animate />}

      <Button type="submit" className="bg-[#05808b] hover:bg-[#05808b] mt-4" disabled={uploading}>
        {uploading ? 'Uploading...' : 'Submit'}
      </Button>
    </form>
  );
};

// Course Card Component
const CourseCard = ({ course, onClick, onDelete }) => (
  <div
    className="course bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 cursor-pointer flex flex-col h-full"
    onClick={onClick}
  >
    <div className="courseImage h-48 overflow-hidden">
      <LazyLoadImage src={course.courseImage} alt={`Cover image for ${course.courseName}`} effect="blur" className="w-full h-full object-cover" />
    </div>

    <div className="p-6 flex-1">
      <h1 className="text-2xl font-bold text-[#05808b] mb-3">{course.courseName}</h1>
      <p className="text-gray-600">{course.description}</p>
    </div>

    <div className="p-4">
      <Button
        className="w-full bg-[#05808b] hover:bg-[#05808b]"
        onClick={(e) => {
          e.stopPropagation();
          onDelete();
        }}
      >
        Delete Course
      </Button>
    </div>
  </div>
);
