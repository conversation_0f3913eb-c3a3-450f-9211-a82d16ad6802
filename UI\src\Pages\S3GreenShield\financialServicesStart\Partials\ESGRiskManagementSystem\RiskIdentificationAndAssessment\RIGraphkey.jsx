import React from "react";
import { Switch } from "@mantine/core";
import { IoEllipse } from "react-icons/io5";
import { PiExportLight } from "react-icons/pi";
import { Button } from "@mantine/core";

export default function RIGraphkey({onSwitchChange,exportToImage}) {
  return (
    <div className="flex flex-col md:ml-9 py-5 px-2 bg-white rounded-xl mt-2 shadow-lg items-center">
      <div className="flex flex-col justify-between items-start gap-3">
        <h3 className="font-bold text-[#000000] mb">GRAPH KEY</h3>

        <div className="flex justify-center items-center px-2 gap-3 w-full">
          <span className=" font-bold w-4 h-4 mb-2 text-xl">
            <IoEllipse className="text-[#FF0000]" />
          </span>
          <div className="flex flex-row justify-between mb-2 font-medium w-full">
            <span>Extreme Risks</span>
            <Switch
              // onChange={() => onSwitchChange()}
              size="md"
              color="#07838F"
            />
          </div>
        </div>

        <div className="flex justify-center items-center px-2 gap-3 w-full">
          <span className="font-bold w-4 h-4 mb-2 text-xl">
            <IoEllipse className="text-[#fe880e]" />
          </span>
          <div className="flex justify-between mb-2 font-medium w-full">
            <span className="">High Risks</span>
            <Switch
              // onChange={() => onSwitchChange()}
              size="md"
              color="#07838F"
            />
          </div>
        </div>

        <div className="flex justify-center items-center px-2 gap-3 w-full">
          <span className="font-bold w-4 h-4 mb-2 text-xl">
            <IoEllipse className="text-[#d3bb0f]" />
          </span>
          <div className="flex justify-between mb-2 font-medium w-full">
            <span className="mr-20">Medium Risks</span>
            <Switch
              // onChange={() => onSwitchChange()}
              size="md"
             color="#07838F"
            />
          </div>
        </div>

        <div className="flex justify-center items-center px-2 gap-3 w-full">
          <span className="font-bold  w-4 h-4 mb-2 text-xl">
            <IoEllipse className="text-[#01BD36]" />
          </span>
          <div className="flex justify-between mb-2 font-medium w-full">
            <span>Low Risks</span>
            <Switch size="md" color="#07838F" />
          </div>
        </div>
      </div>
      <Button
        className="border-2 w-3/5 h-[42px] text-sm font-bold mt-12 mb-4 rounded-lg text-secondary-300 border-secondary-300 bg-white px-2"
        size="md"
        onClick={()=>exportToImage()}
      >
        <span className="me-1">
          <PiExportLight className="text-lg" />
        </span>
        <span>PNG Export</span>
      </Button>
    </div>
  );
}
