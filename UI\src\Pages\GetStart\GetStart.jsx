import useSideBarRoute from "@/hooks/useSideBarRoute";
import MainLayout from "@/Layout/MainLayout";
import { useTranslation } from "react-i18next";
import Dash from "./Dash";

const GetStart = () => {
  const { GetStart } = useSideBarRoute();
  const { t } = useTranslation();

  return (
    <MainLayout
      menus={GetStart}
      activeChatbot
      Dash
      navbarTitle={
        <>
          <h2 className="text-2xl">
            {t("Welcome to LevelUp ESG®: Your Sustainability ERP")}
          </h2>
        </>
      }
    >
      <Dash />
    </MainLayout>
  );
};

export default GetStart;
