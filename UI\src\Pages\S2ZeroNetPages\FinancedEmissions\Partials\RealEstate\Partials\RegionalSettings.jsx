import { Select, TextInput } from "@mantine/core";


const RegionalSettings = () => {
  const selectOptions = {
    regions: ["United States", "European Union", "United Kingdom", "Canada", "Australia"],
    scope3Options: ["No (Building operations only)", "Yes (Include embodied carbon)"],
  };

  return (
    <div className="bg-[#FFFFFF] border border-[#E8E7EA] p-4 rounded-[10px]">
      <h2 className="text-2xl font-bold text-[#272727]">Regional Settings & Emission Factors</h2>
      <div className="grid grid-cols-4 gap-4 mt-4">
        <Select
          label="Primary Market Region"
          placeholder="Select a region"
          data={selectOptions.regions}
          inputWrapperOrder={["label", "input", "description"]}
        />
        <TextInput
          label="Grid Emission Factor (for EVs)"
          placeholder="386"
          description="g CO₂/kWh"
          inputWrapperOrder={["label", "input", "description"]}
        />
        <TextInput
          label="Natural Gas Factor"
          placeholder="386"
          description="g CO₂/kWh"
          inputWrapperOrder={["label", "input", "description"]}
        />
        <Select
          label="Include Scope 3 Emissions?"
          placeholder="Select an option"
          data={selectOptions.scope3Options}
          inputWrapperOrder={["label", "input", "description"]}
        />
      </div>
    </div>
  );
};

export default RegionalSettings;
