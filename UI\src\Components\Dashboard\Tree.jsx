import React, { useEffect, useState } from "react";
import flower from "../../assets/images/flower.png";
import tree1 from "../../assets/images/tree1.png";
import tree2 from "../../assets/images/tree2.png";
import tree3 from "../../assets/images/tree3.png";
import tree4 from "../../assets/images/tree4.png";
import Location from "./Location";
import ApiSustain360 from "@/Api/apiSustain360";

const Tree = () => {
  const [assessmentData, setAssessmentData] = useState({});
  useEffect(() => {
    ApiSustain360.get("/api/assessments/finished/last").then((response) => {
      const assessmentData = response.data;
      setAssessmentData(assessmentData);
    });
  }, []);
  return (
    <div className="mb-8 w-full flex flex-col items-center mt-16">
      <p className="border border-1 border-[#84CCD0] p-3 rounded-xl w-fit text-center">
        The outcome of the self-assessment level in contrast to your results is
        that you are at the {assessmentData?.maturity_level?.split(" ")[0]} level
      </p>
      <div className="flex justify-between mt-[70px] text-sm relative after:w-[82%] after:h-0.5 after:bg-black after:absolute after:bottom-[75%] md:after:bottom-[65%] lg:after:bottom-[55%] xl:after:bottom-[52%] after:left-1/2 after:-translate-x-1/2 after:-z-10">
        <div className="w-1/5 flex flex-col items-center relative">
          {assessmentData?.maturity_level?.split(" ")[0] == "Beginning" && <Location />}
          <div className="flex justify-center items-end w-full h-[150px]">
            <img src={flower} alt="tree1" className="h-[60px] w-[30%]" />
          </div>
          <h1 className="mt-5 mb-1.5 font-bold text-lg">Beginning</h1>
          <p className="text-center text-secondary-gray-200">
            We understand the significance of ESG/sustainability but have not
            started developing a formal strategy or integrating it into our
            business operations yet.
          </p>
        </div>

        <div className="w-1/5 flex flex-col items-center relative">
          {assessmentData?.maturity_level?.split(" ")[0] == "Developing" && <Location />}
          <div className="flex justify-center items-end w-full h-[150px]">
            <img src={tree1} alt="tree1" className="h-[90px]" />
          </div>
          <h1 className="mt-5 mb-1.5 font-bold text-lg">Developing</h1>
          <p className="text-center text-secondary-gray-200">
            We are in the early stages of developing an ESG/sustainability
            strategy, and it is not yet fully integrated into our business
            operations.
          </p>
        </div>

        <div className="w-1/5 flex flex-col items-center relative">
          {assessmentData?.maturity_level?.split(" ")[0] == "Maturing" && <Location />}
          <div className="flex justify-center items-end w-full h-[150px]">
            <img src={tree2} alt="tree1" className="h-[110px]" />
          </div>
          <h1 className="mt-5 mb-1.5 font-bold text-lg">Maturing</h1>
          <p className="text-center text-secondary-gray-200">
            We have a developed ESG/sustainability strategy but are still in the
            process of fully integrating it into our business operations.
          </p>
        </div>

        <div className="w-1/5 flex flex-col items-center relative">
          {assessmentData?.maturity_level?.split(" ")[0] == "Advancing" && <Location />}
          <div className="flex justify-center items-end w-full h-[150px]">
            <img src={tree3} alt="tree1" className="h-[130px]" />
          </div>
          <h1 className="mt-5 mb-1.5 font-bold text-lg">Advancing</h1>
          <p className="text-center text-secondary-gray-200">
            We have a well-defined ESG strategy integrated into core business;
            measuring and reporting on performance.
          </p>
        </div>

        <div className="w-1/5 flex flex-col items-center relative">
          {assessmentData?.maturity_level?.split(" ")[0] == "Leading" && <Location />}
          <div className="flex justify-center items-end w-full h-[150px]">
            <img src={tree4} alt="tree1" className="h-[150px]" />
          </div>
          <h1 className="mt-5 mb-1.5 font-bold text-lg">Leading</h1>
          <p className="text-center text-secondary-gray-200">
            We have a well-established ESG/sustainability strategy, integrated
            into our core business operations, with dedicated resources and
            metrics to monitor progress.
          </p>
        </div>
      </div>
      {/* <div className="w-[82%] h-[3px] relative bottom-[134px]  self-center bg-black -z-10"></div> */}
    </div>
  );
};

export default Tree;
