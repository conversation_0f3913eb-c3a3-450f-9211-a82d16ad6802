import SearchBox from "@/Components/SearchBox";
import React from "react";
import { FaRegCircle } from "react-icons/fa";

const DocReposity = () => {
  const recDocs = [
    "Sustainability Report 2023",
    "Green Marketing Guidelines",
    "ESG Initiative Documentation",
  ];
  return (
    <div>
      <div className="w-full flex justify-center">
        <SearchBox
          classNames={{
            input:
              "border-[1px] border-[#BFBFBF] rounded-lg shadow-sm lg:w-[800px] ",
          }}
        />
      </div>

      <div className="bg-white rounded-2xl mt-9 text-xl h-[105px] flex justify-center items-center p-5">
        <h3>[Secure, Searchable Document Repository]</h3>
      </div>

      <h3 className="font-medium text-xl mt-7 ">Recent Documents</h3>
      <div className="d lg:w-[570px] min-h-[115px] bg-white rounded-2xl p-4 mt-3">
        {recDocs.map((el) => (
          <div className="flex gap-4 mt-2 items-center">
            <FaRegCircle className="text-[#00C0A9]" />
            <h3> {el} </h3>
          </div>
        ))}
      </div>
    </div>
  );
};

export default DocReposity;
