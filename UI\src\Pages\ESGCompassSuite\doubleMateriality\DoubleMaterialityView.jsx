import ReportButton from "@/Components/ReportButton/ReportButton";
import DoubleMaterialityProvider from "@/Contexts/DoubleMaterialityContext";
import MainLayout from "@/Layout/MainLayout";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import { IoMdHome } from "react-icons/io";
import { Outlet } from "react-router";

export default function DoubleMaterialityView() {
  const { DoubleMaterialityAssessment } = useSideBarRoute();

  return (
    <MainLayout
      menus={DoubleMaterialityAssessment}
      navbarTitle={"Materiality Assessment"}
      NotificationsPosition={"top-right"}
      breadcrumbItems={[
        { title: <IoMdHome size={20} />, href: "/get-started" },
        { title: "Materiality Assessment", href: "#" },
      ]}
    >
      <DoubleMaterialityProvider>
        <Outlet />
      </DoubleMaterialityProvider>
      <br />
      <br />
    </MainLayout>
  );
}
