import ApiS1Config from "@/Api/apiS1Config";
import Loading from "@/Components/Loading";
import {
 btnStyle,
 linkStyle,
} from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/StaticData";
import { AreaChart } from "@mantine/charts";
import { Button, RingProgress, Text } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { FiDownload } from "react-icons/fi";
import { MdHistory,} from "react-icons/md";
import HistoryPopUp from "../../AssessmentType/Partials/HistoryPopUp";
import { assessmentLevel } from "../../ESGDD/Components/dueDiligenceData";
import ViewPDF from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/ViewPDF";
import Share from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/Share";

export default function HrddReport() {
 const [historyOpened, { open: historyOpen, close: historyClose }] =
  useDisclosure(false);
 const { t } = useTranslation();
 const [data, setdata] = useState({});
 const [lineData, setLineData] = useState([]);
 const [loading, setloading] = useState(true);
 const getDynamicData = async () => {
  const { data: dynamic } = await ApiS1Config.get("/dynamic_data", {
   headers: {
    assessmentType: "Human Rights Due Diligence (HRDD)",
    toolName: "Human Rights Due Diligence (HRDD) Assessment",
   },
  });

  await ApiS1Config.get("/dashboard", {
   headers: { assessmentType: "ESG Due Diligence" },
  })
   .then((response) => response.data.all_assessment_summaries)
   .then((assessmentData) => {
    // setAssessments(assessmentData);
    //console.log(assessmentData, "assessmentData");

    const data = assessmentData.map((item) => {
     let name = item?.name?.split(" ").slice(0, -1).join(" ");
     const Day = name?.split(" ")[0];
     const Month = name?.split(" ")[1].slice(0, 3);
     // //console.log(Day);
     return {
      Month: item?.total_score,
      date: `${Day} ${Month}`,
     };
    });

    setLineData(data);
   });

  setdata(dynamic);
  setloading(false);
 };
 useEffect(() => {
  getDynamicData();
 }, []);

 const riskCategory = [
  { id: 1, name: "LOW (A)", color: "#00C0A9", bg: "#00C0A936" },
  { id: 2, name: "Medium (B)", color: "#FFAB07", bg: "#FFAB0736" },
  { id: 3, name: "High (C)", color: "#FF6007", bg: "#FF600736" },
  { id: 4, name: "Critical (D)", color: "#AB0202", bg: "#AB020236" },
 ];

 let riskValue = riskCategory?.find(
  (risk) => risk.name == data?.overall_score?.risk_category
 );
 let assessLevel = assessmentLevel?.find(
  (risk) => risk.name == data?.overall_score?.assessment_level
 );

 let overallScore = data?.overall_score?.average_score;

 //console.log(data.overall_score);
 //console.log(riskValue, "riskValue");

 return (
  <>
   {loading ? (
    <Loading />
   ) : (
    <div className="flex flex-col w-full left-section min-h-svh mt-5">
     <div className="report-page flex flex-col lg:flex-row items-center lg:justify-between gap-y-3 flex-wrap mb-[38px]">
      <div>
       <Button className={linkStyle} size="md" onClick={historyOpen}>
        <MdHistory className="me-1" />
        Assessment History
       </Button>
      </div>
      <div className="flex flex-col lg:flex-row items-center justify-center gap-6">
       <ViewPDF
        btnStyle={
         data?.pdf_url
          ? btnStyle
          : "border-[1px] flex items-center px-2  h-[56px] w-[195px]  justify-center text-[17px] font-bold rounded-lg text-white border-gray-300 bg-gray-300 hover:text-white hover:bg-gray-300 cursor-not-allowed"
        }
        pdfUrl={data?.pdf_url}
        text={"View Report"}
        disabled={!data?.pdf_url && true}
       />

       <Share
        link={data?.pdf_url}
        btnStyle={
         data?.pdf_url
          ? btnStyle
          : "border-[1px] flex items-center px-2  h-[56px] w-[195px]  justify-center text-[17px] font-bold rounded-lg text-white border-gray-300 bg-gray-300 hover:text-white hover:bg-gray-300 cursor-not-allowed"
        }
        disabled={!data?.pdf_url && true}
       />
       <Button
        component="a"
        className={
         data?.pdf_url
          ? linkStyle
          : "border-[1px] flex items-center px-2  h-[56px] w-[195px]  justify-center text-[17px] font-bold rounded-lg text-white border-gray-300 bg-gray-300 hover:text-white hover:bg-gray-300 cursor-not-allowed"
        }
        href={data?.pdf_url}
        download
        disabled={!data?.pdf_url ? true : false}
       >
        Download Report
        <span>
         <FiDownload className="text-lg ms-1" />
        </span>
       </Button>
      </div>
     </div>

     <div className="flex flex-wrap mt-5 mb-7 gap-9 md:flex-nowrap">
      {/* donut chart */}
      <div className="flex bg-white lg:w-[33%] p-5 flex-grow gap-4 md:w-1/2 justify-center rounded-lg shadow-[0px_0px_10px_0px_#0000001A]">
       <div className="score-chart">
        <h5 className="font-bold text-center">{t("Overall Score")}</h5>
        <span className="flex items-center justify-center w-full">
         <RingProgress
          size={180}
          thickness={20}
          // roundCaps
          className="w-full aspect-square text-center flex justify-center items-center scale-x-[-1] rotate-90"
          sections={[{ value: overallScore * 20, color: "#29919B" }]}
          rootColor="#D4E9EB"
          roundCaps
          label={
           <Text
            c="black"
            fw={700}
            ta="center"
            size="xl"
            className="rotate-90 scale-x-[-1] lg:text-4xl flex justify-center items-center"
           >
            {overallScore}
           </Text>
          }
         />
        </span>
       </div>
      </div>

      {/* line chart */}
      <div className="bg-white flex-grow md:w-1/2 text-center p-5 rounded-lg shadow-[0px_0px_10px_0px_#0000001A]">
       <h5 className="mb-6 font-bold">{t("progressTracker")}</h5>
       <AreaChart
        h={200}
        data={lineData}
        dataKey="date"
        series={[{ name: "Month", color: "blue.6" }]}
        curveType="natural"
        withDots={false}
       />
      </div>
     </div>

     {loading ? (
      <Loading />
     ) : (
      <div className="d flex mt-5 mb-7 gap-9 md:flex-nowrap flex-wrap">
       <div className="risk-category flex flex-col items-center justify-around lg:w-[70%] w-full h-[245px] bg-white p-3 rounded-2xl">
        <h3 className="text-center text-base font-bold">Risk Category</h3>
        <Button
         className="text-lg lg:w-[290px] "
         radius={80}
         h={50}
         bg={riskValue?.bg}
         color={riskValue?.color}
         variant="outline"
        >
         {riskValue.name}
        </Button>
       </div>

       <div className="assess-level h-[245px] w-full bg-white p-3 flex flex-col  items-center justify-around rounded-2xl">
        <h3 className="text-center text-base font-bold">Assessment Level</h3>

        <div className="flex flex-wrap mx-auto">
         <Button
          className="text-lg lg:w-[290px] "
          radius={80}
          h={50}
          bg={assessLevel.bg}
          color={assessLevel.color}
          variant="outline"
         >
          {assessLevel.name}
         </Button>
        </div>
       </div>
      </div>
     )}

     <div className="bg-white rounded-2xl p-4">
      <h3 className="text-primary text-base font-bold">Interpretation</h3>
      <h3>
       The proposed investment is likely to have minimal or no adverse impact on
       the environmental and/or human population.
      </h3>
     </div>
    </div>
   )}
   {historyOpened && (
    <HistoryPopUp
     opened={historyOpened}
     close={historyClose}
     assessmentType={"Human Rights Due Diligence (HRDD)"}
    />
   )}
  </>
 );
}
