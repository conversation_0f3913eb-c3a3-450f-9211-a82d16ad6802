/* eslint-disable react-hooks/exhaustive-deps */
import cx from "clsx";
import { Table, ScrollArea, Button, Pagination } from "@mantine/core";
import { RiDeleteBin6Line } from "react-icons/ri";
import { useTranslation } from "react-i18next";
import { useDeleteData } from "./hooks/useDeleteData";
import EditDataPopUp from "./EditDataPopUp";
import { useEffect, useMemo, useState } from "react";
import { useGetData } from "./hooks/useGetData";
import ViewPDF from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/ViewPDF";

export default function AIDataDrivenTable() {
  const { data: items, handleGetData } = useGetData();
  const { handleDeleteData } = useDeleteData();
  const [currentPage, setCurrentPage] = useState(1);
  const rowsPerPage = 10;
  const totalPages = Math.ceil(items?.length / rowsPerPage);

  useEffect(() => {
    handleGetData();
  }, []);

  const currentData = useMemo(() => {
    return (
      (items &&
        items?.slice(
          (currentPage - 1) * rowsPerPage,
          currentPage * rowsPerPage
        )) ||
      []
    );
  }, [items, currentPage, rowsPerPage]);

  const rows = currentData.map(({ id, data, file_url }) => {
    //   const selected = selection.includes(idx);

    return (
      <Table.Tr
        key={id}
        className={`${cx({
          // ["bg-[#07838F1A]"]: selected,
        })} text-sm font-bold text-[#626364] text-center`}
      >
        <Table.Td>
          <Button
            color="red"
            className="flex justify-center items-center text-xl"
            onClick={() => handleDeleteData(id)}
          >
            <RiDeleteBin6Line />
          </Button>
        </Table.Td>
        <Table.Td>
          <EditDataPopUp data={data} id={id} />
        </Table.Td>
        <Table.Td>
          <ViewPDF
            btnStyle={
              "bg-[#07838F] text-white px-2 py-1 rounded-lg text-sm text-center"
            }
            pdfUrl={file_url}
            text=""
          />
        </Table.Td>
        <Table.Td className="text-nowrap">{data["Billing Period"]}</Table.Td>
        <Table.Td>{data["Cost per kWh"].toFixed(2)}</Table.Td>
        <Table.Td>{data["Customer Number"]}</Table.Td>
        <Table.Td>{data["Statement Date"]}</Table.Td>
        <Table.Td>{data["Total Energy Cost (excl. VAT)"]}</Table.Td>
        <Table.Td>{data["Total Energy Cost (incl. VAT)"]}</Table.Td>
        <Table.Td>{data["Total kWh Consumed"]}</Table.Td>
      </Table.Tr>
    );
  });
  const { t } = useTranslation();

  return (
    <div className="bg-[#F7F4F4] mt-7">
      <ScrollArea>
        <Table
          verticalSpacing="sm"
          className="p-2 my-1 bg-white shadow-lg rounded-xl"
        >
          <Table.Thead className="border-b-2 Data-Table-Ai-View bg-[#F5F4F5] border pb-6 text-secondary-500 font-bold text-base text-center">
            <Table.Tr>
              <Table.Th className="text-center text-[#433F59] Delete-the-ai-record">Delete</Table.Th>
              <Table.Th className="text-center text-[#433F59] Edit-the-ai-record">Edit</Table.Th>
              <Table.Th className="text-center Open-a-detailed-view-of-the-record text-[#433F59] text-nowrap">
                View File
              </Table.Th>
              <Table.Th className="text-center text-[#433F59]">
                Billing Period
              </Table.Th>
              <Table.Th className="text-center text-[#433F59]">
                Cost per kWh
              </Table.Th>
              <Table.Th className="text-center text-[#433F59]">
                Customer Number
              </Table.Th>
              <Table.Th className="text-center text-[#433F59]">
                Statement Date
              </Table.Th>
              <Table.Th className="text-center text-[#433F59]">
                Total Energy Cost (excl. VAT)
              </Table.Th>
              <Table.Th className="text-center text-[#433F59]">
                Total Energy Cost (incl. VAT)
              </Table.Th>
              <Table.Th className="text-center text-[#433F59]">
                Total kWh Consumed
              </Table.Th>
              {/* <Table.Th className="text-center">
                <span className="flex gap-3 justify-center">
                  <span className="bg-white text-[#404B52] rounded-md w-8 h-8 flex justify-center items-center text-sm font-medium cursor-pointer">
                    <MdKeyboardArrowLeft />
                  </span>
                  <span className="bg-white text-[#404B52] rounded-md w-8 h-8 flex justify-center items-center text-sm font-medium cursor-pointer">
                    <MdKeyboardArrowRight />
                  </span>
                </span>
              </Table.Th> */}
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>{rows}</Table.Tbody>
        </Table>
      </ScrollArea>
      <div className="md:flex justify-between mt-5">
        <p className="text-sm text-gray-600 font-bold">
          {t("showingData", {
            start: (currentPage - 1) * rowsPerPage + 1,
            end: Math.min(currentPage * rowsPerPage, items?.length),
            total: items?.length,
          })}
        </p>
        <Pagination
          page={currentPage}
          onChange={(e) => {
            setCurrentPage(e);
          }}
          total={totalPages}
          color="#dde7e9"
          autoContrast
          className={`flex justify-center mt-5 gap-0 md:mt-0 ${
            !items?.length && "hidden"
          }`}
          classNames={{ control: "gap:none rounded-none" }}
        />
      </div>
    </div>
  );
}
