import { Button, Input, Modal } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { CiShare2 } from "react-icons/ci";
import pdf from "./1_1_22Oct2024_10-33-16-98_output.pdf";

const Share = ({link,btnStyle,disabled}) => {

 const { t } = useTranslation();

 const [opened, { open, close }] = useDisclosure(false);

 const [shareTo, setShareTo] = useState("");

 return (
  <div className="">
   <Button
    onClick={open}
    className={btnStyle||"border-[1px] flex items-center px-4 justify-between w-[195px] h-[56px] text-[17px] font-bold rounded-lg text-secondary-300 border-secondary-300 bg-white hover:text-white hover:bg-secondary-300"} disabled={disabled}
   >
    <span>{t("share")}</span>
    <span>
     <CiShare2 className="text-lg ms-2" />
    </span>
   </Button>
   <Modal opened={opened} onClose={close} centered>
    <div className="text-center">
     <Input
      placeholder="Write email"
      size="lg"
      radius={12}
      color="green"
      value={shareTo}
      onChange={(e) => setShareTo(e.currentTarget.value)}
     />
     <a
      href={`mailto:${shareTo}?subject=Sustainability%20Maturity%20Assessment%20Results&body=Here%20is%20the%20link%20to%20the%20report:%20${link}`}
      target="_blank"
      className={`${
       shareTo
        ? "bg-[#2b939c] border-primary text-primary"
        : "pointer-events-none bg-gray-400"
      } inline-block mt-4 border-2  mx-3  bg-opacity-10 px-10 py-2 rounded-lg active:scale-95 transition-all`}
     >
      Share
     </a>
    </div>
   </Modal>
  </div>
 );
};

export default Share;
