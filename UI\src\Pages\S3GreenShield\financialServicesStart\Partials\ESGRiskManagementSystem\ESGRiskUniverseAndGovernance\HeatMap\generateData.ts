const generateData = (xValues) => {
  const staticData = [
    [3, 3, 3, 9, 15],    // Rare
    [3, 3, 9, 9, 15],     // Unlikely
    [3, 9, 9, 15, 15],    // Possible
    [9, 9, 15, 15, 25],   // Likely
    [9, 15, 15, 25, 25]   // Almost Certain// Row 1: #FEF089, #FFC787, #FFC787, #FBC8C8, #FBC8C8
  ];

  return staticData.map((row, rowIndex) => {
    return row.map((y, columnIndex) => ({
      x: xValues[columnIndex],
      y,
    }));
  });
};

export default generateData;
