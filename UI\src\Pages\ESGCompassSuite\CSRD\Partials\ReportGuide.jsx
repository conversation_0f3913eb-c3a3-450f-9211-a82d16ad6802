import { useState } from "react";
import {
    Box,
    Title,
    Text,
    List,
    Group,
    ThemeIcon,
    Stack,
    Paper,
} from "@mantine/core";
import { FaCheck } from "react-icons/fa";

export default function CsrdReportingGuide() {
    return (
        <Box mx="auto" maxWidth="1200px" p="md">
            <Title order={1} mb="xl">
                CSRD Readiness: Reporting User Guide
            </Title>

            {/* Page Structure Section */}
            <Stack spacing="lg">
                <Text size="lg" mb="sm">
                    <b>Page Structure</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>The Reporting page includes:</Text>
                        <List listStyleType="circle" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={400}>
                                    Visualized Reports: Displays assessment data
                                    in an intuitive format.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={400}>
                                    Share Option: The ability to distribute
                                    reports to stakeholders.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={400}>
                                    Download Feature: To save reports for
                                    offline access.
                                </Text>
                            </List.Item>
                        </List>
                    </List.Item>
                </List>

                {/* Interacting with the Reporting Page Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Interacting with the Reporting Page</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>View Reports:</Text>
                        <Text size="sm">
                            Explore visualized insights from your CSRD readiness
                            assessment.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Share Reports:</Text>
                        <Text size="sm">
                            Send reports to stakeholders to enhance
                            collaboration.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text fw={700}>Download Reports:</Text>
                        <Text size="sm">
                            Save reports for offline use or record-keeping.
                        </Text>
                    </List.Item>
                </List>

                {/* Understanding Progress Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Understanding Your Progress</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>
                        <Text fw={700}>Report Overview:</Text>
                        <List listStyleType="disc" spacing="sm" ml="1.5rem">
                            <List.Item>
                                <Text fw={700}>Visual Insights:</Text>
                                <Text size="sm">
                                    Reports present your CSRD compliance status
                                    and ESRS alignment clearly.
                                </Text>
                            </List.Item>
                            <List.Item>
                                <Text fw={700}>Stakeholder Focus:</Text>
                                <Text size="sm">
                                    Designed to meet expectations for
                                    transparency and regulatory compliance.
                                </Text>
                            </List.Item>
                        </List>
                    </List.Item>
                </List>

                {/* Next Steps Section */}
                <Text size="lg" mt="lg" mb="sm">
                    <b>Next Steps</b>
                </Text>
                <List listStyleType="decimal" spacing="sm">
                    <List.Item>
                        <Text>
                            Use visualized reports to identify CSRD compliance
                            strengths and areas for improvement.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Share reports with stakeholders to demonstrate your
                            sustainability reporting progress.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Download reports for offline access or to maintain
                            compliance records.
                        </Text>
                    </List.Item>
                    <List.Item>
                        <Text>
                            Regularly generate new reports to track progress
                            over time.
                        </Text>
                    </List.Item>
                </List>

                {/* Tip Section */}
                <Paper shadow="sm" p="md" radius="md" mt="xl" withBorder>
                    <Group position="apart">
                        <ThemeIcon variant="light" radius="xl" size="2rem">
                            <FaCheck size="1.2rem" />
                        </ThemeIcon>
                        <Text fw={500}>
                            Share your report with stakeholders to showcase your
                            CSRD readiness!
                        </Text>
                    </Group>
                </Paper>
            </Stack>
        </Box>
    );
}
