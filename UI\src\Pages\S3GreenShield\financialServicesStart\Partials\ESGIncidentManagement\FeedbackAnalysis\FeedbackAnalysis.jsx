import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@mantine/charts";
import ApiS3 from "@/Api/apiS3";

// Consistent color palette with improved accessibility
const COLORS = {
  environmental: "#00A69C", // Teal - more vibrant
  social: "#0074D9", // Blue - more accessible
  governance: "#FF851B", // Orange - better contrast

  // Stakeholder colors with better contrast
  shareholders: "#00A69C", // Teal
  employees: "#FF851B", // Orange
  customers: "#0074D9", // Blue
  suppliers: "#B10DC9", // Purple
  communities: "#2ECC40", // Green
  government: "#001F3F", // Navy
  others: "#7FDBFF", // Light blue
};

// Color mapping objects
const stakeholderColors = {
  "Shareholders/ Investors": COLORS.shareholders,
  Employees: COLORS.employees,
  Customers: COLORS.customers,
  Suppliers: COLORS.suppliers,
  Communities: COLORS.communities,
  Government: COLORS.government,
  Others: COLORS.others,
};

const distributionColors = {
  Environmental: COLORS.environmental,
  Social: COLORS.social,
  Governance: COLORS.governance,
};

// Helper function to format chart data
function formatChartData(data, colorMap) {
  if (!data) return [];

  return Object.entries(data).map(([key, value]) => ({
    category: key,
    value,
    color: colorMap[key] || "#AAAAAA", // Better fallback color
  }));
}

export default function FeedbackAnalysis() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [monthlyData, setMonthlyData] = useState([]);
  const [overallDistributionData, setOverallDistributionData] = useState([]);
  const [stakeholderGroupData, setStakeholderGroupData] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch both data endpoints in parallel
        const [monthlyResponse, analysisResponse] = await Promise.all([
          ApiS3.get("stakeholder-interactions/InteractionsInfo"),
          ApiS3.get("stakeholder-interactions/overAllAnalysis"),
        ]);

        // Process monthly data
        if (monthlyResponse.data && monthlyResponse.data.monthlyData) {
          setMonthlyData(monthlyResponse.data.monthlyData);
        }

        // Process overall analysis data
        if (analysisResponse.data && analysisResponse.data.length >= 2) {
          const [stakeholderRaw, distributionRaw] = analysisResponse.data;

          setStakeholderGroupData(
            formatChartData(stakeholderRaw, stakeholderColors)
          );
          setOverallDistributionData(
            formatChartData(distributionRaw, distributionColors)
          );
          console.log("Stakeholder Group Data:", stakeholderGroupData);
          console.log("Overall Distribution Data:", overallDistributionData);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        setError("Failed to load dashboard data. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Loading state
  if (isLoading) {
    return (
      <div className="w-full h-64 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard data...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="w-full bg-red-50 p-4 rounded-lg border border-red-200 text-center">
        <p className="text-red-500 font-medium">{error}</p>
        <button
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          onClick={() => window.location.reload()}
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="w-full pb-12">
      {/* Dashboard Header */}
      {/* <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">
          Stakeholder Feedback Analysis
        </h1>
        <p className="text-gray-600">
          Comprehensive view of stakeholder interactions and sentiment
        </p>
      </div> */}

      {/* Monthly Feedback Chart */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 className="text-lg font-semibold mb-4 text-gray-800">
          Feedback by Month
        </h2>
        <div className="h-80">
          {monthlyData.length > 0 ? (
            <BarChart
              h={300}
              data={monthlyData}
              dataKey="month"
              type="grouped"
              withLegend
              legendProps={{
                verticalAlign: "bottom",
                align: "center",
                height: 50,
              }}
              gridProps={{ stroke: "#E2E8F0", strokeDasharray: "5 5" }}
              series={[
                { name: "Environmental", color: COLORS.environmental },
                { name: "Social", color: COLORS.social },
                { name: "Governance", color: COLORS.governance },
              ]}
              tickLine="xy"
              gridAxis="xy"
            />
          ) : (
            <div className="h-full flex items-center justify-center">
              <p className="text-gray-500">No monthly data available</p>
            </div>
          )}
        </div>
      </div>

      {/* Distribution and Stakeholder Charts */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        {/* Overall Distribution */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-lg font-semibold mb-4 text-gray-800">
            ESG Distribution
          </h2>
          {overallDistributionData.length > 0 ? (
            <PieChart
              data={overallDistributionData}
              size={300}
              withLabelsLine
              labelsPosition="outside"
              labelsType="percent"
              withLabels
              withTooltip
              tooltipDataSource="segment"
              mx="auto"
              withLegend
              legendProps={{
                verticalAlign: "middle",
                align: "right",
                layout: "vertical",
              }}
            />
          ) : (
            <div className="h-64 flex items-center justify-center">
              <p className="text-gray-500">No distribution data available</p>
            </div>
          )}
        </div>

        {/* Stakeholder Group */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-lg font-semibold mb-4 text-gray-800">
            Stakeholder Group Distribution
          </h2>
          {stakeholderGroupData.length > 0 ? (
            <PieChart
              size={300}
              data={stakeholderGroupData}
              withLabelsLine
              labelsPosition="outside"
              labelsType="percent"
              withLabels
              withTooltip
              tooltipDataSource="segment"
              mx="auto"
              withLegend
              legendProps={{
                verticalAlign: "middle",
                align: "right",
                layout: "vertical",
              }}
            />
          ) : (
            <div className="h-64 flex items-center justify-center">
              <p className="text-gray-500">No stakeholder data available</p>
            </div>
          )}
        </div>
      </div>

      {/* Sentiment Trend Chart */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-lg font-semibold mb-4 text-gray-800">
          Sentiment Trend Over Time
        </h2>
        {monthlyData.length > 0 ? (
          <LineChart
            h={300}
            data={monthlyData}
            dataKey="month"
            series={[{ name: "sentiment", color: "#3B82F6", lineWidth: 2.5 }]}
            curveType="natural"
            tickLine="xy"
            gridAxis="xy"
            gridProps={{ stroke: "#E2E8F0", strokeDasharray: "5 5" }}
            withLegend
            yAxisProps={{ domain: [-1, 1] }}
            tooltipProps={{
              content: ({ payload }) => {
                if (payload && payload.length > 0) {
                  const data = payload[0].payload;
                  return (
                    <div className="bg-white p-2 border border-gray-200 shadow-md rounded-md">
                      <p className="font-semibold">{data.month}</p>
                      <p className="text-sm">
                        Sentiment:{" "}
                        <span className="font-medium">
                          {data.sentiment.toFixed(2)}
                        </span>
                      </p>
                    </div>
                  );
                }
                return null;
              },
            }}
          />
        ) : (
          <div className="h-64 flex items-center justify-center">
            <p className="text-gray-500">No sentiment data available</p>
          </div>
        )}
      </div>
    </div>
  );
}
