import { useContext } from "react";
import axios from "axios";
import Cookies from "js-cookie";
import { AIDataDriven } from "../AiDataDrivenContext";

export const useGetData = () => {
  const context = useContext(AIDataDriven);
  if (!context) throw new Error("useData must be used within a DataProvider");

  const { data, setData, loading, setLoading } = context;

  const handleRemove = (indexToRemove) => {
    setData((prev) => prev.filter((_, idx) => idx !== indexToRemove));
  };

  const handleGetData = async () => {
    setLoading(true);
    try {
      const { data } = await axios.get(
        "https://pdf-extraction-staging.azurewebsites.net/list",
        {
          headers: {
            "Content-Type": "application/json",
            withCredentials: true,
            Authorization: `Bearer ${Cookies.get("level_user_token")}`,
          },
        }
      );
      console.log(data);
      setData(data);
      setLoading(false);
    } catch (error) {
      console.log(error);
      setLoading(false);
    }
  };

  return { data, loading, handleRemove, handleGetData };
};
