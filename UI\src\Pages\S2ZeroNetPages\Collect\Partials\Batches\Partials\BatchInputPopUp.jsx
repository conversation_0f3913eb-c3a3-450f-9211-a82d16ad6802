import { Modal } from "@mantine/core";
import React from "react";
import PopUpTable from "./PopUpTable";

export default function BatchInputPopUp({
  opened,
  onClose,
  currentBatchId,
  data,
  assetTypeAll,
}) {
  const Status = {
    Pending: {
      bg: "#ffeecd",
      text: "#FFAB07",
      // border: "#00C0A9",
    },
    Accepted: {
      bg: "#ccf2d7",
      text: "#01BD36",
      // border: "#FF6007",
    },
    Rejected: {
      bg: "#eecccc",
      text: "#AB0202",
      // border: "#FF6007",
    },
  };
  const CurrentRow = data?.filter((item) => currentBatchId === item.id);
  return (
    <Modal
      opened={opened}
      onClose={onClose}
      centered
      size={"100%"}
      withCloseButton={false}
    >
      <PopUpTable
        data={CurrentRow}
        Status={Status}
        assetTypeAll={assetTypeAll}
      />
    </Modal>
  );
}
