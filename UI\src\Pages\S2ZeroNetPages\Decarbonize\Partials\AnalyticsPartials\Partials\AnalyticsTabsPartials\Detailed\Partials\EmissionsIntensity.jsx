import React from "react";
import { Scatter<PERSON>hart } from "@mantine/charts";

const EmissionsIntensity = () => {
  const data = [
    {
      color: "blue.5",
      name: "Group 1",
      data: [
        { age: 10000, BMI: 20000 },
        { age: 20000, BMI: 22000 },
        { age: 25000, BMI: 18000 },
        { age: 30000, BMI: 25000 },
        { age: 30000, BMI: 30000 },
        { age: 50000, BMI: 15000 },
        { age: 60000, BMI: 12000 },
        { age: 70000, BMI: 28000 },
        { age: 80000, BMI: 10000 },
        { age: 90000, BMI: 40000 },
        { age: 100000, BMI: 50000 },
        { age: 110000, BMI: 40000 },
        { age: 120000, BMI: 50000 },
        { age: 130000, BMI: 50000 },
        { age: 140000, BMI: 60000 },
      ],
    },
  ];
  return (
    <div className="">
      <div className="flex items-center justify-between p-2 mb-4">
        <h3 className="text-2xl font-semibold me-9">Emissions Intensity</h3>
      </div>
      <ScatterChart
        className="bg-white py-8 px-4 rounded-lg shadow-lg w-[500px] "
        h={350}
        // w={600}
        data={data}
        dataKey={{ x: "age", y: "BMI" }}
        // xAxisLabel="Age"
        // yAxisLabel="BMI"
      />
    </div>
  );
};

export default EmissionsIntensity;
