import { ArrowRightIcon } from "@/assets/icons";
import { ScrollArea, Table } from "@mantine/core";
import { useTranslation } from "react-i18next";

const FuelBasedMethod = () => {
  const { t } = useTranslation();

  return (
    <>
    <div className="bg-white p-4 mt-5 rounded-xl border-2">
      <ScrollArea miw={600}>
        <Table
          // miw={800}
          verticalSpacing="sm"
          className="p-2 bg-white"
          withTableBorder
          highlightOnHover
        >
          <Table.Thead className="border-b-2 border pb-6 bg-[#f5f4f5] font-bold text-lg text-center">
            <Table.Tr>
              <Table.Th>{t("Parameter")}</Table.Th>
              <Table.Th>{t("Value")}</Table.Th>
              <Table.Th>{t("AI Assistant")}</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody className="text-lg">
            <Table.Tr>
              <Table.Td>
                {t("Distance (km)")}
              </Table.Td>
              <Table.Td>
                <input
                  type="text"
                  placeholder={t("e.g., Diesel")}
                  className="w-full border rounded px-2 py-2"
                  disabled
                />
              </Table.Td>
              <Table.Td className="align-middle">
                <span className="text-gray-500">{t("Required Input")}</span>
              </Table.Td>
            </Table.Tr>
            <Table.Tr>
              <Table.Td>
                {t("Distance (km)")}
              </Table.Td>
              <Table.Td>
                <input
                  type="text"
                  placeholder={t("e.g., 500")}
                  className="w-full outline-none border rounded px-2 py-2"
                />
              </Table.Td>
              <Table.Td className="align-middle">
                <button
                  className="bg-[#07838F] w-full text-white px-4 py-2 rounded font-semibold"
                  type="button"
                >
                  {t("Estimate")}
                </button>
              </Table.Td>
            </Table.Tr>
            <Table.Tr>
              <Table.Td>
                {t("Solid Goods Mass (kg)")}
              </Table.Td>
              <Table.Td>
                <input
                  type="text"
                  placeholder={t("e.g., 2000")}
                  className="w-full border rounded px-2 py-1"
                  disabled
                />
              </Table.Td>
              <Table.Td className="align-middle">
                <span className="text-gray-500">{t("From inventory")}</span>
              </Table.Td>
            </Table.Tr>
          </Table.Tbody>
        </Table>
        <button
                  className="bg-[#07838F] mt-5 w-full text-white px-4 py-2 rounded font-semibold"
                  type="button"
                >
                  {t("Calculate Transportation Emissions")}
                </button>
      </ScrollArea>
    </div>
    
    <div className="bg-white border-2 border-[#E8E7EA] rounded-lg p-4 mt-8 mb-2">
      <div className="font-semibold text-gray-700 mb-2">
        Downstream Calculation Tips
      </div>
      <div className="text-sm text-gray-600 space-y-2">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 rounded-full bg-[#07838F]"></div>
          <span>Covers transport to end customers only</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 rounded-full bg-[#07838F]"></div>
          <span>Exclude Company-owned vehicles (Scope 1)</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 rounded-full bg-[#07838F]"></div>
          <span>Focus on largest distribution channels first</span>
        </div>
      </div>
      <div className="mt-2 bg-[#07838F]/10 w-fit p-1 rounded-lg">
        <a
          href="#"
          className="text-[#07838F] text-sm hover:underline flex items-center gap-1"
        >
          Need help? <span className="underline-none">View detailed guide</span> <ArrowRightIcon />
        </a>
      </div>
    </div>
    </>
  );
};

export default FuelBasedMethod;
