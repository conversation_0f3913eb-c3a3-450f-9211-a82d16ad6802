import { useState } from "react";
import Select from "react-select";
import countries from "world-countries";
const prioritized = ['United Kingdom', 'United Arab Emirates', 'United States', 'Saudi Arabia', 'Qatar'];

// Sort countries: prioritized first, then the rest
const sortedCountries = [
  ...countries.filter(c => prioritized.includes(c.name.common)),
  ...countries
    .filter(c => !prioritized.includes(c.name.common))
    .sort((a, b) => a.name.common.localeCompare(b.name.common))
];

// Convert to react-select format
const countryOptions = sortedCountries.map(country => ({
  value: country.name.common,
  label: country.name.common,
}));

// Define reporting framework options
const frameworkOptions = [
  { value: "ISSB", label: "ISSB" },
  { value: "TNFD", label: "TNFD" },
  { value: "GRI", label: "GRI" },
];

export default function CompanyInfoForm({ companyInfo, onChange, onNext }) {
  const industryOptions = [
    { value: "Energy", label: "Energy" },
    { value: "Materials", label: "Materials" },
    { value: "Industrials", label: "Industrials" },
    { value: "Consumer Discretionary", label: "Consumer Discretionary" },
    { value: "Consumer Staples", label: "Consumer Staples" },
    { value: "Health Care", label: "Health Care" },
    { value: "Financials", label: "Financials" },
    { value: "Information Technology", label: "Information Technology" },
    { value: "Communication Services", label: "Communication Services" },
    { value: "Utilities", label: "Utilities" },
    { value: "Real Estate", label: "Real Estate" },
    { value: "Other", label: "Other" },
  ];

  const [selectedCountry, setSelectedCountry] = useState(
    countryOptions.find((c) => c.label === companyInfo.countryIncorp) || null
  );
  const [selectedCountries, setSelectedCountries] = useState(
    companyInfo.countryOperations
      ?.split(", ")
      .map((label) => countryOptions.find((c) => c.label === label)) || []
  );
  const [selectedIndustry, setSelectedIndustry] = useState(
    industryOptions.find(
      (c) => c.value === companyInfo.industryClassification
    ) || null
  );
  const [selectedFramework, setSelectedFramework] = useState(
    frameworkOptions.find((f) => f.value === companyInfo.reportingFramework) || null
  );

  const handleCountryChange = (selected) => {
    setSelectedCountry(selected);
    onChange({
      target: { name: "countryIncorp", value: selected?.label || "" },
    });
  };

  const handleCountriesChange = (selected) => {
    setSelectedCountries(selected || []);
    onChange({
      target: {
        name: "countryOperations",
        value: selected?.map((c) => c.label).join(", ") || "",
      },
    });
  };

  const handleIndustryChange = (selected) => {
    setSelectedIndustry(selected);
    onChange({
      target: { name: "industryClassification", value: selected?.value || "" },
    });
  };

  const handleFrameworkChange = (selected) => {
    setSelectedFramework(selected);
    onChange({
      target: { name: "reportingFramework", value: selected?.value || "" },
    });
  };

  const handleIssuerChange = (e) => {
    onChange(e);
  };

  // Validation to ensure all required fields are filled
  const isValid =
    // companyInfo.companyName.trim() !== '' &&
    // selectedCountry !== null &&
    // selectedCountries.length > 0 &&
    // companyInfo.revenue.trim() !== '' &&
    // companyInfo.numEmployees.trim() !== '' &&
    // selectedIndustry !== null &&
    selectedFramework !== null;

  return (
    <div className="bg-white p-6 rounded-md border border-gray-300">
      <h1 className="text-2xl text-center font-bold mb-4 text-[#272727]">
        Company Specific Information
      </h1>
      <div className="flex flex-col max-md:text-start gap-4">
        <div>
          <label htmlFor="companyName" className="font-medium text-[#272727]">
            Name of Entity/Company
          </label>
          <input
            type="text"
            id="companyName"
            name="companyName"
            value={companyInfo.companyName || ""}
            placeholder="Enter text"
            onChange={onChange}
            className="border border-gray-300 text-[#272727] rounded w-full p-2"
          />
        </div>

        <div>
          <label id="countryIncorpLabel" className="font-medium text-[#272727]">
            Country of Incorporation
          </label>
          <Select
            inputId="countryIncorp"
            aria-labelledby="countryIncorpLabel"
            placeholder="Select"
            options={countryOptions}
            value={selectedCountry}
            onChange={handleCountryChange}
            className="w-full text-[#272727]"
          />
        </div>

        <div>
          <label id="countryOperationsLabel" className="font-medium text-[#272727]">
            Country/-ies of operations (you can select multiple countries)
          </label>
          <Select
            inputId="countryOperations"
            aria-labelledby="countryOperationsLabel"
            options={countryOptions}
            value={selectedCountries}
            placeholder="Select"
            isMulti
            onChange={handleCountriesChange}
            className="w-full text-[#272727]"
          />
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label htmlFor="revenue" className="font-medium text-[#272727]">
              Sales/revenue in the last available reporting year
            </label>
            <input
              type="number"
              id="revenue"
              name="revenue"
              placeholder="Enter number"
              value={companyInfo.revenue || 0}
              onChange={onChange}
              className="border border-gray-300 rounded w-full p-2 text-[#272727]"
            />
          </div>
          <div>
            <label htmlFor="numEmployees" className="font-medium text-[#272727]">
              Number of employees in the last available reporting year
            </label>
            <input
              type="number"
              id="numEmployees"
              name="numEmployees"
              placeholder="Enter number"
              value={companyInfo.numEmployees || 0}
              onChange={onChange}
              className="border border-gray-300 rounded w-full p-2 text-[#272727]"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label id="industryClassificationLabel" className="font-medium text-[#272727]">
              Industry Classification
            </label>
            <Select
              inputId="industryClassification"
              aria-labelledby="industryClassificationLabel"
              placeholder="Select"
              options={industryOptions}
              value={selectedIndustry}
              onChange={handleIndustryChange}
              className="w-full text-[#272727]"
            />
          </div>
          <div>
            <label htmlFor="issuerIdentifier" className="font-medium text-[#272727]">
              Issuer identifier (if the entity is listed) - SEDOL, ISIN, CUSIP
            </label>
            <input
              type="text"
              id="issuerIdentifier"
              name="issuerIdentifier"
              placeholder="Enter identifier"
              value={companyInfo.issuerIdentifier || ""}
              onChange={handleIssuerChange}
              className="border border-gray-300 rounded w-full p-2 text-[#272727]"
            />
          </div>
        </div>

        <div>
          <label id="reportingFrameworkLabel" className="font-medium text-[#272727]">
            Reporting Framework
          </label>
          <Select
            inputId="reportingFramework"
            aria-labelledby="reportingFrameworkLabel"
            placeholder="Select"
            options={frameworkOptions}
            value={selectedFramework}
            onChange={handleFrameworkChange}
            className="w-full text-[#272727]"
          />
        </div>
      </div>
      <div className="flex justify-end mt-4">
        <button
          onClick={onNext}
          disabled={!isValid}
          className={`bg-[#07838F] text-white px-6 w-40 py-1 rounded ${
            !isValid ? 'opacity-50 cursor-not-allowed' : 'hover:bg-[#07848fce]'
          }`}
        >
          Next
        </button>
      </div>
    </div>
  );
}