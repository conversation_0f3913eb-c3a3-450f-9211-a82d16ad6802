import { Checkbox, Combobox, Group, useCombobox } from "@mantine/core";
import { IoFilter } from "react-icons/io5";
import { useEffect, useRef } from "react";
import { CiFilter } from "react-icons/ci";

export default function CollectMultiselectFilter({ setValue, value,style }) {
  const groceries = ["Pending", "Accepted", "Rejected"];
  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
    onDropdownOpen: () => combobox.updateSelectedOptionIndex("active"),
  });

  const dropdownRef = useRef(null);

  const handleValueSelect = (val) => {
    setValue((current) =>
      current?.includes(val)
        ? current?.filter((v) => v !== val)
        : [...current, val]
    );
  };

  const isDisabled = value?.length >= 1;

  const options = groceries.map((item) => (
    <Combobox.Option
      value={item}
      key={item}
      active={value?.includes(item)}
      disabled={isDisabled && !value.includes(item)}
    >
      <Group gap="lg">
        <Checkbox
          checked={value?.includes(item)}
          onChange={() => handleValueSelect(item)}
          aria-hidden
          tabIndex={-1}
          style={{ pointerEvents: "none" }}
          disabled={isDisabled && !value.includes(item)}
        />
        <span className="ms-1">{item}</span>
      </Group>
    </Combobox.Option>
  ));

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        combobox.closeDropdown();
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [combobox]);

  return (
    <div ref={dropdownRef}>
      <Combobox
        store={combobox}
        onOptionSubmit={(val) => handleValueSelect(val)}
        withinPortal={false}
      >
        <Combobox.DropdownTarget>
          <p
            className={style||"font-semibold text-[#9C9C9C] w-full flex justify-center items-center cursor-pointer py-2 text-nowrap"}
            onClick={() => combobox.toggleDropdown()}
          >
            <CiFilter  className="mx-2 text-[#9E939A]" />
            Filter Status
          </p>
        </Combobox.DropdownTarget>

        <Combobox.Dropdown>
          <Combobox.Options>{options}</Combobox.Options>
        </Combobox.Dropdown>
      </Combobox>
    </div>
  );
}
