import React from "react";
import { LineChart } from "@mantine/charts";
import { Select } from "@mantine/core";

const TotalEmissions = () => {
  const data = [
    {
      date: "2012",
      carbon: 40000,
      Emissions: 40000,
    },
    {
      date: "2013",
      carbon: 38000,
      Emissions: 35000,
    },
    {
      date: "2014",
      carbon: 36000,
      Emissions: 42000,
    },
    {
      date: "2015",
      carbon: 34000,
      Emissions: 44000,
    },
    {
      date: "2016",
      carbon: 32000,
      Emissions: 40000,
    },
    {
      date: "2017",
      carbon: 30000,
      Emissions: 30000,
    },
    {
      date: "2018",
      carbon: 28000,
      Emissions: 20000,
    },
    {
      date: "2019",
      carbon: 26000,
      Emissions: 28000,
    },
    {
      date: "2020",
      carbon: 24000,
      Emissions: 35000,
    },
    {
      date: "2021",
      carbon: 22000,
      Emissions: 36000,
    },
    {
      date: "2022",
      carbon: 20000,
      Emissions: 30000,
    },
    {
      date: "2023",
      carbon: 18000,
      Emissions: 32000,
    },
    {
      date: "2024",
      carbon: 16000,
      Emissions: 35000,
    },
  ];

  return (
    <>
      <div className="flex items-center justify-between p-2 mb-4">
        <h3 className="text-2xl font-semibold">Total Emissions</h3>
        <Select
          classNames={{ label: "py-2", root: "rounded-lg focus:border-primary" }}
          // label="Categories"
          placeholder="Pick value"
          defaultValue="Yearly"
          data={["Yearly", "Year", "Years"]}
        />
      </div>

      <LineChart
        className="bg-white py-8 px-4 rounded-lg shadow-lg"
        h={300}
        data={data}
        dataKey="date"
        withDots={false}
        curveType="bump"
        // dotProps={{ r: 2 }}
        // activeDotProps={{ r: 3, strokeWidth: 1 }}
        series={[
        //   { name: "Apples", color: "indigo.6" },
          { name: "carbon", color: "teal.6", strokeDasharray: "5 5" },
          { name: "Emissions", color: "blue.6" },
        ]}
      />
    </>
  );
};

export default TotalEmissions;
