import ApiScrapping from "@/Api/apiSrapping";
import DeleteModal from "@/Components/Modals/DeleteModal";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

const UrlScrapping = () => {
  const [url, setUrl] = useState("");
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState(null);
  const [scrappedData, setScrappedData] = useState(null);
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      const response = await ApiScrapping.post(
        "/url/scrape",
        { url },
        {
          headers: { "Scraper-Type": "static" },
        }
      );
      setData(response.data.scraped_data);
      toast.success(response.data.message);
    } catch (error) {
    toast.error(error.response?.data?.message || "Failed to scrape URL");
    } finally {
      setLoading(false);
    }
  };
  const getScrappedData = async () => {
    try {
      setLoading(true);
      const response = await ApiScrapping.get("/url/get_scraped_data");
      setScrappedData(response.data.data);
    } catch (error) {
    toast.error(error.response?.data?.message || "Failed to scrape URL");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getScrappedData();
  }, [data]);



  return (
    <>
      <form
        className="mt-5 bg-white rounded-xl border-2 p-3"
        onSubmit={(e) => handleSubmit(e)}
      >
        <label className="text-[#494949] font-medium mb-2">Enter a URL</label>
        <div className="flex mb-4">
          <input
            name="scrape-url"
            id="scrape-url"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            type="text"
            className="border-2 border-r-0 outline-none border-[#E8E7EA] rounded-l-lg w-full p-2 pr-20"
            placeholder="https://example.com"
          />
          <button
            disabled={loading}
            type="submit"
            className="bg-primary h-[50px] flex items-center justify-center gap-2 text-white text-lg font-semibold rounded-r-lg px-12 py-2 border-1 border-l-0 border-primary hover:bg-opacity-80 transition duration-300"
          >
            Go
            {loading && <div className="submit-loader" />}
          </button>
        </div>
      </form>

      {data && (
        <div className="mt-8 bg-white rounded-xl border-2 p-6">
          <h2 className="text-xl font-semibold mb-4">Scraped Content</h2>
          <div className="pb-4 mb-4">
            <div>
              <span className="font-medium">URL: </span>
              <span>{data.url}</span>
            </div>
            <div>
              <span className="font-medium">Date Scraped: </span>
              <span>
                {data.extracted_at
                  ? new Date(data.extracted_at).toLocaleString(undefined, {
                      year: "numeric",
                      month: "short",
                      day: "numeric",
                      hour: "2-digit",
                      minute: "2-digit",
                    })
                  : "--"}
              </span>
            </div>
          </div>
          <div className="mb-4">
            <h3 className="font-semibold border-b mb-1">Summary</h3>
            <p className="text-gray-800">
              {data.summary || "No summary available."}
            </p>
          </div>
          <div className="mb-6 pt-2">
            <h3 className="font-semibold mb-1 border-b">Content</h3>
            <div>{data.content || "No content available."}</div>
          </div>
          {data.file_url && (
            <a
              href={data.file_url}
              download
              className="ml-auto w-fit block bg-primary text-white px-6 py-2 rounded font-semibold hover:bg-primary/80 transition"
            >
              Download
            </a>
          )}
        </div>
      )}

      {/* scrapped data */}

      {loading && (
    <div className="flex justify-center bg-primary/50 h-12 w-12 mx-auto items-center rounded-lg mt-8">
      <div className="submit-loader"></div>
    </div>)}
      {scrappedData && scrappedData.length > 0 && (
        <div className="relative mt-8 bg-white rounded-xl border-2 p-6">
          <h2 className="text-xl font-semibold mb-4">Scrapped Data</h2>
          <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
            {scrappedData.map((item, index) => (
              <div
                key={index}
                className="p-4 border rounded-lg shadow-sm bg-gray-50"
              >
                <div className="mb-2">
                  <span className="font-medium">URL: </span>
                  <span>{item.url}</span>
                </div>
                <div className="mb-2">
                  <span className="font-medium">Date Scraped: </span>
                  <span>
                    {item.extracted_at
                      ? new Date(item.extracted_at).toLocaleString(undefined, {
                          year: "numeric",
                          month: "short",
                          day: "numeric",
                          hour: "2-digit",
                          minute: "2-digit",
                        })
                      : "--"}
                  </span>
                </div>
                <div className="mb-2">
                  <span className="font-medium">Summary: </span>
                  <span>{item.summary || "No summary available."}</span>
                </div>
                <div className="mb-2">
                  <span className="font-medium">Content: </span>
                  <p>{item.content || "No content available."}</p>
                </div>

                <div className="flex flex-wrap items-center justify-between w-full">


          <DeleteModal 
          apiLink={'/url/delete_scraped_data'}
          sType={'scrapping'} ids={[item.id]} refreshFn={getScrappedData}  />
          {item?.file_url && (
      <a
        href={item?.file_url}
        download
        className="w-fit bg-primary text-white px-6 py-2 rounded font-semibold hover:bg-primary/80 transition"
      >
        Download
      </a>
    )}
                </div>

              </div>
            ))}
          </div>
          
        </div>
      )}
    </>
  );
};

export default UrlScrapping;
