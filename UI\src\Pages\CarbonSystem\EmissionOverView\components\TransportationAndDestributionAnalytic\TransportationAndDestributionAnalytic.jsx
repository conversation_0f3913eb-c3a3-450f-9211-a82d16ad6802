import { useState } from "react";
import UpstreamAnalytics from "./UpstreamAnalytics";

const TransportationAndDestributionAnalytic = () => {
  const [activeSubTab, setActiveSubTab] = useState("Upestream");

  return (
    <>
      <div
        className="grid lg:grid-cols-2 lg:gap-10 p-6 py-3 gap-5 lg:text-xl mb-5 bg-white rounded-lg border-2"
        role="tablist"
      >
        <button
          className={`p-2 rounded-xl flex items-center justify-center gap-2 font-semibold transition-colors duration-200
                    ${
                      "Upestream" === activeSubTab
                        ? "bg-primary text-white"
                        : "hover:bg-gray-100 border-2"
                    }
                    `}
          onClick={() => setActiveSubTab("Upestream")}
          role="tab"
          aria-selected={"Upestream" === activeSubTab}
          aria-controls={`tabpanel-Upestream`}
        >
          Upestream
        </button>
        <button
          className={`p-2 rounded-xl flex items-center justify-center gap-2 font-semibold transition-colors duration-200
                    ${
                      "Downstream" === activeSubTab
                        ? "bg-primary text-white"
                        : "hover:bg-gray-100 border-2"
                    }
                    `}
          onClick={() => setActiveSubTab("Downstream")}
          role="tab"
          aria-selected={"Downstream" === activeSubTab}
          aria-controls={`tabpanel-Downstream`}
        >
          Downstream
        </button>
      </div>

      <UpstreamAnalytics />
      {/* <DownstreamAnalytics /> */}
    </>
  );
};

export default TransportationAndDestributionAnalytic;
