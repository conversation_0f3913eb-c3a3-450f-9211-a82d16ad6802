import Api_PCAF from '@/Api/apiS2_PCAF';
import { useAutoLoanStore } from '@/Store/useAutoLoanStore';
import { Spinner } from '@react-pdf-viewer/core';
import React from 'react'
const PortfolioBreakdown = () => {
    const {
        loan_amount,
        total_vehicle_value,
        vehicle_make_model,
        vehicle_year,
        fuel_type,
        loan_term,
        annual_mileage,
        data_quality_score,
        fuel_efficiency,
        grid_emission_factor,
        
        setLoanAmount,
        setTotalVehicleValue,
        setVehicleMakeModel,
        setVehicleYear,
        setFuelType,
        setLoanTerm,
        setAnnualMileage,
        setDataQualityScore,
        setFuelEfficiency,
        setGridEmissionFactor,
        setResults,

        loading,
        setLoading
      } = useAutoLoanStore();
  
    const handleCalculate = async () => {
        setLoading(true);
        Api_PCAF.post("/motor-vehicle-loans", {
            "loan_amount": loan_amount,  // $25,000
            "total_vehicle_value": total_vehicle_value,  // $30,000
            "vehicle_lifecycle_emissions": 50.0,  // 50 tCO₂e (lifecycle emissions of the vehicle)
            "vehicle_model": vehicle_make_model,
            "vehicle_year": vehicle_year,
            "fuel_type": fuel_type.toLowerCase(),
            "fuel_efficiency": fuel_efficiency || 0,
            "grid_emission_factor": grid_emission_factor || 0,
            "loan_term": parseInt(loan_term),  // 60 months (5 years)
            "annual_mileage": annual_mileage,  // 15,000 km/year
            "data_quality_score": data_quality_score
        }).then((res) => {
            setLoading(false);
            const financedEmissions = res.data;
            setResults(financedEmissions);
        }).catch((error) => {
            setLoading(false);
        });
    };

  return (
    <div>
        <div className='flex flex-col gap-4 rounded-xl bg-white text-[#8F8F8F] dark:bg-[#1E1E1E] dark:text-white p-4 border-[#E8E7EA] border-2'>
            <h1 className='flex justify-center text-center bg-[#E6F3F4] text-[#8F8F8F] dark:bg-[#1E1E1E]  dark:text-white p-2 rounded-xl'>
            Financed Emissions = (Loan Amount / Vehicle Value) + (Manufacturing + Use Phase + End-of-Life Emissions)
            </h1>
            <table className="w-full">
            <thead className="bg-[#F5F4F5] ">
                <tr className='font-bold'>
                <th className="text-start p-3 ">Parameter</th>
                <th className="text-start p-3 ">Value</th>
                <th className="text-start p-3 ">AI Assistant</th>
                </tr>
            </thead>
            <tbody>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Loan Amount</td>
                    <td className="p-3 border border-gray-300">
                        <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="e.g.,25,000" onChange={(e) => setLoanAmount(e.target.value)} value={loan_amount}/>
                    </td>
                    <td className="p-3 border border-gray-300">Required Input</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Total Vehicle Value</td>
                    <td className="p-3 border border-gray-300">
                        <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="e.g.,25,000" onChange={(e) => setTotalVehicleValue(e.target.value)} value={total_vehicle_value}/>
                    </td>
                    <td className="p-3 border border-gray-300">Required Input</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Vehicle Make/Model</td>
                    <td className="p-3 border border-gray-300">
                        <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="e.g.Toyota" onChange={(e) => setVehicleMakeModel(e.target.value)} value={vehicle_make_model}/>
                    </td>
                    <td className="p-3 border border-gray-300">Required</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Vehicle Year</td>
                    <td className="p-3 border border-gray-300">
                        <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="e.g. 2025" onChange={(e) => setVehicleYear(e.target.value)} value={vehicle_year}/>
                    </td>
                    <td className="p-3 border border-gray-300">Required</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Fuel Type</td>
                    <td className="p-3 border border-gray-300">
                        <select type="text" className="border border-gray-400 rounded-lg p-2 w-full" onChange={(e) => setFuelType(e.target.value)} value={fuel_type}>
                            <option value="Electric">Electric</option>
                            <option value="Hybrid" className='text-gray-400'>Hybrid</option>
                            {/* <option value="Petrol">Petrol</option> */}
                            <option value="Diesel">Diesel</option>
                            <option value="Gasoline">Gasoline</option>
                            {/* <option value="CNG">CNG</option> */}
                        </select>
                    </td>
                    <td className="p-3 border border-gray-300">Required</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Loan Term (years)</td>
                    <td className="p-3 border border-gray-300">
                        <select type="text" className="border border-gray-400 rounded-lg p-2 w-full" onChange={(e) => setLoanTerm(e.target.value)} value={loan_term}>
                            <option value="5" className='text-gray-400'>5</option>
                            <option value="4">4</option>
                            <option value="3">3</option>
                        </select>
                    </td>
                    <td className="p-3 border border-gray-300">Required Input</td>
                </tr>
                {
                    fuel_type === "Electric" && (
                        <tr className="border border-gray-300">
                            <td className="p-3 font-medium border border-gray-300">Grid Emission Factor (kgCO2e/kWh)</td>
                                <td className="p-3 border border-gray-300">
                                    <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="e.g.,15,000" onChange={(e) => setGridEmissionFactor(e.target.value)} value={grid_emission_factor}/>
                                </td>
                            <td className="p-3 border border-gray-300">Required Input</td>
                        </tr>
                    )
                }
                {
                    fuel_type != "Electric" && (
                        <tr className="border border-gray-300">
                            <td className="p-3 font-medium border border-gray-300">Fuel Efficiency (km/L)</td>
                            <td className="p-3 border border-gray-300">
                                <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="e.g.,15,000" onChange={(e) => setFuelEfficiency(e.target.value)} value={fuel_efficiency}/>
                            </td>
                            <td className="p-3 border border-gray-300">Required Input</td>
                        </tr>
                    )
                }
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Annual Mileage (km/year)</td>
                    <td className="p-3 border border-gray-300">
                        <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="e.g.,15,000" onChange={(e) => setAnnualMileage(e.target.value)} value={annual_mileage}/>
                    </td>
                    <td className="p-3 border border-gray-300">Required Input</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Data Quality Score</td>
                    <td className="p-3 border border-gray-300">
                        <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="2 - Manufacturer data + usage estimate Number" onChange={(e) => setDataQualityScore(e.target.value)} value={data_quality_score}/>
                    </td>
                    <td className="p-3 border border-gray-300">Auto-updates</td>
                </tr>
            </tbody>
            </table>

            <button disabled={loading} className='flex w-full justify-center text-center text-base font-semibold bg-[#07838F] text-[#ffffff] p-2 rounded-lg' onClick={handleCalculate}>
                {
                    loading ? <Spinner size='24px' /> : "Calculate Financed Emissions"
                }
                
            </button>
        </div>
    </div>
  )
}

export default PortfolioBreakdown