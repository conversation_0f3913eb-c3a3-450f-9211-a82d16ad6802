import useSideBarRoute from "@/hooks/useSideBarRoute";
import MainLayout from "@/Layout/MainLayout";
import { Link, useLocation, useParams } from "react-router-dom";
import useRoutes from "@/Routes/useRoutes";
import { useTranslation } from "react-i18next";
import { useCallback, useEffect, useState } from "react";
import { IconPlus } from "@tabler/icons-react";
import { Modal, Button, TextInput, FileInput, Progress } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import ApiS3 from "@/Api/apiS3";
import { toast } from "react-toastify";

export default function CourseDetails() {
  const { t } = useTranslation();
  const { GreenHubMenu } = useSideBarRoute();
  const {
    GreenHubResourcesDashboard,
    GreenHubAcademyDashboard,
    GreenHubCommunityDashboard,
  } = useRoutes().GreenHubMap;
  const location = useLocation();
  const { courseId } = useParams();

  const [data, setData] = useState({});
  const [opened, { open, close }] = useDisclosure(false); // Modal state
  const [sectionName, setSectionName] = useState(""); // Section name input
  const [lectures, setLectures] = useState([
    { lectureName: "", lectureVideo: null },
  ]); // Lectures (name and video) input
  const [expandedSection, setExpandedSection] = useState(null); // Track expanded section
  const [uploadProgress, setUploadProgress] = useState(0); // Track upload progress
  const [isUploading, setIsUploading] = useState(false); // Track if upload is in progress

  // Fetch course details
  const getCourseDetails = useCallback(
    async function getCourseDetails() {
      try {
        const response = await ApiS3.get(`/academy/dashboard/${courseId}`);
        if (response) {
          setData(response.data);
          //console.log(response.data);
        }
      } catch (err) {
        console.error("Error fetching course details:", err);
      }
    },
    [courseId]
  );

  // Add a new section
  const addSection = async () => {
    setIsUploading(true); // Start uploading
    setUploadProgress(0); // Reset progress
    try {
      const formData = new FormData();
      formData.append("sectionName", sectionName);

      // Append each lecture name and video
      let lecturesNames = [];
      lectures.forEach((lecture) => {
        lecturesNames.push(lecture.lectureName);
      });
      formData.append("lecturesNames", JSON.stringify(lecturesNames));
      lectures.forEach((lecture) => {
        formData.append(`lectures`, lecture.lectureVideo);
      });

      const response = await ApiS3.post(
        `/academy/add-section/${courseId}`,
        formData,
        {
          onUploadProgress: (progressEvent) => {
            const progress = Math.round(
              (progressEvent.loaded / progressEvent.total) * 100
            );
            setUploadProgress(progress); // Update progress
          },
        }
      );

      // console.log("Raw Response:", response); // Log the raw response

      // // Check if the response is JSON
      // const contentType = response.headers["content-type"];
      // if (contentType && contentType.includes("application/json")) {
      //   const jsonResponse = await response.json();
      //   //console.log("JSON Response:", jsonResponse);
      // } else {
      //   const textResponse = await response.text();
      //   //console.log("Text Response:", textResponse);
      //   // Handle non-JSON response here
      // }

      if (response.status === 201) {
        close(); // Close the modal
        getCourseDetails(); // Refetch course details
        toast.success(response?.data?.message);
      }
    } catch (err) {
      console.error("Error adding section:", err);
    } finally {
      setIsUploading(false); // Stop uploading
    }
  };
  // Toggle lectures visibility
  const toggleLectures = (sectionId) => {
    setExpandedSection(expandedSection === sectionId ? null : sectionId);
  };

  // Handle adding a new lecture field
  const addLectureField = () => {
    setLectures([...lectures, { lectureName: "", lectureVideo: null }]);
  };

  // Handle lecture name change
  const handleLectureNameChange = (index, value) => {
    const updatedLectures = [...lectures];
    updatedLectures[index].lectureName = value;
    setLectures(updatedLectures);
  };

  // Handle lecture video change
  const handleLectureVideoChange = (index, file) => {
    const updatedLectures = [...lectures];
    updatedLectures[index].lectureVideo = file;
    setLectures(updatedLectures);
  };

  useEffect(() => {
    getCourseDetails();
  }, [getCourseDetails]);

  return (
    <MainLayout menus={GreenHubMenu} navbarTitle={t("Green Hub")}>
      <div className="overflow-y-auto">
        {/* Header */}
        <h1 className="px-8 py-4 text-xl font-bold bg-white rounded-2xl text-blue-950 mb-7">
          {t("Manage Green Hub Resources")}
        </h1>

        {/* Navigation Links */}
        <div className="px-8 py-4 bg-white rounded-2xl text-blue-950 mb-7 flex justify-around">
          {[
            {
              to: GreenHubAcademyDashboard.path,
              label: t("GreenHub Academy"),
              key: "academy",
            },
            {
              to: GreenHubResourcesDashboard.path,
              label: t("GreenHub Resources"),
              key: "resourses",
            },
            {
              to: GreenHubCommunityDashboard.path,
              label: t("GreenHub Peers Community"),
              key: "community",
            },
          ].map(({ to, label, key }) => {
            const isActive = location.pathname.split("/")[2] === key;
            const baseClasses =
              "lg:p-3 w-[200px] lg:w-[400px] text-center rounded-xl shadow-xl lg:text-[24px] font-bold";
            const activeClasses = isActive
              ? "bg-[#05808b] text-white"
              : "bg-[#07838F33] text-[#05808b]";
            return (
              <DashboardLink
                key={key}
                to={to}
                label={label}
                className={`${baseClasses} ${activeClasses}`}
              />
            );
          })}
        </div>

        {/* Course Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 p-8">
          {/* Course Image and Description */}
          <div className="courseDetails flex flex-col gap-5">
            <img
              src={data.courseImage}
              alt="Course Cover"
              className="w-full max-w-full h-auto rounded-lg shadow-md"
            />
            <p className="text-gray-600 text-lg text-justify">
              {data.description}
            </p>
          </div>

          {/* Sections and Lectures */}
          <div className="sections">
            <h1 className="text-2xl font-bold text-[#05808b] mb-6">
              {data.courseName} Sections
            </h1>

            {/* Add Section Button */}
            <Button
              onClick={open}
              className="bg-[#05808b] hover:bg-[#05808b] mb-6"
              leftSection={<IconPlus size={20} />}
            >
              Add Section
            </Button>

            {/* Sections List */}
            {data?.sections?.map((section) => (
              <div
                key={section._id}
                className="mb-6 bg-white rounded-lg shadow-md p-4 cursor-pointer"
                onClick={() => toggleLectures(section._id)}
              >
                <h2 className="text-xl font-semibold mb-3">
                  {section.sectionName}
                </h2>
                {/* Toggle Lectures */}
                {expandedSection === section._id && (
                  <div className="lectures flex flex-wrap gap-3">
                    {section?.lectures.map((lecture) => (
                      <span
                        key={lecture._id}
                        className="bg-[#05808b33] text-[#05808b] px-3 py-1 rounded-full"
                      >
                        {lecture.lectureName}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Add Section Modal */}
        <Modal
          opened={opened}
          onClose={close}
          title="Add New Section"
          size="lg"
        >
          <div className="space-y-4">
            {/* Section Name Input */}
            <TextInput
              label="Section Name"
              placeholder="Enter section name"
              value={sectionName}
              onChange={(e) => setSectionName(e.target.value)}
            />

            {/* Dynamic Lecture Fields */}
            {lectures.map((lecture, index) => (
              <div key={index} className="space-y-2">
                <TextInput
                  label={`Lecture ${index + 1} Name`}
                  placeholder="Enter lecture name"
                  value={lecture.lectureName}
                  onChange={(e) =>
                    handleLectureNameChange(index, e.target.value)
                  }
                />
                <FileInput
                  label={`Lecture ${index + 1} Video`}
                  placeholder="Upload lecture video"
                  accept="video/*"
                  onChange={(file) => handleLectureVideoChange(index, file)}
                />
              </div>
            ))}

            {/* Add More Lectures Button */}
            <Button
              onClick={addLectureField}
              className="bg-[#05808b] hover:bg-[#05808b] mt-2"
              leftSection={<IconPlus size={16} />}
            >
              Add More Lectures
            </Button>

            {/* Progress Bar */}
            {isUploading && (
              <Progress value={uploadProgress} label={`${uploadProgress}%`} />
            )}

            {/* Submit Button */}
            <Button
              onClick={addSection}
              className="bg-[#05808b] hover:bg-[#05808b] mt-4"
              disabled={isUploading} // Disable button while uploading
            >
              {isUploading ? "Uploading..." : "Add Section"}
            </Button>
          </div>
        </Modal>
      </div>
    </MainLayout>
  );
}

// Component for Navigation Links
function DashboardLink({ to, label, className }) {
  return (
    <Link to={to} className={className}>
      {label}
    </Link>
  );
}
