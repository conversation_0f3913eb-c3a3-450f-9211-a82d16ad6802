import React from "react";
import S3Layout from "@/Layout/S3Layout";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import MainCard from "./Partials/MainCard";
import firstImg from "@/assets/images/2c89ca2e8c40bb2d4c0ad0f932f1c2fc.png";
import secImg from "@/assets/images/16e9ff51601a31dd3a22146ae9d741e3.png";
import thirdImg from "@/assets/images/f7e2894ad79ac7679fc91bda0b84716a.png";
import { IoMdHome } from "react-icons/io";

export default function NonFinancialServicesStart() {
  const { greenShieldMenu } = useSideBarRoute();
  return (
    <>
      <S3Layout
        menus={greenShieldMenu}
        navbarTitle="GreenShield"
        breadcrumbItems={[
          { title: <IoMdHome size={20}/>, href: "/get-started" },
          { title: "GreenShield", href: "/green-shield/general" },
          {
            title: "Non-Financial Services",
            href: "/green-shield/nonFinancial-services-start",
          },
        ]}
      >
        <div className="grid grid-cols-1  md:grid-cols-2 xl:grid-cols-3 gap-10">
          <div className="w-full rounded-lg overflow-hidden">
            <MainCard
              link={"/green-shield/compilance/CompilanceView"}
              topColor={"bg-[#ade5de]"}
              bodyColor={"bg-[#00c0a9]"}
              borderColor={"border-[#00c0a9]"}
              img={firstImg}
              headTittle={"Anti-GreenWashing Compliance Assessment"}
              mt={"mt-10"}
            />
          </div>

          <div className="w-full rounded-lg overflow-hidden">
            <MainCard
              link={"/green-shield/financial/ESG-risk-management"}
              topColor={"bg-[#b7c4ce]"}
              bodyColor={"bg-[#205374]"}
              borderColor={"border-[#205374]"}
              img={secImg}
              headTittle={"ESG Risk Management"}
              mt={"mt-10"}
            />
          </div>

          <div className="w-full rounded-lg overflow-hidden">
            <MainCard
              link={"/green-shield/Financial/ESG-incident-management"}
              topColor={"bg-[#cfeac8]"}
              bodyColor={"bg-[#70d162]"}
              borderColor={"border-[#70d162]"}
              img={thirdImg}
              headTittle={
                <span>
                  Stakeholder Engagement
                  <br />{" "}
                  <p className="text-lg">
                    ESG Opportunities and <br />
                    Incident Management
                  </p>
                </span>
              }
              mt={"mt-10"}
            />
          </div>
        </div>
      </S3Layout>
    </>
  );
}
