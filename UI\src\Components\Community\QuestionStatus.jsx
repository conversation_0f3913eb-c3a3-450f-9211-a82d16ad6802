import { useTranslation } from 'react-i18next';

const QuestionStatus = ({ votes, answers }) => {
  const { t } = useTranslation();

  return (
    <div>
      <span>
        {votes?.upvoteCount || 0} {t('likes')}
      </span>
      <span className="mx-2">•</span>
      <span>
        {answers?.length || 0} {t('CommunityPost.answers')}
      </span>
    </div>
  );
};

export default QuestionStatus;
