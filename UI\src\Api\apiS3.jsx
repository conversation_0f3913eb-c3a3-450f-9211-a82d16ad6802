import axios from "axios";
import Cookies from "js-cookie";

const baseURL = "https://portals3-staging-dwdwc5d6dnhyhpbb.uksouth-01.azurewebsites.net";
  // const baseURL =  "https://portals3-dwdwc5d6dnhyhpbb.uksouth-01.azurewebsites.net";

// const baseURL = "http://localhost:3000";
const ApiS3 = axios.create({
  baseURL,
  withCredentials: true, // Send cookies with requests
});

ApiS3.interceptors.request.use(
  (config) => {
    const token = Cookies.get("level_user_token");
    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

ApiS3.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response && error.response.status === 401) {
      const errorMessage = error.response.data.message.toLowerCase();
      const currentPath = window.location.pathname;

      if (
        (errorMessage.includes("token is expired") ||
          errorMessage.includes("token is invalid")) &&
        currentPath !== "/login"
      ) {
        console.log("Token is invalid. Redirecting to login...");
        Cookies.remove("level_user_token");
        localStorage.clear();
        window.location.href = "/login"; // Redirect to login page
      }
    }
    return Promise.reject(error);
  }
);

export default ApiS3;
