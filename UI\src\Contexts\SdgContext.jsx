import React, { createContext, useContext, useEffect, useState } from "react";
import ApiS1Config from "@/Api/apiS1Config";
import { useNavigate } from "react-router";
import { notifications } from "@mantine/notifications";
import { FaCheckCircle } from "react-icons/fa";

const SdgContext = createContext();

export const SdgProvider = ({ children }) => {
  const [sdgAlignment, setSdgAlignment] = useState(null);
  const [sdgImpact, setSdgImpact] = useState(null);
  const [sdgData, setSdgData] = useState(null);
  const [PostAnswersState, setPostAnswersState] = useState(null);
  const [postLoading, setPostLoading] = useState(false);
  const [reportLoading, setReportLoading] = useState(false);
  const navigate = useNavigate();

  const PriorityLevels = {
    1: "Low",
    2: "Medium",
    3: "High",
    4: "Top",
  };

  const SdgWeights = {
    1: "Low",
    2: "Medium",
    3: "High",
    4: "Critical",
  };

  const ReadinessLevel = {
    1: "Not Started",
    2: "Initial Planning",
    3: "In Development",
    4: "Partially Implemented",
    5: "Fully Implemented",
    6: "Not Applicable",
  };

  const readinessColorMap = {
    "Not Started": {
      bg: "#AB020233",
      border: "#AB0202",
      text: "#AB0202",
    },
    "Not Applicable": {
      bg: "#e0e2e7",
      border: "#667085",
      text: "#667085",
    },
    "Initial Planning": {
      bg: "#e9dff3",
      border: "#9160C1",
      text: "#9160C1",
    },
    "In Development": {
      bg: "#ffeecd",
      border: "#FFAB07",
      text: "#FFAB07",
    },
    "Partially Implemented": {
      bg: "#d4e8fb",
      border: "#298BED",
      text: "#298BED",
    },
    "Fully Implemented": {
      bg: "#e2f6e7",
      border: "#01BD36",
      text: "#01BD36",
    },
  };

  const prioritySelectColorMap = {
    Top: {
      bg: "#AB02024D",
      border: "",
      text: "#AB0202",
    },
    High: {
      bg: "#FF60074D",
      border: "#FF6007",
      text: "#FF6007",
    },
    Medium: {
      bg: "#FFAB074D",
      border: "#FFAB07",
      text: "#FFAB07",
    },
    Low: {
      bg: "#01BD3638",
      border: "#01BD36",
      text: "#01BD36",
    },
  };

  const SdgWeightColorMap = {
    Critical: {
      bg: "#AB02024D",
      border: "",
      text: "#AB0202",
    },
    High: {
      bg: "#FF60074D",
      border: "#FF6007",
      text: "#FF6007",
    },
    Medium: {
      bg: "#FFAB074D",
      border: "#FFAB07",
      text: "#FFAB07",
    },
    Low: {
      bg: "#01BD3638",
      border: "#01BD36",
      text: "#01BD36",
    },
  };

  const getSDGData = async () => {
    try {
      const { data } = await ApiS1Config.get("/get_assessment_data", {
        headers: {
          assessmentName: "SDG Assessment",
        },
      });
      setSdgData(data);
      setSdgImpact(data["SDG Impact Measurement"]);
      setSdgAlignment(data["SDG Alignment Assessment"]);
    } catch (error) {
      if (error.response.data.Message === "Company assessment not found") {
        navigate("/select-assessment");
      }
      //console.log(error);
    }
  };

  useEffect(() => {
    getSDGData();
  }, []);

  const postAnswers = async (extraData, assessmentName, categoryName) => {
    setPostLoading(true);
    try {
      const { data } = await ApiS1Config.post(`/post_scope`, extraData, {
        headers: {
          assessmentName,
          categoryName,
        },
      });
      setPostLoading(false);
      //console.log(data);
      notifications.show({
        title: "Updated successfully!",
        color: "green",
        icon: <FaCheckCircle />,
      });
      setPostAnswersState(false);
      getSDGData();
    } catch (error) {
      setPostLoading(false);
      //console.log(error);
      notifications.show({
        title: error.response?.data?.Message || "An error occurred",
        color: "red",
      });
    }
  };

  const getReport = async () => {
    setReportLoading(true);
    try {
      setReportLoading(true);
      const { data } = await ApiS1Config.post(
        "/get_report",
        {},
        {
          headers: {
            assessmentType: "SDG Assessment",
          },
        }
      );
      setReportLoading(false);
      navigate("/sdg-report");
      //console.log(data);
    } catch (error) {
      setReportLoading(false);
      //console.log(error);
    }
  };

  return (
    <SdgContext.Provider
      value={{
        sdgAlignment,
        sdgImpact,
        PostAnswersState,
        setPostAnswersState,
        postLoading,
        PriorityLevels,
        prioritySelectColorMap,
        SdgWeights,
        SdgWeightColorMap,
        getSDGData,
        postAnswers,
        ReadinessLevel,
        readinessColorMap,
        reportLoading,
        getReport,
        sdgData,
      }}
    >
      {children}
    </SdgContext.Provider>
  );
};
export const useSdg = () => useContext(SdgContext);

export default SdgProvider;
