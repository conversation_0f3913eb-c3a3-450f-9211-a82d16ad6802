import { useState, useEffect } from "react";
import {
  Table,
  TextInput,
  Select,
  Button,
  Badge,
  Group,
  Text,
} from "@mantine/core";
import { IconTrash, IconPlus } from "@tabler/icons-react";
import { FaRegCheckCircle } from "react-icons/fa";
import Cookies from "js-cookie";
import { toast } from "react-toastify";
import Quantitative from "@/data/Quantitative";
import Qualitative from "@/data/Qualitative";

const Performance = ({
  topicId,
  quantitative_metrics,
  qualitative_metrics,
}) => {
  // State for quantitative metrics
  const [quantitativeData, setQuantitativeData] = useState([]);

  // State for qualitative metrics
  const [qualitativeData, setQualitativeData] = useState([]);

  const baseUrl = "https://issb-report-api-staging.azurewebsites.net";

  // Initialize quantitativeData from quantitative_metrics
  useEffect(() => {
    if (quantitative_metrics && quantitative_metrics.length > 0) {
      setQuantitativeData(
        quantitative_metrics.map((metric) => ({
          id: metric.id,
          metric: metric.metric_name,
          current: metric.current_value.toString(),
          target: metric.target_value.toString(),
          status: metric.status,
          submitted: true,
        }))
      );
    }
  }, [quantitative_metrics]);

  // Initialize qualitativeData from qualitative_metrics
  useEffect(() => {
    if (qualitative_metrics && qualitative_metrics.length > 0) {
      setQualitativeData(
        qualitative_metrics.map((metric) => ({
          id: metric.id,
          metric: metric.metric_name,
          current: metric.current_value,
          target: metric.target_value,
          status: metric.status,
          submitted: true,
        }))
      );
    }
  }, [qualitative_metrics]);

  // --- Quantitative Metric Handlers ---

  // Add a new row to quantitative table
  const addQuantitativeRow = () => {
    setQuantitativeData([
      ...quantitativeData,
      { metric: "", current: "", target: "", status: "", submitted: false },
    ]);
  };

  // Delete a row from quantitative table
  const deleteQuantitativeRow = async (index) => {
    const row = quantitativeData[index];
    if (row.id) {
      const token = Cookies.get("level_user_token");
      if (!token) {
        toast.error("Authorization token is missing");
        return;
      }
      try {
        const response = await fetch(
          `${baseUrl}/api/v1/topics/${topicId}/quantitative_metrics/${row.id}`,
          {
            method: "DELETE",
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (!response.ok) {
          throw new Error(
            `Failed to delete quantitative metric: ${response.statusText}`
          );
        }
      } catch (error) {
        console.error("Error deleting quantitative metric:", error);
        toast.error(`Failed to delete metric: ${error.message}`);
        return;
      }
    }
    setQuantitativeData(quantitativeData.filter((_, i) => i !== index));
  };

  // Update a field in quantitative table
  const updateQuantitativeRow = (index, field, value) => {
    const updatedData = [...quantitativeData];
    updatedData[index][field] = value;
    // Reset submitted status if row is modified
    updatedData[index].submitted = false;
    setQuantitativeData(updatedData);
  };

  // Submit or update quantitative metric to API
  const submitQuantitativeMetric = async (index) => {
    const row = quantitativeData[index];
    if (!row.metric) {
      toast.error("Please select a metric");
      return;
    }
    const currentValue = parseFloat(row.current);
    const targetValue = parseFloat(row.target);
    if (isNaN(currentValue) || isNaN(targetValue)) {
      toast.error("Current and target values must be numbers");
      return;
    }
    const token = Cookies.get("level_user_token");
    if (!token) {
      toast.error("Authorization token is missing");
      return;
    }
    try {
      let response;
      if (row.id) {
        const payload = {};
        if (row.current !== "") payload.current_value = currentValue;
        if (row.target !== "") payload.target_value = targetValue;
        if (Object.keys(payload).length === 0) {
          toast.error("No changes to submit");
          return;
        }
        response = await fetch(
          `${baseUrl}/api/v1/topics/${topicId}/quantitative_metrics/${row.id}`,
          {
            method: "PATCH",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify(payload),
          }
        );
        if (!response.ok) {
          throw new Error(
            `Failed to update quantitative metric: ${response.statusText}`
          );
        }
      } else {
        response = await fetch(
          `${baseUrl}/api/v1/topics/${topicId}/quantitative_metrics`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify({
              metric_name: row.metric,
              current_value: currentValue,
              target_value: targetValue,
            }),
          }
        );
        if (!response.ok) {
          throw new Error(
            `Failed to submit quantitative metric: ${response.statusText}`
          );
        }
      }

      // Parse the response and update quantitativeData
      const data = await response.json();
      if (data?.topic?.quantitative_metrics) {
        setQuantitativeData(
          data.topic.quantitative_metrics.map((metric) => ({
            id: metric.id,
            metric: metric.metric_name,
            current: metric.current_value.toString(),
            target: metric.target_value.toString(),
            status: metric.status,
            submitted: true,
          }))
        );
      } else {
        console.error("No quantitative metrics found in response:", data);
        toast.error("Failed to update table with response data");
      }
    } catch (error) {
      console.error("Error submitting/updating quantitative metric:", error);
      toast.error(`Failed to process metric: ${error.message}`);
    }
  };

  // --- Qualitative Metric Handlers ---

  // Add a new row to qualitative table
  const addQualitativeRow = () => {
    setQualitativeData([
      ...qualitativeData,
      { metric: "", current: "", target: "", status: "", submitted: false },
    ]);
  };

  // Delete a row from qualitative table
  const deleteQualitativeRow = async (index) => {
    const row = qualitativeData[index];
    if (row.id) {
      const token = Cookies.get("level_user_token");
      if (!token) {
        toast.error("Authorization token is missing");
        return;
      }
      try {
        const response = await fetch(
          `${baseUrl}/api/v1/topics/${topicId}/qualitative-metrics/${row.id}`,
          {
            method: "DELETE",
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (!response.ok) {
          throw new Error(
            `Failed to delete qualitative metric: ${response.statusText}`
          );
        }
      } catch (error) {
        console.error("Error deleting qualitative metric:", error);
        toast.error(`Failed to delete metric: ${error.message}`);
        return;
      }
    }
    setQualitativeData(qualitativeData.filter((_, i) => i !== index));
  };

  // Update a field in qualitative table
  const updateQualitativeRow = (index, field, value) => {
    const updatedData = [...qualitativeData];
    updatedData[index][field] =
      field === "current" || field === "target" ? value : value;
    // Reset submitted status if row is modified
    updatedData[index].submitted = false;
    setQualitativeData(updatedData);
  };

  // Submit or update qualitative metric to API
  const submitQualitativeMetric = async (index) => {
    const row = qualitativeData[index];
    if (!row.metric || row.current === "" || row.target === "" || !row.status) {
      toast.error("Please select all fields (metric, current, target, status)");
      return;
    }
    const token = Cookies.get("level_user_token");
    if (!token) {
      toast.error("Authorization token is missing");
      return;
    }
    try {
      let response;
      if (row.id) {
        const payload = {};
        if (row.current !== "") payload.current_value = row.current;
        if (row.target !== "") payload.target_value = row.target;
        if (row.status) payload.status = row.status;
        if (Object.keys(payload).length === 0) {
          toast.error("No changes to submit");
          return;
        }
        response = await fetch(
          `${baseUrl}/api/v1/topics/${topicId}/qualitative-metrics/${row.id}`,
          {
            method: "PATCH",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify(payload),
          }
        );
        if (!response.ok) {
          throw new Error(
            `Failed to update qualitative metric: ${response.statusText}`
          );
        }
      } else {
        response = await fetch(
          `${baseUrl}/api/v1/topics/${topicId}/qualitative-metrics`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify({
              metric_name: row.metric,
              current_value: row.current,
              target_value: row.target,
              status: row.status,
            }),
          }
        );
        if (!response.ok) {
          throw new Error(
            `Failed to submit qualitative metric: ${response.statusText}`
          );
        }
      }

      // Parse the response and update qualitativeData
      const data = await response.json();
      if (data?.topic?.qualitative_metrics) {
        setQualitativeData(
          data.topic.qualitative_metrics.map((metric) => ({
            id: metric.id,
            metric: metric.metric_name,
            current: metric.current_value,
            target: metric.target_value,
            status: metric.status,
            submitted: true,
          }))
        );
      } else {
        console.error("No qualitative metrics found in response:", data);
        toast.error("Failed to update table with response data");
      }
    } catch (error) {
      console.error("Error submitting/updating qualitative metric:", error);
      toast.error(`Failed to process metric: ${error.message}`);
    }
  };

  // Get measurement values for a given metric
  const getMeasurementValues = (metric) => {
    const item = Qualitative.find((q) => q.name === metric);
    return item ? item.measurement_values : [];
  };

  // Convert measurement values to Select-compatible format
  const formatSelectData = (values) => {
    return values.map((value) => ({
      value: value.toString(),
      label: value.toString(),
      original: value,
    }));
  };

  return (
    <div className="mt-4">
      <h2 className="font-bold mb-2">Performance Tracking</h2>
      {/* Quantitative Metric Table */}
      <div className="mb-6 w-full justify-center items-center">
        <div className="overflow-x-auto">
          <Table
            verticalSpacing="sm"
            horizontalSpacing="md"
            withTableBorder
            withColumnBorders
            highlightOnHover
            className="min-w-[1300px] w-full"
          >
            <Table.Thead>
              <Table.Tr>
                <Table.Th style={{ width: "24%" }}>
                  Quantitative Metric
                </Table.Th>
                <Table.Th style={{ width: "23%" }}>Current</Table.Th>
                <Table.Th style={{ width: "23%" }}>Target</Table.Th>
                <Table.Th style={{ width: "15%" }}>Status</Table.Th>
                <Table.Th style={{ width: "15%" }}>Actions</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {quantitativeData.map((row, index) => (
                <Table.Tr key={row.id || index}>
                  <Table.Td>
                    <Select
                      data={Quantitative.map((item) => item.name)}
                      value={row.metric}
                      onChange={(value) =>
                        updateQuantitativeRow(index, "metric", value)
                      }
                      searchable
                      placeholder="Select a metric"
                    />
                  </Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <TextInput
                        value={row.current}
                        onChange={(event) =>
                          updateQuantitativeRow(
                            index,
                            "current",
                            event.currentTarget.value
                          )
                        }
                        placeholder="Enter current value"
                        style={{ flex: 1 }}
                      />
                      <Text size="sm">
                        {Quantitative.find((item) => item.name === row.metric)
                          ?.unit ||
                          quantitative_metrics?.find(
                            (m) => m.metric_name === row.metric
                          )?.metric_unit ||
                          ""}
                      </Text>
                    </Group>
                  </Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <TextInput
                        value={row.target}
                        onChange={(event) =>
                          updateQuantitativeRow(
                            index,
                            "target",
                            event.currentTarget.value
                          )
                        }
                        placeholder="Enter target value"
                        style={{ flex: 1 }}
                      />
                      <Text size="sm">
                        {Quantitative.find((item) => item.name === row.metric)
                          ?.unit ||
                          quantitative_metrics?.find(
                            (m) => m.metric_name === row.metric
                          )?.metric_unit ||
                          ""}
                      </Text>
                    </Group>
                  </Table.Td>
                  <Table.Td>
                    {row.status && (
                      <Badge
                        color={
                          row.status === "On Track"
                            ? "green"
                            : row.status === "Need Attention"
                            ? "yellow"
                            : "red"
                        }
                        className="ml-2"
                      >
                        {row.status}
                      </Badge>
                    )}
                  </Table.Td>
                  <Table.Td className="flex justify-center items-center gap-2">
                    <Button
                      variant="filled"
                      color={row.submitted ? "#00C0A9" : "#07838F"}
                      onClick={() => submitQuantitativeMetric(index)}
                      leftSection={
                        row.submitted ? <FaRegCheckCircle size={14} /> : null
                      }
                    >
                      {row.submitted ? "Submitted" : "Submit"}
                    </Button>
                    <Button
                      variant="subtle"
                      color="red"
                      onClick={() => deleteQuantitativeRow(index)}
                    >
                      <IconTrash size={14} />
                    </Button>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </div>
        <Button
          variant="light"
          color="#07838F"
          leftSection={<IconPlus size={16} />}
          onClick={addQuantitativeRow}
          className="mt-2 w-full"
        >
          Add a new row
        </Button>
      </div>

      {/* Qualitative Metric Table */}
      <div>
        <div className="overflow-x-auto">
                  <Table
          verticalSpacing="sm"
          horizontalSpacing="md"
          withTableBorder
          withColumnBorders
          highlightOnHover
          className="min-w-[1300px] w-full"
        >
          <Table.Thead>
            <Table.Tr>
              <Table.Th style={{ width: "24%" }}>Qualitative Metric</Table.Th>
              <Table.Th style={{ width: "23%" }}>Current</Table.Th>
              <Table.Th style={{ width: "23%" }}>Target</Table.Th>
              <Table.Th style={{ width: "15%" }}>Status</Table.Th>
              <Table.Th style={{ width: "15%" }}>Actions</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {qualitativeData.map((row, index) => (
              <Table.Tr key={row.id || index}>
                <Table.Td>
                  <Select
                    data={Qualitative.map((item) => item.name)}
                    value={row.metric}
                    onChange={(value) =>
                      updateQualitativeRow(index, "metric", value)
                    }
                    searchable
                    placeholder="Select a metric"
                  />
                </Table.Td>
                <Table.Td>
                  <Select
                    data={formatSelectData(getMeasurementValues(row.metric))}
                    value={row.current?.toString()}
                    onChange={(value) => {
                      const selected = formatSelectData(
                        getMeasurementValues(row.metric)
                      ).find((item) => item.value === value);
                      updateQualitativeRow(
                        index,
                        "current",
                        selected?.original ?? value
                      );
                    }}
                    placeholder="Select current value"
                    disabled={!row.metric}
                  />
                </Table.Td>
                <Table.Td>
                  <Select
                    data={formatSelectData(getMeasurementValues(row.metric))}
                    value={row.target?.toString()}
                    onChange={(value) => {
                      const selected = formatSelectData(
                        getMeasurementValues(row.metric)
                      ).find((item) => item.value === value);
                      updateQualitativeRow(
                        index,
                        "target",
                        selected?.original ?? value
                      );
                    }}
                    placeholder="Select target value"
                    disabled={!row.metric}
                  />
                </Table.Td>
                <Table.Td>
                  <Select
                    data={["On Track", "Need Attention", "At Risk"]}
                    value={row.status}
                    onChange={(value) =>
                      updateQualitativeRow(index, "status", value)
                    }
                    placeholder="Select status"
                  />
                </Table.Td>
                <Table.Td className="flex justify-center items-center gap-2">
                  <Button
                    variant="filled"
                    color={row.submitted ? "#00C0A9" : "#07838F"}
                    onClick={() => submitQualitativeMetric(index)}
                    leftSection={
                      row.submitted ? <FaRegCheckCircle size={14} /> : null
                    }
                  >
                    {row.submitted ? "Submitted" : "Submit"}
                  </Button>
                  <Button
                    variant="subtle"
                    color="red"
                    onClick={() => deleteQualitativeRow(index)}
                  >
                    <IconTrash size={14} />
                  </Button>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>
        </div>
        <Button
          variant="light"
          color="#07838F"
          leftSection={<IconPlus size={16} />}
          onClick={addQualitativeRow}
          className="mt-2 w-full"
        >
          Add a new row
        </Button>
      </div>
    </div>
  );
};

export default Performance;
