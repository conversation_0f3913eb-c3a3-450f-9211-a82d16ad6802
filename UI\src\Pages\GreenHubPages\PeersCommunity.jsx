import { useTranslation } from 'react-i18next';
import MainLayout from '@/Layout/MainLayout';
import useSideBarRoute from '@/hooks/useSideBarRoute';
import appStore from '@/assets/images/apple-store.png';
import googlePlay from '@/assets/images/google-play.png';
import CommunityPost from './Partials/CommunityPost';
import { useCallback, useEffect, useRef, useState } from 'react';

import CreateQuestion from '@/Components/Community/CreateQuestion';
import Modal from '@/Components/Modal';
import ApiS3 from '@/Api/apiS3';
import FilterQuestions from '@/Components/Community/FilterQuestions';
import { toast } from 'react-toastify';
import Loading from '@/Components/Loading';
import TagsFilter from '@/Components/Community/TagsFilter';
import CreatePoll from '@/Components/Community/CreatePoll';
import { IoMdHome } from 'react-icons/io';

export default function PeersCommunity() {
  const { t } = useTranslation();
  const textModal = useRef();
  const pollModal = useRef();
  const [filtered, setFiltered] = useState(false);
  const [filteredData, setFilteredData] = useState([]);
  const [filters, setFilters] = useState({ tags: [], sort: ['newest'], type: [] });
  const [data, setData] = useState([]);
  const [recent, setRecent] = useState([]);
  const [loading, setLoading] = useState(false);

  const { GreenHubMenu } = useSideBarRoute();

  const [questionsNo, setQuestionsNo] = useState(0);

  const getNumOfQuestion = (num) => {
    setQuestionsNo(num);
  };

  const fetchQuestions = useCallback(async () => {
    try {
      setLoading(true);
      const response = await ApiS3.get('/community/questions/all');
      const data = response.data.reverse();
      console.log(response);
      setRecent(data.slice(0, 5));

      setFilteredData(data);
      setData(data);
      if (response.data.length > 0) {
        getNumOfQuestion(response.data.length);
      }
    } catch (error) {
      console.error('Error fetching questions:', error.message);
      toast.fail('Something went wrong.Please comeback later!');
    } finally {
      setLoading(false);
    }
  }, []);

  const sortQuestion = useCallback(() => {
    let filteredData = [...data];
    if (filters.sort.includes('newest')) filteredData = filteredData.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    if (filters.sort.includes('oldest')) filteredData = filteredData.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
    if (filters.type.includes('polls')) filteredData = filteredData.filter((question) => question.typeOfQuestion === 'poll');
    if (filters.type.includes('text')) filteredData = filteredData.filter((question) => question.typeOfQuestion === 'text');
    if (filters.tags.length > 0)
      filteredData = filteredData.filter((question) => {
        return filters.tags.some((tag) => question.tags.includes(tag));
      });
    setFilteredData(filteredData);
    setFiltered(true);
  }, [filters, data]);

  useEffect(() => {
    fetchQuestions();
  }, [fetchQuestions]);

  useEffect(() => {
    sortQuestion();
  }, [sortQuestion, filters]);

  return (
    <>
      <Modal
        ref={textModal}
        title={t('askPublicQuestion')}
        className="px-3 py-2 font-medium text-white border rounded-lg bg-secondary-400 border-secondary-400 min-w-32"
      >
        <CreateQuestion close={() => textModal?.current?.close()} fetchQuestions={fetchQuestions} />
      </Modal>
      <Modal
        ref={pollModal}
        title={t('Add Poll Question')}
        className="px-3 py-2 font-medium text-white border rounded-lg bg-secondary-400 border-secondary-400 min-w-32"
      >
        <CreatePoll close={() => pollModal?.current?.close()} fetchQuestions={fetchQuestions} />
      </Modal>
      <MainLayout menus={GreenHubMenu} navbarTitle={'Peers Community'}
          breadcrumbItems={[
            { title: <IoMdHome size={20} />, href: "/get-started" },
            { title: "Peer Community", href: "#" },
          ]}>
        {/* header */}
        <div className="flex justify-between gap-7">
          <div className="flex flex-col gap-4 grow">
            <div className="bg-white px-7 py-4 flex flex-col gap-4 justify-between rounded-lg w-full h-max shadow-[0px_0px_8.91px_0px_#0000001A]">
              <div className="flex justify-between gap-4">
                <h3 className="text-3xl font-bold">{t('AllQuestions')}</h3>
                <div className="flex self-end gap-4">
                  <button
                    className="px-3 py-2 font-medium text-white border rounded-lg bg-secondary-400 hover:bg-primary duration-300 border-secondary-400 min-w-32"
                    onClick={() => {
                      pollModal?.current?.open() || null;
                    }}
                  >
                    {t('Create Poll')}
                  </button>
                  <button
                    className="px-3 py-2 font-medium text-white border rounded-lg bg-secondary-400 hover:bg-primary duration-300 border-secondary-400 min-w-32"
                    onClick={() => {
                      textModal?.current?.open() || null;
                    }}
                  >
                    {t('Add Question')}
                  </button>
                </div>
              </div>
              {/*  */}
              <div className="flex items-center justify-between gap-4">
                <span className="font-medium">{`Number Of Questions: ${questionsNo}`}</span>
                <div className="flex gap-4" onClick={sortQuestion}>
                  <FilterQuestions setFilters={setFilters} filters={filters} />
                </div>
              </div>
            </div>
            {loading ? (
              <Loading />
            ) : data.length > 0 ? (
              <div className="flex flex-col gap-4 mb-4">
                {(filtered ? filteredData : data).map((question) => (
                  <CommunityPost key={question._id} question={question} />
                ))}
              </div>
            ) : (
              <h2 className="text-primary text-xl">There is No Questions at the moment. Go ahead and ask Yor Question!</h2>
            )}
          </div>
          <div className="flex flex-col w-full gap-4 max-w-96">
            {/* Explore by Question component */}

            <div className="px-5 py-4 bg-white rounded-lg w-full shadow-[0px_0px_8.91px_0px_#0000001A]">
              <h5 className="text-base font-bold">{t('ExploreByQuestion')}</h5>
              <TagsFilter setFilters={setFilters} filters={filters} sortQuestion={sortQuestion} />
            </div>
            {/* RecentActivities */}
            <div className="px-5 py-4 bg-white rounded-lg w-full shadow-[0px_0px_8.91px_0px_#0000001A]">
              <h5 className="mb-4 text-base font-bold">{t('RecentActivities')}</h5>
              <div className="flex flex-col gap-3">
                {recent?.map((event, i) => (
                  <div key={i} className="flex gap-3">
                    <div className="rounded-full w-9 h-9">
                      {event.user?.image ? (
                        <img src={event.user?.image} alt={event?.user?.userName || 'User'} className="w-10 h-10 rounded-full" />
                      ) : (
                        <div className="w-10 h-10 rounded-full cursor-default text-center flex items-center justify-center font-bold text-sm text-primary bg-[#07838F1A]">
                          {(event?.user?.userName && event?.user?.userName[0]) || 'U'}
                        </div>
                      )}
                    </div>
                    <div>
                      <h5 className="text-sm">{event?.user?.userName}</h5>
                      <span className="text-xs text-[#565D6D]">Added a {event.typeOfQuestion === 'poll' ? 'Poll' : 'Question'}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="px-5 py-4 bg-white rounded-lg w-full shadow-[0px_0px_8.91px_0px_#0000001A]">
              <h5 className="mb-4 text-base font-bold">{t('TryNewExperience')}</h5>
              <div className="flex gap-8">
                <button>
                  <img src={googlePlay} alt="Google Play" />
                </button>
                <button>
                  <img src={appStore} alt="App Store" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </MainLayout>
    </>
  );
}
