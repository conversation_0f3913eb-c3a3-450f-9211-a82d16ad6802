import React, { useState } from "react";
import { MdOutlineArticle } from "react-icons/md";
import carbonManagement from "@/assets/pdf/CarbonManagementAwareness.pdf";
import GuideforEmployees from "@/assets/pdf/GuideforEmployees.pdf";
import GuidesPartialsPDFIframe from "./Partials/GuidesPartialsPDFIframe";
import { useDisclosure } from "@mantine/hooks";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";

const Guides = () => {
  const { t } = useTranslation();
  const [opened, { open, close }] = useDisclosure(false);
  const [PDFURL, setPDFURL] = useState();

  const handlePdfClick = (pdf) => {
    const pdfUrl = pdf;
    setPDFURL(pdfUrl);
  };

  const data = [
    {
      id: 1,
      value: "Guide for Employees.",
      icons: <MdOutlineArticle className="text-primary" />,
      file: carbonManagement,
    },
    {
      id: 2,
      value: "Carbon Management Awareness.",
      icons: <MdOutlineArticle className="text-primary" />,
      file: GuideforEmployees,
    },
  ];

  return (
    <section className="p-6 bg-white rounded-2xl min-h-[376px]">
      <div className="flex flex-wrap items-center justify-between font-semibold text-primary">
        <h1 className="text-[1.4rem]">{t("Guides")}</h1>
        {/* <a className="text-base underline" href="#">
          View All
        </a> */}
      </div>
      <h3 className="mt-5 text-sm font-medium mb-7">
        {t("View all user guides, manuals, and training videos.")}
      </h3>
      <div className="flex flex-col h-full gap-5">
        {data.map((item) => (
          <div className="flex justify-between gap-3" key={item.id}>
            <div className="flex items-center gap-3">
              <span>{item.icons}</span>
              <Link
                //
                onClick={() => {
                  open();
                  handlePdfClick(item.file);
                }}
                className="text-[1rem] font-medium bg-transparent text-black"
              >
                {item.value}
              </Link>
            </div>
            {/* <Link
              to={item.file}
              download
              target="_blank"
              rel="noopener noreferrer"
              className="bg-[#2b939c] text-white px-4 py-2 float-end rounded-lg active:scale-95 transition-all"
            >
              Download PDF
            </Link> */}
          </div>
        ))}
        <GuidesPartialsPDFIframe
          opened={opened}
          close={close}
          PDFURL={PDFURL}
        />
      </div>
    </section>
  );
};

export default Guides;
