import React, { useEffect } from "react";

const ESGComponent = ({ assessmentData }) => {
  const { Environment, Social, Governance } = assessmentData?.category_scores;

  return (
    <svg  viewBox="0 0 600 600" className="w-[300px] h-[65%] lg:w-[600px] lg:h-[300px] mx-auto">
      {/* Circles */}
      <circle
        cx="300"
        cy="150"
        r="100"
        stroke="#00BFFF"
        strokeWidth="5"
        fill="none"
      />
      <circle
        cx="150"
        cy="400"
        r="100"
        stroke="#00BFFF"
        strokeWidth="5"
        fill="none"
      />
      <circle
        cx="450"
        cy="400"
        r="100"
        stroke="#00BFFF"
        strokeWidth="5"
        fill="none"
      />

      {/* Connecting Lines */}
      <line
        x1="260"
        y1="240"
        x2="220"
        y2="330"
        stroke="#00BFFF"
        strokeWidth="4"
      />
      <line
        x1="340"
        y1="240"
        x2="376"
        y2="330"
        stroke="#00BFFF"
        strokeWidth="4"
      />
      <line
        x1="250"
        y1="400"
        x2="350"
        y2="400"
        stroke="#00BFFF"
        strokeWidth="4"
      />

      {/* Inner Circles for Percentages */}
      <circle
        cx="60"
        cy="450"
        r="50"
        stroke="#00BFFF"
        strokeWidth="5"
        fill="#fff"
      />
      <circle
        cx="540"
        cy="450"
        r="50"
        stroke="#00BFFF"
        strokeWidth="5"
        fill="#fff"
      />
      <circle
        cx="400"
        cy="140"
        r="50"
        stroke="#00BFFF"
        strokeWidth="5"
        fill="#fff"
      />

      {/* Inner Dotted Circles */}
      <circle
        cx="650"
        cy="450"
        r="30"
        stroke="#000000"
        strokeWidth="3"
        fill="none"
        strokeDasharray="4 4"
      />
      <circle
        cx="-50"
        cy="450"
        r="30"
        stroke="#000000"
        strokeWidth="3"
        fill="none"
        strokeDasharray="4 4"
      />
      <circle
        cx="520"
        cy="130"
        r="30"
        stroke="#000000"
        strokeWidth="3"
        fill="none"
        strokeDasharray="4 4"
      />

      {/* Text in Circles */}
      <text x="290" y="150" textAnchor="middle" fontSize="30" fill="black">
        Social
      </text>
      <text x="400" y="150" textAnchor="middle" fontSize="40" fill="black">
        %
      </text>
      <text x="520" y="140" textAnchor="middle" fontSize="30" fill="black">
        {Math.round(Social)}
      </text>

      <text x="450" y="390" textAnchor="middle" fontSize="27" fill="black">
        Environmental
      </text>
      <text x="540" y="460" textAnchor="middle" fontSize="40" fill="black">
        %
      </text>
      <text x="650" y="460" textAnchor="middle" fontSize="30" fill="black">
        {Math.round(Environment)}
      </text>

      <text x="150" y="390" textAnchor="middle" fontSize="27" fill="black">
        Governance
      </text>
      <text x="60" y="460" textAnchor="middle" fontSize="40" fill="black">
        %
      </text>
      <text x="-50" y="460" textAnchor="middle" fontSize="30" fill="black">
        {Math.round(Governance)}
      </text>

      {/* Inner Dotted Line */}
      <line
        x1="-18"
        y1="445"
        x2="10"
        y2="445"
        stroke="#00BFFF"
        strokeWidth="4"
        strokeDasharray="4 4"
      />
      <line
        x1="619"
        y1="445"
        x2="590"
        y2="445"
        stroke="#00BFFF"
        strokeWidth="4"
        strokeDasharray="4 4"
      />
      <line
        x1="450"
        y1="130"
        x2="490"
        y2="130"
        stroke="#00BFFF"
        strokeWidth="4"
        strokeDasharray="4 4"
      />

      {/* Outer Descriptive Text */}
      <text x="600" y="400" textAnchor="start" fontSize="20" fill="black">
        Score
      </text>
      <text x="5" y="400" textAnchor="end" fontSize="20" fill="black">
        Score
      </text>
      <text x="460" y="80" textAnchor="start" fontSize="20" fill="black">
        Score
      </text>
    </svg>
  );
};

export default ESGComponent;
