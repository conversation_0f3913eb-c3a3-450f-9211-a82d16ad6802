
const ThresholdBadge = ({ level }) => {
    return (
      <div
        className="flex items-center justify-center gap-2 px-4 py-1 rounded-full w-fit"
        style={{ backgroundColor: level.bgColor }}
      >
        <div
          className="w-2 h-2 rounded-full"
          style={{ backgroundColor: level.color }}
        />
        <span
          className="font-semibold uppercase text-sm"
          style={{ color: level.color }}
        >
          {level.label}
        </span>
      </div>
    );
}

export default ThresholdBadge
