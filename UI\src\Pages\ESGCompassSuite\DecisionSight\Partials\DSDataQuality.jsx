import React from "react";
import { But<PERSON>, Group, Pagination, Table } from "@mantine/core";
import { CiFilter } from "react-icons/ci";
import { useTranslation } from "react-i18next";

export default function DSDataQuality() {
  const { t } = useTranslation();
  const elements = [
    {
      id: "#897632",
      relevantSolution: "CSRD",
      issueDetails: "Missing carbon data",
      orderDate: "21/05/2023",
      status: "Critical",
    },
    {
      id: "#345782",
      relevantSolution: "ISSB",
      issueDetails: "Incomplete data submission",
      orderDate: "23/06/2023",
      status: "Medium",
    },
    {
      id: "#653216",
      relevantSolution: "GreenWash",
      issueDetails: "Greenwashing Risk",
      orderDate: "12/02/2022",
      status: "Medium",
    },
    {
      id: "#562334",
      relevantSolution: "CSRD",
      issueDetails: "Data integrity issues",
      orderDate: "24/02/2022",
      status: "Critical",
    },
    {
      id: "#897865",
      relevantSolution: "ISSB",
      issueDetails: "Outdated information in reports",
      orderDate: "17/08/2022",
      status: "Low",
    },
  ];
  const rows = elements.map((element, index) => (
    <Table.Tr key={index}>
      <Table.Td>{index + 1}</Table.Td>
      <Table.Td>{element.id}</Table.Td>
      <Table.Td>{element.relevantSolution}</Table.Td>
      <Table.Td>{element.issueDetails}</Table.Td>
      <Table.Td>{element.orderDate}</Table.Td>
      <Table.Td>
        <p
          className={`p-2 rounded-2xl w-1/2 mx-auto
          capitalize
          ${
            element.status === "Critical"
              ? " text-[#FF6666] bg-[#ffe5e5]"
              : element.status === "Medium"
              ? "text-[#CCA300] bg-[#fff5cc]"
              : element.status === "Low"
              ? "text-[#667085] bg-[#e0e2e7]"
              : ""
          }`}
        >
          {element.status}
        </p>
      </Table.Td>
    </Table.Tr>
  ));
  return (
    <div className="w-full p-5 bg-white rounded-lg shadow-md">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-black">
          {t("dataQualityManagement")}
        </h1>
        <Button className="flex items-center bg-transparent border border-primary text-primary hover:bg-transparent hover:text-primary">
          {t("filter")} <CiFilter className="w-5 h-5 ms-2" />
        </Button>
      </div>
      <div className="mt-5">
        <Table.ScrollContainer>
          <Table className="text-center">
            <Table.Thead>
              <Table.Tr>
                <Table.Th className="font-normal text-center text-primary">
                  #
                </Table.Th>
                <Table.Th className="font-normal text-center text-primary">
                  Id
                </Table.Th>
                <Table.Th className="font-normal text-center text-primary">
                  Relevant Solution
                </Table.Th>
                <Table.Th className="font-normal text-center text-primary">
                  Issue Details
                </Table.Th>
                <Table.Th className="font-normal text-center text-primary">
                  Order Date
                </Table.Th>
                <Table.Th className="font-normal text-center text-primary">
                  Status
                </Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>{rows}</Table.Tbody>
          </Table>
        </Table.ScrollContainer>
        <Pagination.Root total={10}>
          <Group gap={5} justify="space-between">
            <div className="flex justify-center items-center border-2 pe-2 rounded-lg hover:bg-[#f8f9fa] cursor-pointer">
              <Pagination.Previous
                className="p-0 m-0 border-0"
                id="PaginationPrevious"
              />
              <label
                htmlFor="PaginationPrevious"
                className="py-1 cursor-pointer"
              >
                {t("previous")}
              </label>
            </div>
            <Group>
              <Pagination.Items />
            </Group>
            <div className="flex justify-center items-center border-2 ps-2 rounded-lg hover:bg-[#f8f9fa] cursor-pointer">
              <label htmlFor="PaginationNext" className="py-1 cursor-pointer">
                {t("next")}
              </label>
              <Pagination.Next
                className="p-0 m-0 bg-transparent border-0"
                id="PaginationNext"
              />
            </div>
          </Group>
        </Pagination.Root>
      </div>
    </div>
  );
}
