import {
  ESGReportingBuildingIcon,
  ESGReportingDownloadIcon,
} from "@/assets/icons";
import IROReport from "./Reports/IROReport";
import RiskAuditReport from "./Reports/RiskAuditReport";
import GreenwashingRiskReport from "./Reports/GreenwashingRiskReport";
import iro from './iro-report.pptx'
import riskAndAudit from './risk-and-audit-report.pptx'

function ReportingPage() {
  const handleDownload = (file) => {
    // Create a Blob from the file URL
    fetch(file)
      .then(response => response.blob())
      .then(blob => {
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = file.split('/').pop(); // Get the file name from the URL
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url); // Clean up the URL object
      })
      .catch(error => {
        console.error('Download failed:', error);
      });
  };

  return (
    <div className="flex flex-col gap-5 w-full min-h-screen">
      <div className="flex justify-between items-center gap-6 flex-wrap sm:flex-nowrap">
        <IROReport />
        <RiskAuditReport />
        <GreenwashingRiskReport />
      </div>

      <div className="text-primary border-s-4 border-primary bg-white flex flex-col  pb-20 justify-start my-4 rounded-xl px-8 py-4">
        <h2 className="flex justify-start items-center text-[#494949] font-semibold text-xl mb-2 gap-1">
          <ESGReportingBuildingIcon /> Presentation Template
        </h2>
        <p className="mb-6 text-[#272727]">
          Download our PowerPoint template. You&apos;ll need to fill it in with
          your own data.
        </p>
        <div className="flex justify-between items-center gap-4 flex-wrap sm:flex-nowrap">
        <button 
        onClick={() => handleDownload(iro)} 
        className="bg-[#07838F1A] rounded-lg text-[#07838F] text-bold text-sm py-3 px-5 flex justify-center sm:items-center gap-1 hover:bg-[#07848f36] duration-300 w-full text-start sm:text-center"
      >
        <ESGReportingDownloadIcon main={true} />
        PPTX Download for IRO Assessment Report
      </button>
      <button 
        onClick={() => handleDownload(riskAndAudit)} 
        className="bg-[#07838F1A] rounded-lg text-[#07838F] text-bold text-sm py-3 px-5 flex justify-center sm:items-center gap-1 hover:bg-[#07848f36] duration-300 w-full text-start sm:text-center"
      >
        <ESGReportingDownloadIcon main={true} />
        PPTX Download for Risk and Audit Report
      </button>
        </div>
      </div>

      {/* <LazyLoadImage
        className="mx-auto"
        src={mountainLogo}
        alt="outlined mountains with levelup logo"
      /> */}
    </div>
  );
}

export default ReportingPage;
