import useSideBarRoute from '@/hooks/useSideBarRoute';
import MainLayout from '@/Layout/MainLayout';
import useRoutes from '@/Routes/useRoutes';
import { useTranslation } from 'react-i18next';
import { IoMdHome } from 'react-icons/io';
import { Link } from 'react-router-dom';

export default function GreenHubStart() {
  const { t } = useTranslation();
  const { GreenHubMenu } = useSideBarRoute();
  const { GreenHubAcademyDashboard, GreenHubResourcesDashboard, GreenHubCommunityDashboard } = useRoutes().GreenHubMap;

  return (
    <MainLayout menus={GreenHubMenu} navbarTitle={'GreenHub'}
    breadcrumbItems={[
      { title: <IoMdHome size={20} />, href: "/get-started" },
      { title: "Your GreenHub", href: "#" },
    ]}>
      <div className="h-full overflow-y">
        <h1 className="px-8 py-4 text-xl font-bold bg-white rounded-2xl text-blue-950 mb-7">{t('GreenHubTitle')}</h1>
        <div className="flex h-full gap-6 flex-wrap md:flex-nowrap">
          <Link
            to={GreenHubCommunityDashboard.path}
            className={`w-full block  relative  h-[65vh] rounded-lg bg-[url(@/assets/images/peer-community.png)] bg-no-repeat bg-center bg-cover`}
          >
            <div className="absolute top-0 bottom-0 left-0 right-0 bg-black rounded-lg opacity-40"></div>
            <h2 className="absolute inset-0 px-8 py-10 text-4xl font-bold text-white">{t('PeersCommunity')}</h2>
          </Link>
          <Link
            to={GreenHubAcademyDashboard.path}
            className={`w-full block relative h-[65vh]  rounded-lg bg-[url(@/assets/images/academy.jpeg)] bg-no-repeat bg-center bg-cover`}
          >
            <div className="absolute top-0 bottom-0 left-0 right-0 bg-black rounded-lg opacity-40"></div>
            <h2 className="absolute inset-0 px-8 py-10 text-4xl font-bold text-white">{t('Academy')}</h2>
          </Link>
          <Link
            to={GreenHubResourcesDashboard.path}
            className={`w-full block relative h-[65vh] rounded-lg bg-[url(@/assets/images/resources.jpeg)]  bg-no-repeat bg-center bg-cover`}
          >
            <div className="absolute top-0 bottom-0 left-0 right-0 z-0 bg-black rounded-lg opacity-40"></div>
            <h2 className="absolute inset-0 px-8 py-10 text-4xl font-bold text-white ">{t('Resources')}</h2>
          </Link>
        </div>
      </div>
    </MainLayout>
  );
}
