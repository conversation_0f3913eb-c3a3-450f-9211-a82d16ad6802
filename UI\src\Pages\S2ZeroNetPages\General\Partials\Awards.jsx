import React from "react";
import awards01 from "../../../../assets/images/awards-03.png";
import awards02 from "../../../../assets/images/awards-02.png";
import awards03 from "../../../../assets/images/awards-01.png";
import awards04 from "../../../../assets/images/awards-04.png";
import acheived from "../../../../assets/images/acheived.png";
import { useTranslation } from "react-i18next";
import { MdOutlineFileDownload } from "react-icons/md";
import { Button } from "@mantine/core";

const Awards = () => {
 const { t } = useTranslation();

 return (
  <div className="grid md:grid-cols-3 xl:grid-cols-5 gap-4 text-start">
   {/* Bronze Award */}
   <div className=" rounded-2xl bg-white shadow-md  ">
    <div className="px-4 py-3 flex justify-center">
     <div className="ms-5">
      <img
       className="max-w-full h-auto justify-self-center"
       src={awards01}
       alt={t("BronzeAwardAlt")}
      />
     </div>
     <div className="">
      <Button className="bg-transparent text-[#404B52] hover:bg-transparent hover:text-[#404B52] p-0 justify-self-end">
       <MdOutlineFileDownload className="w-6 h-6 cursor-pointer" />
      </Button>
     </div>
    </div>
    <div className="my-3 ms-4">
     <p className="flex items-center  gap-4">
      {t("YouGotThis")}{" "}
      <img className="inline-block" src={acheived} alt={t("AchievedAlt")} />
     </p>
     <p className="text-xl font-semibold">{t("Bronze")}</p>
     <p>{t("GreenStarter")}</p>
    </div>
   </div>

   {/* Silver Award */}
   <div className=" rounded-2xl bg-gradient-to-tr from-[#0000003e] to-[#fffefe47] shadow-md  ">
    <div className="px-4 py-4 flex justify-center">
     <img
      className="max-w-full h-auto"
      src={awards02}
      alt={t("SilverAwardAlt")}
     />
    </div>
    <div className="my-3 ms-4">
     <p className="text-xl font-semibold">{t("Silver")}</p>
     <p>{t("EcoInnovator")}</p>
    </div>
   </div>

   {/* Gold Award */}
   <div className=" rounded-2xl bg-gradient-to-tr from-[#0000003e] to-[#fffefe47] shadow-md  ">
    <div className="px-4 py-4 flex justify-center">
     <img
      className="max-w-full h-auto"
      src={awards03}
      alt={t("GoldAwardAlt")}
     />
    </div>
    <div className="my-3 ms-4">
     <p className="text-xl font-semibold">{t("Gold")}</p>
     <p>{t("SustainabilityLeader")}</p>
    </div>
   </div>

   {/* Diamond Award */}
   <div className=" rounded-2xl bg-gradient-to-tr from-[#0000003e] to-[#fffefe47] shadow-md  ">
    <div className="px-4 py-4 flex justify-center">
     <img
      className="max-w-full h-auto"
      src={awards04}
      alt={t("DiamondAwardAlt")}
     />
    </div>
    <div className="my-3 ms-4">
     <p className="text-xl font-semibold">{t("Diamond")}</p>
     <p>{t("ClimateChampion")}</p>
    </div>
   </div>

   {/* Dots */}
   <div className="rounded-2xl bg-blue-200 shadow-md flex justify-center items-center border-2 border-cyan-600">
    <div className="flex justify-center items-center px-20 py-4 my-auto">
     <span className="w-4 h-4 bg-cyan-700 rounded-full mx-2"></span>
     <span className="w-4 h-4 bg-cyan-700 rounded-full mx-2"></span>
     <span className="w-4 h-4 bg-cyan-700 rounded-full mx-2"></span>
    </div>
   </div>
  </div>
 );
};

export default Awards;
