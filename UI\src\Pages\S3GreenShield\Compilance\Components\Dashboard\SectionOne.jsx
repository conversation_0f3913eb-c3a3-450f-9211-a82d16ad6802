import { AreaChart } from "@mantine/charts";
import React, { useEffect, useState } from "react";
import { keys } from "../../compilanceData";
import { useTranslation } from "react-i18next";
import Cookies from "js-cookie";
import axios from "axios";

const SectionOne = () => {
  const { t } = useTranslation();
  const [chartData, setChartData] = useState([]);
  const [result, setResult] = useState([]);

  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  // Fetch compliance data
  useEffect(() => {
    const fetchComplianceData = async () => {
      try {
        const token = Cookies.get("level_user_token");
        if (!token) {
          console.error("No token found in cookies");
          return;
        }

        const response = await axios.get(
          "https://portals3-staging-dwdwc5d6dnhyhpbb.uksouth-01.azurewebsites.net/compliance-assessement/chartData",
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        setChartData(response.data);
      } catch (error) {
        console.error("Error fetching compliance data:", error);
      }
    };

    fetchComplianceData();
  }, []);

  useEffect(() => {
    if (chartData.length > 0) {
      const uniqueMonths = {
        Jan: { totalScore: 0, count: 0 },
        Feb: { totalScore: 0, count: 0 },
        Mar: { totalScore: 0, count: 0 },
        Apr: { totalScore: 0, count: 0 },
        May: { totalScore: 0, count: 0 },
        Jun: { totalScore: 0, count: 0 },
        Jul: { totalScore: 0, count: 0 },
        Aug: { totalScore: 0, count: 0 },
        Sep: { totalScore: 0, count: 0 },
        Oct: { totalScore: 0, count: 0 },
        Nov: { totalScore: 0, count: 0 },
        Dec: { totalScore: 0, count: 0 },
      };

      chartData.forEach((item) => {
        const monthName = months[item.month];
        uniqueMonths[monthName].totalScore += item.score;
        uniqueMonths[monthName].count += 1;
      });

      const processedData = Object.keys(uniqueMonths).map((month) => ({
        date: month,
        Progress:
          uniqueMonths[month].count > 0
            ? uniqueMonths[month].totalScore / uniqueMonths[month].count
            : 0,
      }));

      setResult(processedData);
    }
  }, [chartData]);

  return (
    <div className="flex gap-5 lg:flex-nowrap flex-wrap">
      <div className=" mx-auto bg-white md:w-1/2 text-center p-5 rounded-lg shadow-[0px_0px_10px_0px_#0000001A]">
        <h5 className="mb-6 text-xl font-bold min-h-[300px] min-w-[350px]">
          {t("progressTracker")}
        </h5>
        {result.length > 0 ? (
          <AreaChart
            aspect={2}
            data={result}
            dataKey="date"
            series={[{ name: "Progress", color: "#00C0A940" }]}
            curveType="natural"
            yAxisProps={{ domain: [0, 100] }}
          />
        ) : (
          <p>Loading chart data...</p>
        )}
      </div>
      {/* <div className="bg-white md:w-1/2 flex p-5 flex-col justify-between items-center px-14 rounded-lg shadow-[0px_0px_10px_0px_#0000001A]">
        <h3 className="mb-6 text-xl font-bold">Key Actions</h3>
        <div className="h-full mt-10">
          {keys.map((el) => (
            <div key={el.name} className="flex items-center gap-x-5 mt-5">
              {el.icon}
              <h3>{el.name}</h3>
            </div>
          ))}
        </div>
      </div> */}
    </div>
  );
};

export default SectionOne;
