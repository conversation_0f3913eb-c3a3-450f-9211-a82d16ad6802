import { useEffect, useState } from "react";
import {
  Table,
  Checkbox,
  rem,
  TextInput,
  Pagination, // Import Mantine Pagination
  Select,
  Badge,
  TagsInput,
  Button,
  Loader,
  MultiSelect, // Import Mantine Select
} from "@mantine/core";
import _ from "lodash";
import { useTranslation } from "react-i18next";
import { useDoubleMateriality } from "@/Contexts/DoubleMaterialityContext";
import priorityLevelColor from "@/Utils/priorityLevel";
import { GoDotFill } from "react-icons/go";
import axios from "axios";
import Cookies from "js-cookie";
import Loading from "@/Components/Loading";

export default function SingleMaterialityTable({
  data,
  onSelectItem,
  setSelectedItems,
}) {
  const [selection, setSelection] = useState(["0"]);
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [assignees, setAssignees] = useState([]);
  const [assigneesArray, setAssigneesArray] = useState([]);


  const {handleComments, propertyLevelMap, handleScoreInputChange, handleSubmitChanges,postLoading,handleSelect} = useDoubleMateriality();
  useEffect(() => {
    const initialSelectedIds = data
      .filter((item) => item?.selected)
      .map((item) => item.id);
    setSelection(initialSelectedIds);
    setSelectedItems(initialSelectedIds);
  }, []);


  const toggleRow = (id) => {
    setSelection((current) =>
      current.includes(id)
        ? current.filter((item) => item !== id)
        : [...current, id]
    );
    onSelectItem(id);
  };

  const chunkedData = _.chunk(data, rowsPerPage);
  const totalPages = chunkedData.length || 0;
  const currentData = chunkedData[Math.min(currentPage, totalPages) - 1] || [];


   const assigneesData = async () => {
    try {
      const assignees = await axios.get(
        "https://portal-auth-main.azurewebsites.net/get-all-company-users",
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("level_user_token")}`,
          },
        }
      );
      setAssigneesArray(assignees.data)
      const uniqueNames = [...new Set(assignees.data.map((item) => item.user_name))];
      setAssignees(uniqueNames);
    } catch (error) {
      //console.log(error);
    }
  };
  useEffect(() => {
    assigneesData();
  }, [])

  const rows = currentData.map((item) => {
    const ownerStrings = item.owner?.map(o=> o?.user_name)
    const defaultAssignees = ownerStrings.map(
      (name) =>
       assigneesArray.find((item) => item?.user_name === name)?.user_name
     )
    .filter(Boolean)
    return (
    <Table.Tr
      key={item.id}
      className={`${
        selection.includes(item.id) ? "bg-[#07838F1A]" : ""
      } font-bold text-gray-600`}
    >
      <Table.Td>
        <Checkbox
          checked={item?.selected || selection.includes(item.id)}
          value={item?.selected ? item?.selected : ""}
          onChange={(e) => {
            toggleRow(item.id);
            handleScoreInputChange(
              item.id,
              "selected",
              e.target.checked ? true : false
              , 'single'
            );
            handleSelect(item.id,'selected',e.target.checked,'single');
          }}
          color="#07838F"
        />
      </Table.Td>
      <Table.Td>{item.uid}</Table.Td>
      <Table.Td>{item.name}</Table.Td>
      <Table.Td className="text-center">{item.Category}</Table.Td>

      <Table.Td className="text-center">
        <TextInput
          type="number"
          variant="filled"
          className="w-24"
          classNames={{ input: "text-center bg-gray-200" }}
          display="inline-block"
          value={item.Financial_Score}
          onChange={(e) => {
            const value = Math.max(0, Math.min(Number(e.target.value), 100));
            handleScoreInputChange(item.id, "Financial_Score", value,'single');
          }}
        />
      </Table.Td>

      <Table.Td className="text-center">
        <TextInput
          type="number"
          variant="filled"
          className="w-24"
          classNames={{ input: "text-center bg-gray-200" }}
          display="inline-block"
          value={item.Impact_Score}
          onChange={(e) => {
            const value = Math.max(0, Math.min(Number(e.target.value), 100));
            handleScoreInputChange(item.id, "Impact_Score", value,'single');
          }}
        />
      </Table.Td>

      <Table.Td className="text-center">{item.Total_Impact}</Table.Td>

      <Table.Td className="">
              <Badge
                leftSection={<GoDotFill />}
                variant="light"
                size="lg"
                style={{ display: 'flex', alignItems: 'center', justifyContent: 'center',width: '100%',height: '100%' }}
                color={priorityLevelColor(propertyLevelMap[item.Priority])}
              >
                <span>
                  {propertyLevelMap[item.Priority]}
                </span>
              </Badge>
        </Table.Td>
      
      <Table.Td>
        <TagsInput
          placeholder="Press Enter to add item"
          clearable
          title="Comments"
          onChange={(e) => handleComments(item.id, "Comments", e,'single')}
          defaultValue={item.Comments ? item.Comments : []}
        />
      </Table.Td>



      <Table.Td>
      {assignees.length == 0 ? (
      <>
       <Loading />
      </>
     ) : (
      <MultiSelect
       onChange={(selectedNames) => {

          const selectedIds = selectedNames.map(
           (name) =>
            assigneesArray.find((item) => item.user_name === name)
          );
          handleComments(item.id, "owner", selectedIds,'double')
         }}
       label=""
       placeholder="Select Owner"
       data={assignees}
       defaultValue={defaultAssignees || []}

      />
     )}
      </Table.Td>

    </Table.Tr>
  )});

  const { t } = useTranslation();

  return (
    <div className="bg-white mt-7">
      <Table.ScrollContainer minWidth={500}>
        <Table miw={800} verticalSpacing="sm"  withTableBorder withColumnBorders>
          <Table.Thead className="pb-6 text-base font-bold text-center border-b-2 border-primary text-primary">
            <Table.Tr>
              <Table.Th style={{ width: rem(40) }}>
              </Table.Th>
              <Table.Th>
                <br />
                <br />
                {t("UID")}
              </Table.Th>
              <Table.Th>
                <br />
                <br />
                {t("topic")}
              </Table.Th>
              <Table.Th className="text-center">
                <br />
                <br />
                {t("category")}
              </Table.Th>
              <Table.Th className="text-center min-w-[200px]">
                <br />
                <br />
                {t("Financial Impact")}
              </Table.Th>
              <Table.Th className="text-center relative min-w-[200px] h-[140px]">
                <span className="w-[599px] bg-white absolute -left-[200px] text-black -top-0 z-10 text-center border-b-[1px] block py-5">
                Impact to Business
                </span>
                <br />
                <br />
                {t("Impact to People/ Planet")}
              </Table.Th>
              <Table.Th className="text-center min-w-[200px]">
              <br />
              <br />
                {t("Total Impact to Business")}
              </Table.Th>
              <Table.Th className="text-center relative min-w-[200px]">
                <span className="w-[200px] absolute left-0 text-black -top-0 z-50 text-center border-b-[1px] block py-5">
                  Materiality Calculation
                </span>
                <br />
                <br />
                {t("Overall Materiality ")}
              </Table.Th>
              <Table.Th className="text-center">
              <br />
              <br />
                {t("Comments")}</Table.Th>
              <Table.Th className="text-center min-w-[250px]">
              <br />
              <br />
                {t("Owner")}</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>{rows}</Table.Tbody>
        </Table>
      </Table.ScrollContainer>

      <Button
      onClick={()=> handleSubmitChanges('single')}
      disabled={postLoading}
        className={`flex-1 ml-auto mr-2 flex gap-2 items-center justify-center  text-white py-2 px-4 text-center ${
          postLoading
            ? "cursor-not-allowed bg-gray-400"
            : "bg-primary"
        }`}
      >
        Save Changes
        {postLoading && <Loader size="xs" color="white" className="ml-2" />}
      </Button>

      <div className="flex justify-between mt-4 items-center">
        <Select
          data={["10", "20", "50", "100"]}
          value={String(rowsPerPage)}
          onChange={(value) => setRowsPerPage(Number(value))}
          label={t("rowsPerPage")}
          placeholder={t("selectRowsPerPage")}
          allowDeselect={false}
        />

        <Pagination
          color="#05808b"
          value={Math.min(currentPage, totalPages)}
          onChange={setCurrentPage}
          total={totalPages}
        />
      </div>
    </div>
  );
}
