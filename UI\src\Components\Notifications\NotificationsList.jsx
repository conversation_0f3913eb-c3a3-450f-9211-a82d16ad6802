import { useNotificationStore } from '../store/useNotificationStore';
import { List, ThemeIcon } from '@mantine/core';
import { IconBell } from '@tabler/icons-react';

export const NotificationsList = () => {
  const { notifications } = useNotificationStore();

  return (
    <List
      spacing="xs"
      center
      icon={<ThemeIcon color="blue" size={24}><IconBell size={16} /></ThemeIcon>}
    >
      {notifications.map((notif, index) => (
        <List.Item key={index}>{notif.message}</List.Item>
      ))}
    </List>
  );
};