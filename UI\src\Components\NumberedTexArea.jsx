import React, { useState, useRef } from 'react';
import { Textarea } from '@mantine/core';

const NumberedTextarea = ({ ...props }) => {
  const [value, setValue] = useState('');
  const textareaRef = useRef(null);

  const handleKeyDown = (event) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      const lines = value.split('\n');
      const currentLine = lines.length;
      const newLine = `${currentLine + 1}. `;
      const newValue = `${value}\n${newLine}`;
      setValue(newValue);
      setTimeout(() => {
        textareaRef.current.focus();
      }, 0);
    }
  };

  const handleChange = (event) => {
    const { value } = event.target;
    if (value.endsWith('\n')) {
      return;
    }
    setValue(value);
  };

  return (
    <Textarea
      ref={textareaRef}
      value={value}
      onChange={handleChange}
      onKeyDown={handleKeyDown}
      autosize
      minRows={3}
      {...props}
    />
  );
};

export default NumberedTextarea;

// import React, { useState, useRef } from 'react';
// import { Textarea } from '@mantine/core';

// const NumberedTextarea = ({ ...props }) => {
//   const [value, setValue] = useState('');
//   const textareaRef = useRef(null);

//   const handleKeyDown = (event) => {
//     if (event.key === 'Enter') {
//       event.preventDefault();
//       const lines = value.split('\n');
//       const lastLine = lines[lines.length - 1];
      
//       // Check if the last line is a numbered line
//       const match = lastLine.match(/^(\d+)\.\s*$/);
//       const currentNumber = match ? parseInt(match[1], 10) : 0;
//       const newLineNumber = currentNumber + 1;

//       const newValue = `${value}\n${newLineNumber}. `;
//       setValue(newValue);

//       // Ensure the cursor is placed correctly after adding the new line
//       setTimeout(() => {
//         textareaRef.current.focus();
//         textareaRef.current.selectionStart = newValue.length;
//         textareaRef.current.selectionEnd = newValue.length;
//       }, 0);
//     }
//   };

//   const handleChange = (event) => {
//     const { value } = event.target;
//     if (value.endsWith('\n')) {
//       return;
//     }
//     setValue(value);
//   };

//   const handleFocus = () => {
//     // Ensure the textarea starts with "1. " if it's empty
//     if (value.trim() === '') {
//       setValue('1. ');
//     }
//   };

//   return (
//     <Textarea
//       ref={textareaRef}
//       value={value}
//       onChange={handleChange}
//       onKeyDown={handleKeyDown}
//       onFocus={handleFocus}
//       autosize
//       minRows={3}
//       {...props}
//     />
//   );
// };

// export default NumberedTextarea;
