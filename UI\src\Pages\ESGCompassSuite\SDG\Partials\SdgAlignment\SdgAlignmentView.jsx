import Loading from "@/Components/Loading";
import { useSdg } from "@/Contexts/SdgContext";
import { Button, Tooltip } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { GoShare } from "react-icons/go";
import SdgAlignmentTable from "./Partials/SdgAlignmentTable";

const SdgAlignmentView = () => {
 const { t } = useTranslation();
 const {
  sdgAlignment,
  PostAnswersState,
  setPostAnswersState,
  postLoading,
  PriorityLevels,
  prioritySelectColorMap,
  postAnswers,
  ReadinessLevel,
  readinessColorMap,
  reportLoading,
  getReport,
  sdgData,
 } = useSdg();

 return (
  <>
   <div className="items-center justify-between w-full px-4 py-5 bg-white rounded-lg shadow-md mb-7 sm:flex ">
    <div>
     <h1 className="text-lg font-bold text-black">SDG Alignment Assessment</h1>
     <p className="font-normal text-sm text-[#667085]">
      {t("descriptiveBodyText")}
     </p>
    </div>
    <div className="mt-5 sm:mt-0 grid md:grid-cols-2  gap-5">
     <Tooltip
      multiline
      w={200}
      radius={"md"}
      withArrow
      transitionProps={{ duration: 200 }}
      label={
       !sdgData?.solved ? (
        <span className="capitalize flex justify-center text-center">
         Please solve the all assessments first
        </span>
       ) : null
      }
      className={!sdgData?.solved ? "" : "hidden"}
     >
      <Button
       className={`text-white   rounded-md bg-primary  ${
        !sdgData?.solved
         ? "cursor-not-allowed opacity-50"
         : "hover:bg-primary hover:opacity-90 "
       }`}
       size="md"
       disabled={!sdgData?.solved}
       onClick={
        reportLoading
         ? ""
         : () => {
            getReport();
           }
       }
      >
       {reportLoading ? <Loading /> : "Assess"}
      </Button>
     </Tooltip>
     <Button
      className="duration-200 ease-out bg-transparent border-2 rounded-lg text-primary border-secondary-300 hover:-translate-y-2 hover:bg-secondary-400 hover:bg-transparent hover:text-primary outline-none focus:outline-none"
      size="md"
     >
      <GoShare className="me-2" />
      {t("export")}
     </Button>
    </div>
   </div>
   <SdgAlignmentTable
    readinessColorMap={readinessColorMap}
    prioritySelectColorMap={prioritySelectColorMap}
    sdgAlignment={sdgAlignment}
    PriorityLevels={PriorityLevels}
    ReadinessLevel={ReadinessLevel}
    postAnswers={postAnswers}
    setPostAnswersState={setPostAnswersState}
    PostAnswersState={PostAnswersState}
    postLoading={postLoading}
   />
  </>
 );
};

export default SdgAlignmentView;
