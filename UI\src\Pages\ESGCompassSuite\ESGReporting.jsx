import CompanyInfoForm from "@/Pages/ESGCompassSuite/GRI/MultiStepForm/CompanyInfoForm";
import { useAnswersStore } from "@/Store/useAnswersStore";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import { useNavigate } from "react-router-dom";
import MainLayout from "@/Layout/MainLayout";
import { IoMdHome } from "react-icons/io";


export default function ESGReporting() {
  const { issbMenu } = useSideBarRoute();

  const companyInfo = useAnswersStore((state) => state.companyInfo);
  const setCompanyInfo = useAnswersStore((state) => state.setCompanyInfo);
  const navigate = useNavigate();

  const handleCompanyInfoChange = (e) => {
    setCompanyInfo(e.target.name, e.target.value);
  };

  const nextStep = () => {
    const framework = companyInfo.reportingFramework;
    if (framework === "GRI") {
      navigate("/gri-reporting");
    } else if (framework === "ISSB") {
      navigate("/issb-reporting-assessment");
    } else if (framework === "TNFD") {
      navigate("/tnfd-reporting");
    } else {
      console.error("Invalid reporting framework selected");
    }
  };


  return (
    <MainLayout menus={issbMenu} navbarTitle={"ESG Reporting"}
    breadcrumbItems={[
      { title: <IoMdHome size={20} />, href: "/get-started" },
      { title: "ESG Reporting Suite", href: "/Insights-reporing/issb-reporting-suite" },
      { title: "ESG Reporting", href: "#" },
    ]}>
    <div className="w-full mx-auto p-4 md:p-20">
        <CompanyInfoForm
          companyInfo={companyInfo}
          onChange={handleCompanyInfoChange}
          onNext={nextStep}
        />
    </div>
    </MainLayout>
  );
}