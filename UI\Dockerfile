FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files first for better caching
COPY package*.json ./

# Install all dependencies (including devDependencies for build)
RUN npm install

# Copy the rest of the application code
COPY . .

# Build the application
RUN NODE_OPTIONS="--max-old-space-size=8192" npm run build

# Production stage
FROM node:18-alpine

WORKDIR /app

# Copy only production files from builder
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/package*.json ./

# Install only production dependencies
RUN npm install --production && \
    npm install -g serve && \
    npm cache clean --force

# Expose the port
EXPOSE 5173

# Start the server
CMD ["serve", "-s", "dist", "-l", "5173"]