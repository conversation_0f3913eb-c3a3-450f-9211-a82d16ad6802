import { useTranslation } from "react-i18next";
import PublicDownloadTemplate from "./Partials/PublicDownloadTemplate";
import PublicUploadTemplate from "./Partials/PublicUploadTemplate";

export default function PublicBatchInputs({ handelAssetType, assetTypeAll ,allItemSpecificities}) {
  const { t } = useTranslation();
  return (
    <div className="">
      <div className="bg-white rounded-lg p-5">
        <PublicDownloadTemplate />

        <PublicUploadTemplate  assetTypeAll={assetTypeAll} allItemSpecificities={allItemSpecificities}/>
      </div>
    </div>
  );
}
