import S2Layout from "@/Layout/S2Layout";
import useRoutes from "@/Routes/useRoutes";
import { Tabs } from "@mantine/core";
import { useState } from "react";
import Reporting from "./Partials/Reporting";
import Assessment from "./Partials/Assessment";
import { IoMdHome } from "react-icons/io";

const ChainSight = () => {
  const { getStart } = useRoutes();
  const [activeTab, setActiveTab] = useState("Assessment");

  return (
    <S2Layout
      navbarTitle="ChainSight: Sustainable Procurement"
      breadcrumbItems={[
        { title: <IoMdHome size={20}/>, href: getStart.path },
        { title: "ChainSight", href: "#" },
      ]}
    >
      <div>
        <Tabs
          variant="pills"
          className="px-5"
          defaultValue={activeTab}
          onChange={setActiveTab}
        >
          <Tabs.List
            justify="center"
            className="grid md:grid-cols-2 justify-center bg-[#FFFFFF] border-2 border-[#E8E7EA] px-6 gap-5 py-3 mb-6 rounded-lg text-primary"
          >
            <Tabs.Tab
              value="Assessment"
              className={`relative text-lg font-semibold py-3 px-6 transition-all ${
                activeTab === "Assessment"
                  ? "active-tab"
                  : " text-[#5A5A5A] bg-[#FFFFFF] rounded-lg border-[#E8E7EA] border-2"
              }`}
              onClick={() => setActiveTab("Assessment")}
            >
              Assessment
            </Tabs.Tab>
            <Tabs.Tab
              value="Reporting"
              className={`relative text-lg font-semibold py-3 px-6 transition-all ${
                activeTab === "Reporting"
                  ? "active-tab"
                  : " text-[#5A5A5A] bg-[#FFFFFF] rounded-lg border-[#E8E7EA] border-2"
              }`}
              onClick={() => setActiveTab("Reporting")}
            >
              Reporting
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="Reporting">
            <Reporting isActive={activeTab === "Reporting"} />
          </Tabs.Panel>
          <Tabs.Panel value="Assessment">
            <Assessment isActive={activeTab === "Assessment"} />
          </Tabs.Panel>
        </Tabs>
      </div>
    </S2Layout>
  );
};

export default ChainSight;