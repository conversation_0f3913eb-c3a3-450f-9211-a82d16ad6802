import ApiS2 from "@/Api/apiS2Config";
import Loading from "@/Components/Loading";
import { Button, Select, Tooltip } from "@mantine/core";
import { showNotification } from "@mantine/notifications";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { FaChevronDown } from "react-icons/fa";
import { FiUpload } from "react-icons/fi";
import { HiOutlineDocumentDownload } from "react-icons/hi";
import { IoIosDownload } from "react-icons/io";

export default function DownloadTemplate() {
  const { t } = useTranslation();
  const [SelectedTemplate, setSelectedTemplate] = useState();
  const [loading, setLoading] = useState(false);
  const downloadSelectedTemplate = async () => {
    setLoading(true);
    try {
      // const response = await ApiS2.get("/batch_inputs/get_template", {
      //   headers: {
      //     "template-name": SelectedTemplate,
      //   },
      //   responseType: "arraybuffer",
      // });

      // const blob = new Blob([response.data], {
      //   type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      // });

      // const url = window.URL.createObjectURL(blob);
      // const link = document.createElement("a");
      // link.href = url;
      // link.download = `${SelectedTemplate}.xlsx`;
      // document.body.appendChild(link);
      // link.click();
      // document.body.removeChild(link);

      // setLoading(false);
      // showNotification({
      //   message: "File downloaded successfully",
      //   color: "green",
      // });
      const { data } = await ApiS2.get("/batch_inputs/get_template", {
        headers: {
          "template-name": SelectedTemplate,
        },
      });
      // setFileUrl(data.file_url);
      if (data.file_url) {
        const link = document.createElement("a");
        link.href = data.file_url;
        link.download = SelectedTemplate;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        setLoading(false);

        showNotification({
          message: "File downloaded successfully",
          color: "green",
        });
      }
    } catch (error) {
      setLoading(false);
      showNotification({
        message: "File not downloaded successfully",
        color: "red",
      });
      console.error(error);
    }
  };

 return (
  <>
   <div className="md:flex items-center justify-between mb-3">
    <p className="font-semibold font-inter text-[16px] leading-[24px] capitalize Download-Batch-Template">
     <span className="text-secondary-300 capitalize me-1">{t("step1 :")}</span>
     {t(" Select a template and download it.")}
    </p>
    <div className="md:flex justify-center items-center 1/2">
     <Select
      id="Template"
      name="Template"
      data={[
       "Refrigerants",
       "Heat_and_Steam",
       "Other_Stationary",
       "Purchased_Electricity",
       "Company_Vehicles_Distance_based",
       "Company_Vehicles_Fuel_based",
       "Natural_Gas",
       "Purchased_Goods_and_Services",
       "Captial_Goods",
       "business_Travel",
      ].map((item) => ({
       key: item,
       value: item?.replace(/_/g, " "),
      }))}
      placeholder="Select the template"
      className="md:me-10 w-60 md:w-72 mx-auto Select-Emission-Source"
      radius={10}
      value={SelectedTemplate?.replace(/_/g, " ")}
      onChange={(value) => {
       setSelectedTemplate(value?.replace(/ /g, "_"));
      }}
      rightSection={<FaChevronDown />}
     />

     <Tooltip
      multiline
      w={220}
      radius={"md"}
      withArrow
      transitionProps={{ duration: 200 }}
      className={`${SelectedTemplate ? "hidden" : ""}`}
      label={
       !SelectedTemplate && (
        <span className="capitalize flex justify-center">
         please Select Template first
        </span>
       )
      }
     >
      <Button
       className={`Download-Empty-Template px-[25px] flex justify-center items-center gap-x-2  mx-auto md:mx-0 mt-5 md:mt-0 rounded-xl shadow-md border-2   ${
        !SelectedTemplate
         ? "cursor-not-allowed opacity-20 border-slate-800 text-slate-800 "
         : "border-secondary-300 text-white bg-secondary-300 hover:bg-secondary-300 hover:text-white"
       }`}
       onClick={downloadSelectedTemplate}
       disabled={!SelectedTemplate || loading}
       size="md"
      >
       {loading ? (
        <Loading />
       ) : (
        <>
         <span >
          <HiOutlineDocumentDownload   size={20} />
         </span>
         <span>{t("downloadTemplate.downloadTemplate")}</span>
        </>
       )}
      </Button>
     </Tooltip>
    </div>
   </div>
   <hr className="h-[2px] bg-gray-300  mx-auto" />
  </>
 );
}
