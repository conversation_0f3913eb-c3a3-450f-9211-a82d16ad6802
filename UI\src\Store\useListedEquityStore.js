// useAutoLoanStore.js
import { create } from "zustand";

export const useListedEquityStore = create((set) => ({
  investment_value: "",  // $1,000,000
  evic: "",  // $50,000,000 (Enterprise Value Including Cash)
  company_emissions: "",  // 100,000 tCO₂e (Company's Total Emissions)
  company_name: "",
  company_industry: "Technology",
  data_quality_score: "",

  // results
  attribution_percentage: 0,
  financed_emissions: 0,
  loading: false,
  estimate_evic_loaging:false,
  estimate_company_emissions_loading:false,

  // Setters
  setInvestmentValue: (v) => set({ investment_value: v }),
  setEvic: (v) => set({ evic: v }),
  setCompanyName: (v) => set({ company_name: v }),
  setCompanyIndustry: (v) => set({ company_industry: v }),
  setDataQualityScore: (v) => set({ data_quality_score: v }),
  setCompanyEmissions: (v) => set({ company_emissions: v }),
  setLoading: (v) => set({ loading: v }),
  setEstimateEvicLoading: (v) => set({ estimate_evic_loaging: v }),
  setEstimateCompanyEmissionsLoading: (v) => set({ estimate_company_emissions_loading: v }),

  setResults: (results) => set({
    attribution_percentage: results.attribution_percentage,
    financed_emissions: results.financed_emissions
  }),
}));
