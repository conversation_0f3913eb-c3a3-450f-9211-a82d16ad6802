import ApiS2 from "@/Api/apiS2Config";
import {
  MeasurDocumentcloudIcon,
  MeasureWindIcon,
} from "@/assets/svg/ImageSVG";
import { Tabs } from "@mantine/core";
import classNames from "classnames";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import EmissionsBreakdownView from "./Partials/Emissions Breakdown/EmissionsBreakdownView";
import EmissionsOverView from "./Partials/EmissionsOverView/EmissionsOverView";

export default function MeasureView({ target }) {
  const { t } = useTranslation();

  const [activeTab, setActiveTab] = useState(target || "emissions-overview");
  const [totalEmissions, setTotalEmissions] = useState([]);
  const [loading, setLoading] = useState(true);


  const tabs = [
    {
      value: "emissions-overview",
      label: t("EmissionsOverview"),
      icon: (
        <MeasureWindIcon
          color={activeTab === "emissions-overview" ? "#07838F" : "#5A5A5A"}
        />
      ),
      component: (
        <EmissionsOverView
          totalEmissions={totalEmissions?.emissions_overview}
          loading={loading}
          activeTab={activeTab}
        />
      ),
    },
    {
      value: "emissions-breakdown",
      label: t("Emissions Breakdown"),
      icon: (
        <MeasurDocumentcloudIcon
          color={activeTab === "emissions-breakdown" ? "#07838F" : "#5A5A5A"}
        />
      ),
      component: (
        <EmissionsBreakdownView
          totalEmissions={totalEmissions?.emissions_breakdown}
          activeTab={activeTab}
        />
      ),
    },
  ];
  const fetchData = async () => {
    try {
      const { data } = await ApiS2.get("/admin/get_emissions_breakdown");
      setTotalEmissions(data);
    } catch (error) {
      console.error("Error fetching emissions data:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // update emission over view
  // update analytics for transportion add and update charts and add table section
  // update risk management (add the main systems page) then when enter the system activate and show the seleced system
  //  update launchpad routing for the main risk systems page
  // update data collection UI

  return (
    <>
      <div className="pb-3">
        <Tabs defaultValue={activeTab} onChange={setActiveTab}>
          <Tabs.List className="static The-Measure-stage-consists-of-two-main-parts grid lg:grid-cols-2 gap-3  mb-3 overflow-hidden rounded-md lg:justify-start text-primary">
            {tabs.map((tab) => (
              <Tabs.Tab
                key={tab.value}
                value={tab.value}
                className={classNames(
                  "rounded-lg font-bold bg-white flex items-center justify-center border-0 md:w-auto text-base shadow-md",
                  {
                    "border-s-8 border-primary text-primary":
                      activeTab === tab.value,
                    "text-[#5A5A5A]": activeTab !== tab.value,
                  }
                )}
              >
                <div className="flex  items-center justify-center gap-2">
                  <p className="p-2 bg-[#F0F0F0] rounded-full">{tab.icon}</p>
                  {tab.label}
                </div>
              </Tabs.Tab>
            ))}
          </Tabs.List>

          {tabs.map((tab) => (
            <Tabs.Panel key={tab.value} value={tab.value}>
              {tab.component}
            </Tabs.Panel>
          ))}
        </Tabs>
        {/* <EmissionsOverView
          totalEmissions={totalEmissions?.emissions_overview}
          loading={loading}
          activeTab={activeTab}
        /> */}
      </div>
    </>
  );
}
