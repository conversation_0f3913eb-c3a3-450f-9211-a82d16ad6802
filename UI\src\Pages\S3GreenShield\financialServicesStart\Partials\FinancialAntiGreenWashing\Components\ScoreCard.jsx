import React from "react";
import { Table } from "@mantine/core";

const ScoreCard = () => {
  const KN = [
    //Key Insights
    "The overall score (XX) indicates the level of greenwashing risk.",
    "Each NO answer is treated as a red flag, significantly impacting the score.",
    "Providing evidence for YES answers can help offset the penalties from NO answers.",
    "The Compliance Score and Action Progress Score provide additional context for regulatory adherence and proactive improvements.",
  ];
  const GR = [
    //General Recommendations
    "Focus on addressing items with NO answers to reduce red flags.",
    "Ensure all YES answers have supporting evidence to maximize the evidence bonus.",
    "Prioritize improving compliance, especially for regulatory requirements.",
    "Accelerate the completion of planned actions to improve the Action Progress Score.",
    "Regularly review and update the assessment to track progress over time.",
  ];
  const IYS = [
    //Interpreting Your Scores
    "Overall Score: Reflects your general performance in preventing greenwashing. A lower score indicates higher risk.",
    "Compliance Score: Indicates how well you're meeting regulatory requirements. Aim for 100% compliance.",
    "Action Progress Score: Shows how proactive you are in implementing improvements. A higher score demonstrates commitment to ongoing enhancement.",
  ];

  const head2 = ["Metric", "Score", "Interpretation"];

  const t2 = [
    {
      metric: "Overall Score",
      score: "XX/100",
      Interpretation: "[Excellent/Good/Fair/Poor] [Low/Medium/High] greenwashing risk",
    },
    {
      metric: "Compliance Score",
      score: "XX%",
      Interpretation: "XX out of XX regulatory requirements met",
    },
    {
      metric: "Action Progress Score",
      score: "XX%",
      Interpretation: "XX out of XX planned actions completed",
    },
  ];

  const rows2 = t2.map((item, i) => {
    return (
      <Table.Tr key={i} className={`risk-matrix-table bg-white text-sm`}>
        <Table.Td className="bg-secondary-gray-100 text-xs p-2">
          {item.metric}
        </Table.Td>
        <Table.Td className="p-2">{item.score}</Table.Td>
        <Table.Td className="p-2">{item.Interpretation}</Table.Td>
      </Table.Tr>
    );
  });

  return (
    <div className="basis-[48%] flex flex-col gap-3">
      <h2 className="text-base font-semibold">Key Insights</h2>
      {KN.map((f, i) => (
        <h4 key={i} className="text-secondary-gray-200 text-sm">
          {i + 1}. {f}
        </h4>
      ))}
      <h2 className="text-base font-semibold">General Recommendations</h2>
      {GR.map((f, i) => (
        <h4 key={i} className="text-secondary-gray-200 text-sm">
          {i + 1}. {f}
        </h4>
      ))}
      <h2 className="text-base font-semibold">Interpreting Your Scores</h2>
      {IYS.map((f, i) => (
        <ul key={i} className="ml-5 li-circle">
          <li className="text-secondary-gray-200 text-sm">{f}</li>
        </ul>
      ))}
      <h2 className="text-base font-semibold">Anti-Greenwashing Scorecard</h2>
      <Table withTableBorder withColumnBorders miw={"50%"} verticalSpacing="lg">
        <Table.Thead className="border-b-2 bg-white pb-6 text-base text-center">
          <Table.Tr>
            {head2.map((el, i) => (
              <Table.Th
                key={i}
                className="bg-secondary-gray-100 font-medium text-xs p-2"
              >
                {el}
              </Table.Th>
            ))}
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>{rows2} </Table.Tbody>
      </Table>
    </div>
  );
};

export default ScoreCard;
