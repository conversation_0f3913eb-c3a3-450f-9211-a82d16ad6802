import { useState, useMemo, useEffect } from "react";
import { TextInput, Group, Loader, Button, MultiSelect } from "@mantine/core";
import { Dropzone } from "@mantine/dropzone";
import { IconUpload, IconX } from "@tabler/icons-react";
import axios from "axios";
import Cookies from "js-cookie";
import { toast } from "react-toastify";
import { DeleteIcon } from "@/assets/icons";
import { FileSavedIcon } from "@/assets/icons";
import { UploadFileIcon } from "@/assets/icons";

const Evidence = ({ topicId, evidences: initialEvidences }) => {
  const [inputName, setInputName] = useState("");
  const [url, setUrl] = useState("");
  const [files, setFiles] = useState([]);
  const [companyUsers, setCompanyUsers] = useState([]);
  const [selectedCollaborators, setSelectedCollaborators] = useState([]);
  const [evidences, setEvidences] = useState(initialEvidences || []);

  const docVaultBaseUrl = "https://docvault-staging.azurewebsites.net";
  const baseUrl = "https://issb-report-api-staging.azurewebsites.net";

  // Handle file upload to DocVault
  const handleFileUpload = async (file, tempId, currentFiles) => {
    const fileIndex = currentFiles.findIndex((f) => f.tempId === tempId);
    if (fileIndex === -1) {
      console.error(
        "File not found in state:",
        file.name,
        tempId,
        "Current files:",
        currentFiles
      );
      return;
    }

    setFiles((prevFiles) => {
      const updatedFiles = [...prevFiles];
      updatedFiles[fileIndex] = { ...updatedFiles[fileIndex], loading: true };
      return updatedFiles;
    });
    console.log("Starting upload for:", file.name);

    const token = Cookies.get("level_user_token");
    if (!token) {
      toast.error("Authorization token is missing");
      setFiles((prevFiles) => {
        const updatedFiles = [...prevFiles];
        updatedFiles[fileIndex] = {
          ...updatedFiles[fileIndex],
          loading: false,
        };
        return updatedFiles;
      });
      return;
    }

    const formData = new FormData();
    formData.append("file", file);

    try {
      const response = await axios.post(
        `${docVaultBaseUrl}/api/v1/files/upload`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "x-filename": file.name,
          },
        }
      );
      const { data } = response.data;
      console.log("Upload Response for", file.name, ":", response.data);

      setFiles((prevFiles) => {
        const updatedFiles = [...prevFiles];
        updatedFiles[fileIndex] = {
          name: data.name,
          id: data.id,
          mimetype: data.mimetype,
          size: data.size,
          created_at: data.created_at,
          updated_at: data.updated_at,
          loading: false,
          success: true,
          tempId: tempId,
        };
        console.log("Upload complete, state:", updatedFiles);
        return updatedFiles;
      });
    } catch (error) {
      console.error(
        "Upload failed for",
        file.name,
        ":",
        error.response || error
      );
      setFiles((prevFiles) => {
        const updatedFiles = [...prevFiles];
        updatedFiles[fileIndex] = {
          ...updatedFiles[fileIndex],
          loading: false,
          success: false,
        };
        return updatedFiles;
      });
      toast.error(
        `Upload failed for ${file.name}: ${
          error.response?.data?.message || error.message || "Unknown error"
        }`
      );
    }
  };

  // Handle file delete from DocVault
  const handleDelete = async (tempId) => {
    const fileIndex = files.findIndex((f) => f.tempId === tempId);
    if (fileIndex === -1) {
      console.error(
        "File not found in state:",
        tempId,
        "Current files:",
        files
      );
      toast.error("File not found");
      return;
    }

    const file = files[fileIndex];
    if (!file.id) {
      console.error("File has not been uploaded yet:", file.name);
      toast.error("Cannot delete file: Not uploaded");
      return;
    }

    setFiles((prevFiles) => {
      const updatedFiles = [...prevFiles];
      updatedFiles[fileIndex] = { ...updatedFiles[fileIndex], loading: true };
      return updatedFiles;
    });
    console.log("Starting deletion for:", file.name);

    const token = Cookies.get("level_user_token");
    if (!token) {
      toast.error("Authorization token is missing");
      setFiles((prevFiles) => {
        const updatedFiles = [...prevFiles];
        updatedFiles[fileIndex] = {
          ...updatedFiles[fileIndex],
          loading: false,
        };
        return updatedFiles;
      });
      return;
    }

    try {
      await axios.delete(`${docVaultBaseUrl}/api/v1/files/${file.id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      console.log("Deletion successful for:", file.name);

      // Remove the file from state
      setFiles((prevFiles) => {
        const updatedFiles = prevFiles.filter((f) => f.tempId !== tempId);
        console.log("File removed from state:", updatedFiles);
        return updatedFiles;
      });
    } catch (error) {
      console.error(
        "Deletion failed for",
        file.name,
        ":",
        error.response || error
      );
      setFiles((prevFiles) => {
        const updatedFiles = [...prevFiles];
        updatedFiles[fileIndex] = {
          ...updatedFiles[fileIndex],
          loading: false,
        };
        return updatedFiles;
      });
      toast.error(
        `Failed to delete ${file.name}: ${
          error.response?.data?.message || error.message || "Unknown error"
        }`
      );
    }
  };

  // Handle evidence deletion
  const handleEvidenceDelete = async (evidenceId) => {
    const token = Cookies.get("level_user_token");
    if (!token) {
      toast.error("Authorization token is missing");
      return;
    }

    try {
      await axios.delete(
        `${baseUrl}/api/v1/topics/${topicId}/evidences/${evidenceId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      console.log("Evidence deletion successful for ID:", evidenceId);

      // Remove the evidence from state
      setEvidences((prevEvidences) =>
        prevEvidences.filter((evidence) => evidence.id !== evidenceId)
      );
    } catch (error) {
      console.error(
        "Evidence deletion failed for ID:",
        evidenceId,
        ":",
        error.response || error
      );
      toast.error(
        `Failed to delete evidence: ${
          error.response?.data?.message || error.message || "Unknown error"
        }`
      );
    }
  };

  // Send evidence to backend
  const sendEvidenceToBackend = async (evidenceName, evidenceUrl) => {
    const token = Cookies.get("level_user_token");
    if (!token) {
      toast.error("Authorization token is missing");
      return null;
    }

    try {
      const response = await axios.post(
        `${baseUrl}/api/v1/topics/${topicId}/evidences`,
        {
          evidence_name: evidenceName,
          evidence_url: evidenceUrl,
          tagged_user_ids: selectedCollaborators.map(Number),
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );
      console.log("Evidence sent to backend:", response.data);
      return response.data.topic.evidences; // Return the updated evidences
    } catch (error) {
      console.error(
        "Failed to send evidence to backend:",
        error.response || error
      );
      toast.error(
        `Failed to register evidence: ${
          error.response?.data?.message || error.message || "Unknown error"
        }`
      );
      return null;
    }
  };

  // Handle manual evidence submission
  const handleSubmit = async () => {
    if (!inputName.trim() && files.length === 0) {
      toast.error(
        "Evidence name is required or at least one file must be uploaded"
      );
      return;
    }

    const token = Cookies.get("level_user_token");
    if (!token) {
      toast.error("Authorization token is missing");
      return;
    }

    try {
      // Submit manual evidence if inputName and url are provided
      if (inputName.trim() && url.trim()) {
        const updatedEvidences = await sendEvidenceToBackend(inputName, url);
        if (updatedEvidences) {
          setEvidences(updatedEvidences);
          setInputName("");
          setUrl("");
          setSelectedCollaborators([]);
        }
      }

      // Submit each uploaded file to backend and remove it from the list
      const updatedFiles = [...files];
      for (let i = updatedFiles.length - 1; i >= 0; i--) {
        if (updatedFiles[i].id) {
          // Only process files that have been successfully uploaded to DocVault
          const evidenceUrl = `https://docvault.azurewebsites.net/api/v1/files/${updatedFiles[i].id}/download`;
          const updatedEvidences = await sendEvidenceToBackend(
            updatedFiles[i].name,
            evidenceUrl
          );
          if (updatedEvidences) {
            setEvidences(updatedEvidences);
            updatedFiles.splice(i, 1); // Remove the file from the list after successful submission
          }
        }
      }
      setFiles(updatedFiles); // Update the state with the filtered list
    } catch (error) {
      console.error("Submit failed:", error.response || error);
      toast.error(
        `Failed to submit evidence: ${
          error.response?.data?.message || error.message || "Unknown error"
        }`
      );
    }
  };

  // Handle dropzone drop event
  const handleDrop = (droppedFiles) => {
    const newFiles = droppedFiles.map((file, index) => ({
      name: file.name,
      file: file,
      loading: true,
      success: false,
      tempId: `${file.name}-${Date.now()}-${index}`,
    }));
    setFiles((prevFiles) => {
      const updatedFiles = [...prevFiles, ...newFiles];
      newFiles.forEach((newFile) =>
        handleFileUpload(newFile.file, newFile.tempId, updatedFiles)
      );
      return updatedFiles;
    });
  };

  // Fetch company users
  const fetchCompanyUsers = async () => {
    const token = Cookies.get("level_user_token");
    if (!token) {
      toast.error("Authorization token is missing");
      return;
    }

    try {
      const response = await fetch(
        "https://portal-auth-main.azurewebsites.net/get-all-company-users",
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error(
          `Failed to fetch company users: ${response.statusText}`
        );
      }

      const users = await response.json();
      setCompanyUsers(users);
    } catch (error) {
      console.error("Error fetching company users:", error);
      toast.error("Failed to fetch company users");
    }
  };

  // Fetch company users on component mount
  useEffect(() => {
    fetchCompanyUsers();
  }, []);

  // Update evidences when initialEvidences prop changes
  useEffect(() => {
    setEvidences(initialEvidences || []);
  }, [initialEvidences]);

  // Prepare options for MultiSelect
  const userOptions = useMemo(() => {
    return companyUsers.map((user) => ({
      value: user.user_id.toString(),
      label: user.user_name,
    }));
  }, [companyUsers]);

  return (
    <div className="mt-4">
      <h2 className="font-medium mb-2">Upload Evidence</h2>
      <div className="bg-[#F8F8F8] w-full border rounded p-4">
        <div className="flex justify-between flex-col">
          <div className="flex max-md:flex-col gap-4 justify-between items-center pb-4  ">
            <div className="flex flex-col gap-4 w-full md:w-[48%]">
              <TextInput
                label="Evidence name"
                placeholder="Enter Evidence name"
                value={inputName}
                onChange={(event) => setInputName(event.currentTarget.value)}
                className="w-full"
              />
              <TextInput
                label="Evidence URL"
                placeholder="https://example.com"
                value={url}
                onChange={(event) => setUrl(event.currentTarget.value)}
                className="w-full"
              />
              <MultiSelect
                label="Collaborator"
                data={userOptions}
                placeholder="Select Colleague"
                searchable
                value={selectedCollaborators}
                onChange={setSelectedCollaborators}
                styles={{ dropdown: { zIndex: 1000 } }}
              />
            </div>
            <div className="md:w-[48%] w-full h-full">
              <label className="pb-4">Browse evidence</label>
              <Dropzone
                onDrop={handleDrop}
                onReject={(files) => console.log("Rejected files:", files)}
                accept={{}}
                multiple
                className="border-2 border-[#E8E7EA] rounded-lg p-6 cursor-pointer"
              >
                <Group
                  justify="center"
                  className="flex flex-col items-center h-[140px]"
                  gap="sm"
                  mih={100}
                  style={{ pointerEvents: "none" }}
                >
                  <Dropzone.Accept>
                    <IconUpload
                      size={52}
                      color="var(--mantine-color-blue-6)"
                      stroke={1.5}
                    />
                  </Dropzone.Accept>
                  <Dropzone.Reject>
                    <IconX
                      size={52}
                      color="var(--mantine-color-red-6)"
                      stroke={1.5}
                    />
                  </Dropzone.Reject>
                  <Dropzone.Idle>
                    <UploadFileIcon />
                  </Dropzone.Idle>
                  <div className="text-center">
                    <p className="text-[#9C9C9C] underline">
                      Click to upload
                    </p>
                  </div>
                </Group>
              </Dropzone>
              {files.length > 0 && (
                <div className="mt-4">
                  <ul className="text-left text-sm text-[#272727]">
                    {files.map((file) => (
                      <li
                        key={file.id || file.tempId}
                        className="flex items-center justify-between bg-[#FFFFFF] border border-[#D5D5D8] p-4 mt-2 rounded-lg gap-2"
                      >
                        <div className="flex flex-col gap-2">
                          <div className="flex items-center gap-2">
                            {file.loading && <Loader size="sm" />}
                            {file.success && !file.loading && <FileSavedIcon />}
                            {!file.success && !file.loading && (
                              <IconX size={16} color="red" />
                            )}
                            <span className="text-ellipsis inline-block text-[#525252] overflow-hidden h-5">
                              <span className="font-bold">File Name: </span>
                              {file.name}
                            </span>
                          </div>
                          {file.success && (
                            <span className="text-[#7F7F7F]">
                              The file has been uploaded successfully.
                            </span>
                          )}
                        </div>
                        <div
                          className="cursor-pointer"
                          onClick={() => handleDelete(file.tempId)}
                        >
                          <DeleteIcon />
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>

          <div className="w-full">
            <Button
              fullWidth
              color={`${
                files.length > 0 ||
                (url.trim() !== "" && inputName.trim() !== "")
                  ? "#07838F"
                  : "#B1A9AE"
              }`}
              onClick={handleSubmit}
              disabled={
                files.length === 0 &&
                (url.trim() === "" || inputName.trim() === "")
              }
            >
              Submit Evidence
            </Button>
          </div>
          {evidences.length > 0 && (
            <div className="mt-4">
              <h3 className="text-sm font-medium">Submitted Evidences</h3>
              <ul className="text-left text-sm text-[#272727]">
                {evidences.map((evidence) => (
                  <li
                    key={evidence.id}
                    className="flex items-center justify-between bg-[#FFFFFF] border border-[#D5D5D8] p-4 mt-2 rounded-lg gap-2"
                  >
                    <div className="flex flex-col gap-2">
                      <div className="flex items-center gap-2">
                        <FileSavedIcon />
                        <span className="text-ellipsis inline-block text-[#525252] overflow-hidden h-5">
                          <span className="font-bold">File Name:</span>{" "}
                          {evidence.name}
                        </span>
                      </div>
                      <span className="text-[#7F7F7F]">
                        <span className="font-bold">URL:</span>{" "}
                        <a
                          href={evidence.url}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          {evidence.url}
                        </a>
                        {evidence.tagged_users?.length > 0 && (
                          <>
                            <br />
                            Collaborators:{" "}
                            {evidence.tagged_users
                              .map((user) => user.user_name)
                              .join(", ")}
                          </>
                        )}
                      </span>
                    </div>
                    <div
                      className="cursor-pointer"
                      onClick={() => handleEvidenceDelete(evidence.id)}
                    >
                      <DeleteIcon />
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Evidence;
