import microsoftazureBlobLogo from "@/assets/images/1_wr8GvIhYl0cvPL0al9xVaA.png";
import AWSLogo from "@/assets/images/63588a8a2e82ff2d5e3434b6_202210-redshift-logo.png";
import AWSS3Logo from "@/assets/images/download.png";
import GoogleCloudLogo from "@/assets/images/Google-Cloud-Logo.png";
import microsoftazureLogo from "@/assets/images/microsoft-azure-icon-2048x590-2uommq4p.png";
import { showNotification } from "@mantine/notifications";
import axios from "axios";
import Cookies from "js-cookie";
import { useCallback } from "react";
import { useTranslation } from "react-i18next";
import { connectorsData } from "../connectorsData";
import CreateConnectionInput from "./CreateConnectionInput";
export default function CreatConnectionTab({ getTableData }) {
 const { t } = useTranslation();

 const connectionTest = useCallback(
  async (connectionData) => {

   const {        database,
    host,
    port,
    user,
    password} = connectionData.connection.config;

const {client,name} = connectionData;

let newObj = {}

const allData = {
    database,
    host,
    port,
    user,
    password,
    client,
    name
    }

    if(client == 'mysql' || client == "microsoft") {
        newObj == allData
    }else {
        newObj == connectionData
    }
    
   try {
    const { data } = await axios.post(
     "https://connectors0.azurewebsites.net/connector/test",
     connectionData,
     {
      headers: { Authorization: `Bearer ${Cookies.get("level_user_token")}` },
     }
    );

    showNotification({
     title: t("Success"),
     message: t("Connection test successful!"),
     color: "green",
    });

    console.log(data);
    return { success: true }; // ✅ إرجاع success عند النجاح
   } catch (error) {
    console.error(error);
    showNotification({
     title: t("Error"),
     message: error.response?.data?.message || t("Connection test failed!"),
     color: "red",
    });

    return { success: false }; // ❌ إرجاع failure عند الفشل
   }
  },
  [t]
 );
 const connectionSubmit = useCallback(
  async (connectionData) => {
    const {        database,
        host,
        port,
        user,
        password} = connectionData.connection.config;
        const {keyName} = connectionData.connection;
        console.log("🚀 ~ keyName:", keyName)


    const {client,name} = connectionData;

    let newObj = {}

    const allData = {
        database,
        host,
        port,
        user,
        password,
        client,
        name
        }

        if(keyName == 'MySQL' || keyName == "MSSQL") {
            newObj == allData
        }else {
            newObj = connectionData
        }
        


   try {
    const { data } = await axios.post(
     "https://connectors0.azurewebsites.net/connector",
     connectionData,
     {
      headers: { Authorization: `Bearer ${Cookies.get("level_user_token")}` },
     }
    );
    getTableData();
    showNotification({
     title: t("Success"),
     message: t("Connection test successful!"),
     color: "green",
    });

    console.log(data);
    return { success: true };
   } catch (error) {
    console.error(error);
    showNotification({
     title: t("Error"),
     message: error.response?.data?.message || t("Connection test failed!"),
     color: "red",
    });

    return { success: false };
   }
  },
  [t]
 );
 const DataBaseInfo = [
  {
   //  name: "Google Cloud Storage",
   button: "Google Cloud DB",
   img: GoogleCloudLogo,
  },
  {
   //  name: "Google Cloud Storage",
   button: "Microsoft Azure DB",
   img: microsoftazureLogo,
  },
  {
   //  name: "Google Cloud Storage",
   button: "Azure Blob DB",
   img: microsoftazureBlobLogo,
  },
  {
   //  name: "Google Cloud Storage",
   button: "AWS DB",
   img: AWSLogo,
  },
  {
   //  name: "Google Cloud Storage",
   button: "AWS S3 DB",
   img: AWSS3Logo,
  },
 ];
 return (
  <div className="flex flex-col flex-wrap justify-center items-center py-10 font-inter">
   <article className="max-w-[600px]">
    <h1 className="text-black mb-8 font-semibold text-[36px] leading-[24px] text-center">
     {t("Data Integrations")}
    </h1>
   </article>
   <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3  gap-9 w-full px-10">
    {Object.entries(connectorsData).map(([key, value],indx) => (
     <CreateConnectionInput
      onTest={connectionTest}
      onSubmit={connectionSubmit}
      img={value.img}
      initialValues={value.formConnection}
      inputs={value.inputs}
      validate={value.validate}
      title={value.title}
      key={indx}
     />
    ))}
    
    {DataBaseInfo.map((item, i) => (
     <button
      key={i}
      // onClick={open}
      className="rounded-3xl bg-white w-full drop-shadow-[0_4px_4px_#00000017] h-full items-center"
     >
      <figure className="py-6 h-full flex flex-col gap-5 w-full items-center">
       {item.img === microsoftazureLogo ? (
        <img
         src={item.img}
         alt={item.button}
         className="max-h-14 max-w-[90%] flex"
        />
       ) : (
        <img
         src={item.img}
         alt={item.button}
         className="max-h-20 max-w-[90%] flex"
        />
       )}
       <figcaption className="text-start font-bold md:text-[1.5vw] lg:text-[1.1vw] text-primary bg-secondary-primary-lite px-4 py-2 rounded-full mt-auto">
        <h2 className="flex items-center justify-center gap-3 text-center">
         {item.button}
        </h2>
       </figcaption>
      </figure>
     </button>
    ))}
   </div>
  </div>
 );
}
