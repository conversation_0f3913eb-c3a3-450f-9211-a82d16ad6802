import ApiS2 from "@/Api/apiS2Config";
import Loading from "@/Components/Loading";
import {
 Button,
 Checkbox,
 Pagination,
 ScrollArea,
 Table,
 TextInput,
 rem,
} from "@mantine/core";
import cx from "clsx";
import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { CiSearch } from "react-icons/ci";
import { IoFilter } from "react-icons/io5";
import { MdDelete, MdModeEdit } from "react-icons/md";
import { RiDeleteBin6Line } from "react-icons/ri";

const TableOfData = () => {
 const { t } = useTranslation();
 const [selection, setSelection] = useState([]);
 const [data, setData] = useState();

 const [loadingState, setLoadingState] = useState({});
 const [currentPage, setCurrentPage] = useState(1);
 const rowsPerPage = 10;
 const totalPages = Math.ceil(data?.length / rowsPerPage);

 const [loading, setLoading] = useState(false);
 const [GeneratedLoading, setGeneratedLoading] = useState(false);
 const toggleLoading = (id, value) => {
  setLoadingState((prevState) => ({
   ...prevState,
   [id]: value,
  }));
 };
 const getTableData = async () => {
  try {
   setLoading(true);
   const { data } = await ApiS2.get("/suppliers/list-company-suppliers");
   setLoading(false);
   // const reversedData = data ? [...data].reverse() : [];
   setData(data);
   // //console.log(data);
  } catch (error) {
   setLoading(false);
   //console.log(error);
  }
 };
 const getGeneratedToken = async (SupplierEmail, id) => {
  try {
   //console.log(id);

   //  SuppliersEmail && setGeneratedLoading(true);
   const { data } = await ApiS2.post(
    `/suppliers/generate-supplier-token`,
    {},
    { headers: { supplierId: id } }
   );
   //  SuppliersEmail && setGeneratedLoading(false);

   // setData(data);
   //console.log(data);
   const supplierTokenUrl = `https://portal.levelupesg.co/PublicSupplyChain?token=${data?.supplier_token?.replace(
    " ",
    "%20"
   )}`;

   window.location.href = `mailto:${
    SupplierEmail || SuppliersEmail
   }?subject=Supplier%20Emissions%20Data%20Collection%20-%20Your%20Access&body=Dear%20Business%20Partner,%0D%0A%0D%0AWe're%20providing%20you%20with%20unique%20access%20to%20collaborate%20on%20collecting%20emissions%20data.%20This%20initiative%20is%20crucial%20for%20our%20sustainability%20efforts.%0D%0A%0D%0APlease%20submit%20your%20data%20after%205%20hours%20from%20receiving%20this%20mail.%0D%0A%0D%0AURL%20Link%20:%20%0D%0A%0D%0A${encodeURIComponent(
    supplierTokenUrl
   )}%0D%0A%0D%0APlease%20copy%20the%20URL%20and%20paste%20it%20in%20your%20browser.%0D%0A%0D%0AThank%20you%20for%20your%20cooperation.%0D%0A%0D%0ABest%20regards,%0D%0ALevelUp%20ESG!`;
  } catch (error) {
   //  SuppliersEmail && setGeneratedLoading(false);
   //console.log(error);
  }
 };
 useEffect(() => {
  getTableData();
 }, []);
 const currentData = useMemo(() => {
  return (
   data?.slice((currentPage - 1) * rowsPerPage, currentPage * rowsPerPage) || []
  );
 }, [data, currentPage, rowsPerPage]);

 const toggleRow = (id) =>
  setSelection((current) =>
   current.includes(id)
    ? current.filter((item) => item !== id)
    : [...current, id]
  );
 const toggleAll = () =>
  setSelection((current) =>
   current.length === data.length ? [] : data.map((item) => item.id)
  );
 const SuppliersEmail = currentData?.map((item) => item.contact);
 const rows = currentData.map((item) => {
  const selected = selection.includes(item.id);
  const del = (e) => {
   e.target.closest("tr").remove();
  };
  const isLoading = loadingState[item.id];
  return (
   <Table.Tr
    key={item.id}
    className={`${cx({
     ["bg-[#07838F1A]"]: selected,
    })} text-sm font-bold text-[#626364] text-left`}
   >
    <Table.Td>
     <span className="flex justify-center items-center text-xl" onClick={del}>
      <RiDeleteBin6Line />
     </span>
    </Table.Td>
    <Table.Td>
     <Checkbox
      checked={selection.includes(item.id)}
      onChange={() => toggleRow(item.id)}
      color="#07838F"
     />
    </Table.Td>
    <Table.Td>{item.name}</Table.Td>
    <Table.Td>{item.category}</Table.Td>
    <Table.Td>{item.contact}</Table.Td>
    <Table.Td>{item.fromContactDate}</Table.Td>
    <Table.Td>{item.toContactDate}</Table.Td>
    <Table.Td>{item.region}</Table.Td>
    <Table.Td>{item.sector}</Table.Td>
    <Table.Td>{item.services}</Table.Td>
    <Table.Td>
     <Button
      className="bg-secondary-300 hover:bg-secondary-300 text-white py-1 px-3 rounded-lg "
      onClick={() => {
       toggleLoading(item.id, true);
       getGeneratedToken(item.contact, item.id)
        .then(() => {
         toggleLoading(item.id, false);
        })
        .catch(() => {
         toggleLoading(item.id, false);
        });
      }}
     >
      {isLoading ? <Loading /> : "Request"}
     </Button>
    </Table.Td>
   </Table.Tr>
  );
 })

 return (
  <>
   {loading ? (
    <Loading />
   ) : (
    <div className="bg-[#F7F4F4] mt-7">
     <div className="p-2 my-1 bg-white shadow-lg rounded-xl">
      <div className="grid items-center justify-end px-4 bg-white grid-cols-1 lg:grid-cols-2 rounded-xl">
       <div className="sm:flex sm:justify-around lg:justify-start">
        <div className="Delete-Suppliers-Information">
         <Button className="text-black bg-transparent hover:bg-transparent hover:text-black">
          <MdDelete className="me-1" />
          Delete
         </Button>
        </div>
        <div className="Edit-Suppliers-Information">
         <Button
          className="text-black bg-transparent hover:bg-transparent hover:text-black rounded-none "
          // onClick={''}
         >
          <MdModeEdit className="me-1" />
          Edit
         </Button>
        </div>
        {/* <div>
         <Button
          className="text-white bg-secondary-300  hover:bg-secondary-300 hover:text-white rounded-lg lg:ms-5"
          onClick={
           GeneratedLoading ? (
            <Loading />
           ) : (
            () => getGeneratedToken(null, SuppliersEmail)
           )
          }
         >
          {GeneratedLoading ? <Loading /> : "Request All"}
         </Button>
        </div> */}
       </div>
       <div>
        <div className="grid items-start justify-center grid-cols-1 md:grid-cols-3 lg:grid-cols-4 sm:items-center gap-x-5">
         <div className="col-span-1  m-3 w-full mx-auto">
          <p className="font-semibold  bg-[#EBEBEB] p-2 text-[#00C0A9] rounded-lg shadow-sm flex items-center w-full justify-center">
           <span className="text-black">
            <IoFilter className="mx-2" />
           </span>
           Filter
          </p>
         </div>

         <div className="col-span-1 m-3 py-2 px-4 bg-[#EBEBEB] rounded-lg shadow-sm w-full mx-auto">
          <p className="font-semibold text-[#9C9C9C] text-center">Columns</p>
         </div>

         <TextInput
          className="w-full col-span-1 lg:col-span-2"
          placeholder="Search by Topic Area or Assessment Question"
          rightSection={<CiSearch className="w-5 h-5" />}
          // value={''}
          // onChange={''}
          // disabled
         />
        </div>
       </div>
      </div>
     </div>
     <ScrollArea>
      <Table
       miw={800}
       verticalSpacing="sm"
       className="bg-white shadow-lg rounded-xl"
      >
       <Table.Thead className="border-b-2 border-primary pb-6 text-primary font-bold text-base text-center">
        <Table.Tr>
         <Table.Th className="text-center">Delete</Table.Th>
         <Table.Th style={{ width: rem(40) }}>
          <Checkbox
           onChange={toggleAll}
           checked={selection.length === data?.length}
           indeterminate={
            selection.length > 0 && selection.length !== data?.length
           }
           color="#07838F"
          />
         </Table.Th>
         <Table.Th className="text-left">Name</Table.Th>
         <Table.Th className="text-left">Supplies Category</Table.Th>
         <Table.Th className="text-left">Contact</Table.Th>
         <Table.Th className="text-left">Date of contact</Table.Th>
         <Table.Th className="text-left">To</Table.Th>
         <Table.Th className="text-left">Region</Table.Th>
         <Table.Th className="text-left">Sector</Table.Th>
         <Table.Th className="text-left">Services</Table.Th>
         <Table.Th className="text-left Send-req-Suppliers-Information">Action</Table.Th>
        </Table.Tr>
       </Table.Thead>
       <Table.Tbody>{rows}</Table.Tbody>
      </Table>
     </ScrollArea>
     <div className="md:flex justify-between mt-5">
      <p className="text-sm text-secondary-300">
       {t("showingData", {
        start: (currentPage - 1) * rowsPerPage + 1,
        end: Math.min(currentPage * rowsPerPage, data?.length),
        total: data?.length,
       })}
      </p>
      <Pagination
       page={currentPage}
       onChange={(e) => {
        setCurrentPage(e);
        // setEdit(false);
       }}
       total={totalPages}
       color="#00c0a9"
       // className={`mt-5 ${DueDateError||actionItemsError?'cursor-not-allowed':''}`}
       // disabled={DueDateError||actionItemsError}
       className="flex justify-center mt-5 md:mt-0"
      />
     </div>
    </div>
   )}
  </>
 );
};
export default TableOfData;
