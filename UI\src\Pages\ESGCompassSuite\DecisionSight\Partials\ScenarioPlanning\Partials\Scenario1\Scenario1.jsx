import React from "react";
import { Donut<PERSON>hart } from "@mantine/charts";
import { Bar<PERSON><PERSON> } from "@mantine/charts";

const Scenario1 = () => {
  const data = [
    { name: "USA", value: 400, color: "indigo.6" },
    { name: "India", value: 300, color: "yellow.6" },
    { name: "Japan", value: 100, color: "teal.6" },
    { name: "Other", value: 200, color: "gray.6" },
  ];
  const data2 = [
    { month: "January", Smartphones: 1200, Laptops: 900, Tablets: 200 },
    { month: "February", Smartphones: 1900, Laptops: 1200, Tablets: 400 },
    { month: "March", Smartphones: 400, Laptops: 1000, Tablets: 200 },
    { month: "April", Smartphones: 1000, Laptops: 200, Tablets: 800 },
    { month: "May", Smartphones: 800, Laptops: 1400, Tablets: 1200 },
    { month: "June", Smartphones: 750, Laptops: 600, Tablets: 1000 },
  ];
  return (
    <div>
      <div className="flex flex-col gap-4">
        <div className='flex flex-col gap-4 rounded-xl bg-[#dfedf0] p-4 border-[#E8E7EA] border-2 dark:bg-[#282828] dark:text-white dark:border-black'>
          ANALYSIS RESULTS
        </div>
        <div className="flex flex-row w-full justify-between bg-[#dfedf0] p-4 items-center gap-4 dark:bg-[#282828] dark:text-white dark:border-black">
          <div className="w-1/2 ">
            <DonutChart 
            data={data} 
            chartLabel="Scenario Impact" 
            withLabelsLine 
            labelsType="value" 
            withLabels
            size={300} thickness={30}
            tooltipDataSource="segment"
            />
          </div>
          <div className="w-1/2">
            <BarChart
              h={300}
              data={data2}
              dataKey="month"
              orientation="vertical"
              yAxisProps={{ width: 80 }}
              barProps={{ radius: 10 }}
              series={[{ name: "Smartphones", color: "blue.6" }]}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Scenario1;
