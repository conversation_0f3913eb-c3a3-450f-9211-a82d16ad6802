import os
from dotenv import load_dotenv
from azure.storage.blob import BlobServiceClient, BlobClient, ContainerClient

# Get the path to the root directory of the repository
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables from .env file in the root directory
load_dotenv(os.path.join(root_dir, '.env'))


# Function to upload PDF files to an Azure Blob Storage container
def upload_pdfs_to_azure_and_retrieve_uri(file_name):
    # Load the connection string and container name from the environment variables file
    connection_string = os.environ.get('AZURE_STORAGE_CONNECTION_STRING')
    container_name = os.environ.get('AZURE_STORAGE_CONTAINER_NAME')

    blob_service_client = BlobServiceClient.from_connection_string(connection_string)
    blob_container_client = blob_service_client.get_container_client(container_name)

    current_dir = os.getcwd()

    
    if file_name.endswith('.pdf'):
        file_path = os.path.join(current_dir, file_name)
        blob_client = blob_container_client.get_blob_client(file_name)
    
        print(file_path)
        with open(file_path, 'rb') as f:
            blob_client.upload_blob(f)

        # After uploading the file to Azure, delete it from the local directory
        # No need to make a check that they are uploaded first as
        # The upload_blob function in the Azure SDK is a blocking operation, 
        # which means that it doesn't return until the file has been fully uploaded. 
        # Therefore, the os.remove(file_path) line won't be executed until the file upload is complete.

        # os.remove(file_path)
        blob_service_client = BlobServiceClient.from_connection_string(connection_string)
        blob_client = blob_service_client.get_blob_client(container_name, file_name)
        return blob_client.url
# testing-function-output

#print(upload_pdfs_to_azure_and_retrieve_uri("ResiliencyReport-ResiliencyReport-levelupwww-2024-07-25T11-42-32_103Z.pdf"))