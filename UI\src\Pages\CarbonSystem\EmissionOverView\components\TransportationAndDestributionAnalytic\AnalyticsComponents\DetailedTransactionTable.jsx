import { ExportIcon } from "@/assets/icons/GeneralIcons";
import DeleteModal from "@/Components/Modals/DeleteModal";
import {
  Button,
  Checkbox,
  Input,
  Pagination,
  ScrollArea,
  Select,
  Table,
} from "@mantine/core";
import { Search } from "lucide-react";
import { useState } from "react";
import { CiEdit } from "react-icons/ci";
import { IoEyeOutline } from "react-icons/io5";
import { Link } from "react-router-dom";

const TABLE_HEADERS = [
  "Company",
  "Industry",
  "E",
  "S",
  "G",
  "YOY Change",
  "Last Assessment",
];
const companiesData = [
  {
    company: "xxx",
    industry: "xxx",
    E: "92%",
    S: "80%",
    G: "84%",
    YoYChange: "+8%",
    lastAssessment: "2 Apr 2025",
  },
  {
    company: "xxx",
    industry: "Technology",
    E: "78%",
    S: "68%",
    G: "70%",
    YoYChange: "+12%",
    lastAssessment: "12 Apr 2025",
  },
  {
    company: "xxxx",
    industry: "Manufacturing",
    E: "66%",
    S: "60%",
    G: "64%",
    YoYChange: "+4%",
    lastAssessment: "22 Mar 2025",
  },
  {
    company: "Retail xxx",
    industry: "Retail",
    E: "58%",
    S: "70%",
    G: "60%",
    YoYChange: "+15%",
    lastAssessment: "5 Apr 2025",
  },
  {
    company: "Logistics Plus",
    industry: "Transportation",
    E: "51%",
    S: "62%",
    G: "65%",
    YoYChange: "-2%",
    lastAssessment: "30 Mar 2025",
  },
  {
    company: "Finservices Group",
    industry: "Finance",
    E: "52%",
    S: "65%",
    G: "62%",
    YoYChange: "-3%",
    lastAssessment: "20 Mar 2025",
  },
];

const DetailedTransactionTable = () => {
  const PAGE_SIZE = 5;
  const [page, setPage] = useState(1);
  const [selected, setSelected] = useState([]);
  const [selectAll, setSelectAll] = useState(false);

  const paginatedData = [""].slice((page - 1) * PAGE_SIZE, page * PAGE_SIZE);
  const allCurrentPageSelected =
    paginatedData.every((row) => selected.includes(row)) &&
    paginatedData.length > 0;
  const handleSelectAll = (checked) => {
    setSelectAll(checked);
    if (checked) {
      const currentPageIds = paginatedData.map((row) => row);
      setSelected((prev) => Array.from(new Set([...prev, ...currentPageIds])));
    } else {
      const currentPageIds = paginatedData.map((row) => row);
      setSelected((prev) => prev.filter((id) => !currentPageIds.includes(id)));
    }
  };

  const handleSelectRow = (id, checked) => {
    if (checked) {
      setSelected((prev) => [...prev, id]);
    } else {
      setSelected((prev) => prev.filter((item) => item !== id));
    }
  };
  return (
    <div>
      <div className="flex items-center justify-between lg:flex-row flex-col gap-2">
        Detailed Transaction Table
        <Button className="bg-primary hover:bg-primary/70 text-white px-4 py-2 rounded-md ml-auto flex items-center justify-between gap-4">
          <ExportIcon />
          <span className="mx-2">Export</span>
        </Button>
      </div>

      <div className="flex lg:flex-row flex-col gap-2 mt-5">
        <Input
          className="w-full"
          placeholder="Search ..."
          leftSection={<Search size={16} />}
        />

        <Select placeholder="Filter" data={["1", "2", "3", "4"]} />
      </div>

      <div className=" bg-white relative overflow-y-hidden mt-5">
        <ScrollArea>
          <Table highlightOnHover verticalSpacing="sm">
            <Table.Thead bg={"#F5F4F5"}>
              <Table.Tr>
                <Table.Th>
                  <Checkbox
                    checked={allCurrentPageSelected}
                    indeterminate={
                      selected.some((id) => paginatedData.map((row) => row)) &&
                      !allCurrentPageSelected
                    }
                    onChange={(event) =>
                      handleSelectAll(event.currentTarget.checked)
                    }
                    aria-label="Select all URLs"
                  />
                </Table.Th>
                {TABLE_HEADERS.map((header) => (
                  <Table.Th key={header}>{header}</Table.Th>
                ))}
                <Table.Th>
                  Actions
                </Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              <Table.Td></Table.Td>

              {companiesData.map((row,i) => (
                <Table.Tr key={i}>
                  <Table.Td>
                    <Checkbox
                      checked={selected.includes(i)}
                      onChange={(event) =>
                        handleSelectRow(i, event.currentTarget.checked)
                      }
                      aria-label={`Select URL ${i}`}
                    />
                  </Table.Td>
                  <Table.Td>
                      {row.company}
                  </Table.Td>
                <Table.Td>
                  {row.industry}
                </Table.Td>
                <Table.Td>
                  {row.E}
                </Table.Td>
                <Table.Td>
                  {row.S}
                </Table.Td>
                <Table.Td>
                  {row.G}
                </Table.Td>
                <Table.Td>
                  {row.YoYChange}
                </Table.Td>
                <Table.Td>
                  {row.lastAssessment}
                </Table.Td>
                <Table.Td>
                
              <div className="flex w-fit gap-2 p-3">
                <Link
                  // to={`/RiskIdentificationAndAssessment/edit-risk/${"item._id"}?type=view`}
                  className="w-8 h-8 bg-[#F5F4F5] rounded-md flex items-center justify-center"
                >
                  <IoEyeOutline className="text-primary" size={15} />
                </Link>

                <Link
                  // to={`/RiskIdentificationAndAssessment/edit-risk/${"item._id"}?type=edit`}
                  className="w-8 h-8 bg-[#F5F4F5] rounded-md flex items-center justify-center"
                >
                  <CiEdit className="text-primary" size={15} />
                </Link>

                <DeleteModal
                  sType="s3"
                  apiLink={`/risk/`}
                  // refreshFn={refetchFn}
                />
              </div>
                </Table.Td>
                
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </ScrollArea>
        <div className="flex items-center justify-between mt-4">
          <span className="text-sm text-gray-500">
            Showing {(page - 1) * PAGE_SIZE + 1} to{" "}
            {Math.min(page * PAGE_SIZE, ["urls"].length)} of {[""].length}
          </span>
          <Pagination
            total={Math.ceil([""].length / PAGE_SIZE)}
            value={page}
            onChange={setPage}
            size="sm"
            radius="xl"
          />
        </div>
      </div>
    </div>
  );
};

export default DetailedTransactionTable;
