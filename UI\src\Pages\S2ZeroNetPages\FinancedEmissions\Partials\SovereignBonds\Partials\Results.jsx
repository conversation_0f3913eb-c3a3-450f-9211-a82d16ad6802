import { useSovereignBondsStore } from '@/Store/useSovereignBondsStore';
import React from 'react'

const Results = () => {
    const {
        attribution_percentage,
        financed_emissions,
        emissions_per_sm_invested
    } = useSovereignBondsStore();
  return (
    <div>
        <div className='flex flex-col gap-4 rounded-xl bg-white p-4 border-[#E8E7EA] border-2'>
            <h1 className='text-xl font-bold p-2 rounded-xl'>
                Results
            </h1>
            <table className="w-full">
            <thead className="bg-[#F5F4F5]">
                <tr className='font-bold'>
                <th className="text-start p-3 ">Metric</th>
                <th className="text-start p-3 ">Value</th>
                </tr>
            </thead>
            <tbody>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Financed Emissions </td>
                    <td className="p-3 border border-gray-300">{financed_emissions} tCO2e/year </td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Attribution Percentage</td>
                    <td className="p-3 border border-gray-300">{attribution_percentage}%</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Emissions Per SM Invested</td>
                    <td className="p-3 border border-gray-300">{emissions_per_sm_invested} tCO2e/SM</td>
                </tr>
            </tbody>
            </table>
        </div>
    </div>
  )
}

export default Results