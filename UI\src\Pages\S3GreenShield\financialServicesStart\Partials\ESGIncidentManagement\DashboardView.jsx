import useSideBarRoute from "@/hooks/useSideBarRoute";
import { Tabs } from "@mantine/core";
import { useEffect, useState } from "react";
import Dashboard from "./Dashboard/Dashboard";
import FeedbackAnalysis from "./FeedbackAnalysis/FeedbackAnalysis";
import FeedbackCollection from "./FeedbackCollection/FeedbackCollection";
import StakeholderInteractions from "./StakeholderInteractions/StakeholderInteractions";
import { notifications } from "@mantine/notifications";
import { useLocation } from "react-router";

import ApiS3 from "@/Api/apiS3";
import MainLayout from "@/Layout/MainLayout";
import { IoMdHome } from "react-icons/io";

export default function DashboardView() {
  const { greenShieldESGIncidentManagement } = useSideBarRoute();
  const [activeTab, setActiveTab] = useState("Dashboard");
  const [filteredData, setFilteredData] = useState([]);
  const [loading, setLoading] = useState(false);
  const { pathname } = useLocation();
  const fetchData = async () => {
    try {
      setLoading(true);
      const { data } = await ApiS3.get("stakeholder-interactions");
      console.log(data);
      setFilteredData(data);
    } catch (error) {
      console.log(error);
      notifications.show({
        title: "Error",
        message: "Failed to fetch Recent Interactions",
        color: "red",
      });
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchData();
  }, []);
  const links = [
    { value: "Dashboard", label: "Dashboard" },
    { value: "Stakeholder Interactions", label: "Stakeholder Interactions" },
    { value: "Interactions Analysis", label: "Interactions Analysis" },
    { value: "Questionnaire", label: "Questionnaire" },
  ];
  return (
    <MainLayout
      menus={greenShieldESGIncidentManagement}
      navbarTitle="Stakeholder Engagement"
      breadcrumbItems={[
        { title: <IoMdHome size={20}/>, href: "/get-started" },
        { title: `${activeTab.replace(/-/, " ")}`, href: `${pathname}` },
      ]}
    >
      <Tabs className="" defaultValue={activeTab} onChange={setActiveTab}>
        <Tabs.List className="grid md:grid-cols-4 justify-center bg-[#FFFFFF] border-2 border-[#E8E7EA] p-6 gap-5 py-3 mb-6 rounded-lg text-primary">
          {links.map((link) => (
            <Tabs.Tab
              key={link.value}
              value={link.value}
              className={`relative text-lg font-semibold py-3 px-6 ${
                activeTab === link.value
                  ? " active-tab rounded border-none shadow-none"
                  : "text-[#5A5A5A] rounded-lg border border-[#E8E7EA]"
              } `}
            >
              {link.label}
            </Tabs.Tab>
          ))}
        </Tabs.List>

        <Tabs.Panel value="Dashboard">
          <Dashboard filteredData={filteredData} loading={loading} />
        </Tabs.Panel>
        <Tabs.Panel value="Stakeholder Interactions">
          <StakeholderInteractions data={filteredData} loading={loading} />
        </Tabs.Panel>
        <Tabs.Panel value="Interactions Analysis">
          <FeedbackAnalysis />
        </Tabs.Panel>
        <Tabs.Panel value="Questionnaire">
          <FeedbackCollection />
        </Tabs.Panel>
      </Tabs>Dashboard
    </MainLayout>
  );
}
