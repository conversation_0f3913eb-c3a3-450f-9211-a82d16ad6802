import MainLayout from "@/Layout/MainLayout";
import { BioEnergyGSIcon, EcoPowerGSIcon } from "@/assets/icons";
import useSideBarRoute from "@/hooks/useSideBarRoute";

import GreenSightCard from "./GreenSightCard";
import { IoMdHome } from "react-icons/io";

const sections = [
  {
    id: 1,
    icon: <EcoPowerGSIcon />,
    title: "Sustain360",
    path: "summary",
    description:
      "Cutting-edge proprietary system to assess and enhance your ESG maturity journey: ",
    advantages: [
      "Intuitive ESG analytics, dashboard and sharable report",
      "AI-powered ESG SWOT analysis",
      "Strategic prioritised action plans and progress tracking",
    ],
  },
  {
    id: 2,
    icon: <BioEnergyGSIcon />,
    title: "Sustain360 Portfolio",
    path: "Portfolio",
    description:
      "Cutting-edge proprietary system to assess and enhance your Portfolio's ESG maturity journey: ",
    advantages: [
      "Portfolio-wide ESG materiality assessment",
      "AI-powered ESG SWOT analysis",
      "Strategic prioritised action plans",
      "Progress tracking and monitoring",
    ],
  },
  // {
  //   id: 3,
  //   icon: <BioEnergyGSIcon />,
  //   title: "Sustain360 for Financial Services",
  //   path: "financial",
  //   description:
  //     "Specialised system designed for financial institutions to assess and enhance ESG maturity: ",
  //   advantages: [
  //     "Finance sector-specific ESG assessment",
  //     "AI-powered ESG SWOT analysis",
  //     "Strategic prioritised action plans",
  //     "Progress tracking and benchmarking",
  //   ],
  // },
];

const GreenSight = () => {
  const { esgAssessmentMenu } = useSideBarRoute();

  return (
    <MainLayout
      menus={esgAssessmentMenu}
      navbarTitle={"Sustain360 AI"}
      breadcrumbItems={[
        { title: <IoMdHome size={20} />, href: "/get-started" },
        { title: "Sustain360 AI", href: "#" },
      ]}
    >
      <div className="flex flex-col gap-5 p-20 w-full">
        {sections.map((section) => (
          <GreenSightCard key={section.id} section={section} />
        ))}
      </div>
    </MainLayout>
  );
};

export default GreenSight;
