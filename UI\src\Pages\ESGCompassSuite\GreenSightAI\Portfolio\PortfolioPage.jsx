import MainLayout from "@/Layout/MainLayout";
import { Input, NativeSelect } from "@mantine/core";
import { IconChevronDown } from "@tabler/icons-react";
import { useEffect, useState } from "react";
import { FaSearch } from "react-icons/fa";
import { IoMdHome } from "react-icons/io";
import ApiSustain360 from "@/Api/apiSustain360";
import Loading from "@/Components/Loading";
import { AreaChart, BarChart, DonutChart } from "@mantine/charts";
import PortfolioCompanies from "./Partials/PortfolioCompanies";

const formatDate = (isoDate) => {
  const options = {
    year: "numeric",
    month: "long",
    day: "2-digit",
  };
  return new Date(isoDate).toLocaleDateString("en-US", options);
};

export default function PortfolioPage() {
  const [portfolioData, setPortfolioData] = useState([]);
  const [allPortfolioData, setAllPortfolioData] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true); // Loading state
  const [error, setError] = useState(null); // Error state
  const [maturityLineData, setMaturityLineData] = useState([]);
  const [barChartData, setBarChartData] = useState([]);
  const [donutChartData, setDonutChartData] = useState([]);
  const [search, setSearch] = useState("");

  useEffect(() => {
    setLoading(true);
    Promise.all([getPortfolioData(), getAllPortfolioData()]).then(() => {
      setLoading(false);
    }).catch((error) => {
      setError(error.message);
      console.log("Error", error);
      setLoading(false);
    });
  }, []);

  async function getPortfolioData() {
    try {
      const response = await ApiSustain360.get("/api/portfolio");
      setPortfolioData(response.data);
      setTasks([
        {
          name: "Portfolio Average",
          value: `${response.data.PortfolioHistory.average_total_percentage.toFixed(2)}%`,
          // change: "+7% year-over-year",
          type: "",
        },
        {
          name: "Environmental",
          value: `${response.data.PortfolioHistory.average_environmental_percentage.toFixed(2)}%`,
          change: "",
          type: "",
        },
        {
          name: "Social",
          value: `${response.data.PortfolioHistory.average_social_percentage.toFixed(2)}%`,
          change: "",
          type: "",
        },
        {
          name: "Governance",
          value: `${response.data.PortfolioHistory.average_governance_percentage.toFixed(2)}%`,
          change: "",
          type: "",
        },
      ]);
      const data = response.data.PortfolioHistory.maturity_level_history.map((item) => ({
        Assessment: item?.score_percentage,
        date: item?.date,
      }));
      setMaturityLineData(data);
      const barData = response.data.PortfolioHistory.maturity_level_history.map((item) => ({
        Project: item?.department_project_id || "Company",
        Score: item?.score_percentage,
        date: item?.date,
      }));
      let donutChart = response.data.PortfolioHistory.maturity_level_counts;
      let donutChartData = [];
      for (const [key, value] of Object.entries(donutChart)) {
        donutChartData.push({
          name: key,
          value: value,
          color: statuses.find((status) => status.name === key)?.color,
        });
      }
      setBarChartData(barData);
      setDonutChartData(donutChartData);
    } catch (error) {
      setError(error.message);
    } 
  }

  async function getAllPortfolioData() {
    try {
      const response = await ApiSustain360.get("/api/portfolio/all");
      setAllPortfolioData(response.data);
    } catch (error) {
      setError(error.message);
    }
  }

  const statuses = [
    {
      name: "Maturing",
      color: "#FFD700",
    },
    {
      name: "Beginning",
      color: "#00FA9A",
    },
    {
      name: "Leading",
      color: "#00BFFF",
    },
    {
      name: "Advancing",
      color: "#4169E1",
    },
    {
      name: "Developing",
      color: "#6A0DAD",
    },
  ];

  // if (loading) return <div>Loading...</div>;
  // if (error) return <div>Error: {error}</div>;

  return (
    <MainLayout
      navbarTitle="Sustain360 Portfolio"
      breadcrumbItems={[
        { title: <IoMdHome size={20} />, href: "/get-started" },
        { title: "Sustain360 AI", href: "/Insights-reporing/greensight" },
        { title: "Sustain360 Portfolio", href: "#" },
      ]}
    >
      {loading ? (<Loading />):
      (<div className="w-full h-[80%] overflow-hidden pr-2 overflow-y-auto flex flex-col items-center gap-6">
        {/* Welcome Section */}
        <div className="rounded-lg border-[#E8E7EA] border bg-white p-4 w-full">
          <h1 className="text-2xl font-bold mb-2">Portfolio Dashboard</h1>
          <p className="text-[#8D8D8D]">
            Overview of 8 portfolio companies • Last updated:{" "}
            {formatDate(portfolioData.updatedAt)}
          </p>
          <div className="mt-4 grid grid-cols-4 w-full">
            {tasks.map((task, index) => (
              <div
                key={index}
                className={`border-[#E8E7EA] border p-4 ${
                  index == 0
                    ? "rounded-l-lg border-r-0"
                    : index == tasks.length - 1
                    ? "rounded-r-lg"
                    : "border-r-0"
                }`}
              >
                <h2 className="text-[#7A7A7A]">{task.name}</h2>
                <div className="flex items-end">
                  <span className="text-3xl font-bold mr-2">{task.value}</span>
                  <span
                    className={`text-sm -translate-y-1 ${
                      task.type == "good"
                        ? "text-[#00C0A9]"
                        : task.type == "bad"
                        ? "text-[#EA0B0B]"
                        : "text-[#6B7280]"
                    }`}
                  >
                    {task.change}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Analysis Section */}
        <div className="flex w-full gap-6">
          <div className="rounded-lg border-[#E8E7EA] border bg-white p-4 flex-1 flex flex-col gap-8">
            <h1 className="text-2xl font-bold w-fit text-[#055C64]">
              Maturity Level Distribution
            </h1>
            <div className="w-full px-2 rounded-lg h-full flex justify-center items-center">
              <AreaChart
              h={300}
              data={maturityLineData}
              dataKey="date"
              series={[{ name: "Assessment", color: "blue.6" }]}
              curveType="natural"
              withDots={false}
            />
            </div>
          </div>

          <div className="rounded-lg border-[#E8E7EA] border bg-white p-4 w-fit flex flex-col gap-12">
            <h1 className="text-2xl font-bold w-fit text-[#055C64]">
              Maturity Level Distribution
            </h1>
            <div className="w-fit flex gap-8 justify-between items-center">
              <div className="px-5 w-96">
              <DonutChart
                  data={donutChartData}
                  labelsType="value"
                  withLabelsLine={false}
                  withLabels
                />
              </div>
              <div className="flex flex-col h-fit justify-start w-fit gap-2">
                {statuses.map((status, index) => (
                  <div className="flex gap-2 items-center h-fit" key={index}>
                    <span
                      className={`inline-block aspect-square w-4 rounded-full`}
                      style={{
                        backgroundColor: status.color,
                      }}
                    />
                    <span className="text-[#525252]">{status.name}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Portfolio Progress Over Time Section */}
        <div className="rounded-lg border-[#E8E7EA] border bg-white p-4 w-full flex flex-col gap-8">
          <div className="flex justify-between w-full items-center">
            <h1 className="text-2xl font-bold w-fit text-[#055C64]">
              Portfolio Progress Over Time
            </h1>
            {/* <button className="bg-slate-500 rounded-lg px-5 py-1 font-semibold text-white">
              Export
            </button> */}
          </div>
          <div className="w-full px-2 rounded-lg h-96 flex justify-center items-center">
          <BarChart
              h={300}
              data={barChartData}
              xAxisProps={{
                label: 'Score',
                tickSize: 14,
                domain: [0, 100], // optional: fix range from 0 to 100
                fontSize: 12,
                width: 80,
              }}
              yAxisProps={{
                label: 'Project',
                width: 160,
                angle: 45, // tilt labels for readability
                fontSize: 12,
                
              }}
              dataKey="Project"
              type="normal"
              orientation="vertical"
              series={[{name: 'Company', color: 'blue.6'},
                {name: 'Project', color: 'blue.6'},
                {name: 'Score', color: 'blue.6'},
              ]}
            />
          </div>
        </div>

        {/* Portfolio Companies Section */}
        <div className="rounded-lg border-[#E8E7EA] border bg-white p-4 w-full flex flex-col gap-8">
          <div className="flex justify-between w-full items-center">
            <h1 className="text-2xl font-bold w-fit text-[#055C64]">
              Portfolio Companies
            </h1>
            {/* <button className="bg-slate-500 rounded-lg px-5 py-1 font-semibold text-white">
              Export
            </button> */}
          </div>
          <div className="flex w-full gap-4 items-center">
            <Input
              placeholder="Company Name"
              className="flex-1"
              leftSection={<FaSearch size={16} />}
              onChange={(e) => setSearch(e.target.value)}
            />
            {/* <NativeSelect
              className="w-60"
              rightSection={<IconChevronDown size={16} />}
              data={["Filter 1", "Filter 2"]}
            /> */}
          </div>
          <div className="w-full px-2 rounded-l h-96">
            <PortfolioCompanies allPortfolioData={allPortfolioData.filter((item) => item?.company_name?.toLowerCase()?.includes(search?.toLowerCase()))}/>
          </div>
        </div>
      </div>)}
    </MainLayout>
  );
}
