import CircleNum from "./CircleNum";
import { useNavigate } from "react-router-dom";
import PropTypes from "prop-types";

const Program = ({
  number,
  isActive = true,
  programBadge,
  title,
  programImg,
  description,
  buttonText,
  link,
}) => {
  const navigate = useNavigate();

  return (
    <div className={`m-auto px-6 ${!isActive ? "opacity-60" : null}`}>
      <div
        className="flex flex-col lg:flex-row items-center gap-2 my-6"
        data-aos="zoom-in"
        data-aos-offset="-100"
      >
        <CircleNum CircleImg={programBadge} />

        <div className="flex bg-white rounded-xl shadow-xl overflow-hidden">
          <div className="bg-primary px-5 text-center text-white flex">
            <span className="my-auto font-bold md:text-3xl">{number}</span>
          </div>
          <div className="p-4">
            <h2 className="font-bold max-md:text-center mb-4 px-5 ">{title}</h2>
            <div className="flex flex-col lg:flex-row max-md:items-center gap-2">
              <div className="px-5">
                <img src={programImg} alt={title} />
              </div>
              <div className="w-full lg:w-[400px]">
                <ul className="lg:list-disc">
                  {description.map((item, index) => (
                    <li key={index} className="mb-2">
                      {item}
                    </li>
                  ))}
                </ul>
              </div>
              <div className="flex items-end lg:w-24 max-lg:justify-end">
                <button
                  className={`${
                    !isActive ? "hidden" : null
                  } bg-[#29929b] font-medium px-2 py-1 text-center  text-white w-full rounded-md hover:bg-[#25828a] duration-300`}
                  onClick={() => navigate(link)}
                >
                  {buttonText}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

Program.propTypes = {
  number: PropTypes.number.isRequired,
  isActive: PropTypes.bool.isRequired,
  title: PropTypes.string.isRequired,
  programBadge: PropTypes.object.isRequired,
  programImg: PropTypes.string.isRequired,
  description: PropTypes.arrayOf(PropTypes.string).isRequired,
  buttonText: PropTypes.string.isRequired,
  link: PropTypes.string.isRequired,
};

export default Program;
