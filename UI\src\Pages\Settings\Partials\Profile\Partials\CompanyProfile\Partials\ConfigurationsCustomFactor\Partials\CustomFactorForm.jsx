import {
  Modal,
  Alert,
  Input,
  MultiSelect,
  Select,
  Switch,
} from "@mantine/core";
import { useState } from "react";
import { IoIosArrowDown } from "react-icons/io";
import CustomFactorInputs from "./CustomFactorInputs";
import CustomFactor<PERSON>pi from "./CustomFactorApi";
import Loading from "@/Components/Loading";
import { useDisclosure } from "@mantine/hooks";
import { FaPlus } from "react-icons/fa";

const CustomFactorForm = ({
  uniqueActivites,
  uniqueUom,
  uniqueEFactor,
  assetTypes,
  fetchAgain,
  assetListState,
  assetNamesList,
  uniqueActivitesList,
  uniqueEfactorList,
  uniqueUomList,
  assignEmissionId,
  resetEmissionId,
}) => {
  const [opened, { open, close }] = useDisclosure(false);

  const [selectedAssetType, setSelectedAssetType] = useState("");
  const [selctedEmissionId, setSelctedEmissionId] = useState("");

  const [specificFactor, setSpecificFactor] = useState(false);
  const [isReseting, setIsReseting] = useState(false);

  const [response, setResponse] = useState("");
  const [formState, setFormState] = useState({});
  const [storeKeys, setStoreKeys] = useState({});
  const [allValues, setAllValues] = useState([]);
  let stobj = {};

  const [storedAssetsIds, setStoredAssetsIds] = useState([]);

  const handleToggle = (event) => {
    setSpecificFactor(event.currentTarget.checked);
  };

  /* if one asset selected show emissions on it
   if more than one, start compare the emission inside every asset to check if any value in the all selected assets will show it in the emission selection
*/

  const [selectedEmissionList, setSelectedEmissionList] = useState([]);

  const handleAssetList = (values) => {
    // console.log("🚀 ~ handleAssetList ~ values:", values);
    resetEmissionId();
    setResponse(null);
    setIsReseting(true);
    setSelectedAssetType("");
    setSelectedEmissionList([]);
    setTimeout(() => {
      setIsReseting(false);
    }, 100);
    const assetsItems = assetNamesList.filter((el) =>
      values.includes(el.assetName)
    );
    if (values.length == 0) {
      setStoredAssetsIds([]);
      return;
    }
    // 1. the first selection
    if (assetsItems.length == 1) {
      const emissionList = assetsItems[0].linked_emission_sources.map(
        (em) => em.emission
      );
      setSelectedEmissionList(emissionList);
    } else {
      // Create a map to store emissions by their IDs
      const emissionMap = new Map();

      // // Process first asset's emissions
      assetsItems[0].linked_emission_sources.forEach((emission) => {
        emissionMap.set(emission.id, {
          emission: emission.emission,
          count: 1,
        });
      });

      // Check remaining assets for matches
      for (let i = 1; i < assetsItems.length; i++) {
        const currentAssetEmissionIds = new Set(
          assetsItems[i].linked_emission_sources.map((em) => em.id)
        );

        // Keep only emissions that exist in current asset
        for (const [id, value] of emissionMap) {
          if (currentAssetEmissionIds.has(id)) {
            emissionMap.set(id, {
              ...value,
              count: value.count + 1,
            });
          } else {
            emissionMap.delete(id);
          }
        }

        // If no common emissions found, break early
        if (emissionMap.size === 0) {
          break;
        }
      }

      // Find emissions that appear in ALL assets
      const commonEmissions = Array.from(emissionMap.entries())
        .filter(([_, value]) => value.count === assetsItems.length)
        .map(([id, value]) => ({
          id,
          emission: value.emission,
        }));

      // If no common emissions found, return empty array
      if (commonEmissions.length === 0) {
        setSelectedEmissionList([]);
      } else {
        setSelectedEmissionList(commonEmissions.map((em) => em.emission));
      }
    }
    if (values.length !== 0) {
      const assetsIds = assetsItems.map((el) => el.id);
      setStoredAssetsIds(assetsIds);
    }
  };

  const handleEmissionId = (emission) => {
    const emissionId = assetTypes.find((em) => em.asset == emission).id;
    setSelctedEmissionId(emissionId);
    assignEmissionId(emissionId);
    open();
  };

  const refetchData = () => {
    fetchAgain();
    close();
  };

  return (
    <div style={{ padding: "20px" }}>
      <div className="flex gap-5 item-center">
        <Switch
          className="mt-8 font-bold"
          label="Tier 2 Factor"
          onChange={handleToggle}
        />

        <Input.Wrapper label="Select Assets" className="Select-asset">
          <MultiSelect
            disabled={assetListState.length == 0}
            onChange={handleAssetList}
            placeholder="Enter Assets ..."
            data={assetListState.length > 0 ? assetListState : []}
          />
        </Input.Wrapper>

        {isReseting ? (
          <Loading />
        ) : (
          <Input.Wrapper className="Select-emission"
            error={`${
              storedAssetsIds.length > 0 && selectedEmissionList.length == 0
                ? "No Common Emission from selected assets"
                : ""
            }`}
            label="Select Emission Source"
          >
            <Select
              onChange={(e) => {
                setSelectedAssetType(e);
                handleEmissionId(e);
                setAllValues([
                  {
                    assettype: e,
                  },
                ]);
                setResponse();
                setFormState({});
                setStoreKeys({});
                stobj = {};
              }}
              disabled={selectedEmissionList.length == 0}
              placeholder={`Enter Emission Source ...`}
              data={selectedEmissionList}
              rightSection={<IoIosArrowDown />}
              value={selectedAssetType}
            />
          </Input.Wrapper>
        )}
      </div>
      {isReseting ? (
        <Loading />
      ) : (
        <>
          {(selectedAssetType == "") | (storedAssetsIds.length == 0) ? (
            <Alert
              mt={5}
              mb={5}
              color="orange"
              variant="light"
              className="w-fit"
              title="Select at least one Asset and Emission source"
            ></Alert>
          ) : (
            ""
          )}
          {!storedAssetsIds.length == 0 && (
            <>
              {specificFactor ? (
                <>
                  {/* form component */}
                  <Modal size={"xl"} opened={opened} onClose={close} title="">

                    <div className="p-6">
                      <div className="flex items-center gap-2 mb-6">
                        <div className="w-6 h-6 rounded-xl bg-primary/50 flex items-center justify-center">
                          <FaPlus size={10} className="text-primary" />
                        </div>
                        <h1 className="text-2xl font-bold text-navy-900">
                          Add Custom Factor Tier 2
                        </h1>
                      </div>
                      <hr className="mt-1 mb-3 bg-[#D1D1D1] h-[2px]" />

                      <h2 className="bg-gray-100 py-1 px-1 text-xl font-bold rounded-xl text-navy-900">
                        Custom Factor Tier 2
                      </h2>
                      <CustomFactorInputs
                        fetchAgain={fetchAgain}
                        assetType={selectedAssetType}
                        assetNamesIds={storedAssetsIds}
                        selctedEmissionId={selctedEmissionId}
                        uniqueActivites={uniqueActivites}
                        uniqueUom={uniqueUom}
                        uniqueEFactor={uniqueEFactor}
                        uniqueActivitesList={uniqueActivitesList}
                        uniqueEfactorList={uniqueEfactorList}
                        uniqueUomList={uniqueUomList}
                      />
                    </div>
                  </Modal>
                </>
              ) : (
                <>
                  <Modal size={"xl"} opened={opened} onClose={close} title="" >
                    <div className="p-6">
                      <div className="flex items-center gap-2 mb-6">
                        <div className="w-6 h-6 rounded-xl bg-primary/50 flex items-center justify-center">
                          <FaPlus size={10} className="text-primary" />
                        </div>
                        <h1 className="text-2xl font-bold text-navy-900">
                          Add Custom Factor
                        </h1>
                      </div>
                      <hr className="mt-1 mb-3 bg-[#D1D1D1] h-[2px]" />

                      <h2 className="bg-gray-100 py-1 px-1 text-xl font-bold rounded-xl text-navy-900">
                        Custom Factor
                      </h2>
                      <CustomFactorApi
                        selctedEmissionId={selctedEmissionId}
                        assetNamesIds={storedAssetsIds}
                        fetchAgain={refetchData}
                        assetType={selectedAssetType}
                        allValues={allValues}
                        formState={formState}
                        response={response}
                        setAllValues={setAllValues}
                        setFormState={setFormState}
                        setResponse={setResponse}
                        setStoreKeys={setStoreKeys}
                        stobj={stobj}
                        storeKeys={storeKeys}
                        setSelectedAssetType={setSelectedAssetType}
                      />
                    </div>
                  </Modal>
                </>
              )}
            </>
          )}
        </>
      )}
    </div>
  );
};
export default CustomFactorForm;
