import { useState } from "react";
import S3Layout from "@/Layout/S3Layout";
import useSideBarRoute from "@/hooks/useSideBarRoute";

import { Button } from "@mantine/core";
import {
  ESGAnalyticsStatusUpOutlineIcon,
  ESGAnalyticsStatusUpSolidIcon,
  ESGReportingNoteOutlineIcon,
  ESGReportingNoteSolidIcon,
} from "@/assets/icons";
import ReportingPage from "@/Components/GreenShield/RiskManagement/AnalyticsAndReporting/ReportingPage";
import AnalyticsPage from "@/Components/GreenShield/RiskManagement/AnalyticsAndReporting/AnalyticsPage";

export default function AnalyticsReportingView() {
  const [activeTab, setActiveTab] = useState(1);
  const { ESGRiskManagementMenu } = useSideBarRoute();
  const Buttons = [
    {
      title: "Analytics",
      icon: <ESGAnalyticsStatusUpOutlineIcon />,
      sIcon: <ESGAnalyticsStatusUpSolidIcon />,
      id: 1,
    },
    {
      title: "Reporting",
      icon: <ESGReportingNoteOutlineIcon />,
      sIcon: <ESGReportingNoteSolidIcon />,
      progress: 0,
      id: 2,
    },
  ];

  return (
    <>
      <div className="flex items-center justify-between sm:gap-4 flex-wrap md:flex-nowrap">
        {Buttons.map((item, index) => (
          <Button
            onClick={() => setActiveTab(item.id)}
            key={index}
            variant="default"
            className="w-full"
            unstyled
          >
            <div key={item.id}
              className={`mb-5 flex items-center gap-3  rounded-xl h-16 overflow-hidden shadow-md hover:bg-[#56A1A91A] pr-6 duration-300 ${
                item.id == activeTab ? " bg-[#56A1A91A] " : "bg-white"
              } `}
            >
              <div
                className={`bg-primary  duration-500 ${
                  item.id == activeTab ? "h-full w-2" : "h-0 w-0"
                }`}
              ></div>
              <div
                className={`full-center rounded-full h-11 w-11 bg-bg-lite1 ${
                  activeTab == item.id ? "bg-white" : "ml-4"
                } `}
              >
                {activeTab == item.id ? item.sIcon : item.icon}
              </div>
              <h5
                className={`font-semibold ${
                  activeTab == item.id ? "text-[#07838F]" : "text-[#5A5A5A]"
                } `}
              >
                {item.title}
              </h5>
            </div>
          </Button>
        ))}
      </div>

      {activeTab === 1 && <AnalyticsPage />}
      {activeTab === 2 && <ReportingPage />}
    </>
  );
}
