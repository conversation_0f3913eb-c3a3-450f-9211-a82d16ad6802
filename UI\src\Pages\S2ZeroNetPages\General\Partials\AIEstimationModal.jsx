import {
 AIRecommendations,
 AIRecommendationsIcon,
 LoadingSpinnerIcon,
 RightIcon,
} from "@/assets/svg/ImageSVG";
import { Button, Modal, NumberInput, Select, TextInput } from "@mantine/core";
import { useForm } from "@mantine/form";
import axios from "axios";
import Cookies from "js-cookie";
import { useEffect, useState } from "react";
import { setDate } from "rsuite/esm/internals/utils/date";
import countries from "world-countries";
// import Countries from "../../Measure/Partials/Emissions Breakdown/Partials/Countries/Countries";

const prioritized = ['United Kingdom', 'United Arab Emirates', 'United States', 'Saudi Arabia', 'Qatar'];

// Sort countries: prioritized first, then the rest
const sortedCountries = [
  ...countries.filter(c => prioritized.includes(c.name.common)),
  ...countries.filter(c => !prioritized.includes(c.name.common)).sort((a, b) => a.name.common.localeCompare(b.name.common))
];

export default function AIEstimationModal({ opened, close }) {
 const [emissionsEstimation, setEmissionsEstimation] = useState(null);
 const [loading, SetLoading] = useState(false);
 const [data, SetData] = useState();
 const [showMessage, setShowMessage] = useState(false);
 const form = useForm({
  initialValues: {
   processor: "carbon_estimation",
   resources: {
    company_sector: "",
    industry: "",
    number_of_employees: "",
    country: "",
    office_space: "",
    turnover_rate: "",
    estimated_year: "",
    historical_emissions: "",
   },
  },
  validate: {
   resources: {
    company_sector: (value) => (!value ? "Company Sector is required" : null),
    industry: (value) => (!value ? "Industry is required" : null),
    number_of_employees: (value) =>
     !value ? "Number of Employees is required" : null,
    country: (value) => (!value ? "Country is required" : null),
    office_space: (value) => (!value ? "Office Space is required" : null),
    turnover_rate: (value) => (!value ? "Turnover Rate is required" : null),
    estimated_year: (value) => (!value ? "Estimated Year is required" : null),
    // historical_emissions: (value) =>
    //  emissionsEstimation === "true" && !value
    //   ? "Historical Emissions is required"
    //   : null,
   },
  },
 });

 const handleSubmit = async (values) => {
  SetData(null);
  console.log("Form Values before modification:", values);

  const updatedValues = structuredClone(values);

  if (
   updatedValues.resources.historical_emissions &&
   !String(updatedValues.resources.historical_emissions).includes("TCO2e")
  ) {
   updatedValues.resources.historical_emissions = `${updatedValues.resources.historical_emissions} TCO2e`;
  }
  try {
   SetLoading(true);
   const { data } = await axios.post(
    "https://gen-ai0-staging.azurewebsites.net/process_request",
    updatedValues,
    {
     headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + Cookies.get("level_user_token"),
     },
    }
   );
   SetLoading(false);
   SetData(data);
   setShowMessage(true);
   setTimeout(() => {
    setShowMessage(false);
   }, 1000);

   console.log("Data from AI Estimation:", data);
  } catch (error) {
   SetLoading(false);
   console.error("Error in handleSubmit", error);
  }
 };

 //  useEffect(() => {
 //   if (data && !loading) {
 //    setShowMessage(true);
 //    const timeout = setTimeout(() => {
 //     setShowMessage(false);
 //     SetData(data);
 //    }, 1000);

 //    return () => {
 //     clearTimeout(timeout);
 //     // setShowMessage(false);
 //    };
 //   }
 //  }, [data, loading]);
 return (
  <Modal
   opened={opened}
   onClose={() => {
    close();
    setEmissionsEstimation(null);
    form.reset();
    SetData(null);
   }}
   withCloseButton={false}
   size="50%"
   radius="xl"
  >
   <div>
    <h1 className="flex items-center text-lg font-bold font-inter border-b-2 border-primary pb-2">
     <AIRecommendations />
     <span className="ms-2 bg-gradient-to-r from-[#2C5085] via-[#236D8B] to-[#12B6AD] text-transparent bg-clip-text">
      AI Estimation
     </span>
    </h1>
    <form
     onSubmit={form.onSubmit(handleSubmit)}
     className="grid grid-cols-2 items-center mt-4 gap-2"
    >
     <Select
      label="Company Sector"
      {...form.getInputProps("resources.company_sector")}
      data={[
       "Energy",
       "Materials",
       "Industrials",
       "Consumer Discretionary",
       "Consumer Staples",
       "Health Care",
       "Financial",
       "Information Technology",
       "Communication Services",
       "Utilities",
       "Real Estate",
      ]}
     />
     <TextInput
      label="Industry"
      {...form.getInputProps("resources.industry")}
     />
     <NumberInput
      label="Number of Employees"
      {...form.getInputProps("resources.number_of_employees")}
      hideControls
     />
     <Select
      label="Country"
      {...form.getInputProps("resources.country")}
      data={sortedCountries.map((country) => ({
        value: country.name.common,
        label: country.name.common,
      }))}
     />
     <NumberInput
      label="Office Space"
      {...form.getInputProps("resources.office_space")}
      hideControls
     />
     <NumberInput
      label="Turnover Rate"
      {...form.getInputProps("resources.turnover_rate")}
      hideControls
     />
     <Select
      label="Estimated Year"
      {...form.getInputProps("resources.estimated_year")}
      data={['Previous Year','Current Year','Next Year']}
      hideControls
     />
     <Select
      label="Want to use our data for emissions estimation?"
      data={['Yes', 'No']}
      value={'No'}
      onChange={setEmissionsEstimation}
     />

     {emissionsEstimation === 'Yes' && (
      <NumberInput
       label="Historical Emissions"
       {...form.getInputProps("resources.historical_emissions")}
       hideControls
       rightSection={
        <span className="text-xs ps-1 me-5 h-full flex items-center bg-white">
         TCO2e
        </span>
       }
      />
     )}

     <Button
      type="submit"
      className={`bg-gradient-to-r from-[#2C5085] via-[#236D8B] to-[#12B6AD] border-0 col-span-2 ${
       emissionsEstimation !== 'Yes' ? " mt-4" : " mt-auto"
      }`}
      // loading={loading}
      disabled={loading}
     >
      Estimate Emissions
     </Button>
    </form>

    {!data && !loading && (
     <div className="flex flex-col items-center justify-center mt-4">
      <h1 className="flex flex-col items-center text-lg font-medium font-inter text-gray-600">
       <AIRecommendationsIcon />
       Ready to Calculate
      </h1>
      <p className="text-base font-normal font-inter text-gray-400">
       Select your parameters and click Estimate
      </p>
     </div>
    )}
    {!data && loading && (
     <div className="flex flex-col items-center justify-center mt-4">
      <h1 className="flex flex-col items-center text-lg font-medium font-inter text-gray-600 animate-spin">
       <LoadingSpinnerIcon />
      </h1>
      <p className="text-base font-normal font-inter text-gray-600">
       Our AI is doing its magic please wait...
      </p>
     </div>
    )}
    {showMessage ? (
     <div className="flex flex-col items-center justify-center mt-4">
      <h1 className="flex flex-col items-center text-lg font-medium font-inter text-gray-600">
       <RightIcon />
      </h1>
      <p className="text-base font-normal font-inter text-gray-600">
       Estimation Complete
      </p>
     </div>
    ) : (
     data && (
      <div className="mt-5">
       <p className="border-b-2  pb-4 text-center text-sm font-medium font-inter text-primary flex flex-col">
        Estimated Emissions For
        <span className="text-gray-500 text-lg font-bold">2025</span>
       </p>
       <p className="pb-4 text-center text-lg font-bold font-inter text-black mt-5">
        {data?.ai_response?.value}
       </p>
      </div>
     )
    )}
   </div>
  </Modal>
 );
}
