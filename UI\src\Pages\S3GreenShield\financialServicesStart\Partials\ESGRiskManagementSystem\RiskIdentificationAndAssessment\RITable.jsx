import * as React from "react";
import { useCustom } from "@table-library/react-table-library/table";
import { CompactTable } from "@table-library/react-table-library/compact";
import { useTheme } from "@table-library/react-table-library/theme";
import {
  DEFAULT_OPTIONS,
  getTheme,
} from "@table-library/react-table-library/mantine";
import { usePagination } from "@table-library/react-table-library/pagination";
import { useRowSelect } from "@table-library/react-table-library/select";
import { useSort } from "@table-library/react-table-library/sort";
import { Group, Checkbox, Pagination, Button, ActionIcon } from "@mantine/core";
import {
  FaChevronRight,
  FaChevronDown,
  FaChevronUp,
  FaChevronLeft,
} from "react-icons/fa";

const riskData = [
  {
    riskId: "#24001",
    description: "Data Breach Incident",
    category: "Security",
    impact: "1-5",
    likelihood: "1-5",
    riskLevel: { text: "Extreme", color: "red" },
    treatmentCategory: "Technical Controls",
    mitigationStrategy: "Implement encryption for sensitive data",
    owner: "John Doe",
    dueDate: "2024-10-15",
    notes: "Ongoing monitoring",
    status: { text: "Completed", color: "green" },
  },
  {
    riskId: "#24002",
    description: "Supply Chain Disruption",
    category: "Operational",
    impact: "1-5",
    likelihood: "1-5",
    riskLevel: { text: "Medium", color: "yellow" },
    treatmentCategory: "Supplier Diversification",
    mitigationStrategy: "Establish backup suppliers",
    owner: "Jane Smith",
    dueDate: "2024-11-01",
    notes: "Risk reassessed monthly",
    status: { text: "Completed", color: "green" },
  },
  {
    riskId: "#24003",
    description: "Regulatory Non-Compliance",
    category: "Compliance",
    impact: "1-5",
    likelihood: "1-5",
    riskLevel: { text: "Low", color: "green" },
    treatmentCategory: "Policy Review",
    mitigationStrategy: "Update compliance policies",
    owner: "Alice Brown",
    dueDate: "2024-09-30",
    notes: "Legal review pending",
    status: { text: "In Progress", color: "yellow" },
  },
];

const Component = () => {
  const [data, setData] = React.useState({ nodes: riskData });
  const [scrollX, setScrollX] = React.useState(0);
  const tableRef = React.useRef(null);

  //* Theme *//
  const mantineTheme = getTheme({
    ...DEFAULT_OPTIONS,
    striped: false,
    highlightOnHover: false,
  });
  const customTheme = {
    Table: `
      --data-table-library_grid-template-columns: repeat(11, minmax(0, 1fr));
      margin: 16px 0px;
    `,
  };
  const theme = useTheme([mantineTheme, customTheme]);

  //* Pagination *//
  const pagination = usePagination(data, {
    state: {
      page: 0,
      size: 4,
    },
    onChange: onPaginationChange,
  });

  function onPaginationChange(action, state) {
    console.log(action, state);
  }

  //* Row Selection *//
  const select = useRowSelect(data, {
    onChange: onSelectChange,
  });

  function onSelectChange(action, state) {
    console.log("Selected rows:", state.ids);
  }

  //* Sort *//
  const sort = useSort(
    data,
    {
      onChange: onSortChange,
    },
    {
      sortFns: {
        RISK_ID: (array) =>
          array.sort((a, b) => a.riskId.localeCompare(b.riskId)),
        CATEGORY: (array) =>
          array.sort((a, b) => a.category.localeCompare(b.category)),
        IMPACT: (array) => array.sort((a, b) => a.impact - b.impact),
        LIKELIHOOD: (array) =>
          array.sort((a, b) => a.likelihood - b.likelihood),
      },
    }
  );

  function onSortChange(action, state) {
    console.log(action, state);
  }

  //* Columns *//
  const COLUMNS = [
    {
      label: (
        <Checkbox
          checked={select.state.all}
          indeterminate={!select.state.all && !select.state.none}
          onChange={select.fns.onToggleAll}
        />
      ),
      renderCell: (item) => (
        <Checkbox
          checked={select.state.ids.includes(item.riskId)}
          onChange={() => select.fns.onToggleById(item.riskId)}
        />
      ),
    },
    {
      label: "Risk ID",
      renderCell: (item) => item.riskId,
      sort: {
        sortKey: "RISK_ID",
        sortIcon:
          sort.state.sortKey === "RISK_ID" ? (
            sort.state.reverse ? (
              <FaChevronUp />
            ) : (
              <FaChevronDown />
            )
          ) : (
            <FaChevronRight />
          ),
      },
    },
    {
      label: "Category",
      renderCell: (item) => item.category,
      sort: {
        sortKey: "CATEGORY",
        sortIcon:
          sort.state.sortKey === "CATEGORY" ? (
            sort.state.reverse ? (
              <FaChevronUp />
            ) : (
              <FaChevronDown />
            )
          ) : (
            <FaChevronRight />
          ),
      },
    },
    {
      label: "Impact",
      renderCell: (item) => item.impact,
      sort: { sortKey: "IMPACT" },
    },
    {
      label: "Likelihood",
      renderCell: (item) => item.likelihood,
      sort: { sortKey: "LIKELIHOOD" },
    },
    {
      label: "Risk Level",
      renderCell: (item) => (
        <Button color={item.riskLevel.color} compact>
          {item.riskLevel.text}
        </Button>
      ),
    },
    {
      label: "Treatment Category",
      renderCell: (item) => item.treatmentCategory,
    },
    {
      label: "Mitigation Strategy",
      renderCell: (item) => item.mitigationStrategy,
    },
    { label: "Owner", renderCell: (item) => item.owner },
    { label: "Due Date", renderCell: (item) => item.dueDate },
    {
      label: "Status",
      renderCell: (item) => (
        <Button color={item.status.color} compact>
          {item.status.text}
        </Button>
      ),
    },
  ];

  //* Scroll Handling *//
  const scrollTable = (direction) => {
    if (tableRef.current) {
      const scrollAmount = direction === "right" ? 100 : -100;
      tableRef.current.scrollLeft += scrollAmount;
      setScrollX(tableRef.current.scrollLeft);
    }
  };

  return (
    <div style={{ position: "relative", width: "100%" }}>
      <div
        style={{
          overflowX: "auto",
          maxWidth: "100%",
        }}
        ref={tableRef}
      >
        <CompactTable
          columns={COLUMNS}
          data={{
            ...data,
            nodes: data.nodes.filter((node) => node.riskId.includes("")),
          }}
          theme={theme}
          layout={{ custom: true, horizontalScroll: true }}
          select={select}
          sort={sort}
          pagination={pagination}
          style={{ minWidth: "100%" }}
        />
      </div>

      {/* Scroll Buttons */}
      {scrollX > 0 && (
        <ActionIcon
          variant="filled"
          color="gray"
          style={{
            position: "fixed",
            right: "50px",
            top: "50%",
            transform: "translateY(-50%)",
            zIndex: 100,
          }}
          onClick={() => scrollTable("left")}
        >
          <FaChevronLeft />
        </ActionIcon>
      )}

      <Group position="right" mx={10}>
        <Pagination
          total={pagination.state.getTotalPages(data.nodes)}
          page={pagination.state.page + 1}
          onChange={(page) => pagination.fns.onSetPage(page - 1)}
        />
      </Group>
    </div>
  );
};

export default Component;
