import { But<PERSON> } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { GoShare } from "react-icons/go";
import MaterialityReadinessTable from "./Partials/MaterialityReadinessTable";
import { useState } from "react";
import DoubleMaterialityAssessment from "../DoubleMaterialityAssessment/DoubleMaterialityAssessment";
import DoubleMaterialityReporting from "../DoubleMaterialityReporting/DoubleMaterialityReporting";
import GuideModalButton from "@/Components/Guide/GuideModalButton";
import ReadinessGuide from "./ReadinessGuide";
import ReportGuide from "./ReportGuide";

export default function DoubleMaterialityReadiness() {
    const { t } = useTranslation();
    const [active, setActive] = useState("Readiness");

    const renderContent = () => {
        switch (active) {
            case "Readiness":
                return (
                    <div className="DoubleMateriality">
                        <div className="w-full justify-between flex items-center py-5 px-3">
                            <GuideModalButton buttonText="Readiness Guide">
                                <ReadinessGuide />
                            </GuideModalButton>
                        </div>
                        <div className="items-center justify-between w-full p-5 bg-white rounded-lg shadow-md sm:flex">
                            <div>
                                <h1 className="text-lg font-bold text-black">
                                    {t("doubleMaterialityReadiness")}
                                </h1>
                                <p className="font-normal text-sm text-[#667085]">
                                    {t("descriptiveText")}
                                </p>
                            </div>
                            <div className="mt-5 sm:mt-0 gap-5">
                                <Button
                                    className="duration-200 ease-out bg-transparent border-2 rounded-lg text-primary border-secondary-300 hover:-translate-y-2 hover:bg-secondary-400 hover:bg-transparent hover:text-primary outline-none focus:outline-none"
                                    size="md"
                                >
                                    <GoShare className="me-2" />
                                    {t("export")}
                                </Button>
                            </div>
                        </div>
                        <MaterialityReadinessTable />
                    </div>
                );
            case "Assessment":
                return (
                    <>
                        <DoubleMaterialityAssessment />
                    </>
                );
            case "Reporting":
                return (
                    <>
                        <div className="w-full justify-between flex items-center py-5 px-3">
                            <GuideModalButton buttonText="Report Guide">
                                <ReportGuide />
                            </GuideModalButton>
                        </div>
                        <DoubleMaterialityReporting />
                    </>
                );
            default:
                return null;
        }
    };

    return (
        <>
            <div className="grid md:grid-cols-3 justify-center bg-[#FFFFFF] border-2 border-[#E8E7EA] p-6 gap-5 py-3 mb-6 rounded-lg text-primary">
                <button
                    className={`relative text-lg font-semibold py-3 px-6 transition-all ${
                        active === "Readiness"
                            ? "active-tab rounded"
                            : "text-[#5A5A5A] rounded-lg border-2"
                    }`}
                    onClick={() => setActive("Readiness")}
                >
                    {t("Readiness")}
                </button>

                <button
                    className={`relative text-lg font-semibold py-3 px-6 transition-all ${
                        active === "Assessment"
                            ? "active-tab rounded"
                            : "text-[#5A5A5A] rounded-lg border-2"
                    }`}
                    onClick={() => setActive("Assessment")}
                >
                    {t("Assessment")}
                </button>

                <button
                    className={`relative text-lg font-semibold py-3 px-6 transition-all ${
                        active === "Reporting"
                            ? "active-tab rounded"
                            : "text-[#5A5A5A] rounded-lg border-2"
                    }`}
                    onClick={() => setActive("Reporting")}
                >
                    {t("Reporting")}
                </button>
            </div>

            {renderContent()}
        </>
    );
}
