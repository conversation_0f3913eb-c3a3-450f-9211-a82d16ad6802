import ApiS2 from "@/Api/apiS2Config";
import Loading from "@/Components/Loading";
import { Button, Checkbox, FileInput, Select, Tooltip } from "@mantine/core";
import { DateInput } from "@mantine/dates";
import { isNotEmpty, useForm } from "@mantine/form";
import { useDisclosure } from "@mantine/hooks";
import { showNotification } from "@mantine/notifications";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { FaCalendarDay } from "react-icons/fa";
import { FaRegCircleCheck } from "react-icons/fa6";
import { IoIosArrowDown, IoIosCheckmarkCircle } from "react-icons/io";
import { MdOutlineUploadFile } from "react-icons/md";
import ManualInputPopUp from "./ManualInputPopUp";
export default function ManualForm({
  AssetsError,
  loading,
  getTableData,
  allItemSpecificities,
}) {
  const [uploadValue, setUploadValue] = useState();
  const [opened, { open, close }] = useDisclosure(false);

  const [QuantityOpened, { open: QuantityOpen, close: QuantityClose }] =
    useDisclosure(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [selectedActivity, setSelectedActivity] = useState(null);
  const [selectedEFactors, setSelectedEFactors] = useState(null);
  const [selectedUom, setSelectedUom] = useState(null);
  const [additionalPayload, setAdditionalPayload] = useState({});
  const [additionalPayloadData, setAdditionalPayloadData] = useState({});
  const [inputValues, setInputValues] = useState({});

  const [Quantity, SetQuantity] = useState(null);
  const [startDateValue, setStartDateValue] = useState(null);
  const [endDateValue, setEndDateValue] = useState(null);
  const [selected, setSelected] = useState("");

  const [CompanyAssetAll, setCompanyAssetAll] = useState(); //? list of object and value
  const [companyAssetDrop, setCompanyAssetDrop] = useState([]); //? list of strings
  const [companyAssetSelected, setCompanyAssetSelected] = useState(); //? string

  const [companyAssetsLoad, setCompanyAssetsLoad] = useState(false);
  //  //console.log("inputValues:", inputValues);
  // emissions
  const [emissionsAll, setEmissionsAll] = useState([]); //? list of strings
  const [emissions, setEmissions] = useState([]); //? list of strings
  const [selectedEmissions, setSelectedEmissions] = useState("");
  //  //console.log("emissions", emissionsAll);
  const [dataByActivity, setDataByActivity] = useState([]);
  const [dataEFactorByActivity, setDataEFactorByActivity] = useState([]);
  const [dataUOMByActivity, setDataUOMByActivity] = useState([]);
  const [dataByEmissions, setDataByEmissions] = useState([]);
  const [dataActivityByEmissions, setDataActivityByEmissions] = useState([]);
  const [CustomFactors, setCustomFactors] = useState([]); //? list of objects
  const [associatedFactors, setAssociatedFactors] = useState([]); //? list of objects

  // call emission source based on company asset id
  const getEmissionSources = async (companyId) => {
    setSelectedEmissions("");
    setCompanyAssetsLoad(true);
    setInputValues({});
    try {
      const { data } = await ApiS2.get(
        `/carbon-factors/get-items-associated-with-company-asset`,
        {
          headers: {
            "company-asset-id": companyId,
          },
        }
      );
      setEmissionsAll(data?.associated_emissions);
      const emissionsList = data.associated_emissions.map((el) => el.asset);
      setEmissions(emissionsList);
      setAssociatedFactors(data.associated_factors);
      // //console.log("🚀 ~ getEmissionSources ~ data:", data)
    } catch (er) {
      //console.log("🚀 ~ getEmissionSources ~ er:", er);
    }
    setCompanyAssetsLoad(false);
  };

  const selectCompanyAsset = (value) => {
    if (value) {
      const assetId = CompanyAssetAll?.find(
        (asset) => asset?.assetName == value
      )?.id;

      getEmissionSources(assetId);
    }
  };

  //? list of strings
  //  //console.log(additionalPayloadData);
  //console.log(dataByActivity);
  //console.log(inputValues);
  const assignDataBasedOnEmissions = (emission, activity, eFactor, uom) => {
    if (emission) {
      const filtredEmissionId = emissionsAll?.find(
        (em) => em.asset === emission
      )?.id;
      const filtredCustomFactor = associatedFactors.filter(
        (em) => em?.emissionSourceId === filtredEmissionId
      );

      // استخراج الأنشطة الفريدة
      const activityValues = filtredCustomFactor.map((item) => item.activity);
      const uniqueActivities = Array.from(
        new Set(activityValues.map(JSON.stringify))
      ).map(JSON.parse);
      const formattedActivities = uniqueActivities.map((activity) => {
        const entries = Object.entries(activity).map(
          ([key, value]) => `${key}:${value}`
        );
        return entries.length > 1 ? `${entries.join(" || ")}` : `${entries[0]}`;
      });

      setDataActivityByEmissions(formattedActivities);
      setDataByEmissions(filtredCustomFactor);
    }

    if (activity) {
      const dataByActivity = dataByEmissions.filter((item) => {
        return Object.entries(activity).every(
          ([key, value]) => item.activity[key] === value
        );
      });
      setDataByActivity(dataByActivity);
      // فلترة eFactors غير null
      const eFactorsValues = dataByActivity
        .map((item) => item.eFactors)
        .filter((eFactors) => eFactors !== null);

      // تنسيق eFactors
      const uniqueEFactors = Array.from(
        new Set(eFactorsValues.map(JSON.stringify))
      ).map(JSON.parse);
      const formattedEFactors = uniqueEFactors.map((eFactors) => {
        const entries = Object.entries(eFactors).map(
          ([key, value]) => `${key}:${value}`
        );
        return entries.length > 1 ? `${entries.join(" || ")}` : `${entries[0]}`;
      });

      setDataEFactorByActivity(formattedEFactors);
    }

    if (eFactor) {
      const dataByEfactor = dataByActivity.filter((item) => {
        return (
          item.eFactors &&
          Object.entries(eFactor).every(
            ([key, value]) => item.eFactors[key] === value
          )
        );
      });
      setCustomFactors(dataByEfactor);
      const UOMValues = dataByEfactor
        .map((item) => item.uom)
        .filter((uom) => uom !== null); // تجاهل null

      // تنسيق UOM
      const uniqueUOM = Array.from(new Set(UOMValues.map(JSON.stringify))).map(
        JSON.parse
      );
      const formattedUOM = uniqueUOM.map((UOM) => {
        const entries = Object.entries(UOM).map(
          ([key, value]) => `${key}:${value}`
        );
        return entries.length > 1 ? `${entries.join(" || ")}` : `${entries[0]}`;
      });
      //console.log(formattedUOM);

      setDataUOMByActivity(formattedUOM);
    }

    if (uom) {
      const dataByUOM = associatedFactors.filter((item) => {
        return (
          item.uom &&
          Object.entries(uom).every(([key, value]) => item.uom[key] === value)
        );
      });
      setCustomFactors(dataByUOM);
      const UOMValues = dataByUOM
        .map((item) => item.uom)
        .filter((uom) => uom !== null); // تجاهل null

      const uniqueUOM = Array.from(new Set(UOMValues.map(JSON.stringify))).map(
        JSON.parse
      );
      const formattedUOM = uniqueUOM.map((UOM) => {
        const entries = Object.entries(UOM).map(
          ([key, value]) => `${key}:${value}`
        );
        return entries.length > 1 ? `${entries.join(" || ")}` : `${entries[0]}`;
      });

      //console.log(dataByUOM);
    }
  };

  useEffect(() => {
    const handelCustomFactorsByAssetType = async () => {
      try {
        const { data: getAllCompanyAssets } = await ApiS2.get(
          `/carbon-factors/get-assets-accosted-with-factors`
        );
        setCompanyAssetDrop(getAllCompanyAssets.map((item) => item.assetName));
        setCompanyAssetAll(getAllCompanyAssets);
      } catch (error) {
        //console.log(error);
      }
    };
    handelCustomFactorsByAssetType();
  }, []);

  // From Core
  const form = useForm({
    mode: "uncontrolled",
    initialValues: {
      fromDate: "",
      toDate: "",
      assetsType: "",
      assets: "",
      activity: "",
      eFactors: "",
      uom: "",
      quantity: "",
      reportingYear: "",
      additionalPayload: {},
      file: "",
      source_physical: "",
      source_service: "",
      location_specificity: "",
      actual_data: "",
    },
    validate: {
      fromDate: isNotEmpty("Start Date is required"),
      toDate: isNotEmpty("End Date is required"),
      assetsType: isNotEmpty("Assets Type is required"),
      assets: isNotEmpty("Assets is required"),
      activity: isNotEmpty("Activity is required"),
      eFactors:
        selectedEFactors === "No Options"
          ? ""
          : isNotEmpty("Efactors is required"),
      uom: selectedUom === "No Options" ? "" : isNotEmpty("UOM is required"),
      quantity:
        dataByActivity?.[0]?.quantityKeys !== null &&
        isNotEmpty("quantity is required"),
      reportingYear: isNotEmpty("Reporting Years required"),
      source_physical: isNotEmpty("Source Physical required"),
      source_service: isNotEmpty("Source Service required"),
      location_specificity: isNotEmpty("Location Specificity required"),
      // additionalPayload:
      //   additionalPayload && !Object.keys(additionalPayload)?.length
      //     ? ""
      //     : isNotEmpty("Additional Payloadis required"),
      file: isNotEmpty(""),
      actual_data: isNotEmpty(""),
    },
  });

  const handledFormSubmit = async (value) => {
    //console.log("value", value);
    // return
    const newValue = {
      evidence_files: value.file,
    };

    const formData = new FormData();

    if (newValue.evidence_files) {
      if (Array.isArray(newValue.evidence_files)) {
        newValue.evidence_files.forEach((file) =>
          formData.append("evidence_files", file)
        );
      } else {
        formData.append("evidence_files", newValue.evidence_files);
      }
    }
    try {
      setSubmitLoading(true);
      const { data: UploadedFile } = await ApiS2.post(
        "/carbon-factors/upload_evidence_files",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      showNotification({
        message: "File Uploaded Successfully",
        color: "green",
      });

      const companyAssetID = CompanyAssetAll.filter(
        (item) => item.assetName === value.assets
      )[0]?.id;

      // //console.log(tempData);
      const newJsonValues = {
        from_date: value.fromDate,
        to_date: value.toDate,
        activity: value.activity || {},
        eFactors: value.eFactors || {},
        uom: value.uom || {},
        asset_type_id: dataByActivity?.[0]?.emissionSourceId,
        company_asset_id: companyAssetID,
        quantity: value.quantity || {},
        reporting_year:
          value.reportingYear === "No Options"
            ? null
            : Number(value.reportingYear) || null,
        evidence_uris: UploadedFile,
        additionalPayload: value.additionalPayload || null,
        source_physical: value.source_physical,
        source_service: value.source_service,
        location_specificity: value.location_specificity,
        asset_type_name: value.assetsType,
        actual_data:
          value.actual_data === "actual"
            ? true
            : value.actual_data === "estimate" && false,
            input_type: "manual",
      };
      //console.log(newJsonValues);
      ApiS2.post("/carbon-factors/submit-manual-inputs-form", newJsonValues)
        .then(({ data }) => {
          setSubmitLoading(false);
          showNotification({
            message: "Form Data Added Successfully",
            color: "green",
          });
          form.reset();
          restAllInputs();
          getTableData("manual");
          //console.log(data);
        })
        .catch((error) => {
          setSubmitLoading(false);
          //console.log(error);
          showNotification({
            message: error.response.data.message,
            color: "red",
          });
        });
    } catch (error) {
      setSubmitLoading(false);
      //console.log(error);
      showNotification({
        message: error.response.data.error,
        color: "red",
      });
    }
  };

  // rest All Inputs Fun
  const restAllInputs = () => {
    setCompanyAssetSelected(null);
    setUploadValue(null);
    setSelectedEmissions("");
    setSelectedActivity(null);
    setSelectedEFactors(null);
    setSelectedUom(null);
    setAdditionalPayloadData(null);
    setInputValues({});
    setDataByActivity([]);
    setStartDateValue(null);
    setEndDateValue(null);
    setSelected("");
    SetQuantity(null);
  };
  // form.setValues useEffect
  useEffect(() => {
    form.setValues({
      assetsType: selectedEmissions || "",
      assets:
        companyAssetSelected === "No Options"
          ? null
          : companyAssetSelected || null,
      file: uploadValue || "",
      additionalPayload: additionalPayloadData || {},
      activity: selectedActivity === "No Options" ? null : selectedActivity,
      quantity: inputValues.quantityKeys,
      eFactors: selectedEFactors === "No Options" ? null : selectedEFactors,
      uom: selectedUom === "No Options" ? null : selectedUom,
      //  reportingYear: dateValue?.getFullYear() || "",
      fromDate: startDateValue
        ? dayjs(startDateValue).format("DD/MM/YYYY")
        : "",
      toDate: endDateValue ? dayjs(endDateValue).format("DD/MM/YYYY") : "",
      actual_data: selected,
    });
  }, [
    companyAssetSelected,
    uploadValue,
    selectedEmissions,
    additionalPayloadData,
    selectedActivity,
    selectedEFactors,
    selectedUom,
    inputValues,
    startDateValue,
    endDateValue,
    selected,
  ]);

  // Function to handle Input Change In additional payload data
  const handleInputChange = (key, value, section) => {
    // //console.log("🚀 ~ handleInputChange ~ section:", section);
    setInputValues((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value,
      },
    }));
  };

  // Function to handle saving additional payload data
  const handleSaveAdditionalPayload = (item) => {
    switch (item) {
      case "quantityKeys":
        SetQuantity(inputValues?.quantityKeys);
        QuantityClose();
        break;
      case "additional_payload":
        setAdditionalPayloadData(inputValues?.additional_payload);
        close();
        break;

      default:
        break;
    }
  };
  const { t } = useTranslation();

  return (
    <>
      {loading ? (
        <Loading />
      ) : AssetsError ? (
        <h1 className="capitalize text-center">{AssetsError}</h1>
      ) : (
        <form
          className="block w-full px-10 py-4 bg-white shadow-md rounded-xl"
          onSubmit={form.onSubmit(handledFormSubmit)}
        >
          <span className="notes1"></span>
          <span className="notes2"></span>
          <span className="notes3"></span>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 sm:gap-x-5 md:gap-x-10 lg:gap-x-28 gap-y-10 text-start">
            <DateInput
              {...form.getInputProps("fromDate")}
              key={form.key("fromDate")}
              label="From"
              placeholder="DD/MM/YYYY"
              valueFormat="DD/MM/YYYY"
              rightSection={<FaCalendarDay />}
              value={startDateValue}
              onChange={setStartDateValue}
              radius={10}
              size="md"
              className="From-Dates"
            />
            <DateInput
              {...form.getInputProps("toDate")}
              key={form.key("toDate")}
              label="To"
              placeholder="DD/MM/YYYY"
              valueFormat="DD/MM/YYYY"
              value={endDateValue}
              onChange={setEndDateValue}
              rightSection={<FaCalendarDay />}
              radius={10}
              size="md"
              className="To-Dates"
            />

            <Select
              {...form.getInputProps("assets")}
              key={form.key("assets")}
              onChange={(e) => {
                setCompanyAssetSelected(e);
                selectCompanyAsset(e);
                setSelectedEmissions("");
                setSelectedActivity(null);
                setSelectedEFactors(null);
                setSelectedUom(null);
                SetQuantity(null);
                setInputValues({});
                setDataByActivity([]);
              }}
              value={companyAssetSelected}
              label="Assets"
              placeholder="Choose"
              data={
                companyAssetDrop.length === 0 ? ["No Option"] : companyAssetDrop
              }
              radius={10}
              size="md"
              rightSection={<IoIosArrowDown />}
              className="Assets-collect"
            />

            {companyAssetsLoad ? (
              <div className="col-span-1 md:col-span-2  lg:col-span-3">
                <Loading />
              </div>
            ) : (
              <>
                <Tooltip
                  multiline
                  w={220}
                  radius={"md"}
                  withArrow
                  transitionProps={{ duration: 200 }}
                  label={
                    <span className="capitalize flex justify-center text-center">
                      please select asset first
                    </span>
                  }
                  hidden={
                    companyAssetSelected ||
                    (companyAssetSelected && emissions.length !== 0)
                  }
                >
                  <Select
                    {...form.getInputProps("assetsType")}
                    key={form.key("assetsType")}
                    disabled={emissions.length == 0 || !companyAssetSelected}
                    label={`Emission Source`}
                    placeholder="Choose"
                    onChange={(value) => {
                      setSelectedEmissions(value ? value : "");
                      assignDataBasedOnEmissions(value);
                      setInputValues({});
                      setSelectedActivity(null);
                      setSelectedEFactors(null);
                      setSelectedUom(null);
                      SetQuantity(null);
                      setDataByActivity([]);
                      setAdditionalPayload({});
                    }}
                    value={selectedEmissions}
                    data={emissions}
                    radius={10}
                    size="md"
                    rightSection={<IoIosArrowDown />}
                    className="Emission-Source"
                  />
                </Tooltip>
                {/* open activity */}
                {/* <div className="flex items-start justify-end flex-col"> */}
                <Tooltip
                  multiline
                  w={220}
                  radius={"md"}
                  withArrow
                  transitionProps={{ duration: 200 }}
                  hidden={
                    !selectedEmissions ||
                    (selectedEmissions && dataByEmissions.length !== 0)
                  }
                  label={
                    <span className="capitalize flex justify-center text-center">
                      there is no Custom Factor in this Emission Source
                    </span>
                  }
                >
                  <Select
                    {...form.getInputProps("activity")}
                    key={form.key("activity")}
                    label="Activity"
                    radius={10}
                    placeholder="Choose"
                    rightSection={<IoIosArrowDown />}
                    onChange={(e) => {
                      if (e) {
                        if (e === "No Options") {
                          setSelectedActivity("No Options"); // حفظ "No Options" عند اختياره
                        } else {
                          const activity = Object.fromEntries(
                            e.split(" || ").map((pair) => pair.split(":"))
                          );
                          setSelectedActivity(activity);
                          assignDataBasedOnEmissions(null, activity);
                        }
                        // //console.log(selectedActivity);
                      } else {
                        setSelectedActivity(null);
                        SetQuantity(null);
                        setDataByActivity([]);
                        setAdditionalPayload({});
                        setInputValues({});
                      }
                      setSelectedEFactors(null);
                      setSelectedUom(null);
                    }}
                    data={
                      dataActivityByEmissions?.length > 0
                        ? dataActivityByEmissions
                        : [{ label: "No Options", value: "No Options" }]
                    }
                    value={
                      selectedActivity === "No Options"
                        ? "No Options" // إظهار "No Options" عند اختياره
                        : selectedActivity
                        ? typeof selectedActivity === "object"
                          ? Object.entries(selectedActivity)
                              .map(([key, value]) => `${key}:${value}`)
                              .join(" || ")
                          : selectedActivity
                        : ""
                    }
                    disabled={!selectedEmissions}
                    className="Activity"
                  />
                </Tooltip>

                <Select
                  {...form.getInputProps("eFactors")}
                  key={form.key("eFactors")}
                  label="E-Factors"
                  placeholder="Choose"
                  rightSection={<IoIosArrowDown />}
                  radius={10}
                  onChange={(e) => {
                    if (e) {
                      if (e === "No Options") {
                        setSelectedEFactors("No Options");
                        assignDataBasedOnEmissions(null, null, "No Options");
                      } else {
                        const eFactors = Object.fromEntries(
                          e.split(" || ").map((pair) => pair.split(":"))
                        );
                        setSelectedEFactors(eFactors);
                        assignDataBasedOnEmissions(null, null, eFactors);
                      }
                      // //console.log(selectedActivity);
                    } else {
                      setSelectedEFactors(null);
                    }
                  }}
                  value={
                    selectedEFactors === "No Options"
                      ? "No Options" // إظهار "No Options" عند اختياره
                      : selectedEFactors
                      ? typeof selectedEFactors === "object"
                        ? Object.entries(selectedEFactors)
                            .map(([key, value]) => `${key}:${value}`)
                            .join(" || ")
                        : selectedEFactors
                      : ""
                  }
                  data={
                    dataEFactorByActivity?.length > 0
                      ? dataEFactorByActivity
                      : [{ label: "No Options", value: "No Options" }]
                  }
                  disabled={!selectedActivity}
                  className="E-Factors"
                />

                <Select
                  {...form.getInputProps("uom")}
                  key={form.key("uom")}
                  rightSection={<IoIosArrowDown />}
                  label="UOM"
                  radius={10}
                  placeholder="Choose"
                  onChange={(e) => {
                    if (e) {
                      if (e === "No Options") {
                        setSelectedUom("No Options"); // حفظ "No Options" عند اختياره
                        assignDataBasedOnEmissions(
                          null,
                          null,
                          null,
                          "No Options"
                        );
                      } else {
                        const UOM = Object.fromEntries(
                          e.split(" || ").map((pair) => pair.split(":"))
                        );
                        setSelectedUom(UOM);
                        assignDataBasedOnEmissions(null, null, null, UOM);
                      }
                      // //console.log(selectedActivity);
                    } else {
                      setSelectedUom(null);
                    }
                  }}
                  value={
                    selectedUom === "No Options"
                      ? "No Options" // إظهار "No Options" عند اختياره
                      : selectedUom
                      ? typeof selectedUom === "object"
                        ? Object.entries(selectedUom)
                            .map(([key, value]) => `${key}:${value}`)
                            .join(" || ")
                        : selectedUom
                      : ""
                  }
                  data={
                    dataUOMByActivity?.length > 0
                      ? dataUOMByActivity
                      : [{ label: "No Options", value: "No Options" }]
                  }
                  disabled={!selectedActivity || !selectedEFactors}
                  className="UOM"
                />

                {/* open quantity */}
                <div className="flex items-start justify-end flex-col">
                  <Tooltip
                    multiline
                    w={220}
                    radius={"md"}
                    withArrow
                    transitionProps={{ duration: 200 }}
                    className={`${
                      !selectedActivity
                        ? "hidden"
                        : selectedActivity &&
                          dataByActivity?.[0]?.quantityKeys !== null &&
                          "hidden"
                    }`}
                    label={
                      <span className="capitalize flex justify-center">
                        there is no Quantity
                      </span>
                    }
                  >
                    <Button
                      disabled={
                        selectedActivity === null ||
                        dataByActivity?.[0]?.quantityKeys === null
                      }
                      radius={10}
                      size="md"
                      className={`relative z-10 w-full bg-[#e6f3f4] hover:bg-[#e6f3f4] ${
                        (form.errors?.quantity
                          ? "  bg-red-500 hover:bg-red-500"
                          : "",
                        selectedActivity === null
                          ? "cursor-not-allowed text-[#989898] hover:text-[#989898]"
                          : "cursor-pointer text-primary hover:text-primary")
                      }`}
                      onClick={QuantityOpen}
                    >
                      + {t("Add Quantity")}
                      {Quantity != undefined && (
                        <IoIosCheckmarkCircle
                          className="absolute text-white top-1 right-1 z-20"
                          color="white"
                        />
                      )}
                    </Button>
                  </Tooltip>
                  <p className="capitalize text-red-500 text-sm">
                    {form.errors.quantity}
                  </p>
                </div>
                <Select
                  {...form.getInputProps("reportingYear")}
                  key={form.key("reportingYear")}
                  rightSection={<IoIosArrowDown />}
                  label="Reporting Year"
                  placeholder="Enter Year..."
                  radius={10}
                  size="md"
                  data={
                    dataByActivity?.[0]?.emissionValueByYear?.length > 0
                      ? dataByActivity[0].emissionValueByYear
                          .map((item) => item.year.toString())
                          .sort((a, b) => b - a) 
                      : [] 
                  }
                  clearable
                  disabled={
                    selectedActivity === null ||
                    !dataByActivity?.length ||
                    selectedActivity === "No Options"
                  }
                  className="Reporting-Year"
                />
                <Select
                  {...form.getInputProps("location_specificity")}
                  key={form.key("location_specificity")}
                  rightSection={<IoIosArrowDown />}
                  label="Location Specificity"
                  radius={10}
                  size="md"
                  placeholder="Choose"
                  data={allItemSpecificities?.location_specificity?.map(
                    (item) => item.location
                  )}
                  clearable
                  className="Location-Specificity"
                />
                <Select
                  {...form.getInputProps("source_physical")}
                  key={form.key("source_physical")}
                  rightSection={<IoIosArrowDown />}
                  label="Source Physical"
                  radius={10}
                  size="md"
                  placeholder="Choose"
                  data={allItemSpecificities?.source_physical?.map(
                    (item) => item.source
                  )}
                  className="Source-Physical"
                />
                <Select
                  {...form.getInputProps("source_service")}
                  key={form.key("source_service")}
                  rightSection={<IoIosArrowDown />}
                  label="Source Service"
                  radius={10}
                  size="md"
                  placeholder="Choose"
                  // onChange={setSelectedUom}
                  // value={selectedUom}
                  data={allItemSpecificities?.source_service?.map(
                    (item) => item.source
                  )}
                  className="Source-Service"
                />

                <div className="flex items-center lg:items-end">
                  <Tooltip
                    multiline
                    w={220}
                    radius={"md"}
                    withArrow
                    transitionProps={{ duration: 200 }}
                    className={`${
                      !selectedEmissions
                        ? "hidden"
                        : selectedEmissions &&
                          dataByActivity?.[0]?.additionalPayload !== null &&
                          "hidden"
                    }`}
                    label={
                      dataByActivity?.length !== 0 &&
                      dataByActivity?.[0]?.additionalPayload === null && (
                        <span className="capitalize flex justify-center">
                          there is no additionalPayload
                        </span>
                      )
                    }
                  >
                    <Button
                      disabled={
                        dataByActivity?.length === 0 ||
                        dataByActivity?.[0]?.additionalPayload === null
                      }
                      radius={10}
                      size="md"
                      className={`w-full Additional-Payload bg-[#e6f3f4] hover:bg-[#e6f3f4]  ${
                        (form.errors?.additionalPayload
                          ? "  bg-red-500 hover:bg-red-500"
                          : "",
                        dataByActivity?.length === 0 ||
                        dataByActivity?.[0]?.additionalPayload === null
                          ? "cursor-not-allowed text-[#989898] hover:text-[#989898]"
                          : "cursor-pointer text-primary hover:text-primary")
                      }`}
                      onClick={open}
                    >
                      + {t("Add Additional Payload")}
                    </Button>
                  </Tooltip>
                </div>

                <Tooltip
                  multiline
                  w={220}
                  radius={"md"}
                  withArrow
                  transitionProps={{ duration: 200 }}
                  className={
                    !uploadValue || !uploadValue?.length ? "" : "hidden"
                  }
                  label={
                    <span className="capitalize">
                      You can Select many Files Click CTRL From your Keyboard
                      then choose your files
                    </span>
                  }
                >
                  <FileInput
                    {...form.getInputProps("file")}
                    key={form.key("file")}
                    size="md"
                    id="upload_file"
                    type="file"
                    radius={"md"}
                    className="truncate Evidence"
                    label={"Evidence"}
                    placeholder={"File.pdf"}
                    defaultValue={uploadValue}
                    onChange={setUploadValue}
                    leftSection={<MdOutlineUploadFile />}
                    leftSectionPointerEvents="none"
                    clearable
                    multiple
                    styles={{
                      input: {
                        border: form.errors.file
                          ? "2px red solid"
                          : "2px #05808b solid",
                        "&:hover": {
                          borderColor: "#05808b",
                        },
                        "&:focus": {
                          borderColor: "#05808b",
                        },
                      },
                    }}
                  />
                </Tooltip>

                <div className="flex flex-col Actual-Estimate-Data justify-center items-center md:items-start  md:col-span-1">
                  <label htmlFor="" className="lg:text-nowrap">
                    Actual / Estimate data
                  </label>
                  <div className="flex flex-row gap-5 cursor-pointer">
                    <Checkbox
                      value="actual"
                      label="Actual"
                      size="md"
                      radius={100}
                      checked={selected === "actual"}
                      onChange={() => setSelected("actual")}
                      error={form?.errors.actual_data}
                      classNames={{
                        input: "cursor-pointer",
                        label: "cursor-pointer",
                      }}
                    />
                    <Checkbox
                      value="estimate"
                      label="Estimate"
                      size="md"
                      radius={100}
                      checked={selected === "estimate"}
                      onChange={() => setSelected("estimate")}
                      error={form?.errors.actual_data}
                      classNames={{
                        input: "cursor-pointer",
                        label: "cursor-pointer",
                      }}
                    />
                  </div>
                </div>
                <div className="hidden lg:flex"></div>
                <div className="hidden lg:flex"></div>
                <div className="flex items-end">
                  <Button
                    // onClick={handledFormSubmit}
                    type={submitLoading ? "reset" : "submit"}
                    // leftSection={}
                    // disabled
                    title="Under development"
                    color=""
                    radius={10}
                    size="md"
                    className="w-full Click-Confirm-Button bg-primary hover:bg-[#00C0A9]"
                  >
                    {submitLoading ? (
                      <Loading />
                    ) : (
                      <span className="flex items-center ">
                        <FaRegCircleCheck className="me-1" /> {t("Confirm")}
                      </span>
                    )}
                  </Button>
                </div>
              </>
            )}

            {QuantityOpened && (
              <ManualInputPopUp
                opened={QuantityOpened}
                close={QuantityClose}
                t={t}
                handleSaveAdditionalPayload={handleSaveAdditionalPayload}
                handleInputChange={handleInputChange}
                title={"Quantity"}
                item={"quantityKeys"}
                customFactors={dataByActivity}
                inputValues={inputValues}
              />
            )}

            {/* additionalPayload Modal */}
            {opened && (
              <ManualInputPopUp
                opened={opened}
                close={close}
                t={t}
                handleSaveAdditionalPayload={handleSaveAdditionalPayload}
                handleInputChange={handleInputChange}
                title={"Additional Payload"}
                item={"additional_payload"}
                inputValues={inputValues}
                customFactors={dataByActivity}
              />
            )}
            {/* End additionalPayload Modal */}
          </div>
        </form>
      )}
    </>
  );
}
