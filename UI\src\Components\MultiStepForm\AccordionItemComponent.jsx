import { useMemo, useEffect, useRef } from "react";
import {
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/Components/ui/accordion";
import {
  IoIosCheckmarkCircle,
  IoIosCheckmarkCircleOutline,
} from "react-icons/io";
import { TextAreaGroup } from "./TextAreaGroup";
import Performance from "./Performance";
import Framework from "./Framework";
import Comments from "./Comments";
import Evidence from "./Evidence";
import Details from "./Details";
import QualityAssurance from "./QualityAssurance";
import Cookies from "js-cookie";
import io from "socket.io-client";

export function AccordionItemComponent({
  value,
  title,
  reportId,
  subtitle,
  questions,
  titleID,
  setSections,
  visibility = {
    details: true,
    evidence: true,
    framework: true,
    remarks: true,
    performance: true,
    qualityAssurance: true,
  },
  detailsData,
  topicId,
  topicReviewers,
  topicDueDate,
  double_materiality_uid,
  topicComments,
  quantitative_metrics,
  qualitative_metrics,
  quality_assurance,
  framework_requirements,
  xbrl_tags,
  evidences,
}) {
  const isComplete = useMemo(() => {
    return questions.every((disclosure) => disclosure.disclosure_text?.trim());
  }, [questions]);
  const socketRef = useRef(null);

  // Set up Socket.IO
  useEffect(() => {
    const token = Cookies.get("level_user_token");
    if (!token) {
      return;
    }

    const initialize = async () => {
      socketRef.current = io(
        // "http://localhost:9000/",
        "https://issb-report-api-staging.azurewebsites.net",
        {
          path: "/socket.io/",
          reconnection: true,
          reconnectionAttempts: Infinity,
          randomizationFactor: 0.5,
          auth: { token: `Bearer ${token}` },
          withCredentials: true,
          extraHeaders: { Authorization: `Bearer ${token}` },
        }
      );

      socketRef.current.on("connect", () => {
        // socketRef.current.emit("join_disclosure", { disclosure_id: id });
      });

      socketRef.current.on("connect_error", (error) => {
        console.error("[Socket.IO] Connection error:", error.message);
      });

      socketRef.current.on("disconnect", (reason) => {
        console.log("[Socket.IO] Disconnected from server:", reason);
      });
    };

    initialize();

    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
        socketRef.current = null;
      }
    };
  }, []);

  return (
    <AccordionItem value={value}>
      <AccordionTrigger className="font-semibold text-[#272727]">
        {isComplete ? (
          <IoIosCheckmarkCircle className="mr-2 inline text-[#07838F]" />
        ) : (
          <IoIosCheckmarkCircleOutline className="mr-2 inline text-[#07838FCC]" />
        )}
        {titleID} {title}
      </AccordionTrigger>
      <AccordionContent>
        {subtitle && <p className="text-[#494949] font-semibold">{subtitle}</p>}
        <div className="gap-4 flex flex-col text-[#272727]">
          {questions.map((question) => (
            <TextAreaGroup
              socketRef={socketRef}
              key={question.id}
              id={question.id}
              reportId={reportId}
              label={question.requirement_text}
              question={question}
              value={question.disclosure_text || ""}
              setSections={setSections}
            />
          ))}
        </div>

        {visibility.details && (
          <Details
            topicId={topicId}
            topicReviewers={topicReviewers}
            detailsData={detailsData}
            topicDueDate={topicDueDate}
            double_materiality_uid={double_materiality_uid}
            xbrl_tags={xbrl_tags}
          />
        )}

        {visibility.remarks && (
          <Comments topicId={topicId} topicCommentsAttr={topicComments} />
        )}

        {visibility.evidence && <Evidence topicId={topicId} evidences={evidences} />}

        {visibility.performance && (
          <Performance
            topicId={topicId}
            quantitative_metrics={quantitative_metrics}
            qualitative_metrics={qualitative_metrics}
          />
        )}

        {visibility.qualityAssurance && (
          <QualityAssurance
            topicId={topicId}
            quality_assurance={quality_assurance}
          />
        )}

        {visibility.framework && (
          <Framework framework_requirements={framework_requirements} />
        )}
      </AccordionContent>
    </AccordionItem>
  );
}
