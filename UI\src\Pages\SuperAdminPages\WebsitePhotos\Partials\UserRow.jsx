import React, { useState } from "react";
import Cookies from "js-cookie";
import axios from "axios";

import { Avatar, Table, Button } from "@mantine/core";
import Loading from "@/Components/Loading";
import { showNotification } from "@mantine/notifications";

const UserRow = ({ user, idx ,refetch,position}) => {
  const [image, setimage] = useState("");
  const [deleteState, setdeleteState] = useState("");

  const msg = (txt, color = "green") => {
    showNotification({ message: txt, color: color, timing: 7000 });
  };
  const handleDelete = async (url) => {
    if (deleteState == "") {
      setdeleteState("open-delete");
    }
    if (deleteState == "open-delete") {
      setdeleteState("delete");
      try {
        const res = await axios.post(
          "https://portal-auth-main-staging.azurewebsites.net/delete_image",
          {
            image_url: url,
          },
          {
            headers: {
              Authorization: `Bearer ${Cookies.get("level_user_token")}`,
            },
          }
        );
        msg(`${res.data.message}`);
        refetch();
        setdeleteState("");
      } catch (error) {
        msg(`${error.response.data.message}`,'red');
        setdeleteState("");
      }
    }
  };
  return (
    <>
      {image != "" && (
        <div className="fixed top-5 left-5 flex justify-center z-50 w-full">
          <div className="w-[70%] h-[500px] bg-primary rounded-xl ">
            <button
              className="bg-red-400 p-3 text-white"
              onClick={() => setimage("")}
            >
              Close
            </button>
            <img
              src={image}
              alt="image"
              width={300}
              className="w-full h-full object-contain"
            />
          </div>
        </div>
      )}
      <Table.Tr
        key={idx} // Use the unique ID as the key
      >
        <Table.Td>{user.name}</Table.Td>
        <Table.Td>
          <Avatar
            src={user.url}
            alt={user.name}
            className="cursor-pointer"
            size={`lg`}
            onClick={() => setimage(user.url)}
          />
        </Table.Td>
        <Table.Td className="">
          {position}
        </Table.Td>
        <Table.Td className="flex">
          <Button
            disabled={deleteState == "delete"}
            className={`bg-red-500 rounded-xl text-white`}
            onClick={() => handleDelete(user.url, idx)}
          >
            {deleteState == "open-delete" ? "Are you sure?" : "Delete"}
            {deleteState == "delete" && <Loading />}
          </Button>
        </Table.Td>
      </Table.Tr>
    </>
  );
};

export default UserRow;
