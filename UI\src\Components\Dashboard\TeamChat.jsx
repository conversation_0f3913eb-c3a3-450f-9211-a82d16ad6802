import React from "react";
import ChatItems from "./ChatItems";
import DateActivity from "./DateActivity";
import photoUser from "../../../public/assets/Images/img.jpg";
import { IoMdCheckmark } from "react-icons/io";

function TeamChat() {
  return (
    <div className="bg-white py-[1rem] px-[1.5rem] rounded-2xl shadow-2xl w-[90%]  xl:w-[37%] mx-auto">
      <h1 className="font-bold text-[18px] ">Team Chats</h1>
      <div>
        <ChatItems
          img={photoUser}
          name="<PERSON>"
          massege="Hi, are you Available Tomorrow?"
          num="1"
          date=<DateActivity date="10:35 AM" />
        />
        <ChatItems
          img={photoUser}
          name="<PERSON>"
          massege="Nice One. Will Do it tomorrow"
          num="3"
          date=<DateActivity date="10:35 AM" />
        />
        <ChatItems
          img={photoUser}
          name="<PERSON>"
          massege="That's Great. I am looking forward to having a great start."
          num=<IoMdCheckmark />
          date=<DateActivity date="10:35 AM" />
        />
      </div>
    </div>
  );
}

export default TeamChat;
