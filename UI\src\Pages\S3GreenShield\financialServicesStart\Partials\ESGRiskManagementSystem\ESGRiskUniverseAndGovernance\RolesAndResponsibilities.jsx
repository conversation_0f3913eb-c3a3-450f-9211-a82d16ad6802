import { useState } from "react";
import { MdDelete } from "react-icons/md";
import Edit from "@/assets/images/edit.svg";
import { IoAddCircleOutline } from "react-icons/io5";
import { Modal, Button, TextInput } from "@mantine/core"; // Import modal components from Mantine
import GuideModalButton from "@/Components/Guide/GuideModalButton";
import Guide from "./ResponsibilitiesGuide";

export default function RolesAndResponsibilities() {
    const [headers, setHeaders] = useState([
        "",
        "Board of Directors",
        "C-Suite",
        "Risk Management Team",
        "ESG Committee",
        "Department Heads",
    ]);

    const [rows, setRows] = useState([
        { task: "Strategy Approval", roles: ["", "", "", "", ""] },
        { task: "Risk Identification", roles: ["", "", "", "", ""] },
        { task: "Risk Assessment", roles: ["", "", "", "", ""] },
        { task: "Mitigation Strategy", roles: ["", "", "", "", ""] },
        { task: "Monitoring & Reporting", roles: ["", "", "", "", ""] },
    ]);

    const headerColors = [
        "#298BEDC4",
        "#70D162AB",
        "#FFAB07B5",
        "#FF9990",
        "#8B32E463",
    ];

    const addColumn = () => {
        const newColumnHeader = `New Column ${headers.length}`;
        setHeaders((prevHeaders) => [...prevHeaders, newColumnHeader]);
        setRows((prevRows) =>
            prevRows.map((row) => ({
                ...row,
                roles: [...row.roles, ""],
            }))
        );
    };

    const addRow = () => {
        const newRow = {
            task: `New Task ${rows.length + 1}`,
            roles: Array(headers.length - 1).fill(""),
        };
        setRows((prevRows) => [...prevRows, newRow]);
    };

    // Modal state management
    const [modalOpened, setModalOpened] = useState(false);
    const [editableHeaders, setEditableHeaders] = useState(headers); // State to store editable headers
    const [editableRows, setEditableRows] = useState(
        rows.map((row) => row.task)
    ); // State to store editable row tasks

    // State to track whether delete buttons should be visible
    const [showDeleteButtons, setShowDeleteButtons] = useState(false);

    // Function to handle opening the modal
    const handleEditClick = () => {
        setEditableHeaders(headers);
        setEditableRows(rows.map((row) => row.task));
        setModalOpened(true);
    };

    // Function to handle text input change for headers
    const handleHeaderChange = (index, value) => {
        const updatedHeaders = [...editableHeaders];
        updatedHeaders[index] = value;
        setEditableHeaders(updatedHeaders);
    };

    // Function to handle text input change for row tasks
    const handleRowTaskChange = (index, value) => {
        const updatedRows = [...editableRows];
        updatedRows[index] = value;
        setEditableRows(updatedRows);
    };

    // Function to save the new content to the headers and rows
    const handleSave = () => {
        setHeaders(editableHeaders);
        setRows((prevRows) =>
            prevRows.map((row, index) => ({
                ...row,
                task: editableRows[index],
            }))
        );
        setModalOpened(false);
    };

    // Function to delete a column
    const deleteColumn = (columnIndex) => {
        const updatedHeaders = headers.filter(
            (_, index) => index !== columnIndex
        );
        const updatedRows = rows.map((row) => ({
            ...row,
            roles: row.roles.filter((_, index) => index !== columnIndex - 1),
        }));
        setHeaders(updatedHeaders);
        setRows(updatedRows);
    };

    // Function to delete a row
    const deleteRow = (rowIndex) => {
        const updatedRows = rows.filter((_, index) => index !== rowIndex);
        setRows(updatedRows);
    };

    // Toggle visibility of delete buttons
    const toggleDeleteButtons = () => {
        setShowDeleteButtons(!showDeleteButtons);
    };

    return (
        <div className="p-5 flex flex-col">
            <div className="w-full justify-between flex items-center py-5 px-3">
                <GuideModalButton buttonText="Responsibilities Guide">
                    <Guide />
                </GuideModalButton>
            </div>
            <div className="flex flex-row">
                <table className="min-w-full border-collapse table-fixed">
                    <thead>
                        <tr>
                            {/* Empty Header with Edit and Delete Icons */}
                            <th className="p-3 bg-transparent border-none text-center">
                                <div className="flex justify-center items-center space-x-2">
                                    <button onClick={handleEditClick}>
                                        <img src={Edit} alt="edit icon" />
                                    </button>
                                    <MdDelete
                                        className="text-[#00C0A9] h-6 w-6 cursor-pointer"
                                        onClick={toggleDeleteButtons}
                                    />
                                </div>
                            </th>
                            {headers.slice(1).map((header, index) => (
                                <th
                                    key={index}
                                    className="text-white text-center align-middle relative"
                                    style={{
                                        backgroundColor:
                                            headerColors[index] || "#CCCCCC",
                                        width: "100px",
                                        height: "100px",
                                    }}
                                >
                                    {header}
                                    {/* Conditionally render delete button */}
                                    {showDeleteButtons && (
                                        <MdDelete
                                            className="absolute top-1 right-1 text-red-600 cursor-pointer"
                                            onClick={() =>
                                                deleteColumn(index + 1)
                                            } // `+1` to skip the empty header
                                        />
                                    )}
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody>
                        {rows.map((row, rowIndex) => (
                            <tr
                                key={rowIndex}
                                className={`rounded-lg ${
                                    rowIndex % 2 === 0
                                        ? "bg-white"
                                        : "bg-gray-100"
                                }`}
                            >
                                <td
                                    className="p-3 border-4 border-[#0000000D] bg-[#F4F4F4] text-[#000000CC] rounded-lg whitespace-normal break-words font-bold text-center align-middle relative"
                                    style={{ height: "80px", width: "120px" }}
                                >
                                    {row.task}
                                    {/* Conditionally render delete button */}
                                    {showDeleteButtons && (
                                        <MdDelete
                                            className="absolute top-1 right-1 text-red-600 cursor-pointer"
                                            onClick={() => deleteRow(rowIndex)}
                                        />
                                    )}
                                </td>
                                {row.roles.map((role, colIndex) => (
                                    <td
                                        key={colIndex}
                                        className="p-3 border-4 border-[#0000000D] rounded-br-lg text-center align-middle"
                                        style={{
                                            height: "80px",
                                            width: "150px",
                                        }}
                                    >
                                        {role}
                                    </td>
                                ))}
                            </tr>
                        ))}
                    </tbody>
                </table>

                {/* Add Column Button */}
                <button className="flex justify-start ml-4" onClick={addColumn}>
                    <IoAddCircleOutline className="text-[#00C0A9] h-6 w-6" />
                </button>
            </div>

            {/* Add Row Button */}
            <div className="flex justify-start mt-4">
                <button onClick={addRow}>
                    <IoAddCircleOutline className="text-[#00C0A9] h-6 w-6" />
                </button>
            </div>

            {/* Modal for editing headers and row tasks */}
            <Modal
                opened={modalOpened}
                onClose={() => setModalOpened(false)}
                title="Edit Headers and Row Tasks"
            >
                <div className="space-y-4">
                    <h3 className="font-bold">Edit Column Headers</h3>
                    {editableHeaders.map((header, index) => (
                        <TextInput
                            key={index}
                            value={header}
                            onChange={(event) =>
                                handleHeaderChange(
                                    index,
                                    event.currentTarget.value
                                )
                            }
                            placeholder={`Header ${index}`}
                        />
                    ))}
                    <h3 className="font-bold mt-6">Edit Row Tasks</h3>
                    {editableRows.map((task, index) => (
                        <TextInput
                            key={index}
                            value={task}
                            onChange={(event) =>
                                handleRowTaskChange(
                                    index,
                                    event.currentTarget.value
                                )
                            }
                            placeholder={`Task ${index}`}
                        />
                    ))}
                </div>

                <Button onClick={handleSave} className="mt-4 bg-[#00C0A9]">
                    Save
                </Button>
            </Modal>
        </div>
    );
}
