import React from 'react'
import { CgNotes } from "react-icons/cg";
import  PropType from 'prop-types';

function Infrastructure({type, region , ownership}) {
  return (
    <div className='bg-white py-[1rem] px-[1.5rem] rounded-2xl shadow-2xl w-[90%]  xl:w-[60%] mx-auto'>
        <h1 className='font-bold text-[18px] mb-5'>ABC Infrastructure</h1>
        <div className='flex flex-wrap   gap-[1rem] xl:gap-[2rem] justify-between items-center'>
            <div className='flex gap-[10px] items-center' >
                <CgNotes className='text-[20px] text-[#9C9C9C]'/> 
                <div>
                    <h2 className='text-[15px]'>Type</h2>
                    <p className='font-bold'>{type}</p>
                </div>
            </div>
            <div>
                <h2 className='text-[15px]'>Region</h2>
                <p className='font-bold'>{region}</p>
            </div>
            <div>
                <h2 className='text-[15px]'>Ownership</h2>
                <p className='font-bold'>{ownership}</p>
            </div>
        </div>
    </div>
  )
}

Infrastructure.propTypes = {
  type: PropType.string,
  region: PropType.string,
  ownership: PropType.string
};

export default Infrastructure
