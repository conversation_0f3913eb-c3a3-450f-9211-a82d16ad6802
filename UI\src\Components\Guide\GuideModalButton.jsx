// GuideModalButton.jsx
import { useState } from "react";
import { Modal } from "@mantine/core";
import { TbMessage2Question } from "react-icons/tb";

export default function GuideModalButton({ buttonText, children }) {
    const [opened, setOpened] = useState(false);

    return (
        <>
            <button
                className="bg-[#07838F1A] font-bold px-6 py-2 rounded-full flex items-center gap-3 hover:bg-[#07848f44] transition-all group"
                onClick={() => {
                    setOpened(true);
                }}
            >
                <span className="flex items-center gap-2 text-transparent bg-clip-text bg-gradient-to-r from-[#2C5A8C] via-[#1C889C] to-[#13B1A8] group-hover:text-[#2C5A8C] transition-all">
                    <TbMessage2Question size={22} color="#2C5A8C" />
                    {buttonText}
                </span>
            </button>

            <Modal
                opened={opened}
                onClose={() => setOpened(false)}
                title={
                    <span className="font-semibold text-3xl">{buttonText}</span>
                }
                size="1140px"
                miw="75vw"
                maw="90vw"
            >
                {children}
            </Modal>
        </>
    );
}
