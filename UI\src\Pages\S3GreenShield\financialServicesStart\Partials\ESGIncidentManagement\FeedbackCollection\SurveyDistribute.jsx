import React, { useState, useEffect } from "react";
import * as pdfjsLib from "pdfjs-dist";
import mammoth from "mammoth";
import authConfig from "@/Api/authConfig";

import {
  TextInput,
  Button,
  Title,
  Text,
  Paper,
  Divider,
  Tabs,
  CopyButton,
  Switch,
  Select,
  MultiSelect,
  Group,
  FileInput,
  FileButton,
} from "@mantine/core";
import { notifications } from "@mantine/notifications";
import {
  IconLink,
  IconUsers,
  IconCheck,
  IconCopy,
  IconMail,
} from "@tabler/icons-react";
import ApiS3 from "@/Api/apiS3";
import { useParams } from "react-router";

const SurveyDistribute = () => {
  const [emails, setEmails] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [surveyLink, setSurveyLink] = useState(null);
  const [duration, setDuration] = useState(0);
  const [unit, setUnit] = useState("");
  const [name, setName] = useState("");
  const [senderEmail, setSenderEmail] = useState("");
  const [companyName, setCompanyName] = useState("");
  const [message, setMessage] = useState("");
  const [link, setLink] = useState("");
  const params = useParams();
  const [companyEmails, setCompanyEmails] = useState([]);

  useEffect(() => {
    const CompanyEmails = async () => {
      try {
        const response = await authConfig.get("/get_all_user_by_token");

        const users = response.data.companyUsers;

        if (Array.isArray(users)) {
          const emails = users
            .filter((user) => !!user.email)
            .map((user) => ({
              value: user.email,
              label: user.userName,
            }));
          setCompanyEmails(emails);
        } else {
          console.error("Error:", response.data);
        }
      } catch (error) {
        console.error("Error company emails:", error);
      }
    };

    CompanyEmails();
  }, []);

  const handleSendInvitations = async () => {
    try {
      const response = await ApiS3.post("support/send", {
        name: name,
        senderEmail: senderEmail,
        companyName: companyName,
        message: message,
        link: surveyLink?.url || link,
        destination: emails.split(","),
      });
      notifications.show({
        title: "Success",
        message: `Emails sent successfully to ${
          emails.split(",").length
        } recipients!`,
        color: "green",
      });
      console.log("Emails sent successfully:", response.data);
    } catch (error) {
      notifications.show({
        title: "Error",
        message: "Failed to send emails. Please try again.",
        color: "red",
      });
      console.error("Error sending emails:", error);
    }
  };
  const generateSurveyLink = async () => {
    try {
      const response = await ApiS3.post(`survey/${params.id}/generate-link`, {
        expiresIn: duration,
        unit: unit,
      });
      setSurveyLink(response.data);
      console.log("Generated Link:", response.data.url);
    } catch (error) {
      console.error("Error generating link:", error);
    }
  };

  const numbers = Array.from({ length: 100 }, (_, i) => ({
    value: `${i + 1}`,
    label: `${i + 1}`,
  }));

  const timeUnits = [
    { value: "days", label: "Days" },
    { value: "hours", label: "Hours" },
  ];
  const [file, setFile] = useState(null);
  const handleExtractEmails = async (file) => {
    if (!file) return;

    const extractFromText = (text) => {
      const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
      const foundEmails = text.match(emailRegex) || [];

      if (foundEmails.length > 0) {
        setEmails(() => [...new Set([...foundEmails])].join(","));

        notifications.show({
          title: "Emails Extracted",
          message: `${foundEmails.length} emails found and added.`,
          color: "green",
        });
      } else {
        notifications.show({
          title: "No Emails Found",
          message: "No valid emails were found in the file.",
          color: "yellow",
        });
      }
    };

    const fileType = file.type;
    const fileName = file.name;

    if (fileType === "text/plain" || fileName.endsWith(".csv")) {
      const reader = new FileReader();
      reader.onload = (event) => {
        extractFromText(event.target.result);
      };
      reader.readAsText(file);
    } else if (fileType === "application/pdf" || fileName.endsWith(".pdf")) {
      const reader = new FileReader();
      reader.onload = async (event) => {
        const typedArray = new Uint8Array(event.target.result);
        const pdf = await pdfjsLib.getDocument({ data: typedArray }).promise;
        let fullText = "";

        for (let i = 1; i <= pdf.numPages; i++) {
          const page = await pdf.getPage(i);
          const content = await page.getTextContent();
          const strings = content.items.map((item) => item.str).join(" ");
          fullText += strings + "\n";
        }

        extractFromText(fullText);
      };
      reader.readAsArrayBuffer(file);
    } else if (
      fileType ===
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
      fileName.endsWith(".docx")
    ) {
      const reader = new FileReader();
      reader.onload = async (event) => {
        const arrayBuffer = event.target.result;
        const result = await mammoth.extractRawText({ arrayBuffer });
        extractFromText(result.value);
      };
      reader.readAsArrayBuffer(file);
    } else {
      notifications.show({
        title: "Unsupported File",
        message: "This file type is not supported for email extraction.",
        color: "red",
      });
    }
  };

  return (
    <div className="mx-auto bg-white p-6 rounded-xl border border-gray-200 shadow-sm">
      <Title order={3} className="mb-4">
        Distribute Survey
      </Title>

      <Text className="mb-6 text-gray-600">
        Share your survey with your target audience using any of the methods
        below.
      </Text>

      <Tabs defaultValue="link">
        <Tabs.List className="mb-4">
          <Tabs.Tab value="link" leftSection={<IconLink size={16} />}>
            Survey Link
          </Tabs.Tab>
          <Tabs.Tab value="email" leftSection={<IconMail size={16} />}>
            Email Invitations
          </Tabs.Tab>
          {/* <Tabs.Tab value="settings" leftSection={<IconUsers size={16} />}>
            Response Settings
          </Tabs.Tab> */}
        </Tabs.List>

        <Tabs.Panel value="link">
          <Paper p="lg" shadow="xs" withBorder className="mb-4">
            <Title order={4} className="mb-4">
              Share Survey Link
            </Title>

            <Group mt="md" grow>
              <Select
                label="Duration"
                placeholder="Select a number"
                data={numbers}
                searchable
                withinPortal
                value={duration.toString()}
                onChange={(value) => setDuration(Number(value))}
              />
              <Select
                label="Unit"
                placeholder="Select unit"
                data={timeUnits}
                withinPortal
                value={unit}
                onChange={setUnit}
              />
            </Group>

            <Button
              className="mt-5 mb-5 bg-[#07838F] hover:bg-[#07848fad]"
              onClick={generateSurveyLink}
              disabled={!duration || !unit}
            >
              Generate Survey Link
            </Button>

            {surveyLink?.url && (
              <>
                <Text className="mb-4">
                  Your survey is accessible via the following link:
                </Text>

                <div className="flex items-center">
                  <TextInput
                    value={surveyLink.url}
                    className="flex-1 mr-2"
                    readOnly
                  />
                  <CopyButton value={surveyLink.url}>
                    {({ copied, copy }) => (
                      <Button
                        color={copied ? "teal" : "#07838F"}
                        onClick={copy}
                        leftSection={
                          copied ? (
                            <IconCheck size={16} />
                          ) : (
                            <IconCopy size={16} />
                          )
                        }
                      >
                        {copied ? "Copied" : "Copy"}
                      </Button>
                    )}
                  </CopyButton>
                </div>
              </>
            )}

            <Divider className="my-4" />
          </Paper>
        </Tabs.Panel>

        <Tabs.Panel value="email">
          <Paper p="lg" shadow="xs" withBorder className="mb-4">
            <Title order={4} className="mb-4">
              Send Email Invitations
            </Title>
            <Group mt="md" grow>
              <TextInput
                label="Sender Name"
                placeholder="Enter your name"
                value={name}
                onChange={(e) => setName(e.currentTarget.value)}
              />
              <TextInput
                label="Sender Email"
                placeholder="Enter sender email"
                value={senderEmail}
                onChange={(e) => setSenderEmail(e.currentTarget.value)}
              />
            </Group>

            <Group mt="md" grow>
              <TextInput
                label="Company Name"
                placeholder="Enter company name"
                value={companyName}
                onChange={(e) => setCompanyName(e.currentTarget.value)}
              />
              <TextInput
                label="Message"
                placeholder="Enter message"
                value={message}
                onChange={(e) => setMessage(e.currentTarget.value)}
              />
            </Group>

            <TextInput
              className="mt-4"
              label="Survey Link"
              placeholder="Enter the survey link"
              value={surveyLink?.url || link}
              onChange={(e) => setLink(e.currentTarget.value)}
              readOnly={!surveyLink}
            />

            <MultiSelect
              className="mt-4"
              label="Inivte You compnay employees "
              placeholder="Choose emails"
              data={companyEmails}
              searchable
              comboboxProps={{
                transitionProps: {
                  transition: "pop",
                  duration: 200,
                  shadow: "md",
                },
              }}
              multiple
              clearable
              onChange={(values) => {
                setEmails(values.join(","));
              }}
            />

            <br />
            <FileButton
              onChange={(selectedFile) => {
                setFile(selectedFile);
                handleExtractEmails(selectedFile);
              }}
              accept=".txt,.csv,.pdf,.docx"
            >
              {(props) => (
                <Button
                  a
                  Fileton
                  {...props}
                  className="bg-[#07838F] hover:bg-[#07848fad] mb-4 "
                >
                  Extract Email From File
                </Button>
              )}
            </FileButton>

            <Text className="mb-4">
              Enter email addresses separated by commas to send personalized
              survey invitations:
            </Text>

            <TextInput
              placeholder="<EMAIL>, <EMAIL>, <EMAIL>"
              value={emails}
              onChange={(e) => setEmails(e.target.value)}
              className="mb-4"
            />

            <Button
              className="bg-[#07838F] hover:bg-[#07848fad]  mb-4"
              onClick={handleSendInvitations}
              disabled={!emails.trim()}
            >
              Send Invitations
            </Button>

            {successMessage && (
              <Text className="mt-2 text-green-600">{successMessage}</Text>
            )}
          </Paper>
        </Tabs.Panel>

        <Tabs.Panel value="settings">
          <Paper p="lg" shadow="xs" withBorder className="mb-4">
            <Title order={4} className="mb-4">
              Response Settings
            </Title>

            <div className="space-y-4">
              <div className="flex items-center justify-between p-2 border-b">
                <div>
                  <Text className="font-medium">Allow Anonymous Responses</Text>
                  <Text size="sm" color="dimmed">
                    Let users submit responses without authentication
                  </Text>
                </div>
                <Switch defaultChecked color="#07838F" />
              </div>

              <div className="flex items-center justify-between p-2 border-b">
                <div>
                  <Text className="font-medium">Limit to One Response</Text>
                  <Text size="sm" color="dimmed">
                    Users can only submit one response
                  </Text>
                </div>
                <Switch defaultChecked color="#07838F" />
              </div>

              <div className="flex items-center justify-between p-2 border-b">
                <div>
                  <Text className="font-medium">Public Results</Text>
                  <Text size="sm" color="dimmed">
                    Allow respondents to view aggregated results
                  </Text>
                </div>
                <Switch color="#07838F" />
              </div>

              <div className="flex items-center justify-between p-2">
                <div>
                  <Text className="font-medium">Close Automatically</Text>
                  <Text size="sm" color="dimmed">
                    Set a date when the survey will close
                  </Text>
                </div>
                <Switch color="#07838F" />
              </div>
            </div>

            <Button className="bg-[#07838F] hover:bg-[#07848fad] mt-4 mb-4 ">
              Save Settings
            </Button>
          </Paper>
        </Tabs.Panel>
      </Tabs>
    </div>
  );
};

export default SurveyDistribute;
