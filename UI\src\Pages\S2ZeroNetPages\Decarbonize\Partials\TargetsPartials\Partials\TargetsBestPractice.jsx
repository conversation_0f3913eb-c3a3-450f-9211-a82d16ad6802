import React from "react";
import { useTranslation } from "react-i18next";

const TargetsBestPractice = () => {
  const { t } = useTranslation();

  return (
    <div className="my-4">
      <h3 className="my-2 text-2xl font-semibold">
        {t("targetsBestPractice.title")}
      </h3>
      <ul className="space-y-2 list-disc list-inside ms-3">
        <li>{t("targetsBestPractice.practice1")}</li>
        <li>{t("targetsBestPractice.practice2")}</li>
        <li>{t("targetsBestPractice.practice3")}</li>
        <li>{t("targetsBestPractice.practice4")}</li>
        <li>{t("targetsBestPractice.practice5")}</li>
        <li>{t("targetsBestPractice.practice6")}</li>
      </ul>
    </div>
  );
};

export default TargetsBestPractice;
