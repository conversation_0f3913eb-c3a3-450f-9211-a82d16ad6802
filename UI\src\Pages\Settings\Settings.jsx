import useSideBarRoute from '@/hooks/useSideBarRoute';
import MainLayout from '@/Layout/MainLayout';
import Profile from './Partials/Profile/Profile';
import { IoMdHome } from 'react-icons/io';

export default function Settings() {
  const { getStartMenu } = useSideBarRoute();
  return (
    <MainLayout menus={getStartMenu} 
    breadcrumbItems={[
      { title: <IoMdHome size={20}/>, href: "/get-started" },
      { title: 'Settings', path: '/settings' }
    ]}
    navbarTitle={'Profile'}>
      <Profile/>
    </MainLayout>
  );
}
