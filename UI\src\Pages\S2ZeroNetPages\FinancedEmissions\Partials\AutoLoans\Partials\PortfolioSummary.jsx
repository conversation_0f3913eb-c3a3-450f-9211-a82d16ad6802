const PortfolioSummary = () => {
  return (
    <div className="flex flex-col gap-4 rounded-[10px] bg-white p-4 border-[#E8E7EA] border">
      <h2 className="text-[#272727] text-3xl font-bold">Portfolio Summary</h2>
      <div className="flex justify-between divide-x-2 divide-solid gap-4 rounded-lg bg-white border-[#E8E7EA] border">
        <div className="flex-1 p-4 flex flex-col items-center">
          <h3 className="text-[#272727] text-lg font-bold">Total Loans</h3>
          <p className="text-[#07838F] text-3xl font-bold">0</p>
        </div>
        <div className="flex-1 p-4 flex flex-col items-center">
          <h3 className="text-[#272727] text-lg font-bold">Portfolio Value</h3>
          <p className="text-[#07838F] text-3xl font-bold">$0M</p>
        </div>
        <div className="flex-1 p-4 flex flex-col items-center">
          <h3 className="text-[#272727] text-lg font-bold">Reporting Year</h3>
          <p className="text-[#07838F] text-3xl font-bold">2024</p>
        </div>
        <div className="flex-1 p-4 flex flex-col items-center">
          <h3 className="text-[#272727] text-lg font-bold">Avg Data Quality</h3>
          <p className="text-[#07838F] text-3xl font-bold">-</p>
        </div>
      </div>
    </div>
  );
};

export default PortfolioSummary;