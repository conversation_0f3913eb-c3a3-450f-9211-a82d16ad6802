import { AcademyContext } from "@/Contexts/AcademyContext";
import { useContext } from "react";
import { LazyLoadImage } from "react-lazy-load-image-component";

export default function CourseCard({ course, isEnrolled, progressPercentage }) {
  const { action, section } = useContext(AcademyContext);
  if (progressPercentage > 100) progressPercentage = 100;
  let coursePercentage = Math.floor(progressPercentage) || 0;
  let ButtonClasses;
  if (section === "available" && isEnrolled)
    ButtonClasses =
      "px-6 py-2 rounded flex-1  font-semibold text-white w-full cursor-not-allowed duration-500 bg-[#51b8c2]";
  else {
    ButtonClasses =
      "px-6 py-2 rounded flex-1  font-semibold text-white w-full hover:bg-[#1b4b50] duration-500 bg-[#05808b]";
  }

  return (
    <div className="course bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-500 cursor-pointer flex justify-between items-center flex-col md:flex-row p-6 h-full gap-10 border hover:border-[#05808b] ">
      {/* Course Image */}
      <div className="courseImage max-h-[20rem] overflow-hidden sm:w-[50%] rounded-2xl">
        <LazyLoadImage
          src={course?.courseImage}
          alt={`Cover image for ${course?.courseName}`}
          effect="blur"
          className="w-full h-full object-cover "
        />
      </div>

      {/* Course Details */}
      <div className="w-full flex-1">
        <div className="w-full">
          <h1 className="text-2xl font-bold text-[#05808b] mb-3">
            {course?.courseName}
          </h1>
          <p className="text-gray-600">{course?.description}</p>
        </div>
        <div className="my-4">
          {/* <CourseInfo
            students={course?.courseId?.students}
            lessons={course?.courseId?.sections}
          /> */}
        </div>
        {/* Progress Par */}
        {section === "courses" && (
          <div className="flex flex-col my-4">
            <div className="text-sm flex justify-between items-center">
              <p>Progress</p>
              <p>{coursePercentage}%</p>
            </div>
            <div className="bg-teal-500/20 h-3 rounded my-2 w-full">
              <div
                className={`bg-gradient-to-r from-sky-900 to-teal-500 h-full rounded `}
                style={{ width: `${coursePercentage}%` }}
              ></div>
            </div>
          </div>
        )}

        {/* Go To Course Button */}
        <div>
          <button
            className={ButtonClasses}
            onClick={() => action(course)}
            disabled={section === "available" && isEnrolled}
          >
            {section === "courses"
              ? coursePercentage && coursePercentage > 0
                ? "Continue"
                : "Go to Course"
              : null}

            {section === "available"
              ? isEnrolled
                ? "Enrolled"
                : "Enroll"
              : null}
          </button>
        </div>
      </div>
    </div>
  );
}
