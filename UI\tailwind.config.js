import { Outline } from "react-pdf";

/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  // darkMode: "selector",
   darkMode: "class",
  theme: {
    extend: {
      keyframes: {
        burgerMenu: {
          "0%": {
            "max-height": 0,
          },
          "100%": {
            "max-height": "900px",
          },
        },
        burgerGroups: {
          "0%": {
            opacity: 0,
            transform: "translateX(100px)",
          },
          "100%": {
            opacity: 1,
            transform: "translateX(0px)",
          },
        },
        languages: {
          "0%": {
            opacity: 0,
            transform: "translateY(100px)",
          },
          "100%": {
            opacity: 1,
            transform: "translateY(0px)",
          },
        },
        burgerMenuClose: {
          "0%": {
            "max-height": "900px",
          },
          "100%": {
            "max-height": "0",
          },
        },
        burgerGroupsClose: {
          "0%": {
            opacity: 1,
            transform: "translateX(0px)",
          },
          "100%": {
            opacity: 0,
            transform: "translateX(100px)",
          },
        },
      },
      animation: {
        burgerMenu: "burgerMenu 1.2s forwards ease-in-out",
        burgerMenuReverse: "burgerMenuClose 1.2s forwards ease-in-out",
        burgerGroup: "burgerGroups 0.9s forwards ease-in-out",
        burgerGroupReverse: "burgerGroupsClose 0.9s  forwards ease-in-out",
        languageSelections: "languages 0.9s forwards ease-in-out",
      },
      fontFamily: {
        inter: ["Inter", "sans-serif"],
        times: [' "Times New Roman" ', "sans-serif"],
      },
      colors: {
        primary: "#05808B",
        lightBg: '#E1DEE0',
        secondary: {
          DEFAULT: "#2A939C",
          "lite-gray":"#F5F4F5",
          "primary-lite": "#07838F33",
          "gray-100": "#EAEAEA",
          "gray-200": "#57595A",
          "gray-300": "#D9D9D9",
          "lite-100": "#9E9E9E",
          "lite-200": "#626364",
          "danger-100": "#AB0202",
          "danger-200": "#AB020221",
          "green-100": "#00C0A933",
          "yellow-100": "#FFAB0736",
          "yellow-200": "#FFAB07",
          100: "#5BA2A9",
          200: "#2D2D2D",
          300: "#00C0A9",
          400: "#07838F",
          500: "#9C9C9C",
        },
        bg: {
          'lite1': '#F2F2F2',
          'lite2': '#07838F1A',
          'red1': "#EA0B0B",
          'green1': "#08B12D",
          'yellow1': "#F4B351",
          'yellow2': "#FFF504",
        }
      },
      boxShadow: {
        "custom-text": "2px 0px 0px black",
      },
      dropShadow: {
        burgerMenuGroup: "0px 0px 10px rgba(0, 0, 0, 0.1)",
      },
      scrollbar: {
        custom: {
          "&::-webkit-scrollbar": {
            width: "12px",
          },
          "&::-webkit-scrollbar-track": {
            background: "#f1f1f1",
          },
          "&::-webkit-scrollbar-thumb": {
            background: "#888",
            borderRadius: "10px",
          },
          "&::-webkit-scrollbar-thumb:hover": {
            background: "#555",
          },
        },
      },
    },
  },
  variants: {
    scrollbar: ["rounded"],
  },
  plugins: [
    function ({ addUtilities }) {
      const newUtilities = {
        ".text-shadow-custom": {
          textShadow: "2px 0px 0px black",
        },
        ".scrollbar-hide": {
          /* Hide scrollbar for Chrome, Safari and Opera */
          "&::-webkit-scrollbar": {
            display: "none",
          },
          /* Hide scrollbar for IE, Edge and Firefox */
          "-ms-overflow-style": "none",
          "scrollbar-width": "none",
        },
        ".scrollbar-custom": {
          "&::-webkit-scrollbar": {
            width: "3px",
          },
          "&::-webkit-scrollbar-track": {
            background: "#f1f1f1",
          },
          "&::-webkit-scrollbar-thumb": {
            background: "#2A939C",
            borderRadius: "10px",
          },
          "&::-webkit-scrollbar-thumb:hover": {
            background: "#555",
          },
        },
      };
      addUtilities(newUtilities, ["responsive", "hover"]);
    },
  ],
};

