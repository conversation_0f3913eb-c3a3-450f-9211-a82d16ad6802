import csv
from datetime import datetime

from datamodel.uiresponse import QuestionObject, QuestionSet, UIResponse


def create_question_object(row):
    # Adjusted to create a QuestionObject instance
    return QuestionObject(
        questionId=row["questionId"],
        scopeId=row["scopeId"],
        categoryId=row["categoryId"],
        controlQuestion=row["controlQuestion"],
        questionText=row["questionText"],
        questionOption=[
            {int(i): row[f"option{i}"]} for i in range(6)
        ],
        questionDescription="",
        questionMandatory=True,
        questionOrder=int(row["questionOrder"]),
        responseOption=0,
        responseNote="",
        # Assuming a default value for responseDT
        responseDT=datetime.now().isoformat(sep=" ", timespec="minutes"),
    )


class CSVConverter:
    def __init__(self, csv_file_path, json_file_path):
        self.csv_file_path = csv_file_path
        self.json_file_path = json_file_path
        self.data = {}

    def convert(self):
        self.read_csv()
        self.write_json()

    def read_csv(self):
        with open(self.csv_file_path, mode='r') as csvfile:
            csv_reader = csv.DictReader(csvfile)
            for row in csv_reader:
                question_object = create_question_object(row)
                self.group_questions(question_object, row)

    def group_questions(self, question_object, row):
        key = (row["setId"], row["scopeText"], row["categoryText"])
        if key in self.data:
            self.data[key].questionObjects.append(question_object)
        else:
            question_set = QuestionSet(
                setId=row["setId"],
                questionObjects=[question_object],
                scopeText=row["scopeText"],
                categoryText=row["categoryText"],
                scopeOrder=int(row.get("scopeOrder", 0)),  # Example default value
                categoryOrder=int(row.get("categoryOrder", 0)),  # Example default value
            )
            self.data[key] = question_set

    def write_json(self):
        output = UIResponse(
            responseId="",  # Assuming a default value
            questionSet=list(self.data.values())
        )

        with open(self.json_file_path, mode='w') as jsonfile:
            jsonfile.write(output.json(indent=4))  # Using Pydantic's json method for serialization
        with open(self.json_file_path, mode='w') as jsonfile:
            jsonfile.write(output.json(indent=4))  # Using Pydantic's json method for serialization
