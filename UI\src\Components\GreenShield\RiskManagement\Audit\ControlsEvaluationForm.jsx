import { useEffect, useState } from "react";
import { MdKeyboardArrowDown } from "react-icons/md";
import authConfig from "@/Api/authConfig";


import {
  Select,
  Button,
  Textarea,
  Loader,
  Input,
  InputWrapper,
  FileInput,
  Modal,
} from "@mantine/core";


import AiApi from "@/Api/aiApiConfig";

import { showNotification } from "@mantine/notifications";
import { DateInput } from "@mantine/dates";
import { AIIcon, ESGAuditCalenderIcon, ESGAuditFileAddIcon } from "@/assets/icons";
import ApiS3 from "@/Api/apiS3";
import Loading from "@/Components/Loading";
import { useForm } from "@mantine/form";
import { toast } from "react-toastify";
import { useDisclosure } from "@mantine/hooks";
import { FaRegCopy } from "react-icons/fa";
import { aiTextType } from "@/Pages/S3GreenShield/financialServicesStart/Partials/ESGRiskManagementSystem/RiskIdentificationAndAssessment/components/StaticData";

const ControlsEvaluationForm = ({
  handleChange,
  formData,
  isAiDone,
  resetControls,
  setActiveTab,
}) => {
  let { evaluation } = formData;
  const [opened, { open, close }] = useDisclosure(false);

  const [loading, setLoading] = useState(false);
  const [strategies, setStrategies] = useState([]);
  const [mitigationValue, setmitigationValue] = useState("");
  const [aiText, setAiText] = useState("");

  const [aiLoad, setAiLoad] = useState(false);

  const aiFunc = async (type) => {
    // if (evaluation.length < 20) {
    //   showNotification({ message: `Minimum character 20`, color: "red" });
    //   return;
    // }

    const data = {
      processor: "audit_evaluation",
      resources: {
        mitigation_strategy: mitigationValue,
        implementation_status: formData.implementationStatus,
        design_effectiveness: formData.designEffectiveness,
        operating_effectiveness: formData.operatingEffectiveness,
        output_type: type,
      },
    };
    setAiLoad(true);
    try {
      const res = await AiApi.post(`process_request`, data);
      setAiText(res.data.ai_response);
      open();
      // setIsAiDone(true);
    } catch (er) {
      console.log("🚀 ~ aiFunc ~ er:", er);
    }
    setAiLoad(false);
  };
  const [company, setCompany] = useState([]);

  useEffect(() => {
    const Company = async () => {
      try {
        const response = await authConfig.get("/get_all_user_by_token");

        const users = response.data.companyUsers;

        if (Array.isArray(users)) {
          const userAuthId = users
            .filter((user) => !!user.userAuthId)
            .map((user) => ({
              value: user.userAuthId,
              label: user.userName,
            }));
          setCompany(userAuthId);
        } else {
          console.error("Error:", response.data);
        }
      } catch (error) {
        console.error("Error company ", error);
      }
    };

    Company();
  }, []);


  useEffect(() => {
    async function getStrategies() {
      setLoading(true);
      try {
        const response = await ApiS3.get("/mitigation-strategy-types");
        if (response.status === 200) {
          setStrategies(
            response.data.map((strategy) => ({
              ...strategy,
              value: strategy._id,
              label: strategy.name,
            }))
          );
        }
      } catch (error) {
        toast.fail("Something went wrong");
        setStrategies(["Something went wrong"]);
        console.log(error.message);
      } finally {
        setLoading(false);
      }
    }

    getStrategies();
  }, []);

  useEffect(() => {
    const mitigationText = strategies.find(
      (strategy) => strategy._id == formData.controlDescription
    );
    setmitigationValue(mitigationText?.name);
  }, [formData.controlDescription]);

  const form = useForm({
    mode: "controlled",
    initialValues: {
      ...formData,
    },
  });

  const handleSubmit = form.onSubmit(async (values) => {
    const formData = new FormData();
    Object.keys(values).forEach((key) => formData.append(key, values[key]));
    try {
      const response = await ApiS3.post("/auditControls", formData);
      if (response.status === 201) {
        form.reset();
        form.setValues({
          strategy: null,
          status: null,
          design: null,
          operating: null,
          evidence: null,
        });

        resetControls();

        toast.success("Control created successfully");
        setActiveTab(2);
      }
    } catch (error) {
      toast.fail("Something went wrong");

      console.log(error.message);
    }
  });
  const copyFunc = () => {
    navigator.clipboard
      .writeText(aiText)
      .then(() => {
        showNotification({
          message: "Text copied to clipboard!",
          color: "green",
        });
        close();
      })
      .catch(() => {
        showNotification({ message: "Failed to copy text", color: "red" });
      });
  };

  return loading ? (
    <Loading />
  ) : (
    <>
      <Modal size={"lg"} opened={opened} onClose={close} title="Ai suggestion">
        <p className="border border-gray-300 rounded-2xl p-2 mb-3">{aiText}</p>

        <Button className="bg-primary" onClick={copyFunc}>
          Copy <FaRegCopy />
        </Button>
      </Modal>
      <form onSubmit={handleSubmit} className="my-10">
        <h5 className="text-primary font-bold text-center mb-10 lg:text-2xl">
          Controls Evaluation
        </h5>
        <div className="flex-wrap sm:flex-nowrap flex items-center justify-between gap-4">
          <InputWrapper label="Control ID">
            <Input
              className="my-2"
              placeholder="Auto"
              {...form.getInputProps("id")}
              size="md"
              disabled
            />
          </InputWrapper>

          <InputWrapper label="Mitigation Strategy" className="w-full">
            <Select
              className="my-2"
              rightSection={<MdKeyboardArrowDown />}
              name="controlDescription"
              placeholder="Auto linked to Mitigation Strategies"
              {...form.getInputProps("controlDescription")}
              onChange={(value) => {
                handleChange("controlDescription", value);
                form.setFieldValue("controlDescription", value);
              }}
              data={strategies}
              size="md"
              required
            />
          </InputWrapper>

          <InputWrapper label="Implementation Status" className="w-full">
            <Select
              className="my-2"
              rightSection={<MdKeyboardArrowDown />}
              name="implementationStatus"
              placeholder="Implemented"
              data={["Implemented", "Partially", "Not Implemented"]}
              size="md"
              {...form.getInputProps("implementationStatus")}
              onChange={(value) => {
                handleChange("implementationStatus", value);
                form.setFieldValue("implementationStatus", value);
              }}
              required
            />
          </InputWrapper>
        </div>
        <div className="flex-wrap sm:flex-nowrap flex items-center justify-between gap-4">
          <InputWrapper label="Design Effectiveness" className="w-full">
            <Select
              className="my-2"
              rightSection={<MdKeyboardArrowDown />}
              {...form.getInputProps("designEffectiveness")}
              onChange={(value) => {
                handleChange("designEffectiveness", value);
                form.setFieldValue("designEffectiveness", value);
              }}
              name="designEffectiveness"
              placeholder="Effective"
              data={["Effective", "Partially Effective", "Ineffective"]}
              size="md"
              required
            />
          </InputWrapper>

          <InputWrapper label="Operating Effectiveness" className="w-full">
            <Select
              className="my-2"
              rightSection={<MdKeyboardArrowDown />}
              {...form.getInputProps("operatingEffectiveness")}
              onChange={(value) => {
                handleChange("operatingEffectiveness", value);
                form.setFieldValue("operatingEffectiveness", value);
              }}
              name="operatingEffectiveness"
              placeholder="Effective"
              data={["Effective", "Partially Effective", "Ineffective"]}
              size="md"
              required
            />
          </InputWrapper>
        </div>

        <div className="relative mb-8 mt-8">
          <div className="flex justify-between items-center">
            <label
              htmlFor="evaluation"
              className="text-base font-medium text-black"
            >
              Evaluation
            </label>

          </div>

          <div className="relative border border-gray-300 min-h-[145px] rounded py-2 bg-white">
            <div
              title="Ai Suggestion"
              disabled={aiLoad}
              className={`absolute right-2 top-2 group`}
            >
              {aiLoad ? (
                <Loader color="#07838F" size="sm" type="dots" />
              ) : (
                <AIIcon className="cursor-pointer" />
              )}
              <div
                className={`${aiLoad ? "hidden" : "flex"
                  } gap-1 absolute z-20 top-5 right-0 flex-col opacity-0 group-hover:opacity-100 transition-opacity duration-200`}
              >
                {aiTextType.map((type, index) => (
                  <Button
                    variant="transparent"
                    key={index}
                    onClick={() => aiFunc(type)}
                    className={`base-border p-1 capitalize bg-white hover:bg-primary hover:text-white`}
                  >
                    {type}
                  </Button>
                ))}
              </div>
            </div>

            <Textarea
              id="evaluation"
              {...form.getInputProps("evaluation")}
              onChange={(e) => {
                handleChange("evaluation", e.target.value);
                form.setFieldValue("evaluation", e.target.value);
              }}
              placeholder="Enter Text"
              classNames={{
                root: "w-[96%] min-h-[145px]",
                input: "min-h-[145px] border-none",
              }}
              required
            />


          </div>

        </div>
        <div className="flex-wrap sm:flex-nowrap flex items-center justify-between gap-4">
          <InputWrapper label="Test Date" className="w-full">
            <DateInput
              className="my-2"
              rightSection={<ESGAuditCalenderIcon />}
              {...form.getInputProps("testDate")}
              onChange={(value) => {
                handleChange("testDate", value);
                form.setFieldValue("testDate", value);
              }}
              placeholder="Date"
              size="md"
              rightSectionPointerEvents="none"
              required
            />
          </InputWrapper>
          <InputWrapper label="Evaluator" className="w-full">
            <Select
              className="mt-4"
              placeholder="Select evaluator"
              data={company}
              value={form.values.evaluator}
              onChange={(value) => {
                handleChange("evaluator", value);
                form.setFieldValue("evaluator", value);
              }}
              size="md"
              required
              searchable
              maxDropdownHeight={200} 
              nothingFound="No evaluators found"
            />
          </InputWrapper>

        </div>
        <div className="flex-wrap sm:flex-nowrap flex items-center justify-between gap-4">
          <InputWrapper label="Evidence" className="w-full">
            <FileInput
              className="my-2"
              leftSection={<ESGAuditFileAddIcon />}
              {...form.getInputProps("evidence")}
              onChange={(value) => {
                handleChange("evidence", value);
                form.setFieldValue("evidence", value);
              }}
              placeholder="Click to upload evidence"
              leftSectionPointerEvents="none"
              required
            />
          </InputWrapper>
          <InputWrapper label="Next Review Date" className="w-full">
            <DateInput
              className="my-2"
              rightSection={<ESGAuditCalenderIcon />}
              {...form.getInputProps("nextReviewDate")}
              onChange={(value) => {
                handleChange("nextReviewDate", value);
                form.setFieldValue("nextReviewDate", value);
              }}
              placeholder="Date"
              size="md"
              required
              rightSectionPointerEvents="none"
            />
          </InputWrapper>
        </div>
        <div className="flex gap-5 mt-5">
          <Button
            onClick={() => {
              form.reset();
              form.setValues({
                controlDescription: null,
                status: null,
                design: null,
                operating: null,
                evidence: null,
              });
              resetControls();
            }}
            className="flex-1 bg-bg-lite1 text-gray-700 py-2 px-4 text-center border-r border-gray-300 hover:bg-teal-700 duration-500"
          >
            Clear
          </Button>
          <Button
            type="submit"
            className="flex-1 bg-teal-500 text-white py-2 px-4 text-center hover:bg-teal-700 duration-500"
          >
            Save
          </Button>
        </div>
      </form>
    </>
  );
};

export default ControlsEvaluationForm;
