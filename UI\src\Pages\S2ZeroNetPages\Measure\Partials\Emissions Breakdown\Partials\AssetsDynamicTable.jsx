import ApiS2 from "@/Api/apiS2Config";
import Loading from "@/Components/Loading";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import S2Layout from "@/Layout/S2Layout";
import { formatNumber } from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/StaticData";
import { Pagination, ScrollArea, Table, TextInput } from "@mantine/core";
import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { CiSearch } from "react-icons/ci";
import { FaChevronLeft } from "react-icons/fa";
import { useNavigate, useParams } from "react-router";
import { driver } from "driver.js";
import "driver.js/dist/driver.css";
import "@/assets/css/index.css";
import { FaQuestionCircle } from "react-icons/fa";

export default function AssetsDynamicTable() {
  const { category, supCategory, id } = useParams();
  const [totalEmissions, setTotalEmissions] = useState([]);
  const [totalEmissionsNum, setTotalEmissionsNum] = useState(0);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const rowsPerPage = 10;
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { netZeroMenu } = useSideBarRoute();
  const activeTab = "Assets";
  const [elementsReady, setElementsReady] = useState(false);

  useEffect(() => {
    if (!category || !supCategory) return;

    const fetchData = async () => {
      setLoading(true);

      try {
        const { data } = await ApiS2.get("/admin/get_emissions_breakdown");

        const categoryData = data?.emissions_breakdown?.[category];
        const emissions =
          category === "scope_breakdown"
            ? categoryData?.[id]?.categories?.[supCategory]?.[
                "associated_items"
              ] || []
            : categoryData?.[supCategory]?.["associated_items"] || [];

        setTotalEmissions(emissions);

        const totalEmission =
          category === "scope_breakdown"
            ? categoryData?.[id]?.categories?.[supCategory]?.total_emission || 0
            : categoryData?.[supCategory]?.total_emission || 0;

        setTotalEmissionsNum(totalEmission);
      } catch (error) {
        console.error("Error fetching emissions data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [category, supCategory]);

  const totalPages = Math.ceil(totalEmissions.length / rowsPerPage);
  const currentData = useMemo(() => {
    return totalEmissions.slice(
      (currentPage - 1) * rowsPerPage,
      currentPage * rowsPerPage
    );
  }, [totalEmissions, currentPage]);

  const filteredData = useMemo(() => {
    if (!search) return currentData;
    return currentData.filter((item) => {
      const { companyAsset, customFactor, reportingYear } = item;
      return (
        companyAsset?.assetName?.toLowerCase().includes(search.toLowerCase()) ||
        Object.values(customFactor?.activity || {}).some((val) =>
          val.toString().toLowerCase().includes(search.toLowerCase())
        ) ||
        Object.values(customFactor?.eFactors || {}).some((val) =>
          val.toString().toLowerCase().includes(search.toLowerCase())
        ) ||
        Object.values(customFactor?.uom || {}).some((val) =>
          val.toString().toLowerCase().includes(search.toLowerCase())
        ) ||
        reportingYear?.toString().includes(search)
      );
    });
  }, [search, currentData]);

  const rows = filteredData.map((item, i) => (
    <Table.Tr key={i} className="text-sm font-bold text-[#626364] text-center">
      <Table.Td>{item.fromDate}</Table.Td>
      <Table.Td>{item.toDate}</Table.Td>
      <Table.Td>
        {formatNumber(item?.calculatedCarbonFactors?.Total_Emissions)}
      </Table.Td>
      <Table.Td>
        {Object.values(item?.customFactor?.activity || {}).map((val, idx) => (
          <p key={idx} className="mt-2">
            {val}
          </p>
        ))}
      </Table.Td>
      <Table.Td>
        {Object.values(item?.customFactor?.eFactors || {}).map((val, idx) => (
          <p key={idx} className="mt-2">
            {val}
          </p>
        ))}
      </Table.Td>
      <Table.Td>
        {Object.values(item?.customFactor?.uom || {}).map((val, idx) => (
          <p key={idx} className="mt-2">
            {val}
          </p>
        ))}
      </Table.Td>
      <Table.Td>
        {Object.values(item?.quantity || {}).map((val, idx, arr) => (
          <p key={idx} className="mt-2">
            {val}
            {idx !== arr.length - 1 && ","}
          </p>
        ))}
      </Table.Td>
      <Table.Td>{item.reportingYear}</Table.Td>
    </Table.Tr>
  ));

  const getGuideSteps = () => [
    {
      element: ".A-dynamic-table",
      popover: {
        title: t(
          "A dynamic table lists all recently uploaded emission data for the selected asset."
        ),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".A-dynamic-table-search",
      popover: {
        title: t(
          "Use Search Functionality"
        ),
        description: t("Search bar is available to quickly find specific entries by any keyword (e.g., Activity type, Date, etc.)."),
        side: "left",
        align: "center",
      },
    },
  ];

  const checkElementsExist = () => {
    const steps = getGuideSteps();
    const allElementsExist = steps.every((step) =>
      document.querySelector(step.element)
    );
    return allElementsExist;
  };

  useEffect(() => {
    if (activeTab === "Assets") {
      const observer = new MutationObserver(() => {
        if (checkElementsExist()) {
          setElementsReady(true);
          observer.disconnect();
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true,
      });

      return () => observer.disconnect();
    }
  }, [activeTab, elementsReady]);

  const startGuide = () => {
    const driverObj = driver({
      showProgress: true,
      popoverClass: "my-custom-popover-class",
      nextBtnText: "Next",
      prevBtnText: "Back",
      doneBtnText: "Done",
      overlayColor: "rgba(0, 0, 0, 0)",
      progressText: "Step {{current}} of {{total}}",
      steps: getGuideSteps(),
      onDestroyStarted: () => {
        localStorage.setItem("hasSeenemissionsBreaktableGuide", "true");
        driverObj.destroy();
      },
    });
    driverObj.drive();
  };

  useEffect(() => {
    const hasSeenGuide = localStorage.getItem("hasSeenemissionsBreaktableGuide");
    if (!hasSeenGuide) {
      startGuide();
    }
    return () => {
      const driverObj = driver();
      if (driverObj.isActive()) {
        driverObj.destroy();
      }
    };
  }, [activeTab, elementsReady]);

  return (
    <S2Layout menus={netZeroMenu} navbarTitle={"Measure"}>
      {loading ? (
        <Loading />
      ) : (
        <>
                    <div
                      onClick={startGuide}
                      style={{
                        position: "fixed",
                        bottom: 20,
                        right: 20,
                        cursor: "pointer",
                        zIndex: 1000,
                      }}
                    >
                      <div className="h-12 w-12 flex items-center justify-center bg-gradient-to-tr from-[#2C5A8C] via-[#1C889C] to-[#13B1A8] p-1  cursor-pointer rounded-full">
                        <FaQuestionCircle
                          size={34}
                          color="#ffffff"
                          className="mx-auto cursor-pointer"
                        />
                      </div>
                    </div>
          <div className="flex justify-between items-center">
            <h1 className="font-medium text-3xl font-inter flex items-center">
              <span
                className="text-base p-2 border-2 border-primary rounded-lg me-1 bg-[#E6F3F4] cursor-pointer"
                onClick={() => navigate(-1)}
              >
                <FaChevronLeft className="text-primary" />
              </span>
              Assets
            </h1>
            <p className="p-2 bg-white rounded-lg font-medium text-base">
              Total Emission:{" "}
              <span className="text-primary font-bold">
                {formatNumber(totalEmissionsNum)}
              </span>
              <small>(T CO₂e)</small>
            </p>
          </div>
          <div className="bg-white mt-7 p-1 rounded-lg overflow-hidden">
            <div className="p-2 mt-1 grid items-center xl:justify-between px-4 bg-white xl:grid-cols-3 rounded-t-lg w-full">
              <h1 className="font-bold text-lg text-black xl:col-span-1 text-center xl:text-left A-dynamic-table">
                {t("Recent upload Measure data")}
              </h1>
              <TextInput
                className="w-full xl:col-span-2 A-dynamic-table-search"
                placeholder="Search..."
                leftSection={<CiSearch className="w-5 h-5" />}
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
            </div>
            {filteredData.length === 0 ? (
              <h1 className="mt-5 text-center">Your Search is not Found</h1>
            ) : (
              <>
                <ScrollArea>
                  <Table withTableBorder highlightOnHover>
                    <Table.Thead className="bg-[#f5f4f5] text-secondary-500 font-bold text-base text-center">
                      <Table.Tr>
                        <Table.Th className="text-center">From</Table.Th>
                        <Table.Th className="text-center">To</Table.Th>
                        <Table.Th className="text-center">
                          Total Emissions
                        </Table.Th>
                        <Table.Th className="text-center">Activity</Table.Th>
                        <Table.Th className="text-center">eFactors</Table.Th>
                        <Table.Th className="text-center">UOM</Table.Th>
                        <Table.Th className="text-center">Quantity</Table.Th>
                        <Table.Th className="text-center">
                          Reporting Year
                        </Table.Th>
                      </Table.Tr>
                    </Table.Thead>
                    <Table.Tbody>{rows}</Table.Tbody>
                  </Table>
                </ScrollArea>
                <div className="md:flex justify-between mt-5">
                  <p
                    className="text-sm text-black"
                    hidden={!totalEmissions?.length}
                  >
                    {t("showingData", {
                      start: (currentPage - 1) * rowsPerPage + 1,
                      end: Math.min(
                        currentPage * rowsPerPage,
                        totalEmissions?.length
                      ),
                      total: totalEmissions?.length,
                    })}
                  </p>
                  <Pagination
                    page={currentPage}
                    onChange={(e) => {
                      setCurrentPage(e);
                    }}
                    total={totalPages}
                    color="#dde7e9"
                    autoContrast
                    className={`flex justify-center mt-5 gap-0 md:mt-0 ${
                      !totalEmissions?.length && "hidden"
                    }`}
                    classNames={{ control: "gap:none rounded-none" }}
                  />
                </div>
              </>
            )}
          </div>
        </>
      )}
    </S2Layout>
  );
}
