@tailwind base;
@tailwind components;
@tailwind utilities;


body {
  @apply bg-white text-black;
}

.dark body {
  @apply bg-[#1E1E1E] text-white;
}



.active .step {
  @apply bg-[#1C4D88];
}

.gradient-text {
  background: linear-gradient(90deg, #2C5A8C 0%, #1C889C 50%, #13B1A8 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}


.complete .step {
  @apply text-black duration-500 bg-green-300;
}

.full-center {
  @apply flex justify-center items-center;
}

.animate-3 {
  @apply transition-all duration-300 ease-in-out;
}


.base-border {
  @apply border-[#E8E7EA] border-2 
}


::selection {
  background-color: #16999a;
  color: #fff;
}

.bg-dots {
  background-color: #ffffff; /* Default background color */
  background-image: radial-gradient(#888 0.05em, transparent 0.05em),
    radial-gradient(#888 0.05em, transparent 0.05em);
  background-size: 1.5em 1.5em;
  background-position: 0 0, 0.75em 0.75em;
}
/* .chatbot-button {
  background-image: url('../images/smallchat.png');
  background-size: cover;
  background-repeat: no-repeat;
  transition: background-image 0.3s ease;
}

.chatbot-button:hover {
  background-image: url('../svg/Chatbot.png');
  
} */
.chatbox-scrollbar::-webkit-scrollbar {
  width: 12px;
}
.chatbox-scrollbar::-webkit-scrollbar-track {
  background: #f0f0f0;
}
.chatbox-scrollbar::-webkit-scrollbar-thumb {
  background-color: #a8a8a8;
  border-radius: 20px;
  border: 3px solid #f0f0f0;
}
.chatbox-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #919191;
}

.report-selects .mantine-InputPlaceholder-placeholder,
.report-selects .mantine-Select-input::placeholder {
  color: #16999a;
}

.report-page .year-pick .mantine-InputPlaceholder-placeholder {
  color: white;
}

.open-chatbot {
  opacity: 1;
  transform: translateY(0);
}

.close-chatbot {
  opacity: 0;
  transform: translateY(20px);
}

.grediant-bg {
  background: linear-gradient(243.86deg, #03e2c8 5.96%, #07838f 97.48%);
}

/*--- Start support & help page */
.support-request .mantine-Textarea-input {
  height: 147px;
}

.support-request label {
  font-size: 14px;
}

.f-q-section ul li {
  list-style-type: circle;
}
/*--- End support & help page */

/* any ul have this class the ( li ) will be disc */
.li-circle li {
  list-style-type: disc;
}

/* chart */
.chart-container {
  position: relative;
  width: 100%; /* Adjust width as needed */
  height: 300px; /* Adjust height as needed */
}

.circle {
  position: absolute;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.small-circle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: white;
  position: absolute;
}

/* Add specific styles for each circle category */
.category-Extreme {
  background-color: red;
}

.category-High {
  background-color: orange;
}

.category-Low {
  background-color: blue;
}

.category-Medium {
  background-color: green;
}

.squar-chart {
  background: linear-gradient(
    79.55deg,
    #02ae50 0%,
    #bdba16 26.5%,
    #ffbe00 54.5%,
    #fd6418 83%,
    #f72e24 100%
  );
}

/* chart */

.typing-indicator {
  display: flex;
  gap: 0.2rem;
}

.typing-indicator .dot {
  animation: bounce 1s infinite;
}

.typing-indicator .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.burger-menu-main {
  @apply rounded-[10px] p-[15px] max-w-[280px] z-50 gap-[24px] bg-secondary-gray-300 border-white border-[2px];
}
.burger-menu-group {
  @apply bg-white   rounded-[10px] w-full items-center z-50 gap-2 sm:gap-4 grid grid-cols-[auto_1fr] drop-shadow-burgerMenuGroup px-[22px]  py-[16px];
}
.language-list {
  z-index: 99999999 !important;
}


.active-tab {
  background-color: #07838F1A;
  color: #008287;
  position: relative;
}

.active-tab::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6px;
  background-color: #07838F;
  border-radius: 9999px 0 0 9999px;
}

.my-custom-popover-class {
  background-color: #07838F;
  border-radius: 8px;
  padding: 16px;
  font-family: 'Arial', sans-serif;
  position: relative;
  max-width: 380px;
}

.my-custom-popover-class.driver-popover {
  background-color: #07838F;
}

.my-custom-popover-class .driver-popover-arrow-side-left.driver-popover-arrow {
  border-left-color: #07838F;
  border-width: 10px 10px 10px 10px;
}

.my-custom-popover-class .driver-popover-arrow-side-right.driver-popover-arrow {
  border-right-color: #07838F;
  border-width: 10px 10px 10px 10px;
}

.my-custom-popover-class .driver-popover-arrow-side-top.driver-popover-arrow {
  border-top-color: #07838F;
  border-width: 10px 10px 10px 10px;
}

.my-custom-popover-class .driver-popover-arrow-side-bottom.driver-popover-arrow {
  border-bottom-color: #07838F;
  border-width: 10px 10px 10px 10px;
}

.my-custom-popover-class .driver-popover-title {
  font-size: 16px;
  font-weight: bold;
  color: #FFFFFF;
  margin-top: 10px;
  margin-bottom: 8px;
  min-height: 10px;
}

.my-custom-popover-class .driver-popover-description {
  font-size: 12px;
  color: #FFFFFF;
  line-height: 1.4;
  margin-bottom: 12px;
  font-weight: 500;
}

.my-custom-popover-class .driver-popover-progress-text {
  font-size: 12px;
  color: #FFFFFF;
  margin-bottom: 12px;
}

.my-custom-popover-class .driver-popover-prev-btn,
.my-custom-popover-class .driver-popover-next-btn,
.my-custom-popover-class .driver-popover-close-btn {
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.my-custom-popover-class .driver-popover-prev-btn {
  background-color: transparent;
  border: 1px solid #FFFFFF;
  color: #FFFFFF;
}

.my-custom-popover-class .driver-popover-prev-btn:hover {
  background-color: #FFFFFF;
  border-color: #FFFFFF; 
  color: #07838F;
}

.my-custom-popover-class .driver-popover-next-btn {
  background-color: #FFFFFF;
  border: none;
  color: #07838F;
}

.my-custom-popover-class .driver-popover-next-btn:hover {
  background-color: #045f68;
  color: #FFFFFF;
}

.my-custom-popover-class .driver-popover-close-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  background: none;
  border: none;
  color: white !important;
  cursor: pointer;
  font-weight: bold;
  font-size: 18px;
  padding: 0;
}

.my-custom-popover-class .driver-popover-close-btn:hover {
  color: #FFFFFF;
}

.my-custom-popover-class .driver-popover-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.my-custom-popover-class .driver-popover-footer button {
  text-shadow: none;
}

/* .submit-loader {
  border: 11px solid #FFF;
  border-bottom-color: #FF3D00;
  border-radius: 50%;
  display: inline-block;
  position: relative;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}  */

.submit-loader {
  border: 2px solid #FFF;
  width: 23px;
  height: 23px;
  background: #05808b;
  border-radius: 50%;
  display: inline-block;
  position: relative;
  box-sizing: border-box;
  animation: rotation 2s linear infinite;
}
.submit-loader::after {
  content: '';  
  box-sizing: border-box;
  position: absolute;
  left: 50%;
  top: 50%;
  border: 11px solid;
  border-color: transparent #FFF;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
} 
@keyframes draw-check {
  0% {
    stroke-dasharray: 50;
    stroke-dashoffset: 50;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

.animate-draw-check {
  stroke-dasharray: 50;
  stroke-dashoffset: 0;
  animation: draw-check 0.5s ease-in-out forwards;
}
