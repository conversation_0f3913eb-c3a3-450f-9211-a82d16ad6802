import { ActionIcon, <PERSON>ton, Chip, Flex, useMantineTheme } from "@mantine/core";
import { IconDownload, IconEye, IconPencil, IconTrash } from "@tabler/icons-react";
import { Group } from "lucide-react";
import React from "react";
import { Table, Checkbox } from "@mantine/core";


const ResultTableRow = ({
  id,
  name,
  type,
  tags,
  lastModified,
  owner,
  status,
  selectedRows,
  setSelectedRows,
}) => {
  const theme = useMantineTheme();
  return (
    <Table.Tr
      key={name}
      bg={
        selectedRows.includes(id)
          ? "var(--mantine-color-blue-light)"
          : undefined
      }
    >
      <Table.Td>
        <Checkbox
          aria-label="Select row"
          checked={selectedRows.includes(id)}
          onChange={(event) =>
            setSelectedRows(
              event.currentTarget.checked
                ? [...selectedRows, id]
                : selectedRows.filter((otherid) => otherid !== id)
            )
          }
        />
      </Table.Td>
      <Table.Td>{name}</Table.Td>
      <Table.Td>{type}</Table.Td>
      <Table.Td>
        <Chip checked={false}>
          {tags}
        </Chip>
      </Table.Td>
      <Table.Td>{lastModified}</Table.Td>
      <Table.Td>{owner}</Table.Td>
      <Table.Td>{status}</Table.Td>

      <Table.Td>
        <Flex spacing={5} direction="row">
          {/* <IconEye size={16} color={theme.colors.gray[6]} />
          <IconPencil size={16} color={theme.colors.gray[6]} />
          <IconTrash size={16} color={theme.colors.red[6]} /> */}
          <ActionIcon variant="default" radius="md">
            <IconDownload size={16} color={theme.colors.gray[6]} />
          </ActionIcon>
        </Flex>
      </Table.Td>
    </Table.Tr>
  );
};

export default ResultTableRow;
