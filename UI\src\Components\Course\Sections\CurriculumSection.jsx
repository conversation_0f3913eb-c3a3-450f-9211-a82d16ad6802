import { useContext } from "react";
import { CourseContext } from "@/Contexts/CourseContext";
import Lecture from "../Lecture";
import { ArrowDownIcon, ArrowRightIcon } from "@/assets/icons";

function CurriculumSection() {
  const { course, expandedSection, toggleSection, progress } =
    useContext(CourseContext);
  const data = course?.courseId;
  return (
    <div className="mt-8">
      {data?.sections?.map((section, index) => (
        <div
          className="mb-4 border border-gray-200 rounded-lg cursor-pointer "
          key={index}
        >
          <div
            className="flex justify-between items-center p-4 bg-transparent hover:text-[#05808b] "
            onClick={() => toggleSection(index)}
          >
            <h3 className="text-lg font-semibold text-[#272727]">
              {section?.sectionName}
            </h3>
            <span>
              {expandedSection === index ? (
                <ArrowDownIcon />
              ) : (
                <ArrowRightIcon />
              )}
            </span>
          </div>
          <ul
            className={`px-2 bg-transparent rounded-xl ${
              expandedSection === index ? "h-full block" : "h-0 hidden"
            } duration-300`}
          >
            {section?.lectures?.map((lecture, lectureIndex) => (
              <Lecture
                key={lectureIndex}
                lecture={lecture}
                progress={progress}
                index={lectureIndex}
              />
            ))}
          </ul>
        </div>
      ))}
    </div>
  );
}

export default CurriculumSection;
