import { Link } from "react-router-dom";
import { FaArrowRightLong } from "react-icons/fa6";
import { TbClipboardText } from "react-icons/tb";
import { WiTime4 } from "react-icons/wi";
import { ReportID } from "@/assets/icons";

const ReportCard = ({
  previousReportid,
  framework,
  status,
  createdAt,
  updatedAt,
}) => (
  <Link
    to="/Insights-reporing/issb-reporting-suite/previous-report"
    state={{ previousReportid, framework, status, createdAt, updatedAt }}
    className="flex hover:border-[#2C5A8C] items-center rounded-xl w-full cursor-pointer border-2 bg-[#FFFFFF] p-4 shadow-sm transition-all mb-4"
  >
    <div className="flex max-sm:flex-col w-full md:items-center justify-between gap-4">
      <div className="flex flex-col gap-2">
        <h2 className="text-xl text-start font-bold text-[#494949]">
          {createdAt ? new Date(createdAt).getFullYear().toString() : "Unknown"}{" "}
          Annual ESG Report
        </h2>
        <div className="flex max-md:flex-col items-start md:items-center gap-1 text-[#494949]">
          <div className="flex items-center gap-2">
            <TbClipboardText className="text-xl" />
            <span className="mr-5">{framework}</span>
          </div>
          <div className="text-[16px]">
            <span>
              <WiTime4 className="inline text-xl text-[#494949]" /> Last edited:{" "}
              {updatedAt
                ? new Date(updatedAt).toLocaleDateString()
                : "Unknown Date"}
            </span>{" "}
          </div>
          <div className="ml-5 flex items-center">
            <ReportID />
            <span className="mx-1">Report ID:</span>
            {previousReportid}
          </div>
        </div>
      </div>

      <div className="flex items-center gap-2">
        <span
          className={`${
            status === "In Progress"
              ? "bg-[#DBEAFE] px-4 py-1 rounded-full text-[#1E40AF]"
              : status === "Completed"
              ? "bg-[#D1FAE5] px-4 py-1 rounded-full text-[#065F46]"
              : "bg-[#FEE2E2] px-4 py-1 rounded-full text-[#991B1B]"
          }`}
        >
          {status}
        </span>{" "}
      </div>
    </div>
    <span className="ml-2 text-[#A8B7B8]">
      <FaArrowRightLong className="text-[#A8B7B8]" />
    </span>
  </Link>
);

export default ReportCard;
