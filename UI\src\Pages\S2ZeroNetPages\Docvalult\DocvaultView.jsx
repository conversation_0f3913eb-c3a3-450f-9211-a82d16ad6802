import useSideBarRoute from "@/hooks/useSideBarRoute";
import S2Layout from "@/Layout/S2Layout";
import {
  Box,
  Button,
  Card,
  Flex,
  Select,
  Text,
  TextInput,
} from "@mantine/core";
import {
  IconFile,
  IconPlus,
  IconSearch,
} from "@tabler/icons-react";
import { useEffect } from "react";
import ResultTable from "./Partials/ResultTable";
import axios from "axios";
import Cookies from "js-cookie";
import { useDocvaultStore } from "@/Store/useDocvaultStore";
import { IoMdHome } from "react-icons/io";

const DocvaultView = () => {
  const { docvaultMenu } = useSideBarRoute();
  const {
    files,
    setApiFiles,
    searchTerm,
    setSearchTerm,
    searchType,
    setSearchType,
    addFile,
    applyFilters,
    filteredFiles
  } = useDocvaultStore((state) => state);
  // Sample data
  useEffect(() => {
     getFolderData().then((res) => {
      setApiFiles(res);
    });
  }, []);

  const handleFileSelect = async () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = "*"; // or specify types: "image/*", ".pdf", etc.

    input.onchange = async () => {
      const file = input.files[0];
      if (!file) return;
      
      try {
        let res = await axios.post(
          "https://docvault-staging.azurewebsites.net/api/v1/files/upload",
          file, // send raw binary
          {
            headers: {
              Authorization: `Bearer ${Cookies.get("level_user_token")}`,
              "Content-Type": "application/octet-stream",
              "x-filename": file.name,
            },
          }
        );
        const { data } = res.data;
        updatefiles(data.name, data.mimetype, data.id , data.size, data.created_at, data.updated_at);
      } catch (error) {
        console.error("Upload failed", error);
        alert("Upload failed: " + (error.response?.data?.message || "Unknown error"));
      }
    };

    input.click(); // trigger file picker
  };

  const getFolderData = async () => {
    try {
      let res = await axios.get(
        "https://docvault-staging.azurewebsites.net/api/v1/folders/explore",
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("level_user_token")}`,
          },
        }
      );
      const { data } = res.data;

      return data;
    } catch (e) {
      console.error("error", e);
    }
  }

  const updatefiles = (name,mimetype,id ,size,created_at,updated_at) => {
    let filesNames = files.map((file) => file.label);
    if (filesNames.includes(name)) {
      alert("File already exists");
      return;
    }
    let newfile = {
      label: name,
      value: name,
      type: "file",
      mimetype: mimetype,
      id: id,
      size: size,
      created_at: created_at,
      updated_at: updated_at,
      childrenLoaded: false,
      children: undefined,
    };
    addFile(newfile);
  };
  // Filtered data based on search term
  const setFilters = (value) => {
    setSearchTerm(value);
    applyFilters();

  };
  return (
    <S2Layout menus={docvaultMenu} navbarTitle={"Docvault"}
    breadcrumbItems={[
      { title: <IoMdHome size={20} />, href: "/get-started" },
      { title: "Docvault", href: "#" },
    ]}>
      <Card radius="md" p="md">
        {/* Header */}
        <Box mb="md">
          <Flex justify={"space-between"}className="flex-col md:flex-row flex">
            <div>
              <Text weight={700} size="lg">
                Evidence Records
              </Text>
              <Text color="dimmed">
                Effortless Audit-Ready Document Control
              </Text>
            </div>
            <Flex gap={5} align={"center"} className="flex-col md:flex-row flex"> 
              <Button
                leftSection={<IconPlus size={16} />}
                variant="filled"
                color="#07838F"
                onClick={handleFileSelect}
              >
                Upload Document
              </Button>
              <Button
                leftSection={<IconFile size={16} />}
                variant="light"
                color="#555F62"
              >
                New Folder
              </Button>
            </Flex>
          </Flex>
        </Box>

        {/* Search Bar */}
        <Flex direction={"row"} className="w-full" mb={"md"} gap={5}>
          <TextInput
            placeholder="Search"
            value={searchTerm}
            onChange={(event) => setFilters(event.target.value)}
            leftSection={<IconSearch size={16} />}
            flex={1}
            radius={"md"}
          />
          <Select
            placeholder="All"
            value={searchType}
            onChange={(value) => {
              setSearchType(value);
              applyFilters();
            }}
            data={[
              { value: "", label: "All" },
              { value: "PDF", label: "PDF" },
              { value: "DOCX", label: "DOCX" },
              { value: "TXT", label: "TXT" },
              { value: "JSON", label: "JSON" },
            ]}
            style={{ width: "150px", borderRadius: "16px" }}
            radius={"md"}
          />
        </Flex>

        {/* Table */}
        <ResultTable
          style={{ marginBottom: "16px" }}
        />
      </Card>
    </S2Layout>
  );
};

export default DocvaultView;
