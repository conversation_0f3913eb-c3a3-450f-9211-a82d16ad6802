import React, { useState } from "react";
import {
  Modal,
  Button,
  Checkbox,
  Input,
  Textarea,
  MultiSelect,
} from "@mantine/core";
import { IoIosArrowForward } from "react-icons/io";
import { YearPickerInput } from "@mantine/dates";
import { useTranslation } from "react-i18next";
import { isNotEmpty, useForm } from "@mantine/form";
import ApiS2 from "@/Api/apiS2Config";
import Loading from "@/Components/Loading";

import { showNotification } from "@mantine/notifications";
import { useDisclosure } from "@mantine/hooks";
import { MdEdit } from "react-icons/md";

const UpdateTarget = ({ data,fetetchAgain,allAssingees }) => {
  
  const { t } = useTranslation();
  const [opened, { open, close }] = useDisclosure(false);

  const form = useForm({
    mode: "uncontrolled",
    initialValues: {
      targetName: "",
      yearFrom: "",
      yearTo: "",
      reductionPct: "",
    },
    validate: {
      targetName: isNotEmpty("targetName is required"),
      yearFrom: isNotEmpty("End Date is required"),
      yearTo: isNotEmpty("End Date is required"),
      reductionPct: isNotEmpty("End Date is required"),
    },
  });

  const [targetName, setTargetName] = useState(data.name);
  const [yearFrom, setYearFrom] = useState(data.yearFrom);
  const [yearTo, setYearTo] = useState(data.yearTo);
  const [reductionPct, setReductionPct] = useState(data.reductionPct);
  const [scope, setScope] = useState(data.scope.split(","));
  const [note, setnote] = useState(data.notes || '');
  const [sbtCompatible, setsbtCompatible] = useState(data.sbtCompatible);
  const u = allAssingees
  .filter((user) => data?.userIds?.includes(parseInt(user?.value))).map(u=> u.value)
  
  const [assignees, setAssignees] = useState(u);
  const [selectedAssignees, setSelectedAssignees] = useState([]);

  const [submit, setSubmit] = useState(false);

  const addTargetFunc = async () => {
    let d = {
      name: targetName,
      yearFrom: yearFrom,
      yearTo: yearTo,
      sbtCompatible: sbtCompatible,
      scope: scope.join(","),
      reductionPct: reductionPct,
      notes: note,
      assignees: selectedAssignees
    };

    setSubmit(true);
    try {
      const res = await ApiS2.put(
        `/decarbonize/update_climate_target`,
        d,
        {
          headers: {
            'target-id': data.id
          }
        }
      );
      showNotification({
        message: "Form Data Added Successfully",
        color: "green",
      });
      fetetchAgain();
    } catch (er) {
      showNotification({
        message: "Error happend",
        color: "red",
      });
      console.log(er);
    }
    setSubmit(false);
  };

  const handleYearFrom = (d) => {
    let da = d.getFullYear();
    setYearFrom(da);
  };

  const handleYearTo = (d) => {
    let da = d.getFullYear();
    setYearTo(da);
  };

  const handleScope = (e, value) => {
    const isChecked = e.target.checked;
    const newScope = [...scope]; // Create a copy of the current scope state

    if (isChecked) {
      // Add the selected value to the scope array if it's not already present
      if (!newScope.includes(value)) {
        newScope.push(value);
      }
    } else {
      // Remove the deselected value from the scope array
      const index = newScope.indexOf(value);
      if (index !== -1) {
        newScope.splice(index, 1);
      }
    }
    setScope(newScope);
  };

  return (
    <>
      <button onClick={open}>
        <MdEdit className="w-6 h-6 p-1.5 bg-sky-500 text-blue-700 rounded-full" />
      </button>

      <Modal opened={opened} onClose={close} title={t("targetsModal.title")}>
        <form onSubmit={form.onSubmit(addTargetFunc)}>
          <div>
            <label
              htmlFor="targetName"
              className="block mb-3 text-sm font-semibold text-gray-700"
            >
              {t("targetsModal.targetNameLabel")}
            </label>
            <Input
              value={targetName}
              id="targetName"
              name="targetName"
              onChange={(e) => setTargetName((p) => e.target.value)}
              type="text"
            />
          </div>

          <div>
            <Checkbox
              checked={sbtCompatible}
              className="my-4 font-semibold"
              label={t("targetsModal.sbtCompatibleLabel")}
              color="teal"
              variant="outline"
              id="sbtCompatible"
              name="sbtCompatible"
              onChange={(e) => setsbtCompatible(e.target.checked)}
            />
            <p className="text-sm font-light">
              {t("targetsModal.sbtDescription")}{" "}
              <span className="text-sky-700">
                {t("targetsModal.helpCenter")}
              </span>
              .
            </p>

            <div className="flex items-center justify-start my-4">
              <IoIosArrowForward />
              <p>{t("targetsModal.additionalInfo")}</p>
            </div>

            <MultiSelect
                onChange={(e) => {
                  setSelectedAssignees(e.map(Number))
                  setAssignees(p=> e)
                }}
                value={assignees}
                label="Assignees"
                placeholder="Pick value"
                data={allAssingees}
              />
          </div>

          <div className="grid grid-cols-1 gap-4 mt-4 sm:grid-cols-2">
            <div>
              <YearPickerInput
                label={t("targetsModal.baselineYearLabel")}
                placeholder={t("targetsModal.baselineYearPlaceholder")}
                onChange={handleYearFrom}
              />
            </div>
            <div>
              <YearPickerInput
                label={t("targetsModal.targetYearLabel")}
                placeholder={t("targetsModal.targetYearPlaceholder")}
                onChange={handleYearTo}
              />
            </div>
          </div>

          <div className="my-4">
            <h3 className="text-sm font-semibold">
              {t("targetsModal.scopeSelection")}
            </h3>
            <div className="flex justify-start gap-5 py3">
              <Checkbox
                checked={scope.includes("S1")}
                onChange={(e) => handleScope(e, "S1")}
                className="my-4 font-semibold"
                label={t("targetsModal.scope1Label")}
                color="teal"
                variant="outline"
              />
              <Checkbox
                checked={scope.includes("S2")}
                onChange={(e) => handleScope(e, "S2")}
                className="my-4 font-semibold"
                label={t("targetsModal.scope2Label")}
                color="teal"
                variant="outline"
              />
              <Checkbox
                checked={scope.includes("S3")}
                onChange={(e) => handleScope(e, "S3")}
                className="my-4 font-semibold"
                label={t("targetsModal.scope3Label")}
                color="teal"
                variant="outline"
              />
            </div>
          </div>

          <div className="mt-4">
            <Input.Wrapper label={t("targetsModal.emissionReductionLabel")}>
              <Input
                placeholder={t("targetsModal.emissionReductionPlaceholder")}
                rightSection="%"
                value={reductionPct}
                onChange={(e) => setReductionPct(e.target.value)}
              />
              <Input.Description className="py-2">
                {t("targetsModal.emissionReductionDescription")}
              </Input.Description>
            </Input.Wrapper>
          </div>

          <Textarea
            value={note}
            className="py-3"
            label={t("targetsModal.notesLabel")}
            placeholder={t("targetsModal.notesPlaceholder")}
            onChange={(e) => setnote(e.target.value)}
          />

          <div className="flex justify-end">
            {submit ? (
              <Loading />
            ) : (
              <Button
                onClick={addTargetFunc}
                type="submit"
                variant="filled"
                size="sm"
                radius="md"
                className="mt-auto mb-2 ms-2 bg-primary hover:bg-secondary"
              >
                update
              </Button>
            )}
          </div>
        </form>
      </Modal>
    </>
  );
};

export default UpdateTarget;
