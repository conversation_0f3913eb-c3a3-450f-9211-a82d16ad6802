import { IoWarningOutline } from "react-icons/io5";
import { FaArrowUp, FaArrowDown } from "react-icons/fa6";
import { SemiCircleProgress } from "@mantine/core";

const Overall = ({ latestReport }) => {
  const fullScore = latestReport?.score?.full_score;
  const totalScore = latestReport?.score?.total_score;
  const maturityLevel = latestReport?.maturity_level;
  const scoreChange = latestReport?.score?.score_change;
  const ReadinessScore = latestReport?.score_analysis?.lse_grade;
  const ReadinessNumber = latestReport?.score_analysis?.numeric_score;
  const insights = latestReport?.insights;

  if (!latestReport) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="border-2 border-[#E8E7EA] bg-[#FFFFFF] rounded-lg p-6">
      <h2 className="mb-5 font-semibold text-[#272727]">
        Overall Readiness Score
      </h2>
      <div className="flex border rounded-lg mb-5">
        <div className="flex-1 flex items-center border-r p-6">
          <span className="text-5xl font-bold text-[#07838F]">
            {ReadinessNumber}
          </span>
        </div>
        <div className="flex-1 flex flex-col p-6">
          <span className="text-2xl font-bold text-[#272727]">
            {ReadinessScore}
          </span>
          <span className="text-sm text-[#07838F] mt-1">
            In {maturityLevel}
          </span>
        </div>
      </div>
      <div className="active-tab rounded-md p-6 mb-5">
        <span>
          <IoWarningOutline className="inline-block mr-2 text-[#07838F] text-xl" />
          Key Insights
        </span>
        <ul className="list-disc pl-5 mt-4">
          {insights.map((insight, index) => (
            <li key={index} className="text-sm text-[#525252] mt-2">
              {insight}
            </li>
          ))}
        </ul>
      </div>
      <div className="rounded-md p-6 mb-5 border-2 border-[#E8E7EA]">
        <h2 className="mb-5 font-semibold text-[#272727]">
          Overall Maturity Score
        </h2>
        <div className="flex justify-center items-center">
          <SemiCircleProgress
            fillDirection="left-to-right"
            orientation="up"
            filledSegmentColor="#70D162"
            size={280}
            thickness={25}
            value={(totalScore / fullScore) * 100}
            label={
              <div className="flex flex-col items-center">
                <span className=" text-[#272727]">
                  <span className="text-4xl font-bold">{totalScore}</span>/
                  {fullScore}
                </span>
                <span className="text-sm text-[#565656] mt-1">
                  {maturityLevel}
                </span>
              </div>
            }
            labelPosition="center"
          />
        </div>
        <div className="flex justify-center mt-5">
          <span className="text-[#272727] px-2">From previous:</span>
          <span
            className={`rounded-full ${
              scoreChange > 0
                ? "bg-[#01BD361A] text-[#01BD36]"
                : scoreChange < 0
                ? "bg-[#FEE2E2] text-[#991B1B]"
                : "bg-gray-200 text-gray-700"
            } px-2  flex items-center gap-2`}
          >
            {scoreChange > 0 && <FaArrowUp />}
            {scoreChange < 0 && <FaArrowDown />}
            {scoreChange > 0 ? "+" : ""}
            {scoreChange} points
          </span>
        </div>
      </div>
    </div>
  );
};
export default Overall;
