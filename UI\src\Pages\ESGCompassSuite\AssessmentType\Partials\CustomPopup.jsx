// CustomPopup.js
import ApiS1Config from "@/Api/apiS1Config";
import { Modal } from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import popup from "/assets/Images/popup.png";

function CustomPopup({ opened, close, Active, path,worked, assessmentType ,launchQuiz,loading}) {
  const navigate = useNavigate();

 
  return (
    <Modal
      opened={opened}
      onClose={close}
      centered
      size="50rem"
      className=""
      transitionProps={{ transition: "fade", duration: 800 }}
      withCloseButton={false}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 p-5">
        <div className="text row-start-2 md:row-start-1">
          <h2 className="text-3xl font-bold mb-6">
            {assessmentType}
          </h2>
          <p className="mb-6 text-[#57595A]">
            Gain valuable insights into your company&apos;s ESG practices
            effortlessly with our intuitive rapid assessment tool.
          </p>
          <div className="mb-12 text-[#57595A]">
            <p>No. of Questions: 29</p>
            <p>Completion Time: ~30 min</p>
          </div>
          <div>
            <button
              className="bg-[#05808B] hover:bg-[#1a5257] duration-300 w-full md:w-[200px] text-[#FFFFFF] font-semibold px-6 py-1 rounded-md"
              onClick={worked ? () => navigate(path) : launchQuiz}
              disabled={loading}
            >
              {loading ? "Starting..." : "Let's Begin"}
            </button>
          </div>
        </div>
        <div className="img row-start-1">
          <img className="mx-auto" src={popup} alt="Popup illustration" />
        </div>
      </div>
    </Modal>
  );
}

export default CustomPopup;
