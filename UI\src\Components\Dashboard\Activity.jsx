import React from "react";
import { IoIosArrowDown } from "react-icons/io";
import VerticalLinearStepper from "./Steps";
import { FaRegComment } from "react-icons/fa6";
import DateActivity from "./DateActivity";
import { FaRegQuestionCircle } from "react-icons/fa";
import { CiCircleCheck } from "react-icons/ci";
import PropType from "prop-types";

function Activity({ comment, added, report }) {
  return (
    <div className="bg-white py-[1rem] px-[1.5rem] rounded-2xl shadow-2xl w-[90%]  xl:w-[37%] mx-auto">
      <div className="flex items-center justify-between mb-5 border-b-[1px] pb-6">
        <h1 className="font-bold text-[18px] ">Activities</h1>
        <div className="flex gap-[.5rem] ">
          <button className="font-[300] text-[#15192C] flex items-center gap-[.5rem] border-[#2A939C] border-[1px] py-1 px-3 rounded-lg shadow-2xl shadow-black text-[14px]">
            All Users <IoIosArrowDown />
          </button>
          <button className="font-[300] text-[#15192C] flex items-center gap-[.5rem] border-[#2A939C] border-[1px] py-1 px-3 rounded-lg shadow-2xl shadow-black text-[14px]">
            Today <IoIosArrowDown />
          </button>
        </div>
      </div>
      <div>
        <h3 className="text-[14px] text-[#07838F] font-light">Today</h3>
        <div className="flex gap-[.5rem]">
          <VerticalLinearStepper />
          <div className="mt-5">
            <div className="flex gap-[.5rem] items-center justify-between  border-b-[1px] pb-5 mt-3">
              <div className="w-[30px] h-[30px] rounded-full bg-[#70D162] flex justify-center items-center text-white">
                <FaRegComment />
              </div>
              <p className="text-[13px]">{comment}</p>
              <DateActivity date="12:33" />
            </div>
            <div className="flex gap-[.5rem] items-center justify-between border-b-[1px] pb-5  mt-3">
              <div className="w-[30px] h-[30px] rounded-full bg-[#70D162] flex justify-center items-center text-white">
                <FaRegQuestionCircle />
              </div>
              <p className="text-[13px]">{added}</p>
              <DateActivity date="10:45" />
            </div>
            <div className="flex gap-[.5rem] items-center justify-between  border-b-[1px] pb-5 mt-3">
              <div className="w-[30px] h-[30px] rounded-full bg-[#70D162] flex justify-center items-center text-white text-[18px]">
                <CiCircleCheck />
              </div>
              <p className="text-[13px]">{report}</p>
              <DateActivity date="9:20" />
            </div>
          </div>
        </div>
        <div className="relative">
          <button className="absolute right-0 top-5 text-[14px] text-[#07838F] font-light">
            View All
          </button>
        </div>
      </div>
    </div>
  );
}

Activity.propTypes = {
  comment: PropType.string,
  added: PropType.string,
  report: PropType.string,
};

export default Activity;
