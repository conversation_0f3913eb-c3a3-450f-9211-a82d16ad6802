import ApiS2 from "@/Api/apiS2Config";
import Loading from "@/Components/Loading";
import {
 Button,
 Checkbox,
 Pagination,
 rem,
 ScrollArea,
 Table,
 TextInput,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { showNotification } from "@mantine/notifications";
import cx from "clsx";
import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { CiSearch } from "react-icons/ci";
import { GoDatabase } from "react-icons/go";
import { RiDeleteBin6Line } from "react-icons/ri";
// import ReviewMultiselectFilter from "../ReviewMultiselectFilter";
import ReviewBatchInputPopUp from "./Partials/ReviewBatchInputPopUp";

const BatchInputsTable = ({
 UserType,
 Status,
 InputSource,
 data,
 loading,
 assetTypeAll,
 getTableData,
 approvedRow,
 loadingApprovedBatch,
 toggleLoading,
 error,
}) => {
 // //console.log(data);
//  useEffect(() => {
//   !data && getTableData("batch");
//  }, []);
 const { t } = useTranslation();
 const [selection, setSelection] = useState([]);
 const [opened, { open, close }] = useDisclosure(false);
 const [currentBatchId, setCurrentBatchId] = useState();
 const [DelLoading, setDelLoading] = useState({});
 const [value, setValue] = useState([]);
 const [search, setSearch] = useState("");
 const [sortedData, setSortedData] = useState();
 const [currentPage, setCurrentPage] = useState(1);
 const rowsPerPage = 10;
 const totalPages = Math.ceil(data?.length / rowsPerPage);

 const currentData = useMemo(() => {
  return (
   (data &&
    data?.slice((currentPage - 1) * rowsPerPage, currentPage * rowsPerPage)) ||
   []
  );
 }, [data, currentPage, rowsPerPage]);
 // //console.log(sortedData);
 useEffect(() => {
  let filteredData;
  if (value[0] === "Pending") {
   filteredData = currentData.filter((item) => item.reviewState === null);
  } else if (value[0] === "Accepted") {
   filteredData = currentData.filter((item) => item.reviewState === true);
  } else if (value[0] === "Rejected") {
   filteredData = currentData.filter((item) => item.reviewState === false);
  } else {
   filteredData = currentData;
  }

  if (search) {
   filteredData = filteredData.filter((data) =>
    data.items.some(
     (item) =>
      item.companyAsset.assetName
       .toLowerCase()
       .includes(search.toLowerCase()) ||
      item.customFactor.activity.toLowerCase().includes(search.toLowerCase()) ||
      item.customFactor.eFactors.toLowerCase().includes(search.toLowerCase()) ||
      item.customFactor.uom.toLowerCase().includes(search.toLowerCase()) ||
      item.reportingYear.toString().includes(search)
    )
   );
  }

  setSortedData(filteredData);
 }, [value, search, currentData]);

 const handleSearchChange = (event) => {
  setSearch(event);
 };
 const toggleRow = (id) =>
  setSelection((current) =>
   current.includes(id)
    ? current.filter((item) => item !== id)
    : [...current, id]
  );
 const toggleAll = () =>
  setSelection((current) =>
   current.length === data.length ? [] : data.map((item) => item.id)
  );
 const DelToggleLoading = (id, value) => {
  setDelLoading((prevState) => ({
   ...prevState,
   [id]: value,
  }));
 };
 const del = async (e, id) => {
  const dataset = { dataset_ids: [e] };
  //console.log(dataset);
  try {
   DelToggleLoading(id, true);
   const { data } = await ApiS2.post("/batch_inputs/delete_dataset", dataset);
   showNotification({
    message: "Deleted Successfully",
    color: "teal",
   });
   getTableData("batch");
   DelToggleLoading(id, false);
   //console.log(data);
  } catch (error) {
   showNotification({
    message: "Failed to Delete",
    color: "red",
   });
   DelToggleLoading(id, false);
   //console.log(error);
  }
 };
 const rows = sortedData?.map((item, indx) => {
  const selected = selection.includes(item.id);
  let assetType, asset, dataSet, evidenceKey, isLoadingManual, evidence;
  evidence = item?.uploadFileLinks?.template;
  evidenceKey = evidence ? Object.keys(evidence) : [];
  let isEdit = false;
  item.items.forEach((items) => {
   const assets = assetTypeAll.find(
    (asset) => asset.id === items?.customFactor?.emissionSourceId
   );
   assetType = assets?.asset;
   asset = items?.companyAsset?.assetName;
   dataSet = items?.dataset;

   isLoadingManual = loadingApprovedBatch[item.id];
  });
  return (
   <Table.Tr
    key={item.id}
    className={`${cx({
     ["bg-[#07838F1A]"]: selected,
    })} text-sm font-bold text-[#626364] text-center`}
   >
    <Table.Td>
     <Checkbox
      checked={selection.includes(item.id)}
      onChange={() => toggleRow(item.id)}
      color="#07838F"
     />
    </Table.Td>
    <Table.Td>
     <span
      className="flex justify-center items-center text-xl"
      onClick={() => {
       if (!DelLoading[item.id]) {
        del(dataSet, item.id);
       }
      }}
     >
      {DelLoading[item?.id] ? (
       <Loading />
      ) : (
       <RiDeleteBin6Line className="cursor-pointer" />
      )}
     </span>
    </Table.Td>
    <Table.Td>
     <div className="w-20 block mx-auto">
      <p className="">
       {item.uploadedDT?.split("T")[1]?.substring(0, 5) || "Not Found"}
      </p>
     </div>
    </Table.Td>
    <Table.Td>
     <div className="w-32 block mx-auto">
      <p className="">{assetType || "Not Found"}</p>
     </div>
    </Table.Td>
    <Table.Td>
     <div className="w-24 block mx-auto">
      <p className="">{asset || "Not Found"}</p>
     </div>
    </Table.Td>
    <Table.Td className="flex justify-center">
     <div className=" text-center  ">
      <Button
       className=" bg-gray-300 hover:bg-gray-300 text-gray-700 hover:text-gray-700 
          rounded-full"
       onClick={() => {
        open();
        setCurrentBatchId(item.id);
       }}
      >
       <GoDatabase className="me-2" /> View Data
      </Button>
     </div>
    </Table.Td>
    <Table.Td>
     <p
      style={{
       backgroundColor: InputSource[item.inputType]?.bg,
       border: `2px solid ${InputSource[item.inputType]?.border}`,
       color: InputSource[item.inputType]?.text,
      }}
      className={`p-2 text-nowrap rounded-3xl capitalize`}
     >
      {item.inputType}
     </p>
    </Table.Td>

    <Table.Td>
     <div className=" block mx-auto">
      <p
       className={`py-2 px-10 rounded-2xl`}
       style={{
        backgroundColor:
         item.reviewState === null
          ? Status.Pending.bg
          : item.reviewState === true
          ? Status.Accepted.bg
          : item.reviewState === false
          ? Status.Rejected.bg
          : "",
        color:
         item.reviewState === null
          ? Status.Pending.text
          : item.reviewState === true
          ? Status.Accepted.text
          : item.reviewState === false
          ? Status.Rejected.text
          : "",
       }}
      >
       {item.reviewState === null
        ? "Pending"
        : item.reviewState === true
        ? "Accepted"
        : item.reviewState === false
        ? "Rejected"
        : "Not Found"}
      </p>
     </div>
    </Table.Td>
    <Table.Td>
     <p
      style={{
       backgroundColor: UserType[item.userType]?.bg,
       border: `2px solid ${UserType[item.userType]?.border}`,
       color: UserType[item.userType]?.text,
      }}
      className="p-2 text-nowrap rounded-3xl "
     >
      <span className="capitalize">{item.userType}</span>
     </p>
    </Table.Td>
    <Table.Td>
     <div className="">
      <p className="w-full">
       {evidenceKey?.map((item, idx) => (
        <Button
         key={idx}
         variant="subtle"
         href={evidence[item]}
         target="_blank"
         component="a"
        >
         {item}
        </Button>
       )) || "Not Found"}{" "}
      </p>
     </div>
    </Table.Td>
    <Table.Td>
     <Button
      className={`bg-primary text-white  hover:text-white rounded-lg 
              ${
               item?.reviewState === true
                ? "bg-gray-500 cursor-not-allowed bg-opacity-20"
                : "bg-primary hover:bg-primary"
              }`}
      disabled={item?.reviewState === true}
      onClick={() => {
       if (!isLoadingManual) {
        toggleLoading(item.id, true, "batch");
        approvedRow(dataSet, "batch")
         .then(() => {
          toggleLoading(item.id, false, "batch");
         })
         .catch(() => {
          toggleLoading(item.id, false, "batch");
         });
       }
      }}
     >
      {isLoadingManual ? <Loading /> : "Approved"}
     </Button>
    </Table.Td>
   </Table.Tr>
  );
 });
 return (
  <>
   {loading ? (
    <Loading />
   ) : (
    <div className="bg-[#F7F4F4] mt-7">
     <div className="p-2 my-1 shadow-lg grid items-center  xl:justify-between px-4 bg-white xl:grid-cols-3 rounded-lg w-full">
      <div className="xl:col-span-1 w-full flex justify-center xl:justify-start"></div>
      <div className="xl:col-span-2 grid items-start  justify-center grid-cols-1 lg:grid-cols-3 xl:grid-cols-4 sm:items-center w-full lg:gap-5">
       <div className="md:col-span-1 hidden xl:flex justify-center items-center m-3  p-2 rounded-lg shadow-sm ms-auto "></div>

       {/* <div className=" md:col-span-1 m-3  bg-[#EBEBEB] rounded-lg shadow-sm md:ms-auto mx-auto w-full">
        <ReviewMultiselectFilter setValue={setValue} value={value} />
       </div> */}
       <div></div>
       <TextInput
        className="w-full col-span-2"
        placeholder="Search by Topic Area or Assessment Question"
        rightSection={<CiSearch className="w-5 h-5" />}
        value={search}
        onChange={(e) => handleSearchChange(e.target.value)}
        // disabled
       />
      </div>
     </div>
     <>
      {(search && sortedData?.length === 0) ||
      (value.length && sortedData?.length === 0) ? (
       <h1 className="mt-5 text-center capitalize">Your Search is not Found</h1>
      ) : (
       <>
        <ScrollArea>
         <Table
          // miw={800}
          verticalSpacing="sm"
          className="p-2 my-1 bg-white shadow-lg rounded-xl"
         >
          <Table.Thead className="border-b-2 border pb-6 text-secondary-500 font-bold text-base text-center">
           <Table.Tr>
            <Table.Th style={{ width: rem(40) }}>
             <Checkbox
              onChange={toggleAll}
              checked={selection?.length === data?.length}
              indeterminate={
               selection.length > 0 && selection.length !== data.length
              }
              color="#07838F"
             />
            </Table.Th>
            <Table.Th className="text-center">{t("Delete")}</Table.Th>
            <Table.Th className="text-center">{t("Time Stamp")}</Table.Th>
            <Table.Th className="text-center">{t("Assets Type")}</Table.Th>
            <Table.Th className="text-center">{t("Assets")}</Table.Th>
            <Table.Th className="text-center">{t("Uploaded Data")}</Table.Th>
            <Table.Th className={`text-center`}>{t("Input Source")}</Table.Th>
            <Table.Th className="text-center">{t("Status")}</Table.Th>
            <Table.Th className="text-center">{t("User Type")}</Table.Th>
            <Table.Th className="text-center">{t("Evidence/Notes")}</Table.Th>
            <Table.Th className="text-center">{t("Action")}</Table.Th>
           </Table.Tr>
          </Table.Thead>
          <Table.Tbody>{rows}</Table.Tbody>
         </Table>
        </ScrollArea>

        {/* Pagination */}
        <div className="md:flex justify-between mt-5">
         <p className="text-sm text-secondary-300" hidden={!sortedData?.length}>
          {t("showingData", {
           start: (currentPage - 1) * rowsPerPage + 1,
           end: Math.min(currentPage * rowsPerPage, data?.length),
           total: data?.length,
          })}
         </p>
         <Pagination
          page={currentPage}
          onChange={(e) => {
           setCurrentPage(e);
           // setEdit(false);
          }}
          total={totalPages}
          color="#00c0a9"
          className={`flex justify-center mt-5 md:mt-0 ${
           !sortedData?.length && "hidden"
          }`}
         />
        </div>
       </>
      )}
     </>
     <ReviewBatchInputPopUp
      opened={opened}
      onClose={close}
      currentBatchId={currentBatchId}
      data={data}
      assetTypeAll={assetTypeAll}
      getTableData={getTableData}
     />
    </div>
   )}
  </>
 );
};

export default BatchInputsTable;
