import ApiProfile from "@/Api/apiProfileConfig";
import ApiS1Config from "@/Api/apiS1Config";
import { notifications } from "@mantine/notifications";
import Cookies from "js-cookie";
import { createContext, useContext, useEffect, useState } from "react";
import { useNavigate } from "react-router";
import Swal from "sweetalert2";
import { useUserStore } from "@/Store/useUser";
const AuthContext = createContext();

const AuthProvider = ({ children }) => {
 const navigation = useNavigate();
 const [user, setUser] = useState();
 const [userAdditionalRole, setUserAdditionalRole] = useState([]);
 const [superAdmin, setSuperAdmin] = useState(false);
 const [isAuthenticated, setIsAuthenticated] = useState(false);
 const [CompanyAccess, setCompanyAccess] = useState(false);
 const [S1Access, setS1] = useState();
 const [S2Access, setS2] = useState();
 const [S3Access, setS3] = useState();
 const [greenHubAccess, setGreenHub] = useState();
 const [EnterpriseBackboneAccess, setEnterpriseBackbone] = useState();
 const [S1CurrentAssessment, setS1CurrentAssessment] = useState(false);
 const [launchQuizLoading, setLoading] = useState(false);
 const [token, setToken] = useState(Cookies.get("level_user_token") || "");
 const [otpId, setOtpId] = useState("");
 const {updateAvatar,setLoadingAvatar} = useUserStore();

 useEffect(() => {
  if (CompanyAccess) {
   setS1(CompanyAccess["ESG Compass Suite"]);
   setS2(CompanyAccess["LevelUp Net Zero"]);
   setS3(CompanyAccess["GreenShield"]);
   setGreenHub(CompanyAccess["Green Hub"]);
   setEnterpriseBackbone(CompanyAccess["Enterprise Backbone"]);
  }
 }, [CompanyAccess]);
 const navigate = useNavigate();


 const validate_admin = async () => {
  try {
   const response = await ApiProfile.get("/admin/validate_super_admin");
   response.status === 200 && setSuperAdmin(true);
  } catch (error) {
   // console.log(error);
   setSuperAdmin(false);
  }
 };
 const getUserInfo = async () => {
  try {
   ApiProfile.get("/account_information").then((res) => {
    setUser(res.data);
    setUserAdditionalRole(res.data.additional_roles);
    GetAvatar(res.data,updateAvatar,setLoadingAvatar);
   });
  } catch (error) {
   console.error(error);
  }
 };
 const getCompanyAccess = async () => {
  try {
   const { data: CompanyAccess } = await ApiProfile.get(
    "/check_company_access"
   );
   setCompanyAccess(CompanyAccess);
  } catch (error) {
   console.error(error);
  }
 };
 const checkProgramStatus = async () => {
  try {
   const { data: checkCurrentAssessment } = await ApiS1Config.get(
    "/check_current_assessment"
   );
   setS1CurrentAssessment(checkCurrentAssessment);
  } catch (error) {
   //console.log(error);
  }
 };
 const launchQuiz = (selectedChild) => {
  console.log(selectedChild);
  Swal.fire({
   title: "Please wait...",
   text: "Creating Assessment...",
   icon: "info",
   allowOutsideClick: false,
   allowEscapeKey: false,
   didOpen: () => {
    Swal.showLoading();
   },
  });
  setLoading(true);
  ApiS1Config.post(
   "/create_assessment",
   {},
   {
    headers: { assessmentType: selectedChild.assessmentType },
   }
  )

   .then((res) => {
    Swal.fire({
     title: "Success!",
     text: "Assessment created successfully!",
     icon: "success",
     timer: 2000, // إغلاق تلقائي بعد ثانيتين
     showConfirmButton: false,
    });
    setLoading(false);
    //console.log(res);
    notifications.show({
     message: "Assessment created successfully!",
     color: "green",
    });
    navigation(selectedChild.path || selectedChild.link);
   })
   .catch((error) => {
    setLoading(false);
    Swal.close();
    Swal.fire({
     title: "Error!",
     text: error.response?.data?.error || "An error occurred",
     icon: "error",
     confirmButtonText: "OK",
    });
    notifications.show({
     message: "Failed to create assessment!",
     color: "red",
    });
   });
 };
 useEffect(() => {
  getUserInfo();
  getCompanyAccess();
  validate_admin();
  checkProgramStatus();
 }, [token]);

 const login = async (loginState, rememberMe = false) => {
  try {
    const response = await ApiProfile.post("/user-sign-in", loginState);

    if (response.status === 200) {
      if (
        response.data.message === "Two-factor authentication required" &&
        response.data.otpId
      ) {
        setOtpId(response.data.otpId);
        navigation("/otp-verification");
        return { requiresOTP: true };
      } else if (response.data.access_token) {
        const newToken = response.data.access_token;

        // Remove existing token
        Cookies.remove("level_user_token");

        // Set token with conditional expiry
        if (rememberMe) {
          Cookies.set("level_user_token", newToken, { expires: 1 / 3 });
          Cookies.set("refreshToken", response.data.refresh_token, { expires: 1 / 3 }); 
        } else {
          // Session-only cookie (expires when browser closes)
          Cookies.set("level_user_token", newToken);
        }

        setToken(newToken);
        setIsAuthenticated(true);
        notifications.clean();
        notifications.show({
          message: "Login successful!",
        });

        navigation("/get-started");
        return { success: true };
      }
    }
  } catch (error) {
    notifications.show({
      color: "red",
      message: "Login failed. Please check your credentials and try again.",
    });
    return { error: error.message };
  }
};


 const verifyOTP = async ({ otp }) => {
  try {
   // Ensure OTP is sent as a number, not a string
   const payload = {
    otp: Number(otp), // Convert to number
    otpId: otpId
   };
   
   const response = await ApiProfile.post("/validate-otp", payload);

   if (response.status === 200 && response.data.access_token) {
    const newToken = response.data.access_token;
    Cookies.remove("level_user_token");
    Cookies.set("level_user_token", newToken, { expires: 1 / 3 });
    setToken(newToken);
    setIsAuthenticated(true);
    setOtpId("");
    notifications.clean();
    notifications.show({
     message: "Verification successful!",
     color: "green"
    });
    navigation("/get-started");
    return { success: true };
   } else {
    // If there's no access token but the API responded
    notifications.show({
     color: "red",
     message: "OTP verification failed. Invalid code.",
    });
    navigation("/login");
    return { error: "Invalid OTP" };
   }
  } catch (error) {
   notifications.show({
    color: "red",
    message: error.response?.data?.message || "OTP verification failed. Please try again.",
   });
   navigation("/login");
   return { error: error.message };
  }
 };

 const resendOTP = async () => {
  try {
   const response = await ApiProfile.post("/resend-otp", { otpId });
   
   if (response.status === 200) {
    notifications.show({
     message: "OTP code resent successfully!",
     color: "green",
    });
    return { success: true };
   } else {
    notifications.show({
     color: "red",
     message: "Failed to resend OTP. Please try again.",
    });
    return { error: "Request failed" };
   }
  } catch (error) {
   // If there's a server error or the OTP ID is no longer valid
   if (error.response?.status === 400 || error.response?.status === 404) {
    notifications.show({
     color: "red",
     message: "Authentication session expired. Please login again.",
    });
    navigation("/login");
   } else {
    notifications.show({
     color: "red",
     message: error.response?.data?.message || "Failed to resend OTP. Please try again.",
    });
   }
   return { error: error.message };
  }
 };

 const logout = () => {
  setIsAuthenticated(false);
  setUser(null);
  setToken("");
  setOtpId("");
  Cookies.remove("level_user_token");
  localStorage.clear();
  navigate("/login");
 };

 return (
  <AuthContext.Provider
   value={{
    user,
    token,
    isAuthenticated,
    login,
    logout,
    superAdmin,
    userAdditionalRole,
    CompanyAccess,
    S1CurrentAssessment,
    S1Access,
    S2Access,
    S3Access,
    greenHubAccess,
    EnterpriseBackboneAccess,
    launchQuiz,
    launchQuizLoading,
    getCompanyAccess,
    checkProgramStatus,
    verifyOTP,
    resendOTP,
    otpId,
    // programsData,
   }}
  >
   {children}
  </AuthContext.Provider>
 );
};

export const useAuth = () => useContext(AuthContext);

export default AuthProvider;


const GetAvatar = async (user,updateAvatar,setLoadingAvatar) => {
  try {
    setLoadingAvatar(true);
    if(user.avatar){
      let localavatar = localStorage.getItem("avatar");
      updateAvatar(localavatar);
      if(!localavatar){
        const {data:{avatar_url}} = await ApiProfile.get("/avatar", {headers:{Authorization: `Bearer ${Cookies.get("level_user_token")}`}});
        const response = await fetch(avatar_url);
        const blob = await response.blob();
        
        const reader = new FileReader();
        reader.onloadend = () => {
          localStorage.setItem("avatar", reader.result);
          updateAvatar(reader.result);
          setLoadingAvatar(false);
        };
        reader.readAsDataURL(blob);
        reader.onerror = () => {
          setLoadingAvatar(false);
        };
      }else{
        setLoadingAvatar(false);
      }
    }else{
      setLoadingAvatar(false);
    }
  } catch (error) {
    console.log(error);
    setLoadingAvatar(false);
  }
}