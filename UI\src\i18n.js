import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import Cookies from "js-cookie";
import translationEN from "./locale/en.json";

// Helper function to get the appropriate translation from localStorage
const getLocalStorageTranslation = () => {
  const storedTranslation = localStorage.getItem("upLevelTranslation");
  return storedTranslation ? JSON.parse(storedTranslation) : null;
};

// Load translations for each language, defaulting to English
const resources = {
  en: {
    translation: translationEN,
  },
  fr: {
    translation:
      Cookies.get("upLevelLang") === "fr"
        ? getLocalStorageTranslation()
        : translationEN,
  },
  de: {
    translation:
      Cookies.get("upLevelLang") === "de"
        ? getLocalStorageTranslation()
        : translationEN,
  },
  ru: {
    translation:
      Cookies.get("upLevelLang") === "ru"
        ? getLocalStorageTranslation()
        : translationEN,
  },
  "zh-Hans": {
    translation:
      Cookies.get("upLevelLang") === "zh-Hans"
        ? getLocalStorageTranslation()
        : translationEN,
  },
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next) // passes i18n down to react-i18next
  .init({
    resources,
    lng: localStorage.getItem("upLevelTranslation")
      ? Cookies.get("upLevelLang")
        ? Cookies.get("upLevelLang")
        : "en"
      : "en",
    // fallbackLng: "en",
    interpolation: {
      escapeValue: false, // react already safes from xss
    },
    react: {
      useSuspense: false,
    },
  });

export default i18n;
