import { useState, useEffect } from "react";
import cx from "clsx";
import {
  Checkbox,
  Input,
  Select,
  Table,
  Button,
  Modal,
  Text,
  MultiSelect,
} from "@mantine/core";
import ApiS2 from "@/Api/apiS2Config";
import { showNotification } from "@mantine/notifications";
import countries from "world-countries";
import { IoIosArrowDown } from "react-icons/io";
import Loading from "@/Components/Loading";

const prioritized = [
  "United Kingdom",
  "United Arab Emirates",
  "United States",
  "Saudi Arabia",
  "Qatar",
];

// Sort countries: prioritized first, then the rest
const sortedCountries = [
  ...countries.filter((c) => prioritized.includes(c.name.common)),
  ...countries
    .filter((c) => !prioritized.includes(c.name.common))
    .sort((a, b) => a.name.common.localeCompare(b.name.common)),
];

const locationOptions = [
  "Headquarters",
  "Branch Office",
  "Manufacturing Plant",
  "Warehouse",
  "Distribution Center",
  "Research & Development Center",
  "Sales Office",
  "Service Center",
  "Data Center",
  "Training Center",
  "Regional Office",
  "Remote Office",
  "Factory",
  "Laboratory",
  "Retail Store",
  "Call Center",
  "Logistics Hub",
  "Innovation Center",
  "Operations Center",
  "Customer Service Center"
];

const DepartmentandProjectsRow = ({
  item,
  selection,
  toggleRow,
  fetchAgain,
  itemId,
  setItemId,
}) => {
  const selected = selection.includes(item.id);
  // Check if item is disabled - adjust this based on your actual API response structure
  const isDisabled =
    item.status === "inactive" ||
    item.active === false ||
    item.isActive === false;

  // Debug logging to help identify the issue
  console.log("Item:", item.name, "Status fields:", {
    status: item.status,
    active: item.active,
    isActive: item.isActive,
    isDisabled: isDisabled,
  });

  const msg = (txt, color = "green") => {
    showNotification({ message: txt, color: color, timing: 7000 });
  };

  const [isSubmiting, setIsSubmiting] = useState(false);
  const [isStatusChanging, setIsStatusChanging] = useState(false);
  const [confirmModal, setConfirmModal] = useState({
    opened: false,
    action: "", // 'enable' or 'disable'
    itemName: "",
  });

  const [formData, setFormData] = useState({
    name: item?.name || "",
    region: item?.region || "",
    country: item?.country || "",
    sector: item?.sector || "",
    numberOfEmployees: item?.numberOfEmployees || "",
    locations: item?.locations || [],
    numberOfAssets: item?.numberOfAssets || "",
  });

  // Update form data when item changes (after table reload)
  useEffect(() => {
    setFormData({
      name: item?.name || "",
      region: item?.region || "",
      country: item?.country || "",
      sector: item?.sector || "",
      numberOfEmployees: item?.numberOfEmployees || "",
      locations: Array.isArray(item?.locations) ? item.locations : (item?.locations ? [item.locations] : []),
      numberOfAssets: item?.numberOfAssets || "",
    });
  }, [
    item.id,
    item.name,
    item.region,
    item.country,
    item.sector,
    item.numberOfEmployees,
    item.locations,
    item.numberOfAssets,
  ]);

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const validateFormData = () => {
    // Check required fields
    const requiredFields = ["name", "region", "country", "sector"];

    for (const field of requiredFields) {
      if (!formData[field] || formData[field].toString().trim().length < 3) {
        msg(`${field} must be at least 3 characters long`, "red");
        return false;
      }
    }

    // Validate numeric fields
    if (
      formData.numberOfEmployees &&
      isNaN(Number(formData.numberOfEmployees))
    ) {
      msg("Number of Employees must be a valid number", "red");
      return false;
    }

    if (formData.numberOfAssets && isNaN(Number(formData.numberOfAssets))) {
      msg("Number of Assets must be a valid number", "red");
      return false;
    }

    return true;
  };

  const submitUpdate = async () => {
    if (!validateFormData()) {
      return;
    }

    const updatedData = {
      name: formData.name.trim(),
      region: formData.region.trim(),
      country: formData.country,
      sector: formData.sector.trim(),
      numberOfEmployees: formData.numberOfEmployees
        ? Number(formData.numberOfEmployees)
        : null,
      locations: formData.locations, // This will now be an array
      numberOfAssets: formData.numberOfAssets
        ? Number(formData.numberOfAssets)
        : null,
    };

    setIsSubmiting(true);
    try {
      let res = await ApiS2.put(
        `https://portal-auth-main-staging.azurewebsites.net/company-admin/departments-and-projects/${item.id}`,
        updatedData
      );

      // Show success message
      msg(res.data.message || "Updated successfully");

      // Exit edit mode first
      setItemId(0);

      // Immediately fetch fresh data
      await fetchAgain();
    } catch (error) {
      console.log("🚀 ~ submitUpdate ~ error:", error);
      msg(error.response?.data?.message || "Update failed", "red");
    } finally {
      setIsSubmiting(false);
    }
  };

  const handleCancel = () => {
    // Reset form data to original values
    setFormData({
      name: item?.name || "",
      region: item?.region || "",
      country: item?.country || "",
      sector: item?.sector || "",
      numberOfEmployees: item?.numberOfEmployees || "",
      locations: Array.isArray(item?.locations) ? item.locations : (item?.locations ? [item.locations] : []),
      numberOfAssets: item?.numberOfAssets || "",
    });

    // Exit edit mode
    setItemId(0);
  };

  // Handle Enable/Disable confirmation modal
  const handleStatusChange = (action) => {
    setConfirmModal({
      opened: true,
      action: action,
      itemName: item.name,
    });
  };

  // Confirm status change
  const confirmStatusChange = async () => {
    const { action } = confirmModal;
    const endpoint = action === "enable" ? "active" : "inactive";

    setIsStatusChanging(true);
    try {
      const response = await ApiS2.put(
        `https://portal-auth-main-staging.azurewebsites.net/company-admin/departments-and-projects/${item.id}/${endpoint}`
      );

      msg(response.data?.message || `Successfully ${action}d ${item.name}`);

      // Close modal
      setConfirmModal({ opened: false, action: "", itemName: "" });

      // Clear any selections if this item was selected
      if (selected) {
        toggleRow(item.id);
      }

      // Refresh data to reflect changes
      await fetchAgain();
    } catch (error) {
      console.error(`${action} error:`, error);
      msg(error.response?.data?.message || `Failed to ${action} item`, "red");
    } finally {
      setIsStatusChanging(false);
    }
  };

  // Close modal without action
  const closeModal = () => {
    setConfirmModal({ opened: false, action: "", itemName: "" });
  };

  const isEditing = item.id === itemId;

  return (
    <>
      <Table.Tr
        key={item.id}
        className={`${cx({
          ["bg-[#07838F1A]"]: selected && !isDisabled,
          ["bg-gray-200 text-gray-500"]: isDisabled, // Grey out disabled rows but keep them visible
        })} text-sm font-bold text-[#626364] text-center ${
          isDisabled ? "select-none" : "" // Prevent text selection but keep row visible
        }`}
      >
        <Table.Td>
          <Checkbox
            checked={selected}
            onChange={() => !isDisabled && toggleRow(item.id)} // Disable checkbox for inactive items
            color="#07838F"
            disabled={isDisabled}
          />
        </Table.Td>

        {/* Name */}
        <Table.Td>
          <Input.Wrapper
            error={
              formData.name.length < 3 &&
              isEditing &&
              "must be at least 3 characters long"
            }
          >
            <Input
              className="w-[150px]"
              unstyled={!isEditing}
              disabled={!isEditing || isDisabled}
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              placeholder="Name"
            />
          </Input.Wrapper>
        </Table.Td>

        {/* Region */}
        <Table.Td>
          <Input.Wrapper
            error={
              formData.region.length < 3 &&
              isEditing &&
              "must be at least 3 characters long"
            }
          >
            <Input
              className="w-[150px]"
              unstyled={!isEditing}
              disabled={!isEditing || isDisabled}
              value={formData.region}
              onChange={(e) => handleInputChange("region", e.target.value)}
              placeholder="Region"
            />
          </Input.Wrapper>
        </Table.Td>

        {/* Country */}
        <Table.Td className="w-[150px]">
          {isEditing && !isDisabled ? (
            <Select
              error={!formData.country && isEditing && "Country is required"}
              value={formData.country}
              data={sortedCountries.map((country) => country.name.common)}
              placeholder="Select Country..."
              searchable
              rightSection={<IoIosArrowDown />}
              onChange={(value) => handleInputChange("country", value)}
              clearable
              comboboxProps={{ withinPortal: true }}
            />
          ) : (
            <div className="w-[150px] text-start">{item.country || "-"}</div>
          )}
        </Table.Td>

        {/* Sector */}
        <Table.Td>
          <Input.Wrapper
            error={
              formData.sector.length < 3 &&
              isEditing &&
              "must be at least 3 characters long"
            }
          >
            <Input
              className="w-[150px]"
              unstyled={!isEditing}
              disabled={!isEditing || isDisabled}
              value={formData.sector}
              onChange={(e) => handleInputChange("sector", e.target.value)}
              placeholder="Sector"
            />
          </Input.Wrapper>
        </Table.Td>

        {/* Number of Employees */}
        <Table.Td>
          <Input
            className="w-[120px]"
            unstyled={!isEditing}
            disabled={!isEditing || isDisabled}
            value={formData.numberOfEmployees}
            onChange={(e) =>
              handleInputChange("numberOfEmployees", e.target.value)
            }
            placeholder="0"
            type="number"
          />
        </Table.Td>

        {/* Locations */}
        <Table.Td>
          {isEditing && !isDisabled ? (
            <MultiSelect
              className="w-[200px]"
              data={locationOptions}
              value={formData.locations}
              onChange={(value) => handleInputChange("locations", value)}
              placeholder="Select locations"
              searchable
              creatable
              getCreateLabel={(query) => `+ Create "${query}"`}
              onCreate={(query) => {
                const item = { value: query, label: query };
                locationOptions.push(query);
                return item;
              }}
              comboboxProps={{ withinPortal: true }}
              clearable
            />
          ) : (
            <div className="w-[200px] text-start">
              {Array.isArray(item.locations) 
                ? item.locations.join(", ") 
                : item.locations || "-"
              }
            </div>
          )}
        </Table.Td>

        {/* Number of Assets */}
        <Table.Td>
          <Input
            className="w-[120px]"
            unstyled={!isEditing}
            disabled={!isEditing || isDisabled}
            value={formData.numberOfAssets}
            onChange={(e) =>
              handleInputChange("numberOfAssets", e.target.value)
            }
            placeholder="0"
            type="number"
          />
        </Table.Td>

        {/* Action buttons when editing */}
        {isEditing && !isDisabled && (
          <Table.Td>
            <div className="flex gap-2">
              <Button
                size="xs"
                disabled={isSubmiting}
                className="bg-green-500 hover:bg-green-600 text-white"
                onClick={submitUpdate}
              >
                {isSubmiting ? <Loading /> : "Save"}
              </Button>
              <Button
                size="xs"
                variant="outline"
                onClick={handleCancel}
                disabled={isSubmiting}
              >
                Cancel
              </Button>
            </div>
          </Table.Td>
        )}

        {/* Enable/Disable Actions - Always show buttons, change based on status */}
        <Table.Td>
          <div className="flex flex-row gap-2 justify-left items-center">
            {isDisabled ? (
              // Show Enable button for disabled items
              <Button
                size="xs"
                disabled={isStatusChanging}
                className="bg-green-500 hover:bg-green-600 text-white disabled:opacity-50"
                onClick={() => handleStatusChange("enable")}
              >
                {isStatusChanging ? <Loading /> : "Enable"}
              </Button>
            ) : (
              // Show Disable button for active items
              <Button
                size="xs"
                disabled={isStatusChanging}
                className="bg-red-500 hover:bg-red-600 text-white disabled:opacity-50"
                onClick={() => handleStatusChange("disable")}
              >
                {isStatusChanging ? <Loading /> : "Disable"}
              </Button>
            )}
          </div>
        </Table.Td>
      </Table.Tr>

      {/* Confirmation Modal */}
      <Modal
        opened={confirmModal.opened}
        onClose={closeModal}
        title={`${
          confirmModal.action === "enable" ? "Enable" : "Disable"
        } Item`}
        centered
        size="sm"
      >
        <div className="space-y-4">
          <Text size="md">
            Are you sure you want to{" "}
            <span className="font-semibold">{confirmModal.action}</span> &quot;
            <span className="font-semibold">{confirmModal.itemName}</span>
            &quot;?
          </Text>

          <div className="flex gap-3 justify-end mt-6">
            <Button
              variant="outline"
              onClick={closeModal}
              disabled={isStatusChanging}
            >
              Cancel
            </Button>
            <Button
              className={`${
                confirmModal.action === "enable"
                  ? "bg-green-500 hover:bg-green-600"
                  : "bg-red-500 hover:bg-red-600"
              } text-white`}
              onClick={confirmStatusChange}
              disabled={isStatusChanging}
            >
              {isStatusChanging ? (
                <>
                  <Loading /> Processing...
                </>
              ) : (
                `Yes, ${confirmModal.action}`
              )}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default DepartmentandProjectsRow;