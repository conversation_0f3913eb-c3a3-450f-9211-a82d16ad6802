import { CheckListGSIcon } from '@/assets/icons';
import { CgArrowRight } from 'react-icons/cg';
import { Link } from 'react-router-dom';

const GreenSightCard = ({ section }) => {
  return (
    <Link
      to={section.path}
      className="border-2 border-[#E8E7EA] rounded-3xl group flex flex-col group  gap-3 hover:border-primary duration-300 w-full bg-white p-6 shadow-sm transition-all"
    >
      <div className="p-4 rounded-full bg-primary/10  flex justify-center items-center w-16 h-16 mb-4">{section.icon}</div>
      <div>
        <h2 className="text-xl font-semibold text-[#272727]">{section.title}</h2>
        <p className="text-gray-600 text-sm mt-1">{section.description}</p>
      </div>
      <div className="flex justify-between mt-4 bg-[#07838F1A] p-4 rounded-lg">
        <ul>
          {section.advantages.map((advantage, i) => (
            <li key={i} className="flex items-center gap-2 text-[#29919B] mb-2 last:mb-0">
              <CheckListGSIcon />
              <span className="text-sm text-gray-700">{advantage}</span>
            </li>
          ))}
        </ul>
        <div className="text-[#29919B] font-semibold mt-4 flex items-center gap-2 hover:underline">
          Start Assessment <CgArrowRight className="size-5 mt-1 duration-200 group-hover:translate-x-1" />
        </div>
      </div>
    </Link>
  );
};

export default GreenSightCard;
