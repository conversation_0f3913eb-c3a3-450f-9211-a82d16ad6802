import Api_PCAF from '@/Api/apiS2_PCAF';
import { useRealEstateStore } from '@/Store/useRealEstateStore';
import { Spinner } from '@react-pdf-viewer/core';
import React from 'react';
import { BsStars } from "react-icons/bs";
import { questions } from '@/Pages/S3GreenShield/financialServicesStart/Constants/antiFinanTable';
import Cookies from 'js-cookie';
import axios from 'axios';

const FinancedEmissions = () => {
    const {
        loan_amount,
        property_value,
        building_emissions,
        property_type,
        property_location,
        property_size,
        energy_performance_certificate,
        data_quality_score,
        loading,

        setLoanAmount,
        setPropertyValue,
        setBuildingEmissions,
        setPropertyType,
        setPropertyLocation,
        setPropertySize,
        setEnergyPerformanceCertificate,
        setDataQualityScore,
        setLoading,
        setResults,
        estimate_building_emissions_loading,
        setEstimateBulidingEmissionLoading
    } = useRealEstateStore();

    const handleCalculate = async () => {
        setLoading(true);
        Api_PCAF.post("/real-estate", {
            "loan_amount": loan_amount,
            "property_value": property_value,
            "building_emissions": building_emissions,
            "property_type": property_type,
            "property_location": property_location,
            "property_size": property_size,
            "energy_performance_certificate": energy_performance_certificate,
            "data_quality_score": data_quality_score
        }).then((res) => {
            setLoading(false);
            const financedEmissions = res.data;
            setResults(financedEmissions);
        }).catch((error) => {
            console.log(error);
            setLoading(false);
        });
    }
    const Estimate_BulidingEmissions = async ()=>{
        const questions = getAnsweredQuestions();
        console.log(questions);
        setEstimateBulidingEmissionLoading(true);
        axios.post("https://gen-ai0-staging.azurewebsites.net/process_request",
            {
                "processor": "pcaf",
                "resources":{
                    "qa_context": questions,
                    "estimation_needed": "Building's Total Emissions (tCO2e)"
                }
            },
            {
                headers:{ "Authorization": `Bearer ${Cookies.get("level_user_token")}`}
            }
        ).then((res)=>{
            setEstimateBulidingEmissionLoading(false);
            setBuildingEmissions(res.data.ai_response.value);
            console.log(res.data)
        }).catch((err)=>{
            setEstimateBulidingEmissionLoading(false);
            console.log(err);
        })
    }
    const getAnsweredQuestions = () => {
        let questions = [];
        
        if (loan_amount !== "") {
            questions.push({
                question: "Loan Amount",
                user_answer: loan_amount
            });
        }
        if (property_value !== "") {
            questions.push({
                question: "Property Value",
                user_answer: property_value
            });
        }
        if (building_emissions !== "") {
            questions.push({
                question: "Building Emissions",
                user_answer: building_emissions
            });
        }
        if (property_type !== "") {
            questions.push({
                question: "Property Type",
                user_answer: property_type
            });
        }
        if (property_location !== "") {
            questions.push({
                question: "Property Location",
                user_answer: property_location
            });
        }
        if (property_size !== "") {
            questions.push({
                question: "Property Size",
                user_answer: property_size
            });
        }
        if (energy_performance_certificate !== "") {
            questions.push({
                question: "Energy Performance Certificate",
                user_answer: energy_performance_certificate
            });
        }
        if (data_quality_score !== "") {
            questions.push({
                question: "Data Quality Score",
                user_answer: data_quality_score
            });
        }
    
        return questions;
    };


  return (
    <div>
        <div className='flex flex-col gap-4 rounded-xl bg-white p-4 border-[#E8E7EA] border-2'>
            <h1 className='flex justify-center text-center bg-[#E6F3F4] text-[#8F8F8F] p-2 rounded-xl'>
            Financed Emissions = (Loan Amount / Property Value) * Building&apos;s Total Emissions
            </h1>
            <table className="w-full">
            <thead className="bg-[#F5F4F5]">
                <tr className='font-bold'>
                <th className="text-start p-3 ">Parameter</th>
                <th className="text-start p-3 ">Value</th>
                <th className="text-start p-3 ">AI Assistant</th>
                </tr>
            </thead>
            <tbody>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Loan Amount</td>
                    <td className="p-3 border border-gray-300">
                        <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="e.g., 500,000" onChange={(e) => setLoanAmount(e.target.value)} value={loan_amount}/>
                    </td>
                    <td className="p-3 border border-gray-300">Required Input</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Property Value</td>
                    <td className="p-3 border border-gray-300">
                        <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="e.g., 500,000" onChange={(e) => setPropertyValue(e.target.value)} value={property_value}/>
                    </td>
                    <td className="p-3 border border-gray-300">Required Input</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Property Type</td>
                    <td className="p-3 border border-gray-300">
                    <select type="text" className="border border-gray-400 rounded-lg p-2 w-full" onChange={(e) => setPropertyType(e.target.value)} value={property_type}>
                            <option value="Commercial-Office" className='text-gray-400'>Commercial-Office</option>
                            <option value="Technology">Technology</option>
                            <option value="Finance">Finance</option>
                        </select>
                    </td>
                    <td className="p-3 border border-gray-300">Required</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Property Location</td>
                    <td className="p-3 border border-gray-300">
                        <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="eg, New York, USA" onChange={(e) => setPropertyLocation(e.target.value)} value={property_location}/>
                    </td>
                    <td className="p-3 border border-gray-300">Required</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Property Size (m²)</td>
                    <td className="p-3 border border-gray-300">
                        <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="e.g., 20,000" onChange={(e) => setPropertySize(e.target.value)} value={property_size}/>
                    </td>
                    <td className="p-3 border border-gray-300">Required</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Energy Performance (EPC)</td>
                    <td className="p-3 border border-gray-300">
                    <select type="text" className="border border-gray-400 rounded-lg p-2 w-full" onChange={(e) => setEnergyPerformanceCertificate(e.target.value)} value={energy_performance_certificate}> 
                            <option value="C" className='text-gray-400'>C</option>
                            <option value="Technology">Technology</option>
                            <option value="Finance">Finance</option>
                        </select>
                    </td>
                    <td className="p-3 border border-gray-300">If available</td>
                </tr>
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Building&apos;s Total Emissions (tCO2e)</td>
                    <td className="p-3 border border-gray-300">
                        <input disabled={estimate_building_emissions_loading} type="text" className={"border border-gray-400 rounded-lg p-2 w-full" + (estimate_building_emissions_loading ? " cursor-not-allowed text-gray-500" : "")} placeholder="e.g., 20" onChange={(e) => setBuildingEmissions(e.target.value)} value={building_emissions}/>
                    </td>
                    <td className="p-3 border border-gray-300">
                        <button disabled={estimate_building_emissions_loading} className='flex flex-row items-center justify-center gap-2 rounded-md bg-[#1C889C] text-[#fff] p-2 w-full' onClick={Estimate_BulidingEmissions}>
                            {estimate_building_emissions_loading ? <Spinner size='24px' /> : <BsStars /> } Estimate
                        </button>
                    </td>
                </tr> 
                <tr className="border border-gray-300">
                    <td className="p-3 font-medium border border-gray-300">Data Quality Score</td>
                    <td className="p-3 border border-gray-300">
                        <input type="text" className="border border-gray-400 rounded-lg p-2 w-full" placeholder="e.g., 2 Number" onChange={(e) => setDataQualityScore(e.target.value)} value={data_quality_score}/>
                    </td>
                    <td className="p-3 border border-gray-300">Auto-updates</td>
                </tr>              
            </tbody>
            </table>

            <button disabled={loading} className='flex w-full justify-center text-center text-base font-semibold bg-[#07838F] text-[#ffffff] p-2 rounded-lg' onClick={handleCalculate}>
                {
                    loading ? <Spinner size='24px' /> : "Calculate Financed Emissions"
                }
            </button>
        </div>
    </div>
  )
}

export default FinancedEmissions