import React, { useState } from "react";
import ApiS2 from "@/Api/apiS2Config";
import { Button, Modal } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { FaTrashAlt } from "react-icons/fa";
import { showNotification } from "@mantine/notifications";
import Loading from "@/Components/Loading";

const DeleteModal = ({ title, ids, refetchAgain, url,keyName}) => {
  const [opened, { open, close }] = useDisclosure(false);
  const [submit, setSubmit] = useState(false);

  const deleteFunc = async () => {
    setSubmit(true);
    try {
      const data = await ApiS2.post(`/${url}`,{[keyName]: ids});      
      showNotification({
        message: "deleted Successfully",
        color: "green",
      });
      refetchAgain();
    } catch (er) {
      showNotification({
        message: "Error happend",
        color: "red",
      });
      //console.log(er);
    }
    setSubmit(false);
  };

  return (
    <>
      <button onClick={open} className="hover:bg-red-400 hover:text-white bg-red-100 border-2 border-red-600 p-2 rounded-2xl">
        Delete Selected
      </button>

      <Modal opened={opened} onClose={close} title={title}>
        {submit ? (
          <Loading />
        ) : (
          <Button
            leftSection={<FaTrashAlt size={12} />}
            variant="filled"
            size="sm"
            radius="md"
            className="mt-auto mb-2 ms-2 bg-red-600 text-white hover:bg-secondary"
            onClick={deleteFunc}
          >
            Confirm Delete All
          </Button>
        )}
      </Modal>
    </>
  );
};

export default DeleteModal;
