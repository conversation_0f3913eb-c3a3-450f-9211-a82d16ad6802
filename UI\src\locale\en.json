{"csrdDashboard": "CSRD & ESRS Readiness Dashboard", "description": "A descriptive body text comes here", "reportPage": "Report Page", "reportYearPlaceholder": "Reporting Year", "viewReport": "View Report", "downloadReport": "Download Report", "yourReviewReady": "Your review is ready to download", "overallReadinessScore": "Overall readiness score", "progressTracker": "Progress Tracker", "download": "Download", "share": "Share", "pleaseScrollHint": "To scroll Right and left Hold Shift and Scroll using your mouse.", "topicArea": "Topic Area", "assessmentQuestion": "Assessment Question", "assessmentLevel": "Assessment Level", "evidenceNotes": "Evidence/Notes", "priority": "Priority", "actionItems": "Action Items", "csrdReference": "CSRD Reference", "tagColleagues": "Tag Colleagues", "owner": "Owner", "dueDate": "Due Date", "howIsRiskCalculated": "How is risk calculated?", "doubleMaterialityReadiness": "Double Materiality Readiness", "descriptiveBodyText": "A descriptive body text comes here", "export": "Export", "assess": "<PERSON><PERSON><PERSON>", "stakeholderImpactAssessment": "Stakeholder Impact Assessment", "wantToAddSomething": "Want to add something?", "environmentalFootprint": "Environmental Footprint", "dashboard": "Dashboard", "dataQuality": "Data Quality", "recommendations": "Recommendations", "dataIntegration": "Data Integration", "reportingYear": "Reporting Year", "reviewReady": "Your review is ready to download", "dataQualityManagement": "Data Quality Management", "filter": "Filter", "Recommendations": "Recommendations", "DSDataIntegration": "DSDataIntegration", "Scenario": "<PERSON><PERSON><PERSON>", "Latest Results": "Latest Results", "Target pathway": "Target pathway", "Pathway to SBTi 1.5°C": "Pathway to SBTi 1.5°C", "howRiskCalculated": "How is risk calculated?", "descriptiveText": "A descriptive body text comes here", "question": "Question", "financialMateriality": "Financial Materiality 1-5", "impactMateriality": "Impact Materiality 1-5", "doubleMaterialityAssessment": "Double Materiality Assessment", "topic": "Topic", "category": "Category", "impactScore": "Impact Score", "financialScore": "Financial Score", "showingData": "Showing data {{start}} to {{end}} of {{total}} entries", "previousPage": "Previous", "nextPage": "Next", "pngExport": "PNG Export", "addFilter": "Add Filter", "governance": "Governance", "environment": "Environment", "social": "Social", "economic": "Economic", "keyFindings": "Key Findings:", "esgDueDiligence": "ESG Due Diligence", "descriptionText": "A descriptive body text comes here", "issbReference": "ISSB Reference", "humanRightsDueDiligence": "Human Rights Due Diligence", "finding1": "1. XXX", "finding2": "2. XXX", "finding3": "3. XXX", "backToDashboard": "Back to Dashboard", "issb": "ISSB", "readinessLevel": "Readiness Level", "launchpad": "Launchpad", "review": "Review", "reviewReadyMessage": "Your review is ready to download", "impactMeasurement": "Impact Measurement", "sdgsAlignmentAssessment": "SDGs Alignment Assessment", "sdg": "SDG", "impactArea": "Impact Area", "kpi": "Key Performance Indicator (KPI)", "baselineYear": "Baseline Year", "targetYear": "Target Year", "currentResultYear": "Current Result Year", "reportPageMain": {"reportingYear": "Reporting Year", "viewReport": "View Report", "share": "Share", "downloadReport": "Download Report", "reviewReady": "Your review is ready to download"}, "login": {"welcome": "Welcome to LevelUp ESG® Intelligent Portal:", "subtitle": "Your Path to Achieving Net Zero and Sustainable Growth", "connect": "Connect to Your LevelUp ESG Intelligent Platform", "email": "Email", "password": "Password", "loginButton": "<PERSON><PERSON>", "terms": "By logging in, you agree to our", "privacyPolicy": "Terms and Conditions"}, "Preparing the report": "Preparing the report", "navbar": {"title": "LevelUp NetZero"}, "Save": "Save", "Confirm": "Confirm", "Add Additional Payload": "Add Additional Payload", "uploadTemplate": {"step2": "Step 2: ", "selectFileAndUpload": "Select the file and upload the template.", "addDataAndUpload": "Add data in the template, save the file, and upload the template.", "uploadTemplate": "Upload template"}, "downloadTemplate": {"step1": "Step 1: ", "selectAndDownload": "Select an object and download the template.", "selectPlaceholder": "Select an object ...", "downloadTemplate": "Download template"}, "documentTable": {"receivedUploaded": "Received / Uploaded Document", "viewAll": "View All", "delete": "Delete", "date": "Date", "uploadFiles": "Upload Files", "status": "Status"}, "Data Integrations": "Data Integrations", "tabs": {"targets": "Targets", "actions": "Actions", "analytics": "Analytics", "climateProgram": "Climate Program", "manualInputs": "Manual Inputs", "batchInputs": "Batch Inputs", "connectors": "Connectors"}, "breadcrumb": {"launchpad": "Launchpad", "decarbonize": "Decarbonize", "collect": "Collect"}, "targetsView": {"title": "Targets", "description": "Set and track your emission reduction targets to drive your sustainability efforts.", "newTarget": "New Target"}, "targetsBestPractice": {"title": "Best Practices", "practice1": "Align your targets with global climate goals (e.g., Paris Agreement)", "practice2": "Set both short-term and long-term targets", "practice3": "Consider separate targets for different scopes of emissions", "practice4": "Regularly review and update targets based on progress and new information", "practice5": "Engage stakeholders in the target-setting process", "practice6": "Ensure targets are specific, measurable, achievable, relevant, and time-bound"}, "targetsModal": {"title": "Add New Target", "targetNameLabel": "Target name:", "sbtCompatibleLabel": "SBT Compatible", "sbtDescription": "SBTs (Science Based Targets) aim to align corporate sustainability strategies with the Paris Agreement. These targets require a high level of climate ambition and organizational buy-in. Learn more in our", "helpCenter": "Help Center", "additionalInfo": "Additional information related with SBTs", "assigneesLabel": "Assignees", "pickValue": "Pick value", "baselineYearLabel": "Baseline year", "baselineYearPlaceholder": "Baseline year", "targetYearLabel": "Target year", "targetYearPlaceholder": "Target year", "scopeSelection": "Scope Selection", "scope1Label": "Scope 1", "scope2Label": "Scope 2", "scope3Label": "Scope 3", "emissionReductionLabel": "Emission reduction percentage", "emissionReductionPlaceholder": "Input inside Input.Wrapper", "emissionReductionDescription": "Type in the desired value in % (Must be from 1-100)", "notesLabel": "Notes", "notesPlaceholder": "", "addTarget": "Add Target"}, "table": {"targetName": "Target Name", "targetType": "Target Type", "reductionAmbition": "Reduction Ambition", "targetScopes": "Target Scopes", "baseTargetYears": "Base-Target Years"}, "climateProgram": {"yourDecarbonizationJourney": "Your Decarbonization Journey", "checkEmissions": "Check your current and predicted CO₂ emissions."}, "graphKey": {"title": "GRAPH KEY", "currentEmissions": "Current emissions", "bauEmissionsForecast": "BAU emissions forecast", "decarbonizationPlanLevelUp": "Decarbonization Plan (LevelUp ESG + Your initiatives)", "decarbonizationPlanYourInitiatives": "Decarbonization Plan (Your initiatives)", "targetPathway": "Target pathway", "pathwaySBTi": "Pathway to SBTi 1.5°C"}, "EmissionOverview": "Emission Overview", "Launchpad": "Launchpad", "Awards": "Awards", "SaveAsPng": "Save as png", "CarbonFootprintBreakdown": "Carbon Footprint Breakdown", "PickDatesRange": "Pick dates range", "Electricity": "Electricity", "Transportation": "Transportation", "FuelAndGas": "Fuel & Gas", "NetZeroTargetOverview": "NetZero Target Overview", "CurrentConsumptionOverview": "Current Consumption Overview", "CO2E": "CO2 E", "FromLastMonth": "From last month", "FromTarget": "From Target", "TopEmissionsByScope": "Top Emissions by <PERSON><PERSON>", "Scope1": "Scope 1", "Scope2": "Scope 2", "Scope3": "Scope 3", "TopEmissionsByLocations": "Top Emissions by Locations", "Location1": "Location 1", "Location2": "Location 2", "Location3": "Location 3", "Today": "Today", "Sunday": "Sunday", "Monday": "Monday", "AleeshaAddedComment": "<PERSON><PERSON><PERSON> added a comment to E- Q.17", "LisaAddedQuery": "<PERSON> added a query to E- Q.10", "ReportAccepted": "Your environment report was accepted", "Lisa added a query to E- Q.10": "<PERSON> added a query to E- Q.10", "Your environment report was accepted": "Your environment report was accepted", "Aleesha added a comment to E- Q.17": "<PERSON><PERSON><PERSON> added a comment to E- Q.17", "Location": "Location", "Scope": "<PERSON><PERSON>", "Monthly Consumption Target": "Monthly Consumption Target", "Current Consumption Overview": "Current Consumption Overview", "From last month": "From last month", "Fuel & Gas": "Fuel & Gas", "View All": "View All", "BronzeAwardAlt": "Bronze Award", "YouGotThis": "You Got this", "AchievedAlt": "Achieved", "Bronze": "Bronze", "GreenStarter": "<PERSON>er", "SilverAwardAlt": "Silver Award", "Silver": "Silver", "EcoInnovator": "Eco Innovator", "GoldAwardAlt": "Gold Award", "Gold": "Gold", "SustainabilityLeader": "Sustainability Leader", "DiamondAwardAlt": "Diamond Award", "Diamond": "Diamond", "ClimateChampion": "Climate Champion", "Measure": "Measure", "TimePeriod": "Time period", "DatePlaceholder": "\"From dd / MM / YYYY\" , \"To dd / MM / YYYY\"", "Assets": "Assets", "Choose": "<PERSON><PERSON>", "EmissionsOverview": "Emissions Overview", "ScopeOverview": "Scope Overview", "LocationOverview": "Location Overview", "Coming Soon": "Coming Soon", "ScopeBreakdown": "Scope Breakdown", "TotalAbsoluteEmissions": "Total Absolute Emissions", "TopContributingCategories": "Top Contributing Categories", "ViewAll": "View All", "TopContributingFacilities": "Top Contributing Facilities", "EmissionsIntensity": "Emissions Intensity", "ByRevenue": "By Revenue", "Revenue": "Revenue", "ByGrossFloorArea": "By Gross Floor Area", "GrossFloorArea": "Gross Floor Area", "ByGrossMerchandiseValue": "By Gross Merchandise Value", "GrossMerchandiseValue": "Gross Merchandise Value", "InputRequired": "Input Required", "Your review is ready to download": "Your review is ready to download", "Customize": "Customize", "Yearly reporting": "Yearly reporting", "Two formats availble": "Two formats availble", "About your emissions report": "About your emissions report", "Batch Inputs": "Batch Inputs", "Manual Inputs": "Manual Inputs", "Delete": "Delete", "Date": "Date", "Period": "Period", "UsageType": "Usage Type", "ShowingData": "Showing data {{start}} to {{end}} of {{total}} entries", "ReviewStatus": "Review Status", "Pending": "Pending", "LastReviewed": "Last reviewed", "ReviewedBy": "Reviewed by", "Review Status": "Review Status", "Last reviewed": "Last reviewed", "Reviewed by": "Reviewed by", "Suppliers Inputs": "Suppliers Inputs", "Suppliers Data": "Suppliers Data", "Review Suppliers": "Review Suppliers", "Showing data": "Showing data", "to": "to", "of": "of", "entries": "entries", "Upload Evidence": "Upload Evidence", "reviewSupplier": {"delete": "Delete", "name": "Name", "date_of_contact": "Date of contact", "qty_of_production": "Qty of production", "family_name": "Family Name", "region": "Region", "sector": "Sector", "showingData": "Showing data {{start}} to {{end}} of {{total}} entries", "reviewStatus": "Review Status:", "pending": "Pending", "lastReviewed": "Last reviewed:", "reviewedBy": "Reviewed by:"}, "pagination": {"next": "Next", "previous": "Previous"}, "Change Password": "Change Password", "Request Password Change": "Request Password Change", "Configurations": "Configurations", "CustomFactor": "Custom Factor", "BatchAssets": "<PERSON><PERSON>", "SelectObject": "Select an object...", "DownloadTemplate": "Download template", "AddDataTemplate": "Add data in the template, save the file, and upload the template.", "UploadTemplate": "Upload template", "NavbarTitle": "Configurations", "SearchPlaceholder": "Search....", "AddNewAsset": "+ Add New Asset", "AssetType": "Asset Type", "AssetName": "Asset Name", "AssetReference": "Asset Reference", "AdditionalNotes": "Additional Notes", "Action": "Action", "View all user guides, manuals, and training videos.": "View all user guides, manuals, and training videos.", "Guides": "Guides", "SupportRequestTitle": "Support Request", "SupportRequestSubtitle": "Submit a support request to update existing content or resolve any issues you encounter.", "DescriptionLabel": "Description", "SupportRequestNote": "We’ll get back to you via your email or your phone in account settings.", "SubmitButton": "Submit", "Frequently Asked Questions": "Frequently Asked Questions", "Get to know our platform and find answers to common questions.": "Get to know our platform and find answers to common questions.", "There seems to be an issue displaying the PDF. Please check if you have any browser extensions like Internet Download Manager (IDM) that might be blocking the PDF display.": "There seems to be an issue displaying the PDF. Please check if you have any browser extensions like Internet Download Manager (IDM) that might be blocking the PDF display.", "Unleash your ESG potential with LevelUp's ESG Compass Suite. Select your respective system from our launchpad to streamline your sustainability journey and join us in creating a greener future together.": "Unleash your ESG potential with LevelUp's ESG Compass Suite. Select your respective system from our launchpad to streamline your sustainability journey and join us in creating a greener future together.", "Latest Assessment": "Latest Assessment", "Progress Tracker": "Progress Tracker", "CommunityPost": {"climateChangeActivist": "Climate Change Activist", "askedAgo": "asked {{seconds}} ago", "valueQuestion": "What is the value of addressing climate change in the community?", "sustainability": "Sustainability", "governance": "Governance", "sustainabilityTag": "Sustainability", "governanceTag": "Governance", "save": "Save", "share": "Share", "votes": "votes", "views": "views", "answers": "answers"}, "CourseCart": {"courseOverview": "Course Overview"}, "previous": "Previous", "next": "Next", "askPublicQuestion": "Ask a public question", "writingAGoodQuestion": "Writing a good question", "writingGuidance": "You’re ready to ask a sustainability-related question and this form will help guide you through the process.", "businessQuestion": "Looking to ask a business-related question? ", "businessQuestionGuidance": "Feel free to add your topic tag below to find peers.", "steps": "Steps for writing a good question:", "stepsList": ["Summarize your problem in a one-line title.", "Describe your problem in more detail.", "Add attachment if available to help peers to understand your question.", "Add “tags” which help surface your question to members of the community.", "Review your question and post it to the site."], "addTitle": "Add a title", "titleGuidance": "Be specific and imagine you’re asking a question to another person.", "titlePlaceholder": "e.g. What is the main value your organization’s sustainability department delivers?", "detailsQuestion": "What are the details of your problem?", "detailsGuidance": "Introduce the problem and expand on what you put in the title. Minimum 20 characters.", "detailsPlaceholder": "Describe your problem here in details", "tags": "Tags", "tagsGuidance": "Add up to 5 tags to describe what your question is about. Start typing to see suggestions.", "tagsPlaceholder": "e.g. (society, sustainability, etc.)", "nothingFound": "No matching tags found", "Add an attachment to describe your question.": "Add an attachment to describe your question.", "Attachment": "Attachment", "PeersCommunity": "Peers Community", "Academy": "Academy", "Resources": "Resources", "SharedPost": "shared a post", "AskedQuestion": "asked a question", "AnsweredOnQuestion": "answered a question", "AllQuestions": "All Questions", "CreatePoll": "Create Poll", "AddQuestion": "Add Question", "QuestionsCount": "{{count}} questions", "Newest": "Newest", "Oldest": "Oldest", "Unanswered": "Unanswered", "Polls": "Polls", "AddFilters": "Add Filters", "ExploreByQuestion": "Explore by Question", "Sustainably": "Sustainably", "Governance": "Governance", "Environment": "Environment", "GreenShield": "GreenShield", "Society": "Society", "Marketing": "Marketing", "RecentActivities": "Recent Activities", "TryNewExperience": "Try the New Experience", "ResourcesHubTitle": "Resources Hub", "ResourcesHubSubtitle": "Explore a wide range of topics and resources to empower business leaders in areas such as governance, risk management, and internal controls.", "EmpoweringBusinessLeaders": "Empowering Business Leaders with Essential Knowledge", "ResourceDescription": "Our resources are designed to equip business leaders with the tools they need to drive sustainability and governance in their organizations.", "PickFavoriteTopic": "Pick Your Favorite Topic", "GreenHubTitle": "LevelUp ESG®: Empowering Green Growth: Connect, Learn, and Access Resources", "PeerCommunity": "Peer Community", "Sustainability": "Sustainability", "AnsweredQuestion": "Answered on your question", "LevelUpAcademyTitle": "LevelUp Academy", "AcademySubtitle": "Your Gateway to Sustainable Growth", "PickYourFavoriteCourse": "Pick Your Favorite Course", "edit_profile_information": "Edit profile information", "notifications": "Notifications", "on": "On", "off": "Off", "language": "Language", "english": "English", "security": "Security", "theme": "Theme", "light_mode": "Light Mode", "dark_mode": "Dark Mode", "help_support": "Help & Support", "contact_us": "Contact us", "privacy_policy": "Privacy policy", "Remove": "Remove", "Upload complete": "Upload complete", "document.CSV": "document.CSV", "Welcome": "Welcome", "Transform your company’s path to sustainability": "Transform your company’s path to sustainability", "Gain a competitive edge by putting your finger on the pulse of your ESG footprint and embracing responsible practices.": "Gain a competitive edge by putting your finger on the pulse of your ESG footprint and embracing responsible practices.", "Assess your organisation's compliance status and identify gaps to ensure alignment with evolving ESG regulatory requirements.": "Assess your organisation's compliance status and identify gaps to ensure alignment with evolving ESG regulatory requirements.", "AI-powered carbon accounting - Real-time emissions tracking across your value chain and data-driven optimization for effective reduction strategies.": "AI-powered carbon accounting - Real-time emissions tracking across your value chain and data-driven optimization for effective reduction strategies.", "Streamlined net zero journey - Automated data collection, compliance reporting, and accelerated path to achieve net zero emissions targets.": "Streamlined net zero journey - Automated data collection, compliance reporting, and accelerated path to achieve net zero emissions targets.", "Launch": "Launch", "Credibility Protection - Rigorous assessments to validate ESG claims, disclosures, and marketing accurately reflect actual sustainability practices.": "Credibility Protection - Rigorous assessments to validate ESG claims, disclosures, and marketing accurately reflect actual sustainability practices.", "Transparency & Accountability Reinforced - Enable anonymous reporting of concerns for impartial assessment, ensuring transparency and building stakeholder trust through effective greenwashing risk management.": "Transparency & Accountability Reinforced - Enable anonymous reporting of concerns for impartial assessment, ensuring transparency and building stakeholder trust through effective greenwashing risk management."}