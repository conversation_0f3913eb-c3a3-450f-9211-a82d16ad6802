import React from 'react'
import Sidebar from '../Navbar/Sidebar'
import Welcome from '../Dashboard/Welcome'
import CompanyName from './CompanyName'
import photoUser from '/assets/Images/img.jpg'
import { FaFire } from "react-icons/fa";
import FESG from './FESG'
import ChartCo from '../Dashboard/ChartCo'
import CO2 from './CO2'
import Strategies from './Strategies'
import Vendors from '../Dashboard/Vendors'

function DashboardStared() {
  return (
    <div className='relative flex gap-[1rem] bg-[#F9FFF5] w-full'>
      <Sidebar/>
      <div className='relative py-[3rem] px-[2rem] w-full'>
            <Welcome name="Deepa"/>
            <div className='mt-[2rem]'>
                <div className='mb-[2rem]'>
                    <CompanyName photo={photoUser} icon=<FaFire /> country="London, United Kingdom" construction="construction" employee="50-100"/>
                </div>
                <div className='flex flex-wrap xl:flex-nowrap gap-[1.7rem] items-center mb-[2rem]'>
                    <FESG name="Fundamentals"/>
                    <FESG name="Environmental"/>
                    <FESG name="Social"/>
                    <FESG name="Governance"/>
                </div>
                
               <div className='flex flex-col xl:flex-row  gap-[1rem] xl:gap-[2rem]  mb-[2rem]'>
                    <div className='bg-white py-[1rem] px-[1.5rem] rounded-2xl shadow-2xl w-[90%]  xl:w-[60%] mx-auto mb-[2rem]'>
                        <div className='flex items-center justify-between mb-5'>
                            <h1 className='font-bold text-[18px] mb-5'>Carbon Footprint Breakdown</h1>
                            <div className=' bg-[#2A939C]  py-1 px-5 rounded-lg shadow-2xl shadow-black'>
                                <button className='font-[300] text-white flex items-center gap-[.5rem]'>
                                Add New
                                </button>
                            </div>
                        </div>
                        <div className='flex flex-wrap gap-[.5rem] justify-between'>
                                <ChartCo/>
                        </div>
                    </div>
                    <div className='xl:w-[37%] w-[90%]'>
                        <CO2 number="412.314" numMonth="34.3%"/>
                        <h2 className='font-bold mb-3'>Strategies tailored for your company</h2>
                        <Strategies icon=<FaFire /> desc= "Electrify 25% of your delivery fleet."/>
                        <Strategies icon=<FaFire /> desc= "Implement water recycling technologies at Factory A."/>
                        <Strategies icon=<FaFire /> desc= "Improve renewable energy usage by installing solar panets at HQ"/>
                    </div>  
                </div>
                <Vendors/>
            </div>

      </div>
    </div>
  )
}

export default DashboardStared