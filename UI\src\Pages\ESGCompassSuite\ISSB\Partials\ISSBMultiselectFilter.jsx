import { Checkbox, Combobox, Group, useCombobox } from "@mantine/core";
import { IoFilter } from "react-icons/io5";

const groceries = ["Evidence"];

export default function ISSBMultiselectFilter({ setValue, value }) {
  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
    onDropdownOpen: () => combobox.updateSelectedOptionIndex("active"),
  });

  const handleValueSelect = (val) => {
    setValue((current) =>
      current?.includes(val)
        ? current.filter((v) => v !== val)
        : [...current, val]
    );
  };

  const handleValueRemove = (val) => {
    setValue((current) => current.filter((v) => v !== val));
  };

  const options = groceries.map((item) => (
    <Combobox.Option value={item} key={item} active={value?.includes(item)}>
      <Group gap="lg">
        <Checkbox
          checked={value?.includes(item)}
          onChange={() => handleValueSelect(item)}
          aria-hidden
          tabIndex={-1}
          style={{ pointerEvents: "none" }}
        />
        <span>{item}</span>
      </Group>
    </Combobox.Option>
  ));

  return (
    <Combobox
      store={combobox}
      onOptionSubmit={(val) => handleValueSelect(val)}
      withinPortal={false}
    >
      <Combobox.DropdownTarget>
        <p
          className="font-semibold text-[#9C9C9C] w-full flex justify-center  items-center cursor-pointer py-2 text-nowrap"
          onClick={() => combobox.toggleDropdown()}
        >
          <IoFilter className="mx-2 text-primary" />
          Filter Columns
        </p>
      </Combobox.DropdownTarget>

      <Combobox.Dropdown>
        <Combobox.Options>{options}</Combobox.Options>
      </Combobox.Dropdown>
    </Combobox>
  );
}
