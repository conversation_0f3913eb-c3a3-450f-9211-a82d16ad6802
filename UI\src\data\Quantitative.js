const Quantitative = [
  {
    "type": "GHG Emissions",
    "name": "Direct (Scope 1) GHG emissions",
    "unit": "tCO₂e"
  },
  {
    "type": "GHG Emissions",
    "name": "Energy indirect (Scope 2) GHG emissions",
    "unit": "tCO₂e"
  },
  {
    "type": "GHG Emissions",
    "name": "Other indirect (Scope 3) GHG emissions",
    "unit": "tCO₂e"
  },
  {
    "type": "GHG Intensity",
    "name": "GHG emissions intensity",
    "unit": "tCO₂e/$ million revenue"
  },
  {
    "type": "GHG Reduction",
    "name": "GHG emissions reduction",
    "unit": "tCO₂e"
  },
  { 
    "type": "Air Emissions",
    "name": "NOx emissions",
    "unit": "metric tons"
  },
  { 
    "type": "Air Emissions",
    "name": "SOx emissions",
    "unit": "metric tons"
  },
  {
    "type": "Air Emissions",
    "name": "Particulate matter emissions",
    "unit": "metric tons"
  },
  { "type": "Energy", "name": "Total energy consumption", "unit": "MWh" },
  {
    "type": "Energy Intensity",
    "name": "Energy intensity",
    "unit": "MWh/$ million revenue"
  },
  { "type": "Renewable Energy", "name": "Renewable energy ratio", "unit": "%" },
  {
    "type": "Energy Efficiency",
    "name": "Energy efficiency improvement",
    "unit": "%"
  },
  { "type": "Energy Demand", "name": "Peak energy demand", "unit": "MW" },
  { "type": "Water", "name": "Total water withdrawal", "unit": "m³" },
  {
    "type": "Water Intensity",
    "name": "Water intensity",
    "unit": "m³/$ million revenue"
  },
  {
    "type": "Water Recycling",
    "name": "Water recycled and reused",
    "unit": "m³"
  },
  { "type": "Water Recycling", "name": "Water recycled ratio", "unit": "%" },
  {
    "type": "Water Risk",
    "name": "Water consumption in water-stressed areas",
    "unit": "m³"
  },
  {
    "type": "Materials",
    "name": "Total materials used",
    "unit": "metric tons"
  },
  {
    "type": "Recycled Materials",
    "name": "Recycled input materials",
    "unit": "%"
  },
  {
    "type": "Materials Efficiency",
    "name": "Materials efficiency",
    "unit": "metric tons/$ million revenue"
  },
  {
    "type": "Hazardous Materials",
    "name": "Hazardous materials used",
    "unit": "metric tons"
  },
  { "type": "Packaging", "name": "Packaging reduction", "unit": "%" },

  { "type": "Waste", "name": "Total waste generated", "unit": "metric tons" },
  {
    "type": "Waste Intensity",
    "name": "Waste intensity",
    "unit": "metric tons/$ million revenue"
  },
  {
    "type": "Waste Diversion",
    "name": "Waste diverted from disposal",
    "unit": "metric tons"
  },
  { "type": "Waste Diversion", "name": "Waste diversion rate", "unit": "%" },
  {
    "type": "Hazardous Waste",
    "name": "Hazardous waste generated",
    "unit": "metric tons"
  },
  { "type": "Waste Reduction", "name": "Waste reduction", "unit": "%" },
  {
    "type": "Circular Economy",
    "name": "Product reclamation rate",
    "unit": "%"
  },
  {
    "type": "Circular Economy",
    "name": "Circular material flow rate",
    "unit": "%"
  },
  {
    "type": "Product Sustainability",
    "name": "Product repairability score",
    "unit": "score (1 to 10)"
  },
  {
    "type": "Product Sustainability",
    "name": "Product longevity",
    "unit": "years"
  },
  { "type": "Remanufacturing", "name": "Remanufactured products", "unit": "%" },

  { "type": "Land Use", "name": "Land use - total", "unit": "hectares" },
  {
    "type": "Biodiversity",
    "name": "Protected or restored habitats",
    "unit": "hectares"
  },
  {
    "type": "Biodiversity",
    "name": "Operational sites in protected areas",
    "unit": "count"
  },
  {
    "type": "Biodiversity",
    "name": "Biodiversity offset area",
    "unit": "hectares"
  },
  { "type": "Biodiversity", "name": "Species protected", "unit": "count" },
  { "type": "Land Remediation", "name": "Land remediated", "unit": "hectares" },
  {
    "type": "Land Remediation",
    "name": "Land remediation cost",
    "unit": "currency"
  },
  {
    "type": "Biodiversity",
    "name": "Net positive impact area",
    "unit": "hectares"
  },
  { "type": "Deforestation", "name": "Deforestation-free supply", "unit": "%" },
  {
    "type": "Land Conversion",
    "name": "Natural habitat converted",
    "unit": "hectares"
  },

  { "type": "Workforce", "name": "Total employees", "unit": "count" },
  {
    "type": "Workforce Turnover",
    "name": "Employee turnover rate",
    "unit": "%"
  },
  { "type": "Workforce Hiring", "name": "New hire rate", "unit": "%" },
  {
    "type": "Training",
    "name": "Average training hours",
    "unit": "hours/employee"
  },
  { "type": "Engagement", "name": "Employee engagement score", "unit": "%" },
  {
    "type": "Health & Safety",
    "name": "Work-related injuries",
    "unit": "count"
  },
  {
    "type": "Health & Safety",
    "name": "Lost time injury frequency rate (LTIFR)",
    "unit": "rate"
  },
  {
    "type": "Health & Safety",
    "name": "Total recordable incident rate (TRIR)",
    "unit": "rate"
  },
  {
    "type": "Health & Safety",
    "name": "Work-related fatalities",
    "unit": "count"
  },
  { "type": "Pay Equity", "name": "Gender pay gap", "unit": "%" },
  { "type": "Diversity", "name": "Employee diversity - gender", "unit": "%" },
  {
    "type": "Diversity",
    "name": "Employee diversity - ethnicity",
    "unit": "%"
  },
  { "type": "Diversity", "name": "Employee diversity - age", "unit": "%" },
  {
    "type": "Work-Life Balance",
    "name": "Parental leave return rate",
    "unit": "%"
  },
  { "type": "Absenteeism", "name": "Absenteeism rate", "unit": "%" },

  { "type": "Board Governance", "name": "Board independence", "unit": "%" },
  {
    "type": "Board Diversity",
    "name": "Board diversity - gender",
    "unit": "%"
  },
  {
    "type": "Executive Pay",
    "name": "CEO-to-worker pay ratio",
    "unit": "ratio"
  },
  { "type": "Shareholder Rights", "name": "Say-on-pay approval", "unit": "%" },
  { "type": "Ethics", "name": "Ethics training completion", "unit": "%" },
  { "type": "Ethics", "name": "Ethics violations", "unit": "count" },
  { "type": "Corruption", "name": "Corruption incidents", "unit": "count" },
  { "type": "Whistleblower", "name": "Whistleblower reports", "unit": "count" },
  { "type": "Data Security", "name": "Data breaches", "unit": "count" },
  { "type": "Privacy", "name": "Privacy complaints", "unit": "count" },
  {
    "type": "Regulatory Fines",
    "name": "Regulatory fines",
    "unit": "currency"
  },
  {
    "type": "Compliance",
    "name": "Compliance training completion",
    "unit": "%"
  },

  {
    "type": "Economic Value",
    "name": "Direct economic value generated",
    "unit": "currency"
  },
  { "type": "Green Revenue", "name": "Green revenue", "unit": "%" },
  { "type": "ESG Finance", "name": "ESG-linked finance", "unit": "currency" },
  {
    "type": "Economic Distribution",
    "name": "Economic value distributed",
    "unit": "currency"
  },
  {
    "type": "Economic Retention",
    "name": "Economic value retained",
    "unit": "currency"
  },
  {
    "type": "Climate Finance",
    "name": "Climate-related financial impacts",
    "unit": "currency"
  },
  {
    "type": "Cost Savings",
    "name": "ESG-related cost savings",
    "unit": "currency"
  },
  {
    "type": "R&D Investment",
    "name": "Sustainability R&D investment",
    "unit": "currency"
  },
  { "type": "R&D Ratio", "name": "Sustainability R&D ratio", "unit": "%" },
  { "type": "Taxation", "name": "Tax paid", "unit": "currency" },
  { "type": "Taxation", "name": "Effective tax rate", "unit": "%" },

  {
    "type": "Supplier ESG",
    "name": "Suppliers assessed for ESG",
    "unit": "count"
  },
  { "type": "Local Procurement", "name": "Local procurement", "unit": "%" },
  {
    "type": "Supply Chain Emissions",
    "name": "Supply chain emissions",
    "unit": "tCO₂e"
  },
  {
    "type": "Supplier Diversity",
    "name": "Supplier diversity spending",
    "unit": "currency"
  },
  { "type": "Supplier Diversity", "name": "Supplier diversity", "unit": "%" },
  {
    "type": "Supplier Compliance",
    "name": "Supplier code of conduct coverage",
    "unit": "%"
  },
  { "type": "Supplier Risk", "name": "High-risk suppliers", "unit": "count" },
  {
    "type": "Supplier Criticality",
    "name": "Critical suppliers",
    "unit": "count"
  },
  { "type": "Supplier Audits", "name": "Supplier ESG audits", "unit": "count" },

  { "type": "Product Recalls", "name": "Product recalls", "unit": "count" },
  { "type": "Product Recalls", "name": "Products recalled", "unit": "%" },
  {
    "type": "Product Safety",
    "name": "Product safety incidents",
    "unit": "count"
  },
  {
    "type": "Product Safety",
    "name": "Product safety testing coverage",
    "unit": "%"
  },
  {
    "type": "Safety Certifications",
    "name": "Safety certifications",
    "unit": "count"
  },
  {
    "type": "Customer Complaints",
    "name": "Customer complaints",
    "unit": "count"
  },
  {
    "type": "Customer Complaints",
    "name": "Customer complaint rate",
    "unit": "%"
  },
  { "type": "Quality", "name": "Defect rate", "unit": "%" },
  { "type": "Quality", "name": "First-pass yield", "unit": "%" },
  { "type": "Quality Cost", "name": "Quality cost", "unit": "% of revenue" },
  { "type": "Eco-Labels", "name": "Products with eco-labels", "unit": "%" },
  {
    "type": "Lifecycle Assessment",
    "name": "Lifecycle assessment coverage",
    "unit": "%"
  },
  {
    "type": "Product Carbon Footprint",
    "name": "Product carbon footprint",
    "unit": "kgCO₂e/unit"
  },
  {
    "type": "Product Water Footprint",
    "name": "Product water footprint",
    "unit": "m³/unit"
  },
  {
    "type": "Sustainable Materials",
    "name": "Sustainable materials content",
    "unit": "%"
  },
  { "type": "Innovation", "name": "Innovation rate", "unit": "%" },
  { "type": "Patents", "name": "Patents filed", "unit": "count" },
  { "type": "Patents", "name": "Patents granted", "unit": "count" },
  { "type": "R&D", "name": "R&D investment", "unit": "currency" },
  { "type": "R&D Intensity", "name": "R&D intensity", "unit": "% of revenue" },
  { "type": "Digital Revenue", "name": "Digital revenue", "unit": "%" },
  { "type": "Digital Customers", "name": "Digital customers", "unit": "%" },
  { "type": "Digital Operations", "name": "Digital operations", "unit": "%" },
  {
    "type": "Digital Investment",
    "name": "Digital investment",
    "unit": "currency"
  },
  {
    "type": "Cybersecurity",
    "name": "Cybersecurity incidents",
    "unit": "count"
  },
  {
    "type": "Cybersecurity Response",
    "name": "Mean time to detect",
    "unit": "hours"
  },
  {
    "type": "Cybersecurity Response",
    "name": "Mean time to resolve",
    "unit": "hours"
  },
  {
    "type": "Security Training",
    "name": "Security training completion",
    "unit": "%"
  },
  {
    "type": "Security Assessment",
    "name": "Security assessment coverage",
    "unit": "%"
  },
  {
    "type": "Customer Satisfaction",
    "name": "Net Promoter Score (NPS)",
    "unit": "score (-100 to 100)"
  },
  {
    "type": "Customer Satisfaction",
    "name": "Customer satisfaction index",
    "unit": "score (1 to 100)"
  },
  {
    "type": "Customer Retention",
    "name": "Customer retention rate",
    "unit": "%"
  },
  {
    "type": "Customer Value",
    "name": "Customer lifetime value",
    "unit": "currency"
  },
  {
    "type": "Customer Effort",
    "name": "Customer effort score",
    "unit": "score (1 to 10)"
  },
  {
    "type": "Community Investment",
    "name": "Community investment",
    "unit": "currency"
  },
  {
    "type": "Community Investment",
    "name": "Community investment ratio",
    "unit": "% of profit"
  },
  { "type": "Volunteering", "name": "Volunteer hours", "unit": "hours" },
  {
    "type": "Volunteering",
    "name": "Volunteer participation rate",
    "unit": "%"
  },
  {
    "type": "Community Projects",
    "name": "Community projects",
    "unit": "count"
  },
  { 
    "type": "Social Impact",
    "name": "Beneficiaries reached",
    "unit": "count"
  },
  {
    "type": "Social ROI",
    "name": "Social return on investment",
    "unit": "ratio"
  },
  { 
    "type": "Data Quality",
    "name": "Data completeness",
    "unit": "%"
  },
  { 
    "type": "Data Quality",
    "name": "Error correction rate",
    "unit": "%"
  },
  { 
    "type": "Assurance",
    "name": "External assurance coverage",
    "unit": "%"
  },
  {
    "type": "Data Collection",
    "name": "Data collection lead time",
    "unit": "days"
  },
  {
    "type": "Reporting Timeliness",
    "name": "Reporting timeliness",
    "unit": "days"
  },
  {
    "type": "Data Verification",
    "name": "Data verification coverage",
    "unit": "%"
  },
  {
    "type": "Stakeholder Engagement",
    "name": "Stakeholder groups engaged",
    "unit": "count"
  },
  {
    "type": "Stakeholder Engagement",
    "name": "Stakeholder engagement activities",
    "unit": "count"
  },
  {
    "type": "Stakeholder Feedback",
    "name": "Stakeholder feedback implemented",
    "unit": "%"
  },
  {
    "type": "Stakeholder Satisfaction",
    "name": "Stakeholder satisfaction",
    "unit": "%"
  },
  {
    "type": "Reporting Frameworks",
    "name": "Reporting frameworks used",
    "unit": "count"
  },
  { 
    "type": "ESG Ratings", 
    "name": "ESG ratings coverage", 
    "unit": "count" 
  },
  { 
    "type": "ESG Disclosure",
    "name": "ESG disclosure score",
    "unit": "score"
  }
];

export default Quantitative

