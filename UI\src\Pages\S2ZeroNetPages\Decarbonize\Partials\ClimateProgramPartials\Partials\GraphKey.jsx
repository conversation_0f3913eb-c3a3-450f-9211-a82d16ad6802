import React from "react";
import { Switch } from "@mantine/core";
import { useTranslation } from "react-i18next";

const GraphKey = ({ visibleSeries, onSwitchChange }) => {
  const { t } = useTranslation();

  return (
    <div className="flex px-4 py-5 bg-white md:ml-9 rounded-3xl">
      <div className="flex flex-col items-start justify-between gap-3">
        <h3>{t("graphKey.title")}</h3>

        <div className="flex items-center justify-center gap-3">
          <span className="w-6 h-1 bg-black"></span>
          <span>{t("graphKey.currentEmissions")}</span>
        </div>

        <div className="flex items-center justify-center w-full gap-3">
          <span className="text-[#DA4848] font-bold text-xl">...</span>
          <div className="flex justify-between w-full">
            <span>{t("graphKey.bauEmissionsForecast")}</span>
            <Switch
              checked={visibleSeries.Scope1}
              onChange={() => onSwitchChange("Scope1")}
              size="md"
              color="red"
            />
          </div>
        </div>

        <div className="flex items-center justify-center w-full gap-3">
          <span className="w-7 h-1 bg-[#29919B]"></span>
          <div className="flex justify-between w-full">
            <span className="px-4">
              {t("graphKey.decarbonizationPlanLevelUp")}
            </span>
            <Switch
              checked={visibleSeries.Scope2}
              onChange={() => onSwitchChange("Scope2")}
              size="md"
              color="blue"
            />
          </div>
        </div>

        <div className="flex items-center justify-center w-full gap-3">
          <span className="w-6 h-1 bg-[#E87E42]"></span>
          <div className="flex justify-between w-full">
            <span className="px-4">
              {t("graphKey.decarbonizationPlanYourInitiatives")}
            </span>
            <Switch
              checked={visibleSeries.Scope3}
              onChange={() => onSwitchChange("Scope3")}
              size="md"
              color="green"
            />
          </div>
        </div>

        <div className="flex items-center justify-center w-full gap-3">
          <span className="text-[#2DC9FA] font-bold text-xl">...</span>
          <div className="flex justify-between w-full">
            <span>{t("graphKey.targetPathway")}</span>
            <Switch size="md" color="#07838F" />
          </div>
        </div>

        <div className="flex items-center justify-between w-full gap-3">
          <span className="text-[#DACB48] font-bold text-xl mb-2">...</span>
          <div className="flex justify-between w-full">
            <span>{t("graphKey.pathwaySBTi")}</span>
            <Switch size="md" color="#07838F" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default GraphKey;
