import React from "react";
import PropType from "prop-types";

const Assessments = ({ assessments, activeTabIndex, setActiveTabIndex }) => {
  // console.log("assessments", assessments);
  return (
    <div className="">
      
      <ul className="flex gap-4 text-[#9C9C9C] relative bg-white p-4 rounded-md">
        {assessments?.map((assessment, index) => (
          <li key={index}>
            <button
              className={`hover:text-[#112b65] transition-all w-36 ${
                activeTabIndex == index ? "text-[#112b65] -translate-y-2" : ""
              }`}
              onClick={() => setActiveTabIndex(index)}
            >
              {index === 0 ? "Latest Assessment" : assessment?.name}
            </button>
          </li>
        ))}
        <span
          className={`absolute h-2 aspect-square rounded-full transition-all ease-out bg-[#112b65] left-0 top-full`}
          style={{
            transform: `translate(calc(${
              160 * activeTabIndex + 72
            }px - 50% + 16px), -20px)`,
          }}
        ></span>
      </ul>
    </div>
  );
};

Assessments.propTypes = {
  assessments: PropType.array,
  activeTabIndex: PropType.number,
  setActiveTabIndex: PropType.func,
};

export default Assessments;
