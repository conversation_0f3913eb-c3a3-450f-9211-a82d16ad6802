import SWOT from "./SWOT";

const SWOTSections = ({ categoryData }) => {
  
  
  return (
    <section>
      {categoryData &&
        categoryData.map((catData, index) => (
          <SWOT
            key={index}
            scopeTitle={catData.category_name}
            statsS={catData.swot_analysis.strengths}
            statsW={catData.swot_analysis.weaknesses}
            statsO={catData.swot_analysis.opportunities}
            statsT={catData.swot_analysis.threats}
          />
        ))}
    </section>
  );
};

export default SWOTSections;
