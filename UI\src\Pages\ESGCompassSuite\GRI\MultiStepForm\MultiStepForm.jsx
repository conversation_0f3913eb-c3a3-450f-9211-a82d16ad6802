import CompanyInfoForm from "./CompanyInfoForm";
import { useAnswersStore } from "@/Store/useAnswersStore";
import { useNavigate } from "react-router-dom";

export default function MultiStepForm() {
  const companyInfo = useAnswersStore((state) => state.companyInfo);
  const setCompanyInfo = useAnswersStore((state) => state.setCompanyInfo);
  const navigate = useNavigate();

  const handleCompanyInfoChange = (e) => {
    setCompanyInfo(e.target.name, e.target.value);
  };

  const nextStep = () => {
    const framework = companyInfo.reportingFramework;
    if (framework === "GRI") {
      navigate("/gri-reporting");
    } else if (framework === "ISSB") {
      navigate("/issb-reporting");
    } else if (framework === "TNFD") {
      navigate("/tnfd-reporting");
    } else {
      console.error("Invalid reporting framework selected");
    }
  };

  return (
    <div className="w-full mx-auto">
      <CompanyInfoForm
        companyInfo={companyInfo}
        onChange={handleCompanyInfoChange}
        onNext={nextStep}
      />
    </div>
  );
}
