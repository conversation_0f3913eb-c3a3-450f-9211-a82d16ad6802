import {
    Box,
    Title,
    Text,
    List,
    Stack,
    Paper,
    Group,
    ThemeIcon,
} from "@mantine/core";
import { FaCheck } from "react-icons/fa";

export default function CarbonSystemUserGuideBatchInput() {
    return (
        <Box mx="auto" maxWidth="1200px" p="md">
            <Title order={1} mb="xl">
                Carbon System User Guide
            </Title>

            <Stack spacing="lg">
                {/* Batch Input Section */}
                <Text size="lg" mb="sm">
                    <b>Batch Input</b>
                </Text>
                <Text size="sm" c="dimmed" mb="sm">
                    Navigation: Emissions Calculation &gt; Collect &gt; Batch
                    Input
                </Text>

                <Text size="lg" mt="lg" mb="sm">
                    <b>Bulk Upload</b>
                </Text>
                <List listStyleType="disc" spacing="sm">
                    <List.Item>Upload files in CSV or XLSX format.</List.Item>
                    <List.Item>
                        Check for missing columns in your file.
                    </List.Item>
                    <List.Item>Use templates for easy formatting.</List.Item>
                </List>

                {/* Optional Tip Banner */}
                <Paper shadow="sm" p="md" radius="md" mt="xl" withBorder>
                    <Group position="apart">
                        <ThemeIcon variant="light" radius="xl" size="2rem">
                            <FaCheck size="1.2rem" />
                        </ThemeIcon>
                        <Text fw={500}>
                            Use the system-provided template to avoid column
                            mismatch errors!
                        </Text>
                    </Group>
                </Paper>
            </Stack>
        </Box>
    );
}
