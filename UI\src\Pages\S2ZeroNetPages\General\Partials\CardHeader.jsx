import { useTranslation } from "react-i18next";
import { <PERSON> } from "react-router-dom";

const CardHeader = ({ cardTitle, viewAllLink }) => {
  const { t } = useTranslation();
  return (
    <div className="flex justify-between px-2 mb-1">
      <h2 className="font-bold text-md">{cardTitle}</h2>
      <Link to={viewAllLink} className="underline text-primary">
        {t("View All")}
      </Link>
    </div>
  );
};

export default CardHeader;
