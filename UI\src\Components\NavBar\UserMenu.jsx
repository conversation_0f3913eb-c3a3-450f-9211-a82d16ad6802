import Notification from "@/assets/svg/Notification";
import Translation from "@/assets/svg/Translation";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next"; // Import useTranslation
import LanguageDropList from "../LanguageDropList";
import { EditProfileIcon } from "@/assets/icons";
import { Link } from "react-router-dom";

function UserMenu({ openMenu, notifications, setNotifications }) {
  const [openLanguageList, setOpenLanguageList] = useState(false);
  const { t, i18n } = useTranslation(); // Use the translation hook
  const [darkMode, setDarkMode] = useState(
    localStorage.levelUpESGTheme === "dark"
  );

  useEffect(() => {
    if (!localStorage.levelUpESGTheme) {
      localStorage.setItem("levelUpESGTheme", darkMode ? "dark" : "light");
    }
    if (darkMode) {
      localStorage.setItem("levelUpESGTheme", "dark");
    } else {
      localStorage.setItem("levelUpESGTheme", "light");
    }
  }, [darkMode]);

  const languageNames = {
    en: "English",
    ar: "العربية",
    fr: "Français",
    de: "Deutch",
    ru: "Русский",
    "zh-Hans": "中文-Hans",
  };

  return (
    <div
      className={`burger-menu-main  ${
        openMenu ? "translate-x-0 opacity-100" : "translate-x-[200%] opacity-0"
      } absolute top-[65px] right-0 text-sm cursor-default duration-1000 flex flex-col items-center`}
    >
      {/* //// Group 1 //// */}
      {openLanguageList && <LanguageDropList />}

      <div className={`burger-menu-group z-10`} style={{ zIndex: 9999999 }}>
        <Link
          to="/Settings"
          className="flex items-center justify-start gap-2 sm:gap-4 col-span-full"
        >
          <EditProfileIcon />
          <p className=" text-secondary-400 whitespace-break-spaces text-start text-xs sm:text-sm ">
            {t("edit_profile_information")}
          </p>
        </Link>
        <div className="flex items-center justify-start gap-3 sm:gap-4">
          <Notification />
          <p className="text-secondary-400 sm:text-nowrap text-start">
            {t("notifications")}
          </p>
        </div>
        <p
          className="text-black cursor-pointer text-end hover:text-secondary-400"
          onClick={() => setNotifications(!notifications)}
        >
          {notifications ? t("on") : t("off")}
        </p>
        <div className="flex items-center justify-start gap-3 sm:gap-4">
          <Translation />
          <p className="text-secondary-400 sm:text-nowrap text-start">
            {t("language")}
          </p>
        </div>
        <div className="relative ms-auto">
          <button
            className="flex items-center gap-1 text-black cursor-pointer translateBtn sm:gap-2 text-end hover:text-secondary-400"
            onClick={() => setOpenLanguageList((prev) => !prev)}
          >
            {languageNames[i18n.language]}
          </button>
        </div>
      </div>
      {/* 
      //// Group 2 ////
      <div className={`burger-menu-group z-20`}>
        <div className="flex items-center justify-start flex-1 gap-3 sm:gap-4">
          <Security />
          <p className="text-secondary-400 sm:text-nowrap text-start">{t('security')}</p>
        </div>
      </div> */}

      {/* //// Group 3 //// */}
      {/* <div
        className={`burger-menu-group ${
          !isReverse ? "animate-burgerGroup" : "animate-burgerGroupReverse"
        }`}
      >
        <div className="flex items-center justify-start gap-3 sm:gap-4">
          <HelpAndSupport />
          <p className="text-secondary-400 sm:text-nowrap text-start">
            {t("help_support")}
          </p>
        </div>
        <p></p>
        <div className="flex items-center justify-start gap-3 sm:gap-4">
          <ContactUs />
          <p className="text-secondary-400 sm:text-nowrap text-start">
            {t("contact_us")}
          </p>
        </div>
        <p></p>
        <div className="flex items-center justify-start gap-3 sm:gap-4">
          <PrivacyPolicy />
          <p className="text-secondary-400 sm:text-nowrap text-start">
            {t("privacy_policy")}
          </p>
        </div>
        <p></p>
      </div> */}
    </div>
  );
}

export default UserMenu;
