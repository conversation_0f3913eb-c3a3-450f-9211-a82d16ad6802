import React from "react";

import { ScrollArea, Table } from "@mantine/core";

const head = [
  "Criteria",
  "Level 1",
  "Level 2",
  "Level 3",
  "Level 4",
  "Level 5",
];

// Major
// High financial loss ($1M-$10M)
// Significant reputational damage, international media coverage
// Significant regulatory breaches, substantial fines
// Significant impact on stakeholder trust
const data = [
  {
    id: "1",
    criteria: "Likelihood",
    level1: [
      "Rare",
      "<10% chance of occurrence",
      "May occur only in exceptional circumstances",
    ],
    level2: [
      "Unlikely",
      "10-30% chance of occurrence",
      "Could occur at some time",
    ],
    level3: [
      "Possible",
      "30-60% chance of occurrence",
      "Might occur at some time",
    ],
    level4: [
      "Likely",
      "60-90% chance of occurrence",
      "Will probably occur in most circumstances",
    ],
    level5: ["Almost Certain",'>90% chance of occurrence','Expected to occur in most circumstances']
  },
  {
    id: "2",
    criteria: "Impact",
    level1: [
      "Insignificant",
      "Minimal financial loss (<$10,000)",
      "No reputational damage",
      "No regulatory implications",
      "No impact on stakeholder trust",
    ],
    level2: [
      "Minor",
      "Low financial loss ($10,000-$100,000)",
      "Limited reputational damage, local media coverage",
      "Minor regulatory breaches",
      "Slight impact on stakeholder trust",
    ],
    level3: [
      "Moderate",
      "Moderate financial loss ($100,000-$1M)",
      "Moderate reputational damage, national media coverage",
      "Moderate regulatory breaches, potential fines",
      "Moderate impact on stakeholder trust",
    ],
    level4: [
      "Major",
      "High financial loss ($1M-$10M)",
      "Significant reputational damage, international media coverage",
      "Significant regulatory breaches, substantial fines",
      "Significant impact on stakeholder trust",
    ],
    level5: ["Catastrophic",'Extreme financial loss (>$10M)','Severe reputational damage, long-term international media coverage','Severe regulatory breaches, potential for legal action or loss of license to operate','Significant impact on stakeholder trust, potential loss of key stakeholders'],
  },
];
// Catastrophic
// Extreme financial loss (>$10M)
// Severe reputational damage, long-term international media coverage
// Severe regulatory breaches, potential for legal action or loss of license to operate
// Significant impact on stakeholder trust, potential loss of key stakeholders
const RiskMatrixTable = () => {
  const rows = data.map((item) => {
    return (
      <Table.Tr key={item.id} className={`risk-matrix-table bg-white text-sm`}>
        <Table.Td className="bg-secondary-gray-100 p-2">
          {item.criteria}
        </Table.Td>
        <Table.Td>
          <ul className="li-circle ml-3">
            {item.level1.map((el, i) => (
              <li key={i}>{el}</li>
            ))}
          </ul>
        </Table.Td>
        <Table.Td>
          <ul className="li-circle ml-3">
            {item.level2.map((el, i) => (
              <li key={i}>{el}</li>
            ))}
          </ul>
        </Table.Td>
        <Table.Td>
          <ul className="li-circle ml-3">
            {item.level3.map((el, i) => (
              <li key={i}>{el}</li>
            ))}
          </ul>
        </Table.Td>
        <Table.Td>
          {" "}
          <ul className="li-circle ml-3">
            {item.level4.map((el, i) => (
              <li key={i}>{el}</li>
            ))}
          </ul>
        </Table.Td>
        <Table.Td>
          {" "}
          <ul className="li-circle ml-3">
            {item.level5.map((el, i) => (
              <li key={i}>{el}</li>
            ))}
          </ul>
        </Table.Td>
      </Table.Tr>
    );
  });

  return (
    <div>
      <ScrollArea>
        <Table withTableBorder withColumnBorders miw={800} verticalSpacing="lg">
          {/* {...rows} */}
          <Table.Thead className="border-b-2 bg-white pb-6 text-base text-center">
            <Table.Tr>
              {head.map((el, i) => (
                <Table.Th key={i} className="bg-secondary-gray-100 p-2">
                  <div className="flex items-center justify-center gap-2">
                    <span className="font-medium">{el}</span>
                  </div>
                </Table.Th>
              ))}
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>{rows}</Table.Tbody>
        </Table>
      </ScrollArea>
    </div>
  );
};

export default RiskMatrixTable;
