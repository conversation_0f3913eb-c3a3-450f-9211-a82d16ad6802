import React, { useEffect } from "react";
import { useDisclosure } from "@mantine/hooks";
import { Modal, Button } from "@mantine/core";
import CreateFullSurvey from "./CreateFullSurvey";
import { useNavigate } from "react-router";

import ApiS3 from "@/Api/apiS3";
export default function FeedbackCollection() {
  const [surveys, setSurveys] = React.useState([]);
  const navigate = useNavigate();

  const getSurveys = async () => {
    try {
      const response = await ApiS3.get("surveys");
      setSurveys(response.data);
      console.log("Surveys:", surveys);
    } catch (error) {
      console.error("Error fetching surveys:", error);
    }
  };

  useEffect(() => {
    getSurveys();
  }, []);
  return (
    <div className="flex flex-col gap-4 p-4 min-h-screen">
      <div className="create-survey flex px-10 w-full justify-end">
        <Button
          variant="default"
          className="rounded-xl bg-[#07838F] hover:bg-[#07848fad] text-white"
          onClick={() => navigate("/creatserve")}
        >
          Create Questionnaire
        </Button>
      </div>

      {surveys.length > 0 &&
        surveys.map((survey) => (
          <div
            key={survey.id}
            className="bg-white p-4 rounded-lg shadow-md mb-4 cursor-pointer"
            onClick={() => {
              navigate(`/surveyDetails/${survey._id}`);
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = `linear-gradient(0deg, rgba(7, 131, 143, 0.1), rgba(7, 131, 143, 0.1))`;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = "#ffffff";
            }}
          >
            <h3 className="text-lg font-semibold">{survey.title}</h3>
            <p className="text-gray-600">{survey.description}</p>
            <p className="text-gray-600">Target: {survey.target}</p>
            <p className="text-gray-600">
              One Time Response: {survey.oneTimeresponse ? "Yes" : "No"}
            </p>
            <p className="text-gray-600">
              Active: {survey.isActive ? "Yes" : "No"}
            </p>
          </div>
        ))}
    </div>
  );
}
