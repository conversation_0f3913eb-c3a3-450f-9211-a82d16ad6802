import { Box, Flex, Group, Tooltip, Tree } from "@mantine/core";
// import classes from './styles.css';
import {
  IconBrandNpm,
  IconDownload,
  IconFile,
  IconFileTypeCss,
  IconFileTypeDoc,
  IconFileTypeDocx,
  IconFileTypePdf,
  IconFileTypePpt,
  IconFileTypeTs,
  IconFileTypeTxt,
  IconFileTypeZip,
  IconFolder,
  IconFolderFilled,
  IconFolderOpen,
} from "@tabler/icons-react";
import { useEffect, useState } from "react";
import axios from "axios";
import Cookies from "js-cookie";
import { Spinner } from "@react-pdf-viewer/core";
import { useDocvaultStore } from "@/Store/useDocvaultStore";

const ResultTable = () => {
  const { apiFiles, files, setFiles ,filteredFiles } = useDocvaultStore((state) => state);

  useEffect(() => {
    let filesNames = [];

    let builttreedata = apiFiles
      ?.filter((item) => {
        if (filesNames.includes(item.name)) {
          return false; // Skip duplicates
        }
        filesNames.push(item.name);
        return true; // Include unique items
      })
      .map((item) => ({
        label: item.name,
        value: item.name,
        type: item.type,
        mimetype: item.mimetype,
        size: item.size,
        created_at: item.created_at,
        updated_at: item.updated_at,
        id: item.id,
        childrenLoaded: false,
        children: item.type === "folder" ? [] : undefined,
      }));
    setFiles(builttreedata);
  }, [apiFiles, setFiles]);

  const loadChildren = async (node) => {
    if (node.type !== "folder") return [];
    try {
      const response = await getFolderData(node.id);
      const children = response.map((child) => ({
        label: child.name,
        value: `${node.value}/${child.name}`, // Use the full path as the value
        type: child.type,
        childrenLoaded: false,
        mimetype: child.mimetype,
        size: child.size,
        created_at: child.created_at,
        updated_at: child.updated_at,
        id: child.id,
        children: child.type === "folder" ? [] : undefined,
      }));

      // Mark the folder as loaded
      const updatedTree = updateTreeNode(files, node.value, children, true);
      setFiles(updatedTree);

      return children;
    } catch (error) {
      console.error("Error loading children:", error);
      return [];
    }
  };

  // Update the `updateTreeNode` function to set `loaded`
  const updateTreeNode = (tree, nodeId, newChildren, markAsLoaded) => {
    return tree.map((node) => {
      if (node.value === nodeId) {
        return {
          ...node,
          children: newChildren,
          childrenLoaded: markAsLoaded, // Mark as loaded
        };
      }
      ``;

      if (node.children) {
        return {
          ...node,
          children: updateTreeNode(
            node.children,
            nodeId,
            newChildren,
            markAsLoaded
          ),
        };
      }

      return node;
    });
  };

  const getFolderData = async (folder_id) => {
    try {
      let res = await axios.get(
        "https://docvault-staging.azurewebsites.net/api/v1/folders/explore?folder_id=" +
          folder_id,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("level_user_token")}`,
          },
        }
      );
      const { data } = res.data;

      return data;
    } catch (e) {
      console.error("error", e);
    }
  };
  return (
    <>
      <Tree
        data={filteredFiles.length > 0 ? filteredFiles : files}
        onLoad={loadChildren}
        styles={(theme) => ({
          control: {
            padding: theme.spacing.xs,
            borderRadius: theme.radius.md,
          },
        })}
        renderNode={(leafProps) => (
          <Leaf
            className="p-4 hover:bg-gray-200 transition-colors duration-200"
            {...leafProps}
            onNodeClick={async (node) => {
              const children = await loadChildren(node);
              const updatedTree = updateTreeNode(files, node.value, children);
              setFiles(updatedTree);
            }}
          />
        )}
      />
    </>
  );
};

export default ResultTable;

function Leaf({ node, expanded, elementProps, onNodeClick }) {
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingDownload, setIsLoadingDownload] = useState(false);
  const isEmpty = node.type === "folder" && node.children?.length === 0;

  // Handle folder click
  let isFolder = node.type === "folder";
  useEffect(() => {
    loadChildren().then((children) => {
      if (children && children.length > 0) {
        setIsLoading(false);
      }
    });
  }, []);

  const loadChildren = async () => {
    setIsLoading(true);
    await onNodeClick(node);
    setIsLoading(false);
  };

  const handleDownload = async () => {
    setIsLoadingDownload(true);
    try {
      const response = await axios.get(
        `https://docvault-staging.azurewebsites.net/api/v1/files/${node.id}/download`,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("level_user_token")}`,
            "Content-Type": node.mimetype,
            Accept: "application/octet-stream",
            withCredentials: true,
            connection: "keep-alive",
            "Cache-Control": "no-cache",
          },
          responseType: "blob",
        }
      );

      const blobUrl = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = blobUrl;
      link.setAttribute("download", node.label || "file");
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(blobUrl);
    } catch (error) {
      console.error("Download failed", error);
    } finally {
      setIsLoadingDownload(false);
    }
  };

  return (
    <>
      {isFolder ? (
        <Flex
          gap={5}
          {...elementProps}
          style={{ cursor: "pointer" }}
          className={`${elementProps.className} hover:bg-gray-200 transition-colors duration-200`}
        >
          {isLoading ? (
            <Spinner size="24px" />
          ) : (
            <FileIcon
              name={node.label}
              isFolder={true}
              expanded={expanded}
              isEmpty={isEmpty}
              onClick={loadChildren}
            />
          )}
          <span>{node.label}</span>
        </Flex>
      ) : (
        <Flex
          style={{ cursor: "default" }}
          justify="space-between"
          {...elementProps}
          className={`${elementProps.className} hover:bg-gray-200 transition-colors duration-200`}
        >
          <Flex gap={5}>
            <FileIcon
              name={node.label}
              isFolder={false}
              expanded={expanded}
              isEmpty={isEmpty}
            />
            <Tooltip label={node.label}>
              <span
                className="
                whitespace-nowrap 
                overflow-hidden 
                text-ellipsis 
                max-w-[150px]
                sm:max-w-[200px] 
                md:max-w-[300px] 
                lg:max-w-[400px]
              "
              >
                {node.label}
              </span>
            </Tooltip>
          </Flex>
          <Flex gap={24} align="center">
            <Tooltip label="Size">
              <span className="hidden sm:hidden md:block lg:block">
                {convertbitsize(node.size)}
              </span>
            </Tooltip>
            <Tooltip label={"last Updated"}>
              <span className="hidden sm:hidden md:block lg:block">
                {new Date(node.updated_at).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "2-digit",
                  day: "2-digit",
                })}
              </span>
            </Tooltip>
            <Tooltip label={"Created At"}>
              <span className="hidden sm:hidden md:block lg:block">
                {new Date(node.created_at).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "2-digit",
                  day: "2-digit",
                })}
              </span>
            </Tooltip>
            {isLoadingDownload ? (
              <Spinner size="24px" />
            ) : (
              <IconDownload
                stroke={2}
                color="black"
                size={24}
                className="cursor-pointer stroke-emerald-600 hover:bg-gray-200 transition-colors duration-200"
                onClick={handleDownload}
              />
            )}
          </Flex>
        </Flex>
      )}
    </>
  );
}

function FileIcon({ name, isFolder, expanded, isEmpty }) {
  if (name.endsWith("package.json")) {
    return <IconBrandNpm stroke={2} color="red" size={32} />;
  }

  if (
    name.endsWith(".ts") ||
    name.endsWith(".tsx") ||
    name.endsWith("tsconfig.json")
  ) {
    return <IconFileTypeTs stroke={2} color="#07838f" size={32} />;
  }

  if (name.endsWith(".css")) {
    return <IconFileTypeCss stroke={2} color="#07838f" size={32} />;
  }
  if (name.endsWith(".pdf")) {
    return <IconFileTypePdf stroke={2} color="#f70000" size={32} />;
  }
  if (name.endsWith(".docx")) {
    return <IconFileTypeDocx stroke={2} color="#6ab937" size={32} />;
  }
  if (name.endsWith(".doc")) {
    return <IconFileTypeDoc stroke={2} color="#6ab937" size={32} />;
  }
  if (name.endsWith(".zip")) {
    return <IconFileTypeZip stroke={2} color="#07838f" size={32} />;
  }
  if (name.endsWith(".txt")) {
    return <IconFileTypeTxt stroke={2} color="#07838f" size={32} />;
  }
  if (name.endsWith(".ppt")) {
    return <IconFileTypePpt stroke={2} color="#d55736" size={32} />;
  }

  if (isFolder) {
    if (isEmpty) return <IconFolder stroke={2.5} size={32} />;
    return expanded ? (
      <IconFolderOpen color="#07838f" stroke={2.5} size={32} />
    ) : (
      <IconFolderFilled color="#07838f" stroke={5} size={32} />
    );
  }

  return <IconFile stroke={2.5} color="#07838f" size={32} />;
}

function convertbitsize(size) {
  var i = Math.floor(Math.log(size) / Math.log(1024));
  return (size / Math.pow(1024, i)).toFixed(2) * 1 + " " + ["B", "kB", "MB"][i];
}