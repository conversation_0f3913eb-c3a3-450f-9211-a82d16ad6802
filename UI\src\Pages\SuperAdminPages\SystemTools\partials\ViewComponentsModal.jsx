import { useEffect, useState, useCallback } from "react";
import ApiProfile from "@/Api/apiProfileConfig";
import Loading from "@/Components/Loading";
import { Button, Modal, ScrollArea, Table } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { showNotification } from "@mantine/notifications";
import { useDisclosure } from "@mantine/hooks";

const ViewComponentsModal = ({
  close,
  opened,
  groupId,
  groupName,
  refreshGroups
}) => {
  const { t } = useTranslation();
  const [components, setComponents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [confirmDeleteOpened, { open: openConfirmDelete, close: closeConfirmDelete }] = useDisclosure(false);
  const [componentToDelete, setComponentToDelete] = useState(null);

  const fetchComponents = useCallback(async () => {
    try {
      setLoading(true);
      // We can use the system-components endpoint to get all groups and filter for the one we need
      const { data } = await ApiProfile.get("https://portal-auth-main.azurewebsites.net/admin/system-components");
      
      // Find the specific group components from the grouped_components object
      if (data && data.grouped_components) {
        const groupData = Object.entries(data.grouped_components)
          .find(([name, group]) => group.componentGroupId === groupId);
        
        if (groupData) {
          // Transform the components data
          const groupComponents = groupData[1].components.map(comp => ({
            id: comp.systemComponentId,
            name: comp.componentName,
            enabled: comp.enabled,
            level: comp.level
          }));
          
          setComponents(groupComponents);
        } else {
          setComponents([]);
        }
      } else {
        setComponents([]);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      showNotification({
        message: error.response?.data?.message || "Failed to fetch components",
        color: "red",
      });
      console.error(error);
    }
  }, [groupId]);

  useEffect(() => {
    if (opened && groupId) {
      fetchComponents();
    }
  }, [opened, groupId, fetchComponents]);

  const handleDeleteButtonClick = (componentId, componentName) => {
    setComponentToDelete({ id: componentId, name: componentName });
    openConfirmDelete();
  };

  const confirmDeleteComponent = async () => {
    try {
      setLoading(true);
      
      // Perform the component deletion directly here using the correct endpoint and header
      await ApiProfile.delete("https://portal-auth-main.azurewebsites.net/admin/delete-system-component", {
        headers: {
          'systemComponentId': componentToDelete.id
        }
      });
      
      showNotification({
        message: `Component "${componentToDelete.name}" deleted successfully`,
        color: "green",
      });
      
      // Close the confirmation modal
      closeConfirmDelete();
      
      // Refresh the components list
      fetchComponents();
      
      // Also refresh the groups in the parent component to update counts
      if (refreshGroups) refreshGroups();
      
      setLoading(false);
    } catch (error) {
      setLoading(false);
      showNotification({
        message: error.response?.data?.message || "Failed to delete component",
        color: "red",
      });
      console.error(error);
    }
  };

  // Function to determine if a component has children (sub-components)
  const hasChildren = (component) => {
    return component.children && component.children.length > 0;
  };

  // Function to render component rows, including nested components if any
  const renderComponentRows = (componentsArray, level = 0) => {
    return componentsArray.map((component) => (
      <>
        <Table.Tr key={component.id}>
          <Table.Td>
            {/* Add indentation for nested components based on level */}
            <div className={`flex items-center ${level > 0 ? 'pl-4' : ''}`}>
              {level > 0 && <span className="mr-2">└─</span>}
              {component.name}
            </div>
          </Table.Td>
          <Table.Td className="flex justify-center gap-3">
            <Button 
              className="bg-red-700 hover:bg-red-700" 
              size="xs"
              onClick={() => handleDeleteButtonClick(component.id, component.name)}
            >
              Delete Component
            </Button>
          </Table.Td>
        </Table.Tr>
        {/* Render child components if any */}
        {hasChildren(component) && renderComponentRows(component.children, level + 1)}
      </>
    ));
  };

  return (
    <>
      <Modal 
        opened={opened} 
        onClose={close} 
        title={`Components in ${groupName}`}
        size="lg"
        centered
      >
        {loading ? (
          <Loading />
        ) : (
          <ScrollArea style={{ height: 400 }}>
            <Table miw={600} verticalSpacing="sm" striped highlightOnHover>
              <Table.Thead className="pb-6 text-base font-bold text-center border-b-2 border-primary text-primary">
                <Table.Tr>
                  <Table.Th className="text-start">{t("Component Name")}</Table.Th>
                  <Table.Th className="text-center">{t("Action")}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody className="text-center mt-2">
                {components.length > 0 ? (
                  renderComponentRows(components)
                ) : (
                  <Table.Tr>
                    <Table.Td colSpan={2} className="text-center py-4">
                      No components found in this group
                    </Table.Td>
                  </Table.Tr>
                )}
              </Table.Tbody>
            </Table>
          </ScrollArea>
        )}
      </Modal>

      {/* Confirmation Modal */}
      <Modal
        opened={confirmDeleteOpened}
        onClose={closeConfirmDelete}
        title="Confirm Delete Component"
        centered
      >
        <p>Are you sure you want to delete the component &quot;{componentToDelete?.name}&quot;?</p>
        <div className="flex justify-end gap-3 mt-4">
          <Button onClick={closeConfirmDelete} variant="outline">
            Cancel
          </Button>
          <Button 
            className="bg-red-700 hover:bg-red-700"
            onClick={confirmDeleteComponent}
            loading={loading}
          >
            Yes, Delete
          </Button>
        </div>
      </Modal>
    </>
  );
};

export default ViewComponentsModal;