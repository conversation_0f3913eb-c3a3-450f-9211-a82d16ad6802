import TopRiskTable from "./TopRiskTable"
import RiskByCateg from "./charts/RiskByCateg"
import ControlPerformance from "./charts/ControlPerformance";
import OpenIssues from "./charts/OpenIssues";

const AnalyticsPage = () => {

  return (
    <>

    {/* top 10 risk table */}
    <TopRiskTable />
    {/* top 10 risk chart */}
    <RiskByCateg />

    <div className="grid lg:grid-cols-2 gap-4  pb-20">
      <ControlPerformance />
      <OpenIssues />
    </div>

    <br />
    <br />
      
    </>
  )
}

export default AnalyticsPage
