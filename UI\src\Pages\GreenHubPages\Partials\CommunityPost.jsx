import { Button, Input } from '@mantine/core';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ApiS3 from '@/Api/apiS3';
// import { FaCircleChevronDown, FaCircleChevronUp } from 'react-icons/fa6';
import { toast } from 'react-toastify';
import Loading from '@/Components/Loading';
import QuestionStatus from '@/Components/Community/QuestionStatus';
import Answer from '@/Components/Community/Answer';
import { FiPaperclip } from 'react-icons/fi';
import Modal from '@/Components/Modal';
import UploadFile from '@/Components/Community/UploadFile';
import Files from '@/Components/Community/Files';
import { useAuth } from '@/Contexts/AuthContext';
import Poll from '@/Components/Community/Poll';

const colors = {
  Sustainably: '#feeaa2',
  Governance: '#d58080',
  Environment: '#93e396',
  Data: '#7fdfd4',
  Society: '#f9bfa6',
  General: '#93c5f6',
};

export default function CommunityPost({ question }) {
  const { t } = useTranslation();
  const [answerInputs, setAnswerInputs] = useState({}); // Tracks answers for each question
  const [answers, setAnswers] = useState({}); // Stores answers for each question
  const [votes, setVotes] = useState({}); // Stores answers for each question
  const [showAnswers, setShowAnswers] = useState({}); // Controls visibility of answers
  const [loading, setLoading] = useState({});
  const [replyLoading, setReplyLoading] = useState(false);
  const [files, setFiles] = useState([]);
  const { user } = useAuth();

  const replayRef = useRef();
  // Fetch questions on component load

  // Handle voting
  const handleVote = async (id, type) => {
    try {
      const url = `/community/questions/${id}/upVote`;
      await ApiS3.post(url);

      getVotes(id);
    } catch (error) {
      console.error(`Error in ${type} vote:`, error.message);
    }
  };

  // Handle showing the answer input
  const handleShowAnswerInput = (id) => {
    setAnswerInputs((prev) => ({
      ...prev,
      [id]: { show: true, value: '' },
    }));
  };

  // Handle hiding the answer input
  const handleHideAnswerInput = (id) => {
    setAnswerInputs((prev) => ({
      ...prev,
      [id]: { ...prev[id], show: false },
    }));
  };

  // Handle showing answers
  const handleShowAnswers = (id) => {
    setShowAnswers((prev) => ({
      ...prev,
      [id]: { show: true },
    }));
  };

  // Handle hiding answers
  const handleHideAnswers = (id) => {
    setShowAnswers((prev) => ({
      ...prev,
      [id]: { show: false },
    }));
  };

  // Fetch answers for a question
  const getAnswers = useCallback(async (id) => {
    try {
      setLoading((prev) => ({
        ...prev,
        [id]: true,
      }));
      const response = await ApiS3.get(`/community/questions/answer/${id}`);

      setAnswers((prev) => ({
        ...prev,
        [id]: [...response.data] || [],
      }));
    } catch (error) {
      console.error('Error fetching answers:', error.message);
    } finally {
      setLoading((prev) => ({
        ...prev,
        [id]: false,
      }));
    }
  }, []);
  const getVotes = useCallback(async (id) => {
    try {
      setLoading((prev) => ({
        ...prev,
        [id]: true,
      }));
      const response = await ApiS3.get(`/community/questions/${id}/votes`);

      setVotes((prev) => ({
        ...prev,
        [id]: { ...response.data } || {},
      }));
    } catch (error) {
      console.error('Error fetching answers:', error.message);
    } finally {
      setLoading((prev) => ({
        ...prev,
        [id]: false,
      }));
    }
  }, []);

  // Handle answer input change
  const handleAnswerChange = (id, value) => {
    setAnswerInputs((prev) => ({
      ...prev,
      [id]: { ...prev[id], content: value },
    }));
  };

  // Handle adding the answer
  const handleAddAnswer = async (id) => {
    try {
      setReplyLoading(true);
      const answer = answerInputs[id]?.content;
      const formData = new FormData();
      formData.append('content', answer);
      formData.append('tags', JSON.stringify(question.tags));

      files.forEach((file) => {
        formData.append('attachments', file);
      });

      if (!answer) return;

      const response = await ApiS3.post(`/community/questions/${id}/answer`, formData);

      if (response.status === 201) {
        toast.success('Answer Added Successfully!');
      }
      //console.log("Answer added:", response.data);
      getAnswers(id);
      handleShowAnswers(id);
      handleHideAnswerInput(id); // Hide input after answering
    } catch (error) {
      toast.error('Please try again later!');
      console.error('Error adding answer:', error.message);
    } finally {
      setFiles([]);
      setAnswerInputs((prev) => ({
        ...prev,
        [id]: { ...prev[id], content: '' },
      }));
      setReplyLoading(false);
    }
  };

  useEffect(() => {
    if (!question.attachments) return;
    question.attachments = question.attachments.filter((e) => e !== null).filter((e) => !e.includes('[object File]'));
    getAnswers(question._id);
    getVotes(question._id);
    handleShowAnswers(question._id);
  }, [question, getAnswers, getVotes]);

  // Helper function to display time ago
  const timeAgo = (postTimestamp) => {
    const now = new Date();
    const postDate = new Date(postTimestamp);
    const timeDiff = now - postDate;

    const seconds = Math.floor(timeDiff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''} ago`;
    }
    if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    }
    if (minutes > 0) {
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    }
    if (seconds > 0) {
      return `${seconds} second${seconds > 1 ? 's' : ''} ago`;
    } else {
      return 'Now';
    }
  };

  return (
    <div>
      <div className="flex flex-row gap-4 items-start">
        {/* <div className="flex flex-col items-center gap-4">
          <button
            className="p-3 text-xl rounded-full bg-secondary-400 bg-opacity-10 text-secondary-400"
            onClick={() => handleVote(question._id, 'Up')}
          >
            <FaCircleChevronUp />
          </button>
          <span className="text-base font-medium text-green-500">{question?.upvotes.length}</span>
          <button
            className="p-3 text-xl rounded-full bg-secondary-400 bg-opacity-10 text-secondary-400"
            onClick={() => handleVote(question._id, 'Down')}
          >
            <FaCircleChevronDown />
          </button>
        </div> */}

        <div className="w-full bg-white rounded-lg shadow-md overflow-hidden px-2">
          <div className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex items-center justify-start gap-2 w-full">
                {question.user?.image ? (
                  <img src={question?.user?.image} alt={question?.user?.userName || 'User'} className="w-10 h-10 rounded-full" />
                ) : (
                  <div className="w-10 h-10 rounded-full cursor-default text-center flex items-center justify-center font-bold text-sm text-primary bg-[#07838F1A]">
                    {(question?.user?.userName && question?.user?.userName[0]) || 'U'}
                  </div>
                )}
                <div className=" w-full flex items-start flex-col">
                  <div className="flex items-center justify-between w-full">
                    <h3 className="text-sm font-sans font-semibold md:text-base text-[#171A1F] pt-0 mt-0">{t(question?.user?.userName || 'user')}</h3>
                    <div className="text-[10px] md:text-sm font-normal text-[#9C9C9C] ">{timeAgo(question?.createdAt)}</div>
                  </div>
                  <p className="text-[10px] md:text-sm font-normal text-[#565D6D] ">{t('@ ' + (question?.user?.company_name || 'unknown'))}</p>
                </div>
              </div>
            </div>
            <div className="flex my-4 space-x-2">
              {question.tags &&
                question?.tags?.map((tag, i) => (
                  <span key={i} className="px-2 py-1 text-xs rounded text-black" style={{ backgroundColor: colors[tag] || '#e2e8f0' }}>
                    {tag}
                  </span>
                ))}
            </div>
            <p className="mt-4 mb-2 text-black text-lg font-medium">{t(question?.question)}</p>
            {question.typeOfQuestion === 'poll' && question.options.length > 0 && <Poll options={question.options} questionId={question._id} />}
            {question.attachments && question.attachments.length > 0 && (
              <div className="bg-secondary-400 bg-opacity-10 rounded p-3">
                <p className="flex items-center gap-2 font-normal text-lg mb-2">
                  <FiPaperclip /> Attachments
                </p>
                <Files attachments={question.attachments} />
              </div>
            )}

            <div className="flex items-center justify-between mt-4 text-sm text-gray-500">
              <div className="flex space-x-4">
                {/* <div className="text-[#3ABAB4] hover:text-primary duration-300 font-semibold cursor-pointer">{t('CommunityPost.save')}</div> */}

                <div
                  className="text-[#3ABAB4] hover:text-primary duration-300 font-semibold cursor-pointer"
                  onClick={() => (showAnswers[question._id]?.show ? handleHideAnswers(question._id) : handleShowAnswers(question._id))}
                >
                  {showAnswers[question._id]?.show ? (answers[question._id]?.length > 0 ? t('Hide Answers') : t('Answers')) : t('Answers')}
                </div>

                <div
                  className="text-[#3ABAB4] hover:text-primary duration-300 font-semibold cursor-pointer"
                  onClick={() => (answerInputs[question._id]?.show ? handleHideAnswerInput(question._id) : handleShowAnswerInput(question._id))}
                >
                  {answerInputs[question._id]?.show ? t('Hide') : t('reply')}
                </div>
                <div
                  className="text-[#3ABAB4] hover:text-primary duration-300 font-semibold cursor-pointer"
                  onClick={() => handleVote(question._id, 'Up')}
                >
                  {votes[question._id]?.votes?.includes(user?.userId.toString()) ? t('UnLike') : t('Like')}
                </div>
              </div>
              {/*  question status */}
              <QuestionStatus votes={votes[question._id]} answers={answers[question._id]} />
            </div>
            {answerInputs[question._id]?.show && (
              <div className="flex items-center mt-4 gap-2">
                <div className="flex items-center gap-2 w-full">
                  <Input
                    placeholder={t('Write your answer')}
                    value={answerInputs[question._id]?.content || ''}
                    onChange={(e) => handleAnswerChange(question._id, e.target.value)}
                    className="flex-grow w-full"
                  />
                  <button
                    className="bg-transparent text-primary px-1 border border-transparent duration-300 hover:border-primary py-2 rounded flex items-center w-full flex-1"
                    onClick={() => replayRef.current.open()}
                  >
                    <FiPaperclip />
                  </button>
                </div>
                <Modal title="More Options" ref={replayRef}>
                  <div className="px-5 py-4 bg-white rounded-lg w-full shadow-[0px_0px_8.91px_0px_#0000001A] my-2">
                    <h3 className="text-base font-bold">{t('tags')}</h3>

                    <div className="my-5 w-full">
                      {question?.tags.map((tag, i) => (
                        <span key={i} className="px-2 py-1 text-xs rounded text-black" style={{ backgroundColor: colors[tag] || '#e2e8f0' }}>
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div className="px-5 py-4 bg-white rounded-lg w-full shadow-[0px_0px_8.91px_0px_#0000001A]">
                    <h3 className="text-base font-bold">{t('Attachment')}</h3>
                    <p className="mb-4 text-sm font-medium">{t('Add an attachment to describe your question.')}</p>
                    <UploadFile onFilesChange={setFiles} files={files} />
                  </div>
                  <div className="flex items-center justify-end">
                    <button
                      onClick={() => replayRef.current.close()}
                      className={`text-white ${
                        replyLoading ? 'bg-secondary-100' : 'bg-secondary-300 hover:bg-primary'
                      }  duration-300 px-4 rounded mt-2 `}
                    >
                      Finish
                    </button>
                  </div>
                </Modal>
                <Button
                  variant="filled"
                  onClick={() => handleAddAnswer(question._id)}
                  className={`text-white ${replyLoading ? 'bg-secondary-100' : 'bg-secondary-300 hover:bg-primary'}  duration-300 px-4`}
                  disabled={replyLoading}
                >
                  {replyLoading ? 'Adding...' : t('Add')}
                </Button>
              </div>
            )}
          </div>

          {showAnswers[question._id]?.show && answers[question._id]?.length > 0 && (
            <div className="answers mt-2 border-t border-gray-200 p-2 ">
              {loading[question._id] ? (
                <Loading key={question._id} />
              ) : answers[question._id]?.length > 0 ? (
                answers[question._id]?.map((answer) => {
                  return <Answer key={answer._id} Answer={answer} timeAgo={timeAgo} colors={colors} />;
                })
              ) : // <p className="text-center text-primary">There is No Answers yet!</p>
              null}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
