// Section.jsx - Section component with animations and UI
import { useState, useRef, useEffect } from "react";
import { useAssessmentStore } from "../AssessmentContext";
import Criterion from "./Criterion";
import { GoCheckCircle, GoCheckCircleFill } from "react-icons/go";
import { IoIosArrowDown } from "react-icons/io";

const Section = ({ section }) => {
    const [isOpen, setIsOpen] = useState(false);
    const [height, setHeight] = useState(0);
    const sectionRef = useRef(null);
    const { assessment } = useAssessmentStore();

    // Get the current section's completion status
    const isSectionCompleted = section.is_completed;

    // Update section height when opening/closing
    useEffect(() => {
        if (sectionRef.current) {
            setHeight(isOpen ? sectionRef.current.scrollHeight : 0);
        }
    }, [isOpen]);

    const toggleSection = () => {
        setIsOpen(!isOpen);
    };

    return (
        <div className="mb-4">
            {/* Section header with animation and icons */}
            <div
                className="bg-[rgba(7,131,143,0.1)] cursor-pointer p-2 rounded-lg flex justify-between items-center font-semibold"
                onClick={toggleSection}
            >
                <div className="flex gap-4 items-center">
                    <span className="text-gray-400 font-semibold">
                        {isSectionCompleted ? (
                            <GoCheckCircleFill size={20} />
                        ) : (
                            <GoCheckCircle size={20} />
                        )}
                    </span>
                    <span className="select-none">
                        SECTION {section.index}: {section.title}
                    </span>
                </div>

                {/* Animated arrow icon */}
                <span
                    className={`transition-transform duration-300 ${
                        isOpen ? "rotate-180" : "rotate-0"
                    }`}
                >
                    <IoIosArrowDown />
                </span>
            </div>

            {/* Animated content container */}
            <div
                style={{ height: `${height}px` }}
                className="transition-all duration-300 overflow-hidden"
            >
                <div
                    ref={sectionRef}
                    className="flex flex-col gap-4 p-6 divide-gray-200 divide-y"
                >
                    {section.topics.map((topic, topicIndex) => (
                        <div key={topic.id} className="pt-6">
                            <div className="flex justify-start items-center gap-4 border-b-2 px-2 border-[#E8E7EA] pb-1 w-fit">
                                <h3 className="text-xl font-semibold text-[#07838F]">
                                    {section.index}.{topic.index} {topic.title}
                                </h3>
                                <span className="bg-[#D1FAE5] text-[#065F46] text-xs rounded-full px-2 py-0.5">
                                    Target score: {topic.target}
                                </span>
                            </div>

                            <div className="flex flex-col gap-4 p-6 overflow-x-auto">
                                <table className="w-[1708px]">
                                    <thead className="bg-[#F5F4F5]">
                                        <tr>
                                            <th className="text-start px-4 py-3">
                                                Assessment Criteria
                                            </th>
                                            <th className="text-start px-4 py-3">
                                                Assessment Level
                                            </th>
                                            <th className="text-start px-4 py-3">
                                                Evidence
                                            </th>
                                            <th className="text-start px-4 py-3">
                                                Action Required
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="w-full">
                                        {topic.criteria.map((criterion) => (
                                            <Criterion
                                                key={criterion.id}
                                                criterion={criterion}
                                                sectionIndex={section.index}
                                                topicIndex={topicIndex + 1}
                                            />
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default Section;
