import { Pagination, ScrollArea, Table } from "@mantine/core";
import { useMemo, useState } from "react";

import { useTranslation } from "react-i18next";
const CompanyAdminLogsTable = ({ data }) => {
  const { t } = useTranslation();

  const [currentPage, setCurrentPage] = useState(1);
  const rowsPerPage = 10;
  const totalPages = Math.ceil(data?.length / rowsPerPage);
  const currentData = useMemo(() => {
    return (
      (data &&
        data?.slice(
          (currentPage - 1) * rowsPerPage,
          currentPage * rowsPerPage
        )) ||
      []
    );
  }, [data, currentPage, rowsPerPage]);
  const rows = currentData?.map((item,idx) => {
    // const [hours, minutes] = item.time.split(":")
    const utcDate = new Date(`${item.createdAt}z`);
    const options = {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    };
    const localTime = utcDate.toLocaleTimeString(undefined,options);

    return (
      <Table.Tr
        key={idx} // Use the unique ID as the key
      // className={`${cx({
      //   ["bg-[#07838F1A]"]: selected,
      // })} text-sm font-bold text-[#626364] text-center`}
      >
        <Table.Td className="">
          <div className="flex items-center justify-center text-nowrap">
            {item.year}-{item.month}-{item.day}
          </div>
        </Table.Td>
        <Table.Td className="">
          <div className="flex items-center justify-center text-nowrap">
            {localTime}
          </div>
        </Table.Td>
        <Table.Td className="">
          <div className="flex items-center justify-start">
            {item.actionMessage.replace(/^[\d\-:\+\s]+/, "")}
          </div>
        </Table.Td>
        <Table.Td className="">
          <div className="flex items-center justify-start text-nowrap">
            {item.actionType}
          </div>
        </Table.Td>
      </Table.Tr>
    );
  });

  return (
    <div className="my-8">
      <ScrollArea>
        <Table miw={800} verticalSpacing="sm">
          <Table.Thead className="pb-6 text-base font-bold text-center border-b-2 border-primary text-primary">
            <Table.Tr>
              <Table.Th className="text-center">
                {t("Date")} {/* Translated table header */}
              </Table.Th>
              <Table.Th className="text-center">
                {t("Time")} {/* Translated table header */}
              </Table.Th>
              <Table.Th className="text-start">
                {t("Logs Action")} {/* Translated table header */}
              </Table.Th>
              <Table.Th className="text-start">
                {t("Action Type")} {/* Translated table header */}
              </Table.Th>

              {/* <Table.Th className="text-center">
                {t("Action")} 
              </Table.Th> */}
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>{rows}</Table.Tbody>
        </Table>
      </ScrollArea>
      <div className="md:flex justify-between mt-5">
        <p
          className="text-sm text-secondary-300"
        //   hidden={data?.length}
        >
          {t("showingData", {
            start:
              (currentPage - 1) * rowsPerPage + 1,
            end: Math.min(
              currentPage * rowsPerPage,
              data?.length
            ),
            total: data?.length,
          })}
        </p>
        <Pagination
          page={currentPage}
          onChange={(e) => {
            setCurrentPage(e);
          }}
          total={totalPages}
          color="#00c0a9"
        />
      </div>
    </div>
  );
};

export default CompanyAdminLogsTable;
