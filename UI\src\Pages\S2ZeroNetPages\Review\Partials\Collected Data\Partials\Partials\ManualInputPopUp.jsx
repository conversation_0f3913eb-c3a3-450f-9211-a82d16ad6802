import { Button, Modal, NumberInput, Select, TextInput } from "@mantine/core";
import React, { useState } from "react";
import { IoIosArrowDown } from "react-icons/io";

export default function ManualInputPopUp({
 opened,
 close,
 PopUpInputs,
 handleSaveAdditionalPayload,
 t,
 handleInputChange,
 title,
 item,
 inputValues,
 customFactors = [],
 assignDataBasedOnActivity,
 handleModalChange,
 dataByIDActivity,
}) {
 const myListQuantity =
  item == "quantityKeys" && customFactors[0]?.quantityKeys;
 const myObjQuantity =
  item == "quantityKeys" &&
  myListQuantity?.reduce((obj, key) => {
   obj[key] = null;
   return obj;
  }, {});

 const [quantity, setquantity] = useState(myObjQuantity);

 return (
  <Modal
   opened={opened}
   onClose={close}
   title={title}
   size={"70rem"}
   centered
   className=""
  >
   {!inputValues?.[item] && PopUpInputs && (
    <>
     <p className="font-bold">
      Current <span className="capitalize">{item}:</span>
     </p>
     {item === "quantityKeys" && (
      <div className="flex gap-5">
       {Object.entries(PopUpInputs || {}).map(([key, value], i) => (
        <p key={key}>
         {key}:{value}
        </p>
       ))}
      </div>
     )}
    </>
   )}
   <p className="mt-3 font-bold">
    New Values in <span className="capitalize">{item}</span>
   </p>
   <>
    {item === "quantityKeys" && (
     <div className="flex gap-5">
      {customFactors.length ? (
       Object.entries(quantity || {}).map(([key, value], i) => (
        <NumberInput
         key={key}
         label={key}
         placeholder="Enter Number..."
         value={inputValues?.[item]?.[key] || ""}
         onChange={(e) => handleInputChange(key, e, item)}
         radius={10}
         rightSection={" "}
         size="md"
        />
       ))
      ) : (
       <span className="mt-5 text-sm">
        There is no {item} please select Activity First
       </span>
      )}
     </div>
    )}
   </>

   <Button
    disabled={!inputValues?.[item]}
    className={
     !inputValues?.[item]
      ? "bg-gray-500 mt-4 cursor-not-allowed"
      : "mt-4 bg-primary hover:bg-primary"
    }
    onClick={() => {
     handleSaveAdditionalPayload(item);
    }}
    radius={10}
    size="md"
   >
    {t("save")}
   </Button>
  </Modal>
 );
}
