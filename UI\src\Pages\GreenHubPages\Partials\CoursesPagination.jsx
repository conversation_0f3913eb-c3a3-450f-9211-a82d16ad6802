import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";
import { usePagination } from "./usePagination";
import ArrowFront from "../../../assets/svg/ArrowFront.jsx";
import ArrowBack from "../../../assets/svg/ArrowBack.jsx";

CoursesPagination.propTypes = {
  numOfResourses: PropTypes.number,
  numOfPageElements: PropTypes.number,
  page: PropTypes.number,
  loading: PropTypes.bool,
  setPage: PropTypes.func,
};

function CoursesPagination({
  numOfResourses = 6,
  numOfPageElements = 6,
  setPage = () => {},
  loading = false,
  page = 1,
}) {
  const { t } = useTranslation();

  const {
    handleChangePage,
    handleLastNumber,
    handleFirstNumber,
    currentArray,
    numOfPages,
  } = usePagination({
    numOfResourses,
    numOfPageElements,
    setPage,
    page,
  });

  return currentArray.length ? (
    <div
      className={`m-auto flex flex-row flex-wrap items-center text-secondary-500 justify-between w-full gap-3 rounded-md bg-transparent p-3 align-middle ${
        loading ? "pointer-events-none opacity-60" : ""
      }`}
    >
      <div
        className={`${
          page === 1 ? "" : "cursor-pointer hover:text-secondary"
        } text-xs flex items-center gap-2 md:text-base`}
        onClick={() => handleChangePage(page - 1)}
      >
        <ArrowBack />
        <p>{t("previous")}</p>
      </div>

      <div className="flex flex-row flex-wrap items-center justify-center gap-1 hover:text-secondary sm:gap-2 md:gap-3">
        {currentArray[0] !== 1 && (
          <span
            className={`numOfPagination cursor-pointer ${
              page === 1 && "text-secondary-400"
            }`}
            onClick={handleFirstNumber}
          >
            1
          </span>
        )}

        {currentArray[0] >= 3 && <span>{t("ellipsis")}</span>}

        {currentArray.map((item) => (
          <div
            onClick={() => handleChangePage(item)}
            className={`cursor-pointer numOfPagination ${
              page === item && "text-secondary-400"
            }`}
            key={item}
          >
            <span>{item}</span>
          </div>
        ))}

        {currentArray[currentArray.length - 1] <= numOfPages - 2 && (
          <span>....</span>
        )}

        {currentArray[currentArray.length - 1] !== numOfPages && (
          <span
            className={`numOfPagination ${
              page === numOfPages && "text-secondary-400"
            } cursor-pointer`}
            onClick={handleLastNumber}
          >
            {numOfPages}
          </span>
        )}
      </div>

      <div
        className={`${
          page === numOfPages ? "" : "cursor-pointer hover:text-secondary"
        } flex items-center gap-2`}
        onClick={() => handleChangePage(page + 1)}
      >
        <p>{t("next")}</p>
        <ArrowFront />
      </div>
    </div>
  ) : null;
}

export default CoursesPagination;
