import { useAuth } from "@/Contexts/AuthContext";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import MainLayout from "@/Layout/MainLayout";
import { useTranslation } from "react-i18next";
import ManageCompaniesTable from "./Partials/ManageCompaniesTable";
import ApiProfile from "@/Api/apiProfileConfig";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router";

const ManageAdminCompanies = () => {
  const { getStartMenu } = useSideBarRoute();
  const [CompanyAccounts, setCompanyAccounts] = useState();
  const [loading, setLoading] = useState(false);
  const navigation = useNavigate()
  const { t } = useTranslation();
  const getAllCompanyAccounts = async () => {
    try {
      setLoading(true);
      const { data } = await ApiProfile.get("/admin/get-all-company-accounts");
      setCompanyAccounts(data);
      setLoading(false);

      console.log(data);
    } catch ({ response }) {
      setLoading(false);
      response.data.error && navigation('/get-started')
      console.log(response);
    }
  };
  useEffect(() => {
    getAllCompanyAccounts();
  }, []);
  return (
    <>
      {/* <div className="w-full p-5 text-center bg-white rounded-lg">
        <h1 className="text-3xl font-bold text-primary">
          {t("Admin Companies Management")}
        </h1>
      </div> */}
      <ManageCompaniesTable
        data={CompanyAccounts}
        getAllCompanyAccounts={getAllCompanyAccounts}
        tableLoading={loading}
      />
    </>
  );
};

export default ManageAdminCompanies;
