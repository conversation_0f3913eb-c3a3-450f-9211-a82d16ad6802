import Chatbot from "@/Components/Chatbot";
import Navbar from "@/Components/NavBar/Navbar";
import Sidebar from "@/Components/SideBar/Sidebar";

import { Breadcrumbs } from "@mantine/core";
import { useState } from "react";
const GrcLayout = ({
  activeChatbot = false,
  children,
  navbarTitle,
  items,
}) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  return (
    <div className="flex flex-col">
      <Navbar
        navbarTitle={navbarTitle}
        isSidebarOpen={isSidebarOpen}
        setIsSidebarOpen={setIsSidebarOpen}
        items={items && items}
      />

      <div className="flex-grow flex overflow-hidden overflow-y-auto z-10 bg-[#f7f4f4]">
        <Sidebar
          isOpen={isSidebarOpen}
          setIsOpen={setIsSidebarOpen}
        />
        <div className={"px-2 w-full"}>
          <div className="text-2xl font-semibold font-inter leading-none text-primary mx-auto my-8 px-2 flex flex-col sm:flex-row items-center justify-between">
            <div>{navbarTitle}</div>
            <p className="text-base font-normal mt-5 sm:mt-0">
              {items?.length > 0 && (
                <Breadcrumbs className="" separator="→">
                  {items}
                </Breadcrumbs>
              )}
            </p>
          </div>
          {children}
          <br />
          <br />
          <br />
          <br />
          <br />
        </div>
      </div>
      {activeChatbot && <Chatbot />}

    </div>
  );
};

export default GrcLayout;
