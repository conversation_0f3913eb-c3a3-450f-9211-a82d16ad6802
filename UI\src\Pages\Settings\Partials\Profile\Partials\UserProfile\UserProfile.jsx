import ApiProfile from "@/Api/apiProfileConfig";
import { Button, Select, TextInput, Switch } from "@mantine/core";
import { isNotEmpty, useForm } from "@mantine/form";
import { showNotification } from "@mantine/notifications";
import {
  IconCheck as IconCheckNotification,
  IconX as IconXNotification,
} from "@tabler/icons-react";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { PiSpinnerLight } from "react-icons/pi";
import { RiLockPasswordFill } from "react-icons/ri";
import { Link } from "react-router-dom";
import UpdatePassword from "../UpdatePassword/UpdatePassword";
import { UserImg } from "../UserImg/UserImg";
import { useUserStore } from "@/Store/useUser";
import Cookies from "js-cookie";

export default function UserProfile({ profileData, getUserData }) {
  // const [profileData, setProfileData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [hiddenPass, setHiddenPass] = useState(true);
  const [disabled, setDisabled] = useState(true);
  const [twoFactorLoading, setTwoFactorLoading] = useState(false);
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const [profileUpdates, setProfileUpdates] = useState({});
  const { updateAvatar , clearTempAvatar} = useUserStore((state) => state);
  
  // // table Form
  const form = useForm({
    mode: "uncontrolled",
    initialValues: {
      user_name: "",
      phoneNumber: "",
      country: "",
      companyName: "",
      role: "",
    },
    validate: {
      user_name: isNotEmpty("User Name is required"),
    },
  });

  const updateUserInfo = async (values) => {
    setLoading(true);
    const { user_name, phoneNumber: phone_number, country } = profileUpdates;
    const value = { user_name, phone_number, country };
    try {
      setLoading(true);
      if(profileUpdates.user_name ||
         profileUpdates.phoneNumber ||
         profileUpdates.country){
      const { data } = await ApiProfile.post("/update-user-information", value);
      // console.log(data);
      if (data.message === "User information updated successfully") {
        showNotification({
          title: "Success",
          message: data.message,
          color: "teal",
          icon: <IconCheckNotification />,
        });
        setLoading(false);
        // console.log(data);
        getUserData();
      } 
    }

      if(profileUpdates.avatar){
        try {
          setLoading(true);
          const formData = new FormData();
          formData.append("avatar", profileUpdates.avatar);
          const response = await ApiProfile.post("/avatar",
            formData,
            {
              headers: {
                'Content-Type': 'multipart/form-data',
                Authorization: `Bearer ${Cookies.get("level_user_token")}`,
              },
            }
          );
          const reader = new FileReader();
          reader.onloadend = () => {
            localStorage.setItem("avatar", reader.result);
            updateAvatar(reader.result);
          }
          reader.readAsDataURL(profileUpdates.avatar);
          clearTempAvatar();
          setLoading(false);
          console.log(response);
        } catch (error) {
          console.error("Upload failed", error);
          showNotification({
            title: "Error",
            message: error.response?.data?.message || "Failed to update avatar",
            color: "red",
            icon: <IconXNotification />,
          }); 
          setLoading(false);
        }
        
      }

      // console.log(data);
    } catch (error) {
      // console.log(error);
      setLoading(false);
      clearTempAvatar();
      if (
        error.response.data.message === "No changes detected, same old values"
      ) {
        setLoading(false);
        showNotification({
          title: "Error",
          message: error.response.data.message,
          color: "red",
          icon: <IconXNotification />,
        });
      }
      // console.log(error);
    }
    setDisabled(true);
  };

  // Handle two-factor authentication toggle
  const handleTwoFactorToggle = async (checked) => {
    setTwoFactorLoading(true);
    try {
      const endpoint = checked 
        ? "/user/enable-two-factor" 
        : "/user/disable-two-factor";
      
      const { data } = await ApiProfile.put(endpoint);
      
      if (data) {
        showNotification({
          title: "Success",
          message: checked 
            ? "Two-factor authentication enabled successfully" 
            : "Two-factor authentication disabled successfully",
          color: "teal",
          icon: <IconCheckNotification />,
        });
        
        // Update local state to match server state
        setTwoFactorEnabled(checked);
        
        // Refresh user data to get updated information
        getUserData();
      }
    } catch (error) {
      showNotification({
        title: "Error",
        message: error.response?.data?.message || "Failed to update two-factor authentication",
        color: "red",
        icon: <IconXNotification />,
      });
      
      // Reset switch to previous state since operation failed
      setTwoFactorEnabled(!checked);
    } finally {
      setTwoFactorLoading(false);
    }
  };

  useEffect(() => {
    if (profileData) {
      form.setValues({
        user_name: profileData.userName || "",
        phoneNumber: profileData.phoneNumber || "",
        country: profileData.country || "",
        companyName: profileData.company_name || "",
        role: profileData.userRole || "",
      });
      
      // Set the two-factor state from profile data
      setTwoFactorEnabled(profileData.twoFactor || false);
    }
  }, [profileData]);
  const isUpdateChanges = () => {
    const updatedValues = {
      user_name: profileUpdates.user_name,
      phoneNumber: profileUpdates.phoneNumber,
      country: profileUpdates.country,
      companyName: profileUpdates.companyName,
      role: profileUpdates.role,
    };
    console.log(profileUpdates);
    console.log(Object.values(updatedValues).some((value) => value !== ""));
    return Object.values(profileUpdates).some((value) => value !== "");
  }
  const { t } = useTranslation();
  // console.log(loading);
  // End table Form
  return (
    <>
      <div className="p-5">
        <div className="grid items-center justify-between grid-cols-1 md:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2">
          <div className="col-span-1">
            <UserImg
              setProfileUpdates={setProfileUpdates}
              avatar={profileData.avatar}
              userName={profileData.userName}
              userEmail={profileData.email}
              isEdit={!disabled}
            />
          </div>
          <div className="justify-end col-span-1 mt-3 md:flex lg:block xl:flex md:mt-0 lg:mt-3 w-full">
            {disabled ? (
              <Button
                className="w-full bg-primary hover:bg-primary md:w-1/2"
                size="md"
                onClick={() => {
                  setDisabled(false);
                  setProfileUpdates({});
                }}
              >
                {loading ? (
                  <PiSpinnerLight className="text-2xl animate-spin" />
                ) : (
                  "Edit"
                )}
              </Button>
            ) : (
              <div className="flex gap-2 flex-col w-full md:w-1/2">
                <Button
                className="w-full bg-primary hover:bg-primary"
                size="md"
                disabled={!isUpdateChanges() || loading}
                onClick={form.onSubmit(updateUserInfo)}
              >
                {loading ? (
                  <PiSpinnerLight className="text-2xl animate-spin" />
                ) : (
                  "Update"
                )}
              </Button>
              <Button
                className="w-full bg-primary hover:bg-primary"
                size="md"
                disabled={loading}
                onClick={() => {
                  setDisabled(true);
                  setProfileUpdates({});
                  clearTempAvatar();
                }}
              >
                {loading ? (
                  <PiSpinnerLight className="text-2xl animate-spin" />
                ) : (
                  "Cancel"
                )}
              </Button>
              </div>
            )}
          </div>
        </div>
        <form onSubmit={form.onSubmit(updateUserInfo)}>
          <div className="grid grid-cols-1 mt-5 sm:gap-x-5 md:gap-x-10 lg:gap-x-28 text-start">
            <TextInput
              {...form.getInputProps("user_name")}
              key={form.key("user_name")}
              type="text"
              id="user_name"
              name="user_name"
              label="Name"
              placeholder={
                profileData.userName
                  ? "Update your User Name"
                  : "Enter your User Name"
              }
              mb="md"
              value={disabled ? profileData.userName : profileUpdates.user_name}
              disabled={disabled}
              onChange={(e) => {
                setProfileUpdates({ ...profileUpdates, user_name: e.target.value });
              }}
            />
            <TextInput
              {...form.getInputProps("phoneNumber")}
              key={form.key("phoneNumber")}
              type="text"
              id="phoneNumber"
              name="phoneNumber"
              label="Phone"
              placeholder={
                profileData.phoneNumber
                  ? "Update your Phone Number"
                  : "Enter your Phone Number"
              }
              mb="md"
              value={disabled ? profileData.phoneNumber : profileUpdates.phoneNumber}
              disabled={disabled}
              onChange={(e) => {
                setProfileUpdates({ ...profileUpdates, phone_number: e.target.value });
              }}
            />
            <TextInput
              {...form.getInputProps("country")}
              key={form.key("country")}
              type="text"
              id="country"
              name="country"
              label="Country"
              placeholder={
                profileData.country
                  ? "Update your Country"
                  : "Enter your Country"
              }
              mb="md"
              value={disabled ? profileData.country : profileUpdates.country}
              disabled={disabled}
              onChange={(e) => {
                setProfileUpdates({ ...profileUpdates, country: e.target.value });
              }}
            />
            <TextInput
              {...form.getInputProps("companyName")}
              key={form.key("companyName")}
              type="text"
              id="companyName"
              name="companyName"
              label="Company Name"
              placeholder="Update your Company Name"
              mb="md"
              value={disabled ? profileData.companyName : profileUpdates.companyName}
              disabled
              onChange={(e) => {
                setProfileUpdates({ ...profileUpdates, company_name: e.target.value });
              }}
            />
            <Select
              {...form.getInputProps("role")}
              key={form.key("role")}
              mb="md"
              id="role"
              name="role"
              label="Role"
              data={["User", "Admin"]}
              placeholder="Update your Role"
              defaultValue={profileData.userRole}
              disabled
              onChange={(e) => {
                setProfileUpdates({ ...profileUpdates, role: e.target.value });
              }}
            />
          </div>
        </form>
        <div>
          <h1 className="text-xl font-semibold">{t("Change Password")}</h1>
          <div className="grid items-center gap-4 justify-center grid-cols-1 md:justify-start">
            <div className="flex items-center mt-3">
              <span className="p-3 bg-[#E5F5F7] rounded-full me-2">
                <RiLockPasswordFill className="text-primary" />
              </span>

              <Link
                className="underline text-primary"
                onClick={() => setHiddenPass(false)}
              >
                {t("Request Password Change")}
              </Link>
            </div>
            <div className={` ${hiddenPass ? "hidden" : ""}`}>
              <UpdatePassword />
            </div>
            <div className="flex items-center mt-3 gap-4">
              <p className="text-sm font-medium">Enable Login OTP (One Time Passcode)</p>
              <Switch 
                size="lg" 
                color="#05808B" 
                onLabel="ON" 
                offLabel="OFF" 
                checked={twoFactorEnabled}
                disabled={twoFactorLoading}
                onChange={(event) => handleTwoFactorToggle(event.currentTarget.checked)}
              />
              {twoFactorLoading && <PiSpinnerLight className="ml-2 text-xl animate-spin" />}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}