import React from "react";
import Chart from "../Charts/RadarChart";
import {
  Chart as ChartJS,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  <PERSON><PERSON><PERSON>,
  Legend,
} from "chart.js";
import PropTypes from "prop-types";
ChartJS.register(
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend
);

const RadarChart = ({ assessmentScore, assessmentName, category }) => {
  return (
    <>
      <Chart
        labels={assessmentName}
        data={assessmentScore}
        label={category}
        backgroundColor="rgba(10,111,133, 0.2)"
        borderColor="rgb(10,111,133)"
      />
    </>
  );
};

RadarChart.propTypes = {
  assessmentScore: PropTypes.arrayOf(PropTypes.number).isRequired,
  assessmentName: PropTypes.arrayOf(PropTypes.string).isRequired,
  category:PropTypes.arrayOf(PropTypes.array).isRequired,
};
export default RadarChart;