import { useEffect, useRef, useState } from "react";
import Loading from "@/Components/Loading";
import { Button, Modal, MultiSelect, Popover, Table } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { showNotification } from "@mantine/notifications";
import axios from "axios";
import Cookies from "js-cookie";

import { IoIosSearch } from "react-icons/io";
import { IoAddOutline, IoFilter } from "react-icons/io5";
import { PiExportLight } from "react-icons/pi";
import GreenRiskInputs from "../GreenRiskInputs";
import GreenRiskRow from "./GreenRiskRow";
import { greenWashingHead } from "../constants";
import RiskHeatMap from "../../ESGRiskManagementSystem/RiskIdentificationAndAssessment/NewHeatMap";

export default function GreenRiskAssessmentTable() {
  const [loading, setLoading] = useState(false);
  const [opened, { open, close }] = useDisclosure(false);
  const [myData, setMyData] = useState();
  const [filters, setFilters] = useState([]);
  const tableRef = useRef();
  const [searchQuery, setSearchQuery] = useState("");
  const [refreshTable, setRefreshTable] = useState(false);

  const handleRiskAdded = () => {
    setRefreshTable((prev) => !prev); // This toggles refreshTable, causing GreenRiskAssessmentTable to reload data
    close(); // Close the modal after successful addition
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const token = Cookies.get("level_user_token");
        const response = await axios.get(
          "https://portals3-h3bjdkhtbghye2aa.uksouth-01.azurewebsites.net/anti-green-washing-risk",
          { headers: { Authorization: `Bearer ${token}` } }
        );
        if (response?.data) {
          setMyData(response.data); // Update table data
        }
        // console.log("data======>", response);
      } catch (error) {
        showNotification({
          title: "Error",
          message: "Failed to fetch data",
          color: "red",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [refreshTable]); // Refetch data whenever refreshTable changes

  const rows = myData?.map((risk) => (
    <GreenRiskRow filters={filters} risk={risk} key={risk._id} />
  ));

  useEffect(() => {
    setSearchQuery("");
    if (filters.length == 0) {
      setFilters(greenWashingHead);
    }
  }, [filters]);

  return (
    <div className="bg-[#F7F4F4] mt-4">
      <RiskHeatMap risks={myData} impactSeverity="impactId" />
      <br />
      <br />

      <div className="flex flex-wrap justify-between items-center bg-white rounded-xl p-6">
        <div className="title">
          <h1 className="text-xl font-bold">Risk Management</h1>
          <h2 className="text-sm text-secondary-lite-100 mt-1">
            Intelligent ESG risk identification, assessment, and mitigation
          </h2>
        </div>

        <Modal
          size={"90%"}
          opened={opened}
          onClose={close}
          withCloseButton={false}
        >
          <GreenRiskInputs onRiskAdded={handleRiskAdded} />
        </Modal>

        <div className="btns flex items-center gap-6">
          <Button
            onClick={open}
            className="border-[1px] w-[105px] h-[42px] text-sm font-bold rounded-lg bg-secondary-300 px-1"
            size="md"
          >
            <span className="me-1">
              <IoAddOutline className="text-lg" />
            </span>
            <span>Add Risk</span>
          </Button>
        </div>
      </div>

      <div className="flex flex-row items-center justify-between shadow-lg font-semibold text-[#000] bg-[#fff] mt-4 py-2 px-4">
        <div className=" flex flex-row text-lg  font-normal">
          <Button
            className="border-[1px] w-[105px] h-[42px] text-sm font-bold rounded-lg text-secondary-300 border-secondary-300 bg-white px-1"
            size="md"
            // onClick={() => exportRiskTableToExcel(myData)}
          >
            <span className="me-1">
              <PiExportLight className="text-lg" />
            </span>
            <span>Export</span>
          </Button>

          <div className="border bg-[#D1D1D1] ml-6"></div>
        </div>

        <div className="flex flex-row mr-4 gap-4">
          <div className="flex flex-row items-center shadow-none px-4 rounded-lg bg-[#EBEBEB] text-[#00C0A9]">
            <Popover width={300} position="bottom" withArrow shadow="md">
              <Popover.Target>
                <Button
                  style={{ backgroundColor: "transparent", color: "#9C9C9C" }}
                >
                  <IoFilter className="text-[#212121] mr-2" />
                </Button>
              </Popover.Target>
              <Popover.Dropdown>
                <MultiSelect
                  placeholder="Select Columns "
                  data={greenWashingHead}
                  comboboxProps={{ withinPortal: false }}
                  onChange={setFilters}
                  clearable={true}
                />
              </Popover.Dropdown>
            </Popover>
          </div>
          <div className="flex flex-row items-center bg-[#F5F5F5] rounded-lg px-6 py-2">
            <input
              placeholder="Find"
              className="w-full bg-transparent"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <IoIosSearch className="text-[#626364] w-6 h-6 ml-10" />
          </div>
        </div>
      </div>

      <br />
      {loading && (
        <div className="fixed z-50 bg-slate-500 flex justify-center">
          <Loading />
        </div>
      )}
      <div className="flex bg-white relative overflow-y-hidden px-3">
        <Table.ScrollContainer
            className="scrollable-container"
            maw={"99%"}
            style={{ display: "flex", alignItems: "center" }}
        >
            <Table
              miw={1500}
              verticalSpacing="lg"
              className="scrollable-container"
              ref={tableRef}
            >
              <Table.Thead className="border-b-2 bg-white pb-6 text-base text-center mb-5">
                <Table.Tr>
                {greenWashingHead
                  .filter((head) => (filters ? filters.includes(head) : true))
                  .map((el, i) => (
                    <Table.Th key={i} className="text-secondary-500 px-0 mx-4
                     w-fit whitespace-nowrap">
                      {el}
                    </Table.Th>
                  ))}
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>{myData ? <>{rows}</> : <Loading />}</Table.Tbody>
          </Table>
        </Table.ScrollContainer>
      </div>
      <br />
      <br />
      <br />
    </div>
  );
}
