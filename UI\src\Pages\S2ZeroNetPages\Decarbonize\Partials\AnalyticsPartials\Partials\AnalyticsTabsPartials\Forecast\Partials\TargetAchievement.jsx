import { LineChart } from "@mantine/charts";
import React from "react";

const TargetAchievement = () => {
  const data = [
    {
      type: "Highest",
      value: 10000,
    },
    {
      type: "High",
      value: 20000,
    },
    {
      type: "Moderate",
      value: 30000,
    },
    {
      type: "Low",
      value: 40000,
    },
    {
      type: "Lowest",
      value: 50000,
    },
  ];
  return (
    <div className="my-5">
      <div className="flex items-center justify-between p-2 mb-4">
        <h3 className="text-2xl font-semibold">Target Achievement Likelihood</h3>
      </div>

      <LineChart
        className="bg-white py-8 px-4 rounded-lg shadow-lg"
        h={400}
        w={500}
        data={data}
        dataKey="type"
        withDots={false}
        curveType="linear"
        // dotProps={{ r: 2 }}
        // activeDotProps={{ r: 3, strokeWidth: 1 }}
        series={[
         
          { name: "value", color: "blue.6" },
        ]}
      />
    </div>
  );
};

export default TargetAchievement;
