import { create } from "zustand";
import ApiSustain360 from "@/Api/apiSustain360";

export const useSustain360Store = create((set, get) => ({
    assessmentData: {},
    assessmetSummery: {},
    activeAssesmentSection: "General",
    currentSectionData: {},
    activeQuestion: 1,
    activeQuestionIndex: 0,
    report: {},
    hasPrevios: false,
    hasNext: false,
    QuizLoading: false,

    setQuizLoading: (loading) => set({ QuizLoading: loading }),
    getQuizLoading: () => get().QuizLoading,

    setAssessmentData: (data) => set({ assessmentData: data }),
    getAssessmentData: () => get().assessmentData,
    loadAssessmentData: () => {
        set({ QuizLoading: true });
        fetchAssessmentData().then((data) => {
            set({ assessmentData: data });
        }).catch((error) => {
            console.log(error);
        }).finally(() => {
            set({ QuizLoading: false });
        });
    },
    getAssessmentSectionData: (section) => get().assessmentData.sections.find((item) => item.title === section),
    getActiveAssesmentSection: () => {
        return get().assessmentData?.sections?.find((item) => item.title === get().activeAssesmentSection);
    },
    setActiveAssesmentSection: (section,questionId) => {
        // reset the question index and active question with the first question in the section questions id

        if(questionId){
            let questions = get().assessmentData.sections.find((item) => item.title === section).questions.sort((a, b) => a.questionId - b.questionId);
            let index = questions.findIndex((item) => item.questionId === questionId);
            set({ activeQuestionIndex: index });
            set({ activeQuestion: questionId });
        }else{
            set({ activeQuestionIndex: 0 });
            set({ activeQuestion: get().assessmentData.sections.find((item) => item.title === section).questions[0].questionId });
        }
        set({ activeAssesmentSection: section });
    },
    updateQuestionAnswer: (questionId, answerOptionId) => {
        let assessmentData = get().assessmentData;
        let updatedAssessment = {
            ...assessmentData, sections: assessmentData.sections.map((section) => {
                if (section.title === get().activeAssesmentSection) {
                    return {
                        ...section, questions: section.questions.map((question) => {
                            if (question.questionId === questionId) {
                                return {
                                    ...question, options: question.options.map((option) => {
                                        if (option.id == answerOptionId) {
                                            updateQuestionAnswer(question.id, answerOptionId);
                                            return { ...option, is_chosen: true };
                                        }
                                        return { ...option, is_chosen: false };
                                    })
                                };
                            }
                            return question;
                        })
                    }
                }
                return section;
            })
        }
        set({ assessmentData: updatedAssessment });
    },
    goToNextQuestion: () => {
        let newQuestion = get().activeQuestion + 1;
        let newQuestionIndex = get().activeQuestionIndex + 1;

        if (newQuestion > 29) {
            newQuestion = 29;
        }
        let currentSection = get().activeAssesmentSection;
        let newSection = currentSection;
        get().assessmentData.sections.forEach(s => {
            s.questions.forEach(q => {
                if (q.questionId === newQuestion) {
                    newSection = s.title;
                }
            });
        });
        if (newSection !== currentSection) {
            newQuestionIndex = 0;
            console.log(newSection);
            set({ activeAssesmentSection: newSection });
        }
        console.log(newQuestion);
        if (newQuestion == 29) {
            set({ activeQuestion: newQuestion, activeQuestionIndex: newQuestionIndex, hasPrevios: true, hasNext: false });
        } else {
            set({ activeQuestion: newQuestion, activeQuestionIndex: newQuestionIndex, hasPrevios: true, hasNext: true });
        }
    },
    goToPreviousQuestion: () => {
        let newQuestion = get().activeQuestion - 1;
        let newQuestionIndex = get().activeQuestionIndex - 1;
        if (newQuestionIndex < 0) {
            newQuestionIndex = 0;
        }
        if (newQuestion < 1) {
            newQuestion = 1;
        }
        let currentSection = get().activeAssesmentSection;
        let newSection = currentSection;
        get().assessmentData.sections.forEach(s => {
            s.questions.forEach(q => {
                if (q.questionId === newQuestion) {
                    newSection = s.title;
                }
            });
        });
        if (newSection !== currentSection) {
            newQuestionIndex = get().assessmentData.sections.find((item) => item.title === newSection).questions.length - 1;
            set({ activeAssesmentSection: newSection });
        }
        console.log(newQuestion);
        if (newQuestion == 1) {
            set({ activeQuestion: newQuestion, activeQuestionIndex: newQuestionIndex, hasPrevios: false, hasNext: true });
        } else {
            set({ activeQuestion: newQuestion, activeQuestionIndex: newQuestionIndex, hasPrevios: true, hasNext: true });
        }
    },
    isLastSection: () => {
        let orderedSections = get().assessmentData.sections.sort((a, b) => a.order - b.order);
        let currentSection = get().activeAssesmentSection;
        let currentSectionIndex = orderedSections.findIndex((item) => item.title === currentSection);
        return currentSectionIndex === orderedSections.length - 1;
    },
    
    setActiveQuestion: (question) => set({ activeQuestion: question }),
    getActiveQuestionData: () => get().assessmentData.sections.find((item) => item.title === get().activeAssesmentSection).questions.find((item) => item.question_id === get().activeQuestion),

    setActiveQuestionIndex: (index) => set({ activeQuestionIndex: index }),
    getActiveQuestionIndex: () => get().activeQuestionIndex,

    setAssessmentSummery: (data) => set({ assessmentSummery: data }),
    getAssessmentSummery: () => get().assessmentSummery,

    setReport: (data) => set({ report: data }),
    getReport: () => get().report,
    resetStore: () => set({ assessmentData: {}, assessmetSummery: {}, activeAssesmentSection: "General", activeQuestion: 1 }),
    resetActiveAssesmentSection: () => set({ activeAssesmentSection: "General", activeQuestion: 1 })
}));


const fetchAssessmentData = async () => {
    try {
        const response = await ApiSustain360.get("/api/assessments/in-progress/last")
        const assessmentData = response.data;
        return assessmentData;
    } catch (error) {
        const response = await ApiSustain360.post("/api/assessments");
        const assessmentData = response.data;
        return assessmentData;
    }
};

const updateQuestionAnswer = async (questionId, answerOptionId) => {
    try {
        const response = await ApiSustain360.post(`/api/questions/answers/`, 
            [
                {
                    question_id: questionId,
                    option_id: answerOptionId   
                }
            ]
        )
        const assessmentData = response.data;
        return assessmentData;
    } catch (error) {
        console.log(error);
    }
};
