import Loading from "@/Components/Loading";
import { Button, Input, Select, Text } from "@mantine/core";
import { showNotification } from "@mantine/notifications";
import axios from "axios";
import Cookies from "js-cookie";
import React, { useRef, useState } from "react";
import { useTranslation } from "react-i18next";

const PhotosForm = ({ refetch }) => {
  const msg = (txt, color = "green") => {
    showNotification({ message: txt, color: color, timing: 7000 });
  };
  const { t } = useTranslation();
  const [submiting, setSubmiting] = useState(false);

  // Create refs for inputs
  const [usename,setUsername] = useState('');
  const personType = useRef(null);
  const imageRef = useRef(null);

  // State for error messages
  const [errors, setErrors] = useState({
    userName: "",
    imageType: "",
    photoType: "",
  });

  const validateInputs = () => {
    const newErrors = {};
    const userNameValue = usename;
    const imageTypeValue = imageRef.current;
    const photo = personType.current;

    if (userNameValue == null || userNameValue.length < 3) {
      newErrors.userName = "User name must be at least 3 characters long.";
    }

    if (imageTypeValue == null) {
      newErrors.imageType = "Image is required.";
    }

    if (photo == null) {
      newErrors.photoType = "Image type is required.";
    }

    setErrors(newErrors);

    // Return true if no errors
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateInputs()) {
      addPhoto();
    }
  };

  const addPhoto = async () => {

    let person = personType.current == "Top" ? "vip" : "commoner";

    setSubmiting(true);

    try {
      const res = await axios.post(
        "https://portal-auth-main-staging.azurewebsites.net/upload_image",
        {
          image: imageRef.current,
        },
        {
          headers: {
            "Content-Type": "multipart/form-data",
            userName: usename,
            imageType: person,
            Authorization: `Bearer ${Cookies.get("level_user_token")}`,
          },
        }
      );

      msg(`Sucess`);
      refetch();
      personType.current = null;
      imageRef.current = null;
      setUsername('')
      console.log("🚀 ~ addPhoto ~ res:", res);
    } catch (error) {
      msg(`Error ${error.response.data.message}`, "red");
      console.log("🚀 ~ addPhoto ~ error:", error);
    }

    setSubmiting(false);
  };
  return (
    <div className="flex items-start gap-4">
        {
          submiting ?
          <Loading />
:
          <>
      <div>

        <Input
          placeholder="Enter user name"
          error={errors.userName}
          label="User Name"
          value={usename}
          onChange={(e)=> setUsername(e.target.value)}
        />
        {errors.userName && (
          <Text color="red" size="sm" mt={1}>
            {errors.userName}
          </Text>
        )}
      </div>

      <div>
        {/* Image Type Input */}
        <Input
          onChange={(e) => (imageRef.current = e.target.files[0])}
          type="file"
          placeholder="Enter image type"
          error={errors.imageType}
          label="Image Type"
        />
        {errors.imageType && (
          <Text color="red" size="sm" mt={1}>
            {errors.imageType}
          </Text>
        )}
      </div>

      <div>
        <Select
          onChange={(e) => {
            personType.current = e;
          }}
          label=""
          placeholder="Pick type"
          data={["Top", "Wheel"]}
        />
        {errors.photoType && (
          <Text color="red" size="sm" mt={1}>
            {errors.photoType}
          </Text>
        )}
      </div>
          </>
        }

      <Button
        disabled={submiting}
        className={`${submiting && 'cursor-not-allowed'} bg-[#05808b57] flex-1 border-2 border-primary font-bold text-primary hover:bg-[#05808b57] hover:text-primary`}
        size="sm"
        onClick={() => handleSubmit()}
      >
        {t("+ Add Photo")} {submiting && <Loading />}
      </Button>
    </div>
  );
};

export default PhotosForm;
