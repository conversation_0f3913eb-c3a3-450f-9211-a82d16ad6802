import ApiS2 from "@/Api/apiS2Config";
import Loading from "@/Components/Loading";
import MainLayout from "@/Layout/MainLayout";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import { Button } from "@mantine/core";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next"; // Import i18next hook
import { useLocation, useNavigate } from "react-router";
import CustomFactorDataTable from "./Partials/CustomFactorDataTable";
import { useAuth } from "@/Contexts/AuthContext";
import CustomFactorForm from "./Partials/CustomFactorForm";
import { driver } from "driver.js";
import "driver.js/dist/driver.css";
import "@/assets/css/index.css";
import { FaQuestionCircle } from "react-icons/fa";

export default function ConfigurationsCustomFactor() {
  const { user } = useAuth();
  const { getStartMenu } = useSideBarRoute();
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation(); // Use translation function
  const [loading, setLoading] = useState(false);

  // assets list
  const [assetListState, setAssetListState] = useState([]);
  const [assetNamesList, setAssetNamesList] = useState([]);

  const [customerFactorData, setCustomerFactorData] = useState([]);
  const [assetTypes, setAssetTypes] = useState([]);
  const getFactors = async () => {
    setLoading(true);
    try {
      const assetTypes = await ApiS2.get("carbon-factors/get-all-asset-types");
      setAssetTypes(assetTypes.data);
      const { data } = await ApiS2.get("carbon-factors/get-all-custom-factors");
      setLoading(false);
      setCustomerFactorData(data);
    } catch (error) {
      setLoading(false);
    }
  };

  const assetList = async () => {
    try {
      const { data } = await ApiS2.get(
        "/carbon-factors/get-all-company-assets"
      );
      const dropList = data.map((el) => el.assetName);
      setAssetListState(dropList);
      setAssetNamesList(data);
    } catch (err) {
      console.log(err);
    }
  };

  useEffect(() => {
    getFactors();
    assetList();
  }, []);

  const [filterTableData2, setfilterTableData2] = useState([]);

  const allKeys = filterTableData2.reduce((acc, item) => {
    if (item.activity) {
      const keys = Object.keys(item.activity);
      acc.push(...keys); // Add the keys to the accumulator
    }
    return acc;
  }, []);
  const uniqueActivites = [...new Set(allKeys)];

  const allActivityValues = filterTableData2.reduce((acc, item) => {
    if (item.activity) {
      const values = Object.values(item.activity);
      acc.push(...values);
    }
    return acc;
  }, []);
  const uniqueActivitesList = [...new Set(allActivityValues)];

  const allUomKeys = filterTableData2.reduce((acc, item) => {
    if (item.uom) {
      const keys = Object.keys(item.uom);
      acc.push(...keys); // Add the keys to the accumulator
    }
    return acc;
  }, []);
  const uniqueUom = [...new Set(allUomKeys)];

  const allEfactorValues = filterTableData2.reduce((acc, item) => {
    if (item.eFactors) {
      const values = Object.values(item.eFactors);
      acc.push(...values);
    }
    return acc;
  }, []);
  const uniqueEfactorList = [...new Set(allEfactorValues)];

  // Extract unique keys for `eFactor`
  const allEFactorKeys = filterTableData2.reduce((acc, item) => {
    if (item.eFactors) {
      const keys = Object.keys(item.eFactors);
      acc.push(...keys); // Add the keys to the accumulator
    }
    return acc;
  }, []);
  const uniqueEFactor = [...new Set(allEFactorKeys)];

  const allUomValues = filterTableData2.reduce((acc, item) => {
    if (item.uom) {
      const values = Object.values(item.uom);
      acc.push(...values);
    }
    return acc;
  }, []);
  const uniqueUomList = [...new Set(allUomValues)];

  const filterTableData = (id) => {
    let newDataTable = customerFactorData.filter(
      (record) => record.emissionSourceId == id
    );
    setfilterTableData2(newDataTable);
  };
  const getGuideSteps = () => [
    {
      element: ".Select-asset",
      popover: {
        title: t("Select Asset"),
        description: t("Pick the asset you want to assign a custom factor to"),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".Select-emission",
      popover: {
        title: t("Select Emission Source "),
        description: t(
          "Choose the emission source for this asset (e.g. Purchased Electricity)"
        ),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".Manage-custom-factors",
      popover: {
        title: t("Manage your Custom factor by editing and deleting them."),
        side: "left",
        align: "center",
      },
    },
    {
      element: ".Search-Custom-Factor",
      popover: {
        title: t("Search for Custom Factor"),
        description: t(
          "Use the “Search” feature to find your Custom factors easily"
        ),
        side: "left",
        align: "center",
      },
    },
  ];
  
  const startGuide = () => {
    const driverObj = driver({
      showProgress: true,
      popoverClass: "my-custom-popover-class",
      nextBtnText: "Next",
      prevBtnText: "Back",
      doneBtnText: "Done",
      overlayColor: "rgba(0, 0, 0, 0)",
      progressText: "Step {{current}} of {{total}}",
      steps: getGuideSteps(),
      onDestroyStarted: () => {
        localStorage.setItem("hasSeenEmissionGuide", "true");
        driverObj.destroy();
      },
    });
    driverObj.drive();
  };

  useEffect(() => {
    const hasSeenGuide = localStorage.getItem("hasSeenEmissionGuide");
    if (!hasSeenGuide) {
      startGuide();
    }
    return () => {
      const driverObj = driver();
      if (driverObj.isActive()) {
        driverObj.destroy();
      }
    };
  }, []);

  return (
    <>
      <MainLayout menus={getStartMenu} navbarTitle={t("NavbarTitle")}>
        <>
          <div
            onClick={startGuide}
            style={{
              position: "fixed",
              bottom: 20,
              right: 20,
              cursor: "pointer",
              zIndex: 1000,
            }}
          >
            <div className="h-12 w-12 flex items-center justify-center bg-gradient-to-tr from-[#2C5A8C] via-[#1C889C] to-[#13B1A8] p-1  cursor-pointer rounded-full">
              <FaQuestionCircle
                size={34}
                color="#ffffff"
                className="mx-auto cursor-pointer"
              />
            </div>
          </div>
          <div className="w-full p-5 text-center bg-white rounded-lg">
            <h1 className="text-3xl font-bold text-primary">
              {t("Emission Factor Selection")}
            </h1>
          </div>
          <div className="justify-around  p-5 mt-5 text-center rounded-lg xl:flex">
            {user?.userRole === "Admin" && (
              <Button
                className={`px-16 hover:bg-primary hover:text-white  ${
                  pathname.includes("/Configurations/CompanyUserManage")
                    ? "bg-primary text-white"
                    : "bg-[#07838F33] border border-primary text-primary"
                }`}
                size="lg"
                onClick={() =>
                  navigate(
                    "/Settings/CompanyProfile/Configurations/CompanyUserManage"
                  )
                }
              >
                {t("Manage Users")}
              </Button>
            )}
            {user?.userRole === "Admin" && (
              <Button
                className={`px-16 hover:bg-primary hover:text-white  block mx-auto xl:mx-0 mt-5 xl:mt-0  ${
                  pathname.includes("/Configurations/CompanyLogs")
                    ? "bg-primary text-white"
                    : "bg-[#07838F33] border border-primary text-primary"
                }`}
                size="lg"
                onClick={() =>
                  navigate(
                    "/Settings/CompanyProfile/Configurations/CompanyLogs"
                  )
                }
              >
                {t("Company Logs")}
              </Button>
            )}
            {user?.userRole === "Admin" && (
            <Button
              className={`hover:bg-primary hover:text-white block mx-auto xl:mx-0 mt-5 xl:mt-0 ${
                pathname.includes("/Configurations/DepartmentsandProjects")
                  ? "bg-primary text-white"
                  : "bg-[#07838F33] border border-primary text-primary"
              }`}
              size="lg"
              onClick={() =>
                navigate("/Settings/CompanyProfile/Configurations/DepartmentsandProjects")
              }
            >
              {t("Departments and Projects")}
            </Button>
            )}
            <Button
              className={`px-24 hover:bg-primary hover:text-white block mx-auto xl:mx-0 mt-5 xl:mt-0 ${
                pathname.includes("/Configurations/Assets")
                  ? "bg-primary text-white"
                  : "bg-[#07838F33] border border-primary text-primary"
              }`}
              size="lg"
              onClick={() =>
                navigate("/Settings/CompanyProfile/Configurations/Assets")
              }
            >
              {t("Assets")}
            </Button>
            <Button
              className={` hover:bg-primary hover:text-white block mx-auto xl:mx-0 mt-5 xl:mt-0 ${
                pathname.includes("/Configurations/CustomFactor")
                  ? "bg-primary text-white"
                  : "bg-[#07838F33] border border-primary text-primary"
              }`}
              size="lg"
              onClick={() =>
                navigate("/Settings/CompanyProfile/Configurations/CustomFactor")
              }
            >
              {t("Emission Factor Selection")}
            </Button>
          </div>
        </>
        <>
          <CustomFactorForm
            uniqueActivites={uniqueActivites}
            uniqueUom={uniqueUom}
            uniqueEFactor={uniqueEFactor}
            uniqueActivitesList={uniqueActivitesList}
            uniqueEfactorList={uniqueEfactorList}
            uniqueUomList={uniqueUomList}
            assetListState={assetListState}
            assetNamesList={assetNamesList}
            fetchAgain={getFactors}
            assetTypes={assetTypes}
            setAssetTypes={setAssetTypes}
            tableData={customerFactorData}
            assignEmissionId={filterTableData}
            resetEmissionId={() => setfilterTableData2([])}
          />

          <hr className="mt-3 bg-[#D1D1D1] h-[2px]" />
        </>
        <div className="mt-3">
          {loading ? (
            <Loading />
          ) : (
            <CustomFactorDataTable
              companyAssets={assetTypes}
              tableData={customerFactorData}
              assetsList={assetNamesList}
              fetchAgain={getFactors}
            />
          )}
        </div>
      </MainLayout>
    </>
  );
}
