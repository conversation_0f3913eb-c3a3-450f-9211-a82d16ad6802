export default function CustomTooltip({
  step,
  index,
  size,
  backProps,
  closeProps,
  primaryProps,
  skipProps,
  tooltipProps,
  continuous,
  isLastStep,
}) {
  return (
    <div
      {...tooltipProps}
      className="max-w-md w-full rounded-lg shadow-xl p-4 bg-white text-gray-800 relative"
    >
      {/* Step Indicator */}
      <div className="text-xs text-gray-400 mb-2">
        Step {index + 1} of {size}
      </div>

      {/* Close Button */}
      <button
        {...closeProps}
        className="absolute top-2 right-2 text-gray-400 hover:text-gray-700 text-lg"
      >
        ×
      </button>

      {/* Title Bold */}
      {step.title && (
        <h3 className="text-base font-semibold mb-1">{step.title}</h3>
      )}

      {/* Subtitle or Description */}
      {step.description && (
        <p className="text-sm text-gray-600 mb-4">{step.description}</p>
      )}

      {/* Footer */}
      <div className="flex justify-between items-center gap-2">
        <button
          {...skipProps}
          className="text-sm text-gray-500 hover:underline"
        >
          Skip
        </button>

        <div className="ml-auto flex gap-2">
          {index > 0 && (
            <button
              {...backProps}
              className="px-4 py-1 border border-gray-300 text-sm rounded-md hover:bg-gray-50"
            >
              Back
            </button>
          )}
          <button
            {...primaryProps}
            className="px-4 py-1 bg-teal-600 text-white text-sm rounded-md hover:bg-teal-700"
          >
            {isLastStep ? "Finish" : "Next"}
          </button>
        </div>
      </div>
    </div>
  );
}
