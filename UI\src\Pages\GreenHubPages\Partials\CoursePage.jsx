import { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import useSideBarRoute from '@/hooks/useSideBarRoute';
import MainLayout from '@/Layout/MainLayout';
import Loading from '@/Components/Loading';
import ErrorMessage from '@/Components/Course/ErrorMessage';
import CourseContent from '@/Components/Course/CourseContent';
import { CourseContext } from '@/Contexts/CourseContext';
import { Link } from 'react-router-dom';
import { useAuth } from '@/Contexts/AuthContext';
import { IoMdHome } from "react-icons/io";
export default function CoursePage() {
  const { t } = useTranslation();

  const { course, loading, error } = useContext(CourseContext);
  const { GreenHubMenu } = useSideBarRoute();
  const { user } = useAuth();
  const data = course;

  return (
    <MainLayout menus={GreenHubMenu} navbarTitle={t('Green Hub')}
        breadcrumbItems={[
            { title: <IoMdHome size={20}/>, href: "/get-started" },
            { title: "Course Page", href: "#" },
        ]}
    >
      <div className="my-4">
        {data && <h1 className="px-8 py-4 text-xl font-bold bg-white rounded-2xl text-[#0C3360] mb-7 shadow-lg">LevelUp Academy</h1>}

        {loading ? (
          <Loading />
        ) : error ? (
          <ErrorMessage message={error} />
        ) : data ? (
          <CourseContent />
        ) : (
          <ErrorMessage message={t('No course data found.')} />
        )}
        <div className="flex flex-col justify-center items-center text-center">
          {data?.completed && data?.progressPercentage >= 100 && <h1 className="font-bold mb-2">Congratulations, You have Completed the course</h1>}
          {data?.completed && data?.progressPercentage >= 100 && (
            <Link
              className="text-white bg-[#05808b] px-4 py-2 hover:bg-[#1b4d52] duration-300 rounded"
              to={`https://www.levelupesg.co/academy/certificate/${course.courseId._id}?id=${user.userId}`}
              target="_blank"
            >
              Get My Certification
            </Link>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
