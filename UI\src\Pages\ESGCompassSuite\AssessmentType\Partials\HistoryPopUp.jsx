// CustomPopup.js
import ApiS1Config from "@/Api/apiS1Config";
import Loading from "@/Components/Loading";
import Share from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/Share";
import ViewPDF from "@/Pages/S2ZeroNetPages/NetZeroReport/Components/ViewPDF";
import { Button, Modal, ScrollArea, Table } from "@mantine/core";
import { useEffect, useState } from "react";
import { FiDownload } from "react-icons/fi";

function HistoryPopUp({ opened, close, assessmentType }) {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState();
  const getAssessmentHistory = async () => {
    try {
      setLoading(true);
      const { data } = await ApiS1Config.get("get_assessment_history", {
        headers: {
          assessmentName: assessmentType,
        },
      });
      setData(data?.all_assessment_summaries);
      setLoading(false);
      //console.log(data);
    } catch (error) {
      setLoading(false);
      //console.log(error);
    }
  };
  useEffect(() => {
    getAssessmentHistory();
  }, [assessmentType]);
  const rows = data?.map((item, idx) => (
    <Table.Tr
      key={`${idx + 1}-${idx}`}
      //  className={`${cx({
      //   // ["bg-[#07838F1A]"]: selected,
      //  })} text-sm font-bold text-[#626364] text-center`}
    >
      <Table.Td className="text-center">
        <p>{item.name}</p>
      </Table.Td>
      <Table.Td className="text-center">
        <p>
          {assessmentType === "GreenSight AI"
            ? item.assessment_score?.score_percentage
            : assessmentType === "SDG Assessment"
            ? item.assessment_score?.alignment_assessment.average_score
            : assessmentType === "ESG Due Diligence"
            ? item.assessment_score?.overall_score
            : item.assessment_score?.average_score}
        </p>
      </Table.Td>
      {assessmentType === "SDG Assessment" && (
        <Table.Td className="text-center">
          <p>{item.assessment_score?.alignment_assessment.coverage_score}</p>
        </Table.Td>
      )}
      <Table.Td className="text-center">
        <p>
          {assessmentType === "GreenSight AI"
            ? item.assessment_score?.level
            : assessmentType === "SDG Assessment"
            ? item.assessment_score?.impact_measurement.overall_weighted_impact
            : assessmentType === "ESG Due Diligence"
            ? item.assessment_score?.overall_coverage
            : item.assessment_score?.coverage_score}
        </p>
      </Table.Td>
      {assessmentType === "SDG Assessment" && (
        <Table.Td className="text-center">
          <p>
            {item.assessment_score?.impact_measurement.overall_progress ||
              item.assessment_score?.impact_measurement}
          </p>
        </Table.Td>
      )}
      {assessmentType === "ESG Due Diligence" && (
        <Table.Td className="text-center">
          <p>{item.assessment_score?.assessment_level}</p>
        </Table.Td>
      )}
      {assessmentType === "ESG Due Diligence" && (
        <Table.Td className="text-center">
          <p>{item.assessment_score?.risk_category}</p>
        </Table.Td>
      )}
      <Table.Td className="mx-auto ">
        <div className="flex justify-center items-center  h-12 mx-auto">
          <ViewPDF
            btnStyle={
              item?.report_url
                ? "border-[1px] flex items-center px-2 justify-between  h-[40px] text-[17px] font-bold rounded-lg text-secondary-300 border-secondary-300 bg-white hover:text-white hover:bg-secondary-300"
                : "border-[1px] flex items-center px-2 justify-between h-[40px] text-[17px] font-bold rounded-lg text-white border-gray-300 bg-gray-300 hover:text-white hover:bg-gray-300 cursor-not-allowed"
            }
            pdfUrl={item?.report_url}
            text={"View Report"}
            disabled={!item?.report_url && true}
          />
        </div>
      </Table.Td>
      <Table.Td className="mx-auto">
        <div className="flex justify-center items-center">
          <Share
            link={item?.report_url}
            btnStyle={
              item?.report_url
                ? "border-[1px] flex items-center px-2 justify-between  h-[40px] text-[17px] font-bold rounded-lg text-secondary-300 border-secondary-300 bg-white hover:text-white hover:bg-secondary-300"
                : "border-[1px] flex items-center px-2 justify-between h-[40px] text-[17px] font-bold rounded-lg text-white border-gray-300 bg-gray-300 hover:text-white hover:bg-gray-300 cursor-not-allowed"
            }
            disabled={!item?.report_url && true}
          />
        </div>
      </Table.Td>
      <Table.Td className="mx-auto">
        <div className="flex justify-center items-center">
          <Button
            component="a"
            className={
              item?.report_url
                ? "border-[1px] flex items-center px-4 justify-between h-[40px] text-sm font-bold rounded-lg text-white bg-secondary-300 ease-out duration-200 hover:translate-y-2 hover:bg-secondary-400"
                : "border-[1px] flex items-center px-2 justify-between h-[40px] text-[17px] font-bold rounded-lg text-white border-gray-300 bg-gray-300 hover:text-white hover:bg-gray-300 cursor-not-allowed"
            }
            href={item?.report_url}
            download
            disabled={!item?.report_url ? true : false}
          >
            Download Report
            <span>
              <FiDownload className="text-lg ms-1" />
            </span>
          </Button>
        </div>
      </Table.Td>
    </Table.Tr>
  ));
  return (
    <Modal
      opened={opened}
      onClose={close}
      centered
      size={"100%"}
      withCloseButton={true}
      title={assessmentType}
    >
      {loading ? (
        <Loading />
      ) : (
        <>
          {/* <h1>{assessmentType || "assessmentName"}</h1> */}
          <ScrollArea>
            <Table
              miw={800}
              //  mih={600}
              verticalSpacing="sm"
              className="p-2 my-1 bg-white  rounded-xl"
            >
              <Table.Thead className="border-b-2 border pb-6 text-secondary-500 font-bold text-base text-center">
                <Table.Tr>
                  <Table.Th className="text-center">Date</Table.Th>
                  <Table.Th
                    className="text-center"
                    colSpan={assessmentType === "SDG Assessment" && 2}
                  >
                    {assessmentType === "GreenSight AI"
                      ? "Score Percentage"
                      : assessmentType === "SDG Assessment"
                      ? "Alignment Assessment"
                      : "Average Score"}
                  </Table.Th>
                  <Table.Th
                    className="text-center"
                    colSpan={assessmentType === "SDG Assessment" && 2}
                  >
                    {assessmentType === "GreenSight AI"
                      ? "Level"
                      : assessmentType === "SDG Assessment"
                      ? "Impact Measurement"
                      : "Coverage Score"}
                  </Table.Th>

                  {assessmentType === "ESG Due Diligence" && (
                    <Table.Th className="text-center">
                      Assessment Level
                    </Table.Th>
                  )}
                  {assessmentType === "ESG Due Diligence" && (
                    <Table.Th className="text-center">Risk Category</Table.Th>
                  )}
                  <Table.Th className="text-center">View report</Table.Th>
                  <Table.Th className="text-center">Share</Table.Th>
                  <Table.Th className="text-center">Download report</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>{rows}</Table.Tbody>
            </Table>
          </ScrollArea>
        </>
      )}
    </Modal>
  );
}

export default HistoryPopUp;
