import Loading from "@/Components/Loading";
import { useCsrdContext } from "@/Contexts/CsrdContext";
import MainLayout from "@/Layout/MainLayout";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import { <PERSON><PERSON>, Tooltip } from "@mantine/core";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { GoShare } from "react-icons/go";
import { Outlet, useLocation } from "react-router";
import CsrdReport from "./Partials/CsrdReport";
import { IoMdHome } from "react-icons/io";
import GuideModalButton from "@/Components/Guide/GuideModalButton";
import Guide from "./DashboardGuide";

const Csrd = () => {
    const { csrdReadinessMenu } = useSideBarRoute();
    const { selectedScope, reportLoading, getReport, CSRDScopesData } =
        useCsrdContext();
    const { pathname } = useLocation();
    const { t } = useTranslation();
    const trueCount = CSRDScopesData?.some((item) =>
        item?.scope?.some((items) => items.solved === true)
    );
    // console.log(trueCount);

    const [active, setActive] = useState("Dashboard");

    return (
        <MainLayout
            menus={csrdReadinessMenu}
            navbarTitle={"CSRD Readiness"}
            breadcrumbItems={[
                { title: <IoMdHome size={20} />, href: "/get-started" },
                {
                    title: "Regulatory Readiness",
                    href: "/Grc/regulatory-readiness",
                },
                { title: "CSRD Readiness", href: "#" },
            ]}
        >
            <div className="grid lg:grid-cols-2 justify-center bg-[#FFFFFF] border-2 border-[#E8E7EA] p-6 gap-5 py-3 mb-6 rounded-lg text-primary">
                <button
                    className={`relative text-lg font-semibold py-3 px-6 transition-all ${
                        active === "Dashboard"
                            ? "active-tab rounded"
                            : "text-[#5A5A5A] rounded-lg border-2"
                    }`}
                    onClick={() => setActive("Dashboard")}
                >
                    {t("Dashboard")}
                </button>

                <button
                    className={`relative text-lg font-semibold py-3 px-6 transition-all ${
                        active === "Report"
                            ? "active-tab rounded"
                            : "text-[#5A5A5A] rounded-lg border-2"
                    }`}
                    onClick={() => setActive("Report")}
                >
                    {t("Report")}
                </button>
            </div>

            {active == "Dashboard" && (
                <>
                    <div className="w-full justify-between flex items-center py-5 px-3">
                        <GuideModalButton buttonText="Dashboard Guide">
                            <Guide />
                        </GuideModalButton>
                    </div>
                    <div
                        className={`w-full p-3 md:p-6 my-5 bg-white rounded-lg shadow-md items-center ${
                            pathname ===
                            "/Grc/regulatory-readiness/csrd-dashboard"
                                ? "justify-start"
                                : !selectedScope &&
                                  pathname !=
                                      "/Grc/regulatory-readiness/csrd-dashboard"
                                ? "justify-center"
                                : "justify-between"
                        }`}
                    >
                        {!selectedScope &&
                        pathname !=
                            "/Grc/regulatory-readiness/csrd-dashboard" ? (
                            <Loading />
                        ) : (
                            <div>
                                {/* CSRD & ESRS Readiness Dashboard */}
                                {pathname ===
                                "/Grc/regulatory-readiness/csrd-dashboard" ? (
                                    <div className="flex justify-between items-center">
                                        <h1 className="font-bold md:text-2xl mb-2 text-black">
                                            CSRD & ESRS Readiness Dashboard
                                        </h1>
                                        <Tooltip
                                            multiline
                                            w={200}
                                            radius={"md"}
                                            withArrow
                                            transitionProps={{ duration: 200 }}
                                            label={
                                                !trueCount ? (
                                                    <span className="capitalize flex justify-center text-center">
                                                        Complete Assessment
                                                        First
                                                    </span>
                                                ) : (
                                                    <span className="capitalize flex justify-center text-center">
                                                        be careful you will
                                                        finish the all system
                                                    </span>
                                                )
                                            }
                                            // className={!trueCount ? "" : "hidden"}
                                        >
                                            <Button
                                                className={`text-white   rounded-md bg-primary  ${
                                                    !trueCount
                                                        ? "cursor-not-allowed opacity-50"
                                                        : "hover:bg-primary hover:opacity-90 "
                                                }`}
                                                size="sm"
                                                disabled={!trueCount}
                                                onClick={
                                                    reportLoading
                                                        ? ""
                                                        : () => {
                                                              getReport();
                                                          }
                                                }
                                            >
                                                {reportLoading ? (
                                                    <Loading />
                                                ) : (
                                                    "Assess"
                                                )}
                                            </Button>
                                        </Tooltip>
                                    </div>
                                ) : (
                                    <div>
                                        <div className="flex items-center justify-between">
                                            <div>
                                                <h1 className="font-bold md:text-2xl mb-2 text-black">
                                                    {selectedScope.title}{" "}
                                                    {selectedScope.name}
                                                </h1>
                                                <p className="font-normal text-[#667085]">
                                                    A descriptive body text
                                                    comes here
                                                </p>
                                            </div>
                                            <div className="mt-5 sm:mt-0   gap-5">
                                                <Button
                                                    className="duration-200 ease-out bg-transparent border-2 rounded-lg text-primary border-secondary-300 hover:-translate-y-2 hover:bg-secondary-400 hover:bg-transparent hover:text-primary outline-none focus:outline-none"
                                                    size="md"
                                                >
                                                    <GoShare className="me-2" />
                                                    {t("export")}
                                                </Button>

                                                {/* <Button
            className="text-indigo-950 rounded-lg bg-gradient-to-r from-[#FFF4D7] via-[#DADEFB] to-[#D8FFDC]  hover:text-indigo-950 hover:-translate-y-2 duration-200 ease-out"
            size="md"
            >
            AI Suggestion
            <TbSquarePlus2 className="ms-2 -rotate-90" />
            </Button> */}
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                    <Outlet />
                </>
            )}

            {active == "Report" && <CsrdReport />}
        </MainLayout>
    );
};

export default Csrd;
