import { Link } from "react-router-dom";

const Card = ({ cardTitle, cardTitleLink,viewAllLink, className, children }) => {
  return (
    <div className={`p-3 ${className}`}>
      <div className="flex justify-between px-1 -mt-10">
        <h2 className="font-bold text-md">{cardTitle}</h2>
        <Link to={viewAllLink} className="text-primary underline">
         {/* {cardTitleLink? cardTitleLink:'View All'} */}
        </Link>
      </div>
      <div className={`pt-4`}>{children}</div>
    </div>
  );
};

export default Card;
