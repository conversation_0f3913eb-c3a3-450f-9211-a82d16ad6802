import React from "react";
import { <PERSON><PERSON><PERSON>, LineChart } from "@mantine/charts";
import { RingProgress, Text } from "@mantine/core";
import { barChartData, lineChartData } from "./data";

export default function AnalyticsAndReportingDashboard() {
  return (
    <div className="grid grid-cols-3 gap-4 mt-6">
      <div className="col-span-3 bg-[#fff] rounded-lg shadow-lg w-full px-4 py-6">
        <div className="col-span-3 flex flex-row justify-between items-center mb-2">
          <p className="font-bold">Risk Trend</p>
          <button className="border-2 border-[#EDF2F7] text-[#000] font-semibold text-lg px-4 py-2 rounded-lg">
            Export
          </button>
        </div>
        <LineChart
          h={250}
          mt={10}
          data={lineChartData}
          dataKey="amount"
          withLegend
          legendProps={{
            layout: "vertical",
            align: "center",
            height: 0,
            verticalAlign: "top",
          }}
          tickLine="none"
          yAxisProps={{ tickMargin: 15, orientation: "left" }}
          xAxisProps={{ tickMargin: 5, orientation: "bottom" }}
          series={[
            { name: "Current", color: "#00C0A9" },
            { name: "Last", color: "#07838F" },
          ]}
          gridAxis="none"
          withDots={false}
        />
      </div>

      <div className="col-span-2 bg-[#fff] rounded-lg shadow-lg p-4">
        <BarChart
          h={200}
          data={barChartData}
          dataKey="Smartphones"
          orientation="vertical"
          yAxisProps={{ width: 80 }}
          barProps={{ radius: 50 }}
          series={[
            { name: "Smartphones", color: "#FF0000" },
            { name: "Laptops", color: "#AB0202" },
            { name: "Tablets", color: "#FFAB07" },
            { name: "Desktops", color: "#01BD36" },
          ]}
          tickLine="none"
          gridAxis="none"
          withXAxis={false}
          withYAxis={false}
        />
      </div>

      <div className="bg-[#fff] rounded-lg shadow-lg p-10">
        <p className="text-start font-bold">Overall ESG Risk Score</p>
        <div className="flex items-center justify-center p-4">
          <RingProgress
            size={150}
            thickness={20}
            roundCaps
            sections={[{ value: 70, color: "#00C0A9" }]}
            label={
              <Text c="#27272E" fw={700} ta="center" size="xl">
                75%
              </Text>
            }
          />
        </div>
      </div>
    </div>
  );
}
