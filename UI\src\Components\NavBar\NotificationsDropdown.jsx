import { useEffect, useRef } from "react";
import { useNotificationStore } from "@/Store/useNotificationStore";
import {
  ScrollArea,
  Skeleton,
  Text,
  Badge,
  Loader,
  Alert,
} from "@mantine/core";
import { IconBell, IconX } from "@tabler/icons-react";
import { motion } from "framer-motion";
import { AnimatePresence } from "framer-motion";
import { useSocket } from "@/hooks/useSocket";

export const NotificationsDropdown = ({ isOpen, onClose }) => {
  const dropdownRef = useRef(null);

  useEffect(() => {

    if (!isOpen) return;
    const handleClickOutside = (event) => {
      const isTrigger = event.target.closest('[data-notification-trigger]');
      if (isTrigger) return;

      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        onClose();
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };

  }, [isOpen, onClose]);
  
  const {
    notifications,
    isLoading,
    error,
    fetchNotifications,
    markMessageAsSeen,
    ReadAllNotifications,
  } = useNotificationStore();

  const { socket } = useSocket();

  const dropdownVariants = {
    hidden: {
      opacity: 0,
      y: -10,
      transition: { duration: 0.2 },
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.2 },
    },
    exit: {
      opacity: 0,
      y: -10,
      transition: { duration: 0.2 },
    },
  };

  // Fetch notifications when dropdown opens
  useEffect(() => {
    if (isOpen && notifications.length === 0 && !isLoading) {
      // fetchNotifications();   //TEMP-FIX
    }
  }, [isOpen]);
  
  const handleReadAllNotifications = () => {
    ReadAllNotifications();
    notifications?.forEach((element) => {
      socket?.emit("mark_notification_as_read", { notificationId: element.notificationId });
    });
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          ref={dropdownRef} 
          variants={dropdownVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className="absolute right-0 top-12 w-80 bg-white dark:bg-[#1E1E1E] text-gray-700 dark:text-white rounded-lg shadow-lg border border-gray-200 overflow-hidden z-50"
          onClick={(e) => e.stopPropagation()} // Prevent click outside close
        >
          {/* Keep your existing dropdown content unchanged */}
          <div className="p-3 bg-secondary border-b border-gray-200 flex items-center justify-between">
            <h3 className="font-medium text-white">Notifications</h3>
            {isLoading && <Loader size="xs" />}
          </div>

          {error && (
            <Alert icon={<IconX />} title="Error" color="red" m={10}>
              {error}
            </Alert>
          )}

          <ScrollArea h={300}>
            <motion.div
              className="p-2 space-y-2 overflow-y-auto h-full"
              variants={dropdownVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
            >
              {isLoading ? (
                Array(3)
                  .fill(0)
                  .map((_, i) => <Skeleton key={i} height={40} radius="md" />)
              ) : notifications.length === 0 ? (
                <Text c="dimmed" align="center" py={20}>
                  No notifications found
                </Text>
              ) : (
                notifications.map((notif, index) => (
                  <NotificationMessage key={index} notification={notif} socket={socket} />
                ))
              )}
            </motion.div>
          </ScrollArea>

          {notifications.length > 0 && (
            <motion.div
              className="p-2 border-t border-gray-200 text-center"
              variants={dropdownVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
            >
              <button
                className="text-sm text-blue-600 hover:text-blue-800 w-full h-full"
                onClick={() => {
                  handleReadAllNotifications();
                }}
              >
                Read All
              </button>
            </motion.div>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
};



const NotificationMessage = ({ notification , socket }) => {
  const { markMessageAsSeen } = useNotificationStore();

  const handleMarkMessageAsSeen = (notificationId) => {
    markMessageAsSeen(notificationId);
    socket.emit("mark_notification_as_read", { notificationId });
  };

  return (
    <div 
    className="p-2 hover:bg-gray-100 rounded-md transition-colors cursor-pointer"
    onClick={() => {
      handleMarkMessageAsSeen(notification.notificationId);
    }}
    >
      <div className="flex justify-between items-start">
        <Text fw={500}>{notification.title}</Text>
        {!notification.seen && (
          <Badge size="xs" color="blue" variant="filled">
            New
          </Badge>
        )}
      </div>
      <Text size="sm" c="dimmed">
        {notification.message}
      </Text>
      <Text size="xs" c="gray" mt={4}>
        {new Date(notification.createdDate).toLocaleDateString()}
      </Text>
    </div>
  );
};