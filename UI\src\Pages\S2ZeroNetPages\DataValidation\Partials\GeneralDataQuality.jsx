import { <PERSON><PERSON>, <PERSON>ton, Group, ScrollArea, Table, Text } from "@mantine/core";
import { IconUpload, IconPhoto, IconX } from "@tabler/icons-react";
import { Dropzone, IMAGE_MIME_TYPE } from "@mantine/dropzone";
import { SuccessAlertIcon } from "@/assets/icons/DataFoundationIcons";
import { HiOutlineFolderDownload } from "react-icons/hi";
import { Quality_Dimensions } from "./Constants";
const GeneralDataQuality = () => {
  const rows = Quality_Dimensions.map((item) => (
    <Table.Tr key={item.dimension}>
      <Table.Td>{item.dimension}</Table.Td>
      <Table.Td>{item.weight}</Table.Td>
      <Table.Td>{item.score}</Table.Td>
      <Table.Td>{item.description}</Table.Td>
    </Table.Tr>
  ));
  return (
    <div>
      <div className="bg-white p-2 rounded-xl mb-5">
        <b>Upload file</b>
        <Dropzone
          onDrop={(files) => console.log("accepted files", files)}
          onReject={(files) => console.log("rejected files", files)}
          maxSize={5 * 1024 ** 2}
          accept={IMAGE_MIME_TYPE}
          className="mt-3 border-2 border-dashed rounded-xl bg-white"
        >
          <Group
            justify="center"
            gap="xl"
            mih={220}
            style={{ pointerEvents: "none", flexDirection: "column" }}
          >
            <Dropzone.Accept>
              <IconUpload
                size={52}
                color="var(--mantine-color-blue-6)"
                stroke={1.5}
              />
            </Dropzone.Accept>
            <Dropzone.Reject>
              <IconX
                size={52}
                color="var(--mantine-color-red-6)"
                stroke={1.5}
              />
            </Dropzone.Reject>
            <Dropzone.Idle>
              <IconPhoto
                size={52}
                color="var(--mantine-color-dimmed)"
                stroke={1.5}
              />
            </Dropzone.Idle>

            <div>
              <Text size="xl" inline>
                Upload or drag a file here
              </Text>
            </div>
          </Group>
        </Dropzone>
        <p>The file size must not exceed 10 MB and be in PDF format.</p>
        <Button className="bg-primary ml-auto block">Analyse Data</Button>
      </div>

      <Alert
        variant="light"
        color="#07838F1A"
        title=""
        icon={<SuccessAlertIcon />}
      >
        <span className="text-primary font-bold">
          Data quality assessment complete
        </span>
      </Alert>

      <div className="bg-white p-3 rounded-xl border-2 my-5">
        <div className="flex justify-between items-center font-bold mb-4">
          <span>Quality Dimensions</span>

          <Button
            className="bg-lightBg"
            leftSection={<HiOutlineFolderDownload color="#555F62" size={20} />}
            variant="default"
          >
            Download Full Report
          </Button>
        </div>

        <ScrollArea>
            <Table miw={900} withColumnBorders withRowBorders withTableBorder horizontalSpacing={'lg'} verticalSpacing={'xl'}>
            <Table.Thead bg={"#F5F4F5"}>
                <Table.Tr>
                <Table.Th>Dimension</Table.Th>
                <Table.Th>Weight</Table.Th>
                <Table.Th>Score</Table.Th>
                <Table.Th>Description</Table.Th>
                </Table.Tr>
            </Table.Thead>
            <Table.Tbody>{rows}</Table.Tbody>
            </Table>
        </ScrollArea>

        <div className="bg-bg-lite2 p-3 rounded-xl mt-3">
        Overall DQS: 0.66 <b className="text-primary">(Good Quality)</b>
        </div>
      </div>
    </div>
  );
};

export default GeneralDataQuality;
