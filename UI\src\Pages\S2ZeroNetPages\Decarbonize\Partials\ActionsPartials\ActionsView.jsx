import ApiS2 from "@/Api/apiS2Config";
import Loading from "@/Components/Loading";
import { Button } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { useEffect, useState } from "react";
import { FaPlus } from "react-icons/fa";
import ActionsBestPractice from "./Partials/ActionsBestPractice";
import ActionsModal from "./Partials/ActionsModal";
import ActionsTable from "./Partials/ActionsTable";
import { useTranslation } from "react-i18next";
// import { driver } from "driver.js";
// import "driver.js/dist/driver.css";
// import "@/assets/css/index.css";
// import { FaQuestionCircle } from "react-icons/fa";

const ActionsView = ({
  assignees,
  assigneesData,
  assetTypes,
  loading,
  allAssetType,
  setLoading,
  activeTab,
}) => {
  const [opened, { open, close }] = useDisclosure(false);
  const [data2, setdata2] = useState([]);
  // const { t } = useTranslation();
  // const [elementsReady, setElementsReady] = useState(false);

  const getClimateActions = async () => {
    try {
      const { data } = await ApiS2.get("/decarbonize/get_all_climate_actions");
      setLoading(false);
      setdata2(data);
    } catch (er) {
      setLoading(false);
    }
  };

  useEffect(() => {
    getClimateActions();
    allAssetType();
    assigneesData();
  }, [loading]);

  const refetchAgain = (value) => {
    // setLoading((p) => (p = value));
    getClimateActions();
  };

  // const getGuideSteps = () => [
  //   {
  //     element: ".Add-Actions-to-Achieve-Your-Goals",
  //     popover: {
  //       title: t("Add Actions to Achieve Your Goals"),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Click-Add-New-Action",
  //     popover: {
  //       title: t("Click 'Add Custom Action'."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Data-Table-View22",
  //     popover: {
  //       title: t("Data Table View"),
  //       description: t("Uploaded data appears in a structured table with the following actions:"),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Data-Table-Edit22",
  //     popover: {
  //       title: t("Edit"),
  //       description: t("Correct or update any field."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Data-Table-Delete22",
  //     popover: {
  //       title: t("Delete"),
  //       description: t("Remove any wrong entry."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  // ];

  // const checkElementsExist = () => {
  //   const steps = getGuideSteps();
  //   const allElementsExist = steps.every((step) =>
  //     document.querySelector(step.element)
  //   );
  //   return allElementsExist;
  // };

  // useEffect(() => {
  //   if (activeTab === "actions") {
  //     const observer = new MutationObserver(() => {
  //       if (checkElementsExist()) {
  //         setElementsReady(true);
  //         observer.disconnect();
  //       }
  //     });

  //     observer.observe(document.body, {
  //       childList: true,
  //       subtree: true,
  //     });

  //     return () => observer.disconnect();
  //   }
  // }, [activeTab]);

  // const startGuide = () => {
  //   const driverObj = driver({
  //     showProgress: true,
  //     popoverClass: "my-custom-popover-class",
  //     nextBtnText: "Next",
  //     prevBtnText: "Back",
  //     doneBtnText: "Done",
  //     progressText: "Step {{current}} of {{total}}",
  //     overlayColor: "rgba(0, 0, 0, 0)",
  //     steps: getGuideSteps(),
  //     onDestroyStarted: () => {
  //       localStorage.setItem("hasSeenDecarbonizeActionsGuide", "true");
  //       driverObj.destroy();
  //     },
  //   });
  //   driverObj.drive();
  // };

  // useEffect(() => {
  //   const hasSeenGuide = localStorage.getItem("hasSeenDecarbonizeActionsGuide");
  //   if (!hasSeenGuide) {
  //     startGuide();
  //   }
  //   return () => {
  //     const driverObj = driver();
  //     if (driverObj.isActive()) {
  //       driverObj.destroy();
  //     }
  //   };
  // }, [activeTab, elementsReady]);

  return (
    <>
          {/* <div
        onClick={startGuide}
        style={{
          position: "fixed",
          bottom: 20,
          right: 20,
          cursor: "pointer",
          zIndex: 1000,
        }}
      >
        <div className="h-12 w-12 flex items-center justify-center bg-gradient-to-tr from-[#2C5A8C] via-[#1C889C] to-[#13B1A8] p-1  cursor-pointer rounded-full">
          <FaQuestionCircle
            size={34}
            color="#ffffff"
            className="mx-auto cursor-pointer"
          />
        </div>
      </div> */}
      <div className="flex flex-col md:flex-row justify-between items-center mb-7">
        <div className="py-3">
          <h2 className="md:text-3xl font-bold my-3 Data-Table-View22">Actions</h2>
          <p className="font-semibold">
            Implement and track emission reduction initiatives to achieve your
            sustainability goals
          </p>
        </div>
        <Button
          leftSection={<FaPlus size={12} />}
          variant="filled"
          size="sm"
          radius="md"
          className="mt-auto ms-2 mb-2 bg-primary hover:bg-secondary Click-Add-New-Action"
          onClick={open}
        >
          Add Custom Action
        </Button>

        <ActionsModal
          isOpen={opened}
          onRequestClose={close}
          refetchAgain={refetchAgain}
          assetTypes={assetTypes}
          allAssignees={assignees}
        />
      </div>
      {loading ? (
        <Loading />
      ) : (
        <ActionsTable
          tableData={data2}
          assetTypes={assetTypes}
          refetchAgain={refetchAgain}
          allAssignees={assignees}
        />
      )}
      <ActionsBestPractice />
    </>
  );
};

export default ActionsView;
