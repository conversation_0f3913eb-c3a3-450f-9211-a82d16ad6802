import Loading from "@/Components/Loading";
import S3Layout from "@/Layout/S3Layout";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import {
  Button,
  Modal,
  Radio,
  Select,
  TagsInput,
  TextInput,
  Textarea,
  rem,
} from "@mantine/core";
import { DateInput, TimeInput } from "@mantine/dates";
import { isNotEmpty, useForm } from "@mantine/form";
import { useDisclosure } from "@mantine/hooks";
import { useEffect, useState } from "react";
import { IoArrowDownOutline } from "react-icons/io5";
import { useNavigate, useParams } from "react-router";
import AddNewIncidentTable from "./Partials/AddNewIncidentTable";
import { UploadFile } from "./Partials/UploadFile";
import ApiS3 from "@/Api/apiS3";
import { notifications } from "@mantine/notifications";

export default function AddNewIncident() {
  const { greenShieldESGIncidentManagement } = useSideBarRoute();
  const [Anonymous, setAnonymous] = useState(false);
  const [anonymousDetails, setAnonymousDetails] = useState({});
  const [file, setFiles] = useState(null);
  const [tableData, setTableData] = useState({});
  const [opened, { open, close }] = useDisclosure(false);
  const [loading, setLoading] = useState(false);
  const [selectedValue, setSelectedValue] = useState("yes");
  const { id } = useParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState("New");
  const [closureDate, setClosureDate] = useState(new Date());

  const handleStatusChange = (e) => {
    setStatus(e.target.value);
  };

  const selectClasses = `${
    status === "In progress"
      ? "bg-[#fff0b8] text-[#FFAB07] md:px-3"
      : status === "New"
      ? "bg-[#cafac2] text-[#70D162] md:px-3"
      : status === "Resolved"
      ? "bg-[#cea7f6] text-[#9160C1] md:px-3"
      : status === "Closed"
      ? "bg-red-300 text-red-800 md:px-3"
      : ""
  } text-base font-medium px-3 py-2 rounded-xl border`;
  const today = new Date();
  const formattedDate = new Intl.DateTimeFormat("en-GB", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  }).format(today);
  const form = useForm({
    initialValues: {
      title: "",
      location: "",
      priority: "",
      interactionDescription: "",
      interactionType: "",
      status: "",
      closureDate: "",
      anonymous: false,
      reporter: {},
      involvedPersons: [],
      evidenceFiles: [],
      relationShip: "",
      remarks: "",
      timeOfInteraction: "",
      dateOfInteraction: "",
    },
    // validate: {
    //   title: isNotEmpty(),
    //   interactionDescription: isNotEmpty(),
    //   interactionType: isNotEmpty(),
    //   location: isNotEmpty("Enter Your Location"),
    //   priority: isNotEmpty("Enter Your  Priority"),
    //   status: isNotEmpty("Enter Your Status"),
    // },
  });

  console.log("we are here in add new incident");

  const handleNewIncidentSubmit = async (values) => {
    setLoading(true);

    try {
      const formdata = {
        title: values.title,
        interactionType: values.interactionType,
        priority: values.priority,
        interactionDescription: values.interactionDescription,
        interactionLocation: values.interactionLocation,
        status: values.status,
        closureDate: values.closureDate,
        involvedPersons: values.involvedPersons,
        relationShip: values.relationShip,
        remarks: values.remarks,
        reporter: values.reporter,
        timeOfInteraction: values.timeOfInteraction,
        dateOfInteraction: values.dateOfInteraction,
      };
      console.log(formdata);
      const { data } = await ApiS3.post("/stakeholder-interactions", formdata);

      notifications.show({
        title: "Success",
        message: data.message,
        color: "teal",
      });

      navigate("/green-shield/financial/ESG-incident-management");
      form.reset();
    } catch (error) {
      const errorMessages = error.response?.data?.errors || [
        error.response?.data?.error,
      ];
      errorMessages.forEach((message) =>
        notifications.show({
          title: "Error",
          message,
          color: "red",
        })
      );
    } finally {
      setLoading(false);
    }
  };

  const modalForm = useForm({
    initialValues: {
      firstName: "",
      lastName: "",
      phoneNumber: "",
      emailAddress: "",
      password: "",
    },
  });
  const handelModalSubmit = (values) => {
    setAnonymousDetails(values);
    modalForm.reset();
    close();
  };
  useEffect(() => {
    form.setValues({
      anonymous: Anonymous,
      reporter: Anonymous === true ? anonymousDetails : null,
      evidences: file,
      involvedPersons: tableData,
      status: status,
      closureDate: closureDate ? Date(closureDate) : "",
    });
  }, [anonymousDetails, file, tableData, Anonymous, status, closureDate]);

  return (
    <S3Layout menus={greenShieldESGIncidentManagement}>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-10 ">
        {/* top section */}
        <div className="lg:col-span-2 bg-white rounded-lg p-5 md:flex justify-between shadow-md">
          {/* left {Incident ID ,Created on ,status ,...etc} section */}
          <div className="">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-black me-1">
                Incident :
              </h1>
              <TextInput
                {...form.getInputProps("title")}
                variant={form.errors.title ? "styled" : `unstyled`}
                key={form.key("title")}
                placeholder="Add Title..."
                size="md"
                styles={{
                  error: { display: "none" },
                }}
              />
            </div>
            <p className="text-base font-medium text-primary text-start">
              Incident ID: {id}
            </p>
          </div>
          <div className="flex items-center justify-center gap-10 mt-5 md:mt-0">
            <div className="text-center">
              <h1 className="text-base font-bold">Created on</h1>
              <p className="text-base font-bold">{formattedDate}</p>
            </div>
            <div>
              <select
                className={`focus:outline-0 ${selectClasses}`}
                value={status}
                onChange={handleStatusChange}
              >
                {/* ["In Progress","New","Resolved","Closed"] */}
                <option value="In Progress">In Progress</option>
                <option value="New">New</option>
                <option value="Resolved">Resolved</option>
                <option value="Closed">Closed</option>
              </select>
            </div>
          </div>
        </div>
        {/* End left {Incident ID ,Created on ,status ,...etc} section */}

        {/* right { Anonymous ,Submit ,cancel} section */}
        <div className="lg:col-span-1 bg-white w-full rounded-lg grid lg:grid-cols-3 items-start p-4 gap-3 shadow-md">
          <div className="">
            <p className="px-6 py-2 rounded-lg text-[#00C0A9] text-sm border-2 border-[#00C0A9] bg-[#00C0A926]">
              Anonymous
            </p>

            <Radio.Group
              value={selectedValue}
              onChange={setSelectedValue}
              name="anonymousRadio"
              className=" mt-3"
            >
              <div className="flex justify-around">
                <Radio
                  value="yes"
                  label="Yes"
                  color="#00C0A9"
                  variant="outline"
                  labelPosition="left"
                  className=""
                  onChange={() => {
                    setAnonymous(false);
                    close();
                  }}
                />
                <Radio
                  value="no"
                  label="No"
                  color="#00C0A9"
                  variant="outline"
                  labelPosition="left"
                  className=""
                  onChange={() => {
                    setAnonymous(true);
                    open();
                  }}
                />
              </div>
            </Radio.Group>
          </div>

          <Button
            className="rounded-lg bg-primary text-white hover:bg-primary hover:text-white"
            size="md"
            type="submit"
            onClick={form.onSubmit(handleNewIncidentSubmit)}
          >
            {loading ? <Loading /> : "Submit"}
          </Button>
          {/* <Button
            className="rounded-lg bg-red-400 text-white hover:bg-red-400 hover:text-white"
            size="md"
            onClick={() =>
              navigate("/green-shield/financial/ESG-incident-management")
            }
          >
            Cancel
          </Button> */}
        </div>
        {/* right { Anonymous ,Submit ,cancel} section */}
      </div>
      {/*End top section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-10 mt-10 items-start">
        <div className="lg:col-span-2 bg-white rounded-lg p-5 md:flex justify-between shadow-md">
          <div className="grid grid-cols-1 lg:grid-cols-2 items-center w-full gap-x-10 gap-y-5 text-start">
            <TextInput
              key={form.key("interactionLocation")}
              {...form.getInputProps("interactionLocation")}
              radius="md"
              label="location"
              placeholder="Enter location"
            />
            <TextInput
              key={form.key("relationShip")}
              {...form.getInputProps("relationShip")}
              radius="md"
              label="relationShip"
              placeholder="What is your relation with LevelUp ESG ...?"
            />
            <Select
              key={form.key("priority")}
              {...form.getInputProps("priority")}
              data={["High", "Medium", "Low"]}
              label="Priority"
              placeholder="Select Priority"
              id="priority"
              name="priority"
              radius="md"
              mb="md"
              rightSectionPointerEvents="none"
              // closeonblur={false}
              rightSection={
                <IoArrowDownOutline
                  style={{ width: rem(12), height: rem(12), color: "#00C0A9" }}
                />
              }
            />
            <Select
              key={form.key("interactionType")}
              {...form.getInputProps("interactionType")}
              id="interactionType"
              name="interactionType"
              label="interaction Type"
              data={["Environmental", "Social", "Governance"]}
              placeholder="Select interaction Type"
              radius="md"
              mb="md"
              rightSectionPointerEvents="none"
              rightSection={
                <IoArrowDownOutline
                  style={{ width: rem(12), height: rem(12), color: "#00C0A9" }}
                />
              }
            />
            <DateInput
              {...form.getInputProps("closureDate")}
              key={form.key("closureDate")}
              placeholder="DD/MM/YYYY"
              valueFormat="DD/MM/YYYY"
              value={closureDate}
              onChange={setClosureDate}
              id="closureDate"
              name="closureDate"
              label="Closure Date"
              radius="md"
              className="font-semibold"
            />
            <TimeInput
              {...form.getInputProps("timeOfInteraction")}
              key={form.key("timeOfInteraction")}
              id="timeOfInteraction"
              name="timeOfInteraction"
              label="time Of Interaction"
              radius="md"
              className="font-semibold"
            />
            <DateInput
              {...form.getInputProps("dateOfInteraction")}
              key={form.key("dateOfInteraction")}
              placeholder="DD/MM/YYYY"
              valueFormat="DD/MM/YYYY"
              id="dateOfInteraction"
              name="dateOfInteraction"
              label="Date Of Interaction"
              radius="md"
              className="font-semibold"
            />
            <div className="lg:col-span-2">
              <Textarea
                key={form.key("interactionDescription")}
                {...form.getInputProps("interactionDescription")}
                label="interaction Description"
                placeholder="Enter Details..."
                minRows={1}
              />
            </div>
            <div className="lg:col-span-2">
              <Textarea
                key={form.key("remarks")}
                {...form.getInputProps("remarks")}
                label="interaction Remarks"
                placeholder="Enter Remarks..."
                minRows={1}
              />
            </div>
          </div>
        </div>
        <div className="lg:col-span-1 bg-white w-full rounded-lg  p-4 gap-3 shadow-md">
          <UploadFile
            onChange={(e) => {
              setFiles(e.target.files[0]);
            }}
          />
        </div>
      </div>
      {/*End sec {details ,upload Files} section */}

      {/* last section AddNewIncidentTable*/}

      <div>
        <AddNewIncidentTable onSetData={setTableData} />
      </div>
      {/* last section AddNewIncidentTable*/}

      {/* Anonymous modal */}
      <Modal
        opened={opened}
        title="Enter Your Data"
        centered
        withCloseButton={true}
        onClose={() => {
          close();
          setSelectedValue("yes");
          setAnonymous(true);
        }}
      >
        <div className="grid grid-cols-1 items-center w-full gap-x-10 gap-y-5 text-start">
          <TextInput
            key={modalForm.key("firstName")}
            {...modalForm.getInputProps("firstName")}
            radius="md"
            label="First Name"
            placeholder="Enter Your first Name"
          />
          <TextInput
            key={modalForm.key("lastName")}
            {...modalForm.getInputProps("lastName")}
            radius="md"
            label="Last Name"
            placeholder="Enter Your last Name"
          />
          <TextInput
            key={modalForm.key("phoneNumber")}
            {...modalForm.getInputProps("phoneNumber")}
            radius="md"
            label="phone Number"
            placeholder="Enter Your phone Number"
          />
          <TextInput
            key={modalForm.key("emailAddress")}
            {...modalForm.getInputProps("emailAddress")}
            radius="md"
            label="email Address"
            placeholder="Enter Your email Address"
          />
          <TextInput
            key={modalForm.key("password")}
            {...modalForm.getInputProps("password")}
            radius="md"
            label="password"
            placeholder="Enter Your password"
            type="password"
          />

          <Button
            className="rounded-lg bg-primary text-white hover:bg-primary hover:text-white"
            size="md"
            type="submit"
            onClick={modalForm.onSubmit(handelModalSubmit)}
          >
            Submit
          </Button>
        </div>
      </Modal>
      {/*End Anonymous modal */}
    </S3Layout>
  );
}
