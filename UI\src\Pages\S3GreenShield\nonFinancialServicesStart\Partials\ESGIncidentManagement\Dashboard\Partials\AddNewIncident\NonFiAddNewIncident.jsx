import S3Layout from "@/Layout/S3Layout";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import { Button, Checkbox, Modal, TextInput, Textarea } from "@mantine/core";
import React, { useEffect, useState } from "react";
import { UploadFile } from "./Partials/UploadFile";
import AddNewIncidentTable from "./Partials/AddNewIncidentTable";
import { useNavigate, useParams } from "react-router";
import { isNotEmpty, useForm } from "@mantine/form";
import { useDisclosure } from "@mantine/hooks";
import ApiS3 from "@/Api/apiS3";
import { showNotification } from "@mantine/notifications";
import {
  IconCheck as IconCheckNotification,
  IconX as IconXNotification,
} from "@tabler/icons-react";
import Loading from "@/Components/Loading";

export default function NonFiAddNewIncident() {
  const { greenShieldNonFiESGIncidentManagement } = useSideBarRoute();
  const [Anonymous, setAnonymous] = useState(false);
  const [anonymousDetails, setAnonymousDetails] = useState({});
  const [files, setFiles] = useState([]);
  const [tableData, setTableData] = useState({});
  const [opened, { open, close }] = useDisclosure(false);
  const [loading, setLoading] = useState(false);
  const { id } = useParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState("in_progress");

  const handleStatusChange = (e) => {
    setStatus(e.target.value);
  };

  const selectClasses = `${
    status === "in_progress"
      ? "bg-[#fff0b8] text-[#FFAB07] md:px-3"
      : status === "new"
      ? "bg-[#cafac2] text-[#70D162] md:px-3"
      : status === "resolved"
      ? "bg-[#cea7f6] text-[#9160C1] md:px-3"
      : status === "closed"
      ? "bg-red-300 text-red-800 md:px-3"
      : ""
  } text-base font-medium px-3 py-2 rounded-xl border`;

  const today = new Date();
  const day = String(today.getDate()).padStart(2, "0");
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const year = today.getFullYear();
  const formattedDate = `${day}/${month}/${year}`;
  // console.log(files);
  const form = useForm({
    mode: "uncontrolled",
    initialValues: {
      title: "",
      status: "",
      trackingId: id,
      Anonymous: "",
      details: {
        country: "",
        city: "",
        location: "",
        impactAssessment: "",
        message: "",
      },
      anonymousDetails: "",
      evidences: "",
      involvedIndividuals: "",
    },
    validate: {
      title: isNotEmpty(),
      details: {
        country: isNotEmpty("Enter Your Country"),
        city: isNotEmpty("Enter Your City"),
        location: isNotEmpty("Enter Your Location"),
        impactAssessment: isNotEmpty("Enter Your Impact Assessment"),
      },
    },
  });
  const handelNewIncidentSubmit = async (value) => {
    if (value.anonymousDetails === null) {
      delete value["anonymousDetails"];
      // console.log(value);
    }
    // console.log(value);
    setLoading(true);
    try {
      setLoading(true);
      // console.log(value);
      const { data } = await ApiS3.post(
        "/incident-management/incidents",
        value
      );
      // console.log(da);
      setLoading(true);

      if (data.message === "Incident's added successfully") {
        setLoading(false);
        showNotification({
          title: "Success",
          message: data.message,
          color: "teal",
          icon: <IconCheckNotification />,
        });
        navigate("/green-shield/NonFinancial/ESG-incident-management");
        form.reset();
      }

      // console.log(data);
    } catch (error) {
      setLoading(false);
      if (error.response.data.errors) {
        setLoading(false);
        error.response.data.errors.map((item) => {
          showNotification({
            title: "Error",
            message: (
              <span>
                {item.type} & {item.path} is required
              </span>
            ),
            color: "red",
            icon: <IconXNotification />,
          });
        });
      }
      if (error.response.data.error) {
        setLoading(false);
        // error.response.data.errors.map((item) => {
          showNotification({
            title: "Error",
            message: (
              <span>
                {error.response.data.error} is required
              </span>
            ),
            color: "red",
            icon: <IconXNotification />,
          });
        // });
      }
      console.log(error);
    }
  };

  const modalForm = useForm({
    initialValues: {
      name: "",
      role: "",
      contact: "",
    },
    validate: {
      name: isNotEmpty("Name is required"),
      role: isNotEmpty("Role is required"),
      contact: isNotEmpty("Contact is required"),
    },
  });
  const handelModalSubmit = (values) => {
    setAnonymousDetails(values);
    modalForm.reset();
    close();
  };
  useEffect(() => {
    form.setValues({
      anonymousDetails: Anonymous === true ? anonymousDetails : null,
      evidences: files,
      involvedIndividuals: tableData,
      status: status,
    });
  }, [anonymousDetails, files, tableData, Anonymous, status]);

  return (
    <S3Layout menus={greenShieldNonFiESGIncidentManagement}>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-10 ">
        {/* top section */}
        <div className="lg:col-span-2 bg-white rounded-lg p-5 md:flex justify-between shadow-md">
          {/* left {Incident ID ,Created on ,status ,...etc} section */}
          <div className="">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-black me-1">
                Incident :
              </h1>
              <TextInput
                {...form.getInputProps("title")}
                variant={form.errors.title ? "styled" : `unstyled`}
                key={form.key("title")}
                placeholder="Add Title..."
                size="md"
                styles={{
                  error: { display: "none" },
                }}
              />
            </div>
            <p className="text-base font-medium text-primary text-start">
              Incident ID: {id}
            </p>
          </div>
          <div className="flex items-center justify-center gap-10 mt-5 md:mt-0">
            <div className="text-center">
              <h1 className="text-base font-bold">Created on</h1>
              <p className="text-base font-bold">{formattedDate}</p>
            </div>
            <div>
              <select
                className={`focus:outline-0 ${selectClasses}`}
                value={status}
                onChange={handleStatusChange}
              >
                <option value="in_progress">In Progress</option>
                <option value="new">New</option>
                <option value="resolved">Resolved</option>
                <option value="closed">Closed</option>
              </select>
              {/* <p className="">
                New
              </p> */}
            </div>
          </div>
        </div>
        {/* End left {Incident ID ,Created on ,status ,...etc} section */}

        {/* right { Anonymous ,Submit ,cancel} section */}
        <div className="lg:col-span-1 bg-white w-full rounded-lg grid lg:grid-cols-3 items-center p-4 gap-3 shadow-md">
          <Checkbox
            label="Anonymous"
            key={form.key("Anonymous")}
            {...form.getInputProps("Anonymous", { type: "Checkbox" })}
            onChange={(event) => {
              form.setFieldValue("Anonymous", event.currentTarget.checked);
              setAnonymous(event.currentTarget.checked);
            }}
            className="col-span-1 border border-[#07838F] p-3 rounded-lg bg-[#07848f49] text-[#07838F] text-sm"
            color="#07838F"
            size="xs"
            onClick={(event) => (event.currentTarget.checked ? open() : "")}
          />

          <Button
            className="rounded-lg bg-primary text-white hover:bg-primary hover:text-white"
            size="md"
            type="submit"
            onClick={form.onSubmit(handelNewIncidentSubmit)}
          >
            {loading ? <Loading /> : "Submit"}
          </Button>
          <Button
            className="rounded-lg bg-red-400 text-white hover:bg-red-400 hover:text-white"
            size="md"
            onClick={() =>
              navigate("/green-shield/financial/ESG-incident-management")
            }
          >
            Cancel
          </Button>
        </div>
        {/* right { Anonymous ,Submit ,cancel} section */}
      </div>
      {/*End top section */}

      {/* sec {details ,upload Files} section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-10 mt-10">
        <div className="lg:col-span-2 bg-white rounded-lg p-5 md:flex justify-between shadow-md">
          <div className="grid grid-cols-1 lg:grid-cols-2 items-center w-full gap-x-10 gap-y-5 text-start">
            <TextInput
              key={form.key("details.country")}
              {...form.getInputProps("details.country")}
              radius="md"
              label="Country"
              placeholder="Enter Country"
            />
            <TextInput
              key={form.key("details.city")}
              {...form.getInputProps("details.city")}
              radius="md"
              label="State / City"
              placeholder="Enter State / City"
            />
            <TextInput
              key={form.key("details.location")}
              {...form.getInputProps("details.location")}
              radius="md"
              label="Location"
              placeholder="Enter Location"
            />
            <TextInput
              key={form.key("details.impactAssessment")}
              {...form.getInputProps("details.impactAssessment")}
              radius="md"
              label="Impact Assessment"
              placeholder="Enter Impact Assessment"
            />
            <div className="lg:col-span-2">
              <Textarea
                key={form.key("details.message")}
                {...form.getInputProps("details.message")}
                label="Message"
                placeholder="Enter Details..."
                minRows={1}
              />
            </div>
          </div>
        </div>
        <div className="lg:col-span-1 bg-white w-full rounded-lg  items-center p-4 gap-3 shadow-md ">
          <UploadFile onFilesChange={setFiles} />
        </div>
      </div>
      {/*End sec {details ,upload Files} section */}

      {/* last section AddNewIncidentTable*/}

      <div>
        <AddNewIncidentTable onSetData={setTableData} />
      </div>
      {/* last section AddNewIncidentTable*/}

      {/* Anonymous modal */}
      <Modal
        opened={opened}
        title="Enter Your Data"
        centered
        withCloseButton={false}
      >
        <div className="grid grid-cols-1 items-center w-full gap-x-10 gap-y-5 text-start">
          <TextInput
            key={modalForm.key("name")}
            {...modalForm.getInputProps("name")}
            radius="md"
            label="Name"
            placeholder="Enter Your Name"
          />
          <TextInput
            key={modalForm.key("role")}
            {...modalForm.getInputProps("role")}
            radius="md"
            label="Role"
            placeholder="Enter Your Role"
          />
          <TextInput
            key={modalForm.key("contact")}
            {...modalForm.getInputProps("contact")}
            radius="md"
            label="Contact"
            placeholder="Enter Your Contact"
          />

          <Button
            className="rounded-lg bg-primary text-white hover:bg-primary hover:text-white"
            size="md"
            type="submit"
            onClick={modalForm.onSubmit(handelModalSubmit)} // استخدام form المنفصل للـ Modal
          >
            Submit
          </Button>
        </div>
      </Modal>
      {/*End Anonymous modal */}
    </S3Layout>
  );
}
