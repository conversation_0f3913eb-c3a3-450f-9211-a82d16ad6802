
  // const MINUTES_BEFORE_EXPIRATION = 10;
  // const INTERVAL_TIME = 30000;
  // var tokenExpiration;
  // setInterval(() => checkTokenAndUpdate('fad6f645-6633-4253-bf7f-89dfe2b040a0', '6b2a4473-2327-4b76-b7b6-2dcf55be0a93'), INTERVAL_TIME);
  // function checkTokenAndUpdate(reportId, groupId) {
  //   console.log("reportId:", reportId);
  //   console.log("groupId:", groupId);

  //   const currentTime = Date.now();
  //   const expiration = Date.parse(tokenExpiration);
  //   const timeUntilExpiration = expiration - currentTime;
  //   const timeToUpdate = MINUTES_BEFORE_EXPIRATION * 60 * 1000;
  //   if (timeUntilExpiration <= timeToUpdate) {
  //     console.log("Updating report access token");
  //     updateToken(reportId, groupId);
  //   }
  // }
  // async function updateToken(reportId, groupId) {
  //   let newAccessToken = await getNewUserAccessToken(reportId, groupId);
  //   tokenExpiration = newAccessToken.expiration;
  //   let embedContainer = $("#embedContainer")[0];
  //   let report = powerbi.get(embedContainer);
  //   await report.setAccessToken(newAccessToken.token);
  // }
  // document.addEventListener("visibilitychange", function () {
  //   if (!document.hidden) {
  //     checkTokenAndUpdate('fad6f645-6633-4253-bf7f-89dfe2b040a0', '6b2a4473-2327-4b76-b7b6-2dcf55be0a93')
  //   }
  // });

  // async function getNewUserAccessToken(reportId, groupId) {
  //   console.log("reportId:", reportId);
  //   console.log("groupId:", groupId);
  //   // Make an API call to your backend to get a new access token
  //   const response = await fetch(`https://api.powerbi.com/v1.0/myorg/groups/${groupId}/reports/${reportId}/GenerateToken`, {
  //     method: 'POST',
  //     headers: {
  //       'Content-Type': 'application/json',
  //       'Authorization': `Bearer ${Cookies.get("level_user_token") || ""}`
  //     },
  //     body: JSON.stringify({
  //       accessLevel: 'View', // or 'Edit' based on your requirement
  //       lifetimeInMinutes: 60
  //     })
  //   });

  //   if (!response.ok) {
  //     throw new Error('Failed to fetch new access token');
  //   }

  //   const data = await response.json();
  //   console.log(data.accessToken);
  //   return {
  //     token: data.accessToken,
  //     expiration: data.expiration
  //   };
  // }

