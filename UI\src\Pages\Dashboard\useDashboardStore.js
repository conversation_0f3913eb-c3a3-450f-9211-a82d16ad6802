// store/useDashboardStore.js
import { create } from "zustand";
import Cookies from "js-cookie";
import axios from "axios";

const DASHBOARD_URL = "https://notificationsys-staging.azurewebsites.net";
const API_BASE_URL = "https://portal-auth-main-staging.azurewebsites.net";

const useDashboardStore = create((set, get) => ({
    // Dashboard Data
    dashboardData: null,
    tasks: [],
    comments: [],
    taskStats: {
        totalTasks: 0,
        finished: 0,
        upcoming: 0,
        pastDue: 0,
        incomplete: 0,
    },
    groups: [],


    // User Data
    user: null,
    loading: true,
    error: null,

    // Fetch Dashboard Data
    fetchDashboardData: async () => {
        try {
            const token = Cookies.get("level_user_token");
            if (!token) throw new Error("Authorization token missing");

            const response = await axios.get(
                `${DASHBOARD_URL}/api/v1/dashboard`,
                {
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${token}`,
                    },
                }
            );

            // Process task data
            const taskPriority = response.data.taskPriority || [];

            // Merge all comments from tasks
            const allComments = taskPriority.reduce((acc, task) => {
                if (task.comments && task.comments.length > 0) {
                    return [...acc, ...task.comments];
                }
                return acc;
            }, []);

            // Set task stats from both taskPriority and overall data
            const taskStats = {
                totalTasks: response.data.overall.totalTasks,
                finished: response.data.overall.finishedTasks,
                upcoming: response.data.overall.upcomingTasks,
                pastDue: response.data.overall.pastDueTasks,
                incomplete: response.data.overall.incompleteTasks,
            };

            // Update store state
            set({
                dashboardData: response.data,
                tasks: taskPriority,
                comments: allComments,
                taskStats,
                groups: response.data.last30Days.groups,
                loading: false,
            });

            return response.data;
        } catch (error) {
            console.error("Error fetching dashboard data:", error);
            set({ error: error.message, loading: false });
            throw error;
        }
    },

    // Fetch User Data
    fetchUserData: async () => {
        try {
            const token = Cookies.get("level_user_token");
            if (!token) throw new Error("Authorization token missing");

            const response = await axios.get(
                `${API_BASE_URL}/account_information`,
                {
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${token}`,
                    },
                }
            );

            set({ user: response.data });
            return response.data;
        } catch (error) {
            console.error("Error fetching user data:", error);
            set({ error: error.message });
            throw error;
        }
    },

    // Initialize Dashboard
    initializeDashboard: async () => {
        set({ loading: true });

        try {
            // Fetch both data in parallel
            const [dashboardData, userData] = await Promise.all([
                get().fetchDashboardData(),
                get().fetchUserData(),
            ]);

            return { dashboardData, userData };
        } catch (error) {
            console.error("Failed to initialize dashboard:", error);
            return null;
        }
    },

    // Reset Store
    reset: () => {
        set({
            dashboardData: null,
            tasks: [],
            comments: [],
            taskStats: {
                totalTasks: 0,
                finished: 0,
                upcoming: 0,
                pastDue: 0,
                incomplete: 0,
            },
            user: null,
            loading: false,
            error: null,
        });
    },
}));

export default useDashboardStore;