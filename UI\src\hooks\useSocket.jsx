import { useEffect,useRef } from "react";
import { io } from "socket.io-client";
import { useNotificationStore } from "@/Store/useNotificationStore";
import { IconAlertCircle } from "@tabler/icons-react";
import Cookies from "js-cookie";
import { notifications as notify } from "@mantine/notifications";


export const useSocket = () => {
  const socketRef = useRef(null);
  const { notifications, isLoading, fetchNotifications } = useNotificationStore();
  
  useEffect(() => {
    if (notifications.length === 0 && !isLoading) {
      // fetchNotifications(); //TEMP-FIX
    }
  }, [notifications, isLoading, fetchNotifications]);

  // Initialize socket once
  if (!socketRef.current) {
    const token = Cookies.get("level_user_token"); // Get token from cookies
    if (token === null || token == "") {
      notify.show({
        title: "Authentication Required",
        message: "Please login to receive notifications",
        color: "yellow",
        icon: <IconAlertCircle />,
        autoClose: 5000,
      });
      return { socket: null };
    }
    socketRef.current = io("https://notificationsys-staging.azurewebsites.net", {
      autoConnect: true,
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      reconnectionAttempts: 5,
      transports: ["websocket"],
      auth: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true, // Important for cookies & headers
      extraHeaders: {
        Authorization: `Bearer ${token}`,
      },
    });
    window.addEventListener('online', () => {
      if (!socketRef.current.connected) socketRef.current.connect();
    }); 
    socketRef.current.io.on('reconnect_attempt', () => {
      console.log('Reconnecting...');
    });
  }

  // Emit event to mark notification as read

  if (!socketRef.current) {
    notify.show({
      title: "Connection Error",
      message: "An error occurred with the connection",
      color: "red",
      autoClose: 10000,
    });
    console.log("socket not initialized");
    return { socket: null };
  }

  return { socket: socketRef.current };
};
