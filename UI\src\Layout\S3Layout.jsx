import Chatbot from '@/Components/Chatbot';
import useSideBarRoute from '@/hooks/useSideBarRoute';
import { Breadcrumbs } from '@mantine/core';
import Navbar from '@/Components/NavBar/Navbar';
import Sidebar from '@/Components/SideBar/Sidebar';
import { useState } from 'react';
import { IoIosArrowForward } from "react-icons/io";
import { Link } from 'react-router-dom';
import {ModeToggle} from "../Components/mode-toggle"
const S3Layout = ({ menus = [], children, breadcrumbItems = [], navbarTitle }) => {
  const { greenShieldMenu } = useSideBarRoute();
  menus = menus.length ? menus : greenShieldMenu;
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isChatBotOpen, setIsChatBotOpen] = useState(false)

  const items = breadcrumbItems.map((item, index) => (
    <Link
      className={`${
        index !== breadcrumbItems.length - 1
          ? "text-gray-400"
          : "text-primary font-[500]"
      }`}
      to={item.href}
      key={index}
    >
      {item.title}
    </Link>
  ));

  return (
    <div className="flex flex-col bg-white  text-black text-black dark:bg-[#1E1E1E] text-gray-700 dark:text-white min-h-screen">
        <Navbar navbarTitle={navbarTitle} isSidebarOpen={isSidebarOpen} setIsSidebarOpen={setIsSidebarOpen} items={items}/>
      <div className="flex-grow flex max-md:text-center z-10">
        <Sidebar menus={menus} isOpen={isSidebarOpen} setIsOpen={setIsSidebarOpen}
          openChatBot={()=> setIsChatBotOpen(!isChatBotOpen)}
        
        />

        <div className={'container h-screen mx-auto px-5 mt-16 block overflow-y-auto '}>
        <div className="container text-2xl px-3 font-semibold font-inter leading-none text-primary mx-auto my-8 flex flex-col sm:flex-row items-center justify-between">
           <div className="flex items-center gap-4">
    <span>{navbarTitle}</span>
  
  </div>
            <p className="text-base font-normal mt-5 sm:mt-0">
              {items?.length > 0 && (
                <Breadcrumbs className="dark:text-gray-300" separator={<IoIosArrowForward color="gray" size={20} />}>
                  {items}
                </Breadcrumbs>
              )}
            </p>
          </div>
          {children}
          </div>
      </div>
      {
        isChatBotOpen &&
      <Chatbot isChatBotOpen={isChatBotOpen} closeChatFunc={()=> setIsChatBotOpen(false)} />
      }
    </div>
  );
};

export default S3Layout;
