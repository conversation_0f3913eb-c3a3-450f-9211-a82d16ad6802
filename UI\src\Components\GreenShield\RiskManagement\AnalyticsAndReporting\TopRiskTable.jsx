import { ExportIcon } from "@/assets/icons/ReportAndAnalytic";
import { ScrollArea, Table } from "@mantine/core";

import { useEffect, useRef, useState } from "react";
import ApiS3 from "@/Api/apiS3";
import TopRiskRow from "./TopRiskRow";

import * as htmlToImage from 'html-to-image';
import Loading from "@/Components/Loading";


const TopRiskTable = () => {

  const [tableData, settableData] = useState([])

  const [loading, setloading] = useState(false)

  const getData = async () => {
    setloading(true)
    try {
      const res = await ApiS3.get("risk/topRisks");
      settableData(res.data);
    } catch (er) {
        console.log("🚀 ~ getData ~ er:", er)
    }
    setloading(false)
  };

  useEffect(() => {
    getData();
  }, []);

  const tableRef = useRef(null);  


  const handleExportPNG = () => {
    if (tableRef.current) {
        htmlToImage
            .toPng(tableRef.current, {
                quality: 0.95, // Image quality (0 to 1)
                pixelRatio: 2, // Increase resolution
                backgroundColor: '#f5f5f5', // Match the background color
            })
            .then((dataUrl) => {
                // Create a link element to trigger the download
                const link = document.createElement('a');
                link.href = dataUrl;
                link.download = 'Top-10-risks.png'; // File name for the download
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            })
            .catch((error) => {
                console.error('Error exporting PNG:', error);
                alert('Failed to export PNG. Please try again.');
            });
    }
};


  const rows = tableData && tableData?.map((item,i) => {
    return <TopRiskRow item={item} key={i} />;
  });

  return (
    <div className="mt-10 bg-white shadow-md rounded-2xl p-4">
      <div className="flex flex-wrap justify-between items-center">
        <h5 className="text-xl">Top 10 Risks</h5>

        <button onClick={handleExportPNG} className="bg-bg-lite2 text-primary flex justify-between items-center rounded-xl p-3 gap-2">
          {/* <MdOutlineFilterList /> */}
          <ExportIcon />
          <span className="text-sm font-bold">PNG Export</span>
        </button>
      </div>

      <ScrollArea mt={30}>
        <Table ref={tableRef} miw={800} verticalSpacing="sm">
          <Table.Thead className="bg-bg-lite1">
            <Table.Tr>
              <Table.Th>Event</Table.Th>
              <Table.Th>Score</Table.Th>
              <Table.Th>Trend</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>  { loading ? <Loading /> :  rows}</Table.Tbody>
        </Table>
      </ScrollArea>
    </div>
  );
};

export default TopRiskTable;
