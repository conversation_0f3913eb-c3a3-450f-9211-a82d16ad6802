import S2Layout from "@/Layout/S2Layout";
import useSideBarRoute from "@/hooks/useSideBarRoute";
import { Tabs } from "@mantine/core";
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import DataQuality from "./Partials/DataQuality/DataQuality";
import ReviewTable from "./Partials/Collected Data/ReviewTable";
import DataAnomalyView from "./Partials/Data Anomaly/DataAnomalyView";
// import { driver } from "driver.js";
// import "driver.js/dist/driver.css";
// import "@/assets/css/index.css";
// import { FaQuestionCircle } from "react-icons/fa";
import { de } from "date-fns/locale";

export default function ReviewView({ target }) {
  const { netZeroMenu } = useSideBarRoute();
  const [activeTab, setActiveTab] = useState(target ? target : "CollectedData");
  const { t } = useTranslation();
  // const [elementsReady, setElementsReady] = useState(false);

  // const checkElementsExist = () => {
  //   const steps = getGuideSteps();
  //   const allElementsExist = steps.every((step) =>
  //     document.querySelector(step.element)
  //   );
  //   return allElementsExist;
  // };

  // useEffect(() => {
  //   if (activeTab === "CollectedData") {
  //     const observer = new MutationObserver(() => {
  //       if (checkElementsExist()) {
  //         setElementsReady(true);
  //         observer.disconnect();
  //       }
  //     });

  //     observer.observe(document.body, {
  //       childList: true,
  //       subtree: true,
  //     });

  //     return () => observer.disconnect();
  //   }
  // }, [activeTab]);

  // const getGuideSteps = () => [
  //   {
  //     element: ".Three-Tabs",
  //     popover: {
  //       title: t("After collecting your data through Manual Input, Batch Input, or AI Data Driven, the review section allows you to validate and manage the data."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Review-page-divided-three-main-parts",
  //     popover: {
  //       title: t("The Review page is divided into three main parts"),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Review-Data-Section",
  //     popover: {
  //       title: t("1. Review Collected Data"),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Data-Quality-Ai-Review",
  //     popover: {
  //       title: t("2. Data Quality"),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Data-Anomaly-Detection",
  //     popover: {
  //       title: t("3. Data Anomaly Detection"),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Review-Manual-Input",
  //     popover: {
  //       title: t("Review Manual Input"),
  //       description: t(
  //         "Review data that was entered manually one record at a time."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Data-Batch-Detection",
  //     popover: {
  //       title: t("Review Batch Input"),
  //       description: t(
  //         "Review data that was uploaded through batch templates."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Find-the-records",
  //     popover: {
  //       title: t("Find the Records"),
  //       description: t(
  //         "To find the records you want to review, you can use the search bar or filters"
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Find-the-records-via-search-bar",
  //     popover: {
  //       title: t("Search"),
  //       description: t(
  //         "Search by Topic Area or Assessment Question to quickly locate specific records."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Find-the-records-via-filters",
  //     popover: {
  //       title: t("Filter by Status"),
  //       description: t(
  //         "◦ Pending ◦ Accepted ◦ Rejected"
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Manage-and-Review-your-Data",
  //     popover: {
  //       title: t("Manage and Review your Data"),
  //       description: t(
  //         "Data Table View: Uploaded data appears in a structured table with the following actions:"
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Manage-and-Review-your-Data-Edit",
  //     popover: {
  //       title: t("Edit Data"),
  //       description: t(
  //         "Correct or update any field."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Manage-and-Review-your-Data-Delete",
  //     popover: {
  //       title: t("Delete Data"),
  //       description: t(
  //         "Remove any wrong entry."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Manage-and-Review-your-Data-NOTE1",
  //     popover: {
  //       title: t("NOTE 1"),
  //       description: t(
  //         "Approval is mandatory before using the data in reporting."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Manage-and-Review-your-Data-NOTE2",
  //     popover: {
  //       title: t("NOTE 2"),
  //       description: t(
  //         "Editing a record reverts its status back to Pending until re-approved."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Manage-and-Review-your-Data-NOTE3",
  //     popover: {
  //       title: t("NOTE 3"),
  //       description: t(
  //         "Deletion is final — the system prompts for confirmation before permanently deleting a record"
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Manage-and-Review-your-Data-NEXT",
  //     popover: {
  //       title: t("Next Step"),
  //       description: t(
  //         "After data is collected and reviewed, the next step is Measure. This section allows you to analyze carbon emissions through insightful summaries and breakdowns."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  // ];

  // const startGuide = () => {
  //   const driverObj = driver({
  //     showProgress: true,
  //     popoverClass: "my-custom-popover-class",
  //     nextBtnText: "Next",
  //     prevBtnText: "Back",
  //     doneBtnText: "Done",
  //     overlayColor: "rgba(0, 0, 0, 0)",
  //     progressText: "Step {{current}} of {{total}}",
  //     steps: getGuideSteps(),
  //     onDestroyStarted: () => {
  //       localStorage.setItem("hasSeenReviewTabGuide", "true");
  //       driverObj.destroy();
  //     },
  //   });
  //   driverObj.drive();
  // };

  // useEffect(() => {
  //   const hasSeenGuide = localStorage.getItem("hasSeenReviewTabGuide");
  //   if (!hasSeenGuide) {
  //     startGuide();
  //   }
  //   return () => {
  //     const driverObj = driver();
  //     if (driverObj.isActive()) {
  //       driverObj.destroy();
  //     }
  //   };
  // }, [activeTab, elementsReady]);

  return (
    <div className="bg-[#F7F4F4] font-inter">
      {/* <div
        onClick={startGuide}
        style={{
          position: "fixed",
          bottom: 20,
          right: 20,
          cursor: "pointer",
          zIndex: 1000,
        }}
      >
        <div className="h-12 w-12 flex items-center justify-center bg-gradient-to-tr from-[#2C5A8C] via-[#1C889C] to-[#13B1A8] p-1  cursor-pointer rounded-full">
          <FaQuestionCircle
            size={34}
            color="#ffffff"
            className="mx-auto cursor-pointer"
          />
        </div>
      </div> */}
      <>
        <Tabs defaultValue={activeTab} onChange={setActiveTab}>
          <Tabs.List
            justify="center"
            className="mb-6 block  md:flex md:justify-around text-gray-500 py-3 rounded-md overflow-hidden bg-white before:border-none"
          >
            <Tabs.Tab
              value="CollectedData"
              className={`text-lg  mx-auto Review-Data-Section
                ${
                  activeTab == "CollectedData"
                    ? "text-primary border-b-2 border-primary"
                    : " border-none"
                } 
              py-3 font-semibold hover:bg-transparent hover:opacity-100`}
            >
              {t("Collected Data")}
            </Tabs.Tab>

            <Tabs.Tab
              value="DataQuality"
              className={`text-lg mx-auto Data-Quality-Ai-Review ${
                activeTab == "DataQuality"
                  ? "text-primary border-b-2 border-primary"
                  : " border-none"
              } 
          py-3 font-semibold hover:bg-transparent hover:opacity-100`}
            >
              {t("Data Quality")}
            </Tabs.Tab>
            <Tabs.Tab
              value="Data Anomaly"
              className={`text-lg mx-auto Data-Anomaly-Detection ${
                activeTab == "Data Anomaly"
                  ? "text-primary border-b-2 border-primary"
                  : " border-none"
              } 
          py-3 font-semibold hover:bg-transparent hover:opacity-100`}
            >
              {t("Data Anomaly")}
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="CollectedData">
            <ReviewTable />
          </Tabs.Panel>

          <Tabs.Panel value="DataQuality">
            <DataQuality />
          </Tabs.Panel>
          <Tabs.Panel value="Data Anomaly">
            <DataAnomalyView />
          </Tabs.Panel>
        </Tabs>
      </>
    </div>
  );
}
