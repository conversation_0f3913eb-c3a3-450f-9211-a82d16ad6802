import {
  BreakdownAssetIcon,
  BreakdownCountriesIcon,
  BreakdownScopesIcon,
  BreakdownSitesIcon,
  BreakdownTier2Icon,
} from "@/assets/svg/ImageSVG";
import Loading from "@/Components/Loading";
import { Tabs } from "@mantine/core";
import classNames from "classnames";
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import Assets from "./Partials/Assets/Assets";
import Countries from "./Partials/Countries/Countries";
import Scopes from "./Partials/Scopes/Scopes";
import Sites from "./Partials/Sites/Sites";
import Tier2Factor from "./Partials/Tier2Factor/Tier2Factor";
// import { driver } from "driver.js";
// import "driver.js/dist/driver.css";
// import "@/assets/css/index.css";
// import { FaQuestionCircle } from "react-icons/fa";

export default function EmissionsBreakdownView({
  target,
  loading,
  totalEmissions,
}) {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState(target || "Assets");
  const [elementsReady, setElementsReady] = useState(false);

  const tabs = [
    {
      value: "Assets",
      label: t("Assets"),
      icon: (
        <BreakdownAssetIcon
          color={activeTab === "Assets" ? "#07838F" : "#5A5A5A"}
        />
      ),
      component: (
        <Assets
          setActiveTab={setActiveTab}
          AssetData={totalEmissions?.asset_breakdown}
          loading={loading}
        />
      ),
    },
    {
      value: "Scopes",
      label: t("Scopes"),
      icon: (
        <BreakdownScopesIcon
          color={activeTab === "Scopes" ? "#07838F" : "#5A5A5A"}
        />
      ),
      component: (
        <Scopes
          setActiveTab={setActiveTab}
          ScopesData={totalEmissions?.scope_breakdown}
          loading={loading}
        />
      ),
    },
    {
      value: "Countries",
      label: t("Countries"),
      icon: (
        <BreakdownCountriesIcon
          color={activeTab === "Countries" ? "#07838F" : "#5A5A5A"}
        />
      ),
      component: (
        <Countries
          setActiveTab={setActiveTab}
          CountriesData={totalEmissions?.location_breakdown}
          loading={loading}
        />
      ),
    },
    {
      value: "Sites",
      label: t("Sites"),
      icon: (
        <BreakdownSitesIcon
          color={activeTab === "Sites" ? "#07838F" : "#5A5A5A"}
        />
      ),
      component: (
        <Sites
          setActiveTab={setActiveTab}
          SitesData={totalEmissions?.site_breakdown}
          loading={loading}
        />
      ),
    },
    {
      value: "Tier 2 Factor",
      label: t("Tier 2 Factor"),
      icon: (
        <BreakdownTier2Icon
          color={activeTab === "Tier 2 Factor" ? "#07838F" : "#5A5A5A"}
        />
      ),
      component: (
        <Tier2Factor
          setActiveTab={setActiveTab}
          Tier2FactorData={totalEmissions?.specific_factor}
          loading={loading}
        />
      ),
    },
  ];

  // const getGuideSteps = () => [
  //   {
  //     element: ".Provide-a-deeper-dive",
  //     popover: {
  //       title: t(
  //         "Provide a deeper dive into emissions categorized by different dimensions like Assets, Scopes, Countries, Sites, and Tier 2 Factors."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Provide-a-deeper-dive",
  //     popover: {
  //       title: t("Select the Breakdown Dimension"),
  //       description: t(
  //         "Choose to view emissions breakdown by: Assets, Scopes, Countries, Sites, and Tier 2 Factors."
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Provide-a-deeper",
  //     popover: {
  //       title: t("After selecting a dimension, details are shown including:"),
  //       description: t(
  //         "• Asset Name / Location Name / Scope Name • Total Emissions related to that item"
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Example-from-Assets-view",
  //     popover: {
  //       title: t("Example from Assets view"),
  //       description: t(
  //         "• Scope 1,2 Test: 57.10K T CO₂e • Scope 3: 17.74K T CO₂e • s1/s2: 2.04M T CO₂e"
  //       ),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  //   {
  //     element: ".Example-from-Assets-view-Button",
  //     popover: {
  //       title: t("Click 'View Details'"),
  //       description: t("button to dive deeper into each entry."),
  //       side: "left",
  //       align: "center",
  //     },
  //   },
  // ];

  // const checkElementsExist = () => {
  //   const steps = getGuideSteps();
  //   const allElementsExist = steps.every((step) =>
  //     document.querySelector(step.element)
  //   );
  //   return allElementsExist;
  // };

  // useEffect(() => {
  //   if (activeTab === "Assets") {
  //     const observer = new MutationObserver(() => {
  //       if (checkElementsExist()) {
  //         setElementsReady(true);
  //         observer.disconnect();
  //       }
  //     });

  //     observer.observe(document.body, {
  //       childList: true,
  //       subtree: true,
  //     });

  //     return () => observer.disconnect();
  //   }
  // }, [activeTab, elementsReady]);

  // const startGuide = () => {
  //   const driverObj = driver({
  //     showProgress: true,
  //     popoverClass: "my-custom-popover-class",
  //     nextBtnText: "Next",
  //     prevBtnText: "Back",
  //     doneBtnText: "Done",
  //     progressText: "Step {{current}} of {{total}}",
  //     overlayColor: "rgba(0, 0, 0, 0)",
  //     steps: getGuideSteps(),
  //     onDestroyStarted: () => {
  //       localStorage.setItem("hasSeenemissionsBreakGuide", "true");
  //       driverObj.destroy();
  //     },
  //   });
  //   driverObj.drive();
  // };

  // useEffect(() => {
  //   const hasSeenGuide = localStorage.getItem("hasSeenemissionsBreakGuide");
  //   if (!hasSeenGuide) {
  //     startGuide();
  //   }
  //   return () => {
  //     const driverObj = driver();
  //     if (driverObj.isActive()) {
  //       driverObj.destroy();
  //     }
  //   };
  // }, [activeTab, elementsReady]);

  return (
    <div className="pb-3">
      {/* <div
        onClick={startGuide}
        style={{
          position: "fixed",
          bottom: 20,
          right: 20,
          cursor: "pointer",
          zIndex: 1000,
        }}
      >
        <div className="h-12 w-12 flex items-center justify-center bg-gradient-to-tr from-[#2C5A8C] via-[#1C889C] to-[#13B1A8] p-1  cursor-pointer rounded-full">
          <FaQuestionCircle
            size={34}
            color="#ffffff"
            className="mx-auto cursor-pointer"
          />
        </div>
      </div> */}
      <Tabs defaultValue={activeTab} onChange={setActiveTab}>
        <Tabs.List className="static justify-around gap-3  mb-3 overflow-hidden rounded-md  bg-white Provide-a-deeper-dive">
          {tabs.map((tab) => (
            <Tabs.Tab
              key={tab.value}
              value={tab.value}
              className={classNames(
                "font-bold bg-white flex items-center justify-center border-0 text-base",
                {
                  "text-primary border-b-2 border-primary":
                    activeTab === tab.value,
                  "text-[#5A5A5A]": activeTab !== tab.value,
                }
              )}
            >
              <div className="flex items-center justify-center gap-2">
                <p className="">{tab.icon}</p>
                {tab.label}
              </div>
            </Tabs.Tab>
          ))}
        </Tabs.List>
        {loading ? (
          <Loading />
        ) : (
          tabs.map((tab) => (
            <Tabs.Panel key={tab.value} value={tab.value}>
              {tab.component}
            </Tabs.Panel>
          ))
        )}
      </Tabs>
    </div>
  );
}
