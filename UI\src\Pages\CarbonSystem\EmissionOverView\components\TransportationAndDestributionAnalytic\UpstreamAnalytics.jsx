import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@mantine/charts";
import DetailedTransactionTable from "./AnalyticsComponents/DetailedTransactionTable";

const UpstreamAnalytics = () => {
  const calcMyMethod = [
    { method: "Fuel-Based", value: 100 },
    { method: "Distance-Based", value: 80 },
    { method: "Spend-Based", value: 60 },
  ];
  const vehicleType = [
    { name: "USA", value: 400, color: "indigo.6" },
    { name: "India", value: 300, color: "yellow.6" },
    { name: "Japan", value: 300, color: "teal.6" },
    { name: "Other", value: 200, color: "gray.6" },
  ];
  const totalEmission = [
    {
      date: "Mar 22",
      Apples: 2890,
      Oranges: 2338,
      Tomatoes: 2452,
    },
    {
      date: "Mar 23",
      Apples: 2756,
      Oranges: 2103,
      Tomatoes: 2402,
    },
    {
      date: "Mar 24",
      Apples: 3322,
      Oranges: 986,
      Tomatoes: 1821,
    },
    {
      date: "Mar 25",
      Apples: 3470,
      Oranges: 2108,
      Tomatoes: 2809,
    },
  ];

  const esgData = [
    {
      category: "ESMS",
      "Compliance Score": 50,
      "Industry Average": 33,
    },
    {
      category: "Labour & Working Conditions",
      "Compliance Score": 40,
      "Industry Average": 50,
    },
    {
      category: "Resource Efficiency",
      "Compliance Score": 30,
      "Industry Average": 50,
    },
    {
      category: "Community Impact",
      "Compliance Score": 33,
      "Industry Average": 50,
    },
    {
      category: "Land & Biodiversity",
      "Compliance Score": 38,
      "Industry Average": 10,
    },
    {
      category: "Indigenous & Cultural",
      "Compliance Score": 44,
      "Industry Average": 45,
    },
    {
      category: "OCF Requirements",
      "Compliance Score": 49,
      "Industry Average": 17,
    },
    {
      category: "Monitoring & Reporting",
      "Compliance Score": 42,
      "Industry Average": 22,
    },
  ];

  const statusItems = [
    { color: "bg-purple-400", label: "Partially Implemented" },
    { color: "bg-red-300", label: "Initial Planning" },
    { color: "bg-cyan-400", label: "Not Started" },
    { color: "bg-orange-300", label: "In Development" },
    { color: "bg-blue-500", label: "Fully Implemented" },
  ];

  return (
    <div>
      <div className="border-2 grid grid-cols-1 md:grid-cols-3 rounded-xl bg-white">
        {/* Card 1: Total Emissions */}
        <div className="border-r-2 rounded-l-xl p-4 flex flex-col">
          <span className="text-lg text-gray-500 mb-2">Total Emissions</span>
          <div className="flex items-center">
            <span className="text-3xl font-bold text-primary mr-2">2</span>
            <span className="text-xs text-green-500 mt-2">
              3% from last month
            </span>
          </div>
        </div>
        {/* Card 2: Emissions per ton-mile */}
        <div className="border-r-2 p-4 flex flex-col">
          <span className="text-lg text-gray-500 mb-2">
            Emissions per ton-mile
          </span>
          <div className="flex items-center">
            <span className="text-3xl font-bold text-primary mr-2">6</span>
            <span className="text-xs text-green-500 mt-2">
              3% from last month
            </span>
          </div>
        </div>
        {/* Card 3: Fuel efficiency by type */}
        <div className=" rounded-r-xl p-4 flex flex-col">
          <span className="text-lg text-gray-500 mb-2">
            Fuel efficiency by type
          </span>
          <div className="flex items-center">
            <span className="text-3xl font-bold text-primary mr-2">6</span>
            <span className="text-xs text-red-500 mt-2">
              3% from last month
            </span>
          </div>
        </div>
      </div>

      <div className="grid lg:grid-cols-3 mt-10">
        <div>
          <BarChart
            h={300}
            data={calcMyMethod}
            dataKey="method"
            orientation="horizontal"
            yAxisProps={{ domain: [0, 100] }}
            series={[{ name: "value", color: "blue.6" }]}
            tickLine="y"
            withXAxis={false}
            barProps={{ radius: 4 }}
          />
          <div className="flex items-center justify-center mt-4">
            <span
              className="inline-block w-3 h-3 rounded-sm mr-2"
              style={{ backgroundColor: "#17929b" }}
            ></span>
            <span className="text-gray-600 text-base font-normal">Method</span>
          </div>
        </div>

        <div className="flex col-span-2 justify-center items-center flex-col">
          <PieChart
            withLabelsLine
            labelsPosition="outside"
            labelsType="percent"
            withLabels
            data={vehicleType}
          />
          <div className="flex flex-wrap gap-6 items-center justify-center py-4">
            {statusItems.map((item, index) => (
              <div key={index} className="flex items-center gap-2">
                <span className={`w-3 h-3 rounded-full ${item.color}`}></span>
                <span className="text-gray-700 text-sm">{item.label}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
      <div className="mt-10 bg-white border-2 rounded-xl p-2">
        <h5 className="mb-10 font-bold text-lg">
          Total emissions over 12 months
        </h5>
        <AreaChart
          h={300}
          data={totalEmission}
          dataKey="date"
          type="stacked"
          series={[{ name: "Apples", color: "indigo.6" }]}
        />
      </div>
      <div className="mt-10 bg-white border-2 rounded-xl p-2 overflow-x-auto">
        <h5 className="mb-10 font-bold text-lg">
          Cost vs. Emissions Tradeoffs
        </h5>
        <BarChart
          className=""
          miw={1200}
          h={400}
          data={esgData}
          dataKey="category"
          series={[
            { name: "Compliance Score", color: "#07838F" },
            { name: "Industry Average", color: "#F4B351" },
          ]}
          orientation="horizontal" // Makes it horizontal if preferred
          tickLine="y" // For better readability
          yAxisProps={{ interval: 0 }} // Ensures all categories are shown
          xAxisProps={{ domain: [0, 50] }} // Sets scale to 0-100 for percentage
        />
      </div>

      <div className="mt-10 bg-white border-2 rounded-xl p-2">
        <DetailedTransactionTable />
      </div>
    </div>
  );
};

export default UpstreamAnalytics;
