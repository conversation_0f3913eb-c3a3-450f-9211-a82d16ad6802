import { useEffect, useState } from "react";
import Loading from "@/Components/Loading";
import SWOTSections from "./Partials/SWOTSections";
import ResultGridChart from "./Partials/ResultGridChart";
import PDFurl from "./Partials/PDFurl";
import { notifications } from "@mantine/notifications";
import ApiSustain360 from "@/Api/apiSustain360";
import { useAuth } from "@/Contexts/AuthContext";
import { useTranslation } from "react-i18next";
import { Button } from "@mantine/core";

const Result = ({setActive}) => {
  const [categoryData, setCategoryData] = useState(null);
  const [pdfURL, setPdfURL] = useState(null);
  const { user } = useAuth();
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    try {
      ApiSustain360.get("/api/assessments/analysis/last").then(({ data }) => {
        if (data) {
          setLoading(false);
          setPdfURL({
            pdf_url: data.report_url,
            report_date: data.report_date,
          });
          setCategoryData(data.analysis.Category_Data);
        }
      });
    } catch (error) {
      // notifications.show({ message: "Try again" });
    }finally{
      setLoading(false);
    }
  }, []);

  return (
    <div>
      {(loading && categoryData == null) ? <Loading /> : (categoryData != null) ?(
        <div>
        {pdfURL && (
          <PDFurl
            pdf_url={pdfURL.pdf_url}
            shareEmail={user?.email}
            report_date={pdfURL?.report_date}
          />
        )}
        {categoryData ? (
          <>
            <ResultGridChart categoryData={categoryData} />
            <SWOTSections categoryData={categoryData} />
          </>
        ) : (
          <>
            <p className="text-center py-6">{t("Preparing the report")}...</p>
            <Loading />
          </>
        )}
      </div>
      ): (
        <div className="flex flex-col items-center py-[10vh] w-full">
          <h2 className="text-xl mb-4">No Assessment Found, go to the back to Assessment Page and create a new assessment</h2>
          <Button size="md" className="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary/80" onClick={() => setActive("Assessment")}>Back to Assessment Page</Button>
        </div>
      
    )}
    </div>
  );
};

export default Result;
