import MainLayout from "@/Layout/MainLayout";
import { useState } from "react";
import AssesmentPage from "./pages/AssesmentPage";
import ReportPage from "./pages/ReportPage";
import { IoMdHome } from "react-icons/io";
export default function Page() {
    const [activeTabIndex, setActiveTabIndex] = useState(0);
    const tabs = [
        { name: "Assessment", component: <AssesmentPage /> },
        { name: "Reporting", component: <ReportPage /> },
    ];

    const selectTab = (index) => {
        setActiveTabIndex(index);
    };

    return (
        <MainLayout navbarTitle={"Equator Principles Assessment"}
            breadcrumbItems={[
                { title: <IoMdHome size={20}/>, href: "/get-started" },
                { title: "FinanceGuard", href: "/Insights-reporing/financeguard" },
                { title: "Equator Principles Assessment", href: "#" },
            ]}
        >
            <div className="w-full h-[90%] flex flex-col overflow-hidden overflow-y-auto pr-6 gap-6">
                <div className="w-full items-center justify-center gap-4 flex">
                    {tabs.map((tab, index) => (
                        <div
                            onClick={() => selectTab(index)}
                            key={index}
                            className={`w-1/2 text-lg py-2 text-center font-semibold cursor-pointer select-none rounded-lg transition-all  ${
                                index == activeTabIndex
                                    ? "border-[#07838F] text-[#07838F] bg-[#07838F1A] border-0 border-l-8"
                                    : "border-2 bg-transparent border-gray-300"
                            }`}
                        >
                            {tab.name}
                        </div>
                    ))}
                </div>

                <div className="w-full h-full overflow-hidden overflow-y-auto">
                    {tabs[activeTabIndex].component}
                </div>
            </div>
        </MainLayout>
    );
}
