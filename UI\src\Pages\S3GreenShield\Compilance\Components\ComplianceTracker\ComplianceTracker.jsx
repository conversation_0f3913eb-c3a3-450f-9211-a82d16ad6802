import React, { useEffect, useState } from "react";

import { Select } from "@mantine/core";
import { Table } from "@mantine/core";
import { FaRegCircle } from "react-icons/fa";
import Axios from "axios";
import Cookies from "js-cookie";

const ComplianceTracker = () => {
  const tableData = [
    {
      Region: "EU",
      Regulation:
        "LoruSustainable Finance Disclosure Regulation (SFDR) m upsum",
      Description:
        "Requires financial market participants to disclose sustainability risks and impacts",
      Date: ["March 10, 2021 (Level 1)", "January 1, 2023 (Level 2)"],
    },
    {
      Region: "EU",
      Regulation:
        "LoruSustainable Finance Disclosure Regulation (SFDR) m upsum",
      Description:
        "Requires financial market participants to disclose sustainability risks and impacts",
      Date: ["March 10, 2021 (Level 1)", "January 1, 2023 (Level 2)"],
    },
    {
      Region: "EU",
      Regulation:
        "LoruSustainable Finance Disclosure Regulation (SFDR) m upsum",
      Description:
        "Requires financial market participants to disclose sustainability risks and impacts",
      Date: ["March 10, 2021 (Level 1)", "January 1, 2023 (Level 2)"],
    },
    {
      Region: "EU",
      Regulation:
        "LoruSustainable Finance Disclosure Regulation (SFDR) m upsum",
      Description:
        "Requires financial market participants to disclose sustainability risks and impacts",
      Date: ["March 10, 2021 (Level 1)", "January 1, 2023 (Level 2)"],
    },
  ];

  const upCData = ['EU Green Claims Directive: 30 days', 'US FTC Green Guides Update: 60 days'
    , 'Global Reporting Initiative Standards: 90 days']



  const [regionsOptions, setRegionsOptions] = useState([]);
  const [selectedRegion, setSelectedRegion] = useState(null);
  const [loadingRegion, setLoadingRegion] = useState(false);
  const [errorRegion, setErrorRegion] = useState(null);

  const [regulatoryData, setRegulatoryData] = useState([]);
  const [filteredRegulatoryData, setFilteredRegulatoryData] = useState([]);
  const [loadingRegulatory, setLoadingRegulatory] = useState(false);
  const [errorRegulatory, setErrorRegulatory] = useState(null);

  // Fetch regions data
  useEffect(() => {
    const fetchRegionData = async () => {
      setLoadingRegion(true);
      try {
        const token = Cookies.get("level_user_token");

        const response = await Axios.get(
          "https://portals3-h3bjdkhtbghye2aa.uksouth-01.azurewebsites.net/regions",
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
            withCredentials: true,
          }
        );

        const regions = response.data.map((region) => ({
          value: region._id,
          label: region.name,
        }));

        setRegionsOptions(regions);

        if (regions.length > 0) {
          setSelectedRegion(regions[0].value);
        }
        setLoadingRegion(false);
      } catch (err) {
        setErrorRegion(err.message);
        setLoadingRegion(false);
      }
    };

    fetchRegionData();
  }, []);

  useEffect(() => {
    const fetchRegulatoryData = async () => {
      setLoadingRegulatory(true);
      try {
        const token = Cookies.get("level_user_token");

        const response = await Axios.get(
          "https://portals3-h3bjdkhtbghye2aa.uksouth-01.azurewebsites.net/regulatory-tracker",
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
            withCredentials: true,
          }
        );

        setRegulatoryData(response.data);
        setLoadingRegulatory(false);
      } catch (err) {
        setErrorRegulatory(err.message);
        setLoadingRegulatory(false);
      }
    };

    fetchRegulatoryData();
  }, []);

  useEffect(() => {
    if (selectedRegion) {
      const filteredData = regulatoryData.filter(
        (item) => item.regionId._id === selectedRegion
      );
      setFilteredRegulatoryData(filteredData);
    } else {
      setFilteredRegulatoryData(regulatoryData);
    }
  }, [selectedRegion, regulatoryData]);

  return (
    <div>
      <div className="s w-[20rem] mb-7 mx-auto">
        <Select
          placeholder="Select your Region"
          data={regionsOptions}
          radius={10}
          size="md"
          value={selectedRegion}
          onChange={(value) => setSelectedRegion(value)}
        />
      </div>

      <Table striped highlightOnHover withRowBorders={false}>
        <Table.Thead className="border-b-[3px]">
          <Table.Tr>
            <Table.Th className="font-normal text-sm">Region</Table.Th>
            <Table.Th className="font-normal text-sm">Regulation</Table.Th>
            <Table.Th className="font-normal text-sm">Description</Table.Th>
            <Table.Th className="font-normal text-sm">Effective Year</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {filteredRegulatoryData.map((el, id) => (
            <Table.Tr
              className={`${id % 2 === 0 ? "bg-[#E7E9E9]" : "bg-[#FFFFFF]"
                }`}
              key={el._id}
            >
              <Table.Td className="font-bold text-secondary-lite-200 text-sm">
                {el.regionId.name}
              </Table.Td>
              <Table.Td className="font-bold text-secondary-lite-200 text-sm">
                {el.regulation}
              </Table.Td>
              <Table.Td className="font-bold text-secondary-lite-200 text-sm">
                {el.description}
              </Table.Td>
              <Table.Td className="font-bold text-secondary-lite-200 text-sm">
                {el.effectiveYear}
              </Table.Td>
            </Table.Tr>
          ))}
        </Table.Tbody>
      </Table>

      {/* <h3 className="font-medium text-xl mt-7 ">Upcoming Deadlines</h3>
      <div className="d lg:w-[570px] min-h-[115px] bg-white rounded-2xl p-4 mt-3">
        {
          upCData.map((el, index) => (
            <div key={index} className="flex gap-4 mt-2 items-center">
              <FaRegCircle className="text-[#00C0A9]" />
              <h3> {el} </h3>
            </div>
          ))
        }
      </div> */}
    </div>
  );
};

export default ComplianceTracker;
