import React, { useState } from "react";
import { <PERSON><PERSON>, Tabs } from "@mantine/core";
import DetailedView from "./Detailed/DetailedView";
import OverviewView from "./Overview/OverviewView";
import ComparativeView from "./Comparative/ComparativeView";
import ForecastView from "./Forecast/ForecastView";
import InsightsView from "./Insights/InsightsView";

const AnalyticsTabs = () => {
  const [activeTab, setActiveTab] = useState("overview");

  return (
    <>
      <Tabs defaultValue={activeTab} onChange={setActiveTab}>
        <div className="flex items-center justify-between">
          <Tabs.List
            className="mb-5"
            // justify="center"
            // className="mb-6 py-5 static text-primary rounded-md flex flex-col md:justify-between justify-center md:flex-row gap-3"
          >
            <Tabs.Tab
              value="overview"
              className={`text-lg ${
                activeTab == "overview"
                  ? "text-primary bg-opacity-100"
                  : "bg-opacity-10"
              } `}
            >
              Overview
            </Tabs.Tab>

            <Tabs.Tab
              value="detailed"
              className={`text-lg ${
                activeTab == "detailed"
                  ? "text-primary bg-opacity-100"
                  : "bg-opacity-10"
              } `}
            >
              Detailed
            </Tabs.Tab>

            <Tabs.Tab
              value="comparative"
              className={`text-lg ${
                activeTab == "comparative"
                  ? "text-primary bg-opacity-100"
                  : "bg-opacity-10"
              } `}
            >
              Comparative
            </Tabs.Tab>

            <Tabs.Tab
              value="forecast"
              className={`text-lg ${
                activeTab == "forecast"
                  ? "text-primary bg-opacity-100"
                  : "bg-opacity-10"
              } `}
            >
              Forecast
            </Tabs.Tab>
            <Tabs.Tab
              value="insights"
              className={`text-lg ${
                activeTab == "insights"
                  ? "text-primary bg-opacity-100"
                  : "bg-opacity-10"
              } `}
            >
              Insights
            </Tabs.Tab>
          </Tabs.List>
          
          <Button
            variant="filled"
            size="sm"
            radius="md"
            className="bg-primary hover:bg-secondary"
          >
            Download Data
          </Button>
        </div>

        <Tabs.Panel value="overview">
          <OverviewView />
        </Tabs.Panel>
        <Tabs.Panel value="detailed">
          <DetailedView />
        </Tabs.Panel>
        <Tabs.Panel value="comparative">
          <ComparativeView />
        </Tabs.Panel>

        <Tabs.Panel value="forecast">
          <ForecastView />
        </Tabs.Panel>
        <Tabs.Panel value="insights">
          <InsightsView />
        </Tabs.Panel>
      </Tabs>
    </>
  );
};

export default AnalyticsTabs;
