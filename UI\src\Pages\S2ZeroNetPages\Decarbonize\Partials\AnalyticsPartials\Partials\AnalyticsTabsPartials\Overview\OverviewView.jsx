import React from "react";
import TotalEmissions from "./Partials/TotalEmissions";
import TopEmissionSources from "./Partials/TopEmissionSources";
import EmissionsByScope from "./Partials/EmissionsByScope";
import ReductionProgress from "./Partials/ReductionProgress";

const OverviewView = () => {
  return (
    <div className="grid grid-flow-row grid-cols-4 gap-4">
      <div className="col-span-2">
        <TotalEmissions />
      </div>
      <div className=" col-span-2 w-[75%] ">
        <EmissionsByScope />
      </div>
      <div className="col-span-2">
        <TopEmissionSources />
      </div>
      <div className="row-span-2">
        <ReductionProgress />
      </div>
    </div>
  );
};

export default OverviewView;
