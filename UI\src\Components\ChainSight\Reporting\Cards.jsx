import { HiOutlineDocumentText } from "react-icons/hi";
import { LuUsersRound } from "react-icons/lu";

const Card = ({
  title,
  icon,
  heading,
  scoreText,
  scoreColor,
  description,
}) => (
  <div className="bg-[#FFFFFF] rounded-xl border-2 border-[#E8E7EA] p-5 w-full ">
    <p className="text-sm text-[#272727] mb-1">{title}</p>
    <div className="flex items-center gap-2 mb-2">
      {icon}
      <h3 className="text-lg font-semibold text-[#272727]">{heading}</h3>
    </div>
    <hr className="my-3" />
    <div className="mb-2">
      <p className="text-sm text-[#272727]">{scoreText.label}</p>
      <span className={`text-xs px-3 py-1 rounded-full ${scoreColor}`}>
        {scoreText.value}
      </span>
    </div>
    <hr className="my-3" />
    <p className="text-sm text-[#272727] mb-3">{description}</p>
  </div>
);

export default function Cards({ latestReport }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-6">
      <Card
        title="Top Performing Area"
        icon={<HiOutlineDocumentText className="text-[#07838F]" />}
        heading={latestReport?.priority_actions.top_performing_areas[0].title}
        scoreText={{
          value: `Score: ${latestReport?.priority_actions.top_performing_areas[0].total_score}/${latestReport?.priority_actions.top_performing_areas[0].max_score}`,
        }}
        scoreColor="bg-[#D1FAE5] text-[#065F46]"
        description="Strong foundation to build upon"
      />

      <Card
        title="Priority Improvement"
        icon={<LuUsersRound className="text-[#07838F]" />}
        heading={latestReport?.priority_actions.bottom_performing_areas[0].title}
        scoreText={{
          value: `Score: ${latestReport?.priority_actions.bottom_performing_areas[0].total_score}/${latestReport?.priority_actions.bottom_performing_areas[0].max_score}`,
        }}
        scoreColor="bg-[#FEE2E2] text-[#991B1B]"
        description="Key focus for improvement"
      />
    </div>
  );
}
