import React, { useEffect, useRef, useState } from "react";
import DMMatriex from "@/assets/svg/Group 239332.svg";
import DMMatriex2 from "@/assets/svg/Group 239335.svg";
import { ScrollArea, Button, Switch } from "@mantine/core";
import { FaCircle, FaPlus } from "react-icons/fa";
import { MdOutlineFileUpload } from "react-icons/md";
import { useDisclosure } from "@mantine/hooks";
import { useTranslation } from "react-i18next";

const HeatMap = ({
  data,
  selectedItems,
  PriorityLevels,
  prioritySelectColorMap,
}) => {
  // console.log(selectedItems);
  const canvasRef = useRef(null);
  const [tooltip, setTooltip] = useState(null);
  const [imgCanvas, setImgCanvas] = useState(DMMatriex);
  // console.log(tooltip);
  const getPriorityLimits = (priority) => {
    switch (priority) {
      case 1:
        return { xMax: 40, xMin: 25, yMax: 40, yMin: 25 };
      case 2:
        return { xMax: 60, xMin: 50, yMax: 60, yMin: 50 };
      case 3:
        return { xMax: 80, xMin: 75, yMax: 80, yMin: 75 };
      case 4:
        return { xMax: 98, yMax: 98.4 };
      default:
        return { xMax: 96.8, yMax: 98.4 };
    }
  };
  const normalizeScoreX = (score, priority) => {
    const { xMax, xMin } = getPriorityLimits(priority);
    const updatedScore = (score * xMin) / xMax;
    const restScore = updatedScore > xMin ? updatedScore - xMin : updatedScore;
    const calculatedScore =
      updatedScore > xMin ? updatedScore - restScore : updatedScore;
    return Math.max(
      1.3,
      Math.min(
        xMax,
        priority !== 4
          ? priority === 1
            ? calculatedScore
            : updatedScore
          : score
      )
    );
  };

  const normalizeScoreY = (score, priority) => {
    const { yMax, yMin } = getPriorityLimits(priority);
    const updatedScore = (score * yMin) / yMax;
    const restScore = updatedScore > yMin ? updatedScore - yMin : updatedScore;
    const calculatedScore =
      updatedScore > yMin ? updatedScore - restScore : updatedScore;

    return Math.max(
      1,
      Math.min(
        yMax,
        priority !== 4
          ? priority === 1
            ? calculatedScore
            : updatedScore
          : score
      )
    );
  };

  const [loading, { toggle }] = useDisclosure(false);

  useEffect(() => {
    setImgCanvas(loading ? DMMatriex2 : DMMatriex);
  }, [loading]);

  const greenBox = {
    xMin: 60,
    yMin: 0,
    xMax: 649,
    yMax: 575,
  };

  useEffect(() => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");
    const img = new Image();
    img.src = imgCanvas;
    const groupedPoints = {};
    img.onload = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

      ctx.strokeStyle = "rgba(0, 255, 0, 0)";
      ctx.lineWidth = 2;
      ctx.strokeRect(
        greenBox.xMin,
        greenBox.yMin,
        greenBox.xMax - greenBox.xMin,
        greenBox.yMax - greenBox.yMin
      );

      selectedItems.forEach((itemId) => {
        const item = data.find((d) => d.id == itemId);
        if (item) {
          const priority = item.Priority;
          const x = calculateXPosition(
            normalizeScoreX(item.Impact_Score, priority),
            canvas.width
          );
          const y = calculateYPosition(
            normalizeScoreY(item.Financial_Score, priority),
            canvas.height
          );
          const key = `${x}-${y}`;
          if (!groupedPoints[key]) {
            groupedPoints[key] = [];
          }
          groupedPoints[key].push({ item, x, y });
        }
      });

      Object.values(groupedPoints).forEach((points) => {
        points.forEach((point) => {
          const color = getColorForCategory(point.item.Category);
          drawPoint(ctx, point.x, point.y, color, 8);
        });
      });
    };

    const handleMouseMove = (event) => {
      const rect = canvas.getBoundingClientRect();
      const mouseX = event.clientX - rect.left;
      const mouseY = event.clientY - rect.top;

      // البحث عن نقاط مشتركة
      const hoveredPoints = Object.values(groupedPoints).flatMap((points) =>
        points.filter(
          (point) =>
            Math.abs(point.x - mouseX) < 10 && Math.abs(point.y - mouseY) < 10
        )
      );

      if (hoveredPoints.length > 0) {
        const tooltipData = hoveredPoints.map((point) => ({
          financialScore: point.item.Financial_Score,
          impactScore: point.item.Impact_Score,
          priority: point.item.Priority,
          category: point.item.Category,
        }));

        setTooltip({
          x: mouseX,
          y: mouseY,
          data: tooltipData, // مجموعة البيانات المرتبطة بالنقاط
        });
      } else {
        setTooltip(null);
      }
    };

    canvas.addEventListener("mousemove", handleMouseMove);
    return () => canvas.removeEventListener("mousemove", handleMouseMove);
  }, [selectedItems, data, greenBox]);

  const getColorForCategory = (category) => {
    switch (category) {
      case "Governance":
        return "red";
      case "Environmental":
        return "green";
      case "Social":
        return "yellow";
      case "Economic":
        return "blue";
      default:
        return "gray";
    }
  };

  const calculateXPosition = (score, width) =>
    greenBox.xMin + (score / 100) * (greenBox.xMax - greenBox.xMin);

  const calculateYPosition = (score, height) =>
    greenBox.yMax - (score / 100) * (greenBox.yMax - greenBox.yMin);

  const drawPoint = (ctx, x, y, color, radius) => {
    ctx.fillStyle = color;
    ctx.beginPath();
    ctx.arc(x, y, radius, 0, 2 * Math.PI);
    ctx.fill();
  };

  const exportImage = () => {
    const canvas = canvasRef.current;
    const image = canvas.toDataURL("image/png");
    const link = document.createElement("a");
    link.download = "canvas-image.png";
    link.href = image;
    link.click();
  };

  const { t } = useTranslation();
  // console.log(tooltip);

  return (
    <>
      {/* {console.log(tooltip)} */}
      <div className="grid grid-cols-1 p-5 bg-white rounded-lg shadow-md lg:grid-cols-4 ">
        <div className="col-span-3 ">
          <ScrollArea style={{ width: "100%" }} className="">
            <canvas
              ref={canvasRef}
              width={650}
              height={650}
              className="relative"
            />

            {tooltip && (
              <div
                style={{
                  position: "absolute",
                  left: tooltip.x- 5,
                  top:
                    tooltip.y <= 200
                      ? tooltip.y + 5
                      : tooltip.data.length > 1
                      ? tooltip.y - 200
                      : tooltip.y - 100,
                  background: "white",
                  border: "1px solid black",
                  overflow:"hidden",
                  padding: 0,
                  borderRadius: "8px",
                  boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
                  zIndex: 1000,
                }}
                // className={`left-[${tooltip.x+5}]`}
              >
                {tooltip?.data?.map((item, idx) => (
                  <div key={idx} className="p-1 " style={{backgroundColor:prioritySelectColorMap[PriorityLevels[item.priority]].bg}}>
                    <strong>Impact Score:</strong> {item.impactScore}
                    <br />
                    <strong>Financial Score:</strong> {item.financialScore}
                    <br />
                    <strong>Category:</strong> {item.category}
                    <br />
                    <strong>Priority:</strong>{" "}
                    <span
                    className="font-bold"
                      style={{ color: prioritySelectColorMap[PriorityLevels[item.priority]].text }}
                    >
                      {PriorityLevels[item.priority]}
                    </span>
                    {idx < tooltip.data.length - 1 && <hr className="my-1" />}
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>
          <div className="flex justify-center">
            <Switch
              checked={loading}
              onChange={toggle}
              label=""
              mt="md"
              size="xl"
              onLabel="Green"
              offLabel="Gray"
              color="green"
            />
          </div>
        </div>
        <div className="flex flex-col items-start justify-between col-span-1 gap-5 mt-5 lg:mt-0">
          <div className="flex flex-col gap-5">
            <Button
              onClick={exportImage}
              className="bg-transparent border-2 border-[#00C0A9] text-[#00C0A9] text-xl flex items-center rounded-xl hover:bg-transparent hover:text-[#00C0A9]"
            >
              <MdOutlineFileUpload className="me-2" />
              {t("pngExport")}
            </Button>
            <Button className="bg-transparent border-2 border-[#00C0A9] text-[#00C0A9] text-xl flex items-center rounded-xl px-7 hover:bg-transparent hover:text-[#00C0A9]">
              <FaPlus className="me-2" />
              {t("addFilter")}
            </Button>
          </div>
          <div className="mb-20">
            <p className="flex items-center text-base font-bold">
              <FaCircle className="text-red-500 me-2" /> {t("governance")}
            </p>
            <p className="flex items-center text-base font-bold">
              <FaCircle className="text-green-500 me-2" /> {t("environment")}
            </p>
            <p className="flex items-center text-base font-bold">
              <FaCircle className="text-yellow-500 me-2" /> {t("social")}
            </p>
            <p className="flex items-center text-base font-bold">
              <FaCircle className="text-blue-500 me-2" /> {t("economic")}
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default HeatMap;
