import img from "@/assets/svg/Group 72.svg";
import send from "@/assets/svg/send.svg";
import chatimg from "@/assets/svg/Final_AI Mascot 2.svg";
import BotImg from "@/assets/svg/Group 72.svg";
import manImg from "@/assets/images/avatar-9.png";
import { Button, TextInput } from "@mantine/core";
import axios from "axios";
import moment from "moment";
import { useEffect, useRef, useState } from "react";
import { FaStop } from "react-icons/fa";
import { IoMdCloseCircleOutline } from "react-icons/io";
import { LuCheckCheck } from "react-icons/lu";

import Cookies from "js-cookie";
import { ChatBotLaunchPadIcon } from "@/assets/icons/GeneralIcons";

const Chatbot = ({isChatBotOpen,closeChatFunc = ()=> {}}) => {
  const [showChatbot, setShowChatbot] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const [userMessage, setUserMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [typingMessage, setTypingMessage] = useState("");
  const [MessageAnimation, setMessageAnimation] = useState(false);
  const [stoppedMessage, setStoppedMessage] = useState("");

  const [showPopMessage, setShowPopMessage] = useState(false);
  const [chatHistory, setChatHistory] = useState([
    {
      message: "👋 Hi! are you looking for help?",
      type: "fIncoming",
      timestamp: moment().format("LT"),
    },
  ]);
  const [showInitialPage, setShowInitialPage] = useState(true);
  const chatboxRef = useRef(null);
  const controllerRef = useRef(new AbortController());
  const intervalRef = useRef();

  const toggleChatbot = () => {
    if (showChatbot) {
      setIsClosing(true);
      setTimeout(() => {
        setIsClosing(false);
        setShowChatbot(false);
      }, 100);
    } else {
      setShowInitialPage(true);
      setShowChatbot(true);
    }
  };

  const closeChatbot = () => {
    sessionStorage.setItem("showedPopMessage", false);
    setIsClosing(true);
    setTimeout(() => {
      handleStopTyping(); // دع هذا الحدث يتوقف عن الكتابة
      setIsClosing(false);
      setShowChatbot(false);
      resetChat(); // Reset chat when closing
    }, 100);
    closeChatFunc()
  };

  const handleUserInput = (e) => setUserMessage(e.target.value);

  const handleChat = () => {
    if (!userMessage.trim()) return;

    const newChatHistory = [
      ...chatHistory,
      ...(stoppedMessage
        ? [
            {
              message: stoppedMessage,
              type: "incoming",
              timestamp: moment().format("LT"),
            },
          ]
        : []),
      {
        message: userMessage,
        type: "outgoing",
        timestamp: moment().format("LT"),
      },
    ];

    setChatHistory(newChatHistory);
    setUserMessage("");
    setTypingMessage("");

    if (!stoppedMessage) {
      setStoppedMessage("");
    }

    setIsTyping(true);
    fetchResponseFromAPI(userMessage);
  };

  const fetchResponseFromAPI = async (query) => {
    setMessageAnimation(true);
    controllerRef.current.abort();
    const newController = new AbortController();
    controllerRef.current = newController;

    const body = {
      message: query,
    };

    try {
      setMessageAnimation(true);
      const { data } = await axios.post(
        "https://levelup-chatbot0-staging.azurewebsites.net/chat",
        body,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("level_user_token")}`,
          },
        }
      );
      // console.log(data);
      setMessageAnimation(true);
      if (data.response) {
        setMessageAnimation(false);
        animateResponseMessage(data.response);
      } else if (data) {
        setMessageAnimation(false);
        // setIsTyping(false);
        animateResponseMessage(data);
      } else {
        setMessageAnimation(false);
        animateResponseMessage(
          "Sorry, I couldn't fetch a response at this moment."
        );
      }
    } catch (error) {
      setIsTyping(false);
      setMessageAnimation(false);
      console.error("Error fetching response:", error);
      setChatHistory((prev) => [
        ...prev,
        {
          message: "Sorry, I couldn't fetch a response at this moment.",
          type: "incoming",
          timestamp: moment().format("LT"),
        },
      ]);
    }
  };
  // console.log(typingMessage);
  const animateResponseMessage = (message) => {
    // console.log(message);
    setTypingMessage("");
    setIsTyping(true);
    let index = -1;
    const interval = setInterval(() => {
      // setTypingMessage(null);
      typingMessage;
      if (index + 1 < message.length) {
        setTypingMessage((prev) => prev + message[index]);
        index += 1;
      } else {
        clearInterval(interval);
        setChatHistory((prevChatHistory) => [
          ...prevChatHistory,
          {
            message: message,
            type: "incoming",
            timestamp: moment().format("LT"),
          },
        ]);
        setTypingMessage("");
        setIsTyping(false);
      }
    }, 50);
    intervalRef.current = interval;
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleChat();
    }
  };

  const handleStopTyping = () => {
    setIsTyping(false);
    setStoppedMessage(typingMessage);
    clearInterval(intervalRef.current);
  };

  useEffect(() => {
    if (sessionStorage.getItem("showedPopMessage") !== "false") {
      setTimeout(() => {
        setShowPopMessage(true);
        sessionStorage.setItem("showedPopMessage", true);
      }, 20);
    }
  }, [sessionStorage.getItem("showedPopMessage") !== "false"]);
  if (showChatbot) {
    sessionStorage.setItem("showedPopMessage", false);
  }
  // console.log(showChatbot);
  window.addEventListener("beforeunload", () => {
    sessionStorage.setItem("reloaded", "true");
  });

  window.addEventListener("load", () => {
    const reloaded = sessionStorage.getItem("reloaded");

    if (reloaded === "true") {
      sessionStorage.setItem("showedPopMessage", true);
      sessionStorage.removeItem("reloaded");
    }
  });

  useEffect(() => {
    if (chatboxRef.current) {
      chatboxRef.current.scrollTop = chatboxRef.current.scrollHeight;
    }
  }, [chatHistory, isTyping, typingMessage, userMessage]);

  const resetChat = () => {
    setChatHistory([
      {
        message: "👋 Hi! are you looking for help?",
        type: "fIncoming",
        timestamp: moment().format("LT"),
      },
    ]);
    setUserMessage("");
    setTypingMessage("");
    setStoppedMessage("");
    setIsTyping(false);
  };

  useEffect(() => {
    if(isChatBotOpen){
      setShowChatbot(true)
      setIsClosing(true)
    }else{
      closeChatFunc()
      setShowChatbot(false)
      setIsClosing(false)
    }
  },[isChatBotOpen])

  return (
    <>
      <div className="fixed LevelUp-AI-dashboard bottom-5 right-6 z-[999999999]">
        <button
          onClick={toggleChatbot}
          className="p-3 text-white rounded-full focus:outline-none chatbot-button relative "
        >
          {!showChatbot && (
            <div className="flex relative group">
              <div
                className={`absolute ${
                  showPopMessage &&
                  sessionStorage.getItem("showedPopMessage") !== "false"
                    ? "transition-all duration-1000 right-[50%] opacity-100"
                    : "-right-[500%] opacity-0"
                }   bottom-[105%] text-white  bg-[#00C0A9] rounded-xl flex 
              justify-around items-center w-[328px] h-[60px] ps-5 pe-1`}
              >
                <h1 className="flex font-medium text-base  w-full text-nowrap">
                  Hi! Are you looking for any help?
                </h1>
                <div className=" flex mb-auto mt-2">
                  <IoMdCloseCircleOutline
                    className=" h-6 w-6"
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowPopMessage(false);
                      sessionStorage.setItem("showedPopMessage", false);
                    }}
                  />
                </div>
              </div>
              {/* <img src={img} className="animate-pulse w-16" alt="chatbot" /> */}
              <div className="animate-pulse flex justify-center items-center">
              <ChatBotLaunchPadIcon />

              </div>
            </div>
          )}
        </button>
        {(showChatbot || isClosing) && (
          <div className="fixed right-5 bottom-20 w-96 bg-white rounded-lg shadow-xl overflow-hidden transform transition-all duration-300 max-sm:w-full max-sm:right-0 max-sm:bottom-0 max-sm:h-full max-sm:rounded-none ">
            <header
              className={`bg-[#00C0A9] text-white p-3 flex shadow-xl ${
                !showInitialPage ? "justify-between" : "justify-end"
              } items-center`}
            >
              {!showInitialPage ? (
                <div className="flex items-center ">
                  <img src={BotImg} className=" me-2" alt="chatbot" />
                  <div>
                    <h2 className="text-lg text-nowrap font-bold">
                      LevelUp Ai
                    </h2>
                    <div className="flex items-center animate-pulse">
                      <p className="w-[2px] h-[3px] p-[5px] bg-[#02ff2c] rounded-full flex me-2  opacity-100"></p>
                      <p className="text-[#02ff2c] font-semibold"> Online</p>
                    </div>
                  </div>
                </div>
              ) : (
                ""
              )}
              <button
                onClick={closeChatbot}
                className={`text-white ${!showInitialPage ? "mt-1" : ""}`}
              >
                <IoMdCloseCircleOutline className="h-6 w-6" />
              </button>
            </header>
            {showInitialPage ? (
              <div className="flex flex-col items-center justify-start max-sm:h-full h-[484px] bg-[#00C0A9] px-2">
                <div className="w-1/2 h-[163px] flex justify-center">
                  <img src={chatimg} alt="Chatbot" className="mb-2" />
                </div>
                <h2 className="text-[28px] leading-10 font-medium mb-1 text-center text-white cursor-default font-inter">
                  Meet
                  <span className="font-extrabold text-black ms-2">
                    LevelUp Ai
                  </span>
                  <br />
                  <span className="text-2xl  tracking-wider ">
                    Your own AI assistant
                  </span>
                </h2>
                <p className="text-center mb-6 text-white text-sm px-5">
                  I'm LevelUp, your AI-powered sustainability assistant created
                  by LevelUp ESG to be helpful, reliable, and honest. Feel free
                  to ask me any questions you may have, and I will do my best to
                  provide accurate and useful information to support you
                  advancing your sustainability journey.
                </p>
                <button
                  onClick={() => setShowInitialPage(false)}
                  className="w-3/4 py-2 bg-white text-primary rounded-3xl font-bold"
                >
                  Get Started
                </button>
                <span className="w-1/2 h-2 bg-white mt-8 rounded-lg"></span>
              </div>
            ) : (
              <ul
                ref={chatboxRef}
                className="h-[400px] max-sm:h-[75%] overflow-y-auto p-4 pb-0 chatbox-scrollbar flex flex-col"
              >
                {chatHistory.map((chat, index) => (
                  <li
                    key={index}
                    className={`flex flex-col  ${
                      chat.type === "fIncoming"
                        ? " items-center "
                        : chat.type === "incoming"
                        ? "items-start"
                        : "items-end"
                    } mb-2`}
                  >
                    <div
                      className={`flex  items-end ${
                        chat.type === "incoming"
                          ? "flex-row"
                          : "flex-row-reverse"
                      }`}
                    >
                      {chat.type === "fIncoming" ? (
                        ""
                      ) : chat.type === "incoming" ? (
                        <img src={img} alt="img" className="w-5 me-[3px]" />
                      ) : (
                        <img
                          src={manImg}
                          alt="manImg"
                          className="w-5 ms-[3px] rounded-full"
                        />
                      )}
                      <div>
                        <p
                          className={`text-[15.5px] py-3 px-4 w-auto   rounded-xl border-2 text-[#444444] ${
                            chat.type === "fIncoming"
                              ? "bg-[#e1f0f1] border-[#b5dbdd]"
                              : chat.type === "incoming"
                              ? "bg-[#e1f0f1] border-[#b5dbdd] rounded-bl-none "
                              : "bg-[#e6f9f7] border-[#b3ece6] rounded-br-none"
                          } max-w-xs break-words`}
                        >
                          {chat.message}
                        </p>
                        <span className="text-xs text-gray-500 mt-2">
                          {chat.type === "fIncoming" ? (
                            <span className="text-center flex justify-center">
                              Today at {chat.timestamp}
                            </span>
                          ) : chat.type === "incoming" ? (
                            <span className="flex items-center">
                              {chat.timestamp}
                              <LuCheckCheck className=" text-secondary-300 ms-2 w-4 h-4" />
                            </span>
                          ) : (
                            <span className="flex items-center justify-end me-1">
                              {chat.timestamp}
                              <LuCheckCheck className=" text-secondary-300 ms-2 w-4 h-4" />
                            </span>
                          )}
                        </span>
                      </div>
                    </div>
                  </li>
                ))}
                {MessageAnimation && (
                  <li className="flex items-start mb-2">
                    <div className="flex items-end">
                      <img src={img} alt="" className="w-5 me-[3px]" />
                      <div className="bg-[#e1f0f1] border-2  border-[#b5dbdd] rounded-bl-none text-[15.5px] py-3 px-4 rounded-xl max-w-xs break-words mb-3">
                        <TypingIndicator />
                      </div>
                    </div>
                  </li>
                )}
                {typingMessage && (
                  <li className="flex items-start mb-2">
                    <div className="flex items-end">
                      <img src={img} alt="" className="w-5 me-[3px]" />
                      <div className="bg-[#e1f0f1] border-2  border-[#b5dbdd] rounded-bl-none text-[15.5px] py-3 px-4 rounded-xl max-w-xs break-words mb-3 text-[#444444]">
                        {typingMessage}
                      </div>
                    </div>
                  </li>
                )}
              </ul>
            )}
            {!showInitialPage && (
              <>
                <div className="px-4 mt-auto"></div>
                <div className="p-4 pt-1 flex items-center bg-white">
                  <div className="w-full bg-[#e8ebf0] flex items-center rounded-2xl">
                    <TextInput
                      type="text"
                      placeholder="Ask any thing..."
                      value={userMessage}
                      onChange={handleUserInput}
                      onKeyDown={handleKeyDown}
                      className="w-full rounded-2xl focus:outline-none  border-none outline-none bg-[#e8ebf0] p-2 placeholder:opacity-100"
                      rightSection={""}
                      variant="unstyled"
                    />
                    {isTyping ? (
                      <Button
                        onClick={handleStopTyping}
                        className="ml-2 p-2 bg-secondary-300 text-white rounded-full focus:outline-none hover:bg-secondary-300 "
                        // disabled={!chatTypeSelected}
                      >
                        <FaStop className="h-4 w-6" />
                      </Button>
                    ) : (
                      <Button
                        onClick={handleChat}
                        className="ml-2 p-2 bg-transparent text-white rounded-full focus:outline-none hover:bg-transparent h-12 w-12"
                        // disabled={!chatTypeSelected}
                      >
                        <img src={send} alt="" className="w-full h-full" />
                        {/* <MdSend className="h-6 w-6" /> */}
                      </Button>
                    )}
                  </div>
                </div>
              </>
            )}
          </div>
        )}
      </div>
    </>
  );
};

const TypingIndicator = () => {
  return (
    <div className="typing-indicator flex gap-2">
      <span className="dot bg-[#444444] rounded-full w-2 h-2 animate-bounce"></span>
      <span className="dot bg-[#444444] rounded-full w-2 h-2 animate-bounce"></span>
      <span className="dot bg-[#444444] rounded-full w-2 h-2 animate-bounce"></span>
    </div>
  );
};

export default Chatbot;
