// useAutoLoanStore.js
import { create } from "zustand";

export const useBusinessLoansStore = create((set) => ({
  loan_amount: "",
  borrower_name: "",
  borrower_industry: "Manufacturing",
  borrower_assets: "", 
  borrower_emissions: "", 
  emission_scope: "Scope 1 & 2",
  data_quality_score: "",

  // results
  attribution_percentage: 0,
  carbon_intensity: 0,
  financed_emissions: 0,
  loading: false,
  estimate_borrower_assets_loading:false,
  estimate_borrower_emissions_loading:false,
  // Setters
  setLoanAmount: (v) => set({ loan_amount: v }),
  setBorrowerName: (v) => set({ borrower_name: v }),
  setBorrowerIndustry: (v) => set({ borrower_industry: v }),
  setBorrowerAssets: (v) => set({ borrower_assets: v }),
  setBorrowerEmissions: (v) => set({ borrower_emissions: v }),
  setEmissionScope: (v) => set({ emission_scope: v }),
  setDataQualityScore: (v) => set({ data_quality_score: v }),
  setLoading: (v) => set({ loading: v }),
  setBorrowerEmissionsLoading: (v) => set({ estimate_borrower_emissions_loading: v }),
  setBorrowerAssetsLoading: (v) => set({ estimate_borrower_assets_loading: v }),

  setResults: (results) => set({
    attribution_percentage: results.attribution_percentage,
    carbon_intensity: results.carbon_intensity,
    financed_emissions: results.financed_emissions
  }),
}));
