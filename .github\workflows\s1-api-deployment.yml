# name: Build and Push Docker Image

# on:
#   push:
#     branches:
#       - DevOps-test

# jobs:
#   build_and_push:
#     runs-on: ubuntu-latest

#     steps:
#     - name: Checkout repository
#       uses: actions/checkout@v2

#     - name: Login to Azure Container Registry
#       uses: azure/docker-login@v1
#       with:
#         login-server: levelupportal.azurecr.io
#         username: ${{ secrets.LEVELUPPORTAL }}
#         password: ${{ secrets.LEVELUPPORTALPASS }}

#     - name: Build Docker image
#       run: |
#         docker build \
#         --build-arg API_KEY=${{ secrets.API_KEY }} \
#         --build-arg DB_PASSWORD=${{ secrets.DB_PASSWORD }} \
#         --build-arg DB_HOST=${{ secrets.DB_HOST }} \
#         --build-arg DB_PORT=${{ secrets.DB_PORT }} \
#         --build-arg DB_NAME=${{ secrets.DB_NAME }} \
#         --build-arg DB_USER=${{ secrets.DB_USER }} \
#         --build-arg AUTH0_CLIENT_ID=${{ secrets.AUTH0_CLIENT_ID }} \
#         --build-arg AUTH0_CLIENT_SECRET=${{ secrets.AUTH0_CLIENT_SECRET }} \
#         --build-arg AUTH0_CLIENT_DOMAIN=${{ secrets.AUTH0_CLIENT_DOMAIN }} \
#         --build-arg SECRET_KEY=${{ secrets.SECRET_KEY }} \
#         --build-arg AI_ASSESSMENT_URL=${{ secrets.AI_ASSESSMENT_URL }} \
#         --build-arg PAYLOAD_ID=${{ secrets.PAYLOAD_ID }} \
#         --build-arg REQUEST_KEY=${{ secrets.REQUEST_KEY }} \
#         --build-arg PAYLOAD_SCOPE=${{ secrets.PAYLOAD_SCOPE }} \
#         --build-arg QUERY=${{ secrets.QUERY }} \
#         --build-arg AI_ASSESSMENT_URL_POST=${{ secrets.AI_ASSESSMENT_URL_POST }} \
#         --build-arg PAYLOAD_ID_POST=${{ secrets.PAYLOAD_ID_POST }} \
#         --build-arg REQUEST_KEY_POST=${{ secrets.REQUEST_KEY_POST }} \
#         --build-arg PAYLOAD_SCOPE_POST=${{ secrets.PAYLOAD_SCOPE_POST }} \
#         -t diagnostic-api ./API

#     - name: Tag Docker image
#       run: |
#         docker tag diagnostic-api levelupportal.azurecr.io/diagnostic-api:latest

#     - name: Push Docker image to Azure Container Registry
#       run: |
#         docker push levelupportal.azurecr.io/diagnostic-api:latest
