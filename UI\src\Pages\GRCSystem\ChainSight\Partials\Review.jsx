import { useEffect, useState } from "react";
import S2Layout from "@/Layout/S2Layout";
import useRoutes from "@/Routes/useRoutes";
import { useNavigate } from "react-router-dom";
import { Table } from "@mantine/core";
import { CiEdit } from "react-icons/ci";
import Cookies from "js-cookie";

const Review = () => {
  const { getStart } = useRoutes();
  const navigate = useNavigate();
  const [sections, setSections] = useState([]);
  const [loading, setLoading] = useState(true);
  const baseUrl = "https://chain-sight-staging.azurewebsites.net";

  // Fetch last in-progress assessment
  const fetchLastInProgressAssessment = async () => {
    const response = await fetch(
      `${baseUrl}/api/assessments/in-progress/last`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${Cookies.get("level_user_token")}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error(
        JSON.stringify({
          status: response.status,
          message: "Failed to fetch last in-progress assessment",
        })
      );
    }

    const data = await response.json();
    return data;
  };

  // Fetch assessment data on mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        const assessmentData = await fetchLastInProgressAssessment();
        const sortedSections = assessmentData.sections.sort(
          (a, b) => a.order - b.order
        );
        setSections(sortedSections);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching assessment:", error);
        setSections([]);
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <S2Layout
        navbarTitle="ChainSight: Sustainable Procurement"
        breadcrumbItems={[
          { title: "Launchpad", href: getStart.path },
          { title: "ChainSight", href: "#" },
          { title: "Review", href: "#" },
        ]}
      >
        <div className="flex items-center justify-center h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#07838F]"></div>
        </div>
      </S2Layout>
    );
  }

  return (
    <S2Layout
      navbarTitle="ChainSight: Sustainable Procurement"
      breadcrumbItems={[
        { title: "Launchpad", href: getStart.path },
        { title: "ChainSight", href: "#" },
        { title: "Review", href: "#" },
      ]}
    >
      <div className="flex flex-col gap-6 p-4 w-full">
        {sections.map((section) => {
          // Get questions for this section
          const sectionQuestions = section.questions;

          // Calculate section-specific stats
          const totalQuestions = sectionQuestions.length;
          const addressedQuestions = sectionQuestions.filter((q) =>
            q.options.some((o) => o.is_chosen)
          ).length;
          const markedForReview = sectionQuestions.filter(
            (q) => q.is_flagged
          ).length;

          return (
            <div
              key={section.id}
              className="w-full flex flex-col gap-4 border border-[#ECECEC] rounded-xl bg-white p-6"
            >
              <div className="bg-[#07838F1A] w-fit text-[#07838F] px-4 py-2 rounded-lg">
                {section.title}
              </div>
              <p className="text-[#494949] font-bold text-3xl">
                Assessment Summary
              </p>
              <div className="w-full flex gap-4 flex-wrap bg-[#07838F1A] text-[#494949] py-2 px-4 rounded-md">
                <span>Total Questions: {totalQuestions}</span>
                <span>Addressed Questions: {addressedQuestions}</span>
                <span>Marked for Review: {markedForReview}</span>
              </div>
              <div className="overflow-x-auto">
                <Table
                  highlightOnHover
                  withTableBorder
                  withColumnBorders
                  className="min-w-full"
                >
                  <thead className="bg-[#F5F4F5]">
                    <tr>
                      <th className="px-4 py-3 text-left text-[#272727] font-semibold">
                        No.
                      </th>
                      <th className="px-4 py-3 text-left text-[#272727] font-semibold">
                        Review Questions
                      </th>
                      <th className="px-4 py-3 text-center text-[#272727] font-semibold">
                        Comments Status
                      </th>
                      <th className="px-4 py-3 text-center text-[#272727] font-semibold">
                        Review
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {sectionQuestions.map((question, index) => (
                      <tr
                        key={question.id}
                        className="hover:bg-[#07838F0A] transition-colors"
                      >
                        <td className="px-4 py-3 text-[#272727] font-semibold">
                          Q{index + 1}
                        </td>
                        <td className="px-4 py-3 text-[#272727] font-medium">
                          {question.text}
                        </td>
                        <td className={`px-4 py-3 text-center text-[#494949]`}>
                          <span
                            className={`px-3 py-1 rounded-full ${
                              question.is_answered
                                ? "bg-[#D1FAE5] text-[#065F46]"
                                : "bg-[#FEE2E2] text-[#991B1B]"
                            }`}
                          >
                            {question.is_answered ? "Addressed" : "Outstanding"}
                          </span>
                        </td>
                        <td className="px-4 py-3 text-center text-[#494949]">
                          <CiEdit
                            className="text-[#07838F] text-lg mx-auto cursor-pointer"
                            onClick={() =>
                              navigate("/Grc/chain-sight", {
                                state: {
                                  targetSectionTitle: section.title,
                                  targetQuestionId: question.id,
                                },
                              })
                            }
                          />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </div>
            </div>
          );
        })}
      </div>
    </S2Layout>
  );
};

export default Review;
