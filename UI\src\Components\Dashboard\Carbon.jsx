import React from 'react'
import ChartCo from './ChartCo';
import  PropType from 'prop-types';


function Carbon({date,icon}) {
  return (
        <div className='bg-white py-[1rem] px-[1.5rem] rounded-2xl shadow-2xl w-[90%]  xl:w-[60%] mx-auto'>
            <div className='flex items-center justify-between mb-5'>
                <h1 className='font-bold text-[18px] mb-5'>Carbon Footprint Breakdown</h1>
                <div className=' border-[#2A939C] border-[1px] py-1 px-5 rounded-lg shadow-2xl shadow-black'>
                    <button className='font-[300] text-[#15192C] flex items-center gap-[.5rem]'>
                        <div>{icon}</div>
                        <span className='text-[13px]'> {date}</span>
                    </button>
                </div>
            </div>
            <div className='flex flex-wrap gap-[.5rem] justify-between'>
                <div>
                    <ChartCo/>
                </div>
                <div>
                    <div className='flex gap-[.8rem] items-center mb-5'>
                        <div className='w-[20px] h-[20px] rounded-full bg-[#D4E9EB]'></div>
                        <h3 className='text-[12px] font-[300]'>Electricity</h3>
                    </div>
                    <div className='flex gap-[.8rem] items-center mb-5'>
                        <div className='w-[20px] h-[20px] rounded-full bg-[#70D162]'></div>
                        <h3 className='text-[12px] font-[300]'>Transportation</h3>
                    </div>
                    <div className='flex gap-[.8rem] items-center mb-5'>
                        <div className='w-[20px] h-[20px] rounded-full bg-[#07838F]'></div>
                        <h3 className='text-[12px] font-[300]'>Fuel & Gas</h3>
                    </div>
                    
                </div>
            </div>
            
        </div>
  )
}

Carbon.propTypes = {
  date: PropType.string,
  icon: PropType.object
};

export default Carbon
